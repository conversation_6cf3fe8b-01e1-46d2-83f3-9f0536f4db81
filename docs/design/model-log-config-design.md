# 模型日志配置设计

## 概述

为了提供更灵活的审计和监控能力，我们为 Trantor 平台的数据模型（DataStructNode）增加了日志配置功能。该功能允许用户针对每个模型单独配置是否开启操作日志记录，以及在日志中重点展示哪些字段。

## 设计目标

1. **灵活性**：允许按模型粒度控制日志开关，避免不必要的日志记录
2. **可配置性**：支持配置日志中的关键展示字段，提高日志可读性
3. **性能考虑**：默认关闭日志功能，避免对系统性能造成影响
4. **向后兼容**：新功能不影响现有模型的正常使用

## 技术方案

### 1. 数据结构设计

在 `DataStructProperties` 中新增 `logConfig` 字段：

```java
/**
 * 模型操作日志配置
 */
@Schema(description = "模型操作日志配置")
private ModelLogConfig logConfig;
```

`ModelLogConfig` 类定义：

```java
@Schema(description = "模型操作日志配置")
@Data
public class ModelLogConfig implements Serializable {
    
    /**
     * 是否开启操作日志
     */
    @Schema(description = "是否开启操作日志")
    private boolean enabled;
    
    /**
     * 日志主要显示字段
     */
    @Schema(description = "日志主要显示字段")
    private String displayField;
}
```

### 2. 配置说明

#### enabled 字段
- 类型：`boolean`
- 默认值：`false`
- 说明：控制是否对该模型的增删改操作进行日志记录

#### displayField 字段
- 类型：`String`
- 默认值：`null`
- 说明：指定在日志中重点展示的字段，便于快速识别操作对象

### 3. 使用场景

#### 场景一：敏感数据模型
对于包含敏感信息的模型（如用户信息、财务数据），开启日志记录：

```json
{
  "key": "user_info",
  "type": "Model",
  "props": {
    "logConfig": {
      "enabled": true,
      "displayField": "username"
    }
  }
}
```

#### 场景二：高频操作模型
对于操作频繁但不太重要的模型，可以关闭日志以提高性能：

```json
{
  "key": "system_cache",
  "type": "Model",
  "props": {
    "logConfig": {
      "enabled": false
    }
  }
}
```

#### 场景三：业务关键模型
对于业务关键模型，开启日志并配置业务关键字段：

```json
{
  "key": "order",
  "type": "Model",
  "props": {
    "logConfig": {
      "enabled": true,
      "displayField": "orderNo"
    }
  }
}
```

## 实现细节

### 1. 日志记录时机

在以下操作时触发日志记录：
- CREATE：创建新记录
- UPDATE：更新记录
- DELETE：删除记录
- BATCH_CREATE：批量创建
- BATCH_UPDATE：批量更新
- BATCH_DELETE：批量删除

### 2. 日志内容

日志记录应包含：
- 操作类型（CREATE/UPDATE/DELETE）
- 操作时间
- 操作人
- 模型标识（key）
- 记录标识（主键值）
- 显示字段的值（根据 displayField 配置）
- 变更内容（UPDATE 操作时记录字段变更）

### 3. 性能优化

- 异步记录：日志记录应采用异步方式，避免影响主业务流程
- 批量写入：对于批量操作，应优化日志写入性能
- 字段过滤：仅记录 displayField 中指定的字段，减少日志数据量

## 扩展性考虑

### 1. 日志级别

未来可以扩展日志级别配置：
```java
private LogLevel logLevel; // BASIC, DETAILED, FULL
```

### 2. 日志存储

支持配置日志存储策略：
```java
private LogStorageConfig storageConfig; // 保留时间、存储位置等
```

### 3. 日志格式

支持自定义日志格式模板：
```java
private String logTemplate; // 自定义日志输出格式
```

## 注意事项

1. **隐私保护**：displayField 配置时应避免包含敏感信息字段
2. **存储成本**：开启日志会增加存储成本，应合理规划
3. **查询性能**：大量日志数据可能影响查询性能，需要适当的索引策略
4. **合规要求**：某些行业可能有强制的审计日志要求，需要确保配置正确

## 最佳实践

1. **按需开启**：仅对重要的业务模型开启日志
2. **精简字段**：displayField 只配置最关键的业务字段，通常选择业务主键或名称字段
3. **定期清理**：制定日志保留策略，定期清理过期日志
4. **监控告警**：对异常操作模式设置监控告警
5. **权限控制**：日志查询功能应有适当的权限控制