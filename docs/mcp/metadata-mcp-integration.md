# 元数据 MCP 集成指南

## 概述

Trantor 平台提供了模型上下文协议（MCP）集成，用于元数据操作，使 AI 驱动的工具能够与平台的元数据系统进行交互。该集成在 Console 和 Runtime 环境中都可用，但具有不同的权限级别。

## 架构

### 组件结构

```
trantor-module-engine/
├── trantor-module-engine-api-common/
│   └── io.terminus.trantor2.module.service/
│       ├── MetaDataResourceNodeService.java       # 服务接口
│       └── io.terminus.trantor2.module.model.dto/
│           └── MoveRequest.java                   # 移动请求 DTO
└── trantor-module-engine-impl-common/
    └── io.terminus.trantor2.module.mcp/
        ├── metadata/
        │   ├── MetaQueryTools.java               # 查询工具
        │   └── MetaEditTools.java                # 编辑工具
        ├── config/
        │   └── MetaMcpConfig.java                # MCP 配置
        └── dto/
            └── MetaTypeDTO.java                  # 元数据类型 DTO
```

### 环境特定功能

| 功能 | Console | Runtime |
|------|---------|---------|
| 元数据查询（读取） | ✅ | ✅ |
| 元数据编辑（创建/更新/删除） | ✅ | ❌ |
| 工具发现 API | ✅ | ❌ |
| MCP 服务器端点 | ✅ | ❌ |

## 配置

### Console 配置

在 `trantor-console/trantor-console-starter/src/main/resources/application.yml` 中：

```yaml
spring:
  ai:
    mcp:
      server:
        enabled: true

iam:
  web:
    request-white-list:
      - /sse
      - /mcp/message
      - /api/trantor/mcp/built-in/servers
```

### Runtime 配置

在 `trantor-runtime/trantor-runtime-impl/src/main/resources/trantor2-config.yml` 中：

```yaml
spring:
  ai:
    mcp:
      server:
        enabled: true

iam:
  web:
    request-white-list:
      - /sse
      - /mcp/message
      - /api/trantor/mcp/built-in/servers
```

**注意**：Runtime 环境需要同时满足 `RuntimeEnabledCondition` 和 `OnTrantor2DefaultModeCondition` 条件才会启用 MCP 服务器。

## 可用工具

### 查询工具（Console 和 Runtime）

#### 1. getMetaTypes
获取所有可用的元数据类型及其描述。

**响应示例：**
```json
[
  {
    "name": "tran.module.Logic",
    "label": "业务逻辑",
    "extendable": true
  },
  {
    "name": "tran.module.Model",
    "label": "数据模型",
    "extendable": true
  }
]
```

#### 2. searchMeta
通过关键字和可选的类型过滤器搜索元数据。

**参数：**
- `keyword`（必需）：搜索关键字
- `type`（可选）：类型过滤器（如 "Logic"、"Model"、"Api"）

**响应：** 匹配搜索条件的元数据项列表

#### 3. getMetaByKey
通过类型和键获取特定元数据。

**参数：**
- `type`（必需）：完整类型名称（如 "tran.module.Logic"）
- `key`（必需）：元数据键

**响应：** 完整的元数据节点信息

#### 4. getUsingModelDependencies
获取给定元素的模型依赖关系。

**参数：**
- `type`（必需）：元素类型
- `key`（必需）：元素键

**响应：** 依赖关系映射

### 编辑工具（仅 Console）

#### 1. createNode
创建新的元数据节点。

**参数：**
- `parentKey`（必需）：父节点键
- `node`（必需）：要创建的节点数据

#### 2. updateNode
更新现有的元数据节点。

**参数：**
- `key`（必需）：要更新的节点键
- `node`（必需）：更新后的节点数据

#### 3. deleteNode
删除元数据节点。

**参数：**
- `key`（必需）：要删除的节点键

## Spring Bean 配置

### 统一配置类

```java
@Configuration
public class MetaMcpConfig {

    /**
     * Console 环境的 MCP 工具配置
     * 包含查询和编辑工具
     */
    @Configuration
    @Conditional(ConsoleEnabledCondition.class)
    public static class ConsoleMcpConfig {
        // Console 配置...
    }

    /**
     * Runtime 环境的 MCP 工具配置
     * 仅包含查询工具
     * 需要同时满足 RuntimeEnabledCondition 和 OnTrantor2DefaultModeCondition
     */
    @Configuration
    @Conditional({RuntimeEnabledCondition.class, OnTrantor2DefaultModeCondition.class})
    public static class RuntimeMcpConfig {
        // Runtime 配置...
    }
}
```

## API 端点

### MCP 服务器发现

**端点：** `GET /api/trantor/mcp/built-in/servers`

**描述：** 返回内置 MCP 服务器及其工具的列表。

**响应示例：**
```json
{
  "total": 1,
  "list": [
    {
      "id": "0c0a3425-1754-4495-8800-baff3000ce1f",
      "name": "trantor-meta",
      "version": "3.0.2506",
      "transportType": "sse",
      "description": "Trantor 元数据 MCP 服务器...",
      "endpoint": "http://trantor2:8080/sse",
      "tools": [...]
    }
  ]
}
```

## 安全注意事项

1. **身份验证**：所有 MCP 端点都需要适当的 IAM 身份验证，除非已加入白名单。
2. **授权**：Runtime 环境对元数据具有只读访问权限。
3. **白名单**：MCP 相关端点必须添加到 IAM 白名单配置中。

## 集成示例

### 在 AI 应用中使用 MCP 工具

```java
// 示例：注入和使用 MCP 工具
@Autowired
private ToolCallbackProvider toolCallbackProvider;

// 工具通过 Spring AI 自动提供给 AI 模型
```

### 访问 MCP 服务器信息

```bash
# 获取可用的 MCP 服务器
curl -X GET http://localhost:8080/api/trantor/mcp/built-in/servers

# 通过 SSE 连接到 MCP 服务器
curl -X GET http://localhost:8080/sse
```

## 故障排除

### 常见问题

1. **MCP 服务器不可用**
   - 检查 `spring.ai.mcp.server.enabled` 是否设置为 `true`
   - 验证白名单配置是否包含 MCP 端点

2. **工具未被发现**
   - 确保 ToolCallbackProvider beans 正确配置
   - 检查 Spring 条件（ConsoleEnabledCondition/RuntimeEnabledCondition）

3. **身份验证错误**
   - 验证 IAM 配置
   - 检查端点是否正确加入白名单

## 相关组件

- **McpSourceConfig**：MCP 服务器源配置
- **McpServerSourceService**：管理 MCP 服务器配置的服务
- **Spring AI 集成**：利用 Spring AI 框架进行工具定义

## 未来增强

1. **扩展 Runtime 权限**：考虑为 runtime 添加可配置的写权限
2. **工具版本控制**：支持多个版本的工具
3. **自定义工具注册**：允许动态注册自定义 MCP 工具
4. **指标和监控**：为 MCP 工具使用添加可观察性