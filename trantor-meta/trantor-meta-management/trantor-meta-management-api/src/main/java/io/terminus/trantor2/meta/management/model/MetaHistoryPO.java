package io.terminus.trantor2.meta.management.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.terminus.trantor2.meta.api.model.MetaNodeAccessLevel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 **/
@Data
public class MetaHistoryPO {
    @JsonProperty(value = "当前版本", index = 0)
    private String oid;
    @JsonProperty(value = "上一版本", index = 1)
    private String lastOid;
    @JsonProperty(value = "标识", index = 2)
    private String key;
    @JsonProperty(value = "名称", index = 3)
    private String name;
    @JsonProperty(value = "类型", index = 4)
    private String type;
    @JsonProperty(value = "描述", index = 5)
    private String description;
    @JsonProperty(value = "属性", index = 6)
    private String props;
    @JsonProperty(value = "父节点标识", index = 7)
    private String parentKey;
    @JsonProperty(value = "访问级别", index = 8)
    private MetaNodeAccessLevel access;
    @JsonProperty("操作人")
    private String operator;
    @JsonProperty("操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ssZ", timezone = "GMT+8")
    private Date operateAt;
    @JsonProperty("备注")
    private String remark;
}
