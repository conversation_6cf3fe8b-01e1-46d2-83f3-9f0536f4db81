<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    .task {
      margin-left: 20px;
    }

    .task:nth-child(even) {
      background-color: #f8f9fa;
    }

    .modal {
      position: fixed;
      top: 85px;
      left: 170px;
      padding: 20px;
      background-color: #fff;
      border: 1px solid #ccc;
      z-index: 1000;
      width: 80%;
      max-height: 80%;
      overflow-y: auto;
    }

    .task-detail {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .task-summary {
      flex-grow: 1;
      display: flex;
      flex-direction: row;
    }

    .task-status {
      width: 200px;
    }

    .task-status-success {
      color: green;
    }

    .task-status-failed {
      color: red;
    }

    .task-status-other {
      color: #071076;
    }

    .progress-bar {
      width: 500px;
      background-color: #f3f3f3;
      border-radius: 5px;
      margin: 5px 0;
      position: relative;
      box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.1); /* Optional: for a bit of depth */
    }

    .progress-bar-inner {
      height: 10px;
      background-color: green;
      border-radius: 5px;
    }

    .progress-detail-text {
      position: absolute;
      width: 100%;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      font-size: 12px;
      color: #333;
    }

    .label-value-pair {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
      margin-right: 5px;
    }

    .label {
      font-weight: bold;
    }

    .value {
      margin-left: 10px;
    }

    .highlighted-row {
      background-color: yellow; /* Or any other highlight color */
    }
  </style>
  <title>Task Run Detail</title>
</head>
<body>
<div id="taskTree"></div>

<script>
  function createTree(task, level = 0) {
    const div = document.createElement('div');
    div.className = 'task';
    div.style.marginLeft = `${level * 20}px`;

    let subTaskList;
    let filterBtn;
    if (task.subTasks && task.subTasks.length > 0) {
      subTaskList = createSubTaskList(task, level);
      filterBtn = createSubTaskFilterBtn(subTaskList);
    }

    const taskDetailDiv = createTaskDetail(task, filterBtn, level);
    const icon = document.createElement('span');
    icon.textContent = level > 0 ? '→ ' : '• ';
    taskDetailDiv.prepend(icon);
    div.appendChild(taskDetailDiv);

    if (subTaskList) {
      div.appendChild(subTaskList);
    }

    return div;
  }

  function createTaskDetail(task, filterBtn, level) {
    const div = document.createElement('div');
    div.className = 'task-detail';

    const taskDetailModal = createDetailModal(
        'Task: ' + task.taskCode + ', RunID: ' + task.taskRunId,
        [
          {header: 'Options', content: task.options},
          {header: 'Result', content: JSON.stringify(task.result)},
          {header: 'Logs', content: task.logs.join('\n')},
          {header: 'PanicMessage', content: task.panicMessage},
        ],
        task.taskRunId,
        level,
    );
    div.appendChild(taskDetailModal);

    const showDetail = createShowDetail(taskDetailModal);
    div.appendChild(showDetail);
    if (filterBtn) {
      div.appendChild(filterBtn);
    }

    const summaryDiv = document.createElement('div');
    summaryDiv.className = 'task-summary';
    summaryDiv.appendChild(createLabelValuePair('Task:', task.taskCode));
    summaryDiv.appendChild(createLabelValuePair('Run ID:', task.taskRunId));
    summaryDiv.appendChild(createLabelValuePair('Team Code:', task.teamCode));
    div.appendChild(summaryDiv);

    const statusAndProgressDiv = document.createElement('div');
    statusAndProgressDiv.style.display = 'flex';
    statusAndProgressDiv.style.alignItems = 'center';

    const statusDiv = document.createElement('div');
    statusDiv.classList.add('task-status');
    statusDiv.textContent = `Status: ${task.status}`;
    statusDiv.classList.add(getStatusClass(task.status));
    statusAndProgressDiv.appendChild(statusDiv);

    const progressDiv = createProgress(task);
    statusAndProgressDiv.appendChild(progressDiv);

    div.appendChild(statusAndProgressDiv);

    return div;
  }

  function createProgress(task) {
    const progressDiv = document.createElement('div');
    progressDiv.className = 'progress-bar';

    const progressBarInner = document.createElement('div');
    progressBarInner.className = 'progress-bar-inner';
    progressBarInner.style.backgroundColor = task.status === 'Success' ? 'green' : task.status === 'Panic' || task.status === 'Error' ? 'red' : '#071076';

    const successRate = ((task.progress.success + 1) / (task.progress.total + 1)) * 100;
    progressBarInner.style.width = `${successRate}%`;
    progressDiv.appendChild(progressBarInner);

    const progressText = document.createElement('div');
    progressText.className = 'progress-detail-text';
    progressText.textContent = `SubTasks #Total: ${task.progress.total}, #Success: ${task.progress.success}, #Failed: ${task.progress.failed}`;

    progressText.style.color = successRate > 70 ? 'white' : '#333';
    progressDiv.appendChild(progressText);

    return progressDiv;
  }

  function openModal(row, modal) {
    closeAllModals();
    row.classList.add('highlighted-row');
    modal.style.display = 'block';
  }

  function closeModal(row, modal) {
    row.classList.remove('highlighted-row');
    modal.style.display = 'none';
  }

  function closeAllModals() {
    const openModals = document.querySelectorAll('.modal');
    openModals.forEach(modal => {
      const relatedRow = modal.parentElement;
      closeModal(relatedRow, modal);
    });
  }

  function getPrevAndNextTasks(modal, offset) {
    const currentRunId = modal.getAttribute('runId');
    const currentLevel = parseInt(modal.getAttribute('level'));
    const neighborModals = []
    modal.parentElement.parentElement.parentElement.querySelectorAll('.modal').forEach(it => {
      if (it.parentElement.parentElement.style.display !== 'none' && parseInt(it.getAttribute('level')) === currentLevel) {
        neighborModals.push(it);
      }
    })
    let currentIndex = -1;
    neighborModals.forEach((taskElement, index) => {
      const runId = taskElement.getAttribute('runId');
      if (runId === currentRunId) {
        currentIndex = index;
      }
    });
    return neighborModals[currentIndex + offset];
  }

  function createDetailModal(title, sections, runId, level) {
    const modal = document.createElement('div');
    modal.setAttribute('runId', runId);
    modal.setAttribute('level', level);
    modal.className = 'modal';
    modal.style.display = 'none';

    const titleDiv = document.createElement('div');
    titleDiv.style.textAlign = 'center';
    titleDiv.style.fontSize = '16px';
    titleDiv.style.marginBottom = '20px';
    titleDiv.textContent = title;
    modal.appendChild(titleDiv);

    const ctl = document.createElement('div');
    ctl.style.position = 'absolute';
    ctl.style.top = '10px';
    ctl.style.left = '10px';
    modal.appendChild(ctl);
    {
      const closeBtn = document.createElement('button');
      closeBtn.textContent = 'Close';
      closeBtn.onclick = () => closeModal(modal.parentElement, modal);

      const leftArrowBtn = document.createElement('button');
      leftArrowBtn.textContent = '←';
      leftArrowBtn.onclick = () => {
        const prevTask = getPrevAndNextTasks(modal, -1)
        if (prevTask) {
          closeModal(modal.parentElement, modal);
          openModal(prevTask.parentElement, prevTask)
        }
      };

      const rightArrowBtn = document.createElement('button');
      rightArrowBtn.textContent = '→';
      rightArrowBtn.onclick = () => {
        const nextTask = getPrevAndNextTasks(modal, 1)
        if (nextTask) {
          closeModal(modal.parentElement, modal);
          openModal(nextTask.parentElement, nextTask)
        }
      };

      ctl.appendChild(closeBtn);
      ctl.appendChild(leftArrowBtn);
      ctl.appendChild(rightArrowBtn);
    }


    sections.forEach((section) => {
      const sectionDiv = document.createElement('div');
      sectionDiv.style.marginBottom = '20px';

      const headerDiv = document.createElement('div');
      headerDiv.style.display = 'flex';
      headerDiv.style.alignItems = 'center';
      headerDiv.style.marginTop = '20px';
      headerDiv.style.marginBottom = '10px';

      const headerTitle = document.createElement('span');  // Changed to 'span' to prevent potential line breaks
      headerTitle.style.flexGrow = '1';  // Allow the header title to take up the remaining space
      headerTitle.innerHTML = section.header + ':';
      headerDiv.appendChild(headerTitle);

      const copyBtn = document.createElement('button');
      copyBtn.textContent = 'Copy';
      copyBtn.style.marginLeft = '10px';
      copyBtn.onclick = () => {
        contentDiv.select();
        document.execCommand('copy');
      };
      headerDiv.appendChild(copyBtn);

      sectionDiv.appendChild(headerDiv);

      const contentDiv = document.createElement('textarea');
      contentDiv.style.width = 'calc(100% - 20px)';
      contentDiv.style.height = 'auto';
      contentDiv.style.padding = '10px';
      contentDiv.style.whiteSpace = 'pre';
      contentDiv.readOnly = true;
      contentDiv.rows = Math.min(section.content.split('\n').length, 30);
      contentDiv.style.resize = 'none';
      contentDiv.value = section.content;
      sectionDiv.appendChild(contentDiv);

      modal.appendChild(sectionDiv);
    });

    return modal;
  }

  function createShowDetail(taskDetail) {
    const btn = document.createElement('button');
    btn.textContent = 'Detail';
    btn.onclick = () => openModal(btn.parentElement, taskDetail);
    return btn;
  }

  function createSubTaskList(task, level) {
    const subTaskList = document.createElement('div');
    subTaskList.style.display = 'none';

    task.subTasks.forEach(subTask => {
      const subTaskTree = createTree(subTask, level + 1);
      subTaskTree.taskData = subTask;
      subTaskList.appendChild(subTaskTree);
    });

    freshSubTaskList(subTaskList, 'notSuccess')

    return subTaskList;
  }

  function createSubTaskFilterBtn(subTaskList) {
    let filterModeIndex = 0;
    const displayModes = [
      {label: 'Filter: Show Not Success', mode: 'notSuccess'},
      {label: 'Filter: Show All', mode: 'show'},
      {label: 'Filter: Hide All', mode: 'hide'},
    ];

    const btn = document.createElement('button');
    btn.textContent = displayModes[filterModeIndex].label;
    btn.style.fontSize = '8pt';

    const input = document.createElement('input');
    input.placeholder = 'Filter by keyword';
    input.style.marginLeft = '2px';
    input.style.fontSize = '8pt';

    input.addEventListener('input', () => {
      freshSubTaskList(subTaskList, displayModes[filterModeIndex].mode, input.value);
    });

    btn.onclick = () => {
      filterModeIndex = (filterModeIndex + 1) % displayModes.length;
      btn.textContent = displayModes[filterModeIndex].label;

      const mode = displayModes[filterModeIndex].mode

      freshSubTaskList(subTaskList, mode, input.value);
    };

    const filterDiv = document.createElement('div');
    filterDiv.style.display = 'flex';
    filterDiv.style.alignItems = 'center';
    filterDiv.style.flexDirection = 'column';
    filterDiv.style.width = '150px';
    filterDiv.style.padding = '5px';
    filterDiv.style.border = '1px solid #ccc';
    filterDiv.style.borderRadius = '5px';

    filterDiv.appendChild(btn);
    filterDiv.appendChild(input);

    return filterDiv;
  }

  function createLabelValuePair(label, value) {
    const pairDiv = document.createElement('div');
    pairDiv.className = 'label-value-pair';

    const labelDiv = document.createElement('div');
    labelDiv.className = 'label';
    labelDiv.textContent = label;

    const valueDiv = document.createElement('div');
    valueDiv.className = 'value';
    valueDiv.textContent = value;

    pairDiv.appendChild(labelDiv);
    pairDiv.appendChild(valueDiv);

    return pairDiv;
  }

  function freshSubTaskList(subTaskList, mode, keyword = '') {
    subTaskList.style.display = mode === 'hide' ? 'none' : 'block';
    Array.from(subTaskList.children).forEach(subTaskDiv => {
      const subTask = subTaskDiv.taskData;
      if (mode === 'hide') {
        subTaskDiv.style.display = 'none';
      } else if (mode === 'show') {
        subTaskDiv.style.display = 'block';
      } else {
        subTaskDiv.style.display = subTask.status !== 'Success' ? 'block' : 'none';
      }

      if (keyword) {
        subTaskDiv.style.display = subTask.logs && subTask.logs.some(log => log.includes(keyword)) ? 'block' : 'none';
      }
    });
  }

  function getStatusClass(status) {
    if (status === 'Success') {
      return 'task-status-success';
    } else if (status === 'Panic' || status === 'Error') {
      return 'task-status-failed';
    } else {
      return 'task-status-other';
    }
  }

  function displayTaskRunDetails(data) {
    const taskTreeDiv = document.getElementById('taskTree');
    if (data.success && data.data) {
      taskTreeDiv.appendChild(createTree(data.data));
    } else {
      taskTreeDiv.textContent = 'Error fetching task details.';
    }
  }

  async function fetchTaskRunDetails(taskRunId) {
    try {
      const response = await fetch(`/api/trantor/task/run-detail/${taskRunId}`);
      const data = await response.json();
      displayTaskRunDetails(data);
    } catch (error) {
      console.error('Error fetching task run details:', error);
    }
  }

  // Parse taskRunId from URL and fetch task run details
  const urlParams = new URLSearchParams(window.location.search);
  const taskRunId = urlParams.get('taskRunId');
  if (taskRunId) {
    fetchTaskRunDetails(taskRunId);
  } else {
    console.error('No taskRunId specified in the URL.');
  }
</script>
</body>
</html>
