package io.terminus.trantor2.meta.management.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.hash.Hashing;
import io.terminus.trantor2.ide.repository.PermissionRepo;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.ViewPermissionDTO;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.permission.PermissionMeta;
import org.springframework.util.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;

/**
 * <AUTHOR>
 */
public final class MigPermUtil {

    public static Map<String, Set<String>> buildSysServiceToModels(MetaQueryService metaQueryService, MetaEditAndQueryContext ctx, List<String> viewKeys) {
        Map<String, Set<String>> sysServiceToModels = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        buildSysServiceToModelsAndApis(metaQueryService, ctx, viewKeys, sysServiceToModels, new HashSet<>());
        return sysServiceToModels;
    }

    public static void buildSysServiceToModelsAndApis(MetaQueryService metaQueryService, MetaEditAndQueryContext ctx, List<String> viewKeys, Map<String, Set<String>> sysServiceToModels, Set<String> apis) {
        List<ViewPermissionDTO> viewPermissions = metaQueryService.findViewPermissions(ctx, null, viewKeys);
        viewPermissions.forEach(viewPermission -> {
            viewPermission.getComponents().forEach(node -> {
                node.getBindSysServices().forEach(sys -> {
                    sysServiceToModels.computeIfAbsent(sys.getServiceKey(), k -> new HashSet<>()).add(sys.getModelKey());
                });
                node.getBindApis().forEach(api -> {
                    String fakeApi = api.getApiMethod() + ":" + api.getApiPath();
                    apis.add(fakeApi);
                });
                node.getVirtualComponents().forEach(virtual -> {
                    virtual.getBindSysServices().forEach(sys -> {
                        sysServiceToModels.computeIfAbsent(sys.getServiceKey(), k -> new HashSet<>()).add(sys.getModelKey());
                    });
                    virtual.getBindApis().forEach(api -> {
                        String fakeApi = api.getApiMethod() + ":" + api.getApiPath();
                        apis.add(fakeApi);
                    });
                });
            });
        });
    }

    public static boolean isPermAlreadySet(boolean regenerate, JsonNode node, String path) {
        if (regenerate) {
            return false;
        }
        JsonNode permKey = node.at(path);
        return permKey.isTextual() && StringUtils.hasText(permKey.asText());
    }

    public static void createPermIfNotExist(PermissionRepo permissionRepo, String permKey, ResourceContext rscCtx) {
        // make sure permission exists or create
        Optional<PermissionMeta> exist = permissionRepo.findOneByKey(permKey, rscCtx);
        if (!exist.isPresent()) {
            forceCreatePermission(permissionRepo, permKey, rscCtx);
        }
    }

    public static void createPermIfNotExist(PermissionRepo permissionRepo, String permKey, String permName, ResourceContext rscCtx) {
        // make sure permission exists or create
        Optional<PermissionMeta> exist = permissionRepo.findOneByKey(permKey, rscCtx);
        if (!exist.isPresent()) {
            forceCreatePermission(permissionRepo, permKey, permName, rscCtx);
        }
    }

    public static void forceCreatePermission(PermissionRepo permissionRepo, String permKey, ResourceContext rscCtx) {
        // use first 6 char of sha256 hash as part of name
        String keyHash = Hashing.sha256().hashUnencodedChars(permKey).toString().substring(0, 6);
        String permName = "权限(自动创建" + keyHash + ") " + permKey;
        if (permKey.length() > 100) {
            // keep first 30 and last 70 chars
            permName = "权限(自动创建" + keyHash + ") " + permKey.substring(0, 30) + "_" + permKey.substring(permKey.length() - 70);
        }
        forceCreatePermission(permissionRepo, permKey, permName, rscCtx);
    }

    private static void forceCreatePermission(PermissionRepo permissionRepo, String permKey, String permName, ResourceContext rscCtx) {
        // create permission
        PermissionMeta perm = new PermissionMeta();
        perm.setKey(permKey);
        perm.setName(permName);
        perm.setParentKey(KeyUtil.moduleKey(permKey));
        permissionRepo.create(perm, rscCtx);
    }
}
