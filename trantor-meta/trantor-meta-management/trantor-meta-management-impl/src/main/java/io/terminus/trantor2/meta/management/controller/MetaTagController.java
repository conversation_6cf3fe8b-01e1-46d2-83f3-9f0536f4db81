package io.terminus.trantor2.meta.management.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.feature.ComparableVersion;
import io.terminus.trantor2.console.service.ConsoleService;
import io.terminus.trantor2.iam.service.TrantorIAMUserService;
import io.terminus.trantor2.meta.api.dto.SnapshotTag;
import io.terminus.trantor2.meta.editor.confg.TrantorMetaProperties;
import io.terminus.trantor2.meta.editor.repository.TagRepository;
import io.terminus.trantor2.meta.management.model.VersionedSystemModulePO;
import io.terminus.trantor2.meta.management.repo.VersionedSystemModuleRepo;
import io.terminus.trantor2.meta.management.request.BuildTagRequest;
import io.terminus.trantor2.meta.management.service.LocalTagService;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskManager;
import io.terminus.trantor2.meta.management.vo.MetaTagVO;
import io.terminus.trantor2.meta.platform.TrantorVersionService;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.repository.ModuleRepo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.Objects;
import java.util.Optional;

import static io.terminus.trantor2.meta.management.service.SystemModuleManagerService.SYSTEM_MODULE_ARTIFACT_ID;

/**
 *
 * @author: yangyuqiang
 * @date: 2023/11/15 9:46 AM
 **/
@Tag(name = "元数据tag接口")
@Slf4j
@RestController
@RequestMapping("/api/trantor/meta/tag")
public class MetaTagController {
    private final TrantorMetaProperties properties;
    private final LocalTagService tagService;
    private final VersionedSystemModuleRepo versionRepo;

    public MetaTagController(TrantorMetaProperties properties, TagRepository tagRepo, TrantorIAMUserService trantorIAMUserService, TaskManager taskManager,
                             VersionedSystemModuleRepo systemModuleRepo, ConsoleService consoleService,
                             ModuleRepo moduleRepo, TrantorVersionService trantorVersionService) {
        this.properties = properties;
        this.versionRepo = systemModuleRepo;
        tagService = new LocalTagService(tagRepo, trantorIAMUserService, taskManager, consoleService, moduleRepo, trantorVersionService);
    }

    @Operation(summary = "版本列表")
    @GetMapping("/list")
    public Response<Paging<MetaTagVO>> list(@RequestParam(required = false) String name, @RequestParam(defaultValue = "1") Integer pageNo, @RequestParam(defaultValue = "10") Integer pageSize) {
        return Response.ok(tagService.list(TrantorContext.getTeamCode(), name, pageNo - 1, pageSize));
    }

    @GetMapping("/last")
    public Response<SnapshotTag> lastTag(@RequestParam String teamCode, @RequestParam String token) {
        checkToken(token);
        if (KeyUtil.SYS_TEAM_KEY.equals(teamCode)) {
            Optional<VersionedSystemModulePO> versionOpt = versionRepo.findAllByKeyAndReleasedTrueAndLocalTrue(SYSTEM_MODULE_ARTIFACT_ID)
                    .stream().filter(it -> it.getCompatibilityRange().containVersion("2.5.24.0430"))
                    .max(Comparator.comparing(it -> ComparableVersion.of(it.getAppVersion())));
            if (!versionOpt.isPresent()) {
                return Response.ok(null);
            } else {
                VersionedSystemModulePO version = versionOpt.get();
                if (version.getContent() == null || version.getContent().getDownloadUrl() == null || version.getContent().getRootOid() == null) {
                    return Response.ok(null);
                } else {
                    return Response.ok(new SnapshotTag(null, teamCode, version.getContent().getRootOid(), version.getAppVersion()));
                }
            }
        }
        return Response.ok(tagService.lastTag(teamCode));
    }

    @GetMapping("/zip")
    public Response<String> zipUrl(@RequestParam String teamCode, @RequestParam String snapshotOid, @RequestParam String token) {
        checkToken(token);
        return Response.ok(tagService.zipUrl(teamCode, snapshotOid));
    }

    @Operation(summary = "发布版本")
    @PostMapping("/build")
    public Response<Void> buildTag(@RequestBody BuildTagRequest request)  {
        TaskContext ctx = new TaskContext(TrantorContext.getTeamId(), TrantorContext.getTeamCode(), TrantorContext.getCurrentUserId(), false);
        tagService.buildTag(ctx, request.getVersion(), request.getCompatibilityRange(), request.getDownloadUrl());
        return Response.ok();
    }

    private void checkToken(String token) {
        if (!Objects.equals(token, properties.getToken())) {
            throw new TrantorRuntimeException("forbidden request, token is invalid");
        }
    }
}
