package io.terminus.trantor2.meta.management.service;

import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.meta.api.dto.MetaLink;
import io.terminus.trantor2.meta.api.dto.ResourceNodeLite;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.editor.util.IndexUtil;
import io.terminus.trantor2.meta.editor.util.ObjectUtil;
import io.terminus.trantor2.meta.index.MetaIndexAsset;
import io.terminus.trantor2.meta.index.MetaIndexAssetRepo;
import io.terminus.trantor2.meta.management.util.LinkUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.Collectors;

import static io.terminus.trantor2.meta.api.model.MetaNodeAccessLevel.Private;

/**
 * <AUTHOR>
 * 2023/6/6 11:26 AM
 */
@Slf4j
@RequiredArgsConstructor
public class MetaChecker {
    /**
     * 元数据检查比较消耗内存，反正内存被打爆，锁住检查操作
     */
    private static final Lock lock = new ReentrantLock();

    private final Long teamId;
    private final String teamCode;
    private final String moduleKey;
    private final MetaIndexAssetRepo indexRepo;

    /**
     * 所有元数据分析之后的所有关联的元数据key
     */
    private final Set<String> targetKeys = new HashSet<>();
    /**
     * 元数据检查单元
     * 描述元数据引用关系，存放单个元数据的所有引用
     * 以单个元数据为单位检查该元数据的所有依赖是否合法
     */
    private final List<MetaCheckUnit> units = new ArrayList<>();
    /**
     * targetKeys能查出来的所有元数据，map key即元数据key
     */
    private Map<String, MetaIndexAsset.Full> existedMetaMap;

    @Getter
    private final List<String> result = new ArrayList<>();

    @Getter
    private final Map<String, GroupedCheckResult> newResult = new HashMap<>();

    public void check() {
        lock.lock();
        try {
            //检查前准备数据
            prepareCheck();
            //元数据检查
            units.forEach(MetaCheckUnit::check);
        } finally {
            lock.unlock();
        }
    }

    private void prepareCheck() {
        if (moduleKey == null || moduleKey.isEmpty()) {
            List<MetaIndexAsset.Full> metas = indexRepo.find(teamId, MetaIndexAsset.Full.class, Cond.and());
            metas.forEach(meta -> {
                Collection<MetaLink> links = LinkUtil.analyzeLinks(IndexUtil.idx2node(meta, teamId, teamCode))
                        .stream()
                        .filter(this::needCheck)
                        .collect(Collectors.toMap(MetaLink::getTargetKey, Function.identity(), (l, r) -> l))
                        .values();
                targetKeys.addAll(links.stream().map(MetaLink::getTargetKey).collect(Collectors.toList()));
                units.add(new MetaCheckUnit(meta.getType().name(), meta.getKey(), links));
            });
            existedMetaMap = metas.stream().collect(Collectors.toMap(MetaIndexAsset.Full::getKey, Function.identity()));
            return;
        }
        indexRepo.findOne(teamId, MetaIndexAsset.Full.class, Cond.and(
                Field.type().equal(MetaType.Module.name()),
                Field.key().equal(moduleKey)
        )).orElseThrow(() -> new TrantorRuntimeException("module not found, key is " + moduleKey));
        List<MetaIndexAsset.Full> metas = indexRepo.find(teamId, MetaIndexAsset.Full.class, Cond.moduleOf(moduleKey));

        metas.forEach(meta -> {
            Collection<MetaLink> links = LinkUtil.analyzeLinks(IndexUtil.idx2node(meta, teamId, teamCode))
                    .stream()
                    .filter(this::needCheck)
                    .collect(Collectors.toMap(MetaLink::getTargetKey, Function.identity(), (l, r) -> l))
                    .values();
            targetKeys.addAll(links.stream().map(MetaLink::getTargetKey).collect(Collectors.toList()));
            units.add(new MetaCheckUnit(meta.getType().name(), meta.getKey(), links));
        });
        existedMetaMap = indexRepo.find(teamId, MetaIndexAsset.Full.class, Field.key().in(targetKeys)).stream().collect(Collectors.toMap(MetaIndexAsset.Full::getKey, Function.identity()));
    }

    private boolean needCheck(MetaLink metaLink) {
        if (Objects.equals(metaLink.getTargetKey(), "null")
                || Objects.equals(metaLink.getTargetKey(), "$context")
                || Objects.equals(metaLink.getTargetType(), LinkUtil.FAKE_SYS_SERVICE)
                || Objects.equals(metaLink.getTargetType(), LinkUtil.FAKE_API)
        ) {
            return false;
        }
        return true;
    }

    @AllArgsConstructor
    private class MetaCheckUnit {
        private String type;
        private String key;
        private Collection<MetaLink> relatedMeta;

        public void check() {
            if (CollectionUtils.isEmpty(relatedMeta)) {
                return;
            }
            if (KeyUtil.isInvalidKey(key)) {
                return;
            }
            String moduleKey = KeyUtil.moduleKey(key);
            MetaIndexAsset.Full module = existedMetaMap.get(moduleKey);
            if (module == null) {
                return;
            }
            if (!Objects.equals(module.getType(), MetaType.Module.name())) {
                return;
            }
            List<String> errors = new ArrayList<>();
            relatedMeta.forEach(related -> {
                if (existedMetaMap.containsKey(related.getTargetKey())) {
                    //判断是否跨模块依赖私有资源
                    MetaIndexAsset.Full meta = existedMetaMap.get(related.getTargetKey());
                    if (!Objects.equals(KeyUtil.moduleKey(key), KeyUtil.moduleKey(meta.getKey())) && Objects.equals(meta.getAccess(), Private)) {
                        String message = "%s%s引用的%s%s是其他模块的私有资源";
                        errors.add(String.format(message, ObjectUtil.metaTypeToName(type), key, ObjectUtil.metaTypeToName(meta.getType().name()), meta.getKey()));
                    }
                } else {
                    //依赖的资源不存在
                    String message = "%s%s引用的%s%s没找到";
                    errors.add(String.format(message, ObjectUtil.metaTypeToName(type), key, ObjectUtil.metaTypeToName(related.getTargetType()), related.getTargetKey()));
                }
            });
            if (errors.isEmpty()) {
                return;
            }
            result.addAll(errors);
            MetaIndexAsset.Full cur = existedMetaMap.get(key);
            if (cur == null) {
                return;
            }
            CheckResultItem checkResultItem = new CheckResultItem();
            checkResultItem.setNode(IndexUtil.idx2lite(cur));
            checkResultItem.setErrors(errors);
            GroupedCheckResult groupedCheckResult = newResult.computeIfAbsent(moduleKey, k -> {
                GroupedCheckResult grp = new GroupedCheckResult();
                grp.setModuleKey(moduleKey);
                grp.setModuleName(module.getName());
                grp.setAppId(module.getId());
                grp.setItems(new HashMap<>());
                return grp;
            });
            String path = getPath(cur);
            groupedCheckResult.getItems().computeIfAbsent(path, k -> new ArrayList<>()).add(checkResultItem);
        }
    }

    private String getPath(MetaIndexAsset.Full cur) {
        String path = cur.getName();
        MetaIndexAsset.Full parent = existedMetaMap.get(cur.getParentKey());
        while (parent != null && !MetaType.FolderRoot.name().equals(parent.getType())) {
            path = parent.getName() + "/" + path;
            parent = existedMetaMap.get(parent.getParentKey());
        }
        return path;
    }

    @Data
    public static class GroupedCheckResult {
        private String moduleKey;
        private String moduleName;
        @Deprecated
        private Long appId;
        private Map<String, List<CheckResultItem>> items;
    }

    @Data
    public static class CheckResultItem {
        private ResourceNodeLite node;
        private List<String> errors;
    }
}
