package io.terminus.trantor2.meta.management.service;

import io.terminus.trantor.workflow.runtime.v2.model.dto.WorkflowDTO;
import io.terminus.trantor.workflow.sdk.core.Workflow;
import io.terminus.trantor.workflow.sdk.model.request.QueryWorkflowDefinitionBySceneRequest;
import io.terminus.trantor.workflow.sdk.model.response.QueryWorkflowDefinitionBySceneResponse;
import io.terminus.trantor2.lang.meta.Structure;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.editor.util.ObjectUtil;
import io.terminus.trantor2.meta.index.MetaIndexAsset;
import io.terminus.trantor2.meta.index.MetaIndexAssetRepo;
import io.terminus.trantor2.meta.index.MetaIndexRef;
import io.terminus.trantor2.meta.index.MetaIndexRefRepo;
import io.terminus.trantor2.meta.index.RefCond;
import io.terminus.trantor2.meta.index.TempTree;
import io.terminus.trantor2.meta.management.util.PathUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Nullable;
import java.text.Collator;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Queue;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MetaUsageResolver {
    private final MetaIndexAssetRepo metaIndexAssetRepo;
    private final MetaIndexRefRepo metaIndexRefRepo;
    private final MetaQueryService metaQueryService;
    private final Workflow workflow;

    public TempTree findTree(
            Long teamId,
            Collection<String> moduleKeys,
            boolean expandInner
    ) {
        return findTree(teamId, moduleKeys, expandInner, null);
    }

    public TempTree findTree(
            Long teamId,
            Collection<String> moduleKeys,
            boolean expandInner,
            @Nullable Cond customCond
    ) {
        if (moduleKeys.isEmpty()) {
            return new TempTree();
        }
        // 如果提供了 customCond，直接使用它（假设它已包含所有必要的条件）
        // 否则使用默认的条件组合
        Cond finalCond;
        if (customCond != null) {
            finalCond = customCond;
        } else {
            finalCond = Cond.and(
                    Cond.or(
                            Field.type().equal(MetaType.Module.name()),
                            Field.type().equal(MetaType.FolderRoot.name()),
                            Field.type().equal(MetaType.Folder.name()),
                            Field.type().in(MetaType.getSearchableTypes())
                    ),
                    Field.moduleKey().in(moduleKeys)
            );
        }
        
        Map<String, MetaIndexAsset.Base> metaMap = metaIndexAssetRepo.find(teamId, MetaIndexAsset.Base.class, finalCond)
                .stream().collect(Collectors.toMap(MetaIndexAsset.Base::getKey, it -> it));
        TempTree tree = cleanAndSort(buildTree(moduleKeys, metaMap));
        if (tree.getModules() != null) {
            for (TempTree.Module module : tree.getModules()) {
                autoPath(module.getChildren(), "");
            }
        }
        return tree;
    }

    private TempTree buildTree(Collection<String> moduleKeys, Map<String, MetaIndexAsset.Base> metaMap) {
        Map<String, Set<String>> parent2children = new HashMap<>();
        for (MetaIndexAsset.Base asset : metaMap.values()) {
            String parentKey = asset.getParentKey();
            parent2children.computeIfAbsent(parentKey, k -> new HashSet<>()).add(asset.getKey());
        }
        TempTree tree = new TempTree();
        for (String moduleKey : moduleKeys) {
            MetaIndexAsset.Base module = metaMap.get(moduleKey);
            if (module == null) {
                continue;
            }
            TempTree.Module moduleNode = new TempTree.Module();
            moduleNode.setKey(module.getKey());
            moduleNode.setName(module.getName());
            moduleNode.setChildren(new ArrayList<>());
            tree.getModules().add(moduleNode);
            Set<String> children = parent2children.get(moduleKey);
            if (children != null) {
                for (String child : children) {
                    TempTree.TreeNode childNode = buildTreeNode(child, parent2children, metaMap);
                    if (childNode != null) {
                        moduleNode.getChildren().add(childNode);
                    }
                }
            }
        }
        return tree;
    }

    private TempTree.TreeNode buildTreeNode(String curKey, Map<String, Set<String>> parent2children, Map<String, MetaIndexAsset.Base> metaMap) {
        MetaIndexAsset.Base asset = metaMap.get(curKey);
        if (asset == null) {
            return null;
        }
        TempTree.TreeNode node = TempTree.newNode(asset.getKey(), asset.getType().name(), asset.getSubType(), asset.getName());
        if (ObjectUtil.isNotZero(asset.getExtOid())) {
            node.setExtended(true);
        }
        Set<String> children = parent2children.get(curKey);
        if (children != null) {
            for (String child : children) {
                TempTree.TreeNode childNode = buildTreeNode(child, parent2children, metaMap);
                if (childNode != null) {
                    node.getChildren().add(childNode);
                }
            }
        }
        return node;
    }

    private TempTree cleanAndSort(TempTree tree) {
        if (tree.getModules() == null) {
            return tree;
        }
        for (TempTree.Module module : tree.getModules()) {
            if (module.getChildren() == null || module.getChildren().isEmpty()) {
                continue;
            }
            List<TempTree.TreeNode> newChildren = new ArrayList<>();
            for (TempTree.TreeNode child : module.getChildren()) {
                if (child.getType().equals(MetaType.FolderRoot.name())) {
                    newChildren.addAll(child.getChildren());
                } else {
                    newChildren.add(child);
                }
            }
            module.setChildren(newChildren);
            // sort all children by name
            Queue<List<TempTree.TreeNode>> process = new LinkedList<>();
            process.add(newChildren);
            while (!process.isEmpty()) {
                List<TempTree.TreeNode> children = process.poll();
                children.sort((l, r) -> {
                    String lType = Optional.ofNullable(l.getType()).orElse("");
                    String rType = Optional.ofNullable(r.getType()).orElse("");
                    // first type, second name
                    if (lType.equals(rType)) {
                        String lName = Optional.ofNullable(l.getName()).orElse("");
                        String rName = Optional.ofNullable(r.getName()).orElse("");
                        return Collator.getInstance(Locale.CHINESE).compare(lName, rName);
                    }
                    // Folder type is always first
                    if (lType.equals(MetaType.Folder.name())) {
                        return -1;
                    }
                    if (rType.equals(MetaType.Folder.name())) {
                        return 1;
                    }
                    return lType.compareTo(rType);
                });
                for (TempTree.TreeNode child : children) {
                    if (child.getChildren() == null || child.getChildren().isEmpty()) {
                        continue;
                    }
                    process.add(child.getChildren());
                }
            }
        }
        return tree;
    }

    public TempTree findUsageTree(
            Long teamId,
            String key,
            @Nullable String innerKey,
            MetaUsageDirection direction,
            boolean expandInner
    ) {
        List<Node> nodes = new ArrayList<>();
        if (direction == MetaUsageDirection.Forward) {
            List<Structure.Ref> refs = metaIndexAssetRepo.findOne(teamId, MetaIndexAsset.Lite.class, Field.key().equal(key))
                    .map(MetaIndexAsset.Lite::getStructure)
                    .map(Structure::getRefs)
                    .orElse(new ArrayList<>());
            refs.stream()
                    .filter(ref -> {
                        if (StringUtils.isBlank(innerKey)) {
                            return true;
                        }
                        return innerKey.equals(ref.getSourceInnerKey());
                    })
                    .map(ref -> {
                        Node node = new Node();
                        node.setKey(ref.getTargetKey());
                        node.setType(ref.getTargetType());
                        node.setInnerKey(ref.getTargetInnerKey());
                        node.setInnerType(ref.getTargetInnerType());
                        return node;
                    })
                    .forEach(nodes::add);
        } else {
            Set<String> sourceKeys = metaIndexRefRepo.find(teamId, RefCond.builder().targetKey(key).build()).stream()
                    .map(MetaIndexRef::getSourceKey)
                    .collect(Collectors.toSet());
            // TODO: how to handle not found sourceKeys
            List<MetaIndexAsset.Lite> assets = metaIndexAssetRepo.find(teamId, MetaIndexAsset.Lite.class, Field.key().in(sourceKeys));
            for (MetaIndexAsset.Lite asset : assets) {
                List<Structure.Ref> refs = Optional.ofNullable(asset.getStructure()).map(Structure::getRefs).orElse(new ArrayList<>());
                refs.stream()
                        .filter(ref -> ref.getTargetKey().equals(key))
                        .filter(ref -> {
                            if (StringUtils.isBlank(innerKey)) {
                                return true;
                            }
                            return innerKey.equals(ref.getTargetInnerKey());
                        })
                        .map(ref -> {
                            Node node = new Node();
                            node.setKey(asset.getKey());
                            node.setType(asset.getType().name());
                            node.setInnerKey(ref.getSourceInnerKey());
                            node.setInnerType(ref.getSourceInnerType());
                            return node;
                        })
                        .forEach(nodes::add);
            }
        }
        nodes = nodes.stream().filter(it -> {
            if (expandInner) {
                return true;
            }
            return StringUtils.isBlank(it.getInnerKey());
        }).collect(Collectors.toList());
        TempTree tree = cleanAndSort(buildTree(teamId, nodes));
        tryAppendFakeWorkflowModule(tree, teamId, key, direction);
        return tree;
    }

    private void tryAppendFakeWorkflowModule(TempTree tree, Long teamId, String key, MetaUsageDirection direction) {
        if (direction != MetaUsageDirection.Backward) {
            return;
        }
        MetaIndexAsset.Lite scene = metaIndexAssetRepo.findOne(teamId, MetaIndexAsset.Lite.class, Field.key().equal(key)).orElse(null);
        if (scene == null) {
            return;
        }
        if (scene.getType() != MetaType.Scene) {
            return;
        }
        List<WorkflowDTO> workflows = getSceneWorkflows(teamId, key);
        if (workflows.isEmpty()) {
            return;
        }
        Map<String, TempTree.TreeNode> nodes = new HashMap<>();
        workflows.forEach(workflowDTO -> {
            TempTree.TreeNode groupNode = nodes.get(workflowDTO.getWorkflowGroupKey());
            if (groupNode == null) {
                groupNode = TempTree.newNode(workflowDTO.getWorkflowGroupKey(), MetaType.WorkflowGroup.name(), null, null);
                nodes.put(workflowDTO.getWorkflowGroupKey(), groupNode);
            }
            TempTree.TreeNode workflowNode = TempTree.newNode(
                    workflowDTO.getWorkflowKey(),
                    "Workflow", null,
                    workflowDTO.getName());
            groupNode.getChildren().add(workflowNode);
        });
        metaIndexAssetRepo.find(teamId, MetaIndexAsset.Lite.class, Field.key().in(nodes.keySet())).forEach(asset -> {
            TempTree.TreeNode node = nodes.get(asset.getKey());
            if (node != null) {
                node.setName(asset.getName());
            }
        });
        TempTree.Module fakeModule = new TempTree.Module();
        fakeModule.setKey("__fake__workflow_module__");
        fakeModule.setName("审批流");
        fakeModule.setChildren(new ArrayList<>(nodes.values()));
        tree.getModules().add(fakeModule);
    }

    private List<WorkflowDTO> getSceneWorkflows(Long teamId, String key) {
        QueryWorkflowDefinitionBySceneRequest request = new QueryWorkflowDefinitionBySceneRequest();
        request.setTeamId(teamId);
        request.setSceneCode(key);
        QueryWorkflowDefinitionBySceneResponse resp = workflow.execute(request);
        if (!resp.getSuccess()) {
            log.error("query workflow failed: code={}, message={}", resp.getErrCode(), resp.getErrMsg());
            return Collections.emptyList();
        }
        if (resp.getWorkflowDTOList() == null) {
            return Collections.emptyList();
        }
        return resp.getWorkflowDTOList();
    }

    private void getAllSceneKeys(TempTree.TreeNode node, Set<String> sceneKeys) {
        if (node.getType().equals(MetaType.Scene.name())) {
            sceneKeys.add(node.getKey());
            return;
        }
        for (TempTree.TreeNode child : node.getChildren()) {
            getAllSceneKeys(child, sceneKeys);
        }
    }

    private Set<String> getAllSceneKeys(TempTree tree) {
        Set<String> sceneKeys = new HashSet<>();
        for (TempTree.Module module : tree.getModules()) {
            for (TempTree.TreeNode child : module.getChildren()) {
                getAllSceneKeys(child, sceneKeys);
            }
        }
        return sceneKeys;
    }

    private void appendFakeWorkflowUsageCount(Map<String, Integer> usageCount, Long teamId, TempTree tree) {
        Set<String> sceneKeys = getAllSceneKeys(tree);
        if (sceneKeys.isEmpty()) {
            return;
        }
        for (String sceneKey : sceneKeys) {
            List<WorkflowDTO> workflows = getSceneWorkflows(teamId, sceneKey);
            if (workflows.isEmpty()) {
                continue;
            }
            usageCount.put(sceneKey, usageCount.getOrDefault(sceneKey, 0) + workflows.size());
        }
    }

    public TempTree filterTreeUnused(Long teamId, String moduleKey, String path) {
        TempTree tree = findTree(teamId, Collections.singletonList(moduleKey), false);
        Map<String, Integer> usageCount = metaIndexRefRepo.countByTargetKeys(teamId);
        appendFakeWorkflowUsageCount(usageCount, teamId, tree);
        tree.getModules().forEach(module -> {
            removeUnrelatedFolder(module.getChildren(), PathUtil.buildPath(path), 0);
            removeHaveUsedMeta(module.getChildren(), usageCount);
            //clearFolderKey(module.getChildren());
        });
        return tree;
    }

    private void autoPath(List<TempTree.TreeNode> children, String parentPath) {
        if (children == null || children.isEmpty()) {
            return;
        }
        for (TempTree.TreeNode child : children) {
            String path = parentPath;
            if (child.getType().equals(MetaType.Folder.name())) {
                path += "/" + child.getName();
            }
            child.setPath(path);
            autoPath(child.getChildren(), path);
        }
    }

    private void removeUnrelatedFolder(List<TempTree.TreeNode> children, String[] path, int depth) {
        if (children == null || children.isEmpty()) {
            return;
        }
        if (depth >= path.length) {
            return;
        }
        String folderName = path[depth];
        List<TempTree.TreeNode> newChildren = new ArrayList<>();
        for (TempTree.TreeNode child : children) {
            if (child.getType().equals(MetaType.Folder.name()) && child.getName().equals(folderName)) {
                newChildren.add(child);
                removeUnrelatedFolder(child.getChildren(), path, depth + 1);
            }
        }
        children.clear();
        children.addAll(newChildren);
    }

    private void removeHaveUsedMeta(List<TempTree.TreeNode> children, Map<String, Integer> usageCount) {
        if (children == null || children.isEmpty()) {
            return;
        }
        List<TempTree.TreeNode> newChildren = new ArrayList<>();
        for (TempTree.TreeNode child : children) {
            if (child.getType().equals(MetaType.Folder.name())) {
                removeHaveUsedMeta(child.getChildren(), usageCount);
                // if folder has children, keep it, otherwise remove it
                if (!child.getChildren().isEmpty()) {
                    newChildren.add(child);
                }
            } else {
                if (usageCount.getOrDefault(child.getKey(), 0) == 0) {
                    newChildren.add(child);
                }
            }
        }
        children.clear();
        children.addAll(newChildren);
    }

    private void clearFolderKey(List<TempTree.TreeNode> children) {
        if (children == null || children.isEmpty()) {
            return;
        }
        for (TempTree.TreeNode child : children) {
            if (child.getType().equals(MetaType.Folder.name())) {
                child.setKey(null);
            }
            clearFolderKey(child.getChildren());
        }
    }

    @Data
    class Node {
        private String key;
        private String type;
        private String innerKey;
        private String innerType;
    }

    private TempTree buildTree(Long teamId, List<Node> nodes) {
        TempTree tree = new TempTree();
        Map<String, List<Node>> module2nodes = nodes.stream().collect(Collectors.groupingBy(it -> KeyUtil.moduleKey(it.getKey())));
        module2nodes.forEach((moduleKey, moduleNodes) -> {
            MetaIndexAsset.Base module = metaIndexAssetRepo.findOne(teamId, MetaIndexAsset.Base.class, Field.key().equal(moduleKey)).orElse(null);
            if (module == null) {
                // TODO: display module not found
                return;
            }
            TempTree.Module moduleNode = new TempTree.Module();
            moduleNode.setKey(module.getKey());
            moduleNode.setName(module.getName());
            moduleNode.setChildren(new ArrayList<>());
            tree.getModules().add(moduleNode);
            Map<String, MetaIndexAsset.Base> allFolders = metaIndexAssetRepo.find(teamId, MetaIndexAsset.Base.class,
                    Field.moduleKey().equal(moduleKey).and(Field.type().in(MetaType.Folder.name(), MetaType.FolderRoot.name()))
            ).stream().collect(Collectors.toMap(MetaIndexAsset.Base::getKey, it -> it));

            Set<String> allKeys = new HashSet<>();
            for (Node node : moduleNodes) {
                allKeys.add(node.getKey());
            }
            Map<String, TempTree.TreeNode> result = new HashMap<>();
            for (String key : allKeys) {
                buildPath(teamId, moduleKey, key, allFolders, result);
            }
            TempTree.TreeNode fakeNode = result.get(moduleKey);
            if (fakeNode == null) {
                return;
            }
            moduleNode.getChildren().addAll(fakeNode.getChildren());

            Map<String, Set<String>> innerKeys = new HashMap<>();
            for (Node node : moduleNodes) {
                if (StringUtils.isBlank(node.getInnerKey())) {
                    continue;
                }
                innerKeys.computeIfAbsent(node.getKey(), k -> new HashSet<>()).add(node.getInnerKey());
            }
            innerKeys.forEach((key, innerKeySet) -> {
                TempTree.TreeNode node = result.get(key);
                if (node == null) {
                    return;
                }
                MetaIndexAsset.Lite asset = metaIndexAssetRepo.findOne(teamId, MetaIndexAsset.Lite.class, Field.key().equal(key)).orElse(null);
                if (asset == null) {
                    return;
                }
                Map<String, Structure.InnerNode> innerNodes = Optional.ofNullable(asset.getStructure())
                        .map(Structure::getInnerNodes)
                        .orElse(new ArrayList<>())
                        .stream()
                        .filter(it -> it.getInnerKey() != null)
                        .collect(Collectors.toMap(Structure.InnerNode::getInnerKey, it -> it, (existing, replacement) -> existing));
                Map<String, TempTree.TreeNode> innerResult = new HashMap<>();
                for (String innerKey : innerKeySet) {
                    buildInnerPath(innerKey, innerNodes, innerResult);
                }
                TempTree.TreeNode fakeInnerNode = innerResult.get("");
                if (fakeInnerNode == null) {
                    return;
                }
                node.getChildren().addAll(fakeInnerNode.getChildren());
            });
        });
        return tree;
    }

    private void buildPath(Long teamId, String moduleKey, String curKey, Map<String, MetaIndexAsset.Base> folderMap, Map<String, TempTree.TreeNode> result) {
        if (result.containsKey(curKey)) {
            // already added
            return;
        }
        if (Objects.equals(curKey, moduleKey)) {
            TempTree.TreeNode fakeNode = TempTree.newNode(curKey, MetaType.Module.name(), null, null);
            result.put(curKey, fakeNode);
            return;
        }
        MetaIndexAsset.Base asset = folderMap.get(curKey);
        if (asset == null) {
            // not folder
            asset = metaIndexAssetRepo.findOne(teamId, MetaIndexAsset.Base.class, Field.key().equal(curKey)).orElse(null);
        }
        if (asset == null) {
            // still not found
            // TODO: display asset not found
            return;
        }
        String parentKey = asset.getParentKey();
        buildPath(teamId, moduleKey, parentKey, folderMap, result);
        TempTree.TreeNode parent = result.get(parentKey);
        if (parent != null) {
            TempTree.TreeNode treeNode = TempTree.newNode(asset.getKey(), asset.getType().name(), asset.getSubType(), asset.getName());
            if (ObjectUtil.isNotZero(asset.getExtOid())) {
                treeNode.setExtended(true);
            }
            parent.getChildren().add(treeNode);
            result.put(asset.getKey(), treeNode);
        }
    }

    private void buildInnerPath(String curInnerKey, Map<String, Structure.InnerNode> innerNodes, Map<String, TempTree.TreeNode> result) {
        if (result.containsKey(curInnerKey)) {
            // already added
            return;
        }
        if (StringUtils.isBlank(curInnerKey)) {
            TempTree.TreeNode fakeRoot = TempTree.newInnerNode("", "", "");
            result.put("", fakeRoot);
            return;
        }
        Structure.InnerNode cur = innerNodes.get(curInnerKey);
        // TODO: display inner node not found
        if (cur == null) {
            return;
        }
        buildInnerPath(cur.getInnerParentKey(), innerNodes, result);
        TempTree.TreeNode parent = result.get(cur.getInnerParentKey());
        if (parent != null) {
            TempTree.TreeNode treeNode = TempTree.newInnerNode(cur.getInnerKey(), cur.getInnerType(), cur.getInnerName());
            parent.getChildren().add(treeNode);
            result.put(cur.getInnerKey(), treeNode);
        }
    }
}
