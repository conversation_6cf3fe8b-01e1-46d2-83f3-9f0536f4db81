package io.terminus.trantor2.meta.management.task.composite;

import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.service.QueryOp;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskDefine;
import io.terminus.trantor2.meta.management.task.TaskInternalException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class TestParserForTeam extends BaseTask<TestParserForTeam.Options> {
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
        private String resourceNodeType;
    }

    @Override
    public void exec(Options opts, List<TaskDefine> subTasks, List<String> outputs, TaskContext ctx) {
        QueryOp q = metaQueryService.queryInTeam(ctx.getTeamCode());
        switch (opts.resourceNodeType) {
            case "View":
                q.findAll(Field.type().equal(opts.resourceNodeType)).forEach(node -> {
                    TestParserSingleResourceNode.Options subOpts = new TestParserSingleResourceNode.Options();
                    subOpts.setResourceNodeKey(node.getKey());
                    subTasks.add(
                            new TaskDefine(TestParserSingleResourceNode.class, subOpts, "", false)
                    );
                });
                break;
            default:
                throw new TaskInternalException("Unknown parser type: " + opts.resourceNodeType);
        }
    }
}
