package io.terminus.trantor2.meta.management.task.primitive;

import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.MoveTarget;
import io.terminus.trantor2.meta.api.dto.MoveTargetType;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.blob.MetaBlob;
import io.terminus.trantor2.meta.blob.MetaBlobRepo;
import io.terminus.trantor2.meta.editor.util.IndexUtil;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskDefine;
import io.terminus.trantor2.meta.object.MetaObjectV2;
import io.terminus.trantor2.meta.util.EditUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * @author: yangyuqiang
 * @date: 2023/11/14 5:23 PM
 **/
@Slf4j
@Component
public class RecoverMetaTask extends BaseTask<RecoverMetaTask.Options> {
    @Autowired
    private MetaBlobRepo metaBlobRepo;

    @Override
    public void exec(Options opts, List<TaskDefine> subTasks, List<String> outputs, TaskContext ctx) {
        metaObjectRepo.findByOid(ctx.getTeamId(), opts.getOid()).map(MetaObjectV2::getContentStruct).ifPresent(obj -> {
            MetaEditAndQueryContext context = EditUtil.newCtx(ctx.getTeamId(), ctx.getUserId());

            MetaTreeNodeExt metaTreeNodeExt = metaBlobRepo.findOne(ctx.getTeamId(), MetaBlob.Full.class, obj.getKey())
                    // TODO: idx2node will cause ExtNode type and key change
                    .map(blob -> IndexUtil.blob2node(blob, ctx.getTeamId(), ctx.getTeamCode()))
                    .orElse(null);
            if (Objects.isNull(metaTreeNodeExt)) {
                MetaTreeNode metaTreeNode = new MetaTreeNode();
                metaTreeNode.setProps(obj.getProps());
                metaTreeNode.setType(obj.getType());
                metaTreeNode.setName(obj.getName());
                metaTreeNode.setParentKey(obj.getParentKey());
                metaTreeNode.setKey(obj.getKey());
                metaTreeNode.setAccess(obj.getAccess());
                metaTreeNode.setDescription(obj.getDescription());
                metaEditService.submitOp(context, EditUtil.createNodeOp(metaTreeNode, new MoveTarget(obj.getParentKey(), MoveTargetType.ChildFirst)));
                return;
            }
            metaTreeNodeExt.setName(obj.getName());
            metaTreeNodeExt.setDescription(obj.getDescription());
            metaTreeNodeExt.setAccess(obj.getAccess());
            metaTreeNodeExt.setProps(obj.getProps());
            metaEditService.submitOp(context, EditUtil.updateNodeOp(metaTreeNodeExt));
        });
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
        private String oid;
    }
}
