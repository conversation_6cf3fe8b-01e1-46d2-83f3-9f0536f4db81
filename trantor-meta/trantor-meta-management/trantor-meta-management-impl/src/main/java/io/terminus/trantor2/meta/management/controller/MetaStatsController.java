package io.terminus.trantor2.meta.management.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.meta.index.MetadataStats;
import io.terminus.trantor2.meta.management.service.MetaStatsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "元数据统计信息接口")
@Slf4j
@RestController
@RequestMapping("/api/trantor/meta/stats")
@RequiredArgsConstructor
public class MetaStatsController {
    private final MetaStatsService statsService;

    @Operation(summary = "获取按模块统计的元数据总量")
    @GetMapping("/modules")
    public Response<List<MetadataStats.ModuleStats>> getModuleMetadataStats(@Parameter(description = "为空时使用上下文 team")
                                                                                  @RequestParam(required = false) Long teamId) {
        List<MetadataStats.ModuleStats> moduleStats = statsService.getModuleMetadataStats(teamId);
        return Response.ok(moduleStats);
    }

    @Operation(summary = "获取按类型统计的元数据总量")
    @GetMapping("/types")
    public Response<List<MetadataStats.TypeStats>> getTypeMetadataStats(@Parameter(description = "为空时使用上下文 team")
                                                                              @RequestParam(required = false) Long teamId,
                                                                              @Parameter(description = "为空时按类型统计统计项目下元数据")
                                                                              @RequestParam(required = false) String moduleKey) {
        List<MetadataStats.TypeStats> typeStats = statsService.getTypeMetadataStats(teamId, moduleKey);
        return Response.ok(typeStats);
    }
}
