package io.terminus.trantor2.meta.management.task.composite;

import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskDefine;
import io.terminus.trantor2.meta.management.task.primitive.CleanObjectTask;
import io.terminus.trantor2.meta.management.task.primitive.DailyMarkObjectTask;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Slf4j
@Component
public class DailyGCTask extends BaseTask<DailyGCTask.Options> {

    @Override
    public void exec(Options opts, List<TaskDefine> subTasks, List<String> outputs, TaskContext ctx) {
        DailyMarkObjectTask.Options options = new DailyMarkObjectTask.Options();
        options.setLastTime(opts.getLastTime());
        subTasks.add(new TaskDefine(DailyMarkObjectTask.class, options, "", true));
        subTasks.add(new TaskDefine(CleanObjectTask.class, new CleanObjectTask.Options(), "", true));
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
        private Date lastTime;
    }
}
