package io.terminus.trantor2.meta.management.task.primitive;

import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskDefine;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 **/
@Component
public class FixMetaTask extends BaseTask<FixMetaTask.Options> {

    @Override
    public void exec(Options opts, List<TaskDefine> subTasks, List<String> outputs, TaskContext ctx) {
        //step 1: fix meta children
//        FixMetaChildrenTask.Options fixChildrenOps = new FixMetaChildrenTask.Options();
//        fixChildrenOps.setBranchId(opts.getBranchId());
//        subTasks.add(new TaskDefine(FixMetaChildrenTask.class, fixChildrenOps, "", false));

        //step2: reset oid
        MetaOidResetTask.Options options = new MetaOidResetTask.Options();
        subTasks.add(new TaskDefine(MetaOidResetTask.class, options, "", false));
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
    }
}
