package io.terminus.trantor2.meta.management.service;

import io.terminus.trantor2.application.ApplicationType;
import io.terminus.trantor2.application.dto.AppItemWithVersionCreateRequest;
import io.terminus.trantor2.application.dto.VersionedAppItemSaveRequest;
import io.terminus.trantor2.application.item.EmptyBaseInfo;
import io.terminus.trantor2.application.item.sysmodule.SystemModuleContent;
import io.terminus.trantor2.application.repo.AbstractTrantorAppRepo;
import io.terminus.trantor2.application.repo.AbstractVersionedTrantorAppRepo;
import io.terminus.trantor2.application.service.AbstractAppManagerService;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.common.feature.VersionRange;
import io.terminus.trantor2.iam.service.TrantorIAMUserService;
import io.terminus.trantor2.console.service.ConsoleService;
import io.terminus.trantor2.meta.api.dto.SnapshotTag;
import io.terminus.trantor2.meta.management.model.SystemModulePO;
import io.terminus.trantor2.meta.management.model.VersionedSystemModulePO;
import io.terminus.trantor2.meta.management.repo.SystemModuleRepo;
import io.terminus.trantor2.meta.management.repo.VersionedSystemModuleRepo;
import io.terminus.trantor2.nexus.dto.Maven;
import io.terminus.trantor2.nexus.service.NexusApiClientFactory;
import io.terminus.trantor2.properties.management.nexus.NexusConfigProperties;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Nonnull;
import java.util.Collections;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
public class SystemModuleManagerService extends AbstractAppManagerService<SystemModulePO, VersionedSystemModulePO> implements ISystemModuleManagerService {
    public static final String SYSTEM_MODULE_GROUP = "io.terminus.trantor.platform";
    public static final String SYSTEM_MODULE_ARTIFACT_ID = "sys_common";
    public static final String SYSTEM_MODULE_NAME = "通用";
    private final SystemModuleRepo repo;
    private final VersionedSystemModuleRepo versionRepo;

    protected SystemModuleManagerService(ConsoleService consoleService, NexusApiClientFactory nexusApiClientFactory,
                                         NexusConfigProperties nexusConfigProperties, TrantorIAMUserService trantorIAMUserService, SystemModuleRepo repo, VersionedSystemModuleRepo versionRepo) {
        super(consoleService, nexusApiClientFactory, nexusConfigProperties, trantorIAMUserService);
        this.repo = repo;
        this.versionRepo = versionRepo;
    }

    @Override
    public ApplicationType getApplicationType() {
        return ApplicationType.SYSTEM_MODULE;
    }

    @Override
    public String getGroup() {
        return SYSTEM_MODULE_GROUP;
    }

    @Override
    public String getExtension() {
        return "json";
    }

    @Override
    public AbstractTrantorAppRepo<SystemModulePO> getRepo() {
        return repo;
    }

    @Override
    public AbstractVersionedTrantorAppRepo<VersionedSystemModulePO> getVersionedRepo() {
        return versionRepo;
    }

    @Override
    @Transactional
    public void release(@Nonnull String version, @Nonnull String rootOid, @Nonnull ByteArrayResource resource, @Nullable VersionRange compatibilityRange) {
        if (BooleanUtils.isNotTrue(consoleService.isPublicMarketDevMode())) {
            throw new ValidationException("this operation cannot be performed on the current console");
        }
        Optional<SystemModulePO> appOpt = findAppOpt(SYSTEM_MODULE_ARTIFACT_ID, true);
        Maven zip = buildZipMaven(version, resource);
        if (!appOpt.isPresent()) {
            AppItemWithVersionCreateRequest req = AppItemWithVersionCreateRequest.builder()
                    .key(SYSTEM_MODULE_ARTIFACT_ID)
                    .appVersion(version)
                    .name(SYSTEM_MODULE_NAME)
                    .content(new SystemModuleContent(getZipDownloadUrl(zip), rootOid))
                    .baseInfo(new EmptyBaseInfo())
                    .create(true)
                    .build();
            createAppWithVersionOp(req);
        } else {
            VersionedAppItemSaveRequest req = VersionedAppItemSaveRequest.builder()
                    .key(SYSTEM_MODULE_ARTIFACT_ID)
                    .create(true)
                    .version(version)
                    .content(new SystemModuleContent(getZipDownloadUrl(zip), rootOid)).build();
            saveVersionedAppOp(req);
        }
        super.releaseOp(SYSTEM_MODULE_ARTIFACT_ID, version, compatibilityRange, Collections.singleton(zip));
    }

    @Nullable
    @Override
    public SnapshotTag lastTag() {
        Optional<VersionedSystemModulePO> versionOpt = findVersionOpt(SYSTEM_MODULE_ARTIFACT_ID, null, true, true);
        if (!versionOpt.isPresent()) {
            return null;
        }
        VersionedSystemModulePO version = versionOpt.get();
        if (version.getContent() == null || version.getContent().getDownloadUrl() == null) {
            return null;
        }
        return new SnapshotTag(null, null, version.getContent().getRootOid(), version.getAppVersion());
    }

    /**
     * 获取最新的系统模块资源
     */
    @Override
    public byte[] downloadLatestResource() {
        VersionedSystemModulePO version = findVersion(SYSTEM_MODULE_ARTIFACT_ID, null, true, true);
        if (version == null || version.getContent() == null || version.getContent().getDownloadUrl() == null) {
            return null;
        }
        String downloadUrl = version.getContent().getDownloadUrl();
        return download(false, downloadUrl);
    }

    private Maven buildZipMaven(@Nonnull String version, @Nonnull Resource resource) {
        return Maven.builder()
                .artifactId(SYSTEM_MODULE_ARTIFACT_ID)
                .groupId(SYSTEM_MODULE_GROUP)
                .extension("zip")
                .version(version)
                .resource(resource)
                .build();
    }

    private String getZipDownloadUrl(Maven maven) {
        return getDownloadUrl(false, maven);
    }
}
