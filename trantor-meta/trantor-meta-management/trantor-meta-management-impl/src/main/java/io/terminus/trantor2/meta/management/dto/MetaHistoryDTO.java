package io.terminus.trantor2.meta.management.dto;

import io.terminus.trantor2.meta.api.dto.MetaChangeOperation;
import lombok.Data;

import java.util.Date;

/**
 * Meta history DTO
 */
@Data
public class MetaHistoryDTO {
    private Long id;
    private String key;
    private String lastOid;
    private String currentOid;
    private MetaChangeOperation changeOperation;
    private Long relatedTaskId;
    private Long createdBy;
    private Date createdAt;
    
    /**
     * Semantic change description message (reserved field for future diff system and LLM integration)
     */
    private String message;
}