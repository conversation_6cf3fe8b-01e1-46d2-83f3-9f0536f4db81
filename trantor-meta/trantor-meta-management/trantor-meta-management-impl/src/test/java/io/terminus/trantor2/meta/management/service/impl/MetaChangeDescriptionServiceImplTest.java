package io.terminus.trantor2.meta.management.service.impl;

import io.terminus.trantor2.meta.api.dto.MetaChangeOperation;
import io.terminus.trantor2.meta.editor.model.MetaObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ai.chat.model.ChatModel;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class MetaChangeDescriptionServiceImplTest {

    @Mock
    private ChatModel chatModel;

    private MetaChangeDescriptionServiceImpl metaChangeDescriptionService;

    @BeforeEach
    void setUp() {
        metaChangeDescriptionService = new MetaChangeDescriptionServiceImpl(chatModel);
    }

    @Test
    void testGenerateMessage_CreateOperation() throws Exception {
        // Arrange
        Map<String, Object> input = new HashMap<>();

        MetaObject afterObject = new MetaObject();
        afterObject.setName("TestPage");
        afterObject.setType("page");
        afterObject.setKey("test.page");

        input.put("after", afterObject);

        // Act
        String result = metaChangeDescriptionService.generateMessage(input);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("创建"));
        assertTrue(result.contains("页面"));
        assertTrue(result.contains("TestPage"));
        System.out.println("[DEBUG_LOG] Create operation result: " + result);
    }

    @Test
    void testGenerateMessage_UpdateOperation() throws Exception {
        // Arrange
        Map<String, Object> input = new HashMap<>();

        MetaObject beforeObject = new MetaObject();
        beforeObject.setName("OldName");
        beforeObject.setType("component");
        beforeObject.setKey("test.component");

        MetaObject afterObject = new MetaObject();
        afterObject.setName("NewName");
        afterObject.setType("component");
        afterObject.setKey("test.component");

        input.put("before", beforeObject);
        input.put("after", afterObject);

        // Act
        String result = metaChangeDescriptionService.generateMessage(input);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("更新"));
        assertTrue(result.contains("组件"));
        assertTrue(result.contains("NewName"));
        assertTrue(result.contains("名称从"));
        System.out.println("[DEBUG_LOG] Update operation result: " + result);
    }

    @Test
    void testGenerateMessage_DeleteOperation() throws Exception {
        // Arrange
        Map<String, Object> input = new HashMap<>();

        MetaObject beforeObject = new MetaObject();
        beforeObject.setName("DeletedPage");
        beforeObject.setType("page");
        beforeObject.setKey("deleted.page");

        input.put("before", beforeObject);

        // Act
        String result = metaChangeDescriptionService.generateMessage(input);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("删除"));
        assertTrue(result.contains("页面"));
        assertTrue(result.contains("DeletedPage"));
        System.out.println("[DEBUG_LOG] Delete operation result: " + result);
    }

    @Test
    void testGenerateMessage_SnapshotImport() throws Exception {
        // Arrange
        Map<String, Object> input = new HashMap<>();

        MetaObject afterObject = new MetaObject();
        afterObject.setName("ImportedSnapshot");
        afterObject.setType("datamodel");
        afterObject.setKey("imported.snapshot");

        input.put("after", afterObject);

        // Act
        String result = metaChangeDescriptionService.generateMessage(input);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("创建"));
        assertTrue(result.contains("数据模型"));
        assertTrue(result.contains("ImportedSnapshot"));
        System.out.println("[DEBUG_LOG] Snapshot import result: " + result);
    }

    @Test
    void testGenerateMessage_ModuleImport() throws Exception {
        // Arrange
        Map<String, Object> input = new HashMap<>();

        MetaObject afterObject = new MetaObject();
        afterObject.setName("ImportedModule");
        afterObject.setType("workflow");
        afterObject.setKey("imported.module");

        input.put("after", afterObject);

        // Act
        String result = metaChangeDescriptionService.generateMessage(input);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("创建"));
        assertTrue(result.contains("工作流"));
        assertTrue(result.contains("ImportedModule"));
        System.out.println("[DEBUG_LOG] Module import result: " + result);
    }

    @Test
    void testGenerateMessage_UnknownOperation() throws Exception {
        // Arrange
        Map<String, Object> input = new HashMap<>();

        MetaObject afterObject = new MetaObject();
        afterObject.setName("UnknownObject");
        afterObject.setType("api");
        afterObject.setKey("unknown.object");

        input.put("after", afterObject);

        // Act
        String result = metaChangeDescriptionService.generateMessage(input);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("创建"));
        assertTrue(result.contains("API"));
        assertTrue(result.contains("UnknownObject"));
        System.out.println("[DEBUG_LOG] Unknown operation result: " + result);
    }

    @Test
    void testGenerateMessage_InvalidOperation() throws Exception {
        // Arrange
        Map<String, Object> input = new HashMap<>();

        MetaObject afterObject = new MetaObject();
        afterObject.setName("TestObject");
        afterObject.setType("component");
        afterObject.setKey("test.object");

        input.put("after", afterObject);

        // Act
        String result = metaChangeDescriptionService.generateMessage(input);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("创建"));
        assertTrue(result.contains("组件"));
        assertTrue(result.contains("TestObject"));
        System.out.println("[DEBUG_LOG] Invalid operation result: " + result);
    }
}
