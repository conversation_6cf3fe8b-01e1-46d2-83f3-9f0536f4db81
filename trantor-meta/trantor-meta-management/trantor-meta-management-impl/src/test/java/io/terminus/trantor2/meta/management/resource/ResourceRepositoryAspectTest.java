package io.terminus.trantor2.meta.management.resource;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.ide.test.Trantor2IDETestApp;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.ModuleInfo;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.editor.repository.ext.ExtNodeRepo;
import io.terminus.trantor2.meta.api.service.ExtMetaService;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.context.MetaContext;
import io.terminus.trantor2.lang.meta.CompPack;
import io.terminus.trantor2.meta.management.base.MetaBaseIntegrationWithRealDataTests;
import io.terminus.trantor2.meta.management.service.EditorMetaEditService;
import io.terminus.trantor2.meta.resource.ResourceBaseMeta;
import io.terminus.trantor2.meta.resource.ext.ExtNodeMeta;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.scene.config.SceneType;
import io.terminus.trantor2.scene.config.datamanager.DataManagerSceneConfig;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.meta.DataManagerViewMeta;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

import static io.terminus.trantor2.meta.resource.ext.ExtNodeMeta.EXT_TYPE;
import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest(
        classes = {Trantor2IDETestApp.class},
        properties = {
                "trantor2.runtime.default-mode=true"
        }
)
class ResourceRepositoryAspectTest extends MetaBaseIntegrationWithRealDataTests {
    @Autowired
    private MetaQueryService queryService;
    @Autowired
    private EditorMetaEditService editService;
    @Autowired
    private ExtMetaService extMetaService;
    @Autowired
    private ExtNodeRepo extNodeRepo;
    @Autowired
    private ModelRepo modelRepo;
    @Autowired
    private SceneRepo sceneRepo;
    @Autowired
    private ViewRepo viewRepo;
    private static final ObjectMapper mapper = ObjectJsonUtil.MAPPER;

    @BeforeEach
    @Override
    public void baseInit(TestInfo info) {
        super.baseInit(info);
        TrantorContext.setModuleKey("extM");
        loadBehaviors();
    }

    @BeforeEach
    void setUp() {
        Set<ModuleInfo> modules = new HashSet<>();
        modules.add(ModuleInfo.of("wjxtest", "extM", false, true, false));
        MetaContext.setCurrentDeployModules(modules);
    }

    @Test
    void create() {
        DataStructNode dataStructNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/model.json", DataStructNode.class);
        assertDoesNotThrow(() -> {
            Long metaId = modelRepo.create(dataStructNode, ResourceContext.newResourceCtx("wjxtest", 1L));
            Optional<MetaTreeNodeExt> extOpt = queryService.findById(metaId);
            Optional<MetaTreeNodeExt> oriOpt = queryService.findByKey(ctx, dataStructNode.getKey());
            assertFalse(oriOpt.isPresent());
            assertTrue(extOpt.isPresent());
            MetaTreeNodeExt ext = extOpt.get();
            assertEquals(MetaType.Model, ext.getMetaType());

            assertEquals(dataStructNode.getKey(), ext.getKey());
            assertEquals(dataStructNode.getParentKey(), ext.getParentKey());
            ObjectNode node = ext.getProps();
            assertNotNull(node);
            assertNotNull(node.at("/props"));
            assertNotNull(node.at("/children"));
            assertNotNull(node.at("/alias"));
            Optional<ExtNodeMeta> extMetaOpt = extNodeRepo.findOneByKeyAndType(
                    KeyUtil.extKey(dataStructNode.getKey()), EXT_TYPE + MetaType.Model.name(),
                    ResourceContext.newResourceCtx(ctx)
            );
            assertTrue(extMetaOpt.isPresent());
            assertNull(extMetaOpt.get().getResourceProps().getNodeProps());
        });
        // test findOne
        Optional<DataStructNode> modelOpt
                = modelRepo.findOneByKey(dataStructNode.getKey(), ResourceContext.newResourceCtx(ctx));
        assertTrue(modelOpt.isPresent());
        DataStructNode model = modelOpt.get();
        assertEquals(dataStructNode.getKey(), model.getKey());
        assertEquals(dataStructNode.getName(), model.getName());
        assertEquals(10, model.getChildren().size());
        assertTrue(model.getExtended());
    }

    @Test
    void create_whit_subMetas() {
        String sceneMetaKey = "extM$sceneb";
        String parentKey = "extM$ungroup";
        String viewKey = "extM$sceneb:list";

        assertDoesNotThrow(() -> {
            Map.Entry<Long, SceneMeta> scene = createScene();

            Optional<MetaTreeNodeExt> extOpt = queryService.findById(scene.getKey());
            Optional<MetaTreeNodeExt> oriOpt = queryService.findByKey(ctx, sceneMetaKey);
            assertFalse(oriOpt.isPresent());
            assertTrue(extOpt.isPresent());
            MetaTreeNodeExt ext = extOpt.get();
            assertEquals(MetaType.Scene, ext.getMetaType());
            assertEquals(sceneMetaKey, ext.getKey());
            assertEquals(parentKey, ext.getParentKey());
            JsonNode node = ext.getProps();
            assertNotNull(node);
            assertNotNull(node.get("sceneConfig"));

            // children
            Optional<MetaTreeNodeExt> oriViewOpt = queryService.findByKey(ctx, viewKey);
            assertFalse(oriViewOpt.isPresent());

            Optional<ExtNodeMeta> extMetaOpt = extNodeRepo.findOneByKeyAndType(
                    KeyUtil.extKey(viewKey), EXT_TYPE + MetaType.View.name(),
                    ResourceContext.newResourceCtx(ctx)
            );
            assertTrue(extMetaOpt.isPresent());
            assertEquals(MetaType.ExtNode, extMetaOpt.get().getMetaType());
        });
        // test findOne
        Optional<SceneMeta> sceneOpt
                = sceneRepo.findOneByKey(sceneMetaKey, ResourceContext.newResourceCtx(ctx));
        assertTrue(sceneOpt.isPresent());
        assertTrue(sceneOpt.get().getExtended());
        assertTrue(sceneOpt.get().getCustomExt());
        assertEquals(sceneMetaKey, sceneOpt.get().getKey());
        assertEquals(SceneType.DATA, sceneOpt.get().getSceneConfig().getType());

        Optional<DataManagerViewMeta> viewOpt = viewRepo.findOneByKey(viewKey, ResourceContext.newResourceCtx(ctx));
        assertTrue(viewOpt.isPresent());
        assertTrue(viewOpt.get().getExtended());
        assertTrue(viewOpt.get().getCustomExt());
        assertEquals(viewKey, viewOpt.get().getKey());
        assertEquals(sceneOpt.get().getKey(), viewOpt.get().getParentKey());
        assertEquals("列表页", viewOpt.get().getResourceProps().getTitle());
    }

    @Test
    public void update() {
        DataStructNode dataStructNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/model.json", DataStructNode.class);
        modelRepo.create(dataStructNode, ResourceContext.newResourceCtx("wjxtest", 1L));

        String name = "新模型新名字";
        dataStructNode.setName(name);
        dataStructNode.getChildren().get(0).setName("新模型新字段名");
        assertDoesNotThrow(() -> {
            modelRepo.update(dataStructNode, ResourceContext.newResourceCtx("wjxtest", 1L));
            Optional<MetaTreeNodeExt> oriOpt = queryService.findByKey(ctx, dataStructNode.getKey());
            assertFalse(oriOpt.isPresent());

            Optional<ExtNodeMeta> extOpt = extNodeRepo.findOneByKey(KeyUtil.extKey(dataStructNode.getKey()), ResourceContext.newResourceCtx(ctx));
            assertTrue(extOpt.isPresent());

            ExtNodeMeta ext = extOpt.get();
            assertEquals(MetaType.ExtNode, ext.getMetaType());
            assertEquals(dataStructNode.getKey(), ext.getKey());
            assertEquals(dataStructNode.getParentKey(), ext.getParentKey());

            Optional<DataStructNode> modelOpt
                    = modelRepo.findOneByKey(dataStructNode.getKey(), ResourceContext.newResourceCtx(ctx));
            assertTrue(modelOpt.isPresent());
            DataStructNode model = modelOpt.get();
            assertEquals(MetaType.Model, model.getMetaType());
            assertEquals(name, model.getName());
            assertEquals("新模型新字段名", model.getResourceProps().getChildren().get(0).getName());
        });
    }

    @Test
    public void update_with_subMetas() {
        Map.Entry<Long, SceneMeta> scene = createScene();
        // edit
        SceneMeta sceneMeta = scene.getValue();
        sceneMeta.setName("场景新名字");
        ResourceBaseMeta<?> meta = sceneMeta.getSubMetas().get(0);
        meta.setName("视图新名字");
        ((DataManagerView) meta.getResourceProps()).setTitle("视图新标题");
        String viewKey = "extM$sceneb:list";

        assertDoesNotThrow(() -> {
            sceneRepo.update(sceneMeta, ResourceContext.newResourceCtx("wjxtest", 1L));
            Optional<MetaTreeNodeExt> oriOpt = queryService.findByKey(ctx, sceneMeta.getKey());
            assertFalse(oriOpt.isPresent());

            Optional<ExtNodeMeta> extOpt = extNodeRepo.findOneByKey(KeyUtil.extKey(sceneMeta.getKey()), ResourceContext.newResourceCtx(ctx));
            assertTrue(extOpt.isPresent());

            ExtNodeMeta ext = extOpt.get();
            assertEquals(MetaType.ExtNode, ext.getMetaType());
            assertEquals(sceneMeta.getKey(), ext.getKey());
            assertEquals(sceneMeta.getParentKey(), ext.getParentKey());

            Optional<SceneMeta> sceneOpt = sceneRepo.findOneByKey(sceneMeta.getKey(), ResourceContext.newResourceCtx(ctx));
            assertTrue(sceneOpt.isPresent());
            SceneMeta updatedSceneMeta = sceneOpt.get();
            assertNotNull(updatedSceneMeta);
            assertEquals("场景新名字", updatedSceneMeta.getName());
            assertTrue(updatedSceneMeta.getExtended());
            assertTrue(updatedSceneMeta.getCustomExt());

            // children
            Optional<DataManagerViewMeta> viewExtOpt = viewRepo.findOneByKey(KeyUtil.extKey(viewKey), ResourceContext.newResourceCtx(ctx));
            assertTrue(viewExtOpt.isPresent());
            DataManagerViewMeta viewExt = viewExtOpt.get();
            assertEquals(MetaType.View, viewExt.getMetaType());
            assertNotNull(viewExt);
            assertTrue(viewExt.getExtended());
            assertTrue(viewExt.getCustomExt());
            assertEquals(sceneMeta.getKey(), viewExt.getParentKey());
            assertEquals(viewKey, viewExt.getKey());
            assertEquals(sceneMeta.getKey(), viewExt.getParentKey());

            DataManagerView viewResourceProps = viewExt.getResourceProps();

            assertEquals("视图新名字", viewResourceProps.getName());
            assertEquals("视图新标题", viewResourceProps.getTitle());
        });

        // test findOne
        Optional<SceneMeta> sceneOpt
                = sceneRepo.findOneByKey(sceneMeta.getKey(), ResourceContext.newResourceCtx(ctx));
        assertTrue(sceneOpt.isPresent());
        assertTrue(sceneOpt.get().getExtended());
        assertTrue(sceneOpt.get().getCustomExt());
        assertEquals(sceneMeta.getKey(), sceneOpt.get().getKey());
        assertEquals(SceneType.DATA, sceneOpt.get().getSceneConfig().getType());
        assertEquals("场景新名字", sceneOpt.get().getName());

        Optional<DataManagerViewMeta> viewOpt = viewRepo.findOneByKey(viewKey, ResourceContext.newResourceCtx(ctx));
        assertTrue(viewOpt.isPresent());
        assertTrue(viewOpt.get().getExtended());
        assertTrue(viewOpt.get().getCustomExt());
        assertEquals(viewKey, viewOpt.get().getKey());
        assertEquals("视图新名字", viewOpt.get().getName());
        assertEquals("视图新标题", viewOpt.get().getResourceProps().getTitle());
    }

    @Test
    public void delete() {
        DataStructNode dataStructNode = ResourceHelper.readValueFromResource(mapper, getClass(), "json/model.json", DataStructNode.class);
        modelRepo.create(dataStructNode, ResourceContext.newResourceCtx("wjxtest", 1L));

        assertTrue(extNodeRepo.findOneByKey(KeyUtil.extKey(dataStructNode.getKey()), ResourceContext.newResourceCtx(ctx)).isPresent());
        assertTrue(modelRepo.findOneByKey(dataStructNode.getKey(), ResourceContext.newResourceCtx(ctx)).isPresent());

        modelRepo.deleteByKey(dataStructNode.getKey(), ResourceContext.newResourceCtx("wjxtest", 1L));
        assertFalse(extNodeRepo.findOneByKey(KeyUtil.extKey(dataStructNode.getKey()), ResourceContext.newResourceCtx(ctx)).isPresent());
        assertFalse(modelRepo.findOneByKey(dataStructNode.getKey(), ResourceContext.newResourceCtx(ctx)).isPresent());
    }

    @Test
    public void delete_with_subMetas() {
        createScene();
        assertTrue(extNodeRepo.findOneByKey(KeyUtil.extKey("extM$sceneb"), ResourceContext.newResourceCtx(ctx)).isPresent());
        assertTrue(extNodeRepo.findOneByKey(KeyUtil.extKey("extM$sceneb:list"), ResourceContext.newResourceCtx(ctx)).isPresent());
        assertTrue(sceneRepo.findOneByKey("extM$sceneb", ResourceContext.newResourceCtx(ctx)).isPresent());
        assertTrue(viewRepo.findOneByKey("extM$sceneb:list", ResourceContext.newResourceCtx(ctx)).isPresent());

        sceneRepo.deleteByKey("extM$sceneb", true, false, true, ResourceContext.newResourceCtx("wjxtest", 1L));

        assertFalse(extNodeRepo.findOneByKey(KeyUtil.extKey("extM$sceneb"), ResourceContext.newResourceCtx(ctx)).isPresent());
        assertFalse(extNodeRepo.findOneByKey(KeyUtil.extKey("extM$sceneb:list"), ResourceContext.newResourceCtx(ctx)).isPresent());
        assertFalse(sceneRepo.findOneByKey("extM$sceneb", ResourceContext.newResourceCtx(ctx)).isPresent());
        assertFalse(viewRepo.findOneByKey("extM$sceneb:list", ResourceContext.newResourceCtx(ctx)).isPresent());
    }

    @Test
    public void findNotExist() {
        assertDoesNotThrow(() -> {
            Optional<DataManagerViewMeta> viewMetaOptional = viewRepo.findOneByKey("extM$hhhh", ResourceContext.newResourceCtx("wjxtest", 1L));
            assertFalse(viewMetaOptional.isPresent());
        });
    }

    private Map.Entry<Long, SceneMeta> createScene() {
        mapper.registerSubtypes(Collections.singletonList(DataManagerSceneConfig.class));
        SceneMeta sceneMeta = ResourceHelper.readValueFromResource(mapper, getClass(), "json/scene.json", SceneMeta.class);
        List<ResourceBaseMeta<?>> children = new ArrayList<>(4);
        DataManagerSceneConfig sceneConfig = (DataManagerSceneConfig) sceneMeta.getSceneConfig();
        children.addAll(sceneConfig.getViews().stream()
                .map(view -> DataManagerViewMeta.fromView(view, sceneMeta.getKey()))
                .collect(Collectors.toList()));
        sceneMeta.setSubMetas(children);
        sceneConfig.setViews(null);
        Long id = sceneRepo.create(sceneMeta, ResourceContext.newResourceCtx("wjxtest", 1L));
        return new AbstractMap.SimpleEntry<>(id, sceneMeta);
    }

    public void loadBehaviors() {
        Path dir;
        try {
            dir = Paths.get(new ClassPathResource("io/terminus/trantor2/meta/index/behaviors/").getURI());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        CompPack.load(dir);
    }
}
