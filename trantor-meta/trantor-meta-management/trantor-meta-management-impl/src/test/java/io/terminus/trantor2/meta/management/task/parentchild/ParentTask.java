package io.terminus.trantor2.meta.management.task.parentchild;

import io.terminus.trantor2.meta.exception.MetaTaskException;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskDefine;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public final class ParentTask extends BaseTask<ParentTask.Options> {
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
        private List<String> markFailed = new ArrayList<>();
        private List<String> markFastFail = new ArrayList<>();
    }

    @Override
    public void exec(Options opts, List<TaskDefine> subTasks, List<String> outputs, TaskContext ctx) {
        outputs.add("ParentTask Start");
        if (opts.markFailed.contains(this.getClass().getSimpleName())) {
            throw new MetaTaskException("ParentTask Failed");
        }

        ChildAlphaTask.Options opt1 = new ChildAlphaTask.Options();
        opt1.setMarkFailed(opts.getMarkFailed());
        Boolean fastFailAlpha = opts.markFastFail.contains(ChildAlphaTask.class.getSimpleName());
        subTasks.add(new TaskDefine(ChildAlphaTask.class, opt1, "child alpha", fastFailAlpha));

        ChildBetaTask.Options opt2 = new ChildBetaTask.Options();
        opt2.setMarkFailed(opts.getMarkFailed());
        Boolean fastFailBeta = opts.markFastFail.contains(ChildBetaTask.class.getSimpleName());
        subTasks.add(new TaskDefine(ChildBetaTask.class, opt2, "child beta", fastFailBeta));
    }
}
