package io.terminus.trantor2.meta.management.dlock;

import io.terminus.trantor2.meta.management.base.MetaBaseIntegrationTests;
import io.terminus.trantor2.module.service.OSSService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import jakarta.annotation.Resource;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
class JdbcDLockTest extends MetaBaseIntegrationTests {
    @Resource
    private JdbcDLock jdbcDLock;
    private static final String token = "123";

    @Test
    void lock() throws InterruptedException {
        String lockKey = "lockKey";
        long timeoutSeconds = 2;

        // 锁住
        boolean locked = jdbcDLock.lock(lockKey, token, timeoutSeconds);
        assertTrue(locked);

        // 立即检查锁信息
        Optional<LockInfo> lockInfo = jdbcDLock.getLockInfo(lockKey);
        assertTrue(lockInfo.isPresent());
        assertEquals(token, lockInfo.get().getToken());
        assertNotNull(lockInfo.get().getExpireAt());

        // 等待 timeoutSeconds +1  秒
        TimeUnit.SECONDS.sleep(timeoutSeconds + 1);

        // 再次检查锁信息
        lockInfo = jdbcDLock.getLockInfo(lockKey);
        assertFalse(lockInfo.isPresent());
    }

    @Test
    void unLock() {
        String lockKey = "unLockKey";

        // 锁住
        boolean locked = jdbcDLock.lock(lockKey, token, 3);
        assertTrue(locked);

        // 解锁
        boolean unlocked = jdbcDLock.unLock(lockKey, token);
        assertTrue(unlocked);

        // 检查锁信息
        Optional<LockInfo> lockInfo = jdbcDLock.getLockInfo(lockKey);
        assertFalse(lockInfo.isPresent());
    }

    @Test
    void unLockByBadToken() {
        String lockKey = "unLockByBadToken";

        // 锁住
        boolean locked = jdbcDLock.lock(lockKey, token, 3);
        assertTrue(locked);

        // 解锁
        boolean unlocked = jdbcDLock.unLock(lockKey, "bad" + token);
        assertFalse(unlocked);

        // 检查锁信息
        Optional<LockInfo> lockInfo = jdbcDLock.getLockInfo(lockKey);
        assertTrue(lockInfo.isPresent());
    }

    @Test
    void unLockForce() {
        String lockKey = "unLockForce";

        // 锁住
        boolean locked = jdbcDLock.lock(lockKey, token, 3);
        assertTrue(locked);

        // 解锁
        boolean unlocked = jdbcDLock.unLock(lockKey, null);
        assertTrue(unlocked);

        // 检查锁信息
        Optional<LockInfo> lockInfo = jdbcDLock.getLockInfo(lockKey);
        assertFalse(lockInfo.isPresent());
    }


    @Test
    void renewal() throws InterruptedException {
        String lockKey = "renewal";
        long timeoutSeconds = 2;

        // 锁住
        boolean locked = jdbcDLock.lock(lockKey, token, timeoutSeconds);
        assertTrue(locked);

        // 等待1秒
        Thread.sleep(1000);

        // 续约
        boolean renewed = jdbcDLock.renewal(lockKey, token, timeoutSeconds);
        assertTrue(renewed);

        // 等待1秒
        Thread.sleep(1000);

        // 检查锁信息
        Optional<LockInfo> lockInfo = jdbcDLock.getLockInfo(lockKey);
        assertTrue(lockInfo.isPresent());
        assertEquals(token, lockInfo.get().getToken());
    }

    @Test
    void getLockInfo() {
        String lockKey = "getLockInfo";
        String token = "123";
        long timeoutSeconds = 1;

        // 锁住
        boolean locked = jdbcDLock.lock(lockKey, token, timeoutSeconds);
        assertTrue(locked);

        // 获取锁信息
        Optional<LockInfo> lockInfo = jdbcDLock.getLockInfo(lockKey);
        assertTrue(lockInfo.isPresent());
        assertEquals(token, lockInfo.get().getToken());
    }

    @Test
    void getToken() {
        String lockKey = "getToken";
        long timeoutSeconds = 1;

        // 锁住
        boolean locked = jdbcDLock.lock(lockKey, token, timeoutSeconds);
        assertTrue(locked);

        // 获取token
        Optional<String> tokenOpt = jdbcDLock.getToken(lockKey);
        assertTrue(tokenOpt.isPresent());
        assertEquals(token, tokenOpt.get());
    }
}
