{"modelKey": "ERP_CO$coa_bdg_acc_structure_md", "isDeleted": null, "isEnabled": true, "serviceName": null, "serviceType": "EVENT", "serviceDslMd5": null, "serviceDslJson": {"id": null, "key": "TERP_BUDGET$COA_BDG_ACC_STRUCTURE_MD_SAVE_EVENT_SERVICE", "name": "COA-预算科目体系-保存服务", "type": "ServiceDefinition", "input": [{"id": null, "fieldKey": "request", "relation": null, "required": true, "fieldName": "request", "fieldType": "Model", "fieldAlias": "request", "description": null, "defaultValue": null, "relatedModel": {"modelKey": "ERP_CO$coa_bdg_acc_structure_md", "modelName": "TERP_BUDGET$coa_bdg_acc_structure_md", "modelAlias": "ERP_CO$coa_bdg_acc_structure_md"}}], "props": {"desc": null, "name": null, "type": "ServiceProperties", "teamId": 22, "transactionPropagation": "REQUIRED"}, "output": [{"id": null, "elements": null, "fieldKey": "info", "required": null, "fieldName": "info", "fieldType": "Object", "fieldAlias": "info", "description": null, "defaultValue": null}, {"id": null, "elements": null, "fieldKey": "err", "required": null, "fieldName": "err", "fieldType": "Object", "fieldAlias": "err", "description": null, "defaultValue": null}, {"id": null, "fieldKey": "success", "required": null, "fieldName": "success", "fieldType": "Boolean", "fieldAlias": "success", "description": null, "defaultValue": null}, {"id": null, "elements": [{"id": null, "elements": null, "fieldKey": "data", "required": null, "fieldName": "data", "fieldType": "Object", "fieldAlias": "data", "description": null, "defaultValue": null}], "fieldKey": "data", "required": null, "fieldName": "data", "fieldType": "Object", "fieldAlias": "data", "description": null, "defaultValue": null}], "children": [{"id": null, "key": "start0", "name": "开始节点", "type": "StartNode", "props": {"desc": "开始节点", "name": "开始节点", "type": "StartProperties", "input": [{"id": null, "fieldKey": "request", "relation": null, "required": true, "fieldName": "request", "fieldType": "Model", "fieldAlias": "request", "description": null, "defaultValue": null, "relatedModel": {"modelKey": "ERP_CO$coa_bdg_acc_structure_md", "modelName": "TERP_BUDGET$coa_bdg_acc_structure_md", "modelAlias": "ERP_CO$coa_bdg_acc_structure_md"}}], "output": [{"id": null, "elements": null, "fieldKey": "info", "required": null, "fieldName": "info", "fieldType": "Object", "fieldAlias": "info", "description": null, "defaultValue": null}, {"id": null, "elements": null, "fieldKey": "err", "required": null, "fieldName": "err", "fieldType": "Object", "fieldAlias": "err", "description": null, "defaultValue": null}, {"id": null, "fieldKey": "success", "required": null, "fieldName": "success", "fieldType": "Boolean", "fieldAlias": "success", "description": null, "defaultValue": null}, {"id": null, "elements": [{"id": null, "elements": null, "fieldKey": "data", "required": null, "fieldName": "data", "fieldType": "Object", "fieldAlias": "data", "description": null, "defaultValue": null}], "fieldKey": "data", "required": null, "fieldName": "data", "fieldType": "Object", "fieldAlias": "data", "description": null, "defaultValue": null}], "globalVariable": [{"id": null, "elements": [{"id": null, "fieldKey": "eventCode", "required": null, "fieldName": "eventCode", "fieldType": "Text", "fieldAlias": "eventCode", "description": null, "defaultValue": null}, {"id": null, "fieldKey": "param", "relation": null, "required": null, "fieldName": "param", "fieldType": "Model", "fieldAlias": "param", "description": null, "defaultValue": null, "relatedModel": {"modelKey": "ERP_CO$coa_bdg_acc_structure_md", "modelName": "TERP_BUDGET$coa_bdg_acc_structure_md", "modelAlias": "ERP_CO$coa_bdg_acc_structure_md"}}], "fieldKey": "event", "required": null, "fieldName": "event", "fieldType": "Object", "fieldAlias": "event", "description": null, "defaultValue": null}]}, "children": null, "preNodeKey": null, "renderType": null, "nextNodeKey": "AssignNode0", "headNodeKeys": null}, {"id": null, "key": "AssignNode0", "name": "赋值", "type": "AssignNode", "props": {"desc": null, "name": "赋值", "type": "AssignProperties", "assignments": [{"id": null, "field": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "GLOBAL", "valueName": "GLOBAL", "modelAlias": null, "relatedModel": null}, {"valueKey": "event", "valueName": "event", "modelAlias": null, "relatedModel": null}, {"valueKey": "eventCode", "valueName": "eventCode", "modelAlias": null, "relatedModel": null}], "fieldType": "Text", "valueType": "VAR", "constValue": null}, "value": {"id": null, "type": "VarValue", "varValue": null, "fieldType": null, "valueType": "CONST", "constValue": "ERP_CO$COA_BDG_ACC_STRUCTURE_MD_SAVE_EVENT"}, "operator": "EQ"}, {"id": null, "field": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "GLOBAL", "valueName": "GLOBAL", "modelAlias": null, "relatedModel": null}, {"valueKey": "event", "valueName": "event", "modelAlias": null, "relatedModel": null}, {"valueKey": "param", "valueName": "param", "modelAlias": null, "relatedModel": null}], "fieldType": "Object", "valueType": "VAR", "constValue": null}, "value": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "REQUEST", "valueName": "REQUEST", "modelAlias": null, "relatedModel": null}, {"valueKey": "request", "valueName": "request", "modelAlias": null, "relatedModel": null}], "fieldType": null, "valueType": "VAR", "constValue": null}, "operator": "EQ"}]}, "children": null, "preNodeKey": null, "renderType": null, "nextNodeKey": "SPINodeKey", "headNodeKeys": null}, {"id": null, "key": "SPINodeKey", "name": "调用线下代码", "type": "SPINode", "props": {"desc": null, "name": "调用线下代码", "type": "SPIProperties", "output": [{"id": null, "elements": null, "fieldKey": "data", "required": null, "fieldName": "ActionResponse", "fieldType": "Object", "fieldAlias": "data", "description": null, "defaultValue": null}], "inputMapping": [{"id": null, "field": {"id": null, "elements": null, "fieldKey": "event", "required": null, "fieldName": "event", "fieldType": "Object", "fieldAlias": "event", "description": null, "defaultValue": null}, "value": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "GLOBAL", "valueName": "全局变量", "modelAlias": null, "relatedModel": null}, {"valueKey": "event", "valueName": "event", "modelAlias": null, "relatedModel": null}], "fieldType": null, "valueType": "VAR", "constValue": null}}], "outputAssign": {"outputAssignType": "CUSTOM", "customAssignments": [{"id": null, "field": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "OUTPUT", "valueName": "服务出参", "modelAlias": null, "relatedModel": null}], "fieldType": "Object", "valueType": "VAR", "constValue": null}, "value": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "NODE_OUTPUT_SPINodeKey", "valueName": "出参结构体", "modelAlias": null, "relatedModel": null}, {"valueKey": "data", "valueName": "ActionResponse", "modelAlias": null, "relatedModel": null}], "fieldType": "Object", "valueType": "MODEL", "constValue": null}, "operator": "EQ"}]}, "implementation": "BizEventExecute", "implementationName": null}, "children": null, "preNodeKey": null, "renderType": null, "nextNodeKey": "end0", "headNodeKeys": null}, {"id": null, "key": "end0", "name": "结束节点", "type": "EndNode", "props": {"desc": null, "name": null, "type": "EndProperties", "outputMapping": null}, "children": null, "preNodeKey": null, "renderType": null, "nextNodeKey": null, "headNodeKeys": null}], "headNodeKeys": ["start0"]}}