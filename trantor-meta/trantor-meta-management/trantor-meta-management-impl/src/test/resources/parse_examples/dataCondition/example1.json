{"oid": "26437cd62200ac450d8a9af5349769277cbb659d6ec45c95fed0250ded9e3826", "key": "Jose_dev_mod$gen_data_auth_cf:252525", "name": "252525", "description": "", "access": "Private", "id": 17131587, "createdBy": 478577366853701, "createdAt": 1733473160000, "updatedBy": 478577366853701, "updatedAt": 1733473160000, "parentKey": "Jose_dev_mod$ungroup", "path": "", "teamId": 123, "teamCode": "PJ0724", "appId": 17127608, "extended": false, "customExt": false, "extensible": false, "props": {"modelKey": "Jose_dev_mod$gen_data_auth_cf", "condition": {"type": "ConditionGroup", "id": "OSoujKWiVoI1FL-EjUHt_", "conditions": [{"type": "ConditionGroup", "id": "enOUMXyD0nWCDoaL8ZkD6", "conditions": [{"type": "ConditionLeaf", "id": "D_afi9Hc41cwVuGgyEMNU", "key": null, "leftValue": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "name2", "valueName": "name2", "fieldType": null, "modelAlias": "Jose_dev_mod$gen_data_auth_cf"}], "scope": null, "valueType": "MODEL", "fieldType": "Text"}, "operator": "IN", "rightValue": null, "rightValues": null}], "logicOperator": "AND"}], "logicOperator": null}, "componentProps": {"D_afi9Hc41cwVuGgyEMNU": {"inputTypes": ["const", "businessVariable", "systemVariable", "modelData", "dataControlDimension"], "modelKey": "Jose_dev_mod$gen_data_auth_cf", "valueField": "name2", "serviceKey": "Jose_dev_mod$SYS_PagingDataService", "showType": "relationSelect", "dataControlDimensionKey": "Jose_dev_mod$zdwd2", "dataControlDimensionValueField": "name2", "fields": ["name2"], "filterFields": ["name2"]}}}, "type": "DataCondition"}