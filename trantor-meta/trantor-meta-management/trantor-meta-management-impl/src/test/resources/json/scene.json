{"key": "extM$sceneb", "name": "新建二开场景", "appId": 3073894, "teamId": 1203, "type": "DATA", "parentKey": "extM$ungroup", "sceneConfig": {"templateConfig": {"templateKey": "master_data", "local": false, "templateType": "MASTER_DATA", "version": "1.1.2"}, "usedModelAlias": ["extM$amodel", "user", "extM$user"], "views": [{"frontendConfig": {"modules": ["base", "service"]}, "name": "列表页", "title": "列表页", "key": "extM$sceneb:list", "content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "extM$sceneb-list-extM$amodel-new", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "新建", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "extM$sceneb-list-extM$amodel-batch", "name": "DropdownButton", "props": {"combinationMode": true, "disabled$": "$context.selectedKeys?.length === 0 || $context.route?.action === \"new\" || $context.route?.action === \"edit\"", "items": [{"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "extM$amodel"}}, {"elements": [{"fieldAlias": "ids", "fieldName": "ids", "fieldType": "Array", "valueConfig": {"expression": "<PERSON><PERSON><PERSON><PERSON>", "name": "ids", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "extM$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "extM$sceneb-list-extM$amodel"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "extM$sceneb-extM$amodel-multi-delete", "label": "批量删除"}], "label": "批量操作", "type": "default", "variant": "primary"}, "type": "Widget"}, {"children": [], "key": "extM$sceneb-list-extM$amodel-import", "name": "ImportButton", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "downloadServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download"}}, "isCustomServiceProps": {"isCustomFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "type": "InvokeService"}}, "label": "导入", "predictServiceProps": {"predictFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "type": "InvokeService"}}, "saveServiceProps": {"saveServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "type": "InvokeService"}}, "saveSubServiceProps": {"saveSubServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "type": "InvokeService"}}, "serviceProps": {"downloadFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download"}, "isCustomFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "type": "InvokeService"}, "predictFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "type": "InvokeService"}, "saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-sub-model"}, "saveServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "type": "InvokeService"}, "saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-direct-by-oss"}, "saveSubServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "type": "InvokeService"}}}, "type": "Widget"}, {"children": [], "key": "extM$sceneb-list-extM$amodel-export", "name": "ExportButton", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "exportButtonServiceProps": {"getUserInfoFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/portal/user/current"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "label": "导出", "serviceProps": {"queryFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "extM$sceneb-list-extM$amodel-batch-actions", "name": "BatchActions", "props": {}}, {"children": [{"children": [], "key": "extM$sceneb-list-extM$amodel-logs", "name": "Logs", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "日志", "logsServiceProps": {"getLogsFlow": {"serviceKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "extM$sceneb-list-extM$amodel-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "extM$sceneb-IzbkRj_yNTxZqC00fEQsT", "name": "Field", "props": {"componentProps": {"fieldAlias": "number", "modelAlias": "extM$amodel", "placeholder": "请输入", "precision": 6}, "hidden": false, "label": "数字", "name": "number", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "extM$sceneb-NE8UKHEjYXNKDiBNXjQ-h", "name": "Field", "props": {"componentProps": {"fieldAlias": "textte", "modelAlias": "extM$amodel", "placeholder": "请输入"}, "hidden": false, "label": "文本", "name": "textte", "type": "TEXT", "width": 146}, "type": "Widget"}], "key": "extM$sceneb-4PRFJOwtZX0EBf9isqUnT", "name": "Fields", "props": {}, "type": "Meta"}], "key": "extM$sceneb-list-extM$amodel", "name": "Table", "props": {"allowRowSelect": true, "filterFields": [{"componentProps": {"fieldAlias": "number", "modelAlias": "extM$amodel", "placeholder": "请输入", "precision": 6}, "hidden": false, "label": "数字", "name": "number", "type": "NUMBER", "width": 144}, {"componentProps": {"fieldAlias": "textte", "modelAlias": "extM$amodel", "placeholder": "请输入"}, "hidden": false, "label": "文本", "name": "textte", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "enumenu", "modelAlias": "extM$amodel", "placeholder": "请选择"}, "hidden": false, "label": "枚举", "name": "enumenu", "type": "SELECT", "width": 116}], "flow": {"containerKey": "extM$sceneb-list-extM$amodel", "modelAlias": "extM$amodel", "serviceKey": "extM$SYS_PagingDataService", "type": "InvokeSystemService"}, "label": "表格", "mode": "simple", "modelAlias": "extM$amodel", "onRowActionConfig": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "enable": true}, "pagination": {"size": "small"}, "serviceKey": "extM$SYS_PagingDataService"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "extM$sceneb-list-extM$amodel-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "extM$sceneb-list-extM$amodel-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "extM$sceneb-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{((data?.code || \"\") + (data?.name || \"\")) || \"amodel详情\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "extM$sceneb-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "extM$amodel"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Text", "valueConfig": {"expression": "route.recordId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "extM$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "extM$sceneb-list-extM$amodel"}, {"action": "Message", "message": "删除成功"}, {"action": "RefreshTab", "target": ["current"]}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "showCondition": {"conditions": [{"conditions": [{"id": "extM$sceneb-detail-shanchu-condition-id1", "leftValue": {"fieldType": "Enum", "scope": "form", "target": "extM$sceneb-editView-extM$amodel", "title": "status", "type": "VarValue", "val": "status", "value": "extM$amodel.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "extM$sceneb-detail-shanchu-condition-id2", "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}}, {"children": [], "key": "extM$sceneb-detailView-actions-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "navigate({ action: 'new', query: { copyId: route.recordId } })"}, "buttonType": "default", "confirmOn": "off", "label": "复制", "type": "default"}, "type": "Widget"}, {"children": [], "key": "extM$sceneb-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "route.recordId", "name": "recordId", "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "showCondition": {"conditions": [{"conditions": [{"id": "extM$sceneb-detail-bianji-condition-id1", "leftValue": {"fieldType": "Enum", "scope": "form", "target": "extM$sceneb-editView-extM$amodel", "title": "status", "type": "VarValue", "val": "status", "value": "extM$amodel.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "extM$sceneb-detail-bianji-condition-id2", "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "extM$sceneb-detail-extM$amodel-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"serviceKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "extM$sceneb-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "extM$sceneb-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "extM$sceneb-detailView-extM$amodel-field-number", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "number", "modelAlias": "extM$amodel", "placeholder": "请输入", "precision": 6}, "editable": false, "label": "数字", "name": "number", "type": "NUMBER"}}, {"children": [], "key": "extM$sceneb-detailView-extM$amodel-field-textte", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "textte", "modelAlias": "extM$amodel", "placeholder": "请输入"}, "editable": false, "label": "文本", "name": "textte", "type": "TEXT"}}, {"children": [], "key": "extM$sceneb-detailView-extM$amodel-field-enumenu", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "enumenu", "modelAlias": "extM$amodel", "placeholder": "请选择"}, "editable": false, "label": "枚举", "name": "enumenu", "type": "SELECT"}}], "key": "extM$sceneb-detailView-extM$amodel-detail-defaultGroup", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "extM$sceneb-bI2Hcscun52DnbYRB8Vxl", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "extM$sceneb-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "extM$user", "parentModelAlias": "extM$amodel", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "extM$SYS_FindDataByIdService", "searchServiceKey": "extM$SYS_PagingDataService"}}, "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "extM$sceneb-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "extM$user", "parentModelAlias": "extM$amodel", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "extM$SYS_FindDataByIdService", "searchServiceKey": "extM$SYS_PagingDataService"}}, "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "extM$sceneb-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "extM$amodel", "placeholder": "请选择"}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "extM$sceneb-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "extM$amodel", "placeholder": "请选择"}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "extM$sceneb-detailView-system-detail-group", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "extM$sceneb-ed5ceAtB1H0ggkGsw7Kz9", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "extM$sceneb-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"key": "AM6cdfjFwhxaqyc3YHLdC", "label": "主体信息"}, {"key": "VH4jVYWSbCT9l_JvhR9yZ", "label": "系统信息"}], "underline": true}, "type": "Layout"}], "key": "extM$sceneb-detailView-extM$amodel-detail", "name": "Detail", "props": {"flow": {"containerKey": "extM$sceneb-detailView-extM$amodel-detail", "context$": "$context", "modelAlias": "extM$amodel", "serviceKey": "extM$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "extM$amodel", "onFinish$": "(values) => invokeSystemService(\"extM$SYS_MasterData_SaveDataService\", \"extM$amodel\", {...data, ...values}).then(() => $(\"extM$sceneb-detailView-page\").action(\"reload\"))", "serviceKey": "extM$SYS_FindDataByIdService"}, "type": "Container"}, {"children": [], "key": "extM$sceneb-detailView-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "extM$sceneb-detailView-page", "name": "Page", "props": {"params": [], "showFooter": false, "showHeader": true}, "type": "Container"}], "key": "extM$sceneb-detailView-view", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [], "key": "extM$sceneb-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" + ((data?.code || \"\") + (data?.name || \"amodel\")) : \"新建amodel\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "extM$sceneb-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "extM$sceneb-editView-extM$amodel-field-number", "name": "FormField", "props": {"componentProps": {"fieldAlias": "number", "modelAlias": "extM$amodel", "placeholder": "请输入", "precision": 6}, "hidden": false, "label": "数字", "name": "number", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "extM$sceneb-editView-extM$amodel-field-textte", "name": "FormField", "props": {"componentProps": {"fieldAlias": "textte", "modelAlias": "extM$amodel", "placeholder": "请输入"}, "hidden": false, "label": "文本", "name": "textte", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "extM$sceneb-editView-extM$amodel-field-enumenu", "name": "FormField", "props": {"componentProps": {"fieldAlias": "enumenu", "modelAlias": "extM$amodel", "placeholder": "请选择"}, "hidden": false, "label": "枚举", "name": "enumenu", "rules": [], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "extM$sceneb-editView-extM$amodel-field-id", "name": "FormField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "extM$amodel", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "extM$sceneb-editView-extM$amodel-field-createdBy", "name": "FormField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "extM$user", "parentModelAlias": "extM$amodel", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "extM$SYS_FindDataByIdService", "searchServiceKey": "extM$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "extM$sceneb-editView-extM$amodel-field-updatedBy", "name": "FormField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "extM$user", "parentModelAlias": "extM$amodel", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "extM$SYS_FindDataByIdService", "searchServiceKey": "extM$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "extM$sceneb-editView-extM$amodel-field-createdAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "extM$amodel", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "extM$sceneb-editView-extM$amodel-field-updatedAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "extM$amodel", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "extM$sceneb-editView-extM$amodel-field-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "extM$amodel", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "extM$sceneb-editView-extM$amodel-field-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "extM$amodel", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}], "key": "extM$sceneb-editView-extM$amodel-form-defaultGroup", "name": "FormGroupItem", "props": {"showSplit": true, "title": false}, "type": "Layout"}], "key": "extM$sceneb-8Fe5EZ_28wYhZ7gllWgEJ", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "extM$sceneb-editView-defaultTabs", "name": "Tabs", "props": {"items": [{"key": "PybM50PYXsKUauF6jPmi1", "label": "主体信息"}, {"key": "D0uQI9GiPyrhXmVSW8RCr", "label": "页签2"}], "underline": true}, "type": "Layout"}], "key": "extM$sceneb-editView-extM$amodel-form", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "extM$sceneb-editView-extM$amodel-form", "context$": "$context", "modelAlias": "extM$amodel", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "extM$amodel"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "route.recordId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "extM$SYS_FindDataByIdService", "test$": "!!route.recordId", "type": "InvokeSystemService"}, {"containerKey": "extM$sceneb-editView-extM$amodel-form", "context$": "$context", "modelAlias": "extM$amodel", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "extM$amodel"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "route.query?.copyId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "extM$SYS_CopyDataConverterService", "test$": "!!route.query?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "extM$amodel", "serviceKey": "extM$SYS_FindDataByIdService"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "extM$sceneb-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "ConditionFlowAction", "children": [{"action": "RefreshTab", "target": ["current"]}], "condition": {"conditions": [{"conditions": [{"id": "extM$sceneb-cancel-condition-flow-1-2", "leftValue": {"fieldType": "Text", "scope": "route", "val": "recordId"}, "operator": "IS_NULL", "type": "ConditionLeaf"}], "id": "extM$sceneb-cancel-condition-flow-1-1", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "extM$sceneb-cancel-condition-flow-1", "logicOperator": "OR", "type": "ConditionGroup"}}, {"action": "ConditionFlowAction", "children": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "route.recordId", "name": "recordId", "type": "expression"}], "refresh": true, "type": "NewPage"}}], "condition": {"conditions": [{"conditions": [{"id": "extM$sceneb-cancel-condition-flow-2-2", "leftValue": {"fieldType": "Text", "scope": "route", "val": "recordId"}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "extM$sceneb-cancel-condition-flow-2-1", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "extM$sceneb-cancel-condition-flow-2", "logicOperator": "OR", "type": "ConditionGroup"}}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "type": "default"}, "type": "Widget"}, {"children": [], "key": "extM$sceneb-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "extM$amodel"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "valueConfig": {"action": {"target": "extM$sceneb-editView-extM$amodel-form"}, "type": "action"}}], "service": "extM$SYS_MasterData_SaveDataService"}, "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "data.id", "name": "recordId", "serviceKey": "extM$SYS_MasterData_SaveDataService", "type": "expression"}], "refresh": true, "type": "NewPage"}}, {"action": "Refresh", "target": ["extM$sceneb-list-extM$amodel"]}, {"action": "Message", "level": "success", "message": "保存成功!"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "type": "primary"}, "type": "Widget"}], "key": "extM$sceneb-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "extM$sceneb-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "extM$sceneb-editView", "name": "Page", "props": {"params": [], "showFooter": true, "showHeader": true}, "type": "Container"}], "key": "extM$sceneb-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "extM$sceneb-column-page", "name": "ColumnPage", "props": {}, "type": "Layout"}, {"children": [], "key": "extM$sceneb-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "extM$sceneb-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [], "key": "extM$sceneb-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "extM$sceneb-page", "name": "Page", "props": {"params": [], "showFooter": false, "showHeader": false, "title": "模块场景B"}, "type": "Container"}, "type": "LIST", "resources": [{"type": "Container", "description": null, "key": "extM$sceneb-list-extM$amodel", "label": "表格", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}], "relations": [{"type": "SystemService", "key": "extM$SYS_FindDataByIdService", "name": null, "props": {"modelAlias": "extM$user", "httpMethod": null}}, {"type": "SystemService", "key": "extM$SYS_PagingDataService", "name": null, "props": {"modelAlias": "extM$user", "httpMethod": null}}, {"type": "SystemService", "key": "extM$SYS_PagingDataService", "name": null, "props": {"modelAlias": "extM$amodel", "httpMethod": null}}]}, {"type": "<PERSON><PERSON>", "description": null, "key": "extM$sceneb-list-extM$amodel-new", "label": "新建", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-list-extM$amodel", "label": "表格", "type": "Table"}, {"key": "extM$sceneb-list-extM$amodel-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": []}, {"type": "<PERSON><PERSON>", "description": null, "key": "extM$sceneb-list-extM$amodel-batch/items/extM$sceneb-extM$amodel-multi-delete", "label": "批量删除", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-list-extM$amodel", "label": "表格", "type": "Table"}, {"key": "extM$sceneb-list-extM$amodel-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "extM$sceneb-list-extM$amodel-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"type": "SystemService", "key": "extM$SYS_BatchDeleteDataService", "name": null, "props": {"modelAlias": "extM$amodel", "httpMethod": null}}]}, {"type": "Container", "description": null, "key": "extM$sceneb-list-extM$amodel-import", "label": "导入", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-list-extM$amodel", "label": "表格", "type": "Table"}, {"key": "extM$sceneb-list-extM$amodel-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"type": "Service", "key": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "name": null, "props": {"modelAlias": null, "httpMethod": null}}, {"type": "Api", "key": "/api/gei/task/import-sub-model", "name": null, "props": {"modelAlias": null, "httpMethod": "GET"}}, {"type": "Service", "key": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "name": null, "props": {"modelAlias": null, "httpMethod": null}}, {"type": "Api", "key": "/api/gei/task/import-direct-by-oss", "name": null, "props": {"modelAlias": null, "httpMethod": "GET"}}, {"type": "Service", "key": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "name": null, "props": {"modelAlias": null, "httpMethod": null}}, {"type": "Api", "key": "/api/gei/template/download", "name": null, "props": {"modelAlias": null, "httpMethod": "GET"}}, {"type": "Service", "key": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "name": null, "props": {"modelAlias": null, "httpMethod": null}}]}, {"type": "Container", "description": null, "key": "extM$sceneb-list-extM$amodel-export", "label": "导出", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-list-extM$amodel", "label": "表格", "type": "Table"}, {"key": "extM$sceneb-list-extM$amodel-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"type": "Service", "key": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "name": null, "props": {"modelAlias": null, "httpMethod": null}}, {"type": "Service", "key": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"modelAlias": null, "httpMethod": null}}, {"type": "Service", "key": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"modelAlias": null, "httpMethod": null}}, {"type": "Api", "key": "/api/trantor/portal/user/current", "name": null, "props": {"modelAlias": null, "httpMethod": "GET"}}]}, {"type": "Container", "description": null, "key": "extM$sceneb-list-extM$amodel-logs", "label": "日志", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-list-extM$amodel", "label": "表格", "type": "Table"}, {"key": "extM$sceneb-list-extM$amodel-toolbar-actions", "label": "按钮组", "type": "ToolbarActions"}], "relations": [{"type": "Service", "key": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "name": null, "props": {"modelAlias": null, "httpMethod": null}}]}, {"type": "Container", "description": null, "key": "extM$sceneb-IzbkRj_yNTxZqC00fEQsT", "label": "数字", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-list-extM$amodel", "label": "表格", "type": "Table"}, {"key": "extM$sceneb-4PRFJOwtZX0EBf9isqUnT", "label": "字段组", "type": "Fields"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-NE8UKHEjYXNKDiBNXjQ-h", "label": "文本", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-list-extM$amodel", "label": "表格", "type": "Table"}, {"key": "extM$sceneb-4PRFJOwtZX0EBf9isqUnT", "label": "字段组", "type": "Fields"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-NC_OKRXED4ZZJ1Nx1W8mv", "label": "枚举", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-list-extM$amodel", "label": "表格", "type": "Table"}, {"key": "extM$sceneb-4PRFJOwtZX0EBf9isqUnT", "label": "字段组", "type": "Fields"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-A3PCXbH7IvzlEi9LSPSsq", "label": "ID", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-list-extM$amodel", "label": "表格", "type": "Table"}, {"key": "extM$sceneb-4PRFJOwtZX0EBf9isqUnT", "label": "字段组", "type": "Fields"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-Lqlbh-aYexwfxc8zSB2SS", "label": "创建人", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-list-extM$amodel", "label": "表格", "type": "Table"}, {"key": "extM$sceneb-4PRFJOwtZX0EBf9isqUnT", "label": "字段组", "type": "Fields"}], "relations": [{"type": "SystemService", "key": "extM$SYS_FindDataByIdService", "name": null, "props": {"modelAlias": "extM$user", "httpMethod": null}}, {"type": "SystemService", "key": "extM$SYS_PagingDataService", "name": null, "props": {"modelAlias": "extM$user", "httpMethod": null}}]}, {"type": "Container", "description": null, "key": "extM$sceneb-fmVoTXmcArghOIYphKNze", "label": "更新人", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-list-extM$amodel", "label": "表格", "type": "Table"}, {"key": "extM$sceneb-4PRFJOwtZX0EBf9isqUnT", "label": "字段组", "type": "Fields"}], "relations": [{"type": "SystemService", "key": "extM$SYS_FindDataByIdService", "name": null, "props": {"modelAlias": "extM$user", "httpMethod": null}}, {"type": "SystemService", "key": "extM$SYS_PagingDataService", "name": null, "props": {"modelAlias": "extM$user", "httpMethod": null}}]}, {"type": "Container", "description": null, "key": "extM$sceneb-wbhRrz96BHryhbqsC2pB_", "label": "创建时间", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-list-extM$amodel", "label": "表格", "type": "Table"}, {"key": "extM$sceneb-4PRFJOwtZX0EBf9isqUnT", "label": "字段组", "type": "Fields"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-kL4gQbF8TsK7niYDudbMG", "label": "更新时间", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-list-extM$amodel", "label": "表格", "type": "Table"}, {"key": "extM$sceneb-4PRFJOwtZX0EBf9isqUnT", "label": "字段组", "type": "Fields"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-I7FQT-eYN4x2qcHqqf2y4", "label": "版本号", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-list-extM$amodel", "label": "表格", "type": "Table"}, {"key": "extM$sceneb-4PRFJOwtZX0EBf9isqUnT", "label": "字段组", "type": "Fields"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-L1YRJ-qOCUTo2uXBayqC_", "label": "逻辑删除标识", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-list-extM$amodel", "label": "表格", "type": "Table"}, {"key": "extM$sceneb-4PRFJOwtZX0EBf9isqUnT", "label": "字段组", "type": "Fields"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-editView-extM$amodel-form", "label": "表单组", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-editView", "label": "页面", "type": "Page"}], "relations": [{"type": "SystemService", "key": "extM$SYS_FindDataByIdService", "name": null, "props": {"modelAlias": "extM$amodel", "httpMethod": null}}, {"type": "SystemService", "key": "extM$SYS_CopyDataConverterService", "name": null, "props": {"modelAlias": "extM$amodel", "httpMethod": null}}]}, {"type": "Container", "description": null, "key": "extM$sceneb-detailView-extM$amodel-detail", "label": "详情", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-detailView-view", "label": "视图", "type": "View"}, {"key": "extM$sceneb-detailView-page", "label": "页面", "type": "Page"}], "relations": [{"type": "SystemService", "key": "extM$SYS_FindDataByIdService", "name": null, "props": {"modelAlias": "extM$amodel", "httpMethod": null}}]}, {"type": "<PERSON><PERSON>", "description": null, "key": "extM$sceneb-editView-action-cancel", "label": "取消", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-editView", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "extM$sceneb-editView-actions", "label": "间距", "type": "Space"}], "relations": []}, {"type": "<PERSON><PERSON>", "description": null, "key": "extM$sceneb-editView-action-save", "label": "保存", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-editView", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "extM$sceneb-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"type": "SystemService", "key": "extM$SYS_MasterData_SaveDataService", "name": null, "props": {"modelAlias": "extM$amodel", "httpMethod": null}}]}, {"type": "<PERSON><PERSON>", "description": null, "key": "extM$sceneb-detailView-actions-delete", "label": "删除", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-detailView-view", "label": "视图", "type": "View"}, {"key": "extM$sceneb-detailView-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "extM$sceneb-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"type": "SystemService", "key": "extM$SYS_DeleteDataByIdService", "name": null, "props": {"modelAlias": "extM$amodel", "httpMethod": null}}]}, {"type": "<PERSON><PERSON>", "description": null, "key": "extM$sceneb-detailView-actions-copy", "label": "复制", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-detailView-view", "label": "视图", "type": "View"}, {"key": "extM$sceneb-detailView-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "extM$sceneb-detailView-actions", "label": "间距", "type": "Space"}], "relations": []}, {"type": "<PERSON><PERSON>", "description": null, "key": "extM$sceneb-detailView-actions-edit", "label": "编辑", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-detailView-view", "label": "视图", "type": "View"}, {"key": "extM$sceneb-detailView-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "extM$sceneb-detailView-actions", "label": "间距", "type": "Space"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-detail-extM$amodel-logs", "label": "日志", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-detailView-view", "label": "视图", "type": "View"}, {"key": "extM$sceneb-detailView-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "extM$sceneb-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"type": "Service", "key": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "name": null, "props": {"modelAlias": null, "httpMethod": null}}]}, {"type": "Container", "description": null, "key": "extM$sceneb-editView-extM$amodel-field-number", "label": "数字", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-editView", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-editView-extM$amodel-form", "label": "表单组", "type": "FormGroup"}, {"key": "extM$sceneb-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "extM$sceneb-8Fe5EZ_28wYhZ7gllWgEJ", "label": "页签项", "type": "TabItem"}, {"key": "extM$sceneb-editView-extM$amodel-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-editView-extM$amodel-field-textte", "label": "文本", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-editView", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-editView-extM$amodel-form", "label": "表单组", "type": "FormGroup"}, {"key": "extM$sceneb-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "extM$sceneb-8Fe5EZ_28wYhZ7gllWgEJ", "label": "页签项", "type": "TabItem"}, {"key": "extM$sceneb-editView-extM$amodel-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-editView-extM$amodel-field-enumenu", "label": "枚举", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-editView", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-editView-extM$amodel-form", "label": "表单组", "type": "FormGroup"}, {"key": "extM$sceneb-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "extM$sceneb-8Fe5EZ_28wYhZ7gllWgEJ", "label": "页签项", "type": "TabItem"}, {"key": "extM$sceneb-editView-extM$amodel-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-editView-extM$amodel-field-id", "label": "ID", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-editView", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-editView-extM$amodel-form", "label": "表单组", "type": "FormGroup"}, {"key": "extM$sceneb-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "extM$sceneb-8Fe5EZ_28wYhZ7gllWgEJ", "label": "页签项", "type": "TabItem"}, {"key": "extM$sceneb-editView-extM$amodel-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-editView-extM$amodel-field-createdBy", "label": "创建人", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-editView", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-editView-extM$amodel-form", "label": "表单组", "type": "FormGroup"}, {"key": "extM$sceneb-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "extM$sceneb-8Fe5EZ_28wYhZ7gllWgEJ", "label": "页签项", "type": "TabItem"}, {"key": "extM$sceneb-editView-extM$amodel-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"type": "SystemService", "key": "extM$SYS_FindDataByIdService", "name": null, "props": {"modelAlias": "extM$user", "httpMethod": null}}, {"type": "SystemService", "key": "extM$SYS_PagingDataService", "name": null, "props": {"modelAlias": "extM$user", "httpMethod": null}}]}, {"type": "Container", "description": null, "key": "extM$sceneb-editView-extM$amodel-field-updatedBy", "label": "更新人", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-editView", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-editView-extM$amodel-form", "label": "表单组", "type": "FormGroup"}, {"key": "extM$sceneb-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "extM$sceneb-8Fe5EZ_28wYhZ7gllWgEJ", "label": "页签项", "type": "TabItem"}, {"key": "extM$sceneb-editView-extM$amodel-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"type": "SystemService", "key": "extM$SYS_FindDataByIdService", "name": null, "props": {"modelAlias": "extM$user", "httpMethod": null}}, {"type": "SystemService", "key": "extM$SYS_PagingDataService", "name": null, "props": {"modelAlias": "extM$user", "httpMethod": null}}]}, {"type": "Container", "description": null, "key": "extM$sceneb-editView-extM$amodel-field-createdAt", "label": "创建时间", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-editView", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-editView-extM$amodel-form", "label": "表单组", "type": "FormGroup"}, {"key": "extM$sceneb-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "extM$sceneb-8Fe5EZ_28wYhZ7gllWgEJ", "label": "页签项", "type": "TabItem"}, {"key": "extM$sceneb-editView-extM$amodel-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-editView-extM$amodel-field-updatedAt", "label": "更新时间", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-editView", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-editView-extM$amodel-form", "label": "表单组", "type": "FormGroup"}, {"key": "extM$sceneb-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "extM$sceneb-8Fe5EZ_28wYhZ7gllWgEJ", "label": "页签项", "type": "TabItem"}, {"key": "extM$sceneb-editView-extM$amodel-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-editView-extM$amodel-field-version", "label": "版本号", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-editView", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-editView-extM$amodel-form", "label": "表单组", "type": "FormGroup"}, {"key": "extM$sceneb-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "extM$sceneb-8Fe5EZ_28wYhZ7gllWgEJ", "label": "页签项", "type": "TabItem"}, {"key": "extM$sceneb-editView-extM$amodel-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-editView-extM$amodel-field-deleted", "label": "逻辑删除标识", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-editView", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-editView-extM$amodel-form", "label": "表单组", "type": "FormGroup"}, {"key": "extM$sceneb-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "extM$sceneb-8Fe5EZ_28wYhZ7gllWgEJ", "label": "页签项", "type": "TabItem"}, {"key": "extM$sceneb-editView-extM$amodel-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-detailView-extM$amodel-field-number", "label": "数字", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-detailView-view", "label": "视图", "type": "View"}, {"key": "extM$sceneb-detailView-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-detailView-extM$amodel-detail", "label": "详情", "type": "Detail"}, {"key": "extM$sceneb-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "extM$sceneb-bI2Hcscun52DnbYRB8Vxl", "label": "页签项", "type": "TabItem"}, {"key": "extM$sceneb-detailView-extM$amodel-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-detailView-extM$amodel-field-textte", "label": "文本", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-detailView-view", "label": "视图", "type": "View"}, {"key": "extM$sceneb-detailView-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-detailView-extM$amodel-detail", "label": "详情", "type": "Detail"}, {"key": "extM$sceneb-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "extM$sceneb-bI2Hcscun52DnbYRB8Vxl", "label": "页签项", "type": "TabItem"}, {"key": "extM$sceneb-detailView-extM$amodel-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-detailView-extM$amodel-field-enumenu", "label": "枚举", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-detailView-view", "label": "视图", "type": "View"}, {"key": "extM$sceneb-detailView-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-detailView-extM$amodel-detail", "label": "详情", "type": "Detail"}, {"key": "extM$sceneb-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "extM$sceneb-bI2Hcscun52DnbYRB8Vxl", "label": "页签项", "type": "TabItem"}, {"key": "extM$sceneb-detailView-extM$amodel-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-detailView-system-detail-field-createdBy", "label": "创建人", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-detailView-view", "label": "视图", "type": "View"}, {"key": "extM$sceneb-detailView-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-detailView-extM$amodel-detail", "label": "详情", "type": "Detail"}, {"key": "extM$sceneb-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "extM$sceneb-ed5ceAtB1H0ggkGsw7Kz9", "label": "页签项", "type": "TabItem"}, {"key": "extM$sceneb-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [{"type": "SystemService", "key": "extM$SYS_FindDataByIdService", "name": null, "props": {"modelAlias": "extM$user", "httpMethod": null}}, {"type": "SystemService", "key": "extM$SYS_PagingDataService", "name": null, "props": {"modelAlias": "extM$user", "httpMethod": null}}]}, {"type": "Container", "description": null, "key": "extM$sceneb-detailView-system-detail-field-updatedBy", "label": "更新人", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-detailView-view", "label": "视图", "type": "View"}, {"key": "extM$sceneb-detailView-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-detailView-extM$amodel-detail", "label": "详情", "type": "Detail"}, {"key": "extM$sceneb-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "extM$sceneb-ed5ceAtB1H0ggkGsw7Kz9", "label": "页签项", "type": "TabItem"}, {"key": "extM$sceneb-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [{"type": "SystemService", "key": "extM$SYS_FindDataByIdService", "name": null, "props": {"modelAlias": "extM$user", "httpMethod": null}}, {"type": "SystemService", "key": "extM$SYS_PagingDataService", "name": null, "props": {"modelAlias": "extM$user", "httpMethod": null}}]}, {"type": "Container", "description": null, "key": "extM$sceneb-detailView-system-detail-field-createdAt", "label": "创建时间", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-detailView-view", "label": "视图", "type": "View"}, {"key": "extM$sceneb-detailView-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-detailView-extM$amodel-detail", "label": "详情", "type": "Detail"}, {"key": "extM$sceneb-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "extM$sceneb-ed5ceAtB1H0ggkGsw7Kz9", "label": "页签项", "type": "TabItem"}, {"key": "extM$sceneb-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": []}, {"type": "Container", "description": null, "key": "extM$sceneb-detailView-system-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "extM$sceneb-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "extM$sceneb-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "extM$sceneb-detailView-view", "label": "视图", "type": "View"}, {"key": "extM$sceneb-detailView-page", "label": "页面", "type": "Page"}, {"key": "extM$sceneb-detailView-extM$amodel-detail", "label": "详情", "type": "Detail"}, {"key": "extM$sceneb-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "extM$sceneb-ed5ceAtB1H0ggkGsw7Kz9", "label": "页签项", "type": "TabItem"}, {"key": "extM$sceneb-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": []}], "containerSelect": {"extM$sceneb-detailView-extM$amodel-detail": [{"field": "number", "selectFields": null}, {"field": "textte", "selectFields": null}, {"field": "enumenu", "selectFields": null}, {"field": "created<PERSON>y", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "updatedBy", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "createdAt", "selectFields": null}, {"field": "updatedAt", "selectFields": null}], "extM$sceneb-editView-extM$amodel-form": [{"field": "number", "selectFields": null}, {"field": "textte", "selectFields": null}, {"field": "enumenu", "selectFields": null}, {"field": "version", "selectFields": null}], "extM$sceneb-list-extM$amodel": [{"field": "number", "selectFields": null}, {"field": "textte", "selectFields": null}, {"field": "version", "selectFields": null}]}}], "type": "DATA"}, "endpointType": "PC"}