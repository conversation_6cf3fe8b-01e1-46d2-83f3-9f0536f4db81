package io.terminus.trantor2.meta.api.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 视图权限
 *
 * <AUTHOR>
 */
@Data
public class ViewPermissionDTO {
    /**
     * 视图key
     */
    private String viewKey;

    /**
     * 视图自身的权限key
     */
    private String permissionKey;

    /**
     * 视图内组件清单
     */
    private List<ComponentPermissionDTO> components;

    @JsonIgnore
    public Set<String> getAllDataConditionKeys() {
        Set<String> dataConditionKeys = new HashSet<>();
        this.getComponents().forEach(c -> {
            c.getBindServices().forEach(s -> {
                if (s.getDataConditionKeys() != null) {
                    dataConditionKeys.addAll(s.getDataConditionKeys());
                }
            });
            c.getBindSysServices().forEach(s -> {
                if (s.getDataConditionKeys() != null) {
                    dataConditionKeys.addAll(s.getDataConditionKeys());
                }
            });
            c.getVirtualComponents().forEach(vc -> {
                vc.getBindServices().forEach(s -> {
                    if (s.getDataConditionKeys() != null) {
                        dataConditionKeys.addAll(s.getDataConditionKeys());
                    }
                });
                vc.getBindSysServices().forEach(s -> {
                    if (s.getDataConditionKeys() != null) {
                        dataConditionKeys.addAll(s.getDataConditionKeys());
                    }
                });
            });
        });
        return dataConditionKeys;
    }

    /**
     * 组件权限
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class ComponentPermissionDTO extends BaseComponentPermissionDataDTO {
        /**
         * 视图key
         */
        private String viewKey;

        /**
         * 组件key
         */
        private String compKey;

        /**
         * 组件名
         */
        private String compName;

        /**
         * 组件标题
         */
        private String compTitle;

        /**
         * 是否开启组件级功能权限
         * 来源于 view content 中组件 props.permissionEnabled 字段
         */
        private Boolean permissionEnabled;

        /**
         * 虚拟组件
         * <p>
         * 组件内部可能会有虚拟按钮，进一步细分权限
         */
        private List<VirtualComponentPermissionDTO> virtualComponents;

        /**
         * 组件是否开启权限访问控制
         * @return  Boolean true：开启权限，false：关闭权限
         */
        public boolean isPermissionEnabled() {
            return Objects.isNull(permissionEnabled) || Objects.equals(Boolean.TRUE, permissionEnabled);
        }
    }

    /**
     * 虚拟组件
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class VirtualComponentPermissionDTO extends BaseComponentPermissionDataDTO {
        /**
         * 虚拟组件key
         */
        private String virtualCompKey;

        /**
         * 虚拟组件完整key
         */
        private String virtualCompFullKey;

        /**
         * 虚拟组件标题
         */
        private String virtualCompTitle;
    }

    @Data
    public static class BaseComponentPermissionDataDTO {
        /**
         * 资源类型：Button, Container
         */
        private String resourceType;

        /**
         * 资源路径
         */
        private List<String> resourcePath;

        /**
         * 最终推倒出来的权限key <strong>[for authz]</strong>
         * <p>
         * 1）如果组件本身配了权限，那么resolvedPermissionKeys = compPermissionKey,
         * see {@link #compPermissionKey}
         * <p>
         * 2）如果组件没有配权限，但是组件绑定了服务或者api，那么resolvedPermissionKeys = compPermissionKey + bindServices.permissionKey + bindApis.permissionKey
         * see {@link #bindServices} and {@link #bindApis}
         */
        private Set<String> resolvedPermissionKeys;

        /**
         * 组件所有的权限key <strong>[for authn]</strong>
         * <p>
         * 包括组件本身的权限key、组件绑定的服务的权限key、组件绑定的api的权限key
         */
        private Set<String> allPermissionKeys;

        /**
         * 组件本身的权限key
         */
        private String compPermissionKey;

        /**
         * 组件绑定的服务
         */
        private List<BindServicePermissionDTO> bindServices;

        /**
         * 组件绑定的系统服务
         */
        private List<BindSysServicePermissionDTO> bindSysServices;

        /**
         * 组件绑定的api
         */
        private List<BindApiPermissionDTO> bindApis;

        /**
         * 组建绑定的AIAgent
         */
        private List<BindAIAgentPermissionDTO> bindAIAgents;

        @JsonIgnore
        public void resolve() {
            this.setResolvedPermissionKeys(new HashSet<>());
            this.setAllPermissionKeys(new HashSet<>());
            if (this.getCompPermissionKey() != null) {
                this.getResolvedPermissionKeys().add(this.getCompPermissionKey());
                this.getAllPermissionKeys().add(this.getCompPermissionKey());
            }
            this.getBindServices().forEach(it -> {
                if (it.getPermissionKey() != null) {
                    if (this.getCompPermissionKey() == null) {
                        this.getResolvedPermissionKeys().add(it.getPermissionKey());
                    }
                    this.getAllPermissionKeys().add(it.getPermissionKey());
                }
            });
            this.getBindSysServices().forEach(it -> {
                if (it.getPermissionKey() != null) {
                    if (this.getCompPermissionKey() == null) {
                        this.getResolvedPermissionKeys().add(it.getPermissionKey());
                    }
                    this.getAllPermissionKeys().add(it.getPermissionKey());
                }
            });
            this.getBindApis().forEach(it -> {
                if (it.getPermissionKey() != null) {
                    if (this.getCompPermissionKey() == null) {
                        this.getResolvedPermissionKeys().add(it.getPermissionKey());
                    }
                    this.getAllPermissionKeys().add(it.getPermissionKey());
                }
            });
        }
    }

    /**
     * 绑定服务权限
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class BindServicePermissionDTO extends BindService {
        /**
         * 服务的权限key
         */
        private String permissionKey;

        /**
         * 服务的数据规则key
         */
        private Set<String> dataConditionKeys;
    }

    /**
     * 绑定服务
     */
    @Data
    public static class BindService {
        /**
         * 服务key
         */
        private String serviceKey;
    }

    /**
     * 绑定系统服务权限
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class BindSysServicePermissionDTO extends BindSysService {
        /**
         * 系统服务的权限key
         */
        private String permissionKey;

        /**
         * 系统服务的数据规则key
         */
        private Set<String> dataConditionKeys;
    }

    /**
     * 绑定系统服务
     */
    @Data
    public static class BindSysService {
        /**
         * 系统服务key
         */
        private String serviceKey;

        /**
         * 模型key
         */
        private String modelKey;
    }

    /**
     * 绑定api权限
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class BindApiPermissionDTO extends BindApi {
        /**
         * api的权限key
         */
        private String permissionKey;
    }

    /**
     * 绑定api
     */
    @Data
    public static class BindApi {
        /**
         * api路径
         */
        private String apiPath;

        /**
         * api方法
         */
        private String apiMethod;
    }

    /**
     * 绑定智能体权限
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class BindAIAgentPermissionDTO extends BindAIAgent {
        /**
         * ai agent的权限key
         */
        private String permissionKey;

        /**
         * ai agent 中用到的服务，以及服务的权限项
         */
        private List<BindServicePermissionDTO> bindServices;
    }

    /**
     * 绑定智能体
     */
    @Data
    public static class BindAIAgent {
        /**
         * ai agent key
         */
        private String aiAgentKey;
    }
}
