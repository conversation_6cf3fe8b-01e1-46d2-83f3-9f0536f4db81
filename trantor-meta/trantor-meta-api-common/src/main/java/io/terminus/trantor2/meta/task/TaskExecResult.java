package io.terminus.trantor2.meta.task;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

@Jackson<PERSON>
@Builder
@Getter
@AllArgsConstructor
public final class TaskExecResult {
    private final Long taskId;
    private final String url;

    public TaskExecResult(Long taskId, Long teamId) {
        String baseUrl = ServletUriComponentsBuilder.fromCurrentContextPath().build().toUriString();
        if (baseUrl.endsWith(":80")) {
            baseUrl = baseUrl.substring(0, baseUrl.length() - 3);
        }
        this.url = baseUrl + "/team/" + teamId + "/task/async?pageNo=1&pageSize=20&taskRunId=" + taskId;
        this.taskId = taskId;
    }
}
