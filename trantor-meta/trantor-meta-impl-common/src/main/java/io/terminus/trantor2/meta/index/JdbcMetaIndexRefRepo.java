package io.terminus.trantor2.meta.index;

import io.terminus.trantor2.meta.jdbc.MetaJdbcTemplateFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
public class JdbcMetaIndexRefRepo implements MetaIndexRefRepo {

    private final MetaJdbcTemplateFactory metaJdbcTemplateFactory;

    public JdbcMetaIndexRefRepo(MetaJdbcTemplateFactory metaJdbcTemplateFactory) {
        this.metaJdbcTemplateFactory = metaJdbcTemplateFactory;
    }

    private NamedParameterJdbcTemplate getNamedParameterJdbcTemplate() {
        return metaJdbcTemplateFactory.createNamedParameterJdbcTemplate();
    }

    @Override
    public void create(Long teamId, List<EntityRef> refs) {
        if (refs == null || refs.isEmpty()) {
            return;
        }
        String insertSql = "INSERT INTO trantor_meta_index_ref ( " +
                "team_id, source_key, source_type, target_key, target_type, created_at, updated_at) " +
                "VALUES (:teamId, :sourceKey, :sourceType, :targetKey, :targetType, NOW(), NOW())";
        SqlParameterSource[] psArray = refs.stream().map(idx -> new MapSqlParameterSource()
                .addValue("teamId", teamId)
                .addValue("sourceKey", idx.getSourceKey())
                .addValue("sourceType", Optional.ofNullable(idx.getSourceType()).orElse(""))
                .addValue("targetKey", idx.getTargetKey())
                .addValue("targetType", Optional.ofNullable(idx.getTargetType()).orElse(""))
        ).toArray(SqlParameterSource[]::new);
        getNamedParameterJdbcTemplate().batchUpdate(insertSql, psArray);
    }

    @Override
    public void deleteBySourceKeys(Long teamId, Collection<String> sourceKeys) {
        if (sourceKeys == null || sourceKeys.isEmpty()) {
            return;
        }
        String deleteSql = "DELETE FROM trantor_meta_index_ref WHERE team_id = :teamId AND source_key IN (:sourceKeys)";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("teamId", teamId);
        paramMap.put("sourceKeys", sourceKeys);
        getNamedParameterJdbcTemplate().update(deleteSql, paramMap);
    }

    @Override
    public void delete(Long teamId, Collection<MetaIndexRef> refs) {
        if (refs == null || refs.isEmpty()) {
            return;
        }
        String deleteSql = "DELETE FROM trantor_meta_index_ref WHERE team_id = :teamId AND source_key = :sourceKey AND target_key = :targetKey";
        SqlParameterSource[] psArray = refs.stream().map(idx -> new MapSqlParameterSource()
                .addValue("teamId", teamId)
                .addValue("sourceKey", idx.getSourceKey())
                .addValue("targetKey", idx.getTargetKey())
        ).toArray(SqlParameterSource[]::new);
        getNamedParameterJdbcTemplate().batchUpdate(deleteSql, psArray);
    }

    @Override
    public List<String> findAllKeys(Long teamId) {
        String sql = "SELECT DISTINCT source_key FROM trantor_meta_index_ref WHERE team_id = :teamId";
        SqlParameterSource ps = new MapSqlParameterSource().addValue("teamId", teamId);
        return getNamedParameterJdbcTemplate().queryForList(sql, ps, String.class);
    }

    @Override
    public List<MetaIndexRef> find(Long teamId, RefCond refCond) {
        // check refCond valid
        RefCond.check(refCond);
        // base sql
        String sql = "SELECT source_key, source_type, target_key, target_type FROM trantor_meta_index_ref WHERE team_id = :teamId";
        MapSqlParameterSource ps = new MapSqlParameterSource().addValue("teamId", teamId);
        if (refCond.getSourceKeys() != null && !refCond.getSourceKeys().isEmpty()) {
            sql += " AND source_key IN (:sourceKeys)";
            ps.addValue("sourceKeys", refCond.getSourceKeys());
        }
        if (refCond.getSourceTypes() != null && !refCond.getSourceTypes().isEmpty()) {
            sql += " AND source_type IN (:sourceTypes)";
            ps.addValue("sourceTypes", refCond.getSourceTypes());
        }
        if (refCond.getTargetKeys() != null && !refCond.getTargetKeys().isEmpty()) {
            sql += " AND target_key IN (:targetKeys)";
            ps.addValue("targetKeys", refCond.getTargetKeys());
        }
        if (refCond.getTargetTypes() != null && !refCond.getTargetTypes().isEmpty()) {
            sql += " AND target_type IN (:targetTypes)";
            ps.addValue("targetTypes", refCond.getTargetTypes());
        }
        return getNamedParameterJdbcTemplate().query(sql, ps, this::mapRow);
    }

    @Override
    public Map<String, Integer> countByTargetKeys(Long teamId) {
        String sql = "SELECT target_key, COUNT(1) AS cnt FROM trantor_meta_index_ref WHERE team_id = :teamId GROUP BY target_key";
        SqlParameterSource ps = new MapSqlParameterSource().addValue("teamId", teamId);
        return getNamedParameterJdbcTemplate().query(sql, ps, rs -> {
            Map<String, Integer> result = new HashMap<>();
            while (rs.next()) {
                result.put(rs.getString("target_key"), rs.getInt("cnt"));
            }
            return result;
        });
    }

    private EntityRef mapRow(ResultSet rs, int rowNum) throws SQLException {
        EntityRef ref = new EntityRef();
        ref.setSourceKey(rs.getString("source_key"));
        ref.setSourceType(rs.getString("source_type"));
        ref.setTargetKey(rs.getString("target_key"));
        ref.setTargetType(rs.getString("target_type"));
        return ref;
    }
}
