package io.terminus.trantor2.meta.index;

import io.terminus.trantor2.meta.api.model.MetaNodeAccessLevel;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.object.Oid;
import lombok.Data;

/**
 * 资源主索引基本信息
 *
 * <AUTHOR>
 */
@Data
class EntityBase implements MetaIndexAsset.Base {

    /**
     * Unique key of the asset in the branch
     */
    private String key;

    /**
     * Type of the asset
     */
    private MetaType type;

    /**
     * Subtype of the asset
     * <p>some asset may have subType, like Model, ServiceDefinition, etc.
     */
    private String subType;

    /**
     * Module key of the asset
     * <p>parsed from key, which means all asset key has a module key as prefix (except module's key itself).
     * <p>because moduleKey is parsed, it's not included in oid hash.
     */
    private String moduleKey;

    /**
     * Name of the asset, used for display
     * <p>no need to be unique
     */
    private String name;

    /**
     * Path of the asset
     * <p>'/' separated path, like 'a/b/c'
     */
    private String path;

    /**
     * @deprecated use path instead
     */
    @Deprecated
    private String parentKey;

    /**
     * Description of the asset
     */
    private String description;

    /**
     * Access level of the asset
     */
    private MetaNodeAccessLevel access;

    /**
     * SHA256 HASH of the asset (base part)
     * <p>include props, key, type, subType, name, path, description, access.
     */
    private Oid oid;

    /**
     * SHA256 HASH of the asset (ext part)
     */
    private Oid extOid;
}
