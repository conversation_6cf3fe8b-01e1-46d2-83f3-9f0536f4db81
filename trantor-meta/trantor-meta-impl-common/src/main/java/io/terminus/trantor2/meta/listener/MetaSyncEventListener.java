package io.terminus.trantor2.meta.listener;

import io.terminus.trantor2.meta.event.MetaSyncEvent;
import io.terminus.trantor2.meta.event.MetaSyncEventHandler;
import io.terminus.trantor2.meta.util.TopicUtil;
import org.redisson.Redisson;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 元数据同步事件监听器注册
 */
@ConditionalOnClass(Redisson.class)
@Component
public class MetaSyncEventListener {
    private final RTopic topic;

    public MetaSyncEventListener(RedissonClient redissonClient, List<MetaSyncEventHandler> handlers) {
        this.topic = redissonClient.getTopic(TopicUtil.META_SYNC_TOPIC);

        if (handlers == null) {
            return;
        }
        handlers.forEach(handler -> this.topic.addListener(MetaSyncEvent.class, handler));
    }
}
