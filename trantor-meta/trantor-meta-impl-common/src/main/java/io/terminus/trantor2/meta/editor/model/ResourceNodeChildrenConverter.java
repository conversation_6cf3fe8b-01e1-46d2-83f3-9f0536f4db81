package io.terminus.trantor2.meta.editor.model;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import io.terminus.trantor2.meta.objects.tree.ResourceNodeIdentity;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import lombok.extern.slf4j.Slf4j;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Converter
public class ResourceNodeChildrenConverter implements AttributeConverter<List<ResourceNodeIdentity>, String> {

    @Override
    public String convertToDatabaseColumn(List<ResourceNodeIdentity> attribute) {
        if (attribute == null) {
            return null;
        }
        try {
            return ObjectJsonUtil.MAPPER.writeValueAsString(attribute);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<ResourceNodeIdentity> convertToEntityAttribute(String s) {
        if (s == null || s.isEmpty()) {
            return new ArrayList<>();
        }
        try {
            return ObjectJsonUtil.MAPPER.readValue(s,
                new TypeReference<List<ResourceNodeIdentity>>() {
                }
            );
        } catch (JsonProcessingException e) {
            log.error("convert ObjectNode from string failed", e);
            throw new RuntimeException(e);
        }
    }
}
