package io.terminus.trantor2.meta.index;

import lombok.Data;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public final class RefCond {

    private final Set<String> sourceKeys;
    private final Set<String> sourceTypes;
    private final Set<String> targetKeys;
    private final Set<String> targetTypes;

    private RefCond(Builder builder) {
        this.sourceKeys = builder.sourceKeys;
        this.sourceTypes = builder.sourceTypes;
        this.targetKeys = builder.targetKeys;
        this.targetTypes = builder.targetTypes;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static void check(RefCond refCond) {
        if (refCond == null) {
            throw new IllegalArgumentException("refCond cannot be null");
        }
        if (refCond.sourceKeys.isEmpty() && refCond.targetKeys.isEmpty()) {
            throw new IllegalArgumentException("sourceKeys and targetKeys cannot be empty at the same time");
        }
    }

    // builder
    public static class Builder {
        private final Set<String> sourceKeys = new HashSet<>();
        private final Set<String> sourceTypes = new HashSet<>();
        private final Set<String> targetKeys = new HashSet<>();
        private final Set<String> targetTypes = new HashSet<>();

        private Builder() {
        }

        public Builder sourceKey(String sourceKey) {
            if (sourceKey != null) {
                this.sourceKeys.add(sourceKey);
            }
            return this;
        }

        public Builder sourceKey(Collection<String> sourceKeys) {
            if (sourceKeys != null) {
                this.sourceKeys.addAll(sourceKeys);
            }
            return this;
        }

        public Builder sourceType(String sourceType) {
            if (sourceType != null) {
                this.sourceTypes.add(sourceType);
            }
            return this;
        }

        public Builder sourceType(Collection<String> sourceTypes) {
            if (sourceTypes != null) {
                this.sourceTypes.addAll(sourceTypes);
            }
            return this;
        }

        public Builder targetKey(String targetKey) {
            if (targetKey != null) {
                this.targetKeys.add(targetKey);
            }
            return this;
        }

        public Builder targetKey(Collection<String> targetKeys) {
            if (targetKeys != null) {
                this.targetKeys.addAll(targetKeys);
            }
            return this;
        }

        /**
         * @deprecated
         * targetType may have multiple values, but only the first one will be used,
         * so query by targetType is not recommended.
         */
        @Deprecated
        public Builder targetType(String targetType) {
            if (targetType != null) {
                this.targetTypes.add(targetType);
            }
            return this;
        }

        /**
         * @deprecated
         * targetType may have multiple values, but only the first one will be used,
         * so query by targetType is not recommended.
         */
        @Deprecated
        public Builder targetType(Collection<String> targetTypes) {
            if (targetTypes != null) {
                this.targetTypes.addAll(targetTypes);
            }
            return this;
        }

        public RefCond build() {
            return new RefCond(this);
        }
    }
}
