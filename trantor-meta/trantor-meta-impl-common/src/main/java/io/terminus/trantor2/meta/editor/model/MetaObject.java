package io.terminus.trantor2.meta.editor.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.meta.api.model.MetaNodeAccessLevel;
import io.terminus.trantor2.meta.objects.AbstractObject;
import io.terminus.trantor2.meta.objects.MetaNode;
import io.terminus.trantor2.meta.objects.tree.TreeNodeIdentity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class MetaObject extends AbstractObject<ObjectNode> implements MetaNode {
    private String key;
    private String name;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String path;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String description;
    @Deprecated
    private List<TreeNodeIdentity> children;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private MetaNodeAccessLevel access;

    @Deprecated
    private String parentKey;

    @JsonIgnore
    @Override
    public String getPlatformVersion() {
        if (getPlatformVersionLegacy() != null) {
            return getPlatformVersionLegacy().getNumber();
        }
        return null;
    }
}
