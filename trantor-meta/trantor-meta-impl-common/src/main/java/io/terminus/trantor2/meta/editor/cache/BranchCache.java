package io.terminus.trantor2.meta.editor.cache;

import io.terminus.trantor2.common.storage.jpa.BaseMetaEntity;
import io.terminus.trantor2.meta.editor.model.MetaBranchEntity;
import io.terminus.trantor2.meta.editor.repository.BranchRepository;

import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

public class BranchCache {
    private final BranchRepository branchRepo;
    private final ConcurrentHashMap<Long, Long> cache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Long> code2BranchMap = new ConcurrentHashMap<>();

    public BranchCache(BranchRepository branchRepo) {
        this.branchRepo = branchRepo;
    }

    public Long exchangeCurrentBranchIdWithTeamId(Long teamId) {
        return exchangeMainBranchIdByTeamId(teamId);
    }

    public void clear() {
        cache.clear();
        code2BranchMap.clear();
    }

    public Long exchangeMainBranchIdByTeamCode(String teamCode) {
        if (teamCode == null) {
            return null;
        }
        Long branchId = code2BranchMap.get(teamCode);
        if (branchId != null) {
            return branchId;
        }
        Optional<Long> found = branchRepo.findByTeamCodeAndName(teamCode, MetaBranchEntity.DEFAULT_BRANCH_NAME)
                .map(BaseMetaEntity::getId);
        if (found.isPresent()) {
            code2BranchMap.put(teamCode, found.get());
            return found.get();
        }
        return null;
    }

    public Long exchangeMainBranchIdByTeamId(Long teamId) {
        if (teamId == null) {
            return null;
        }
        Long branchId = cache.get(teamId);
        if (branchId != null) {
            return branchId;
        }
        Optional<Long> found = branchRepo.findByTeamIdAndName(teamId, MetaBranchEntity.DEFAULT_BRANCH_NAME)
                .map(BaseMetaEntity::getId);
        if (found.isPresent()) {
            cache.put(teamId, found.get());
            return found.get();
        }
        return null;
    }

    public Long exchangeTeamIdByBranchId(Long branchId) {
        if (branchId == null) {
            return null;
        }
        Optional<Long> found = branchRepo.findById(branchId)
                .map(MetaBranchEntity::getTeamId);
        return found.orElse(null);
    }
}
