package io.terminus.trantor2.meta.querytree;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 */
public final class DataRetriever<K, V> {
    private final DataRetrieveBackend<K, V> backend;
    private final Map<K, V> loaded = new HashMap<>();
    private final Set<K> loading = new HashSet<>();
    private final ReentrantLock lock = new ReentrantLock();

    public DataRetriever(DataRetrieveBackend<K, V> backend) {
        this.backend = backend;
    }

    public V get(K key) {
        lock.lock();
        try {
            if (loaded.containsKey(key)) {
                return loaded.get(key);
            }
            loading.add(key);
            return null;
        } finally {
            lock.unlock();
        }
    }

    public void load() {
        lock.lock();
        try {
            if (!loading.isEmpty()) {
                Map<K, V> all = backend.getAll(loading);
                for (K k : loading) {
                    loaded.put(k, all.get(k));
                }
                loading.clear();
            }
        } finally {
            lock.unlock();
        }
    }

    public boolean isAllLoaded() {
        lock.lock();
        try {
            return loading.isEmpty();
        } finally {
            lock.unlock();
        }
    }

    public void clear() {
        lock.lock();
        try {
            loaded.clear();
            loading.clear();
        } finally {
            lock.unlock();
        }
    }
}
