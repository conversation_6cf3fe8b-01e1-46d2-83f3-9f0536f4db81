package io.terminus.trantor2.meta.blob;

import io.terminus.trantor2.meta.object.Oid;

import java.util.Date;

/**
 * <AUTHOR>
 */
public interface MetaBlob {
    interface Key extends MetaBlob {
        String getKey();
    }
    interface Base extends Key {
        String getModuleKey();
        String getType();
        String getParentKey();
        String getPath();
        Oid getOid();
    }
    interface Lite extends Base {
        Long getCreatedBy();
        Long getUpdatedBy();
        Date getCreatedAt();
        Date getUpdatedAt();
    }
    interface Full extends Lite {
        byte[] getObject();
    }
}
