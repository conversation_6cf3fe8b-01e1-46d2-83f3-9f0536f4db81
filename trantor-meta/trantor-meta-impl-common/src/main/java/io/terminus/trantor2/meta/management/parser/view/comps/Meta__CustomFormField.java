package io.terminus.trantor2.meta.management.parser.view.comps;

import com.google.auto.service.AutoService;
import io.terminus.trantor2.meta.management.parser.ParseContext;
import io.terminus.trantor2.meta.management.parser.view.Comp;
import io.terminus.trantor2.meta.management.parser.Parseable;
import io.terminus.trantor2.meta.management.parser.view.parts.ColSizeConfig;
import lombok.Data;

/**
 * <AUTHOR>
 */
@AutoService(Comp.class)
public class Meta__CustomFormField extends Comp<Meta__CustomFormField.Props> {
    @Data
    public static final class Props implements Parseable {
        private ColSizeConfig colSizeConfig;

        @Override
        public void parse(ParseContext ctx) {
            ctx.parseObjectField("colSizeConfig", true, ColSizeConfig.class).ifPresent(it -> {
                this.colSizeConfig = it;
            });
        }
    }

}
