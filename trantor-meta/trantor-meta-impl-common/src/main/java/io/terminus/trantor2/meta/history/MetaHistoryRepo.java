package io.terminus.trantor2.meta.history;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface MetaHistoryRepo {
    /**
     * Create a new meta history record
     *
     * @param teamId  team id
     * @param history meta history to create
     */
    void create(Long teamId, MetaHistory history);

    /**
     * Create multiple meta history records
     *
     * @param teamId    team id
     * @param histories meta histories to create
     */
    void create(Long teamId, List<MetaHistory> histories);

    /**
     * Find one meta history by key
     *
     * @param teamId team id
     * @param key    key of meta history
     * @return optional of the result
     */
    Optional<MetaHistory> findLastVersion(Long teamId, String key);

    /**
     * Find all meta history records by key
     *
     * @param teamId team id
     * @param key    key of meta history
     * @param limit  maximum number of records to return
     * @return list of meta history records ordered by created_at desc
     */
    List<MetaHistory> findHistoryByKey(Long teamId, String key, int limit);

    /**
     * Find meta history by id
     *
     * @param teamId team id
     * @param id     history id
     * @return optional of the result
     */
    Optional<MetaHistory> findById(Long teamId, Long id);
}
