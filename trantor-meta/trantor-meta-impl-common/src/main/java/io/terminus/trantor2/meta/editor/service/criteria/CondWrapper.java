package io.terminus.trantor2.meta.editor.service.criteria;

import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.editor.controller.jsonquerydsl.Condition;

public abstract class CondWrapper {

    protected final Cond cond;

    protected CondWrapper(Cond cond) {
        this.cond = cond;
    }

    public abstract Condition toCondition();

    public static CondWrapper wrap(Cond cond) {
        switch (cond.getFunc()) {
            case AND:
            case OR:
            case NOT:
                return new ChainCondWrapper(cond);
            default:
                return new FieldCondWrapper(cond);
        }
    }
}
