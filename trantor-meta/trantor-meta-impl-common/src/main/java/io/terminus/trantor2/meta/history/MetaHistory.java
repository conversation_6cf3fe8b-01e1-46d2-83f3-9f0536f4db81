package io.terminus.trantor2.meta.history;

import io.terminus.trantor2.meta.api.dto.MetaChangeOperation;
import io.terminus.trantor2.meta.object.Oid;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class MetaHistory {
    private Long id;
    private String key;
    private Oid lastOid;
    private Oid currentOid;
    private MetaChangeOperation changeOperation;
    private Long relatedTaskId;
    private Long createdBy;
    private Date createdAt;

    public static MetaHistory of(
            String key, Oid lastOid, Oid currentOid,
            MetaChangeOperation changeOperation, Long relatedTaskId,
            Long createdBy
    ) {
        MetaHistory history = new MetaHistory();
        history.setKey(key);
        history.setLastOid(lastOid);
        history.setCurrentOid(currentOid);
        history.setChangeOperation(changeOperation);
        history.setRelatedTaskId(relatedTaskId);
        history.setCreatedBy(createdBy);
        // createdAt is set in the repository
        return history;
    }
}
