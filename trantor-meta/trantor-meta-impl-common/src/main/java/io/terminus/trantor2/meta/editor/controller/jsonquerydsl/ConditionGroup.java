package io.terminus.trantor2.meta.editor.controller.jsonquerydsl;

import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import lombok.Data;

import java.util.List;

/**
 * TODO: just copy this class, need to unify the class in the future
 */
@Data
public class ConditionGroup extends Condition {

    private List<Condition> conditions;

    private LogicOperator logicOperator;

    @Override
    public Cond toCond() {
        switch (logicOperator) {
            case AND:
                return Cond.and(conditions.stream().map(Condition::toCond).toArray(Cond[]::new));
            case OR:
                return Cond.or(conditions.stream().map(Condition::toCond).toArray(Cond[]::new));
            default:
                throw new RuntimeException("unsupported logic operator: " + logicOperator);
        }
    }
}
