package io.terminus.trantor2.meta.management.parser;

import io.terminus.trantor2.meta.management.parser.refs.Ref;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Set;

@Getter
@RequiredArgsConstructor
public final class Result {
    private final Parseable item;
    private final Set<Ref> refs;
    private final List<ParseContext.Error> errors;
}
