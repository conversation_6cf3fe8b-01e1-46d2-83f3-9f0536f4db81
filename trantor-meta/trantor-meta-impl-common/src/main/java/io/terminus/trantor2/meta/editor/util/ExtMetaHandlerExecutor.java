package io.terminus.trantor2.meta.editor.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.editor.ext.handler.ExtMetaActionHandler;
import io.terminus.trantor2.meta.resource.ext.*;
import io.terminus.trantor2.meta.resource.ext.patch.ExtPropsMergePatch;
import io.terminus.trantor2.meta.resource.ext.patch.ExtPropsSplitPatch;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.util.*;
import java.util.stream.Collectors;

import static io.terminus.trantor2.meta.editor.ext.handler.ExtMetaActionHandler.EXT_STEPS;

/**
 * <AUTHOR>
 */
public class ExtMetaHandlerExecutor {
    private static final List<ExtMetaActionHandler> handlers = new ArrayList<>(4);

    static {
        ServiceLoader<ExtMetaActionHandler> handlerLoader = ServiceLoader.load(ExtMetaActionHandler.class);
        for (ExtMetaActionHandler handler : handlerLoader) {
            handlers.add(handler);
        }
        handlers.sort(Comparator.comparingInt(ExtMetaActionHandler::getOrder));
    }

    /**
     * 合并节点属性
     *
     * @param node              一开节点
     * @param extProps          扩展属性
     * @param extParams 扩展信息
     * @param inConsoleDesigner         是否为研发态，研发态（设计器）和运行态合并结果可能不同
     */
    public static void doMerge(MetaTreeNodeExt node, ExtMeta.Props extProps, @Nonnull ExtParams extParams, boolean inConsoleDesigner) {
        if (ObjectUtils.isEmpty(extProps) || node.getCustomExt()) {
            return;
        }
        List<ExtMeta.Snippet> snippets = extProps.getSnippets();
        if (CollectionUtils.isEmpty(snippets)) {
            return;
        }
        node.setExtended(true);
        Map<ExtMeta.ActionType, List<ExtMeta.Snippet>> snippetsMap =
            snippets.stream().collect(Collectors.groupingBy(ExtMeta.Snippet::getAction));

        JsonNode jsonNode = !ObjectUtils.isEmpty(extParams.getPropsExtFieldName())
                ? node.getProps().get(extParams.getPropsExtFieldName()) : node.getProps();
        applyMergePatches(false, node, extParams, snippets);
        for (ExtMetaActionHandler handler : handlers) {
            ExtValidScope scope = inConsoleDesigner ? ExtValidScope.CONSOLE : ExtValidScope.RUNTIME;
            if (!snippetsMap.containsKey(handler.type())) {
                continue;
            }
            List<ExtMeta.Snippet> snippetsList = snippetsMap.get(handler.type()).stream().filter(snippet -> {
                ExtValidScope validScope = snippet.getValidScope();
                String rootField = snippet.getRootField();
                return (validScope.equals(ExtValidScope.ALL) || scope.equals(validScope)) &&
                        (rootField == null || rootField.equals(extParams.getPropsExtFieldName()));
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(snippetsList)) {
                continue;
            }
            handler.merge(node.getKey(), jsonNode, snippetsList);
        }
        applyMergePatches(true, node, extParams, snippets);
    }

    /**
     * 拆分扩展属性，将扩展属性拆分到 extMeta 中
     */
    @Nullable
    public static ExtMeta doSplit(@Nullable MetaTreeNodeExt node, @Nonnull ExtParams extParams) {
        if (node == null) {
            return null;
        }
        JsonNode jsonNode = !ObjectUtils.isEmpty(extParams.getPropsExtFieldName()) ?
                node.getProps().get(extParams.getPropsExtFieldName()) : node.getProps();
        if (jsonNode == null || jsonNode.isNull()) {
            return null;
        }
        ExtMeta.Props extProps = new ExtMeta.Props();
        extProps.setOriginType(MetaType.valueOf(node.getType()));

        applySplitPatches(false, node, extParams, extProps);
        splitProps(extProps, jsonNode, node.getKey(), extParams.getValidator());
        if (!ObjectUtils.isEmpty(extParams.getPropsExtFieldName())) {
            extProps.getSnippets().forEach(s -> s.setRootField(extParams.getPropsExtFieldName()));
        }
        applySplitPatches(true, node, extParams, extProps);
        if (CollectionUtils.isEmpty(extProps.getSnippets()) && !MetaType.Module.name().equals(node.getType())) {
            return null;
        }
        ExtMeta extMeta = new ExtMeta();
        extMeta.setKey(KeyUtil.extKey(node.getKey()));
        extMeta.setName(extMeta.getKey());
        extMeta.setTeamCode(node.getTeamCode());
        extMeta.setTeamId(node.getTeamId());
        extMeta.setParentKey(KeyUtil.extKey(KeyUtil.moduleKey(node.getKey())));
        extMeta.setResourceProps(extProps);
        return extMeta;
    }

    private static void applyMergePatches(boolean postPatch, MetaTreeNodeExt node,
                                          @NotNull ExtParams extParams, List<ExtMeta.Snippet> snippets) {
        if (CollectionUtils.isEmpty(extParams.getPatches())) {
            return;
        }
        List<ExtPropsMergePatch> patches = extParams.getPatches().stream()
                .filter(patch -> (patch.postPatch() == postPatch) && patch instanceof ExtPropsMergePatch)
                .map(patch -> (ExtPropsMergePatch) patch)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(patches)) {
            patches.forEach(patch -> {
                List<ExtMeta.Snippet> patchSnippets = snippets;
                if (patch.getRootField() != null) {
                    patchSnippets = snippets.stream().filter(snippet -> patch.getRootField().equals(snippet.getRootField()))
                            .collect(Collectors.toList());
                }
                patch.applySnippets(node.getKey(), node.getProps(), patchSnippets);
            });
        }
    }

    private static void applySplitPatches(boolean postPatch, @NotNull MetaTreeNodeExt node,
                                          @NotNull ExtParams extParams, ExtMeta.Props extProps) {
        if (CollectionUtils.isEmpty(extParams.getPatches())) {
            return;
        }
        List<ExtPropsSplitPatch> patches = extParams.getPatches().stream()
                .filter(patch -> postPatch == patch.postPatch() && patch instanceof ExtPropsSplitPatch)
                .map(patch -> (ExtPropsSplitPatch) patch)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(patches)) {
            patches.forEach(patch -> {
                if (extParams.getOriginalProps() == null) {
                    return;
                }
                List<ExtMeta.Snippet> snippets = patch.fetchSnippets(node.getKey(), node.getProps(), extParams.getOriginalProps(), extProps);
                if (!CollectionUtils.isEmpty(snippets)) {
                    if (patch.getRootField() != null) {
                        snippets.forEach(it -> it.setRootField(patch.getRootField()));
                    }
                    extProps.getSnippets().addAll(snippets);
                }
            });
        }
    }

    private static void splitProps(@Nonnull ExtMeta.Props props, @NotNull JsonNode jsonNode,
                                   @Nullable String parentKey, @Nullable ExtPropsSplitValidator splitValidator) {
        LinkedHashMap<ExtMeta.ActionType, List<ExtStep>> extStepsMap = findExtStepsMap(jsonNode);
        for (ExtMetaActionHandler handler : handlers) {
            Collection<ExtMeta.Snippet> snippets = handler.split(jsonNode, parentKey, extStepsMap.get(handler.type()));
            if (!ObjectUtils.isEmpty(splitValidator)) {
                snippets.forEach(snippet -> splitValidator.splitValidate(jsonNode, snippet));
            }
            props.getSnippets().addAll(snippets);
        }
    }

    private static LinkedHashMap<ExtMeta.ActionType, List<ExtStep>> findExtStepsMap(JsonNode node) {
        if (node == null || node.isNull()) {
            return new LinkedHashMap<>();
        }
        return findExtSteps(node).stream()
                .collect(Collectors.groupingBy(ExtStep::getAction, LinkedHashMap::new, Collectors.toList()));
    }

    private static List<ExtStep> findExtSteps(JsonNode node) {
        List<ExtStep> res = new ArrayList<>();
        if (node.has(EXT_STEPS)) {
            JsonNode extStepsArrayNode = node.get(EXT_STEPS);
            if (extStepsArrayNode.isArray()) {
                List<ExtStep> extSteps = ObjectJsonUtil.MAPPER.convertValue(extStepsArrayNode, new TypeReference<List<ExtStep>>() {
                });
                res.addAll(extSteps);
            }
        }
        if (node.isArray()) {
            for (JsonNode childNode : node) {
                res.addAll(findExtSteps(childNode));
            }
        } else if (node.isObject()) {
            for (JsonNode childNode : node) {
                res.addAll(findExtSteps(childNode));
            }
        }
        return res;
    }
}
