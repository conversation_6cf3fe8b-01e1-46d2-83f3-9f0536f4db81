package io.terminus.trantor2.meta.management.parser.view.comps;

import com.google.auto.service.AutoService;
import io.terminus.trantor2.meta.management.parser.ParseContext;
import io.terminus.trantor2.meta.management.parser.view.Comp;
import io.terminus.trantor2.meta.management.parser.Parseable;
import io.terminus.trantor2.meta.management.parser.view.parts.InvokeService;
import io.terminus.trantor2.meta.management.parser.refs.ModelRef;
import lombok.Data;

/**
 * <AUTHOR>
 */
@AutoService(Comp.class)
public class Container__Detail extends Comp<Container__Detail.Props> {
    @Data
    public static final class Props implements Parseable {
        private ModelRef model;
        private InvokeService flow;

        @Override
        public void parse(ParseContext ctx) {
            ctx.stringField("modelAlias", true).ifPresent(modelAlias -> {
                model = new ModelRef(modelAlias, ctx);
            });
            ctx.parseObjectField("flow", true, InvokeService.class).ifPresent(flow -> {
                this.flow = flow;
            });
        }
    }
}
