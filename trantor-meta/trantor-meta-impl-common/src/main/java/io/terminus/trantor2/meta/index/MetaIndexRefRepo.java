package io.terminus.trantor2.meta.index;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface MetaIndexRefRepo {

    void create(Long teamId, List<EntityRef> refs);

    void deleteBySourceKeys(Long teamId, Collection<String> sourceKeys);

    void delete(Long teamId, Collection<MetaIndexRef> refs);

    List<String> findAllKeys(Long teamId);

    List<MetaIndexRef> find(Long teamId, RefCond refCond);

    Map<String, Integer> countByTargetKeys(Long teamId);
}
