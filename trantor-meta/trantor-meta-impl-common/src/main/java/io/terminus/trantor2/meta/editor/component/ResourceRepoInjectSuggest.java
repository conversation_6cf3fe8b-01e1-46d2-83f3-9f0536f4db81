package io.terminus.trantor2.meta.editor.component;

import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.repository.ResourceRepository;
import io.terminus.trantor2.meta.api.service.ExtMetaService;
import io.terminus.trantor2.meta.api.service.MetaEditService;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.blob.MetaBlobRepo;
import io.terminus.trantor2.meta.editor.aop.ResourceRepositoryAspect;
import io.terminus.trantor2.meta.editor.repository.SimpleResourceRepository;
import io.terminus.trantor2.meta.resource.BaseMeta;
import io.terminus.trantor2.meta.resource.ResourceBaseMeta;
import io.terminus.trantor2.module.service.TeamService;
import lombok.AllArgsConstructor;
import org.springframework.aop.framework.ProxyFactory;
import org.springframework.aop.support.DefaultPointcutAdvisor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor;
import org.springframework.data.util.ReflectionUtils;
import org.springframework.util.ClassUtils;

import java.lang.reflect.Constructor;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2023/7/7 5:19 PM
 */
@AllArgsConstructor
public class ResourceRepoInjectSuggest implements InjectSuggest {
    private final MetaQueryService queryService;
    private final MetaEditService editService;
    private final ExtMetaService extMetaService;
    private final ResourceRepositoryAspect repositoryAspect;
    private final MetaBlobRepo metaBlobRepo;
    private final TeamService teamService;

    @Override
    public boolean support(Class<?> type) {
        if (!type.isInterface()) {
            return false;
        }
        Class<?>[] interfaces = type.getInterfaces();
        for (Class<?> clazz : interfaces) {
            if (clazz == ResourceRepository.class) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Object getValue(Class<?> type) {
        ProxyFactory proxyFactory = new ProxyFactory();

        Class<? extends ResourceBaseMeta<?>> resourceType = getResourceType(type);
        MetaType metaType = BaseMeta.newInstance(resourceType).getMetaType();
        if (ResourceRegistry.getRepository(metaType).isPresent()) {
            return ResourceRegistry.getRepository(metaType).get();
        }
        Object target = instantiateTarget(resourceType, queryService, editService, extMetaService, metaBlobRepo, teamService);
        proxyFactory.setTarget(target);
        proxyFactory.setInterfaces(type, ResourceRepository.class);

        DefaultPointcutAdvisor ad = new DefaultPointcutAdvisor();
        ad.setAdvice(new DefaultMethodInvokingMethodInterceptor());

        DefaultPointcutAdvisor custom = new DefaultPointcutAdvisor();
        custom.setAdvice(repositoryAspect);

        proxyFactory.addAdvisor(ad);
        proxyFactory.addAdvisor(custom);

        Object proxy = proxyFactory.getProxy();
        ResourceRegistry.registerMetaType(metaType, resourceType, proxy);
        return proxy;
    }

    private Class<? extends ResourceBaseMeta<?>> getResourceType(Class<?> type) {
        for (Type clazz : type.getGenericInterfaces()) {
            ParameterizedType pt = (ParameterizedType) clazz;
            if (pt.getRawType() == ResourceRepository.class) {
                return (Class<? extends ResourceBaseMeta<?>>) pt.getActualTypeArguments()[0];
            }
        }
        throw new TrantorRuntimeException(type + " definition is invalid");
    }

    private Object instantiateTarget(Object... constructorArguments) {
        Optional<Constructor<?>> constructor = ReflectionUtils.findConstructor(SimpleResourceRepository.class, constructorArguments);

        return constructor.map(it -> BeanUtils.instantiateClass(it, constructorArguments))
            .orElseThrow(() -> new IllegalStateException(String.format(
                "No suitable constructor found on %s to match the given arguments: %s. Make sure you implement a constructor taking these",
                SimpleResourceRepository.class, Arrays.stream(constructorArguments).map(Object::getClass).map(ClassUtils::getQualifiedName)
                    .collect(Collectors.joining(", ")))));
    }
}
