package io.terminus.trantor2.meta.management.parser;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.ValueNode;
import io.terminus.trantor2.meta.exception.MetaParseException;
import io.terminus.trantor2.meta.management.parser.refs.Ref;
import io.terminus.trantor2.meta.management.parser.refs.RefBase;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class ParseContext {
    private final String path;
    private final JsonNode node;
    private final Class<? extends Parseable> parseClass;
    private final boolean fastFail;
    private final List<Error> errorCollect;
    private final Map<String, Ref> refMap;
    private final Set<String> visited = new HashSet<>();
    private Boolean checkUnknownFields = true;

    public static ParseContext makeRoot(String path, JsonNode node, boolean fastFail) {
        return new ParseContext(path, node, null, fastFail, new ArrayList<>(), new HashMap<>());
    }

    public static ParseContext makeChild(ParseContext parent, Class<? extends Parseable> compClass, String relatedPath, JsonNode node) {
        return new ParseContext(parent.path + relatedPath, node, compClass, parent.fastFail, parent.errorCollect, parent.getRefMap());
    }

    @Getter
    @AllArgsConstructor
    public static class Error {
        private String statement;
        private String path;
        private String detail;

        @Override
        public String toString() {
            return String.format("Error: %s\n  Path: %s\n  Detail: %s\n", statement, path, detail);
        }
    }

    public void checkUnknownFields() {
        if (!checkUnknownFields) {
            return;
        }
        ObjectNode obj = objectNode(false).orElse(null);
        if (obj == null) {
            return;
        }
        for (Iterator<String> it = obj.fieldNames(); it.hasNext(); ) {
            String fieldName = it.next();
            if (!visited.contains(fieldName)) {
                error(String.format("field `%s` is unknown", fieldName));
            }
        }
    }

    private Optional<ObjectNode> objectNode(boolean required) {
        if (node == null || node.isNull()) {
            if (required) {
                error("is null");
            }
            return Optional.empty();
        }
        if (!node.isObject()) {
            error("is not object");
            return Optional.empty();
        }
        return Optional.of((ObjectNode) node);
    }

    public <P extends Parseable> Optional<P> parseThis(boolean required, Class<P> parseClass) {
        try {
            return objectNode(required).flatMap(it -> newInstance(parseClass).map(p -> {
                p.parse(this);
                return ParseProxy.unwrap(p);
            }));
        } catch (Exception e) {
            error("internal error");
            log.error("parse failure", e);
        }
        return Optional.empty();
    }

    public Optional<JsonNode> anyField(String fieldName, boolean required) {
        visited.add(fieldName);
        ObjectNode obj = objectNode(required).orElse(null);
        if (obj == null) {
            return Optional.empty();
        }
        JsonNode field = obj.get(fieldName);
        if (field == null || field.isNull()) {
            if (required) {
                error(String.format("field `%s` is null", fieldName));
            }
            return Optional.empty();
        }
        return Optional.of(field);
    }

    public Optional<ObjectNode> objectField(String fieldName, boolean required) {
        return anyField(fieldName, required).map(it -> {
            if (!it.isObject()) {
                error(String.format("field `%s` is not object", fieldName));
                return null;
            }
            return (ObjectNode) it;
        });
    }

    public <P extends Parseable> Optional<P> parseObjectField(String fieldName, boolean required, Class<P> parseClass) {
        return objectField(fieldName, required).flatMap(it -> newInstance(parseClass).map(p -> {
            ParseContext subCtx = ParseContext.makeChild(this, parseClass, String.format(".%s", fieldName), it);
            p.parse(subCtx);
            return ParseProxy.unwrap(p);
        }));
    }

    public Optional<ArrayNode> arrayField(String fieldName, boolean required) {
        return anyField(fieldName, required).map(it -> {
            if (!it.isArray()) {
                error(String.format("field `%s` is not array", fieldName));
                return null;
            }
            return (ArrayNode) it;
        });
    }

    public <P extends Parseable> Optional<List<P>> parseArrayField(String fieldName, boolean required, Class<P> parseClass) {
        return arrayField(fieldName, required).map(it -> {
            List<P> list = new ArrayList<>();
            for (int i = 0; i < it.size(); i++) {
                final int idx = i;
                newInstance(parseClass).ifPresent(p -> {
                    ParseContext subCtx = ParseContext.makeChild(this, parseClass, String.format(".%s[%d]", fieldName, idx), it.get(idx));
                    p.parse(subCtx);
                    list.add(ParseProxy.unwrap(p));
                });
            }
            return list;
        });
    }

    public Optional<ValueNode> valueField(String fieldName, boolean required) {
        return anyField(fieldName, required).map(it -> {
            if (!it.isValueNode()) {
                error(String.format("field `%s` is not value node", fieldName));
                return null;
            }
            return (ValueNode) it;
        });
    }

    public Optional<String> stringField(String fieldName, boolean required) {
        return valueField(fieldName, required).map(it -> {
            if (!it.isTextual()) {
                error(String.format("field `%s` is not string", fieldName));
                return null;
            }
            return it.asText();
        });
    }

    public Optional<Boolean> booleanField(String fieldName, boolean required) {
        return valueField(fieldName, required).map(it -> {
            if (!it.isBoolean()) {
                error(String.format("field `%s` is not boolean", fieldName));
                return null;
            }
            return it.asBoolean();
        });
    }

    public Optional<Integer> intField(String fieldName, boolean required) {
        return valueField(fieldName, required).map(it -> {
            if (!it.isInt()) {
                error(String.format("field `%s` is not int", fieldName));
                return null;
            }
            return it.asInt();
        });
    }

    public void nullField(String fieldName) {
        anyField(fieldName, false).ifPresent(it -> {
            error(String.format("field `%s` is not null", fieldName));
        });
    }

    public void error(String message) {
        String className = parseClass == null ? "unknown" : getSimpleName(parseClass);
        String statement = String.format("(%s): %s", className, message);
        String example = String.format("node: %s", node == null ? "null" : node.toString());
        Error error = new Error(statement, path, example);
        if (fastFail) {
            throw new MetaParseException(error.toString());
        }
        errorCollect.add(
                error
        );
    }

    /**
     * put ref into refMap, if refMap already contains the ref,
     * return the ref in refMap, otherwise return the cur ref
     */
    @SuppressWarnings("unchecked")
    public <V extends RefBase> V putRefIfAbsent(V ref) {
        return (V) Optional.ofNullable(refMap.putIfAbsent(ref.getRefKey(), ref)).orElse(ref);
    }

    public Set<Ref> getRefsCollect() {
        return new HashSet<>(refMap.values());
    }

    public void setCheckUnknownFields(boolean checkUnknownFields) {
        this.checkUnknownFields = checkUnknownFields;
    }

    private static String getSimpleName(Class<?> clazz) {
        String simpleName = clazz.getSimpleName();
        if (clazz.isMemberClass()) {
            Class<?> enclosingClass = clazz.getEnclosingClass();
            simpleName = enclosingClass.getSimpleName() + "." + simpleName;
        }
        return simpleName;
    }

    private <P extends Parseable> Optional<P> newInstance(Class<P> parseClass) {
        P p = null;
        try {
            p = ParseProxy.makeProxy(parseClass.newInstance());
        } catch (InstantiationException | IllegalAccessException e) {
            error("internal error");
        }
        return Optional.ofNullable(p);
    }
}
