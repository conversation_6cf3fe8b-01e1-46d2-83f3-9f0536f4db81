package io.terminus.trantor2.meta.editor.event.translator;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.meta.context.MetaContext;
import io.terminus.trantor2.meta.event.*;
import io.terminus.trantor2.properties.SpringApplicationProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 **/
@Slf4j
@RequiredArgsConstructor
public class RedisEventTranslator implements EventTranslator {
    private final SpringApplicationProperties applicationProperties;
    private ApplicationEventPublisher publisher;
    private ThreadPoolTaskExecutor taskExecutor;

    @Override
    public void setApplicationEventPublisher(@NotNull ApplicationEventPublisher publisher) {
        this.publisher = publisher;
    }

    @PostConstruct
    private void init() {
        taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(5);
        taskExecutor.setMaxPoolSize(10);
        taskExecutor.setQueueCapacity(100);
        taskExecutor.setThreadNamePrefix("RedisEvent-Async-");
        taskExecutor.initialize();
    }

    public void translate(MetaChangeEvent msg) {
        publishAsync(() -> {
            boolean contextCreated = setUpContextIfAbsent(msg);
            try {
                publisher.publishEvent(msg);
            } finally {
                if (contextCreated) {
                    TrantorContext.clear();
                }
            }
        });
    }

    public void translate(ModuleCreateEvent msg) {
        publishAsync(() -> {
            log.info("module create {}", msg.getModule());
            publisher.publishEvent(msg);
        });
    }

    public void translate(ModuleDeleteEvent msg) {
        publishAsync(() -> {
            if (MetaContext.isCurrentDeployModule(msg.getModule())) {
                log.info("module delete {}", msg.getModule());
                publisher.publishEvent(msg);
            }
        });
    }

    public void translate(DatasourceUpdateEvent msg) {
        publishAsync(() -> {
            log.info("datasource update team id:{}, datasource:{}", msg.getTeamId(), msg.getDatasourceName());
            publisher.publishEvent(msg);
        });
    }

    public void translate(DatasourceDeleteEvent msg) {
        publishAsync(() -> {
            log.info("datasource delete team id:{}, datasource:{}", msg.getTeamId(), msg.getDatasourceName());
            publisher.publishEvent(msg);
        });
    }

    public void translate(DefaultDatasourceSwitchEvent msg) {
        publishAsync(() -> {
            log.info("datasource switch team id:{}, datasource:{}", msg.getTeamId(), msg.getDatasourceName());
            publisher.publishEvent(msg);
        });
    }

    public void translate(ModuleDatasourceUpdateEvent msg) {
        publishAsync(() -> {
            log.info("datasource updated team id:{}, datasource:{}", msg.getTeamId(), msg.getDatasourceName());
            publisher.publishEvent(msg);
        });
    }

    public void translate(ModuleConfigChangedEvent msg) {
        if (applicationProperties.getName().equals(msg.getApplicationName())) {
            publishAsync(() -> {
                log.info("module config changed, append modules {}, remove modules {}", msg.getAppendAll(), msg.getRemoveAll());
                publisher.publishEvent(msg);
            });
        }
    }

    public void translate(ModelShardingConfigUpdateEvent msg) {
        publishAsync(() -> {
            log.info("model sharding config changed team id:{}, model:{}", msg.getTeamId(), msg.getModelKey());
            publisher.publishEvent(msg);
        });
    }

    @PreDestroy
    private void shutDownExecutor() {
        taskExecutor.shutdown();
    }

    private void publishAsync(Runnable eventTask) {
        CompletableFuture.runAsync(eventTask, taskExecutor);
    }

    private boolean setUpContextIfAbsent(MetaChangeEvent msg) {
        if (TrantorContext.getContext() == null) {
            TrantorContext.init();
            TrantorContext.setTeam(msg.getTeamCode());
            User user = new User();
            user.setId(msg.getUserId());
            TrantorContext.setCurrentUser(user);
            return true;
        }
        return false;
    }
}
