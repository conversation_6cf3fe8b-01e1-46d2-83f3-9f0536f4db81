package io.terminus.trantor2.meta.editor.service;

import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.editor.util.BatchUtil;
import io.terminus.trantor2.meta.editor.util.IndexUtil;
import io.terminus.trantor2.meta.index.MetaIndexAsset;
import io.terminus.trantor2.meta.index.MetaIndexAssetRepo;
import io.terminus.trantor2.module.service.TeamService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: yang<PERSON><PERSON><PERSON>
 * @date: 2023/8/16 4:21 PM
 **/
public class ResourceTraversal implements Traversal<ResourceVisitor> {
    private static final int MAX_BULK_SIZE = 1000;

    private final TeamService teamService;
    private final MetaIndexAssetRepo metaIndexAssetRepo;

    public ResourceTraversal(TeamService teamService, MetaIndexAssetRepo metaIndexAssetRepo) {
        this.teamService = teamService;
        this.metaIndexAssetRepo = metaIndexAssetRepo;
    }

    public void breadthFirst(Long teamId, String rootKey, ResourceVisitor visitor) {
        String teamCode = teamService.getTeamCode(teamId);
        List<MetaIndexAsset.Base> all = metaIndexAssetRepo.find(teamId, MetaIndexAsset.Base.class, Cond.all());
        Map<String, List<String>> parentKeyToChildren = all.stream()
                .collect(Collectors.groupingBy(MetaIndexAsset.Base::getParentKey,
                        Collectors.mapping(MetaIndexAsset.Base::getKey, Collectors.toList())));
        // sort by topological order using parentKey
        List<String> ordered = new ArrayList<>();
        ordered.add(rootKey);
        int i = 0;
        while (i < ordered.size()) {
            String current = ordered.get(i);
            List<String> children = parentKeyToChildren.get(current);
            if (children != null) {
                ordered.addAll(children);
            }
            i++;
        }
        BatchUtil.batch(ordered, MAX_BULK_SIZE, (batch) -> {
            Map<String, MetaIndexAsset.Full> result = metaIndexAssetRepo.find(teamId, MetaIndexAsset.Full.class, Field.key().in(batch))
                    .stream().collect(Collectors.toMap(MetaIndexAsset.Full::getKey, x -> x));
            // keep order
            batch.forEach(key -> {
                MetaIndexAsset.Full node = result.get(key);
                if (node != null) {
                    visitor.visit(key, IndexUtil.idx2node(node, teamId, teamCode));
                }
            });
        });
    }
}
