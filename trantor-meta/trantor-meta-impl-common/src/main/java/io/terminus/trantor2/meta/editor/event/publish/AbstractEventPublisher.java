package io.terminus.trantor2.meta.editor.event.publish;

/**
 * <AUTHOR>
 */

import com.google.common.util.concurrent.RateLimiter;
import io.terminus.trantor2.meta.editor.confg.TrantorMetaProperties;
import io.terminus.trantor2.meta.event.Event;
import io.terminus.trantor2.meta.event.EventPublisher;
import io.terminus.trantor2.meta.event.MergeableEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

@Slf4j
@SuppressWarnings({"all"})
public abstract class AbstractEventPublisher implements EventPublisher {
    public static final String TRANTOR2_META_EVENT_TOPIC = "TRANTOR2_META_EVENT_TOPIC";

    private final Boolean mergeEnabled;
    private final RateLimiter rateLimiter;
    private final int bufferThreshold;
    private final Duration bufferTimeout;
    private final Map<String, List<MergeableEvent>> eventBuffer = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private final Map<String, ScheduledFuture<?>> scheduledFutures = new ConcurrentHashMap<>();

    public AbstractEventPublisher(TrantorMetaProperties trantorMetaProperties) {
        TrantorMetaProperties.TrantorMetaEventMergeProperties mergeProps = trantorMetaProperties.getEvent().getMerge();
        this.mergeEnabled = mergeProps.isEnabled();
        this.rateLimiter = RateLimiter.create(mergeProps.getPermitsPerSecond());
        this.bufferThreshold = mergeProps.getBufferThreshold();
        this.bufferTimeout = mergeProps.getBufferTimeout();
    }

    @Override
    public void publish(Event event) {
        if (BooleanUtils.isNotTrue(mergeEnabled) || !event.mergeable()) {
            publishEvent(event);
        } else {
            bufferAndScheduleEventPublish(event);
        }
    }

    protected abstract void publishEvent(Event event);

    private void bufferAndScheduleEventPublish(Event event) {
        if (BooleanUtils.isTrue(mergeEnabled)) {
            if (rateLimiter.tryAcquire()) {
                if (log.isDebugEnabled()) {
                    log.debug("RateLimiter acquisition successful for [{}] message", event.getTag());
                }
                publishEvent(event);
            } else {
                String eventType = event.getClass().getName();
                eventBuffer.compute(eventType, (key, buffer) -> {
                    if (buffer == null) {
                        buffer = new CopyOnWriteArrayList<>();
                    }
                    buffer.add((MergeableEvent<?>) event);
                    return buffer;
                });
                if (eventBuffer.get(eventType).size() < bufferThreshold) {
                    ScheduledFuture<?> existingFuture = scheduledFutures.get(eventType);
                    if (existingFuture == null || existingFuture.isDone()) {
                        if (log.isDebugEnabled()) {
                            log.debug("Scheduling new task for event type [{}]", eventType);
                        }
                        ScheduledFuture<?> future = scheduler.schedule(() -> mergeBufferAndPublish(eventType), bufferTimeout.toMillis(), TimeUnit.MILLISECONDS);
                        scheduledFutures.put(eventType, future);
                    } else if (log.isDebugEnabled()) {
                        log.debug("Existing task for event type [{}] is already scheduled", eventType);
                    }
                } else {
                    if (log.isDebugEnabled()) {
                        log.debug("Merging and publishing events immediately for event type [{}]", eventType);
                    }
                    mergeBufferAndPublish(eventType);
                }
            }
        }
    }

    private void mergeBufferAndPublish(String eventType) {
        try {
            scheduledFutures.remove(eventType);
            List<MergeableEvent> events = eventBuffer.get(eventType);
            if (CollectionUtils.isEmpty(events)) {
                return;
            } else if (log.isDebugEnabled()) {
                log.debug("before merge: {}", events);
            }
            List<MergeableEvent> processedEvents = processEvents(events);
            if (CollectionUtils.isEmpty(processedEvents)) {
                return;
            } else if (log.isDebugEnabled()) {
                log.debug("merged [{}] and published [{}] events for event type [{}]", events.size(), processedEvents.size(), eventType);
                log.debug("after merge: {}", processedEvents);
            }
            for (MergeableEvent<?> processedEvent : processedEvents) {
                if (log.isDebugEnabled()) {
                    log.debug("Publishing [{}] message [{}] to topic", processedEvent.getTag(), processedEvent);
                } else {
                    log.info("Publishing merged [{}] message to topic", eventType);
                }
                publishEvent(processedEvent);
            }
            eventBuffer.remove(eventType);
        } catch (Exception e) {
            log.error("Error occurred while merging and publishing events for type [{}]: {}", eventType, e.getMessage(), e);
        }
    }

    private List<MergeableEvent> processEvents(List<MergeableEvent> events) {
        if (CollectionUtils.isEmpty(events) || events.size() < 2) {
            return events;
        }
        List<MergeableEvent> mergeRes = events.get(0).merge(new ArrayList<>(Collections.singleton(events.get(1))));
        if (events.size() == 2) {
            return mergeRes;
        }
        for (int i = 2; i < events.size(); i++) {
            mergeRes = events.get(i).merge(mergeRes);
        }
        return mergeRes;
    }
}
