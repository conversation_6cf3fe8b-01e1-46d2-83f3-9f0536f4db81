package io.terminus.trantor2.meta.editor.model;

import com.vladmihalcea.hibernate.type.json.JsonStringType;
import io.terminus.trantor2.meta.api.dto.ResourceNodeLite;
import io.terminus.trantor2.meta.api.model.MetaNodeAccessLevel;
import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import org.hibernate.annotations.Type;

@Deprecated
@Data
@Entity
@Table(name = "meta_tree_node_index",
    indexes = {
        @Index(name = "uk_branchId_key", columnList = "branchId,`key`", unique = true),
        @Index(name = "idx_branchId_type", columnList = "branchId,`type`"),
    })
public class MetaTreeNodeIndexEntity extends LockableBaseAppMetaEntity implements ResourceNodeLite {
    private static final long serialVersionUID = -4682249696548737260L;
    private Long branchId;


    @Column(length = 64)
    private String oid;

    @Column(length = 64)
    private String teamCode;

    @Column(name = "`type`", nullable = false)
    private String type;

    @Column(name = "`key`", nullable = false)
    private String key;

    @Column(name = "`name`", nullable = false)
    private String name;

    @Column(name = "`path`")
    private String path;

    @Column(name = "`description`", length = 2000)
    private String description;

    @Type(JsonStringType.class)
    @Column(name = "`props`", columnDefinition = "json")
    private String props;

    @Column(name = "parent_key", nullable = false)
    private String parentKey;

    @Column(name = "access")
    private MetaNodeAccessLevel access;

    @Deprecated
    private Long posLeft;

    @Deprecated
    private Long posRight;
}
