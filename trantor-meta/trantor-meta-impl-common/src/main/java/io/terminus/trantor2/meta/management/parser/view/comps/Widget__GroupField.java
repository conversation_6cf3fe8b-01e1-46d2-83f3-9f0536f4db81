package io.terminus.trantor2.meta.management.parser.view.comps;

import com.google.auto.service.AutoService;
import io.terminus.trantor2.meta.management.parser.ParseContext;
import io.terminus.trantor2.meta.management.parser.view.Comp;
import io.terminus.trantor2.meta.management.parser.Parseable;
import lombok.Data;

/**
 * <AUTHOR>
 */
@AutoService(Comp.class)
public class Widget__GroupField extends Comp<Widget__GroupField.Props> {
    @Data
    public static final class Props implements Parseable {
        @Override
        public void parse(ParseContext ctx) {
        }
    }
}
