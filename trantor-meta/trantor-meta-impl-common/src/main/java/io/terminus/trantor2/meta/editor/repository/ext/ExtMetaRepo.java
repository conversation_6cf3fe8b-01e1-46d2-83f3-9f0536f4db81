package io.terminus.trantor2.meta.editor.repository.ext;

import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.MoveTarget;
import io.terminus.trantor2.meta.api.dto.MoveTargetType;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.service.MetaEditService;
import io.terminus.trantor2.meta.blob.MetaBlob;
import io.terminus.trantor2.meta.blob.MetaBlobRepo;
import io.terminus.trantor2.meta.editor.util.IndexUtil;
import io.terminus.trantor2.meta.resource.BaseMeta;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.module.service.TeamService;
import lombok.Data;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Nonnull;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@Repository
public class ExtMetaRepo {
    private final MetaEditService editService;
    private final MetaBlobRepo metaBlobRepo;
    private final TeamService teamService;

    public void create(ExtMeta resource, ResourceContext ctx) {
        MetaTreeNode node = resource.convert();
        editService.submitOp(
                EditUtil.newFrom(ctx),
                EditUtil.createNodeOp(node, new MoveTarget(node.getParentKey(), MoveTargetType.ChildLast))
        );
    }

    public void update(ExtMeta resource, ResourceContext ctx) {
        MetaTreeNodeExt node = resource.convert();
        editService.submitOp(EditUtil.newFrom(ctx), EditUtil.updateNodeOp(node));
    }

    public void deleteByKey(String key, Boolean recursive, Boolean verbose, Boolean force, ResourceContext ctx) {
        editService.submitOp(EditUtil.newFrom(ctx), EditUtil.deleteNodeOp(key, recursive, verbose, force));
    }

    public Optional<ExtMeta> findOneByKey(String key, ResourceContext ctx) {
        Long teamId = teamService.getTeamIdByCode(ctx.getTeamCode());
        return metaBlobRepo.findOne(teamId, MetaBlob.Full.class, key)
                .filter(it -> Objects.equals(it.getType(), MetaType.Ext.name()))
                .map(blob -> IndexUtil.blob2node(blob, teamId, ctx.getTeamCode()))
                .map(this::convert);
    }

    public List<ExtMeta> findAllByKeys(Collection<String> keys, ResourceContext ctx) {
        if (CollectionUtils.isEmpty(keys)) {
            return Collections.emptyList();
        }
        Long teamId = teamService.getTeamIdByCode(ctx.getTeamCode());
        return metaBlobRepo.find(teamId, MetaBlob.Full.class, keys)
                .stream()
                .filter(it -> Objects.equals(it.getType(), MetaType.Ext.name()))
                .map(blob -> IndexUtil.blob2node(blob, teamId, ctx.getTeamCode()))
                .map(this::convert)
                .collect(Collectors.toList());
    }

    public Map<String, Boolean> existKeys(Collection<String> keys, @Nonnull ResourceContext ctx) {
        if (CollectionUtils.isEmpty(keys)) {
            return Collections.emptyMap();
        }
        Map<String, Boolean> res = new HashMap<>(keys.size());
        Long teamId = teamService.getTeamIdByCode(ctx.getTeamCode());
        metaBlobRepo.find(teamId, MetaBlob.Key.class, keys).forEach(
                exists -> res.put(exists.getKey(), true)
        );
        return res;
    }

    private ExtMeta convert(MetaTreeNodeExt node) {
        ExtMeta r = BaseMeta.newInstance(ExtMeta.class);
        r.from(node);
        return r;
    }
}
