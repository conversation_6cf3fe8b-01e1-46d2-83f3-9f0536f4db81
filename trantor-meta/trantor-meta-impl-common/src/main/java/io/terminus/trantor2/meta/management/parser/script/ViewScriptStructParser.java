package io.terminus.trantor2.meta.management.parser.script;

import com.google.common.collect.Lists;
import io.terminus.trantor2.meta.api.dto.MetaLink;
import io.terminus.trantor2.meta.api.model.MetaType;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2.5.24.0130
 */
public class ViewScriptStructParser extends ScriptParser {

    private static final Map<String, String> FIELD_TYPES = new HashMap<>();

    static {
        FIELD_TYPES.put("sceneKey", MetaType.Scene.name());
    }

    @Override
    public List<Position> doParse(String script) {
        List<Position> positions = Lists.newArrayList();
        if (script == null || script.isEmpty()) {
            return positions;
        }

        int left = 0;
        while (true) {
            while (left < script.length()) {
                char c = script.charAt(left);
                if (c == '{') {
                    break;
                }
                left++;
            }
            if (left == script.length()) {
                break;
            }
            int right = left + 1;
            while (right < script.length()) {
                char c = script.charAt(right);
                if (c == '}') {
                    break;
                }
                right++;
            }
            if (right == script.length()) {
                break;
            }
            String fields = script.substring(left + 1, right);
            String[] fieldArray = fields.split(",");
            int fieldStart = left + 1;
            for (String field : fieldArray) {
                String[] kv = field.split(":");
                if (kv.length != 2) {
                    fieldStart += field.length() + 1;
                    continue;
                }
                String type = FIELD_TYPES.get(kv[0].trim());
                if (type == null) {
                    fieldStart += field.length() + 1;
                    continue;
                }
                String value = kv[1].trim();
                int pad = kv[1].indexOf(value);
                if (value.startsWith("\"") || value.startsWith("'")) {
                    String fieldValue = value.substring(1, value.length() - 1);
                    int argValueStart = fieldStart + pad + kv[0].length() + 2;
                    positions.add(new Position(argValueStart, argValueStart + fieldValue.length(), new MetaLink(type, fieldValue)));
                }
                fieldStart += field.length() + 1;
            }
            left = right + 1;
        }

        // sort positions by start
        positions.sort(Comparator.comparingInt(Position::getStart));
        return positions;
    }
}
