package io.terminus.trantor2.meta.editor.event;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.meta.api.dto.EditOpType;
import io.terminus.trantor2.meta.api.dto.ModuleInfo;
import io.terminus.trantor2.meta.context.MetaContext;
import io.terminus.trantor2.meta.editor.TrantorTestApp;
import io.terminus.trantor2.meta.editor.event.listener.MetaEventListener;
import io.terminus.trantor2.meta.event.*;
import io.terminus.trantor2.properties.SpringApplicationProperties;
import io.terminus.trantor2.test.tool.mysql.MysqlSpringTest;
import io.terminus.trantor2.test.tool.redis.RedisSpringTest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.shaded.com.google.common.collect.Lists;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * <AUTHOR>
 **/
@EnableConfigurationProperties(SpringApplicationProperties.class)
@ExtendWith(SpringExtension.class)
@SpringBootTest(
        classes = {TrantorTestApp.class},
        properties = {
                "spring.application.name=test-app",
                "trantor2.meta.event.type=redis"
        }
)
public class RedisEventTest implements RedisSpringTest, MysqlSpringTest {
    @Autowired
    private MetaEventListener eventListener;

    @Autowired
    private EventPublisher eventPublisher;

    @Test
    void test() {
        MetaContext.setIsDefault(true);
        MetaContext.addModules(Lists.newArrayList(ModuleInfo.of("test", "test")));

        MetaChangeEvent.MetaId metaId = new MetaChangeEvent.MetaId("Module", "module", 1L);
        MetaChangeEvent metaChangeEvent = new MetaChangeEvent(EditOpType.CreateNode, "test", Lists.newArrayList(metaId));
        eventPublisher.publish(metaChangeEvent);
        assertEvent(metaChangeEvent);

        ModuleCreateEvent moduleCreateEvent = new ModuleCreateEvent();
        moduleCreateEvent.setModule(ModuleInfo.of("test", "test"));
        eventPublisher.publish(moduleCreateEvent);
        assertEvent(moduleCreateEvent);

        ModuleDeleteEvent moduleDeleteEvent = new ModuleDeleteEvent();
        moduleDeleteEvent.setModule(ModuleInfo.of("test", "test"));
        eventPublisher.publish(moduleDeleteEvent);
        assertEvent(moduleDeleteEvent);

        DatasourceUpdateEvent datasourceUpdateEvent = new DatasourceUpdateEvent();
        datasourceUpdateEvent.setTeamId(1L);
        datasourceUpdateEvent.setTeamCode("test");
        datasourceUpdateEvent.setDatasourceName("test");
        eventPublisher.publish(datasourceUpdateEvent);
        assertEvent(datasourceUpdateEvent);

        DatasourceDeleteEvent datasourceDeleteEvent = new DatasourceDeleteEvent();
        datasourceDeleteEvent.setTeamId(2L);
        datasourceDeleteEvent.setTeamCode("test2");
        datasourceDeleteEvent.setDatasourceName("test2");
        eventPublisher.publish(datasourceDeleteEvent);
        assertEvent(datasourceDeleteEvent);

        DefaultDatasourceSwitchEvent datasourceSwitchEvent = new DefaultDatasourceSwitchEvent();
        datasourceSwitchEvent.setTeamId(3L);
        datasourceSwitchEvent.setTeamCode("test3");
        datasourceSwitchEvent.setDatasourceName("test3");
        eventPublisher.publish(datasourceSwitchEvent);
        assertEvent(datasourceSwitchEvent);

        ModuleDatasourceUpdateEvent moduleDatasourceUpdateEvent = new ModuleDatasourceUpdateEvent();
        moduleDatasourceUpdateEvent.setTeamId(4L);
        moduleDatasourceUpdateEvent.setTeamCode("test4");
        moduleDatasourceUpdateEvent.setDatasourceName("test4");
        eventPublisher.publish(moduleDatasourceUpdateEvent);
        assertEvent(moduleDatasourceUpdateEvent);

        ModuleConfigChangedEvent moduleConfigChangedEvent = new ModuleConfigChangedEvent();
        moduleConfigChangedEvent.setApplicationName("test-app");
        eventPublisher.publish(moduleConfigChangedEvent);
        assertEvent(moduleConfigChangedEvent);

        ModelShardingConfigUpdateEvent modelShardingConfigUpdateEvent = new ModelShardingConfigUpdateEvent();
        modelShardingConfigUpdateEvent.setModelKey("key");
        eventPublisher.publish(moduleCreateEvent);
        assertEvent(moduleCreateEvent);
    }

    private void assertEvent(Event event) {
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        Event listenerEvent = eventListener.getEvent();
        String listenerJson = JsonUtil.toJson(listenerEvent);
        String publishJson = JsonUtil.toJson(event);
        assertEquals(publishJson, listenerJson);
    }
}
