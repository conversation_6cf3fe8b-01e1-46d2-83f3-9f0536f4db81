package io.terminus.trantor2.meta.util;

import org.junit.jupiter.api.Test;

import java.util.Base64;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
class ZipUtilTest {

    @Test
    void test() {
        String sample1 = "H4sIAAAAAAAAAKVQTUsEMQz9LzkPonibm4heRBD8uA6dNjtbtm1Kmo4sw/53M7NbFa82l+blJS95CxhrsRTo4YX9bAShA7v3wTEm6FMNoQOHxbLP4kkhUMIBj/oRLKJJMhF/smy0UZ42wjAwkQzDCgcjO+L4gVy2MYuqoD2UGpV4/c+3blHjiLzOutoCTirKlMsmRWnnp3YOJpfJJ3k7ZmzYFGg04f7CWyCSw/DoMbhXDGgFHfTCFbvflT19PiQzBjzXVDHQRO8c2tRkxM/4TK42TgeMagW6M6g+lUYuVNniXc4NkG0/uLR3MDfv4KZdWNTEaL5Nvf3bdPoCYeHWDeABAAA=";
        String sample2 = "H4sIAAAAAAAAAKVQy07EMAz8F58rBOK2N4TggpCQeFyrNPG20aZx5DhFq2r/HW+6AcSV5BJPZjz2rGCsxZxhBy/sFyMIHdjJB8cYYRdLCB04zJZ9Ek8KgRIOeNSHYBYtopnxp0pGhfJUCX3PRNL3FZZp06ZgZE88fyDn2nBVP7SHXGYlXP/znOcp84B87nVVL5zUlCnlakVx78e2GEaXyEd5OyZs2BhoMOH+wlthJofh0WNwrxjQCjrYCRfsfv9M9PkQzRBw+1PHQCO9c2hdoxG/4DO50jgdMGoU6DZQE8uNnKmwxbuUGiB1PrjIO1hadnDTNswa4my+Q739Kzp9AY1Y+m/qAQAA";

        // base64 decode
        byte[] bytes1 = Base64.getDecoder().decode(sample1);
        byte[] bytes2 = Base64.getDecoder().decode(sample2);

        // unzip
        byte[] unzipped1 = ZipUtil.unzip(bytes1);
        byte[] unzipped2 = ZipUtil.unzip(bytes2);

        // print
        System.out.println("Unzipped 1: " + new String(unzipped1));
        System.out.println("Unzipped 2: " + new String(unzipped2));
    }

    @Test
    void test2() {
        String sample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

        // 0x to bytes
        byte[] bytes1 = hexStringToByteArray(sample1);

        // unzip
        byte[] unzipped1 = ZipUtil.unzip(bytes1);

        // print
        System.out.println("Unzipped 1: " + new String(unzipped1));
    }

    private byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i+1), 16));
        }
        return data;
    }

}
