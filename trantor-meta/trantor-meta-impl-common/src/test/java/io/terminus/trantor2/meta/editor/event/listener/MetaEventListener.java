package io.terminus.trantor2.meta.editor.event.listener;

import io.terminus.trantor2.meta.event.*;
import lombok.Getter;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 **/
@Component
public class MetaEventListener {
    @Getter
    private Event event;

    @EventListener(MetaChangeEvent.class)
    public void listen(MetaChangeEvent msg) {
        event = msg;
    }

    @EventListener(ModuleCreateEvent.class)
    public void listen(ModuleCreateEvent msg) {
        event = msg;
    }

    @EventListener(ModuleDeleteEvent.class)
    public void listen(ModuleDeleteEvent msg) {
        event = msg;
    }

    @EventListener(DatasourceUpdateEvent.class)
    public void listen(DatasourceUpdateEvent msg) {
        event = msg;
    }

    @EventListener(DatasourceDeleteEvent.class)
    public void listen(DatasourceDeleteEvent msg) {
        event = msg;
    }

    @EventListener(DefaultDatasourceSwitchEvent.class)
    public void listen(DefaultDatasourceSwitchEvent msg) {
        event = msg;
    }

    @EventListener(ModuleDatasourceUpdateEvent.class)
    public void listen(ModuleDatasourceUpdateEvent msg) {
        event = msg;
    }

    @EventListener(ModuleConfigChangedEvent.class)
    public void listen(ModuleConfigChangedEvent msg) {
        event = msg;
    }

    @EventListener(ModelShardingConfigUpdateEvent.class)
    public void listen(ModelShardingConfigUpdateEvent msg) {
        event = msg;
    }
}
