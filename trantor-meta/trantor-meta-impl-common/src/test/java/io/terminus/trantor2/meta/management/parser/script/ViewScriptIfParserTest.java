package io.terminus.trantor2.meta.management.parser.script;

import io.terminus.trantor2.meta.api.dto.MetaLink;
import io.terminus.trantor2.meta.api.model.MetaType;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
class ViewScriptIfParserTest {

    @Test
    void test() {
        ScriptParser parser = new ViewScriptIfParser();

        String script1 = "route.query?.copyId ? \"TERP_MIGRATE$SYS_CopyDataConverterService\" : \"TERP_MIGRATE$SYS_FindDataByIdService\"";

        assertArrayEquals(
            new MetaLink[]{
                new MetaLink(MetaType.ServiceDefinition.name(), "TERP_MIGRATE$SYS_CopyDataConverterService"),
                new MetaLink(MetaType.ServiceDefinition.name(), "TERP_MIGRATE$SYS_FindDataByIdService")
            },
            parser.parse(script1).toArray()
        );

        assertEquals(
            "route.query?.copyId ? \"GEN$SYS_CopyDataConverterService\" : \"TERP_MIGRATE$SYS_FindDataByIdService\"",
            parser.replace(
                script1,
                new MetaLink(MetaType.ServiceDefinition.name(),
                    "TERP_MIGRATE$SYS_CopyDataConverterService"),
                "GEN$SYS_CopyDataConverterService"
            )
        );

        assertEquals(
            "route.query?.copyId ? \"TERP_MIGRATE$SYS_CopyDataConverterService\" : \"GEN$SYS_FindDataByIdService\"",
            parser.replace(
                script1,
                new MetaLink(MetaType.ServiceDefinition.name(),
                    "TERP_MIGRATE$SYS_FindDataByIdService"),
                "GEN$SYS_FindDataByIdService"
            )
        );
    }
}
