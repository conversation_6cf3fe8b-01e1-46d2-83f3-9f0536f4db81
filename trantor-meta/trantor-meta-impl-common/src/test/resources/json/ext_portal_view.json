{"extended": true, "key": "moduleKey$key", "name": "name", "parentKey": "moduleKey$parentKey", "teamCode": "team", "type": "View", "props": {"key": "moduleKey$key-list", "type": "LIST", "title": "list", "select": null, "content": {"key": "moduleKey$key-page", "name": "Page", "type": "Container", "children": [{"key": "moduleKey$key-column-page", "name": "ColumnPage", "type": "Layout", "props": {}, "children": [{"key": "moduleKey$key-list-table", "name": "Table", "type": "Container", "props": {}, "children": [{"key": "moduleKey$key-list-batch-actions", "name": "BatchActions", "props": {}, "children": [{"ext": true, "key": "moduleKey$newButton-ext1-remain0", "name": "newButton", "type": "Widget", "props": {}, "children": []}, {"key": "moduleKey$key-ext1-remain0", "name": "ImportButton", "type": "Widget", "props": {"label": "导入"}, "children": []}]}]}, {"key": "moduleKey$key-column-page-stackView", "name": "StackView", "type": "Container", "props": {"key": "list", "label": "hello", "filterFields": [{"name": "abc", "label": "类型", "editComponentProps": {"options": [{"label": "a", "value": "A"}, {"label": "B", "value": "B"}, {"label": "C", "value": "C"}]}}], "editComponentProps": {"options": [{"label": "省份", "value": "PROVINCE"}, {"label": "市", "value": "CITY"}, {"label": "区", "value": "DISTRICT"}]}}, "children": [{"key": "moduleKey$key-ext2-remain0", "name": "Box", "type": "Layout", "props": {"style": {"height": "100%", "display": "flex", "align-items": "center", "justify-content": "center"}}, "children": [{"key": "moduleKey$key-ext2-children1", "name": "Empty", "props": {"imageStyle": {"width": 220, "height": 220}, "description": "从左侧选中数据后查看详情~"}, "children": []}]}, {"key": "moduleKey$key-detailView", "name": "View", "type": "Container", "props": {}, "children": [{"key": "moduleKey$key-detailView-useQuery", "name": "UseQuery", "type": "Meta", "props": {}, "children": [{"key": "moduleKey$key-detailView-page", "name": "Page", "type": "Container", "props": {}, "children": [{"key": "moduleKey$key-detailView-page-title", "name": "Page<PERSON><PERSON>le", "type": "Layout", "props": {}, "children": []}, {"key": "moduleKey$key-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "Layout", "props": {}, "children": [{"key": "moduleKey$key-ext3-remain1", "name": "Space", "type": "Layout", "props": {}, "children": []}]}]}]}]}]}]}, {"ext": true, "children": [], "key": "moduleKey$addNewLayout-ext1-remain0", "name": "ColumnPage", "props": {}, "type": "Layout"}]}}}