{"type": "ServiceDefinition", "key": "demo$test", "name": "test", "props": {"isEnabled": false, "serviceType": "PROGRAMMABLE", "serviceDslJson": {"key": "demo$test", "name": "test", "type": "ServiceDefinition", "props": {"type": "ServiceProperties", "teamId": 3, "transactionPropagation": "REQUIRED"}, "children": [{"ext": false, "key": "Start", "name": "开始", "type": "StartNode", "props": {"type": "StartProperties"}}, {"ext": false, "key": "A", "name": "A", "type": "RetrieveDataNode", "props": {}, "headNodeKeys": null}, {"ext": false, "key": "PaiTa", "name": "排他分支", "type": "ExclusiveBranchNode", "props": {"type": "ExclusiveBranchProperties"}, "children": [{"ext": false, "key": "PaiTa_IF", "name": "IF", "type": "ConditionNode", "props": {}}, {"ext": false, "key": "PaiTa_Aa", "name": "PaiTa_Aa", "type": "RetrieveDataNode", "props": {}}, {"ext": false, "key": "PaiTa_ELSE", "name": "else", "type": "ConditionElseNode", "props": {}}], "headNodeKeys": ["PaiTa_IF", "PaiTa_ELSE"]}, {"ext": false, "key": "B", "name": "B", "type": "RetrieveDataNode", "props": {}}, {"ext": false, "key": "C", "name": "C", "type": "RetrieveDataNode", "props": {}}, {"ext": false, "key": "END", "name": "结束", "type": "EndNode", "props": {"type": "EndProperties"}}], "headNodeKeys": ["Start"]}}}