package io.terminus.trantor2;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.common.VersionConfig;
import io.terminus.trantor2.common.utils.VersionUtil;
import io.terminus.trantor2.meta.platform.PlatformConfigHolder;
import io.terminus.trantor2.meta.platform.PlatformVersion;
import io.terminus.trantor2.meta.sandbox.ProgressHelper;
import io.terminus.trantor2.meta.sandbox.SandboxCommand;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.scene.config.datamanager.DataManagerSceneConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.io.File;
import java.nio.file.Files;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@SpringBootApplication
public class TrantorMetaSandboxApp implements CommandLineRunner, ApplicationContextAware {

    public static void main(String[] args) {
        SpringApplication.run(TrantorMetaSandboxApp.class, args).close();
    }

    @Override
    public void run(String... args) {
        ObjectJsonUtil.MAPPER.registerSubtypes(
                Collections.singletonList(DataManagerSceneConfig.class));

        String command = System.getProperty("trantor2.sandbox.command");
        if (command == null) {
            System.out.println("No command specified, exiting...");
            System.exit(1);
        }
        String tempDir = System.getProperty("trantor2.sandbox.tempDir");
        // check temp dir
        if (tempDir == null) {
            System.out.println("No temp dir specified, exiting...");
            System.exit(1);
        }
        // check temp dir exists and is a directory
        File tempDirFile = new File(tempDir);
        if (!tempDirFile.exists() || !tempDirFile.isDirectory()) {
            System.out.println("Temp dir not exists, exiting...");
            System.exit(1);
        }
        try {
            byte[] rawInput = Files.readAllBytes(tempDirFile.toPath().resolve("input.json"));
            JsonNode input = ObjectJsonUtil.MAPPER.reader().readTree(rawInput);
            if (input == null || !input.isObject()) {
                System.out.println("Invalid input, exiting...");
                System.exit(1);
            }

            // force set platform version
            Map<String, Object> modInfo = new HashMap<>();
            modInfo.put(
                    PlatformConfigHolder.CONFIG_PLATFORM_VERSION,
                    PlatformVersion.of(
                            VersionUtil.getVersion(VersionConfig.projectVersion),
                            "0000000000000000000000000000000000000000000000000000000000000000"
                    )
            );
            PlatformConfigHolder.setInfo(modInfo);

            SandboxCommand cmd = applicationContext.getBean(command, SandboxCommand.class);

            ProgressHelper progress = new ProgressHelper(tempDirFile);
            cmd.exec((ObjectNode) input, tempDirFile.toPath(), progress);
        } catch (Exception e) {
            log.error("Failed to execute command", e);
            System.exit(1);
        }
        System.exit(0);
    }

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
