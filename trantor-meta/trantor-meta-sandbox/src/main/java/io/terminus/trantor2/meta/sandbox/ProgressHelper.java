package io.terminus.trantor2.meta.sandbox;

import java.io.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

public final class ProgressHelper {

    private final File progressFile;

    public ProgressHelper(File tempDirFile) {
        this.progressFile = new File(tempDirFile, "task-progress.log");
        // create file if not exists
        if (!progressFile.exists()) {
            try {
                progressFile.createNewFile();
            } catch (Exception e) {
                throw new RuntimeException("Failed to create progress file", e);
            }
        }
    }

    @FunctionalInterface
    public interface StepExecution {
        void execute(PrintStream ps);
    }

    private void write(String message) {
        try (PrintWriter out = new PrintWriter(new FileWriter(progressFile, true))) {
            out.println(message);
        } catch (IOException e) {
            throw new RuntimeException("Failed to write to progress file", e);
        }
    }

    private void writeStart(String id, String desc) {
        write("[[" + id + "]] " + desc + " START");
    }

    private void writeMsg(String id, String msg) {
        write("[[" + id + "]] " + msg);
    }

    private void writeDone(String id, String desc) {
        write("[[" + id + "-Done]] " + desc + " DONE");
    }

    public void exec(String id, String desc, StepExecution step) {
        writeStart(id, desc);

        PrintStream originalOut = System.out;
        BlockingQueue<String> logQueue = new LinkedBlockingQueue<>();
        Thread logWriterThread = createLogWriterThread(id, logQueue);

        logWriterThread.start();

        try (PrintStream ps = createLogRedirectStream(logQueue)) {
            step.execute(ps);
        } catch (Exception e) {
            throw new RuntimeException("Error while executing task: " + id, e);
        } finally {
            cleanup(logQueue, logWriterThread, originalOut);
        }

        writeDone(id, desc);
    }

    private Thread createLogWriterThread(String id, BlockingQueue<String> logQueue) {
        return new Thread(() -> {
            try {
                while (true) {
                    String line = logQueue.take();
                    if ("__EOF__".equals(line)) break;
                    writeMsg(id, line);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }, "LogWriterThread-" + id);
    }

    private PrintStream createLogRedirectStream(BlockingQueue<String> logQueue) {
        return new PrintStream(new OutputStream() {
            @Override
            public void write(byte[] b, int off, int len) {
                try {
                    logQueue.put(new String(b, off, len));
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            @Override
            public void write(int b) {
            }
        });
    }

    private void cleanup(BlockingQueue<String> logQueue, Thread logWriterThread, PrintStream originalOut) {
        try {
            logQueue.put("__EOF__");
            logWriterThread.join();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            //System.setOut(originalOut);
        }
    }
}
