package io.terminus.trantor2.meta.sandbox.env;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class EnvFileParser {

    /**
     * 解析 .env 文件
     *
     * @param filePath .env 文件路径
     * @return 解析后的键值对
     * @throws IOException 如果文件读取失败
     */
    public static Map<String, Object> parseEnvFile(String filePath) throws IOException {
        Map<String, Object> envMap = new HashMap<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                // 跳过注释或空行
                line = line.trim();
                if (line.isEmpty() || line.startsWith("#")) {
                    continue;
                }

                // 解析 key=value 格式
                int equalsIndex = line.indexOf('=');
                if (equalsIndex > 0) {
                    String key = line.substring(0, equalsIndex).trim();
                    String value = line.substring(equalsIndex + 1).trim();

                    // 去掉包裹的引号（如果有）
                    if ((value.startsWith("\"") && value.endsWith("\"")) ||
                            (value.startsWith("'") && value.endsWith("'"))) {
                        value = value.substring(1, value.length() - 1);
                    }

                    envMap.put(key, value);
                }
            }
        }
        return envMap;
    }
}
