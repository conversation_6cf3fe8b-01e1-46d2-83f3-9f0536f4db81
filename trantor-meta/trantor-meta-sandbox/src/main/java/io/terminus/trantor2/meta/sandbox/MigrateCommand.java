package io.terminus.trantor2.meta.sandbox;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.google.common.hash.Hashing;
import io.terminus.trantor2.DummyOSSService;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.ide.import_export.MetaReadWriteCenter;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.behaviors.RefreshCompPackJob;
import io.terminus.trantor2.meta.blob.MetaBlobRepo;
import io.terminus.trantor2.meta.client.TrantorConsoleClient;
import io.terminus.trantor2.meta.editor.service.EditorMetaQueryService;
import io.terminus.trantor2.meta.editor.service.ObjectTraversal;
import io.terminus.trantor2.meta.management.service.EditorMetaEditService;
import io.terminus.trantor2.meta.management.task.StartTaskResult;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskDefine;
import io.terminus.trantor2.meta.management.task.TaskManager;
import io.terminus.trantor2.meta.management.task.TaskRunRepo;
import io.terminus.trantor2.meta.management.util.UploadUtil;
import io.terminus.trantor2.meta.object.MetaObjectRepo;
import io.terminus.trantor2.meta.task.PlatformMigrationTask;
import io.terminus.trantor2.meta.task.ResetIndexTask;
import io.terminus.trantor2.meta.task.SnapshotTask;
import io.terminus.trantor2.meta.task.SyncObjectTask;
import io.terminus.trantor2.meta.task.TaskRunDetail;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.meta.util.ZipFileUtil;
import io.terminus.trantor2.module.entity.TrantorTeamEntity;
import io.terminus.trantor2.module.repository.TrantorTeamRepository;
import io.terminus.trantor2.module.service.OSSService;
import io.terminus.trantor2.properties.TrantorConsoleProperties;
import io.terminus.trantor2.task.TaskRunStatus;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@Component
public class MigrateCommand implements SandboxCommand {

    private final TrantorTeamRepository trantorTeamRepository;
    private final MetaBlobRepo metaBlobRepo;
    private final EditorMetaEditService editorMetaEditService;
    private final TaskManager taskManager;
    private final TaskRunRepo taskRunRepo;
    private final MetaObjectRepo metaObjectRepo;
    private final EditorMetaQueryService metaQueryService;
    private final MetaReadWriteCenter metaReadWriteCenter;
    private final DummyOSSService dummyOSSService;

    // local variables
    MigrateInput input;
    private TrantorConsoleClient trantorConsoleClient;
    private String snapshotOid;
    private String downloadUrl;
    private String localTeamCode;
    private Long localTeamId;
    private TaskContext localTaskContext;
    private String localSnapshotOid;

    @Override
    public void exec(ObjectNode originInput, Path tempDir, ProgressHelper progress) {
        progress.exec("remote-prepare", "0. Prepare", (ps) -> {
            TrantorContext.init();
            User fakeUser = new User();
            fakeUser.setId(0L);
            TrantorContext.setCurrentUser(fakeUser);
            input = MigrateInput.fromJson(originInput.toString());
            trantorConsoleClient = new TrantorConsoleClient(input.getBaseUrl(), input.getTeamCode(), input.getSession());
        });

        progress.exec("remote-pre-check", "1. Pre-check", (ps) -> {
            // check trantor2 version
            String version = trantorConsoleClient.getTrantor2Version();
            System.out.println("Trantor2 Version: " + version);
            // check current user
            User user = trantorConsoleClient.getCurrentUser();
            System.out.println("Current User: " + user.getUsername());
        });

        progress.exec("remote-snapshot", "2. Remote Snapshot", (ps) -> {
            snapshotOid = trantorConsoleClient.remoteSnapshot();
            System.out.println("Snapshot OID: " + snapshotOid);
            downloadUrl = trantorConsoleClient.remoteUploadSnapshotToOSS(snapshotOid);
            System.out.println("Download URL: " + downloadUrl);
        });

        progress.exec("local-prepare", "3. Local Prepare", (ps) -> {
            // prepare team
            // localTeamCode = sha256(baseUrl).first 6 + _ + remoteTeamCode
            String baseUrl = input.getBaseUrl();
            String remoteTeamCode = input.getTeamCode();
            localTeamCode = Hashing.sha256().hashBytes(baseUrl.getBytes()).toString().substring(0, 6) + "_" + remoteTeamCode;
            System.out.println("Local Team Code: " + localTeamCode);
            localTeamId = prepareTeam(localTeamCode);
            System.out.println("Local Team ID: " + localTeamId);

            // prepare compPack
            TrantorConsoleProperties refreshProperties = new TrantorConsoleProperties();
            refreshProperties.setDomain(input.getBaseUrl());
            RefreshCompPackJob refreshCompPackJob = new RefreshCompPackJob(refreshProperties);
            // TODO: should isolate behaviors for each site
            try {
                refreshCompPackJob.refreshCompPacks();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }

            localTaskContext = new TaskContext(localTeamId, localTeamCode, 0L, false);
        });

        progress.exec("local-sync", "4. Local Sync", (ps) -> {
            SyncObjectTask.Options taskOpts = new SyncObjectTask.Options();
            taskOpts.setDownloadUrl(downloadUrl);
            TaskRunDetail syncTaskResult = execTask(new TaskDefine(SyncObjectTask.class, taskOpts, "", true), localTaskContext);
            if (!syncTaskResult.getStatus().equals(TaskRunStatus.Success.name())) {
                throw new RuntimeException("Sync Task Failed");
            }
        });

        progress.exec("local-reset-index", "5. Local Reset Index", (ps) -> {
            ResetIndexTask.Options resetIndexOpts = new ResetIndexTask.Options();
            resetIndexOpts.setRootOid(snapshotOid);
            TaskRunDetail resetIndexTaskResult = execTask(new TaskDefine(ResetIndexTask.class, resetIndexOpts, "", true), localTaskContext);
            if (!resetIndexTaskResult.getStatus().equals(TaskRunStatus.Success.name())) {
                throw new RuntimeException("Reset Index Task Failed");
            }
        });

        progress.exec("local-migrate", "6. Local Migrate", (ps) -> {
            Path extractedPath = tempDir.resolve("extracted");
            if (input.test) {
                // clear before
                if (Files.exists(extractedPath)) {
                    try {
                        Files.walk(extractedPath)
                                .sorted(java.util.Comparator.reverseOrder())
                                .map(Path::toFile)
                                .forEach(File::delete);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
                extractProject(localTeamId, extractedPath.resolve("00before"));
            }

            // do migration
            doMigration(input, localTaskContext);

            // TODO: check migration effect

            // local snapshot
            SnapshotTask.Options snapshotOpts = new SnapshotTask.Options();
            TaskRunDetail snapshotResult = execTask(new TaskDefine(SnapshotTask.class, snapshotOpts, "", true), localTaskContext);
            if (!snapshotResult.getStatus().equals(TaskRunStatus.Success.name())) {
                throw new RuntimeException("Snapshot Task Failed");
            }
            if (snapshotResult.getResult().getData() instanceof Map) {
                localSnapshotOid = (String) ((Map) snapshotResult.getResult().getData()).get("snapshotOid");
            } else {
                throw new RuntimeException("Snapshot Task Result Not Map");
            }
            System.out.println("Local Snapshot OID: " + localSnapshotOid);

            if (input.test) {
                extractProject(localTeamId, extractedPath.resolve("01after"));
            }
        });

        if (input.test) {
            return;
        }
        // TODO: allow send remote task but dry-run for a more deeper test

        progress.exec("remote-overwrite", "7. Remote Overwrite", (ps) -> {
            OSSService fakeOSSService = new DummyOSSService() {
                @Override
                public String uploadFileAndGetUrl(String prefix, String fileName, InputStream stream, String contentType, Boolean isPrivate, Boolean encode) {
                    return trantorConsoleClient.uploadZip(fileName, stream);
                }
            };
            ObjectTraversal traversal = new ObjectTraversal(metaObjectRepo, false);
            String uploadedUrl = UploadUtil.uploadMeta(fakeOSSService, traversal, localTeamId, localTeamCode, localSnapshotOid, true);
            System.out.println("Uploaded URL: " + uploadedUrl);

            // upload to remote
            trantorConsoleClient.remoteSyncAllInOne(uploadedUrl);
        });
    }

    private void doMigration(MigrateInput input, TaskContext localTaskContext) {
        // scriptPath and taskCode only one of them should be set
        if (input.getScriptPath() == null && input.getTaskCode() == null) {
            throw new IllegalArgumentException("scriptPath or taskCode should be set");
        }
        if (input.getScriptPath() != null && input.getTaskCode() != null) {
            throw new IllegalArgumentException("scriptPath and taskCode should not be set at the same time");
        }
        if (input.getScriptPath() != null) {
            PlatformMigrationTask.Options migrateOpts = new PlatformMigrationTask.Options();
            migrateOpts.setMigrates(Lists.newArrayList(input.getScriptPath()));
            TaskRunDetail migrateTask = execTask(new TaskDefine(PlatformMigrationTask.class, migrateOpts, "", true), localTaskContext);
            if (!migrateTask.getStatus().equals(TaskRunStatus.Success.name())) {
                throw new RuntimeException("Migration Task Failed");
            }
        } else {
            ObjectNode taskOpts = ObjectJsonUtil.MAPPER.createObjectNode();
            if (input.getTaskOpts() != null) {
                try {
                    taskOpts = (ObjectNode) ObjectJsonUtil.MAPPER.readTree(input.getTaskOpts());
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            }
            TaskRunDetail migrateTask = execTask(input.getTaskCode(), taskOpts);
            if (!migrateTask.getStatus().equals(TaskRunStatus.Success.name())) {
                throw new RuntimeException("Migration Task Failed");
            }
        }
    }

    private void extractProject(Long localTeamId, Path extractedPath) {
        dummyOSSService.setDelegate(new DummyOSSService() {
            @Override
            public String uploadFileAndGetUrl(String prefix, String fileName, InputStream stream, String contentType, Boolean isPrivate, Boolean encode) {
                ZipFileUtil.unzip(stream, extractedPath);
                return "";
            }
        });
        List<String> moduleKeys = metaQueryService.queryInTeam(localTeamId).findAll(Field.type().equal(MetaType.Module.name()))
                .stream().map(it -> it.getKey()).collect(Collectors.toList());
        for (String moduleKey : moduleKeys) {
            metaReadWriteCenter.write(localTeamId, moduleKey);
        }
    }

    private TaskRunDetail execTask(String taskCode, ObjectNode taskOpts) {
        StartTaskResult result = taskManager.startAsyncTask(taskCode, false, taskOpts);
        Long taskRunId = result.getTaskRunId();
        System.out.println("Task Run ID: " + taskRunId + " Started");
        try {
            result.getFuture().get();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
        System.out.println("Task Run ID: " + taskRunId + " Finished");
        return taskRunRepo.findByIdWithLogsAndSubTasks(taskRunId).orElseThrow(() -> new RuntimeException("Task Run Not Found"));
    }

    private TaskRunDetail execTask(TaskDefine taskDefine, TaskContext taskContext) {
        StartTaskResult result = taskManager.startAsyncTask(taskDefine, taskContext);
        Long taskRunId = result.getTaskRunId();
        System.out.println("Task Run ID: " + taskRunId + " Started");
        try {
            result.getFuture().get();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
        System.out.println("Task Run ID: " + taskRunId + " Finished");
        return taskRunRepo.findByIdWithLogsAndSubTasks(taskRunId).orElseThrow(() -> new RuntimeException("Task Run Not Found"));
    }

    private Long prepareTeam(String teamCode) {
        TrantorTeamEntity entity = trantorTeamRepository.findByCode(teamCode);
        if (entity == null) {
            entity = new TrantorTeamEntity();
            entity.setCode(teamCode);
            entity.setName(teamCode);
            trantorTeamRepository.save(entity);
        }
        MetaEditAndQueryContext ctx = EditUtil.newCtx(entity.getId(), 0L);
        ctx.setTeamCode(teamCode);
        TrantorContext.setTeamId(entity.getId());
        TrantorContext.setTeamCode(teamCode);
        editorMetaEditService.initRepo(ctx);
        return entity.getId();
    }

    @Data
    public static class MigrateInput {
        private String baseUrl;
        private String teamCode;
        private String session;

        private Boolean test = Boolean.FALSE;
        private String scriptPath;
        private String taskCode;
        private String taskOpts;
        // TODO: security code

        public static MigrateInput fromJson(String json) {
            try {
                return ObjectJsonUtil.MAPPER.readValue(json, MigrateInput.class);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
    }
}
