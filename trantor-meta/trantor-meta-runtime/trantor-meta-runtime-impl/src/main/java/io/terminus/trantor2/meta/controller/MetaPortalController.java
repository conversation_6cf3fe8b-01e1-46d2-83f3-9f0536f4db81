package io.terminus.trantor2.meta.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.ResourceNodeLite;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.ImportExportType;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.api.service.QueryOp;
import io.terminus.trantor2.meta.editor.util.IndexUtil;
import io.terminus.trantor2.meta.exception.MetaNotFoundException;
import io.terminus.trantor2.service.report.annotation.TrantorServiceRegistry;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "portal元数据接口")
@Slf4j
@RestController
@RequestMapping("/api/trantor/portal/meta")
@TrantorServiceRegistry
@AllArgsConstructor
public class MetaPortalController {

    private final MetaQueryService queryService;

    @Operation(summary = "导入模版列表查询")
    @GetMapping("/list/ImportTemplate")
    public Response<Paging<ResourceNodeLite>> listImportTemplate() {
        return Response.ok(listImportExportTemplate(ImportExportType.Import));
    }

    @Operation(summary = "导出模版列表查询")
    @GetMapping("/list/ExportTemplate")
    public Response<Paging<ResourceNodeLite>> listExportTemplate() {
        return Response.ok(listImportExportTemplate(ImportExportType.Export));
    }

    @Operation(summary = "导入或导出模版详情查询")
    @GetMapping("/detail/ImportExportTemplate")
    public Response<MetaTreeNode> detailImportExportTemplate(@RequestParam String key) {
        String teamCode = mustGetTeamCode();
        MetaTreeNodeExt node = queryService.queryInTeam(teamCode).findOne(
                Field.type().equal(MetaType.ImportExportTemplate.name()).and(
                        Field.key().equal(key)
                )
        ).orElseThrow(() -> new MetaNotFoundException(MetaType.ImportExportTemplate.name(), key));
        return Response.ok(node);
    }

    @Operation(summary = "打印场景列表查询")
    @GetMapping("/list/PrintScene")
    public Response<Paging<ResourceNodeLite>> listPrintScene() {
        return Response.ok(listMeta(MetaType.PrintScene));
    }


    // TODO: 2024/8/6 filter by portal
    private Paging<ResourceNodeLite> listMeta(MetaType type) {
        String teamCode = mustGetTeamCode();
        QueryOp q = queryService.queryInTeam(teamCode);
        List<MetaTreeNodeExt> data = q.findAll(Field.type().equal(type.name()));
        List<ResourceNodeLite> result = new ArrayList<>();
        if (data != null) {
            for (MetaTreeNodeExt node : data) {
                result.add(IndexUtil.full2lite(node));
            }
        }
        return new Paging<>(result);
    }

    private Paging<ResourceNodeLite> listImportExportTemplate(ImportExportType type) {
        String teamCode = mustGetTeamCode();
        QueryOp q = queryService.queryInTeam(teamCode);
        List<MetaTreeNodeExt> data = q.findAll(
                Field.type().equal(MetaType.ImportExportTemplate.name())
                        .and(Field.props(String.class, "type").equal(type.name()))
        );
        List<ResourceNodeLite> result = new ArrayList<>();
        if (data != null) {
            for (MetaTreeNodeExt node : data) {
                result.add(IndexUtil.full2lite(node));
            }
        }
        return new Paging<>(result);
    }

    private String mustGetTeamCode() {
        String teamCode = TrantorContext.getTeamCode();
        if (teamCode == null) {
            throw new IllegalArgumentException("teamCode must not be null");
        }
        return teamCode;
    }
}
