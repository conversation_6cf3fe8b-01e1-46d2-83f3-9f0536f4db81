package io.terminus.trantor2.meta.listener;

import io.terminus.trantor2.meta.api.cache.MetaCache;
import io.terminus.trantor2.meta.event.MetaSyncEvent;
import io.terminus.trantor2.meta.event.MetaSyncEventHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MetaCacheClearHandler implements MetaSyncEventHandler {
    @Autowired
    private MetaCache metaCache;

    @Override
    public void handle(MetaSyncEvent metaSyncEvent) {
        log.info("received meta sync event, starting to clean meta cache...");
        metaCache.clear();
        log.info("cleaned meta cache success");
    }
}
