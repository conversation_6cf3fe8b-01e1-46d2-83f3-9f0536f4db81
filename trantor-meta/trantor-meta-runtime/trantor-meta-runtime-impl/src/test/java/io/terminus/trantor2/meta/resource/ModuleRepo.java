package io.terminus.trantor2.meta.resource;

import io.terminus.trantor2.meta.api.repository.RuntimeResourceRepository;
import io.terminus.trantor2.meta.module.Module;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * 2023/7/10 2:50 PM
 */
@Repository
public interface ModuleRepo extends RuntimeResourceRepository<Module> {
    default String test(String moduleKey) {
        return findOneByKey(moduleKey).getKey();
    }
}
