package io.terminus.trantor2.meta.module;

import io.terminus.trantor2.meta.resource.ResourceProps;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * 2023/6/25 11:17 AM
 */
@Data
public class ModuleProps implements ResourceProps {
    /**
     * logo
     */
    private String logoUrl;

    /**
     * 依赖模块列表
     */
    private List<String> relatedModuleKeys;

    /**
     * iam相关配置
     */
    private ModuleIamConfig iamConfig;
}
