package io.terminus.trantor2.common.advice;

import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.exception.ResourceNotFoundException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.validation.BindingResult;
import org.springframework.validation.DirectFieldBindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;

import jakarta.servlet.http.HttpServletRequestWrapper;
import java.security.Principal;
import java.util.Iterator;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR>
 */
class TrantorExceptionAdviceTest {
    TrantorExceptionAdvice exceptionAdvice = new TrantorExceptionAdvice(null);

    @Test
    void handleTrantorBizException() {
        ResourceNotFoundException e = new ResourceNotFoundException("abc");
        ResponseEntity<Object> objectResponseEntity = exceptionAdvice.exceptionHandler(e, new MockHttpServletRequest());

        Assertions.assertInstanceOf(Response.class, objectResponseEntity.getBody());
        Assertions.assertNotNull(((Response) objectResponseEntity.getBody()).getErr().getMsg());
        Assertions.assertEquals("abc", ((Response) objectResponseEntity.getBody()).getErr().getMsg());
        Assertions.assertEquals(404, objectResponseEntity.getStatusCodeValue());
    }

    @Test
    void handleUnknownException() {
        IllegalStateException e = new IllegalStateException("abc");
        ResponseEntity<Object> objectResponseEntity = exceptionAdvice.exceptionHandler(e,new MockHttpServletRequest());

        Assertions.assertInstanceOf(Response.class, objectResponseEntity.getBody());
        Assertions.assertEquals(500, objectResponseEntity.getStatusCodeValue());
        Assertions.assertNotNull(((Response) objectResponseEntity.getBody()).getErr().getMsg());
        Assertions.assertEquals("abc", ((Response) objectResponseEntity.getBody()).getErr().getMsg());
    }

    @Test
    void handleValidException() {
        BindingResult bindingResult = new DirectFieldBindingResult("a", "b");
        MethodParameter methodParameter = new MethodParameter(TrantorExceptionAdvice.class.getMethods()[0], 0);

        MethodArgumentNotValidException exception = new MethodArgumentNotValidException(methodParameter, bindingResult);
        ResponseEntity<Object> objectResponseEntity = exceptionAdvice.handleMethodArgumentNotValid(exception, new HttpHeaders(), HttpStatus.BAD_REQUEST, Mockito.mock(ServletWebRequest.class));
        Assertions.assertInstanceOf(Response.class, objectResponseEntity.getBody());
        Assertions.assertNull(((Response) objectResponseEntity.getBody()).getErr().getMsg());

        bindingResult.addError(new ObjectError("a", "a"));
        bindingResult.addError(new ObjectError("b", "b"));
        objectResponseEntity = exceptionAdvice.handleMethodArgumentNotValid(exception, new HttpHeaders(), HttpStatus.BAD_REQUEST, Mockito.mock(ServletWebRequest.class));
        Assertions.assertInstanceOf(Response.class, objectResponseEntity.getBody());
        Assertions.assertEquals("a, b", ((Response) objectResponseEntity.getBody()).getErr().getMsg());
    }
}
