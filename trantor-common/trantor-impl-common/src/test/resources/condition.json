{"conditions": [{"conditions": [{"key": "c1", "param": {"modelKey": "CON$user", "modelName": "CON$user", "columnKey": "created<PERSON>y", "columnName": "创建人", "columnType": "Number", "next": {"modelKey": "CON$user", "columnKey": "id", "columnName": "ID", "columnType": "Number"}, "leftValue": {"type": "VarValue", "varValue": [{"valueKey": "created<PERSON>y", "valueName": "创建人", "modelAlias": "CON$con_sign_task_tr", "relatedModel": {"modelKey": "CON$user", "modelAlias": "CON$user", "modelName": "CON$user"}}, {"valueKey": "id", "valueName": "ID", "modelAlias": "CON$user"}], "valueType": "MODEL", "fieldType": "Number"}}, "operator": "EQ", "conditionValues": [{"valueType": "SPECIFY_VALUE", "values": [700885210726533], "variableValue": {"name": "当前登录人id", "value": "CurrentUserId()"}}]}]}, {"conditions": [{"key": "c2", "param": {"modelKey": "CON$con_sign_task_tr", "columnKey": "id", "columnName": "ID", "columnType": "Number", "leftValue": {"type": "VarValue", "varValue": [{"valueKey": "id", "valueName": "ID", "modelAlias": "CON$con_sign_task_tr"}], "valueType": "MODEL", "fieldType": "Number"}}, "operator": "IS_NOT_NULL", "conditionValues": []}]}], "key": "cg1", "nextLogicalOperator": null, "active": null}