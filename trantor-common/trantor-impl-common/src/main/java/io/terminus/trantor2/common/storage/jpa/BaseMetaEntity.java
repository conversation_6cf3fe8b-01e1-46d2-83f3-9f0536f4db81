package io.terminus.trantor2.common.storage.jpa;

import io.terminus.trantor2.common.utils.JsonUtil;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;
import lombok.experimental.SuperBuilder;
import org.hibernate.Hibernate;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 元数据实体基类
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
@MappedSuperclass
@SuperBuilder
@Getter
@Setter
@NoArgsConstructor
@EntityListeners({AuditingEntityListener.class})
@FieldNameConstants
public class BaseMetaEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    private static final int DEFAULT_ALLOCATION_SIZE = 10;

    /**
     * 元数据模型主键
     * 主键生成策略：采用TABLE，可支持多种数据库类型，且生成性能优于AUTO（可由allocationSize调节：值越大性能越高、跳号越大）
     */
    @Id
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "hibernate_sequences")
    @TableGenerator(name = "hibernate_sequences", allocationSize = DEFAULT_ALLOCATION_SIZE)
    protected Long id;

    /**
     * 创建人ID
     */
    @CreatedBy
    protected Long createdBy;

    /**
     * 创建时间
     */
    @CreationTimestamp
    protected Date createdAt;

    /**
     * 修改人ID
     */
    @LastModifiedBy
    protected Long updatedBy;

    /**
     * 最后一次修改时间
     */
    @UpdateTimestamp
    protected Date updatedAt;

    /**
     * 乐观锁版本号
     */
    @Version
    protected Integer version;


    @Override
    public String toString() {
        return JsonUtil.toNonIndentJson(this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) {
            return false;
        }
        BaseMetaEntity baseMetaEntity = (BaseMetaEntity) o;
        return id != null && Objects.equals(id, baseMetaEntity.id);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }

}
