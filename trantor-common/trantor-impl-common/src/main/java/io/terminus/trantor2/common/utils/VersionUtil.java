package io.terminus.trantor2.common.utils;

import cn.hutool.core.util.NumberUtil;
import io.terminus.trantor2.common.VersionConfig;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.lang.Nullable;

import jakarta.annotation.Nonnull;

/**
 * <AUTHOR>
 */
public class VersionUtil {

    /**
     * default version
     */
    public static final String DEFAULT = "1.0.0.DEFAULT";


    public static String getTrantorVersion() {
        return VersionUtil.getVersion(VersionConfig.projectVersion);
    }

    /**
     * 2.0.0..-SNAPSHOT >> 2.0.0(SNAPSHOT返回前三位)
     * 2.5.23.1230..RELEASE >> 2.5.23.1230(RELEASE返回前四位)
     * 其他返回默认版本信息 >> 1.0.0.DEFAULT
     *
     * @param projectVersion 项目版本
     */
    public static String getVersion(String projectVersion) {
        if (projectVersion.endsWith("-SNAPSHOT")) {
            return getSnapshotVersion(projectVersion);
        } else if (projectVersion.endsWith("RELEASE")) {
            String[] versions = projectVersion.split("\\.");
            return getVersion(versions, 3);
        }
        return DEFAULT;
    }

    public static String getVersionWithDate(String projectVersion) {
        if (projectVersion.endsWith("-SNAPSHOT")) {
            return getSnapshotVersion(projectVersion);
        } else if (projectVersion.endsWith("RELEASE")) {
            String[] versions = projectVersion.split("\\.");
            return getVersion(versions, 4);
        }
        return DEFAULT;
    }

    /**
     * 判断指定的版本号是否位于一个左闭右闭的版本区间内。
     * <p>
     * 特殊规则：
     * <ul>
     *   <li>若 lowerBound 和 upperBound 均为空，则不对版本号做限制，任何 version 均返回 true。</li>
     *   <li>若 version 在比较过程中遇到非数字部分，则立即返回 true。
     *       例如：区间 [2.5.1,null] 或 [2.5.1,2.5.28] 都包含 2.5.DEV，因为在比较到非数字部分之前已经满足区间条件。
     *       相反，区间 [2.6,null] 或 [1.6.1,2.0.28] 都不包含 2.5.DEV，因为在比较到非数字部分之前，已经可以判断 version 不在区间内。</li>
     * </ul>
     *
     * @param version    待判断的版本号，不能为空。
     * @param lowerBound 版本区间的左边界（包含），为空表示没有下限。
     * @param upperBound 版本区间的右边界（包含），为空表示没有上限。
     * @return 若版本号落在指定的版本区间内（包括边界），则返回 true；否则返回 false。
     */
    public static boolean versionInRange(@Nonnull String version, @Nullable String lowerBound, @Nullable String upperBound) {
        if (lowerBound == null && upperBound == null) {
            return true;
        }
        if (lowerBound == null) {
            return compareVersions(version, upperBound, true) <= 0;
        }
        if (upperBound == null) {
            return compareVersions(version, lowerBound, false) >= 0;
        }
        return compareVersions(lowerBound, upperBound) <= 0 &&
                compareVersions(version, lowerBound, false) >= 0 &&
                compareVersions(version, upperBound, true) <= 0;
    }


    public static int compareVersions(@Nonnull String v1, @Nonnull String v2) {
        return compareVersions(v1, v2, false);
    }

    /**
     * 比较两个版本号，可指定非数字字符串的比较规则
     * <p>
     * 首先，版本号被分为多个由点"."分隔的段，每个段可以包含数字和字符串。比较时，从左至右逐段进行比较：
     * <ul>
     *   <li>当比较数字部分时，将数字按数值大小进行比较</li>
     *   <li>当比较字符串时，按 strIsSmaller 是否进行比较</li>
     * </ul>
     * <p>
     * @param v1           版本1
     * @param v2           版本2
     * @param strIsSmaller 部分版本为字符串时, 是否小于数值。 例如 false, 2.5.DEV > 2.5.24.0330
     * @return v1 > v2 返回 1, v1 = v2 返回 0, v1 < v2 返回 -1
     */

    public static int compareVersions(@Nonnull String v1, @Nonnull String v2, boolean strIsSmaller) {
        String[] parts1 = v1.replace("-", ".").split("\\.");
        String[] parts2 = v2.replace("-", ".").split("\\.");

        int length = Math.max(parts1.length, parts2.length);

        for (int i = 0; i < length; i++) {
            long num1 = i < parts1.length ? parsePart(parts1[i], strIsSmaller) : 0;
            long num2 = i < parts2.length ? parsePart(parts2[i], strIsSmaller) : 0;

            if (num1 > num2) return 1;
            if (num1 < num2) return -1;
        }
        return 0;
    }

    /**
     * 判断版本号是否为有效发布版本
     */
    public static boolean invalidReleaseVersion(@Nullable String version) {
        if (version == null) {
            return false;
        }
        String regex = "^\\d+(\\.\\d+)*$";
        return !version.matches(regex);
    }

    /**
     * 解析版本号的一部分，如果是数字则返回其数值，如果是字符串则根据 strIsMinNumber 参数返回最大或最小值
     */
    private static long parsePart(String part, boolean strIsMinNumber) {
        if (NumberUtil.isLong(part)) {
            return NumberUtil.parseLong(part);
        } else {
            return !strIsMinNumber ? Long.MAX_VALUE : -1L;
        }
    }

    private static String getSnapshotVersion(String projectVersion) {
        String[] versions = projectVersion.substring(0, projectVersion.length() - 9).split("\\.");
        // 兼容开发版本，默认取前三位
        String version = getVersion(versions, 3);
        if (versions.length > 3) {
            // 测试版本，判断第四位是否数字，是数字保留
            if (NumberUtils.isDigits(versions[3])) {
                version = version + "." + versions[3];
            }
        }
        return version;
    }

    private static String getVersion(String[] versions, int num) {
        StringBuilder result = new StringBuilder();
        int index = 1;
        for (String version : versions) {
            if (index > num) {
                break;
            }
            if (index == num || index >= versions.length) {
                result.append(version);
                break;
            }
            result.append(version).append(".");
            index++;
        }
        return result.toString();
    }

}
