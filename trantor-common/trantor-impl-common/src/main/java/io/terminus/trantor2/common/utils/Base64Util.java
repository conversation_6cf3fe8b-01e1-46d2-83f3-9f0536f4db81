package io.terminus.trantor2.common.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import io.terminus.common.api.util.JsonUtils;
import io.terminus.trantor2.common.user.User;
import org.springframework.util.Base64Utils;

import java.nio.charset.StandardCharsets;

/**
 * base64 编解码工具
 * <p>
 * 以下场景正在使用：
 * 场景1：业务网关 => Trantor 链路的用户信息透传
 * 场景2：T服务RPC调用过程用户信息透传
 *
 * <AUTHOR>
 * 2023/12/25 4:23 PM
 **/
public final class Base64Util {

    private Base64Util() {
    }

    public static <T> String base64Encode(T t) {
        return Base64Utils.encodeToString(JsonUtils.toJson(t).getBytes(StandardCharsets.UTF_8));
    }

    public static <T> T base64Decode(String userEncodedString, Class<T> clazz) {
        byte[] decodedBytes = Base64Utils.decodeFromString(userEncodedString);
        String decodedJson = new String(decodedBytes, StandardCharsets.UTF_8);
        return JsonUtils.fromJson(decodedJson, clazz);
    }

    public static <T> T base64Decode(String userEncodedString, TypeReference<T> typeReference) {
        byte[] decodedBytes = Base64Utils.decodeFromString(userEncodedString);
        String decodedJson = new String(decodedBytes, StandardCharsets.UTF_8);
        return JsonUtils.fromJson(decodedJson, typeReference);
    }

    /**
     * user encode
     *
     * @param user user
     * @return user encode str
     */
    public static String userBase64Encode(User user) {
        return base64Encode(user);
    }

    /**
     * user decode
     *
     * @param userEncodedString 编码用户信息
     * @return User 用户信息对象，内部属性需要与业务网关定义的 User DTO 属性保持一致，才能正确赋值
     */
    public static User userBase64Decode(String userEncodedString) {
        return base64Decode(userEncodedString, User.class);
    }
}
