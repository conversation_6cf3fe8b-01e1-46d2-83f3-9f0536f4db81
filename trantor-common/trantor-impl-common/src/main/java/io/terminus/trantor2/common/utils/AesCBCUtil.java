package io.terminus.trantor2.common.utils;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.PasswordDecryptException;
import io.terminus.trantor2.common.exception.PasswordEncryptException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * AES-CBC加密工具类
 */
@Slf4j
public final class AesCBCUtil {

    private AesCBCUtil() {

    }

    /**
     * 加密类型
     */
    public static final String AES = "AES";
    /**
     * 加密算法
     */
    public static final String AES_CIPHER = "AES/CBC/PKCS5Padding";

    /**
     * AES加密
     *
     * @param content 待加密内容
     * @param key     加密使用的key 需16位长度
     * @param iv      加密使用的偏移量 需16位长度
     * @return 加密后的内容
     */
    @SuppressWarnings("all")
    public static String encrypt(String content, String key, String iv) {
        // 对于前端的密文要先base64解密
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), AES);
        try {
            Cipher cipher = Cipher.getInstance(AES_CIPHER);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, new IvParameterSpec(iv.getBytes()));
            return Base64.getEncoder().encodeToString(cipher.doFinal(content.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception e) {
            ErrorType errorCodeEnum = ErrorType.PASSWORD_ENCRYPT_ERROR;
            String errMsg = String.format(errorCodeEnum.getMessage(), e.getMessage());
            log.error(errMsg);
            throw new PasswordEncryptException(errMsg);
        }
    }

    /**
     * 提供加密解密工具类，统一收口至模型引擎，保证模型定义配置加密字段后　procode 也使用同样的加密和解密逻辑
     * @param toEncrypt 待加密数据，仅支持文本类型加密
     * @return
     */
    public static String encrypt(String toEncrypt) {
        String key = "terminus_trantor";
        if (StringUtils.isNotEmpty(System.getenv("TRANTOR_MODEL_ENCRYPT_KEY"))) {
            key = System.getenv("TRANTOR_MODEL_ENCRYPT_KEY");
        }

        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), AES);
        try {
            Cipher cipher = Cipher.getInstance(AES_CIPHER);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, new IvParameterSpec(key.getBytes()));
            return Base64.getEncoder().encodeToString(cipher.doFinal(toEncrypt.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception e) {
            ErrorType errorCodeEnum = ErrorType.PASSWORD_ENCRYPT_ERROR;
            String errMsg = String.format(errorCodeEnum.getMessage(), e.getMessage());
            log.error(errMsg);
            throw new PasswordEncryptException(errMsg);
        }
    }

    /**
     * AES解密
     *
     * @param cipherStr 加密后的内容
     * @param key       加密使用的key  需16位长度
     * @param iv        加密使用的偏移量  需16位长度
     * @return 解密后的内容
     */
    public static String decrypt(String cipherStr, String key, String iv) {
        // 对于前端的密文要先base64解密
        IvParameterSpec ivParameter = new IvParameterSpec(iv.getBytes());
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), AES);
        try {
            Cipher cipher = Cipher.getInstance(AES_CIPHER);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivParameter);
            return new String(cipher.doFinal(Base64.getDecoder().decode(cipherStr)), StandardCharsets.UTF_8);
        } catch (Exception e) {
            ErrorType errorCodeEnum = ErrorType.PASSWORD_DECRYPT_ERROR;
            String errMsg = String.format(errorCodeEnum.getMessage(), e.getMessage());
            log.error(errMsg);
            throw new PasswordDecryptException(errMsg);
        }
    }

    /**
     * 解密算法统一收口至模型引擎，保证 procode 与 trantor 提供的系统服务流程相同
     * @param encrypted 待解密内容
     * @return
     */
    public static String decrypt(String encrypted) {
        String key = "terminus_trantor";
        if (StringUtils.isNotEmpty(System.getenv("TRANTOR_MODEL_ENCRYPT_KEY"))) {
            key = System.getenv("TRANTOR_MODEL_ENCRYPT_KEY");
        }

        IvParameterSpec ivParameter = new IvParameterSpec(key.getBytes());
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), AES);
        try {
            Cipher cipher = Cipher.getInstance(AES_CIPHER);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivParameter);
            return new String(cipher.doFinal(Base64.getDecoder().decode(encrypted)), StandardCharsets.UTF_8);
        } catch (Exception e) {
            ErrorType errorCodeEnum = ErrorType.PASSWORD_DECRYPT_ERROR;
            String errMsg = String.format(errorCodeEnum.getMessage(), e.getMessage());
            log.error(errMsg);
            throw new PasswordDecryptException(errMsg);
        }
    }

    public static void main(String[] args) {
        for (int i = 1; i <= 1000; i++) {
            repeat("a", i);
        }
    }

    public static void repeat(String str, int repeat) {
        StringBuilder str2 = new StringBuilder();
        for (int i = 0; i < repeat; i++) {
            str2.append(str);
        }
        String result2 = AesCBCUtil.encrypt(str2.toString());
        System.out.println("original length: " + str2.length() + ", encrypted length: " + result2.length());
        // System.out.println("original length: " + str2.length() + ", encrypted length: " + result2.length() + ", original: " + str2 + ", entryped: " + result2);
    }
}
