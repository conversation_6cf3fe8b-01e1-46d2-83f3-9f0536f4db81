package io.terminus.trantor2.common.threadlocal.wrapper;


import io.terminus.trantor2.common.threadlocal.ThreadLocalMigrators;

import java.util.List;

/**
 * 代理runnable执行，执行过程中将当前线程的上下文信息传递给目标线程
 *
 * <AUTHOR>
 * @since 2021/3/26
 */
public class ContextRunnable implements Runnable {

    private final Thread parentThread;
    private final Runnable target;
    private final List<Object> contexts;

    public ContextRunnable(Runnable target, List<Object> contexts) {
        this.parentThread = Thread.currentThread();
        this.target = target;
        this.contexts = contexts;
    }

    public static Runnable convert(Runnable target) {
        // capture context
        List<Object> contexts = ThreadLocalMigrators.capture();
        return new ContextRunnable(target, contexts);
    }

    @Override
    public void run() {
        // set context
        ThreadLocalMigrators.set(contexts);
        try {
            target.run();
        } finally {
            if (!this.parentThread.equals(Thread.currentThread())) {
                // remove context
                ThreadLocalMigrators.remove();
            }
        }
    }
}
