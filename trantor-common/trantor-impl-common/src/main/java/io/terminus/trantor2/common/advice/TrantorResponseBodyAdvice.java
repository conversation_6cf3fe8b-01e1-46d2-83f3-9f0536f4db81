package io.terminus.trantor2.common.advice;

import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.internal.InjectUserInfos;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.iam.service.TrantorIAMUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.MethodParameter;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import jakarta.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice({"io.terminus.trantor2"})
@Order
@RequiredArgsConstructor
public class TrantorResponseBodyAdvice implements ResponseBodyAdvice<Object> {

    @Resource
    private final TrantorIAMUserService userService;

    @Override
    public boolean supports(@NotNull MethodParameter returnType,
                            @NotNull Class<? extends HttpMessageConverter<?>> converterType) {
        return returnType.getMethodAnnotation(InjectUserInfos.class) != null;
    }

    @SuppressWarnings("all")
    @Override
    public Object beforeBodyWrite(Object body, @NotNull MethodParameter returnType,
                                  @NotNull MediaType selectedContentType,
                                  @NotNull Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  @NotNull ServerHttpRequest request, @NotNull ServerHttpResponse response) {
        if (!(body instanceof Response)) {
            return body;
        }
        Response<?> resp = (Response) body;
        Object data = resp.getData();
        if (data == null) {
            return resp;
        }
        Map<Long, User> userInfos = new HashMap<>();
        Set<Long> userIds = new HashSet<>();
        InjectUserInfos anno = returnType.getMethodAnnotation(InjectUserInfos.class);
        if (data instanceof Iterable) {
            for (Object item : (Iterable<?>) data) {
                processItem(item, anno).ifPresent(it -> userIds.addAll(it)); ;
            }
        } else if (data instanceof Map) {
            for (Map.Entry<?, ?> entry : ((Map<?, ?>) data).entrySet()) {
                processItem(entry.getValue(), anno).ifPresent(it -> userIds.addAll(it));
            }
        } else {
            processItem(data, anno).ifPresent(it -> userIds.addAll(it));
        }
        userService.findByIds(userIds).forEach(user -> userInfos.put(user.getId(), user));
        resp.setUserInfos(userInfos);
        if (!CollectionUtils.isEmpty(userInfos)) {
            resp.setUserFields(new HashSet<>(Arrays.asList(anno.value())));
        }
        return resp;
    }

    private Optional<Set<Long>> processItem(Object item, InjectUserInfos annotation) {
        assert annotation != null;
        String[] value = annotation.value();
        if (value == null) {
            return Optional.empty();
        }

        Set<Long> userIds = new HashSet<>();
        for (String userField : value) {
            Field userIdField = getField(item.getClass(), userField);
            if (userIdField != null) {
                userIdField.setAccessible(true);
                try {
                    Long userId = (Long) userIdField.get(item);
                    if (userId != null) {
                        userIds.add(userId);
                    }
                } catch (IllegalAccessException e) {
                    log.error("Failed to get userId from item: {}", item, e);
                }
            }
        }

        return Optional.of(userIds);
    }

    private Field getField(Class<?> clazz, String fieldName) {
        while (clazz != null) {
            try {
                return clazz.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        return null;
    }
}
