package io.terminus.trantor2.common.influxdb;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class InfluxdbCountRequest extends InfluxdbBaseRequest {
    /**
     * 数据的 attribute，用于聚合统计，如: service_key/model_key
     */
    @NotNull
    private List<String> groupBy;
}
