package io.terminus.trantor2.common.utils;

import io.terminus.trantor2.common.dto.Paging;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ClassUtils;

import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.temporal.Temporal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class MapUtil {

    private MapUtil() {
    }

    public static boolean isMap(Object o) {
        if (o == null) {
            return false;
        }
        if (o instanceof Map) {
            return true;
        }
        if ((o instanceof File) || (o instanceof InputStream) || (o instanceof OutputStream)) {
            return false;
        }
        if (o instanceof Collection) {
            return false;
        }
        if (ClassUtils.isPrimitiveOrWrapper(o.getClass()) || ClassUtils.isPrimitiveWrapperArray(o.getClass())
                || ClassUtils.isAssignable(String.class, o.getClass())
                || ClassUtils.isAssignable(Number.class, o.getClass())
                || ClassUtils.isAssignable(Date.class, o.getClass())
                || ClassUtils.isAssignable(Temporal.class, o.getClass())
        ) {
            return false;
        }
        try {
            JsonUtil.toMap(o);
            return true;
        } catch (Throwable ignore) {
            return false;
        }
    }

    @SuppressWarnings({"unchecked"})
    public static Map<String, Object> toMap(Object o) {
        if (o instanceof Map) {
            return (Map<String, Object>) o;
        } else {
            if (o != null) {
                return JsonUtil.toMap(o);
            }
            return null;
        }
    }

    @SuppressWarnings({"unchecked"})
    public static Map<String, Object> cascadeToMap(Object o) {
        if (o instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) o;
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                entry.setValue(convert(entry.getValue()));
            }
            return map;
        } else {
            if (o != null) {
                return JsonUtil.toMap(o);
            }
            return null;
        }
    }

    private static Object convert(Object item) {
        if (item instanceof Collection) {
            List<Object> list = new ArrayList<>();
            for (Object i : (Collection<?>) item) {
                list.add(convert(i));
            }
            return list;
        } else if (item instanceof Object[]) {
            List<Object> list = new ArrayList<>();
            for (Object i : (Object[]) item) {
                list.add(convert(i));
            }
            return list.toArray();
        } else if (MapUtil.isMap(item)) {
            return cascadeToMap(item);
        } else {
            return item;
        }
    }

    public static Map<String, Object> clone(Map<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return map;
        }
        Map<String, Object> newMap = new HashMap<>(map.size());
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            newMap.put(entry.getKey(), clone(entry.getValue()));
        }
        return newMap;
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private static Object clone(Object obj) {
        if (obj instanceof Map) {
            Map newMap = new HashMap(((Map<?, ?>) obj).size());
            ((Map) obj).forEach((k, v) -> newMap.put(k, clone(v)));
            return newMap;
        } else if (obj instanceof Collection) {
            return ((Collection) obj).stream().map(MapUtil::clone).collect(Collectors.toList());
        } else {
            return obj;
        }
    }

    @SuppressWarnings("rawtypes")
    public static <T> T getValue(String key, Map map, Class<T> clazz, boolean ignoreException) {
        if (map != null) {
            Object v = map.get(key);
            if (v != null) {
                if (ignoreException) {
                    try {
                        return JsonUtil.convert(v, clazz);
                    } catch (Exception e) {
                        log.warn(e.getMessage(), e);
                        return null;
                    }
                }
                return JsonUtil.convert(v, clazz);
            }
        }
        return null;
    }

    public static Map<String, Object> of(String k1, Object v1) {
        Map<String, Object> map = new HashMap<>(2);
        map.put(k1, v1);
        return map;
    }

    public static Map<String, String> ofStr(String k1, String v1) {
        Map<String, String> map = new HashMap<>(2);
        map.put(k1, v1);
        return map;
    }

    public static Map<String, Object> of(String k1, Object v1, String k2, Object v2) {
        Map<String, Object> map = new HashMap<>(capacityFor(2));
        map.put(k1, v1);
        map.put(k2, v2);
        return map;
    }

    public static Map<String, Object> of(String k1, Object v1, String k2, Object v2, String k3, Object v3) {
        Map<String, Object> map = new HashMap<>(capacityFor(3));
        map.put(k1, v1);
        map.put(k2, v2);
        map.put(k3, v3);
        return map;
    }

    public static Map<String, Object> toPagingMap(Paging<Map<String, Object>> paging) {
        return of("total", paging.getTotal(), "data", paging.getData());
    }

    private static int capacityFor(int size) {
        // 计算所需容量 = (size / 负载因子) + 1
        int capacity = (int) (size / 0.75f) + 1;

        // HashMap 的容量必须是 2 的幂次方，向上取整
        int n = capacity - 1;
        n |= n >>> 1;
        n |= n >>> 2;
        n |= n >>> 4;
        n |= n >>> 8;
        n |= n >>> 16;
        return (n < 0) ? 1 : (n >= (1 << 30)) ? (1 << 30) : n + 1;
    }
}
