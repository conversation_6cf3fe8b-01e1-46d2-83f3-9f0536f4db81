package io.terminus.trantor2.common.user;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import org.springframework.data.domain.AuditorAware;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/3/27 9:46 上午
 */
@Component
public class AuditorAwareImpl implements AuditorAware<Long> {

    @Override
    public Optional<Long> getCurrentAuditor() {
        Optional<User> userOptional = TrantorContext.safeGetCurrentUser();
        if (userOptional.isPresent()) {
            return userOptional.map(User::getId);
        }

        return Optional.empty();
    }

}
