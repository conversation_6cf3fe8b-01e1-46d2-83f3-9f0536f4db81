package io.terminus.trantor2.common.storage.jpa;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;
import lombok.experimental.SuperBuilder;

import jakarta.persistence.MappedSuperclass;

/**
 * 元数据实体基类
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
@MappedSuperclass
@SuperBuilder
@Getter
@Setter
@NoArgsConstructor
@FieldNameConstants
public class TeamBaseMetaEntity extends BaseMetaEntity {
    private static final long serialVersionUID = 8152298350157162848L;
    /**
     * 所属团队ID
     */
    protected Long teamId;

}
