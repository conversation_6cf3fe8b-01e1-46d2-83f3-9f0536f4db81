package io.terminus.trantor2.common.feature;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import io.terminus.trantor2.common.utils.VersionUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;

import jakarta.annotation.Nonnull;
import java.io.IOException;
import java.io.Serializable;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@JsonSerialize(using = VersionRange.Serializer.class)
@JsonDeserialize(using = VersionRange.Deserializer.class)
@NoArgsConstructor
@AllArgsConstructor
public class VersionRange implements Serializable {
    private static final long serialVersionUID = 5102036051019452322L;

    private ComparableVersion start;
    private ComparableVersion until;

    public static VersionRange ofStrings(@Nullable String start, @Nullable String until) {
        return new VersionRange(
                start != null ? ComparableVersion.of(start) : null,
                until != null ? ComparableVersion.of(until) : null
        );
    }

    public static VersionRange ofComparableVersions(@Nullable ComparableVersion start, @Nullable ComparableVersion until) {
        String s = start != null ? start.getVersion() : null;
        String u = until != null ? until.getVersion() : null;
        return ofStrings(s, u);
    }

    public boolean containVersion(@Nonnull String version) {
        return VersionUtil.versionInRange(version,
                Optional.ofNullable(start).map(ComparableVersion::getVersion).orElse(null),
                Optional.ofNullable(until).map(ComparableVersion::getVersion).orElse(null));
    }

    /**
     * 判断是否包含某个版本区间
     */
    public boolean includeRange(VersionRange that) {
        if (this.getStart() == null && this.getUntil() == null) {
            return true;
        }
        if (that == null || (that.getStart() == null && that.getUntil() == null)) {
            return false;
        }
        if (this.getStart() != null
                && (that.getStart() == null || this.getStart().compareTo(that.getStart()) > 0)) {
            return false;
        }
        return this.getUntil() == null || (that.getUntil() != null && this.getUntil().compareTo(that.getUntil()) >= 0);
    }

    public static class Serializer extends StdSerializer<VersionRange> {
        private static final long serialVersionUID = 1415827008470594322L;

        public Serializer() {
            super(VersionRange.class);
        }

        @Override
        public void serialize(VersionRange value, JsonGenerator gen, SerializerProvider provider) throws IOException {
            if (value.getStart() == null && value.getUntil() == null) {
                gen.writeNull();
            } else {
                gen.writeStartObject();
                if (value.getStart() != null) {
                    gen.writeStringField("start", value.getStart().toString());
                }
                if (value.getUntil() != null) {
                    gen.writeStringField("until", value.getUntil().toString());
                }
                gen.writeEndObject();
            }
        }
    }

    public static class Deserializer extends StdDeserializer<VersionRange> {
        private static final long serialVersionUID = -7954794341134093350L;

        public Deserializer() {
            super(VersionRange.class);
        }

        @Override
        public VersionRange deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            VersionRange versionRange = new VersionRange();
            while (p.currentToken() != JsonToken.END_OBJECT && p.nextToken() != JsonToken.END_OBJECT) {
                String fieldName = p.getCurrentName();
                p.nextToken();
                if ("start".equals(fieldName)) {
                    String startValue = p.getValueAsString();
                    if (StringUtils.isNotBlank(startValue)) {
                        versionRange.setStart(new ComparableVersion(startValue));
                    }
                } else if ("until".equals(fieldName)) {
                    String untilValue = p.getValueAsString();
                    if (StringUtils.isNotBlank(untilValue)) {
                        versionRange.setUntil(new ComparableVersion(untilValue));
                    }
                }
            }
            return versionRange;
        }
    }
}
