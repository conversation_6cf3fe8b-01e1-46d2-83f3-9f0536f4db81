package io.terminus.trantor2.common.redis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Primary
@Component
@ConfigurationProperties(prefix = "spring.redis")
public class TrantorRedisProperties extends RedisProperties {
    @Override
    public Sentinel getSentinel() {
        Sentinel sentinel = super.getSentinel();
        if (sentinel == null) {
            return null;
        }
        if (ObjectUtils.isEmpty(sentinel.getMaster()) && CollectionUtils.isEmpty(sentinel.getNodes())) {
            log.warn("redis sentinel config is empty, automatically converted to singleton mode");
            return null;
        }
        return sentinel;
    }

    @Override
    public Cluster getCluster() {
        Cluster cluster = super.getCluster();
        if (cluster == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(cluster.getNodes())) {
            log.warn("redis cluster config is empty, automatically converted to singleton mode");
            return null;
        }
        return cluster;
    }
}
