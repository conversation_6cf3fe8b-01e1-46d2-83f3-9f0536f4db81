package io.terminus.trantor2.common.exception;

import static io.terminus.trantor2.common.exception.ErrorType.SERVER_ERROR;

/**
 * <AUTHOR>
 */
public class ServerErrorException extends TrantorBizException {
    private static final long serialVersionUID = 6818051802870708049L;

    public ServerErrorException(String message, String innerMsg) {
        super(SERVER_ERROR, message, innerMsg);
    }

    public ServerErrorException(String innerMsg) {
        super(SERVER_ERROR, SERVER_ERROR.getMessage(), innerMsg);
    }
}
