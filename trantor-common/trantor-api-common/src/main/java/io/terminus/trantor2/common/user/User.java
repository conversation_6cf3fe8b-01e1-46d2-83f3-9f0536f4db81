package io.terminus.trantor2.common.user;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by hedy on 2023/3/9.
 */
@Data
public class User implements Serializable {
    private static final long serialVersionUID = -8184031735517443231L;
    public static final String UNKNOWN_USERNAME = "unknown";

    /**
     * 用户唯一标识符
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户状态
     */
    private Boolean status;

    /**
     * 用户头像
     */
    private String avatar;
}
