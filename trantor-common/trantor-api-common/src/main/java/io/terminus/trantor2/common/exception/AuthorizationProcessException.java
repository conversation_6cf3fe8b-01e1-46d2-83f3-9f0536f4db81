package io.terminus.trantor2.common.exception;

/**
 * 授权处理异常
 *
 * <AUTHOR>
 * 2022/11/18 5:18 下午
 **/
public class AuthorizationProcessException extends TrantorRuntimeException {

    public AuthorizationProcessException() {
    }

    public AuthorizationProcessException(String message) {
        super(message);
    }

    public AuthorizationProcessException(String message, Throwable cause) {
        super(message, cause);
    }

    public AuthorizationProcessException(Throwable cause) {
        super(cause);
    }

    public AuthorizationProcessException(String message,
                                         Throwable cause,
                                         boolean enableSuppression,
                                         boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
