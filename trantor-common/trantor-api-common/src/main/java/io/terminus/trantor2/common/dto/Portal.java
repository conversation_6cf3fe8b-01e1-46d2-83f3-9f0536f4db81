package io.terminus.trantor2.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * 2022/11/9 4:12 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Portal {
    private Long id;
    private Long teamId;
    private String teamCode;
    private String code;
    private String name;
    private String path;
    private String logoUrl;
    private Long iamEndpointId;
    private String loginUrl;
    private String loginCallbackUrl;
    private ObtainType obtainType;
    private LicenseConfig license;
    private PortalI18nConfig i18nConfig;

    private String applicationKey;
    @JsonIgnore
    private String accessKey;
    @JsonIgnore
    private String accessSecret;

    public enum ObtainType {
        urlPath,
        refererPath,
        refererHost
    }
}
