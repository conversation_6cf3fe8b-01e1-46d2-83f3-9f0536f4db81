package io.terminus.trantor2.properties;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.SpecVersion;
import io.swagger.v3.oas.models.info.Info;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties("trantor2.api-docs")
public class ApiDocsProperties {

    private String title;

    private String version;

    /**
     * Swagger 文档出入参展示层级深度
     */
    private Integer depth;

    /**
     * OpenAPI 版本
     */
    private SpecVersion openApiVersion = SpecVersion.V31;

    /**
     * 自定义 Swagger OpenAPI 配置
     *
     * @return OpenAPI 配置
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI().specVersion(openApiVersion).info(new Info().title(title).version(version));
    }
}
