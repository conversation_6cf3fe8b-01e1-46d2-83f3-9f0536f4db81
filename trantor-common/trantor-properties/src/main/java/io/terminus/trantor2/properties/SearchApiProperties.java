package io.terminus.trantor2.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

@ConfigurationProperties("trantor2.search-api")
@Data
public class SearchApiProperties {
    /**
     * 搜索api es配置
     */
    @NestedConfigurationProperty
    private ElasticsearchProperties elasticsearch;

    @Data
    public static class ElasticsearchProperties {
        /**
         * es host
         */
        private String url;
        /**
         * es username
         */
        private String username;
        /**
         * es password
         */
        private String password;
        /**
         * es high level client http client
         */
        private HttpConfig http = new HttpConfig();

        @Data
        public static class HttpConfig {
            /**
             * 读取的超时时间，单位ms
             */
            private int socketTimeout = 30000;
            /**
             * 连接超时时间,单位ms
             */
            private int connectTimeout = 1000;
            /**
             * 从连接池获取连接最大等待时间，单位ms
             */
            private int connectionRequestTimeout = 1000;
            /**
             * 连接保活时间，单位ms
             */
            private int maxKeepaliveTime = 300000;
            /**
             * 整个连接管理器的最大连接数
             */
            private int maxConnections = 64;
            /**
             * 每个目标主机的最大连接数
             */
            private int maxConnectionsPerRoute = 64;
        }

    }
}
