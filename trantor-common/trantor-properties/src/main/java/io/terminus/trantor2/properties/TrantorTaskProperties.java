package io.terminus.trantor2.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "trantor2.task")
public class TrantorTaskProperties {

    private Map<String, String> securityCodes = new HashMap<>();

    private Migration migration = new Migration();

    private AutoSnapshot autoSnapshot = new AutoSnapshot();

    private AutoGc autoGc = new AutoGc();

    @Data
    public static class Migration {
        private SimpleJob simpleJob = new SimpleJob();

        @Data
        public static class SimpleJob {
            private Boolean enabled = Boolean.FALSE;
        }
    }

    @Data
    public static class AutoSnapshot {
        private Boolean enabled = Boolean.FALSE;
    }

    @Data
    public static class AutoGc {
        private Boolean enabled = Boolean.FALSE;
    }
}
