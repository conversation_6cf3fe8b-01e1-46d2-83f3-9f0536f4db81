package io.terminus.trantor2.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.lang.NonNull;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;

@ConfigurationProperties("trantor2.search")
@Data
public class SearchDumpProperties {

    /**
     * 全量过程中失败的数据比例达到多少停止全量同步任务
     */
    private BigDecimal fullSyncModelDataFailureRate;

    /**
     * 全量同步时检测锁的心跳时间,单位ms，默认5秒
     */
    private Integer heartbeatInterval;

    /**
     * 模型发布锁超时时间,单位分钟，默认600分钟
     */
    private Integer lockTimeout;

    /**
     * 增量消费到需要删除的数据时等待的ms数，默认是等待5ms，当需要关闭该功能时，将该值配置为0
     */
    private int sleepTimeWhenConsumerDataToDelete;

    /**
     * 搜索模型全量同步任务线程池配置
     */
    @NestedConfigurationProperty
    private FullTaskWorkerProperties fullTaskWorker;

    /**
     * 搜索模型物理表数据查询任务线程池配置
     */
    @NestedConfigurationProperty
    private PhysicalTableReindexWorkerProperties physicalTableReindexWorker;

    /**
     * es配置
     */
    @NestedConfigurationProperty
    private ElasticSearchProperties elasticSearch;


    public String getUrl() {
        return elasticSearch.getHost() + ":" + elasticSearch.getPort();
    }

    /**
     * cdc组件类型
     * 系统级别cdc组件类型，mq消费时会对不同cdc组件做适配
     * 默认为CANAL
     */
    private String cdcType;

    /**
     * 系统默认topic，如果不配置独立topic，则所有搜索模型（可能存储在不同的数据库中）共用一个topic
     */
    private String systemTopic;

    /**
     * 使用独立Topic的搜索模型,dump使用
     * 格式为：topic1[so1:20/so2:20];topic2[so3:20/so4:20]
     * 多个间使用英文逗号分割
     */
    private String exclusiveTopics;

    /**
     * es bulk操作时，是否需要进行乐观锁控制，默认是true
     */
    private boolean needOptimistiLock;

    /**
     * 对接canal admin相关配置
     */
    @NestedConfigurationProperty
    private CanalAdminProperties canalAdmin;

    /**
     * 数据库类型，默认Mysql
     */
    private String defaultDialectType;

    /**
     * 测试用例专用，是否测试模式，默认值是false，不需要对外暴露配置项，测试用例中手动设置该参数值
     */
    private boolean searchUnitTest = false;

    /**
     * pub/sub时是否需要开启事务，publish消息同缓存版本号同处一个事务中
     * 默认是true开启
     *
     * @return true：是；false：否
     */
    private boolean redisPubSubNeedTransaction;

    /**
     * httpClient相关配置
     */
    @NestedConfigurationProperty
    private HttpClientProperties httpClient;

    /**
     * mq subscribe相关配置
     */
    @NestedConfigurationProperty
    private MqSubscribeProperties mqSubscribe;

    /**
     * ons版本
     * 由于ons4.x的api 无法 访问ons 5.x实例信息，所以针对5.x做特殊处理
     * 默认配置为4
     */
    public Integer onsVersion;

    @Data
    public static class FullTaskWorkerProperties {
        /**
         * 全量任务核心线程数
         */
        private Integer poolCoreSize;

        /**
         * 全量任务最大线程数
         */
        private Integer poolMaxSize;

        /**
         * 全量任务队列大小
         */
        private Integer poolQueueSize;

        /**
         * 全量任务步长
         */
        private Integer dataPageQueryStep;
    }

    @Data
    public static class PhysicalTableReindexWorkerProperties {
        /**
         * 核心线程数
         */
        private Integer poolCoreSize;

        /**
         * 最大线程数
         */
        private Integer poolMaxSize;

        /**
         * 队列大小
         */
        private Integer poolQueueSize;
    }

    @Data
    public static class ElasticSearchProperties {

        private String host;

        private Integer port;

        private Integer tcpPort;

        private String name;

        private String secret;

        private String clusterName;

        private String authorizationBase64;

        public String getAuthorizationBase64() {
            if (StringUtils.hasLength(authorizationBase64)) {
                return authorizationBase64;
            }
            if (StringUtils.hasLength(name) && StringUtils.hasLength(secret)) {
                String authorization = name + ":" + secret;
                authorizationBase64 = "Basic " + Base64.getEncoder().encodeToString(authorization.getBytes(StandardCharsets.UTF_8));
                return authorizationBase64;
            }
            return null;
        }

    }

    @Data
    public static class CanalAdminProperties {
        private String zkHosts;
        private String canalHost;
        private Integer canalPort;
        private Long canalAdminPort;
        private int canalReplicas;
        private String canalName;
        private String adminUrl;
        private String userName;
        private String password;
        private String serverIp;
        private String loginUrl;
        private String clusterQueryUrl;
        private String clusterAddUrl;
        private String serverQueryUrl;
        private String serverDeleteUrl;
        private String serverAddUrl;
        private String serverConfigQueryUrl;
        private String serverConfigModifyUrl;
        private String instanceConfigQueryUrl;
        private String instanceConfigAddUrl;
        private String instanceConfigQueryOneUrl;
    }

    @Data
    public static class HttpClientProperties {
        /**
         * 连接超时时间,单位ms
         */
        private Integer connectionTimeOut;

        /**
         * 读取的超时时间，单位ms
         */
        private Integer socketTimeOut;

        /**
         * 从连接池获取连接最大等待时间，单位ms
         */
        private Integer connectionRequestTimeOut;

        /**
         * 空闲连接最大空闲时间，单位ms，默认10000ms
         */
        private Integer connectionIdleTimeOut;

        /**
         * 获取到连接后，检查连接是否有效的时间间隔，连接创建时间+该参数值如果小于当前时间，则进行连接可用性校验
         * 使用系统默认配置2000，单位ms
         */
        private Integer validateAfterInactivity;

        /**
         * 是否强制每次都进行连接有效性校验，默认是false
         * 该参数官方已废弃，建议使用ValidateAfterInactivity
         */
        private boolean staleConnectionCheckEnabled;

        /**
         * 整个连接管理器的最大连接数
         */
        private Integer maxTotal;

        /**
         * 每个目标主机的最大连接数
         */
        private Integer maxPerRoute;

        /**
         * 是否开启重试
         */
        private boolean retryEnable;

        /**
         * 重试次数
         */
        private Integer retryCount;
    }

    @Data
    public static class MqSubscribeProperties {
        /**
         * ONS地域配置,使用ONS时必填
         */
        private String regionId;
        /**
         * ONS实例名,使用ONS时必填
         */
        private String onsInstanceId;

        /**
         * ONS接入点,使用ONS时必填
         */
        private String mqSendOnsEndpoint;

        /**
         * 普通消息最大重试次数
         */
        private int maxReconsumeTimes;

        /**
         * 消费线程数
         */
        private int consumeThreadNums;

        /**
         * BatchConsumer每次批量消费的最大消息数量, 默认值为1, 允许自定义范围为[1, 32], 实际消费数量可能小于该值.
         */
        public int consumeMessageBatchMaxSize;
    }

    /**
     * 获取使用独立Topic的搜索模型
     * key=topic名， value=模型名:消费线程数
     */
    public Map<String, Set<String>> getExclusiveTopicSearchModels() {
        Map<String, Set<String>> exclusiveSearchModelMap = new HashMap<>();
        if (!StringUtils.hasLength(exclusiveTopics)) {
            return exclusiveSearchModelMap;
        }

        // 使用独立topic的搜索模型配置解析
        parseMqSendExclusiveTopic(exclusiveSearchModelMap, exclusiveTopics);

        return exclusiveSearchModelMap;
    }

    /**
     * 使用独立topic模型的配置解析
     *
     * @param exclusiveTopicModelsCache
     * @param exclusiveTopics
     */
    private void parseMqSendExclusiveTopic(@NonNull Map<String, Set<String>> exclusiveTopicModelsCache,
                                           @NonNull String exclusiveTopics) {
        exclusiveTopics = exclusiveTopics.trim();
        String[] topicPartitionArray = exclusiveTopics.split(";");
        for (String topicPartitionInfo : topicPartitionArray) {
            if (!StringUtils.hasLength(topicPartitionInfo)) {
                continue;
            }
            String topicName = topicPartitionInfo.substring(0, topicPartitionInfo.indexOf("[")).trim();
            String models = topicPartitionInfo.substring(topicPartitionInfo.indexOf("[") + 1, topicPartitionInfo.indexOf("]")).trim();
            String[] modelArray = models.split("/");
            for (String modelAlias : modelArray) {
                if (exclusiveTopicModelsCache.containsKey(topicName)) {
                    exclusiveTopicModelsCache.get(topicName).add(modelAlias);
                } else {
                    exclusiveTopicModelsCache.put(topicName, new HashSet<String>() {{
                        add(modelAlias);
                    }});
                }
            }
        }
    }
}
