package io.terminus.trantor2.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.core.Ordered;
import org.springframework.util.CollectionUtils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * trantor2 配置
 */
@Getter
@Setter
@ConfigurationProperties(prefix = "trantor2.web")
public class TrantorFilterProperties {
    @NestedConfigurationProperty
    private List<FilterConfig> filters;

    @Setter
    @Getter
    public static class FilterConfig {
        /**
         * filter 全类名
         */
        private String filter;
        private int order;
        private List<String> urlPatterns;
        private Map<String, String> initParameters = new LinkedHashMap<>();

        /**
         * 非负数时实际 order += {@link Ordered#HIGHEST_PRECEDENCE}
         * 负数时实际 order += {@link Ordered#LOWEST_PRECEDENCE}
         */
        public int getOrder() {
            return order < 0 ? Ordered.LOWEST_PRECEDENCE + order : Ordered.HIGHEST_PRECEDENCE + order;
        }
    }

    public Optional<FilterConfig> getFilter(Class<?> clazz) {
        if (CollectionUtils.isEmpty(filters)) {
            return Optional.empty();
        }
        return filters.stream().filter(it -> it.getFilter().equals(clazz.getName())).findFirst();
    }

    public FilterConfig findFilter(Class<?> clazz) {
        return getFilter(clazz).orElseThrow(() -> new RuntimeException(clazz.getName() + " not found"));
    }
}
