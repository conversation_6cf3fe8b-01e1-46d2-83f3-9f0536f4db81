package io.terminus.trantor2.properties.runtime;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties("trantor2.model.runtime")
public class ModelRuntimeProperties {

    /**
     * 新增数据时的 id 默认从取号中心获取。增加配置可以不依赖取号中心(middleware), 本地生成 id
     */
    private boolean localId;

    /**
     * 是否启动时根据 model 元数据生成加载 entity & repo class
     *
     * <p>
     * 运行时动态根据模型元数据生成加载 entity & repo class 速度较慢(约 300ms 左右)，为了提升执行效率，在应用启动时异步预热
     * </p>
     */
    private boolean loadClassOnStartup;

    /**
     * 子查询白名单，分页查询数据时，数据库表的数据量大时(100w+)，当 offset 值过大(100w)会导致全表扫描，查询超时。为了解决此问题，将查询语句优化成子查询
     */
    private String subQueryWhitelist;

    private String encryptKey;
}
