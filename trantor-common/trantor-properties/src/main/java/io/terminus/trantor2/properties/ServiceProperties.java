package io.terminus.trantor2.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * 服务引擎相关的配置
 *
 * <AUTHOR> Created on 2024/4/9 11:38
 */
@ConfigurationProperties("trantor2.service")
@Data
public class ServiceProperties {

    /**
     * 服务引擎的名称，默认是service-engine
     */
    private String engineName = "service-engine";

    /**
     * 是否启用缓存
     */
    private boolean loaderCacheEnabled = true;

    /**
     * 多组织是否启用，默认启用
     */
    private boolean originOrgEnabled = true;

    /**
     * 多组织的模块key，默认是ERP_GEN
     */
    private String originOrgModuleKey = "ERP_GEN";

    /**
     * 审计日志是否启用，默认启用
     */
    private boolean auditingLogEnabled = true;

    /**
     * 编排服务是否自动ai总结，默认不自动
     */
    private boolean aiSummaryAuto = false;

    /**
     * 新状态的开关，临时兼容
     */
    private boolean newStatusEnabled = true;

    /**
     * 是否验证sql
     */
    private boolean checkSqlEnabled = true;

    /**
     * 是否必须校验sql表是否跨模块，默认必须校验
     */
    private boolean checkSqlTableCrossModule = true;

    /**
     * 服务编排的相关配置
     */
    private ProgrammableConfig programmableConfig = new ProgrammableConfig();

    /**
     * 服务的异步配置
     */
    private Pool asyncPool = new Pool();

    /**
     * 关闭事件创建,配置的模块不允许在创建事件
     */
    private List<String> closeEventCreateModules;

    /**
     * 关闭系统服务来自前端的调用
     */
    private boolean systemServiceCallFromFrontClosed;

    /**
     * 关闭系统服务来自前端的调用的黑名单，按照项目@模块来配置，如：ERP@*，ERP@moduleKey
     */
    private List<String> systemServiceCallFromFrontClosedBlacklist;

    /**
     * 服务允许上传的文件类型
     */
    private List<String> allowedUploadFiles;

    @Data
    public static class Pool {
        /**
         * 线程池的核心线程数
         */
        private int coreSize = 2;
        /**
         * 线程池的最大线程数
         */
        private int maxSize = 20;
        /**
         * 线程池的队列容量
         */
        private int queueCapacity = 1000;

        /**
         * 线程在关闭前的存活时间，单位秒
         */
        private int keepAlive = 60;

        /**
         * 允许核心线程超时
         */
        private boolean allowCoreThreadTimeout = true;

        /**
         * 线程池的线程名称前缀
         */
        private String threadNamePrefix = "service-async";

    }

    @Data
    public static class ProgrammableConfig {

        /**
         * HTTP节点，执行时默认传递的请求头
         *
         * @deprecated 配置中不在支持设置默认传递的请求头，已经移到http节点的配置中
         */
        @Deprecated
        private List<String> httpNodeDefaultHeaders;

        /**
         * 扩展的系统变量
         */
        private ExtSystemVariable extSystemVariable = new ExtSystemVariable();
    }

    @Data
    public static class ExtSystemVariable {
        /**
         * Http的header的keys，这里配置的key在服务编排的系统变量中可以被选择
         */
        private List<String> httpHeaders;
    }
}
