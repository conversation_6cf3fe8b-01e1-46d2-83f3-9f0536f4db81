{"access": "Private", "key": "test$service1", "name": "Service Example 1", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "k1", "name": null, "props": {"conditionGroup": null, "dataConditionPermissionKey": "test$p_d_1", "dataType": "MODEL", "desensitized": true, "dynamicCondition": null, "maximum": null, "outputAssign": null, "pageable": null, "queryModelFields": null, "relatedModel": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "type": "RetrieveDataNode"}], "desc": null, "id": null, "input": [], "key": "test$service1", "name": null, "output": [], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "test$p_f_1", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}