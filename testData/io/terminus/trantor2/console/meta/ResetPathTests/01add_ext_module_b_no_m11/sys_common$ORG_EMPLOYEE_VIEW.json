{"type": "Scene", "key": "sys_common$ORG_EMPLOYEE_VIEW", "name": "员工管理", "description": null, "props": {"type": "DATA", "sceneConfig": {"type": "DATA", "modelConfig": {"modelKey": "sys_common$org_employee_md", "relations": {"LINK": [{"modelKey": "sys_common$user", "relations": {"LINK": []}}], "PARENT_CHILD": [{"modelKey": "sys_common$org_employee_org_link_cf", "relations": {"LINK": [{"modelKey": "sys_common$org_struct_md", "relations": {"LINK": [{"modelKey": "sys_common$org_business_type_cf", "relations": {}}, {"modelKey": "sys_common$user", "relations": {}}, {"modelKey": "sys_common$org_struct_md", "relations": {}}, {"modelKey": "sys_common$org_dimension_cf", "relations": {}}]}}, {"modelKey": "sys_common$user", "relations": {"LINK": []}}, {"modelKey": "sys_common$org_employee_md", "relations": {"LINK": [{"modelKey": "sys_common$user", "relations": {}}]}}, {"modelKey": "sys_common$org_identity_cf", "relations": {"LINK": [{"modelKey": "sys_common$user", "relations": {}}]}}]}}]}}, "templateConfig": {"local": true, "version": "1.0.0-SNAPSHOT", "templateKey": "master_data", "templateType": "MASTER_DATA"}, "usedModelAlias": ["sys_common$org_identity_cf", "sys_common$org_employee_md", "sys_common$org_employee_org_link_cf", "sys_common$user", "sys_common$org_struct_md"]}, "endpointType": "PC"}, "parentKey": "sys_common$ungroup", "access": null}