{"access": "Private", "key": "sys_common$ORG_EMPLOYEE_VIEW:list", "name": "list", "props": {"containerSelect": {"sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail": [{"field": "code", "selectFields": null}, {"field": "name", "selectFields": null}, {"field": "status", "selectFields": null}, {"field": "type", "selectFields": null}, {"field": "email", "selectFields": null}, {"field": "mobile", "selectFields": null}, {"field": "addressDetail", "selectFields": null}, {"field": "entryAt", "selectFields": null}, {"field": "resignationAt", "selectFields": null}, {"field": "idCard", "selectFields": null}, {"field": "userId", "selectFields": [{"field": "id", "selectFields": null, "sortOrders": null}, {"field": "username", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"field": "addressId", "selectFields": null}, {"field": "employeeOrgLinkList", "selectFields": [{"field": "isMainOrg", "selectFields": null, "sortOrders": null}, {"field": "identityId", "selectFields": [{"field": "id", "selectFields": null, "sortOrders": null}, {"field": "name", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"field": "orgUnitId", "selectFields": [{"field": "id", "selectFields": null, "sortOrders": null}, {"field": "orgName", "selectFields": null, "sortOrders": null}], "sortOrders": null}], "sortOrders": null}, {"field": "created<PERSON>y", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "updatedBy", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "createdAt", "selectFields": null}, {"field": "updatedAt", "selectFields": null}], "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form": [{"field": "code", "selectFields": null}, {"field": "name", "selectFields": null}, {"field": "type", "selectFields": null}, {"field": "email", "selectFields": null}, {"field": "mobile", "selectFields": null}, {"field": "addressDetail", "selectFields": null}, {"field": "entryAt", "selectFields": null}, {"field": "resignationAt", "selectFields": null}, {"field": "idCard", "selectFields": null}, {"field": "userId", "selectFields": [{"field": "id", "selectFields": null, "sortOrders": null}, {"field": "username", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"field": "addressId", "selectFields": null}, {"field": "status", "selectFields": null}, {"field": "version", "selectFields": null}, {"field": "employeeOrgLinkList", "selectFields": [{"field": "isMainOrg", "selectFields": null, "sortOrders": null}, {"field": "identityId", "selectFields": [{"field": "id", "selectFields": null, "sortOrders": null}, {"field": "name", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"field": "orgUnitId", "selectFields": [{"field": "id", "selectFields": null, "sortOrders": null}, {"field": "orgName", "selectFields": null, "sortOrders": null}], "sortOrders": null}], "sortOrders": null}], "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md": [{"field": "code", "selectFields": null}, {"field": "name", "selectFields": null}]}, "content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-new", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "新建", "type": "primary"}, "type": "Widget"}, {"children": [], "extProps": {"removed": true}, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch", "name": "DropdownButton", "props": {"combinationMode": true, "disabled$": "$context.selectedKeys?.length === 0 || $context.route?.action === \"new\" || $context.route?.action === \"edit\"", "items": [{"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "sys_common$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md"}, {"action": "PageJump", "target": "list"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-delete", "label": "批量删除"}, {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认统一启用吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "sys_common$SYS_MasterData_MultiEnableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ["sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md"]}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-start", "label": "批量启用"}, {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认统一停用吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "sys_common$SYS_MasterData_MultiDisableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md"}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-stop", "label": "批量停用"}], "label": "批量操作"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "name": "ImportButton", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "downloadServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download"}}, "isCustomServiceProps": {"isCustomFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "type": "InvokeService"}}, "label": "导入", "predictServiceProps": {"predictFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "type": "InvokeService"}}, "saveServiceProps": {"saveServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "type": "InvokeService"}, "saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-direct-by-oss"}}, "saveSubServiceProps": {"saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-sub-model"}, "saveSubServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "type": "InvokeService"}}}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "name": "ExportButton", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "exportButtonServiceProps": {"saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "label": "导出"}, "type": "Widget"}, {"children": [], "ext": true, "extProps": {"appended": true}, "key": "sys_common$ORG_EMPLOYEE_VIEW-kvFTbRyPRuWhc0sg4AJO7", "name": "<PERSON><PERSON>", "props": {"actionConfig": {}, "buttonType": "default", "confirmOn": "off", "label": "按钮", "permissionKey": "sys_common$ORG_EMPLOYEE_VIEW-kvFTbRyPRuWhc0sg4AJO7_perm_ac", "showCondition": {}, "type": "default"}, "type": "Widget"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions", "name": "BatchActions", "props": {}}, {"children": [{"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-logs", "name": "Logs", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-lF1QOZEGtkaCHK1RzD8Vw", "name": "Field", "props": {"componentProps": {"fieldAlias": "code", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "员工编码", "name": "code", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-NdUI184yh2s4lcc6w4sEg", "name": "Field", "props": {"componentProps": {"fieldAlias": "name", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "姓名", "name": "name", "type": "TEXT", "width": 146}, "type": "Widget"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-T0dxNmXpp6q8fg04HXRzp", "name": "Fields", "props": {}, "type": "Meta"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "name": "Table", "props": {"allowRowSelect": true, "filterFields": [{"componentProps": {"fieldAlias": "code", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "员工编码", "name": "code", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "name", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "姓名", "name": "name", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "type", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": false, "label": "员工类型", "name": "type", "type": "SELECT", "width": 116}, {"componentProps": {"fieldAlias": "email", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "邮箱", "name": "email", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "mobile", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "手机", "name": "mobile", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "addressDetail", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "详细地址", "name": "addressDetail", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "entryAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": false, "label": "入职日期", "name": "entryAt", "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "resignationAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": false, "label": "离职日期", "name": "resignationAt", "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "idCard", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "身份证号", "name": "idCard", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "userId", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "用户", "name": "userId", "type": "NUMBER", "width": 144}, {"componentProps": {"fieldAlias": "addressId", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "地址", "name": "addressId", "type": "NUMBER", "width": 144}], "flow": {"containerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "modelAlias": "sys_common$org_employee_md", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "mode": "simple", "modelAlias": "sys_common$org_employee_md", "onRowActionConfig": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "enable": true}, "pagination": {"size": "small"}, "serviceKey": "sys_common$SYS_PagingDataService"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-wPS2MJGuQRIvm1fPSsLOu", "name": "Status", "props": {"text$": "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},UNENABLED:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISENABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.text", "type$": "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},UNENABLED:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISENABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.type"}}], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{((data?.code || \"\") + (data?.name || \"\")) || \"员工信息表\t详情\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}, {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}], "service": "sys_common$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md"}, {"action": "Message", "message": "删除成功"}, {"action": "RefreshTab", "target": ["current"]}], "executeLogic": "BindService"}, "label": "删除", "showCondition": {"conditions": [{"conditions": [{"id": "sys_common$ORG_EMPLOYEE_VIEW-detail-shanchu-condition-id1", "leftValue": {"fieldType": "Enum", "scope": "form", "target": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md", "title": "status", "type": "VarValue", "val": "status", "value": "sys_common$org_employee_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "sys_common$ORG_EMPLOYEE_VIEW-detail-shanchu-condition-id2", "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}}}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "route.recordId", "name": "copyId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "label": "复制"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认停用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}, {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}], "service": "sys_common$SYS_MasterData_DisableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ["sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail"]}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "label": "停用", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "form", "title": "status", "type": "VarValue", "val": "status", "value": "sys_common$org_employee_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认启用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}, {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}], "service": "sys_common$SYS_MasterData_EnableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ["sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail"]}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "label": "启用", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "form", "title": "status", "type": "VarValue", "val": "status", "value": "sys_common$org_employee_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "label": "编辑", "showCondition": {"conditions": [{"conditions": [{"id": "sys_common$ORG_EMPLOYEE_VIEW-detail-bianji-condition-id1", "leftValue": {"fieldType": "Enum", "scope": "form", "target": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md", "title": "status", "type": "VarValue", "val": "status", "value": "sys_common$org_employee_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "sys_common$ORG_EMPLOYEE_VIEW-detail-bianji-condition-id2", "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detail-sys_common$org_employee_md-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-code", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "code", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "editable": false, "label": "员工编码", "name": "code", "type": "TEXT"}}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-name", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "name", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "editable": false, "label": "姓名", "name": "name", "type": "TEXT"}}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-status", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "status", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "editable": false, "label": "状态", "name": "status", "type": "SELECT"}}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-type", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "type", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "editable": false, "label": "员工类型", "name": "type", "type": "SELECT"}}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-email", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "email", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "editable": false, "label": "邮箱", "name": "email", "type": "TEXT"}}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-mobile", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "mobile", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "editable": false, "label": "手机", "name": "mobile", "type": "TEXT"}}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressDetail", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "addressDetail", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "editable": false, "label": "详细地址", "name": "addressDetail", "type": "TEXT"}}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-entryAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "entryAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "editable": false, "label": "入职日期", "name": "entryAt", "type": "DATE"}}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-resignationAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "resignationAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "editable": false, "label": "离职日期", "name": "resignationAt", "type": "DATE"}}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-idCard", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "idCard", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "editable": false, "label": "身份证号", "name": "idCard", "type": "TEXT"}}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-userId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "userId", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "editable": false, "label": "用户", "name": "userId", "type": "NUMBER"}}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressId", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "addressId", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "editable": false, "label": "地址", "name": "addressId", "type": "NUMBER"}}], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list-batch-actions", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-N2CK9jLOrkwXvk-aNk5FJ", "name": "Field", "props": {"componentProps": {"defaultValue": false, "fieldAlias": "isMainOrg", "modelAlias": "sys_common$org_employee_org_link_cf", "placeholder": "请选择"}, "hidden": false, "label": "是否主组织", "name": "isMainOrg", "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "name": "Field", "props": {"componentProps": {"columns": ["code", "name", "status"], "fieldAlias": "identityId", "label": "选择组织身份", "labelField": "name", "modelAlias": "sys_common$org_identity_cf", "parentModelAlias": "sys_common$org_employee_org_link_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": false, "label": "组织身份", "name": "identityId", "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "name": "Field", "props": {"componentProps": {"columns": ["orgCode", "orgName", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "orgSort", "orgBusinessTypeCode", "orgBusinessTypeId", "orgDimensionId", "orgParentId"], "fieldAlias": "orgUnitId", "label": "选择组织单元", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "sys_common$org_employee_org_link_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": false, "label": "组织单元", "name": "orgUnitId", "type": "OBJECT"}, "type": "Widget"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-LAxXu7V8oaf_BP5O6foRo", "name": "Fields", "props": {}, "type": "Meta"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list", "name": "Table", "props": {"fieldName": "employeeOrgLinkList", "flow": {"context$": "$context", "name": "employeeOrgLinkList", "type": "RelationData"}, "modelAlias": "sys_common$org_employee_org_link_cf", "serviceKey": "$context", "subTableEnabled": false}, "type": "Container"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-customDetailField", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "employeeOrgLinkList"}, "type": "Meta"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-gyeTOSkmC__Zr4767Pu1A", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-group", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-zR-xbPeH0dUQGisUgdnZD", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "组织单元"}, {"label": "系统信息"}], "underline": true}, "type": "Layout"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "name": "Detail", "props": {"flow": {"containerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "context$": "$context", "modelAlias": "sys_common$org_employee_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_employee_md", "onFinish$": "(values) => invokeSystemService(\"sys_common$SYS_MasterData_SaveDataService\", \"sys_common$org_employee_md\", {...data, ...values}).then(() => $(\"sys_common$ORG_EMPLOYEE_VIEW-detailView-page\").action(\"reload\"))", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "type": "Container"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showFooter": false, "showHeader": true}, "type": "Container"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" + ((data?.code || \"\") + (data?.name || \"员工信息表\t\")) : \"新建员工信息表\t\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code", "name": "FormField", "props": {"componentProps": {"fieldAlias": "code", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "员工编码", "name": "code", "rules": [{"message": "请输入员工编码", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name", "name": "FormField", "props": {"componentProps": {"fieldAlias": "name", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "姓名", "name": "name", "rules": [{"message": "请输入姓名", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type", "name": "FormField", "props": {"componentProps": {"fieldAlias": "type", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": false, "label": "员工类型", "name": "type", "rules": [{"message": "请输入员工类型", "required": true}], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-email", "name": "FormField", "props": {"componentProps": {"fieldAlias": "email", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "邮箱", "name": "email", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile", "name": "FormField", "props": {"componentProps": {"fieldAlias": "mobile", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "手机", "name": "mobile", "rules": [{"message": "请输入手机", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressDetail", "name": "FormField", "props": {"componentProps": {"fieldAlias": "addressDetail", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "详细地址", "name": "addressDetail", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-entryAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "entryAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": false, "label": "入职日期", "name": "entryAt", "rules": [], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-resignationAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "resignationAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": false, "label": "离职日期", "name": "resignationAt", "rules": [], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-idCard", "name": "FormField", "props": {"componentProps": {"fieldAlias": "idCard", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "身份证号", "name": "idCard", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "userId", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "modelAlias": "sys_common$user", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "用户名", "name": "username", "required": false, "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "username", "modelAlias": "sys_common$user", "placeholder": "请输入"}, "hidden": false, "label": "用户名", "name": "username", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "email", "modelAlias": "sys_common$user", "placeholder": "请输入"}, "hidden": false, "label": "用户邮箱", "name": "email", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "mobile", "modelAlias": "sys_common$user", "placeholder": "请输入"}, "hidden": false, "label": "用户手机", "name": "mobile", "required": false, "type": "TEXT", "width": 120}], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$user", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$user"}}], "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": ["username"], "modelAlias": "sys_common$user", "shape": "line", "showFilterFields": true, "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "用户", "name": "userId", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "addressId", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": false, "label": "地址", "name": "addressId", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-Mhe2IM7i9iyThkD26CsaC", "name": "FormField", "props": {"componentProps": {"fieldAlias": "status", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "editComponentType": "Select", "label": "状态", "name": "status", "required": true, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id", "name": "FormField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "name": "FormField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "name": "FormField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_employee_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "sys_common$org_employee_md", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-originOrgId", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "originOrgId", "modelAlias": "sys_common$org_employee_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "所属组织", "name": "originOrgId", "rules": [], "type": "NUMBER"}, "type": "Widget"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "name": "FormGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-batchActions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {}, "confirmOn": "off", "disabled$": "mode === \"design\" ? undefined : $context.selectedKeys?.length === 0", "label": "删除", "onClick$": "() => $context.batchRemove?.($context.selectedKeys || [])", "type": "primary"}, "type": "Widget"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-batchActions", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-action-deleteLine", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "SystemAction", "name": "remove"}]}, "label": "删除", "type": "text"}, "type": "Widget"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-reordActions", "name": "RecordActions", "props": {"width": 120}, "type": "Layout"}, {"children": [{"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-5JPZoDEYTJahgXeGmcG5P", "name": "Field", "props": {"componentProps": {"defaultValue": false, "fieldAlias": "isMainOrg", "modelAlias": "sys_common$org_employee_org_link_cf", "placeholder": "请选择"}, "hidden": false, "label": "是否主组织", "name": "isMainOrg", "rules": [], "type": "BOOL", "width": 116}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "name": "Field", "props": {"componentProps": {"columns": ["code", "name", "status"], "fieldAlias": "identityId", "label": "选择组织身份", "labelField": "name", "modelAlias": "sys_common$org_identity_cf", "parentModelAlias": "sys_common$org_employee_org_link_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": false, "label": "组织身份", "name": "identityId", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "name": "Field", "props": {"componentProps": {"columns": ["orgCode", "orgName", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "orgSort", "orgBusinessTypeCode", "orgBusinessTypeId", "orgDimensionId", "orgParentId"], "fieldAlias": "orgUnitId", "label": "选择组织单元", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "sys_common$org_employee_org_link_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": false, "label": "组织单元", "name": "orgUnitId", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-MoO66JROlIXfgdLgx0m1p", "name": "Fields", "props": {}, "type": "Meta"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform", "name": "TableForm", "props": {"fieldName": "employeeOrgLinkList", "hideDefaultDelete": true, "modelAlias": "sys_common$org_employee_org_link_cf", "subTableEnabled": false}}], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-form-field-employeeOrgLinkList", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "employeeOrgLinkList"}, "type": "Meta"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-JE2jf3T2tYith5FRLD6QN", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "组织单元"}], "lookup": [], "underline": true}, "type": "Layout"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "modelAlias": "sys_common$org_employee_md", "params$": "{ id: route.recordId }", "serviceKey": "sys_common$SYS_FindDataByIdService", "test$": "!!route.recordId", "type": "InvokeSystemService"}, {"containerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "modelAlias": "sys_common$org_employee_md", "params$": "{ id: route?.query?.copyId }", "serviceKey": "sys_common$SYS_CopyDataConverterService", "test$": "!!route.query?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "sys_common$org_employee_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target$": "route?.action === \"edit\" ? \"show\" : \"list\""}]}, "label": "取消"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Validate", "validate": true}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_employee_md"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "valueConfig": {"action": {"target": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form"}, "type": "action"}}], "service": "sys_common$SYS_MasterData_SaveDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "data.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}, {"action": "Refresh", "target": ["sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md"]}, {"action": "Message", "level": "success", "message": "保存成功!"}], "executeLogic": "BindService"}, "label": "保存", "type": "primary"}, "type": "Widget"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": true, "showHeader": true}, "type": "Container"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "name": "ColumnPage", "props": {}, "type": "Layout"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [], "key": "sys_common$ORG_EMPLOYEE_VIEW-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "sys_common$ORG_EMPLOYEE_VIEW-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": false, "showHeader": false, "title": "员工管理"}, "type": "Container"}, "extended": false, "frontendConfig": {"modules": ["base", "terp", "service"]}, "i18nConfig": {"i18nKeySet": ["用户", "状态", "新建员工信息表", "请输入版本号", "详情", "请输入员工编码", "停用成功", "姓名", "保存", "请输入ID", "地址", "用户名", "ID", "创建人", "身份证号", "请输入更新时间", "保存成功!", "请输入姓名", "启用成功", "用户手机", "逻辑删除标识", "确认统一停用吗？", "请输入手机", "请选择", "版本号", "更新时间", "是否主组织", "批量停用", "确认统一启用吗？", "编辑", "所属组织", "选择创建人", "员工信息表", "请输入逻辑删除标识", "确认删除吗？", "批量启用", "员工编码", "复制", "选择更新人", "手机", "系统信息", "新建", "删除成功", "停用", "请输入员工类型", "按钮", "批量删除", "请输入创建时间", "详细地址", "删除", "入职日期", "离职日期", "邮箱", "启用", "批量操作", "用户邮箱", "组织单元", "选择组织单元", "选择组织身份", "主体信息", "确认启用吗？", "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},UNENABLED:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISENABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.text", "更新人", "请输入", "取消", "组织身份", "创建时间", "员工类型", "确认停用吗？"], "i18nScanPaths": ["sys_common$ORG_EMPLOYEE_VIEW-wPS2MJGuQRIvm1fPSsLOu.props.text$", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-name.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.5.label", "sys_common$ORG_EMPLOYEE_VIEW-Mhe2IM7i9iyThkD26CsaC.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.3.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.2.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.5.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.8.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.2.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs.props.items.0.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-batchActions-delete.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-action-cancel.props.label", "sys_common$ORG_EMPLOYEE_VIEW-5JPZoDEYTJahgXeGmcG5P.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-idCard.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs.props.items.1.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressId.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressId.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.0.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressId.props.label", "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable.props.actionConfig.endLogicOtherConfig.1.message", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.1.actionConfig.endLogicOtherConfig.1.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy.props.label", "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-userId.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable.props.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-code.props.componentProps.placeholder", "@exp:sys_common$ORG_EMPLOYEE_VIEW-editView-page-title.props.title", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name.props.label", "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.0.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-status.props.label", "sys_common$ORG_EMPLOYEE_VIEW-NdUI184yh2s4lcc6w4sEg.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.7.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-action-deleteLine.props.label", "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs.props.items.2.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-resignationAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.10.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.2.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable.props.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-lF1QOZEGtkaCHK1RzD8Vw.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.1.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-mobile.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-status.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-resignationAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressId.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-entryAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.1.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-email.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.7.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressDetail.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-new.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-idCard.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-N2CK9jLOrkwXvk-aNk5FJ.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.10.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-copy.props.label", "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-entryAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-Mhe2IM7i9iyThkD26CsaC.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.1.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.2.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-originOrgId.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.2.actionConfig.endLogicOtherConfig.1.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-entryAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.4.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-name.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-mobile.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-idCard.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-idCard.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-N2CK9jLOrkwXvk-aNk5FJ.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-code.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-email.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressDetail.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-entryAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.0.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.2.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs.props.items.0.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete.props.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-NdUI184yh2s4lcc6w4sEg.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-type.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.6.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save.props.actionConfig.endLogicOtherConfig.2.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-lF1QOZEGtkaCHK1RzD8Vw.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.0.actionConfig.endLogicOtherConfig.2.message", "@exp:sys_common$ORG_EMPLOYEE_VIEW-detailView-page-title.props.title", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.0.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-kvFTbRyPRuWhc0sg4AJO7.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs.props.items.1.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable.props.actionConfig.endLogicOtherConfig.1.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-edit.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-type.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.9.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-resignationAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-email.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.0.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.8.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.6.label", "sys_common$ORG_EMPLOYEE_VIEW-5JPZoDEYTJahgXeGmcG5P.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.9.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressDetail.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.3.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.0.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.fields.0.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-email.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete.props.label", "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.1.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete.props.actionConfig.endLogicOtherConfig.1.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.2.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.1.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressDetail.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.4.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-resignationAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-originOrgId.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-userId.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.fields.0.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.1.label"]}, "key": "sys_common$ORG_EMPLOYEE_VIEW:list", "resources": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "label": "表格", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}], "relations": [{"key": "pre-replace:sys_common$org_employee_md_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-new", "label": "新建", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-delete", "label": "批量删除", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "pre-replace:sys_common$org_employee_md_BATCH_DELETE_DATA_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-start", "label": "批量启用", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "pre-replace:sys_common$org_employee_md_MASTER_DATA_MULTI_ENABLE_DATA_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-stop", "label": "批量停用", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "pre-replace:sys_common$org_employee_md_MASTER_DATA_MULTI_DISABLE_DATA_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "label": "导入", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "/api/gei/template/download", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/import-direct-by-oss", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "/api/gei/task/import-sub-model", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "label": "导出", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-logs", "label": "日志", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-toolbar-actions", "label": "按钮组", "type": "ToolbarActions"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-lF1QOZEGtkaCHK1RzD8Vw", "label": "员工编码", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-T0dxNmXpp6q8fg04HXRzp", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-NdUI184yh2s4lcc6w4sEg", "label": "姓名", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-T0dxNmXpp6q8fg04HXRzp", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "pre-replace:sys_common$org_employee_md_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "pre-replace:sys_common$org_employee_md_COPY_DATA_CONVERTER_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}], "relations": [{"key": "pre-replace:sys_common$org_employee_md_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-cancel", "label": "取消", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "label": "保存", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "pre-replace:sys_common$org_employee_md_MASTER_DATA_SAVE_DATA_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete", "label": "删除", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "pre-replace:sys_common$org_employee_md_DELETE_DATA_BY_ID_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-copy", "label": "复制", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable", "label": "停用", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "pre-replace:sys_common$org_employee_md_MASTER_DATA_DISABLE_DATA_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable", "label": "启用", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "pre-replace:sys_common$org_employee_md_MASTER_DATA_ENABLE_DATA_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-edit", "label": "编辑", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detail-sys_common$org_employee_md-logs", "label": "日志", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code", "label": "员工编码", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name", "label": "姓名", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type", "label": "员工类型", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-email", "label": "邮箱", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile", "label": "手机", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressDetail", "label": "详细地址", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-entryAt", "label": "入职日期", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-resignationAt", "label": "离职日期", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-idCard", "label": "身份证号", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "label": "用户", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "pre-replace:sys_common$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressId", "label": "地址", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-Mhe2IM7i9iyThkD26CsaC", "label": "状态", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id", "label": "ID", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "label": "创建人", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "pre-replace:sys_common$user_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "Service"}, {"key": "pre-replace:sys_common$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "label": "更新人", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "pre-replace:sys_common$user_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "Service"}, {"key": "pre-replace:sys_common$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt", "label": "创建时间", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt", "label": "更新时间", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version", "label": "版本号", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted", "label": "逻辑删除标识", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-originOrgId", "label": "所属组织", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform", "label": "表格表单", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-JE2jf3T2tYith5FRLD6QN", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-form-field-employeeOrgLinkList", "label": "自定义表单字段", "type": "CustomFormField"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-code", "label": "员工编码", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-name", "label": "姓名", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-status", "label": "状态", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-type", "label": "员工类型", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-email", "label": "邮箱", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-mobile", "label": "手机", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressDetail", "label": "详细地址", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-entryAt", "label": "入职日期", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-resignationAt", "label": "离职日期", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-idCard", "label": "身份证号", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-userId", "label": "用户", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressId", "label": "地址", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list", "label": "表格", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-gyeTOSkmC__Zr4767Pu1A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-customDetailField", "label": "自定义详情字段", "type": "CustomDetailField"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "label": "创建人", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-zR-xbPeH0dUQGisUgdnZD", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "pre-replace:sys_common$user_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "Service"}, {"key": "pre-replace:sys_common$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "label": "更新人", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-zR-xbPeH0dUQGisUgdnZD", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "pre-replace:sys_common$user_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "Service"}, {"key": "pre-replace:sys_common$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdAt", "label": "创建时间", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-zR-xbPeH0dUQGisUgdnZD", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-zR-xbPeH0dUQGisUgdnZD", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-batchActions-delete", "label": "删除", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-JE2jf3T2tYith5FRLD6QN", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-form-field-employeeOrgLinkList", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform", "label": "表格表单", "type": "TableForm"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-batchActions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-action-deleteLine", "label": "删除", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-JE2jf3T2tYith5FRLD6QN", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-form-field-employeeOrgLinkList", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform", "label": "表格表单", "type": "TableForm"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-reordActions", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-5JPZoDEYTJahgXeGmcG5P", "label": "是否主组织", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-JE2jf3T2tYith5FRLD6QN", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-form-field-employeeOrgLinkList", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform", "label": "表格表单", "type": "TableForm"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-MoO66JROlIXfgdLgx0m1p", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "label": "组织身份", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-JE2jf3T2tYith5FRLD6QN", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-form-field-employeeOrgLinkList", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform", "label": "表格表单", "type": "TableForm"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-MoO66JROlIXfgdLgx0m1p", "label": "字段组", "type": "Fields"}], "relations": [{"key": "pre-replace:sys_common$org_identity_cf_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_identity_cf"}, "type": "Service"}, {"key": "pre-replace:sys_common$org_identity_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_identity_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "label": "组织单元", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-JE2jf3T2tYith5FRLD6QN", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-form-field-employeeOrgLinkList", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform", "label": "表格表单", "type": "TableForm"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-MoO66JROlIXfgdLgx0m1p", "label": "字段组", "type": "Fields"}], "relations": [{"key": "pre-replace:sys_common$org_struct_md_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}, {"key": "pre-replace:sys_common$org_struct_md_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-N2CK9jLOrkwXvk-aNk5FJ", "label": "是否主组织", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-gyeTOSkmC__Zr4767Pu1A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-customDetailField", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-LAxXu7V8oaf_BP5O6foRo", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "label": "组织身份", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-gyeTOSkmC__Zr4767Pu1A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-customDetailField", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-LAxXu7V8oaf_BP5O6foRo", "label": "字段组", "type": "Fields"}], "relations": [{"key": "pre-replace:sys_common$org_identity_cf_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_identity_cf"}, "type": "Service"}, {"key": "pre-replace:sys_common$org_identity_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_identity_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "label": "组织单元", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-gyeTOSkmC__Zr4767Pu1A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-customDetailField", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-LAxXu7V8oaf_BP5O6foRo", "label": "字段组", "type": "Fields"}], "relations": [{"key": "pre-replace:sys_common$org_struct_md_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}, {"key": "pre-replace:sys_common$org_struct_md_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-kvFTbRyPRuWhc0sg4AJO7", "label": "按钮", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}], "title": "list", "type": "LIST"}, "type": "View"}