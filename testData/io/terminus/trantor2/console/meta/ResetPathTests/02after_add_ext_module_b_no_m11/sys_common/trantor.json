{"key": "sys_common", "name": "通用", "platformVersion": {"checksum": "0000000000000000000000000000000000000000000000000000000000000000", "number": "0.0.0.0"}, "props": {"config": null, "endpointType": null, "globalConfig": {"modelFieldSelected": true, "modelFieldShowEnable": true}, "logoUrl": null, "moduleConfig": null, "nativeModule": false, "relatedModuleKeys": null, "sourceApp": {"moduleKey": "sys_common", "name": "通用", "snapshotOid": "97ba647ba61ee19149424b684b38d679c701b534e0ea37171513fd8bff032db2", "sourceAppKey": "platform.sys_common", "sourceTeamCode": "platform", "sysApp": true, "version": "2.5.24.0530"}, "type": "<PERSON><PERSON><PERSON>", "version": null}, "type": "<PERSON><PERSON><PERSON>"}