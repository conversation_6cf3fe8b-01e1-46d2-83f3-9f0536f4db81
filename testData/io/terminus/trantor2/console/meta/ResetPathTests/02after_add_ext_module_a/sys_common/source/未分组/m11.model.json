{"access": "Private", "key": "sys_common$m11", "name": "m11", "props": {"alias": "sys_common$m11", "children": [{"alias": "fzzz", "key": "fzzz", "name": "fzzz", "props": {"autoGenerated": false, "columnName": "fzzz", "comment": "fzzz", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 256, "required": false, "unique": false}, "type": "DataStructField"}], "desc": null, "props": {"config": {"persist": false, "self": false, "selfRelationFieldAlias": null, "system": false}, "mainField": "id", "mainFieldAlias": "id", "orderNumberEnabled": false, "originOrgIdEnabled": false, "physicalDelete": false, "searchModel": false, "tableName": "m11", "type": "VIEW"}}, "type": "Model"}