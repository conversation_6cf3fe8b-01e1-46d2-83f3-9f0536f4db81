{"access": "Private", "key": "test$view1", "name": "View Example 1", "props": {"content": {"children": [{"children": [{"children": [], "key": "b1", "name": "<PERSON><PERSON>", "props": {"permissionKey": "test$view_p_b1_perm"}}], "key": "t1", "name": "Table", "props": {"permissionKey": "test$view_p_t1_perm"}}], "key": "p1", "name": "Page", "props": {}}, "key": "test$view1", "name": "View Example 1", "permissionKey": "test$view1_perm", "title": "View Example 1", "type": "CUSTOM"}, "type": "View"}