{"access": "Private", "key": "test$view3", "name": "View Example 3", "props": {"content": {"children": [{"key": "btn1", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"service": "test$service1"}, "executeLogic": "BindService"}}}, {"key": "btn2", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"service": "test$service2"}, "executeLogic": "BindService"}, "onClick$": "params => invokeServiceQueryByKey('test$service1', $('myTable').action('getSelectedKeys')?.map(id => ({ id })))", "permissionKey": "test$function_item_1"}}, {"key": "btn3", "name": "<PERSON><PERSON>", "props": {"permissionKey": "test$function_item_2"}}, {"key": "btn4", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"service": "test$service2_empty"}, "executeLogic": "BindService"}}}, {"key": "table1", "name": "Table", "props": {"flow": {"serviceKey": "test$service3"}}}, {"key": "table2", "name": "Table", "props": {"flow": {"serviceKey": "test$service2_empty"}}}, {"key": "apbr1", "name": "ApprovalProxyBatchReplace", "props": {"addServiceProps": {"batchReplaceFlow": {"serviceKey": "test$service3", "type": "InvokeService"}, "fake1Flow": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "test$model1"}}], "serviceKey": "test$sys_123", "type": "InvokeSystemService"}, "fake1dupFlow": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "test$model1"}}], "serviceKey": "test$sys_123", "type": "InvokeSystemService"}, "fake2Flow": {"serviceKey": "test$sys_123", "type": "InvokeSystemService"}, "fake3Flow": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "external$model2"}}], "serviceKey": "test$sys_123", "type": "InvokeSystemService"}, "getEmployeeFlow": {"serviceKey": "test$service1", "type": "InvokeService"}, "onFinish$": "params => invokeSystemServiceQuery('test$sys_123', 'test$model2_missing', params)", "workflowPagingFlow": {"serviceKey": "test$service2", "type": "InvokeService"}}, "approvalProxyBatchReplacePaging": {"permissionKey": "test$function_item_2"}, "fetchServiceProps": {"flow": {"serviceKey": "test$service2", "type": "InvokeService"}}, "otherSelf": {"flow": {"serviceKey": "test$service3", "type": "InvokeService"}}}}, {"key": "rlst1", "name": "RoleList", "props": {}}, {"key": "atc1", "name": "ApprovalTaskCenter", "props": {}}, {"key": "btn5", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "test$model1"}}], "service": "test$sys_123"}, "executeLogic": "BindService"}}}, {"children": [], "key": "ddb1", "name": "DropdownButton", "props": {"combinationMode": true, "disabled$": "$context.selectedKeys?.length === 0 || $context.route?.action === \"new\" || $context.route?.action === \"edit\"", "items": [{"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_business_partner_type_cf"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "ERP_GEN$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_GEN$CEN_PARTNER_TYPE_VIEW-list-ERP_GEN$gen_business_partner_type_cf"}, {"action": "PageJump", "target": "list"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "ERP_GEN$CEN_PARTNER_TYPE_VIEW-ERP_GEN$gen_business_partner_type_cf-multi-delete", "label": "批量删除"}, {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认统一启用吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_business_partner_type_cf"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "ERP_GEN$SYS_MasterData_MultiEnableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ["ERP_GEN$CEN_PARTNER_TYPE_VIEW-list-ERP_GEN$gen_business_partner_type_cf"]}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "ERP_GEN$CEN_PARTNER_TYPE_VIEW-ERP_GEN$gen_business_partner_type_cf-multi-start", "label": "批量启用"}, {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认统一停用吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_business_partner_type_cf"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "ERP_GEN$SYS_MasterData_MultiDisableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_GEN$CEN_PARTNER_TYPE_VIEW-list-ERP_GEN$gen_business_partner_type_cf"}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "ERP_GEN$CEN_PARTNER_TYPE_VIEW-ERP_GEN$gen_business_partner_type_cf-multi-stop", "label": "批量停用"}], "label": "批量操作", "type": "default", "variant": "primary"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_IDENTITY_VIEW-list-sys_common$org_identity_cf-import", "name": "ImportButton", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "downloadServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download"}}, "isCustomServiceProps": {"isCustomFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "type": "InvokeService"}}, "label": "导入", "predictServiceProps": {"predictFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "type": "InvokeService"}}, "saveServiceProps": {"saveServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "type": "InvokeService"}, "saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-direct-by-oss"}}, "saveSubServiceProps": {"saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-sub-model"}, "saveSubServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "type": "InvokeService"}, "testApi": {"api1BadMethodFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/api1"}, "api1Flow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor/api1"}, "api2Flow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/api2"}}}}, "type": "Widget"}, {"children": [], "key": "sys_common$approval_task_center-4kd-Bxk1dtx8WrQbK2bM-", "name": "ApprovalTaskCenter", "props": {"ApprovalInstanceBtnContainerProps": {"customiaedCandidateFlow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_START_WITH_CUSTOMIZED_CANDIDATE_POST", "type": "InvokeService"}, "getApprovalListByBizIdFlow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_INSTANCE_LIST_GET", "type": "InvokeService"}, "getApprovalStepsFlow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_INSTANCE_GET", "type": "InvokeService"}, "permissionKey": "sys_common$approval_task_center:list_perm_ac_z_0_ApprovalInstanceBtnContainerProps", "remarkFlow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_INSTANCE_ADD_COMMENT_POST", "type": "InvokeService"}}, "agreeService": {"flow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_TASK_SUBMIT_POST", "type": "InvokeService"}, "permissionKey": "sys_common$approval_task_center:list_perm_ac_z_0_agreeService"}, "approvalTaskCenterAddApprover": {"permissionKey": "test$view1_perm_ac_z_8_approvalTaskCenterAddApprover"}, "approvalTaskCenterAgree": {"permissionKey": "test$view1_perm_ac_z_8_approvalTaskCenterAgree"}, "approvalTaskCenterBatchAgree": {"permissionKey": "test$view1_perm_ac_z_8_approvalTaskCenterBatchAgree"}, "approvalTaskCenterBatchCancel": {"permissionKey": "test$view1_perm_ac_z_8_approvalTaskCenterBatchCancel"}, "approvalTaskCenterBatchDisagree": {"permissionKey": "test$view1_perm_ac_z_8_approvalTaskCenterBatchDisagree"}, "approvalTaskCenterBatchForward": {"permissionKey": "test$view1_perm_ac_z_8_approvalTaskCenterBatchForward"}, "approvalTaskCenterBatchTransferSign": {"permissionKey": "test$view1_perm_ac_z_8_approvalTaskCenterBatchTransferSign"}, "approvalTaskCenterCancel": {"permissionKey": "test$view1_perm_ac_z_8_approvalTaskCenterCancel"}, "approvalTaskCenterDetail": {"permissionKey": "test$view1_perm_ac_z_8_approvalTaskCenterDetail"}, "approvalTaskCenterDetailTarget": {"permissionKey": "sys_common$approval_task_center:list_perm_ac_z_0_approvalTaskCenterDetailTarget"}, "approvalTaskCenterDisagree": {"permissionKey": "test$view1_perm_ac_z_8_approvalTaskCenterDisagree"}, "approvalTaskCenterReject": {"permissionKey": "test$view1_perm_ac_z_8_approvalTaskCenterReject"}, "approvalTaskCenterUrge": {"permissionKey": "test$view1_perm_ac_z_8_approvalTaskCenterUrge"}, "batchForwardService": {"flow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_TASK_INSTANCE_BATCH_FORWARD_POST", "type": "InvokeService"}, "permissionKey": "sys_common$approval_task_center:list_perm_ac_z_0_batchForwardService"}, "batchTransferService": {"flow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_TASK_BATCH_TRANSFER_POST", "type": "InvokeService"}, "permissionKey": "sys_common$approval_task_center:list_perm_ac_z_0_batchTransferService"}, "cancelService": {"flow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_CANCEL_POST", "type": "InvokeService"}, "permissionKey": "sys_common$approval_task_center:list_perm_ac_z_0_cancelService"}, "disagreeService": {"flow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_TASK_SUBMIT_POST", "type": "InvokeService"}, "permissionKey": "sys_common$approval_task_center:list_perm_ac_z_0_disagreeService"}, "getOrgIdByTaskIdFlow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_TASK_INSTANCE_ORG_ID_GET", "type": "InvokeService"}, "getOrgListFlow": {"serviceKey": "ERP_GEN$ORG_SWITCH_QUERY_USER_COM_EVENT_SERVICE", "type": "InvokeService"}, "groupService": {"createFlow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_SELECTION_CRITERIA_CREATE_POST", "type": "InvokeService"}, "deleteFlow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_SELECTION_CRITERIA_DELETE_POST", "type": "InvokeService"}, "fetchByIdFlow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_SELECTION_CRITERIA_FIND_GET", "type": "InvokeService"}, "fetchListFlow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_SELECTION_CRITERIA_LIST_POST", "type": "InvokeService"}, "updateFlow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_SELECTION_CRITERIA_UPDATE_POST", "type": "InvokeService"}}, "pagingService": {"flow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_TASK_INSTANCE_SEARCH_GET", "type": "InvokeService"}}, "rejectService": {"flow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_TASK_SUBMIT_POST", "type": "InvokeService"}, "permissionKey": "sys_common$approval_task_center:list_perm_ac_z_0_rejectService"}, "title": "审批任务中心", "urgeService": {"flow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_TASK_REMIND_POST", "type": "InvokeService"}, "permissionKey": "sys_common$approval_task_center:list_perm_ac_z_0_urgeService"}}, "type": "Widget"}], "key": "page1", "name": "Page", "props": {"title": "test view1"}}, "key": "test$view3", "name": "View Example 3", "title": "View Example 3", "type": "CUSTOM"}, "type": "View"}