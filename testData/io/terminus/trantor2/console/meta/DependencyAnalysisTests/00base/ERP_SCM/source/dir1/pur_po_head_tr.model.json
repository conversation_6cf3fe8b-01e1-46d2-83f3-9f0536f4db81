{"access": "Private", "key": "ERP_SCM$pur_po_head_tr", "name": "采购订单-H", "props": {"alias": "ERP_SCM$pur_po_head_tr", "children": [{"alias": "poCode", "key": "po_code", "name": "订单编号", "props": {"autoGenerated": false, "columnName": "po_code", "comment": "订单编号", "compositeKey": true, "encrypted": false, "fieldType": "TEXT", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": true, "unique": false}}, {"alias": "poName", "key": "po_name", "name": "标题", "props": {"autoGenerated": false, "columnName": "po_name", "comment": "标题", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "poType", "key": "po_type", "name": "单据类型", "props": {"autoGenerated": false, "columnName": "po_type", "comment": "单据类型", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "poType", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "ERP_SCM$pur_po_type_cf", "relationModelKey": "ERP_SCM$pur_po_type_cf", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "docTypeRef", "key": "doc_type_ref", "name": "关联单据类型", "props": {"autoGenerated": false, "columnName": "doc_type_ref", "comment": "关联单据类型", "compositeKey": false, "dictPros": {"dictValues": [{"label": "采购申请", "value": "PR"}, {"label": "采购询价", "value": "RFQ"}, {"label": "采购订单", "value": "PO"}, {"label": "销售订单", "value": "SO"}, {"label": "商城订单", "value": "MALL"}], "multiSelect": false, "properties": null}, "encrypted": false, "fieldType": "ENUM", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "docIdExt", "key": "doc_id_ext", "name": "外部单据编号", "props": {"autoGenerated": false, "columnName": "doc_id_ext", "comment": "外部单据编号", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "poStatusDel", "key": "po_status_del", "name": "收货状态", "props": {"autoGenerated": false, "columnName": "po_status_del", "comment": "收货状态", "compositeKey": false, "dictPros": {"dictValues": [{"label": "待收货", "value": "WAIT_FOR_RECEIVING"}, {"label": "部分收货", "value": "PART_OF_RECEIVING"}, {"label": "收货完成", "value": "COMPLETION_OF_RECEIVING"}], "multiSelect": false, "properties": null}, "encrypted": false, "fieldType": "ENUM", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "isGrBlock", "key": "is_gr_block", "name": "收货冻结", "props": {"autoGenerated": false, "columnName": "is_gr_block", "comment": "收货冻结", "compositeKey": false, "dictPros": {"dictValues": [{"label": "正常", "value": "NORMAL"}, {"label": "冻结", "value": "FREEZE"}], "multiSelect": false, "properties": null}, "encrypted": false, "fieldType": "ENUM", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "comOrgId", "key": "com_org_id", "name": "公司组织", "props": {"autoGenerated": false, "columnName": "com_org_id", "comment": "公司组织", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "comOrgId", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "sys_common$org_struct_md", "relationModelKey": "sys_common$org_struct_md", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "purOrgId", "key": "pur_org_id", "name": "采购组织", "props": {"autoGenerated": false, "columnName": "pur_org_id", "comment": "采购组织", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "purOrgId", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "sys_common$org_struct_md", "relationModelKey": "sys_common$org_struct_md", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "purTm", "key": "pur_tm", "name": "采购组", "props": {"autoGenerated": false, "columnName": "pur_tm", "comment": "采购组", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "purTm", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "ERP_GEN$org_pur_tm_cf", "relationModelKey": "ERP_GEN$org_pur_tm_cf", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "vendId", "key": "vend_id", "name": "供应商", "props": {"autoGenerated": false, "columnName": "vend_id", "comment": "供应商", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "vendId", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "ERP_GEN$gen_vend_info_md", "relationModelKey": "ERP_GEN$gen_vend_info_md", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "invOrg", "key": "inv_org", "name": "开票公司", "props": {"autoGenerated": false, "columnName": "inv_org", "comment": "开票公司", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "invOrg", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "sys_common$org_struct_md", "relationModelKey": "sys_common$org_struct_md", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "invType", "key": "inv_type", "name": "发票类型", "props": {"autoGenerated": false, "columnName": "inv_type", "comment": "发票类型", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "invType", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "ERP_SCM$cf_invoice_type", "relationModelKey": "ERP_SCM$cf_invoice_type", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "currTypeCode", "key": "curr_type_code", "name": "支付币种", "props": {"autoGenerated": false, "columnName": "curr_type_code", "comment": "支付币种", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "currTypeCode", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "ERP_GEN$gen_curr_type_cf", "relationModelKey": "ERP_GEN$gen_curr_type_cf", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "payTermValue", "key": "pay_term_value", "name": "支付条件", "props": {"autoGenerated": false, "columnName": "pay_term_value", "comment": "支付条件", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "payMethodValue", "key": "pay_method_value", "name": "支付方式", "props": {"autoGenerated": false, "columnName": "pay_method_value", "comment": "支付方式", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "totalAmountGross", "key": "total_amount_gross", "name": "含税总金额", "props": {"autoGenerated": false, "columnName": "total_amount_gross", "comment": "含税总金额", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "i18nEnabled": false, "intLength": 26, "isSystemField": false, "numberDisplayType": "currency", "relationField": false, "required": false, "scale": 2, "unique": false}}, {"alias": "amountNet", "key": "amount_net", "name": "不含税金额", "props": {"autoGenerated": false, "columnName": "amount_net", "comment": "不含税金额", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "i18nEnabled": false, "intLength": 26, "isSystemField": false, "numberDisplayType": "currency", "relationField": false, "required": false, "scale": 2, "unique": false}}, {"alias": "taxAmount", "key": "tax_amount", "name": "税额", "props": {"autoGenerated": false, "columnName": "tax_amount", "comment": "税额", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "i18nEnabled": false, "intLength": 26, "isSystemField": false, "numberDisplayType": "currency", "relationField": false, "required": false, "scale": 2, "unique": false}}, {"alias": "poItem", "key": "po_item", "name": "订单项目行", "props": {"autoGenerated": false, "columnName": "po_item", "comment": "订单项目行", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "poItem", "linkModelAlias": "ERP_SCM$pur_po_item_tr", "linkModelFieldAlias": "purPoHeadTrId", "linkRelationModel": false, "relationKey": null, "relationModelAlias": "ERP_SCM$pur_po_item_tr", "relationModelKey": "ERP_SCM$pur_po_item_tr", "relationType": "PARENT_CHILD", "sync": true}, "required": false, "unique": false}}, {"alias": "text", "key": "text", "name": "文本备注", "props": {"autoGenerated": false, "columnName": "text", "comment": "文本备注", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "text", "linkModelAlias": "ERP_SCM$pur_po_text_link_tr", "linkModelFieldAlias": "doc<PERSON>ef", "linkRelationModel": false, "relationKey": null, "relationModelAlias": "ERP_SCM$pur_po_text_link_tr", "relationModelKey": "ERP_SCM$pur_po_text_link_tr", "relationType": "PARENT_CHILD", "sync": true}, "required": false, "unique": false}}, {"alias": "attachment", "key": "attachment", "name": "附件", "props": {"autoGenerated": false, "columnName": "attachment", "comment": "附件", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "attachment", "linkModelAlias": "ERP_SCM$pur_po_attachment_link_tr", "linkModelFieldAlias": "doc<PERSON>ef", "linkRelationModel": false, "relationKey": null, "relationModelAlias": "ERP_SCM$pur_po_attachment_link_tr", "relationModelKey": "ERP_SCM$pur_po_attachment_link_tr", "relationType": "PARENT_CHILD", "sync": true}, "required": false, "unique": false}}, {"alias": "partner", "key": "partner", "name": "相关方", "props": {"autoGenerated": false, "columnName": "partner", "comment": "相关方", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "partner", "linkModelAlias": "ERP_SCM$pur_po_partner_link_tr", "linkModelFieldAlias": "doc<PERSON>ef", "linkRelationModel": false, "relationKey": null, "relationModelAlias": "ERP_SCM$pur_po_partner_link_tr", "relationModelKey": "ERP_SCM$pur_po_partner_link_tr", "relationType": "PARENT_CHILD", "sync": true}, "required": false, "unique": false}}, {"alias": "invOrgId", "key": "inv_org_id", "name": "库存组织", "props": {"autoGenerated": false, "columnName": "inv_org_id", "comment": "库存组织", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "invOrgId", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "sys_common$org_struct_md", "relationModelKey": "sys_common$org_struct_md", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "invLocId", "key": "inv_loc_id", "name": "库存地点", "props": {"autoGenerated": false, "columnName": "inv_loc_id", "comment": "库存地点", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "invLocId", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "sys_common$org_struct_md", "relationModelKey": "sys_common$org_struct_md", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "invOrgStoFrom", "key": "inv_org_sto_from", "name": "发货库存组织", "props": {"autoGenerated": false, "columnName": "inv_org_sto_from", "comment": "发货库存组织", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "invOrgStoFrom", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "sys_common$org_struct_md", "relationModelKey": "sys_common$org_struct_md", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "invLocStoFrom", "key": "inv_loc_sto_from", "name": "发货库存地点", "props": {"autoGenerated": false, "columnName": "inv_loc_sto_from", "comment": "发货库存地点", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "invLocStoFrom", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "sys_common$org_struct_md", "relationModelKey": "sys_common$org_struct_md", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "purCurrId", "key": "pur_curr_id", "name": "交易币种", "props": {"autoGenerated": false, "columnName": "pur_curr_id", "comment": "交易币种", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "purCurrId", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "ERP_GEN$gen_curr_type_cf", "relationModelKey": "ERP_GEN$gen_curr_type_cf", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "cooperationStatus", "key": "cooperation_status", "name": "协同状态", "props": {"autoGenerated": false, "columnName": "cooperation_status", "comment": "协同状态", "compositeKey": false, "dictPros": {"dictValues": [{"label": "待接单", "value": "WAITING"}, {"label": "已接单", "value": "ACCEPTED"}, {"label": "已拒绝", "value": "REJECTED"}], "multiSelect": false, "properties": null}, "encrypted": false, "fieldType": "ENUM", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "approveStatus", "key": "approve_status", "name": "审核状态", "props": {"autoGenerated": false, "columnName": "approve_status", "comment": "审核状态", "compositeKey": false, "dictPros": {"dictValues": [{"label": "进行中", "value": "INPROCESS"}, {"label": "已通过", "value": "APPROVED"}, {"label": "已拒绝", "value": "REJECTED"}, {"label": "提交审批通过", "value": "EXT_SUBMIT_APPROVED"}, {"label": "提交审批拒绝", "value": "EXT_SUBMIT_REJECTED"}, {"label": "变更审批通过", "value": "EXT_CHANGE_APPROVED"}, {"label": "变更审批拒绝", "value": "EXT_CHANGE_REJECTED"}, {"label": "作废审批通过", "value": "EXT_VOID_APPROVED"}, {"label": "作废审批拒绝", "value": "EXT_VOID_REJECTED"}, {"label": "完成审批通过", "value": "EXT_COMPLETED_APPROVED"}, {"label": "完成审批拒绝", "value": "EXT_COMPLETED_REJECTED"}, {"label": "完成审批中", "value": "EXT_COMPLETED_INPROCESS"}, {"label": "作废审批中", "value": "EXT_VOID_INPROCESS"}, {"label": "变更审批中", "value": "EXT_CHANGE_INPROCESS"}, {"label": "提交审批中", "value": "EXT_SUBMIT_INPROCESS"}, {"label": "支付审批中", "value": "EXT_PAY_INPROCESS"}, {"label": "支付审批通过", "value": "EXT_PAY_APPROVED"}, {"label": "支付审批拒绝", "value": "EXT_PAY_REJECTED"}, {"label": "已撤回", "value": "REVOKED"}, {"label": "支付已撤回", "value": "EXT_PAY_REVOKED"}, {"label": "提交已撤回", "value": "EXT_SUBMIT_REVOKED"}, {"label": "变更已撤回", "value": "EXT_CHANGE_REVOKED"}, {"label": "作废已撤回", "value": "EXT_VOID_REVOKED"}], "multiSelect": false, "properties": null}, "encrypted": false, "fieldType": "ENUM", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "pymtSchl", "key": "pymt_schl", "name": "支付计划行", "props": {"autoGenerated": false, "columnName": "pymt_schl", "comment": "支付计划行", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "pymtSchl", "linkModelAlias": "ERP_SCM$pur_pymt_schl_tr", "linkModelFieldAlias": "poRefId", "linkRelationModel": false, "relationKey": null, "relationModelAlias": "ERP_SCM$pur_pymt_schl_tr", "relationModelKey": "ERP_SCM$pur_pymt_schl_tr", "relationType": "PARENT_CHILD", "sync": true}, "required": false, "unique": false}}, {"alias": "rgStatusDel", "key": "rg_status_del", "name": "退货状态", "props": {"autoGenerated": false, "columnName": "rg_status_del", "comment": "退货状态", "compositeKey": false, "dictPros": {"dictValues": [{"label": "待退货", "value": "RETURN_TODO"}, {"label": "部分退货", "value": "RETURN_PART"}, {"label": "完成", "value": "DONE"}], "multiSelect": false, "properties": null}, "encrypted": false, "fieldType": "ENUM", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "rgDocRef", "key": "rg_doc_ref", "name": "关联正向订单", "props": {"autoGenerated": false, "columnName": "rg_doc_ref", "comment": "关联正向订单", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "rgDocRef", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "ERP_SCM$pur_po_head_tr", "relationModelKey": "ERP_SCM$pur_po_head_tr", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "contCode", "key": "cont_code", "name": "关联合同编号", "props": {"autoGenerated": false, "columnName": "cont_code", "comment": "关联合同编号", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "contName", "key": "cont_name", "name": "关联合同名称", "props": {"autoGenerated": false, "columnName": "cont_name", "comment": "关联合同名称", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "buyer", "key": "buyer", "name": "采购员", "props": {"autoGenerated": false, "columnName": "buyer", "comment": "采购员", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "buyer", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "sys_common$org_employee_md", "relationModelKey": "sys_common$org_employee_md", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "orderState", "key": "order_state", "name": "收货状态", "props": {"autoGenerated": false, "columnName": "order_state", "comment": "收货状态", "compositeKey": false, "dictPros": {"dictValues": [{"label": "待收货", "value": "to_be_shipped"}, {"label": "部分发货", "value": "partial_sending"}, {"label": "收货完成", "value": "receive_completed"}], "multiSelect": false, "properties": null}, "encrypted": false, "fieldType": "ENUM", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "proxy", "key": "proxy", "name": "代理人", "props": {"autoGenerated": false, "columnName": "proxy", "comment": "代理人", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "repository", "key": "repository", "name": "需求仓库", "props": {"autoGenerated": false, "columnName": "repository", "comment": "需求仓库", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "repository", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "sys_common$org_struct_md", "relationModelKey": "sys_common$org_struct_md", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "supplierContactPerson", "key": "supplier_contact_person", "name": "供应商联系人", "props": {"autoGenerated": false, "columnName": "supplier_contact_person", "comment": "供应商联系人", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "supplierContactInfo", "key": "supplier_contact_info", "name": "供应商联系方式", "props": {"autoGenerated": false, "columnName": "supplier_contact_info", "comment": "供应商联系方式", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "pur<PERSON><PERSON><PERSON><PERSON><PERSON>", "key": "pur_contact_person", "name": "采购方联系人", "props": {"autoGenerated": false, "columnName": "pur_contact_person", "comment": "采购方联系人", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "purContactInfo", "key": "pur_contact_info", "name": "采购方联系方式", "props": {"autoGenerated": false, "columnName": "pur_contact_info", "comment": "采购方联系方式", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "pur<PERSON><PERSON><PERSON>", "key": "pur_remark", "name": "订单备注", "props": {"autoGenerated": false, "columnName": "pur_remark", "comment": "订单备注", "compositeKey": false, "encrypted": false, "fieldType": "MULTI_TEXT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "required": false, "unique": false}}, {"alias": "businessDate", "key": "business_date", "name": "单据业务日期", "props": {"autoGenerated": false, "columnName": "business_date", "comment": "单据业务日期", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "i18nEnabled": false, "isSystemField": false, "relationField": false, "required": false, "unique": false}}, {"alias": "deliveryFrozen", "key": "delivery_frozen", "name": "是否交货冻结", "props": {"autoGenerated": false, "columnName": "delivery_frozen", "comment": "是否交货冻结", "compositeKey": false, "defaultValue": false, "encrypted": false, "fieldType": "BOOL", "i18nEnabled": false, "isSystemField": false, "length": 1, "relationField": false, "required": false, "unique": false}}, {"alias": "referPoCode", "key": "refer_po_code", "name": "参考创建单据编号", "props": {"autoGenerated": false, "columnName": "refer_po_code", "comment": "参考创建单据编号", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "documentStatus", "key": "document_status", "name": "单据状态", "props": {"autoGenerated": false, "columnName": "document_status", "comment": "单据状态", "compositeKey": false, "dictPros": {"dictValues": [{"label": "审批中", "value": "IN_APPROVAL"}, {"label": "作废", "value": "ABOLISH"}, {"label": "生效", "value": "EFFECT"}, {"label": "草稿", "value": "DRAFT"}], "multiSelect": false, "properties": null}, "encrypted": false, "fieldType": "ENUM", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "businessStatus", "key": "business_status", "name": "业务状态", "props": {"autoGenerated": false, "columnName": "business_status", "comment": "业务状态", "compositeKey": false, "dictPros": {"dictValues": [{"label": "履约中", "value": "IN_PERFORMANCE"}, {"label": "已完成", "value": "FINISHED"}, {"label": "待接单", "value": "PENDING_RECEIVE_ORDERS"}, {"label": "已接单", "value": "RECEIVED_ORDERS"}, {"label": "已拒单", "value": "REJECTED_ORDERS"}], "multiSelect": false, "properties": null}, "encrypted": false, "fieldType": "ENUM", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "supplyInvOrgId", "key": "supply_inv_org_id", "name": "供应库存组织", "props": {"autoGenerated": false, "columnName": "supply_inv_org_id", "comment": "供应库存组织", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "supplyInvOrgId", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "sys_common$org_struct_md", "relationModelKey": "sys_common$org_struct_md", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "supplyInvLocId", "key": "supply_inv_loc_id", "name": "供应库存地点", "props": {"autoGenerated": false, "columnName": "supply_inv_loc_id", "comment": "供应库存地点", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "supplyInvLocId", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "sys_common$org_struct_md", "relationModelKey": "sys_common$org_struct_md", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "docCodeRef", "key": "doc_code_ref", "name": "关联单据编号", "props": {"autoGenerated": false, "columnName": "doc_code_ref", "comment": "关联单据编号", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "docIdRef", "key": "doc_id_ref", "name": "关联单据id", "props": {"autoGenerated": false, "columnName": "doc_id_ref", "comment": "关联单据id", "compositeKey": false, "encrypted": false, "fieldType": "NUMBER", "i18nEnabled": false, "intLength": 20, "isSystemField": false, "numberDisplayType": "digit", "relationField": false, "required": false, "unique": false}}, {"alias": "supplierCollaborationEnabled", "key": "supplier_collaboration_enabled", "name": "是否开启供应商协同", "props": {"autoGenerated": false, "columnName": "supplier_collaboration_enabled", "comment": "是否开启供应商协同", "compositeKey": false, "defaultValue": false, "encrypted": false, "fieldType": "BOOL", "i18nEnabled": false, "isSystemField": false, "length": 1, "relationField": false, "required": false, "unique": false}}, {"alias": "bizRuleGroup", "key": "biz_rule_group", "name": "付款规则组", "props": {"autoGenerated": false, "columnName": "biz_rule_group", "comment": "付款规则组", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "bizRuleGroup", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "ERP_GEN$gen_brm_rule_group_head_tr", "relationModelKey": "ERP_GEN$gen_brm_rule_group_head_tr", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "brmRuleList", "key": "brm_rule_list", "name": "付款规则明细", "props": {"autoGenerated": false, "columnName": "brm_rule_list", "comment": "付款规则明细", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "brmRuleList", "linkModelAlias": "ERP_SCM$scm_order_rule_head_tr", "linkModelFieldAlias": "purPoHeadTrId", "linkRelationModel": false, "relationKey": null, "relationModelAlias": "ERP_SCM$scm_order_rule_head_tr", "relationModelKey": "ERP_SCM$scm_order_rule_head_tr", "relationType": "PARENT_CHILD", "sync": false}, "required": false, "unique": false}}, {"alias": "thirdPartyOrderStatus", "key": "third_party_order_status", "name": "三方商城订单同步状态", "props": {"autoGenerated": false, "columnName": "third_party_order_status", "comment": "三方商城订单同步状态", "compositeKey": false, "dictPros": {"dictValues": [{"label": "创建预订单成功", "value": "CREATE_PREORDER_SUCCESS"}, {"label": "创建预订单失败", "value": "CREATE_PREORDER_FAILURE"}, {"label": "取消订单成功", "value": "CANCEL_PREORDER_SUCCESS"}, {"label": "取消订单失败", "value": "CANCEL_PREORDER_FAILURE"}, {"label": "确认订单成功", "value": "CONFIRM_PREORDER_SUCCESS"}, {"label": "确认订单失败", "value": "CONFIRM_PREORDER_FAILURE"}], "multiSelect": false, "properties": null}, "encrypted": false, "fieldType": "ENUM", "i18nEnabled": false, "isSystemField": false, "length": 256, "relationField": false, "required": false, "unique": false}}, {"alias": "thirdPartyOrderSyncInfo", "key": "third_party_order_sync_info", "name": "三方商城订单同步resInfo", "props": {"autoGenerated": false, "columnName": "third_party_order_sync_info", "comment": "三方商城订单同步resInfo", "compositeKey": false, "encrypted": false, "fieldType": "MULTI_TEXT", "i18nEnabled": false, "isSystemField": false, "relationField": false, "required": false, "unique": false}}, {"alias": "id", "key": "id", "name": "ID", "props": {"autoGenerated": false, "columnName": "id", "comment": "ID", "compositeKey": false, "encrypted": false, "fieldType": "NUMBER", "i18nEnabled": false, "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "relationField": false, "required": true, "unique": false}}, {"alias": "created<PERSON>y", "key": "created_by", "name": "创建人", "props": {"autoGenerated": false, "columnName": "created_by", "comment": "创建人", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": true, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "created<PERSON>y", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "ERP_SCM$user", "relationModelKey": "ERP_SCM$user", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "updatedBy", "key": "updated_by", "name": "更新人", "props": {"autoGenerated": false, "columnName": "updated_by", "comment": "更新人", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "i18nEnabled": false, "isSystemField": true, "relationField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_po_head_tr", "currentModelFieldAlias": "updatedBy", "linkModelAlias": null, "linkModelFieldAlias": null, "linkRelationModel": false, "relationKey": null, "relationModelAlias": "ERP_SCM$user", "relationModelKey": "ERP_SCM$user", "relationType": "LINK", "sync": false}, "required": false, "unique": false}}, {"alias": "createdAt", "key": "created_at", "name": "创建时间", "props": {"autoGenerated": false, "columnName": "created_at", "comment": "创建时间", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "i18nEnabled": false, "isSystemField": true, "relationField": false, "required": false, "unique": false}}, {"alias": "updatedAt", "key": "updated_at", "name": "更新时间", "props": {"autoGenerated": false, "columnName": "updated_at", "comment": "更新时间", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "i18nEnabled": false, "isSystemField": true, "relationField": false, "required": false, "unique": false}}, {"alias": "version", "key": "version", "name": "版本号", "props": {"autoGenerated": false, "columnName": "version", "comment": "版本号", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "i18nEnabled": false, "intLength": 20, "isSystemField": true, "numberDisplayType": "digit", "relationField": false, "required": true, "unique": false}}, {"alias": "deleted", "key": "deleted", "name": "逻辑删除标识", "props": {"autoGenerated": false, "columnName": "deleted", "comment": "逻辑删除标识", "compositeKey": true, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "i18nEnabled": false, "intLength": 20, "isSystemField": true, "numberDisplayType": "digit", "relationField": false, "required": false, "unique": false}}, {"alias": "originOrgId", "key": "origin_org_id", "name": "所属组织", "props": {"autoGenerated": false, "columnName": "origin_org_id", "comment": "所属组织", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "i18nEnabled": false, "intLength": 20, "isSystemField": true, "numberDisplayType": "digit", "relationField": false, "required": true, "unique": false}}], "desc": null, "props": {"config": {"persist": false, "self": true, "selfRelationFieldAlias": "rgDocRef", "system": false}, "i18nEnabled": false, "mainField": "po_code", "mainFieldAlias": "poCode", "orderNumberEnabled": false, "originOrgIdEnabled": true, "physicalDelete": false, "relationModel": false, "searchModel": false, "shardingConfig": {"enabled": false, "shardingFields": null, "shardingSuffixLength": 3}, "tableName": "pur_po_head_tr", "tenantIdEnabled": false, "type": "PERSIST"}}, "type": "Model"}