{"access": "Private", "key": "test$view5", "name": "View Example 5", "props": {"content": {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "key": "ERP_FIN$SETT_APM_VEND-rSAGy2sgZU6KnOnZoVQMX", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "actionId": "1FyH7ZQ3w4lDZCkxaa5Iw", "text": "是否确认？", "type": "Popconfirm"}], "bindFlowConfig": {"params": [{"elements": [{"fieldAlias": "settDocCode", "fieldName": "结算单编码", "fieldType": "TEXT"}, {"fieldAlias": "settDocTypeId", "fieldName": "结算单类型", "fieldType": "OBJECT"}, {"fieldAlias": "settDocStatus", "fieldName": "结算单状态", "fieldType": "ENUM"}, {"fieldAlias": "comOrgId", "fieldName": "公司组织", "fieldType": "OBJECT"}, {"fieldAlias": "purSlsOrg", "fieldName": "采购/销售组织", "fieldType": "TEXT"}, {"fieldAlias": "purSlsOrgName", "fieldName": "采购/销售组织名称", "fieldType": "TEXT"}, {"fieldAlias": "partnerType", "fieldName": "结算对象类型", "fieldType": "ENUM"}, {"fieldAlias": "partnerCode", "fieldName": "结算对象", "fieldType": "TEXT"}, {"fieldAlias": "partnerId", "fieldName": "结算对象id", "fieldType": "NUMBER"}, {"fieldAlias": "partner<PERSON>ame", "fieldName": "结算对象名称", "fieldType": "TEXT"}, {"fieldAlias": "settDate", "fieldName": "结算日期", "fieldType": "DATE"}, {"fieldAlias": "settDocAmt", "fieldName": "结算金额", "fieldType": "NUMBER"}, {"fieldAlias": "settBaseAmt", "fieldName": "结算金额-本位币", "fieldType": "NUMBER"}, {"fieldAlias": "docCurrId", "fieldName": "单据币别", "fieldType": "OBJECT"}, {"fieldAlias": "baseCurrId", "fieldName": "本位币别", "fieldType": "OBJECT"}, {"fieldAlias": "exchRate", "fieldName": "汇率", "fieldType": "NUMBER"}, {"fieldAlias": "remark", "fieldName": "备注", "fieldType": "TEXT"}, {"fieldAlias": "settItems", "fieldName": "结算项", "fieldType": "Array"}, {"fieldAlias": "tradingDocStatus", "fieldName": "往来单据状态", "fieldType": "ENUM"}, {"fieldAlias": "tradingDocCode", "fieldName": "往来单据编号", "fieldType": "TEXT"}, {"fieldAlias": "sdcType", "fieldName": "执行汇单规则", "fieldType": "ENUM"}, {"fieldAlias": "extLatestPiSbDate", "fieldName": "最晚发票日期", "fieldType": "DATE"}, {"fieldAlias": "extBizGroup", "fieldName": "合同权限筛选组", "fieldType": "TEXT"}, {"fieldAlias": "ext<PERSON><PERSON><PERSON>", "fieldName": "相关方数据权限", "fieldType": "TEXT"}, {"fieldAlias": "approveStatus", "fieldName": "审批状态", "fieldType": "ENUM"}, {"fieldAlias": "clientSideConfirmStatus", "fieldName": "端侧确认状态", "fieldType": "ENUM"}, {"fieldAlias": "purSlsOrgId", "fieldName": "采购/销售组织id", "fieldType": "NUMBER"}, {"fieldAlias": "id", "fieldName": "ID", "fieldType": "NUMBER", "valueConfig": {"expression": "<PERSON><PERSON><PERSON><PERSON>", "type": "expression"}}, {"fieldAlias": "created<PERSON>y", "fieldName": "创建人", "fieldType": "OBJECT"}, {"fieldAlias": "updatedBy", "fieldName": "更新人", "fieldType": "OBJECT"}, {"fieldAlias": "createdAt", "fieldName": "创建时间", "fieldType": "DATE"}, {"fieldAlias": "updatedAt", "fieldName": "更新时间", "fieldType": "DATE"}, {"fieldAlias": "version", "fieldName": "版本号", "fieldType": "NUMBER"}, {"fieldAlias": "deleted", "fieldName": "逻辑删除标识", "fieldType": "NUMBER"}, {"fieldAlias": "originOrgId", "fieldName": "所属组织", "fieldType": "NUMBER"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "ERP_FIN$sett_doc_tr", "required": true}], "service": "ERP_FIN$SETT_DOC_CLIENT_AR_AP_EVENT_SERVICE"}, "endLogicOtherConfig": [{"action": "Refresh", "target": ["ERP_FIN$SETT_APM_VEND-SpbByRMXp3ruNPPhw6lb-", "ERP_FIN$SETT_APM_VEND-TERP_MIGRATE$FIN_ARM_230706-list", "ERP_FIN$SETT_APM_VEND-TERP_MIGRATE$FIN_ARM_230706-table-container-ERP_FIN$fin_arm_ar_head_tr"]}, {"action": "Message", "level": "success", "message": "结算单成功确认"}], "executeLogic": "BindFlow"}, "buttonType": "default", "confirmOn": "off", "disabled": {"items": [{"conditionGroup": {"conditions": [{"conditions": [{"id": "eEv9DIz0MGix6Ml5_bRAk", "leftValue": {"fieldType": "Text", "scope": "context", "val": "selectedKeys.length"}, "operator": "EQ", "rightValue": {"constValue": "0", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "tlS5tP7XeZoEswK3pMJVd", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "dpDC87kh5XYZRQaM2wQzW", "logicOperator": "OR", "type": "ConditionGroup"}, "disabled": true, "resultType": "normal"}], "type": "Conditions"}, "isMultiple": true, "label": "确认", "permissionKey": "ERP_FIN$SETT_APM_VEND-list_perm_ac_z_0_0_0_0_0", "showCondition": {}, "type": "default"}, "type": "Widget"}], "key": "ERP_FIN$SETT_APM_VEND-QTWbtJeAHSFpx4hgkyMO7", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-jw0Z496ucwv7cvhfNcg0_", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-BATG1N8Iunu8bNJcVyYO6", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "ERP_FIN$SETT_APM_VEND-HtnSm0X0GLg9en9rYvU0X", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_FIN$SETT_APM_VEND-q3qbR9X3Ao2htEJGxchh6", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "ERP_FIN$SETT_APM_VEND:aH-UW-UjsBGKJYcQX6YUt", "name": "sett-doc-detail", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "label": "查看", "permissionKey": "ERP_FIN$SETT_APM_VEND-list_perm_ac_z_0_0_0_1_0", "showCondition": {}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-TX35FVNQ08DjjlqLMVwtf", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "是否确认？", "type": "Popconfirm"}], "bindFlowConfig": {"params": [{"elements": [{"fieldAlias": "id", "fieldName": "ID", "fieldType": "NUMBER", "valueConfig": {"expression": "record.id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "ERP_FIN$sett_doc_tr"}], "service": "ERP_FIN$SETT_DOC_CLIENT_AR_AP_ONE_EVENT_SERVICE"}, "endLogicOtherConfig": [{"action": "Message", "level": "success", "message": "结算单成功确认"}, {"action": "Refresh", "target": ["ERP_FIN$SETT_APM_VEND-SpbByRMXp3ruNPPhw6lb-", "ERP_FIN$SETT_APM_VEND-TERP_MIGRATE$FIN_ARM_230706-table-container-ERP_FIN$fin_arm_ar_head_tr"]}], "executeLogic": "BindFlow"}, "buttonType": "default", "confirmOn": "off", "label": "确认", "permissionKey": "ERP_FIN$SETT_APM_VEND-list_perm_ac_z_0_0_0_1_1", "showCondition": {}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-Gjv1_5ASGULM3pNIF9FMh", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "是否确认退回？", "type": "Popconfirm"}], "bindFlowConfig": {"params": [{"elements": [{"fieldAlias": "settDocCode", "fieldName": "结算单编码", "fieldType": "TEXT"}, {"fieldAlias": "settDocTypeId", "fieldName": "结算单类型", "fieldType": "OBJECT"}, {"fieldAlias": "settDocStatus", "fieldName": "结算单状态", "fieldType": "ENUM"}, {"fieldAlias": "comOrgId", "fieldName": "公司组织", "fieldType": "OBJECT"}, {"fieldAlias": "purSlsOrg", "fieldName": "采购/销售组织", "fieldType": "TEXT"}, {"fieldAlias": "purSlsOrgName", "fieldName": "采购/销售组织名称", "fieldType": "TEXT"}, {"fieldAlias": "partnerType", "fieldName": "结算对象类型", "fieldType": "ENUM"}, {"fieldAlias": "partnerCode", "fieldName": "结算对象", "fieldType": "TEXT"}, {"fieldAlias": "partnerId", "fieldName": "结算对象id", "fieldType": "NUMBER"}, {"fieldAlias": "partner<PERSON>ame", "fieldName": "结算对象名称", "fieldType": "TEXT"}, {"fieldAlias": "settDate", "fieldName": "结算日期", "fieldType": "DATE"}, {"fieldAlias": "settDocAmt", "fieldName": "结算金额", "fieldType": "NUMBER"}, {"fieldAlias": "settBaseAmt", "fieldName": "结算金额-本位币", "fieldType": "NUMBER"}, {"fieldAlias": "docCurrId", "fieldName": "单据币别", "fieldType": "OBJECT"}, {"fieldAlias": "baseCurrId", "fieldName": "本位币别", "fieldType": "OBJECT"}, {"fieldAlias": "exchRate", "fieldName": "汇率", "fieldType": "NUMBER"}, {"fieldAlias": "remark", "fieldName": "备注", "fieldType": "TEXT"}, {"fieldAlias": "settItems", "fieldName": "结算项", "fieldType": "Array"}, {"fieldAlias": "tradingDocStatus", "fieldName": "往来单据状态", "fieldType": "ENUM"}, {"fieldAlias": "tradingDocCode", "fieldName": "往来单据编号", "fieldType": "TEXT"}, {"fieldAlias": "sdcType", "fieldName": "执行汇单规则", "fieldType": "ENUM"}, {"fieldAlias": "extLatestPiSbDate", "fieldName": "最晚发票日期", "fieldType": "DATE"}, {"fieldAlias": "extBizGroup", "fieldName": "合同权限筛选组", "fieldType": "TEXT"}, {"fieldAlias": "ext<PERSON><PERSON><PERSON>", "fieldName": "相关方数据权限", "fieldType": "TEXT"}, {"fieldAlias": "approveStatus", "fieldName": "审批状态", "fieldType": "ENUM"}, {"fieldAlias": "clientSideConfirmStatus", "fieldName": "端侧确认状态", "fieldType": "ENUM"}, {"fieldAlias": "purSlsOrgId", "fieldName": "采购/销售组织id", "fieldType": "NUMBER"}, {"fieldAlias": "id", "fieldName": "ID", "fieldType": "NUMBER", "valueConfig": {"expression": "record?.id", "type": "expression"}}, {"fieldAlias": "created<PERSON>y", "fieldName": "创建人", "fieldType": "OBJECT"}, {"fieldAlias": "updatedBy", "fieldName": "更新人", "fieldType": "OBJECT"}, {"fieldAlias": "createdAt", "fieldName": "创建时间", "fieldType": "DATE"}, {"fieldAlias": "updatedAt", "fieldName": "更新时间", "fieldType": "DATE"}, {"fieldAlias": "version", "fieldName": "版本号", "fieldType": "NUMBER"}, {"fieldAlias": "deleted", "fieldName": "逻辑删除标识", "fieldType": "NUMBER"}, {"fieldAlias": "originOrgId", "fieldName": "所属组织", "fieldType": "NUMBER"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "ERP_FIN$sett_doc_tr", "required": true}], "service": "ERP_FIN$SETT_DOC_RETURNED_EVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "ConditionFlowAction", "children": [{"action": "Message", "level": "success", "message": "已成功退回"}, {"action": "Refresh", "target": ["ERP_FIN$SETT_APM_VEND-SpbByRMXp3ruNPPhw6lb-"]}], "condition": {"conditions": [], "id": "fCv7Xp-3LozvlsRjC0wT-", "logicOperator": "OR", "type": "ConditionGroup"}}], "executeLogic": "BindFlow"}, "buttonType": "default", "confirmOn": "off", "label": "退回", "permissionKey": "ERP_FIN$SETT_APM_VEND-list_perm_ac_z_0_0_0_1_2", "showCondition": {"conditions": [{"conditions": [{"id": "HYXTopxIJoTNQ4l4uImGQ", "leftValue": {"fieldType": "Enum", "scope": "row", "title": "端侧确认状态", "type": "VarValue", "val": "clientSideConfirmStatus", "value": "ERP_FIN$sett_doc_tr.clientSideConfirmStatus", "valueType": "VAR", "varVal": "clientSideConfirmStatus", "varValue": [{"valueKey": "clientSideConfirmStatus", "valueName": "clientSideConfirmStatus"}]}, "operator": "NEQ", "rightValue": {"constValue": "RETURNED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "0cvcFJJH8eLXP2cCO-DNx", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "nbCzgvgYu0owjZby0d982", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}], "key": "ERP_FIN$SETT_APM_VEND-vy6DK0rPwVieEuowrNuvt", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_FIN$SETT_APM_VEND-yoNXqE2-SY9CFBNNvfb3h", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "ERP_FIN$SETT_APM_VEND-p-eI-Lx-NeNJwf1v8ZkYg", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_FIN$SETT_APM_VEND-tCTyTicRujAj_0E0VLbdf", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "ERP_FIN$SETT_APM_VEND-WWcBHYP8CBAsYFljSWPx4", "name": "TableTitleActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_FIN$SETT_APM_VEND-tmbEAZKLZ5m6OWFt3h5cO", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "comOrgId", "label": "选择公司组织", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_FIN$sett_doc_tr", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_STRUCT_MD_FIND_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "labelField": ["orgName"], "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"modelAlias": "sys_common$org_struct_md"}, "hidden": false, "label": "采购方", "modelAlias": "sys_common$org_struct_md", "name": "comOrgId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-NwvXvYuDWirXFdDNMUjKA", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "settDocCode", "modelAlias": "ERP_FIN$sett_doc_tr", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "ERP_FIN$sett_doc_tr"}, "displayComponentType": "Text", "editComponentProps": {"modelAlias": "ERP_FIN$sett_doc_tr"}, "hidden": false, "label": "结算单编码", "modelAlias": "ERP_FIN$sett_doc_tr", "name": "settDocCode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-DRR4DXTy7Qzz-4skNhKPW", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "settDocTypeId", "labelField": "settDocTypeName", "modelAlias": "ERP_GEN$gen_sett_doc_type_cf", "parentModelAlias": "ERP_FIN$sett_doc_tr", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_sett_doc_type_cf", "serviceKey": "ERP_GEN$GEN_SETT_DOC_TYPE_CF_FIND_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "labelField": ["settDocTypeName"], "modelAlias": "ERP_GEN$gen_sett_doc_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "settDocTypeCode", "parentModelAlias": "ERP_GEN$gen_sett_doc_type_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "结算单类型编号", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "qxVvqvN5tFHjdEUEMVMYs", "trigger": "auto", "valueRules": null}], "name": "settDocTypeCode", "type": "TEXT"}, {"componentProps": {"fieldAlias": "settDocTypeName", "parentModelAlias": "ERP_GEN$gen_sett_doc_type_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "结算单类型名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "Jtjmwn_TglQeEVlJbb-p6", "trigger": "auto", "valueRules": null}], "name": "settDocTypeName", "type": "TEXT"}, {"componentProps": {"fieldAlias": "settClass", "modelAlias": "ERP_GEN$gen_sett_doc_type_cf", "parentModelAlias": "ERP_GEN$gen_sett_doc_type_cf", "placeholder": "请选择"}, "editComponentProps": {}, "editComponentType": "Select", "label": "业务性质", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "td4s58gEkFWOcj4CoY9UT", "trigger": "auto", "valueRules": null}], "name": "settClass", "type": "SELECT"}, {"componentProps": {"fieldAlias": "btClass", "modelAlias": "ERP_GEN$gen_sett_doc_type_cf", "parentModelAlias": "ERP_GEN$gen_sett_doc_type_cf", "placeholder": "请选择"}, "editComponentProps": {}, "editComponentType": "Select", "label": "业务类别", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "a9Ly5RXvswctTn_fbIW_N", "trigger": "auto", "valueRules": null}], "name": "btClass", "type": "SELECT"}, {"componentProps": {"fieldAlias": "collaborativeConfirmationMethod", "modelAlias": "ERP_GEN$gen_sett_doc_type_cf", "parentModelAlias": "ERP_GEN$gen_sett_doc_type_cf", "placeholder": "请选择"}, "editComponentProps": {}, "editComponentType": "Select", "label": "端侧协同确认方式", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "NgpPvvmwv8c3UWR82LYDk", "trigger": "auto", "valueRules": null}], "name": "collaborativeConfirmationMethod", "type": "SELECT"}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_sett_doc_type_cf", "serviceKey": "ERP_GEN$GEN_SETT_DOC_TYPE_CF_FIND_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_sett_doc_type_cf", "serviceKey": "ERP_GEN$GEN_SETT_DOC_TYPE_CF_PAGING_DATA_SERVICE", "type": "InvokeService"}, "modelAlias": "ERP_GEN$gen_sett_doc_type_cf"}, "editComponentType": "RelationSelect", "hidden": false, "label": "结算单类型", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "MWYE7xBfdZGHgOT2tWDw8", "trigger": "auto", "valueRules": null}], "modelAlias": "ERP_GEN$gen_sett_doc_type_cf", "name": "settDocTypeId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-hzH9Xeg_9i6IFXg9zEmuX", "name": "Field", "props": {"componentProps": {"fieldAlias": "sdcType", "modelAlias": "ERP_FIN$sett_doc_tr", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "ERP_FIN$sett_doc_tr"}, "editComponentProps": {"modelAlias": "ERP_FIN$sett_doc_tr"}, "hidden": false, "label": "执行汇单规则", "name": "sdcType", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-o4JOfBdD07k1jOixYCfbs", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "settDate", "modelAlias": "ERP_FIN$sett_doc_tr", "placeholder": "请选择"}, "displayComponentProps": {"format": "YYYY-MM-DD", "modelAlias": "ERP_FIN$sett_doc_tr"}, "displayComponentType": "Date", "editComponentProps": {"modelAlias": "ERP_FIN$sett_doc_tr"}, "hidden": false, "label": "结算日期", "modelAlias": "ERP_FIN$sett_doc_tr", "name": "settDate", "required": false, "type": "DATE", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-fXFOIItZUt0MYIH-Lhd7V", "name": "Field", "props": {"componentProps": {"fieldAlias": "settDocAmt", "modelAlias": "ERP_FIN$sett_doc_tr", "placeholder": "请输入", "precision": 2}, "displayComponentProps": {"modelAlias": "ERP_FIN$sett_doc_tr"}, "editComponentProps": {"modelAlias": "ERP_FIN$sett_doc_tr"}, "hidden": false, "label": "结算金额", "name": "settDocAmt", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-Wgj8cj6h1xmHxTtZyx8pN", "name": "Field", "props": {"componentProps": {"fieldAlias": "remark", "modelAlias": "ERP_FIN$sett_doc_tr", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "ERP_FIN$sett_doc_tr"}, "editComponentProps": {"modelAlias": "ERP_FIN$sett_doc_tr"}, "hidden": false, "label": "备注", "name": "remark", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-OEJhg5FcxgyTgN4lmNA4t", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_FIN$sett_doc_tr", "placeholder": "请选择"}, "displayComponentProps": {"format": "YYYY-MM-DD HH:mm:ss", "modelAlias": "ERP_FIN$sett_doc_tr"}, "displayComponentType": "Date", "editComponentProps": {"modelAlias": "ERP_FIN$sett_doc_tr"}, "hidden": false, "label": "创建时间", "modelAlias": "ERP_FIN$sett_doc_tr", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-27YjeK5s_s-k7v_jAxmUP", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "clientSideConfirmStatus", "modelAlias": "ERP_FIN$sett_doc_tr", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal", "modelAlias": "ERP_FIN$sett_doc_tr", "options": [{"color": "default", "label": "待确认", "value": "PENDING_CONFIRMED"}, {"color": "success", "label": "已确认", "value": "CONFIRMED"}, {"color": "default", "label": "已退回", "value": "RETURNED"}]}, "displayComponentType": "Enum", "editComponentProps": {"modelAlias": "ERP_FIN$sett_doc_tr"}, "label": "供应方确认状态", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "LNtXUqyaTBji3aMtptIqF", "valueRules": null}], "modelAlias": "ERP_FIN$sett_doc_tr", "name": "clientSideConfirmStatus", "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-8KAhD5NbZBaFabtxm8Qlh", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "settDocStatus", "modelAlias": "ERP_FIN$sett_doc_tr", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal", "modelAlias": "ERP_FIN$sett_doc_tr", "options": [{"color": "default", "label": "已创建", "value": "CREATED"}, {"color": "success", "label": "已确认", "value": "CONFIRMED"}, {"color": "default", "label": "已退回", "value": "RETURNED"}]}, "displayComponentType": "Enum", "editComponentProps": {"modelAlias": "ERP_FIN$sett_doc_tr"}, "label": "需求方确认状态", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "eHaPzBNaTMfZNpj4T6drC", "valueRules": null}], "modelAlias": "ERP_FIN$sett_doc_tr", "name": "settDocStatus", "type": "SELECT", "width": 120}, "type": "Widget"}], "key": "ERP_FIN$SETT_APM_VEND-_LaH5VhvAwmVkAj0bOsr-", "name": "Fields", "props": {}, "type": "Meta"}, {"children": [{"children": [], "key": "ERP_FIN$SETT_APM_VEND-1c4aCoO3tvu56KXfDJR9J", "name": "FilterField", "props": {"componentProps": {"fieldAlias": "comOrgId", "label": "选择公司组织", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_FIN$sett_doc_tr", "placeholder": "请选择"}, "editComponentProps": {"buttonConfig": {}, "fields": [{"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "isRelationColumn": true, "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "dIHxfdXiINMqQXqQ5gmd2", "valueRules": null}], "name": "orgName", "type": "TEXT", "width": 120}, {"align": "left", "componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "isRelationColumn": true, "label": "组织编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "EB0WsAY4bHZyQLddH4mpl", "valueRules": null}], "name": "orgCode", "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "NYM5OsmzoCmb_4K0xrlOJ", "valueRules": null}], "name": "orgName", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentType": "InputText", "filterType": "fuzzy", "label": "组织编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "B_eCKmn0Ahh7ZJOnHWOha", "valueRules": null}], "name": "orgCode", "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_STRUCT_MD_FIND_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "orgStatus", "fieldName": "orgStatus", "fieldType": "Text", "valueConfig": {"type": "const", "value": "ENABLED"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "type": "InvokeService"}, "label": "选择采购方", "labelField": ["orgName"], "modalProps": {"size": "middle", "width": 720}, "mode": "single", "modelAlias": "sys_common$org_struct_md", "pagination": {"defaultPageSize": 20, "pageSizeOptions": [20, 50, 100, 200]}, "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "Svwj9QOcb3LWqGdMijidt", "leftValue": {"fieldType": "Enum", "title": "状态", "type": "VarValue", "val": "orgStatus", "value": "sys_common$org_struct_md.orgStatus", "valueType": "VAR", "varVal": "orgStatus", "varValue": [{"valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "vZL-rgrpCmczoVrNxtdJp", "leftValue": {"fieldType": "Text", "title": "业务类型编码集合", "type": "VarValue", "val": "orgBusinessTypeCodes", "value": "sys_common$org_struct_md.orgBusinessTypeCodes", "valueType": "VAR", "varVal": "orgBusinessTypeCodes", "varValue": [{"valueKey": "orgBusinessTypeCodes", "valueName": "orgBusinessTypeCodes"}]}, "operator": "CONTAINS", "rightValue": {"constValue": "COM_ORG", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "-t3Q-vfbZpsVqN3bprkHj", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "lholecYmvT3r-uYSz-2wL", "logicOperator": "OR", "type": "ConditionGroup"}}, "editComponentType": "RelationSelect", "hidden": false, "label": "采购方", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "lC25maNGDX5t2duU1-s79", "valueRules": null}], "name": "comOrgId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-9wHxmLRXKUDBp59Q3FgPF", "name": "FilterField", "props": {"componentProps": {"fieldAlias": "settDate", "modelAlias": "ERP_FIN$sett_doc_tr", "placeholder": "请选择"}, "editComponentProps": {"format": "YYYY-MM-DD", "quickRanges": ["yesterday", "last-week", "current-month", "next-month"]}, "editComponentType": "DateRangePicker", "hidden": false, "label": "结算日期", "name": "settDate", "required": false, "type": "DATE", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-vjiJLcgiVW04cAbj9SICg", "name": "FilterField", "props": {"componentProps": {"fieldAlias": "settDocCode", "modelAlias": "ERP_FIN$sett_doc_tr", "placeholder": "请输入"}, "hidden": false, "label": "结算单编码", "name": "settDocCode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "ERP_FIN$SETT_APM_VEND-Ksa8xiMug6yHNRnqicaPA", "name": "Filter<PERSON>ields", "props": {}, "type": "Meta"}], "key": "ERP_FIN$SETT_APM_VEND-SpbByRMXp3ruNPPhw6lb-", "name": "Table", "props": {"acceptFilterQuery": true, "allowClickRowSelect": true, "allowRowSelect": true, "enableSolution": false, "filterFields": [{"componentProps": {"fieldAlias": "comOrgId", "label": "选择公司组织", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_FIN$sett_doc_tr", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "sys_common$org_struct_md"}, "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "isRelationColumn": true, "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "dIHxfdXiINMqQXqQ5gmd2", "valueRules": null}], "name": "orgName", "type": "TEXT", "width": 120}, {"align": "left", "componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "isRelationColumn": true, "label": "组织编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "EB0WsAY4bHZyQLddH4mpl", "valueRules": null}], "name": "orgCode", "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "NYM5OsmzoCmb_4K0xrlOJ", "valueRules": null}], "name": "orgName", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentType": "InputText", "filterType": "fuzzy", "label": "组织编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "B_eCKmn0Ahh7ZJOnHWOha", "valueRules": null}], "name": "orgCode", "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_STRUCT_MD_FIND_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "orgStatus", "fieldName": "orgStatus", "fieldType": "Text", "valueConfig": {"type": "const", "value": "ENABLED"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "type": "InvokeService"}, "labelField": ["orgName"], "modalProps": {"size": "middle", "width": 720}, "mode": "single", "modelAlias": "sys_common$org_struct_md", "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "Svwj9QOcb3LWqGdMijidt", "leftValue": {"fieldType": "Enum", "title": "状态", "type": "VarValue", "val": "orgStatus", "value": "sys_common$org_struct_md.orgStatus", "valueType": "VAR", "varVal": "orgStatus", "varValue": [{"valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "vZL-rgrpCmczoVrNxtdJp", "leftValue": {"fieldType": "Text", "title": "业务类型编码集合", "type": "VarValue", "val": "orgBusinessTypeCodes", "value": "sys_common$org_struct_md.orgBusinessTypeCodes", "valueType": "VAR", "varVal": "orgBusinessTypeCodes", "varValue": [{"valueKey": "orgBusinessTypeCodes", "valueName": "orgBusinessTypeCodes"}]}, "operator": "CONTAINS", "rightValue": {"constValue": "COM_ORG", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "-t3Q-vfbZpsVqN3bprkHj", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "lholecYmvT3r-uYSz-2wL", "logicOperator": "OR", "type": "ConditionGroup"}}, "editComponentType": "RelationSelect", "hidden": false, "label": "采购方", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "lC25maNGDX5t2duU1-s79", "valueRules": null}], "name": "comOrgId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "settDate", "modelAlias": "ERP_FIN$sett_doc_tr", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "ERP_FIN$sett_doc_tr"}, "editComponentProps": {"format": "YYYY-MM-DD", "modelAlias": "ERP_FIN$sett_doc_tr"}, "editComponentType": "DateRangePicker", "hidden": false, "label": "结算日期", "name": "settDate", "required": false, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "settDocCode", "modelAlias": "ERP_FIN$sett_doc_tr", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "ERP_FIN$sett_doc_tr"}, "editComponentProps": {"modelAlias": "ERP_FIN$sett_doc_tr"}, "hidden": false, "label": "结算单编码", "name": "settDocCode", "required": false, "type": "TEXT", "width": 120}], "flow": {"context$": "$context", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "分页设置", "fieldType": "Pageable", "required": null}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "required": null}], "serviceKey": "ERP_FIN$SETT_DOC_QUERY_VEND_SERVICE", "type": "InvokeService"}, "isLabelManualModified": true, "isProFilter": false, "label": "", "mode": "normal", "modelAlias": "ERP_FIN$sett_doc_tr", "pagination": {"defaultPageSize": 20, "pageSizeOptions": [20, 50, 100, 200]}, "selectType": "multiple", "showConfigure": false, "showFilterFields": true, "showScope": "all", "showType": "normal", "sortOrders": [{"fieldAlias": "createdAt", "id": "createdAt-0", "sortType": "DESC"}], "subTableConfig": {}, "tableCondition": null, "tableConditionContext$": null}, "type": "Container"}], "key": "ERP_FIN$SETT_APM_VEND-TuHn5rmj-t00e3qBDUJQx", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "ERP_FIN$SETT_APM_VEND-TERP_MIGRATE$FIN_ARM_230706-record-actions-1-button-1", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "show"}]}, "buttonType": "default", "confirmOn": "off", "label": "查看", "permissionKey": "ERP_FIN$SETT_APM_VEND-list_perm_ac_z_0_1_0_0_0", "showCondition": {}, "type": "default"}}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-ztH6WZvmTScaqqllbBf4V", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "是否确认生成发票？", "type": "Popconfirm"}], "bindFlowConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "required": true, "valueConfig": {"expression": "record", "type": "expression"}}], "service": "ERP_FIN$PI_CREATE_BY_AP_EVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_FIN$SETT_APM_VEND-TERP_MIGRATE$FIN_ARM_230706-list"}, {"action": "Message", "message": "发票生成成功"}], "executeLogic": "BindFlow"}, "buttonKey": "button_xuQ1A9VYQmycmft8XQka", "buttonType": "default", "confirmOn": "off", "label": "开票", "permissionKey": "ERP_FIN$SETT_APM_VEND-list_perm_ac_z_0_1_0_0_1", "showCondition": {"conditions": [{"conditions": [{"id": "CzzAXjEd0rZfsDpce6T5d", "leftValue": {"fieldType": "Number", "scope": "row", "title": "ID(id)", "type": "VarValue", "val": "id", "value": "ERP_FIN$fin_apm_ap_head_tr.id", "valueType": "VAR", "varVal": "id", "varValue": [{"valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"constValue": "-1", "fieldType": "Number", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "Faeab2KHRQFEXU3Nw1XsN", "logicOperator": "AND", "type": "ConditionGroup"}], "key": "DjcklbOiB9XveoHSEmLmf", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}], "key": "ERP_FIN$SETT_APM_VEND-TERP_MIGRATE$FIN_ARM_230706-record-actions-1", "name": "RecordActions", "props": {"label": "操作"}}, {"children": [{"children": [{"children": [], "key": "ERP_FIN$SETT_APM_VEND-xH7QKjMA1kLvjc5_cv-PZ", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogicOtherConfig": [], "executeLogic": "ExecuteScript", "executeScriptConfig": "invokeServiceByKey('ERP_FIN$BATCH_AP_CONVERT_TO_PI_VAL_SERVICE', {request:{apmApItemIds:selectedKeys}}, {nowrap: true}).then(res => {\n  if (res.success) {\n    navigate({viewKey:'ERP_FIN$SETT_APM_VEND:pkQqXyEVhkP0HpqdAMQC5',sceneKey:'ERP_FIN$SETT_APM_VEND',\n              query:{\n                filterParams:JSON.stringify({\n                  apmApHeadTrId:selectedKeys,\n                  itemClearingStatus:[\"UNCLEARED\", \"CLEAR_PART\"]\n                })\n              }\n             }\n            )    \n  }\n})", "scriptUsedFlows": [{"useFlow": {"serviceKey": "ERP_FIN$BATCH_AP_CONVERT_TO_PI_VAL_SERVICE", "type": "InvokeService"}}]}, "buttonType": "default", "confirmOn": "off", "disabled": {"items": [{"conditionGroup": {"conditions": [{"conditions": [{"id": "xjbkVhPZM7OHjtbBuICAo", "leftValue": {"fieldType": "Text", "scope": "context", "val": "selectedKeys.length"}, "operator": "EQ", "rightValue": {"constValue": "0", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "n6uEb9oUnJj7VfbLe49KS", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "9Ef0rTShA_yh7k1XWjvOf", "logicOperator": "OR", "type": "ConditionGroup"}, "disabled": true, "resultType": "normal"}], "type": "Conditions"}, "isMultiple": true, "label": "批量开具发票", "permissionKey": "ERP_FIN$SETT_APM_VEND-list_perm_ac_z_0_1_0_1_0", "showCondition": {}, "type": "default"}, "type": "Widget"}], "key": "ERP_FIN$SETT_APM_VEND-XEMW_3La7jpKXbdyqrVuS", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-6OfbGwNfanG7EcHFikVLn", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-RbRMwUOvhJqK__u9rbhvi", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "ERP_FIN$SETT_APM_VEND-TERP_MIGRATE$FIN_ARM_230706-batch-actions-1", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_FIN$SETT_APM_VEND-9qBsmq3SC7dUHUkPqFNw7", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "ERP_FIN$SETT_APM_VEND-YUYcdzAJW48n_I6wLvpKb", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_FIN$SETT_APM_VEND-2HYkbTnJMZt_pxOo82Obb", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "ERP_FIN$SETT_APM_VEND-XjLo2PGpG1QIoLEL9yZ61", "name": "TableTitleActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_FIN$SETT_APM_VEND-en3Le5_NgDzANnIINnUYe", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "comOrgId", "label": "选择公司组织", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_FIN$fin_apm_ap_head_tr", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_STRUCT_MD_FIND_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"modelAlias": "sys_common$org_struct_md"}, "label": "采购方", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "BF9PQeZ0P0mhC8lgu9ORs", "valueRules": null}], "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "name": "comOrgId", "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-vvdsxRMu1-j77lvu-WQKj", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "reSetCode", "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr"}, "displayComponentType": "Text", "editComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr"}, "label": "结算单编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "AEg2xsdKHbwULlWxXQjRb", "valueRules": null}], "modelAlias": "ERP_FIN$fin_apm_ap_type_md", "name": "reSetCode", "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-WFT2xdZJOxM_wlGqYzRgz", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "settDocTypeId", "label": "选择结算单类型", "labelField": "settDocTypeName", "modelAlias": "ERP_GEN$gen_sett_doc_type_cf", "parentModelAlias": "ERP_FIN$sett_doc_tr", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal", "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_sett_doc_type_cf", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_GEN$GEN_SETT_DOC_TYPE_CF_FIND_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "modelAlias": "ERP_GEN$gen_sett_doc_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"modelAlias": "ERP_GEN$gen_sett_doc_type_cf"}, "label": "结算单类型", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "LDhmzC6dBA788HOphf2jF", "valueRules": null}], "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "name": "reSetId.settDocTypeId", "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-5p_CxWpJ6wpKlYgt6tdUA", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "sdcType", "modelAlias": "ERP_FIN$sett_doc_tr", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal", "modelAlias": "ERP_FIN$sett_doc_tr"}, "displayComponentType": "Enum", "editComponentProps": {"modelAlias": "ERP_FIN$sett_doc_tr"}, "label": "执行汇单规则", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "4IFzZhMls6R4q5XJsv8MK", "valueRules": null}], "modelAlias": "sys_common$org_struct_md", "name": "reSetId.sdcType", "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-kwPYegO3fJOX6wByIG0mB", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "apDate", "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "placeholder": "请选择"}, "displayComponentProps": {"format": "YYYY-MM-DD", "modelAlias": "ERP_FIN$fin_apm_ap_head_tr"}, "displayComponentType": "Date", "editComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr"}, "label": "结算日期", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "ijZMwDcUrZ-OGBAsUdVJG", "valueRules": null}], "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "name": "apDate", "type": "DATE", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-ioLuz36IID-UtgIau3aEK", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "grossDocAmt", "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "placeholder": "请输入", "precision": 6}, "displayComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "precision": 6}, "displayComponentType": "Number", "editComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr"}, "label": "结算金额", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "McHtBKXYBYX7qnqOBY9S5", "valueRules": null}], "name": "grossDocAmt", "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-WaBAb6FbLBVMJ6O5iI6eH", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "remark", "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr"}, "displayComponentType": "Text", "editComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr"}, "label": "备注", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "xvt8XA5OB7WaQu1c6PQ2G", "valueRules": null}], "name": "remark", "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-ZBCveBejUb5pUtys5XoHI", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "payClearingStatus", "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal", "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "options": [{"color": "default", "label": "未钩稽", "value": "UNCLEARED"}, {"color": "default", "label": "部分钩稽", "value": "CLEAR_PART"}, {"color": "success", "label": "已钩稽", "value": "CLEARED"}]}, "displayComponentType": "Enum", "editComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr"}, "label": "付款钩稽状态", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "_SsUvsjPUUmhtSZl6j4Y6", "valueRules": null}], "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "name": "payClearingStatus", "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-rI8rw6iLTO9EEjwxAMB8U", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "paypaidDocAmt", "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "placeholder": "请输入", "precision": 6}, "displayComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "precision": 6}, "displayComponentType": "Number", "editComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr"}, "label": "已付款金额", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "jFgN05qIqx1IQBs-m_9O7", "valueRules": null}], "name": "paypaidDocAmt", "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-EO7rNvFvqXVf85cU-NTQ1", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "payingDocAmt", "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "placeholder": "请输入", "precision": 6}, "displayComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "precision": 6}, "displayComponentType": "Number", "editComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr"}, "label": "付款中金额", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "XFTpP-n9Kez7gQm3w_aD6", "valueRules": null}], "name": "payingDocAmt", "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-8GN-jGO3aDrkHO9pZm1Q1", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "unpaidDocAmt", "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "placeholder": "请输入", "precision": 6}, "displayComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "precision": 6}, "displayComponentType": "Number", "editComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr"}, "label": "未付款金额", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "ddyI7nuXWovYXBU8sNocU", "valueRules": null}], "name": "unpaidDocAmt", "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-ZId5FAaEDAvpCR8TV5kCH", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "invClearingStatus", "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal", "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "options": [{"color": "default", "label": "未钩稽", "value": "UNCLEARED"}, {"color": "default", "label": "部分钩稽", "value": "CLEAR_PART"}, {"color": "success", "label": "已钩稽", "value": "CLEARED"}]}, "displayComponentType": "Enum", "editComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr"}, "label": "收票钩稽状态", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "kAlt-SS9wccnoMh4N_mSt", "valueRules": null}], "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "name": "invClearingStatus", "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-18uTLZqJAql8Leesl-FHk", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "invoicedDocAmt", "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "placeholder": "请输入", "precision": 6}, "displayComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "precision": 6}, "displayComponentType": "Number", "editComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr"}, "label": "已收票金额", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "R7UBF1VNFVMvJP0nLp4OC", "valueRules": null}], "name": "invoicedDocAmt", "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-5WRNTJdgEIjoHUPT5d5nJ", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "invoicingDocAmt", "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "placeholder": "请输入", "precision": 6}, "displayComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "precision": 6}, "displayComponentType": "Number", "editComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr"}, "label": "收票中金额", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "-IzRJH42PE0C6c48l5GLK", "valueRules": null}], "name": "invoicingDocAmt", "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-j6Vik3DX5fUYc0pVTu5mj", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "uninvoicedDocAmt", "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "placeholder": "请输入", "precision": 6}, "displayComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "precision": 6}, "displayComponentType": "Number", "editComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr"}, "label": "未收票金额", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "mY1V_1koki9OmHjv99yKT", "valueRules": null}], "name": "uninvoicedDocAmt", "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-S1x0qG8MvIbp4vnmsGJjC", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "placeholder": "请选择"}, "displayComponentProps": {"format": "YYYY-MM-DD HH:mm:ss", "modelAlias": "ERP_FIN$fin_apm_ap_head_tr"}, "displayComponentType": "Date", "editComponentProps": {"modelAlias": "ERP_FIN$fin_apm_ap_head_tr"}, "label": "创建时间", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "NEQ0DfNTegYG1FyEzvKNV", "valueRules": null}], "name": "createdAt", "type": "DATE", "width": 120}, "type": "Widget"}], "key": "ERP_FIN$SETT_APM_VEND-s_KdiERNuO8_wndFisOQh", "name": "Fields", "props": {}, "type": "Meta"}, {"children": [{"children": [], "key": "ERP_FIN$SETT_APM_VEND-qUOOGYqM0j5WVO1A4QfyH", "name": "FilterField", "props": {"componentProps": {"fieldAlias": "comOrgId", "label": "选择公司组织", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_FIN$fin_apm_ap_head_tr", "placeholder": "请选择"}, "editComponentProps": {"buttonConfig": {}, "fields": [{"componentProps": {"fieldAlias": "orgCode", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "isRelationColumn": true, "label": "组织编码", "name": "orgCode", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "orgName", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "isRelationColumn": true, "label": "组织名称", "name": "orgName", "type": "TEXT", "width": 146}], "filterFields": [{"componentProps": {"fieldAlias": "orgCode", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "label": "组织编码", "name": "orgCode", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "orgName", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "rSYA3_AYXx3xtsns50_ib", "trigger": "auto", "valueRules": null}], "name": "orgName", "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_STRUCT_MD_FIND_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "flow": {"context$": "$context", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "orgStatus", "fieldName": "orgStatus", "fieldType": "Text", "valueConfig": {"type": "const", "value": "ENABLED"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "type": "InvokeService"}, "label": "选择采购方", "labelField": ["orgName"], "modalProps": {"size": "middle", "width": 720}, "mode": "single", "modelAlias": "sys_common$org_struct_md", "pagination": {"defaultPageSize": 20, "pageSizeOptions": [20, 50, 100, 200]}, "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "采购方", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "kz8yz8P5xJrWmzsvkU35q", "valueRules": null}], "name": "comOrgId", "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-SKfVFfBSBghZ5ZpstRef_", "name": "FilterField", "props": {"componentProps": {"fieldAlias": "apDate", "parentModelAlias": "ERP_FIN$fin_apm_ap_head_tr", "placeholder": "请选择"}, "editComponentProps": {"format": "YYYY-MM-DD", "quickRanges": ["yesterday", "last-week", "current-month", "next-month"]}, "editComponentType": "DateRangePicker", "label": "业务日期", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "7BXcGzsSj-Bib437kpHBm", "trigger": "auto", "valueRules": null}], "name": "apDate", "type": "DATE", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FIN$SETT_APM_VEND-jdtd4t9jr9eJ7Ho0-rH1e", "name": "FilterField", "props": {"componentProps": {"fieldAlias": "reSetCode", "modelAlias": "ERP_FIN$fin_arm_ar_head_tr", "placeholder": "请输入"}, "editComponentType": "InputText", "filterType": "fuzzy", "label": "结算单编码", "name": "reSetCode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "ERP_FIN$SETT_APM_VEND-4LeFEhjwcOWC2tHdkzbqs", "name": "Filter<PERSON>ields", "props": {}, "type": "Meta"}], "key": "ERP_FIN$SETT_APM_VEND-TERP_MIGRATE$FIN_ARM_230706-table-container-ERP_FIN$fin_arm_ar_head_tr", "name": "Table", "props": {"acceptFilterQuery": true, "allowClickRowSelect": true, "allowRowSelect": true, "enableSolution": false, "filterFields": [{"componentProps": {"fieldAlias": "comOrgId", "label": "选择公司组织", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_FIN$fin_apm_ap_head_tr", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "sys_common$org_struct_md"}, "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "isRelationColumn": true, "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "_pcUH61rGFqCP-Fwpj0XI", "valueRules": null}], "name": "orgName", "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_STRUCT_MD_FIND_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "type": "InvokeService"}, "labelField": ["orgName"], "modalProps": {"size": "middle", "width": 720}, "mode": "single", "modelAlias": "sys_common$org_struct_md", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "采购方", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "kz8yz8P5xJrWmzsvkU35q", "valueRules": null}], "name": "comOrgId", "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "arDate", "modelAlias": "ERP_FIN$fin_arm_ar_head_tr", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "ERP_FIN$fin_arm_ar_head_tr"}, "editComponentProps": {"modelAlias": "ERP_FIN$fin_arm_ar_head_tr"}, "editComponentType": "DateRangePicker", "label": "结算日期", "name": "arDate", "type": "DATE"}, {"componentProps": {"fieldAlias": "reSetCode", "modelAlias": "ERP_FIN$fin_arm_ar_head_tr", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "ERP_FIN$fin_arm_ar_head_tr"}, "editComponentProps": {"modelAlias": "ERP_FIN$fin_arm_ar_head_tr"}, "editComponentType": "InputText", "filterType": "fuzzy", "label": "结算单编码", "name": "reSetCode", "required": false, "type": "TEXT", "width": 120}], "flow": {"context$": "$context", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "分页设置", "fieldType": "Pageable", "required": null}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "required": null}], "serviceKey": "ERP_FIN$AP_QUERY_VEND_SERVICE", "type": "InvokeService"}, "label": "对账单-结算应付-供应商", "mode": "normal", "modelAlias": "ERP_FIN$fin_apm_ap_head_tr", "pagination": {"defaultPageSize": 20, "pageSizeOptions": [20, 50, 100, 200]}, "selectType": "multiple", "showConfigure": false, "showFilterFields": true, "showScope": "all", "showType": "normal", "sortOrders": [{"fieldAlias": "createdAt", "id": "createdAt-0", "sortType": "DESC"}], "subTableConfig": {}, "tableCondition": null, "tableConditionContext$": null}, "type": "Container"}], "key": "ERP_FIN$SETT_APM_VEND-PNmej1fcoRGAk5RbO91VW", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "ERP_FIN$SETT_APM_VEND-QEP_Qzbr8h1aE3VvWIxlJ", "name": "Tabs", "props": {"items": [{"key": "gEH20xHomoGgeJiNipZkp", "label": "待确认结算单"}, {"key": "CU-EgXXwUm7oM_WD6rpDv", "label": "已确认结算单"}], "lookup": []}, "type": "Layout"}], "key": "ERP_FIN$SETT_APM_VEND-TERP_MIGRATE$FIN_ARM_230706-list", "name": "Page", "props": {"actionConfigs": [], "backResourceTabProcessConfig": "retain", "collectionService": {"createBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK/save"}, "deleteBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK/delete"}, "getCurrentSceneBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK"}}, "params": [], "showFooter": true, "showHeader": false}, "type": "Container"}, "key": "test$view5", "name": "eg4", "title": "eg4", "type": "CUSTOM"}, "type": "View"}