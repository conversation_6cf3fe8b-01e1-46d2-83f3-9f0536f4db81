{"access": "Private", "key": "test$view4", "name": "View Example 4", "props": {"content": {"children": [{"key": "good-field", "name": "GlobalFilterField", "props": {"field": {"title": "这是一个好字段"}, "permissionKey": "test$good_field_perm"}, "type": "Meta"}, {"key": "bad-field", "name": "GlobalFilterField", "props": {"field": {"title_bad": "这是一个好字段"}, "permissionKey": "test$bad_field_perm"}, "type": "Meta"}], "key": "p1", "name": "Page", "props": {}}, "key": "test$view4", "name": "eg4", "title": "eg4", "type": "CUSTOM"}, "type": "View"}