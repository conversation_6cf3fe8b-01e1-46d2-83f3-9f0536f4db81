{"key": "test$__folder__", "type": "FolderRoot", "subType": "", "moduleKey": "test", "name": "FolderRoot", "path": "", "parentKey": "test", "description": "", "access": "Private", "oid": "b5f6ef4497de68194742e498d3842d00e52ad2926ec5d7a8d1a71ab7126b0172", "extOid": "", "structure": {"customizedField": null, "links": [], "refs": [], "innerNodes": [], "viewContainers": []}, "liteProps": {}, "modifiedAt": null, "modifiedBy": 0, "designerObject": "H4sIAAAAAAAAAKWOMQ7DMAhF74I6WlWlbjlAl24dulpuQtQotrEAD1GUuwdnyNC1sKAP/PdX6L9THBgzdLnG6GDGBTpQFL14P1IckL0HBzkktMXjUF5EaloJ9qjP86NJMehInN7IMpG5rkbAfpaa7Oj2Z7UcNX2Qm9f1aNgMylTEUDaK0VI46XcHupSf4NsOKkTvgfYAAAA=", "runtimeObject": "H4sIAAAAAAAAAKWOMQ7DMAhF74I6WlWlbjlAl24dulpuQtQotrEAD1GUuwdnyNC1sKAP/PdX6L9THBgzdLnG6GDGBTpQFL14P1IckL0HBzkktMXjUF5EaloJ9qjP86NJMehInN7IMpG5rkbAfpaa7Oj2Z7UcNX2Qm9f1aNgMylTEUDaK0VI46XcHupSf4NsOKkTvgfYAAAA=", "id": null, "teamId": null, "teamCode": null}