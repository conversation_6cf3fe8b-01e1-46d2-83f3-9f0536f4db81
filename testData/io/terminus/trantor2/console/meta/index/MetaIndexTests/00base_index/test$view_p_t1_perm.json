{"key": "test$view_p_t1_perm", "type": "Permission", "subType": "", "moduleKey": "test", "name": "view p t1 perm", "path": "/dir2/", "parentKey": "test$random-key-1", "description": "", "access": "Private", "oid": "3bf980e69543ad471224c1ea08807ca4c455b4933b2b189d76ed17044ab82f92", "extOid": "", "structure": {"customizedField": null, "links": [], "refs": [], "innerNodes": [], "viewContainers": []}, "liteProps": {}, "modifiedAt": null, "modifiedBy": 0, "designerObject": "H4sIAAAAAAAAAKVPyw6CQAz8FdJ4RBG98QteOHkl61LDhn2lWzCE8O92idEPsHvpzrQznRWU1pgSNNCSmRUjlKAHY3tCD42frC1hxEV4xsSH2eCrix3XXURyMuuVQyEzXsSC6+KDRyUCfPttkvJ9cEfROtY7z4NQVW/oUuW/VfwM5O5IyQSxXuUM1GOanIyd/6x86OQeSFnrtD/YxJRClOirtEncnPq6X0vgJeZkreQxaUe3N8zVptAuAQAA", "runtimeObject": "H4sIAAAAAAAAAKVPyw6CQAz8FdJ4RBG98QteOHkl61LDhn2lWzCE8O92idEPsHvpzrQznRWU1pgSNNCSmRUjlKAHY3tCD42frC1hxEV4xsSH2eCrix3XXURyMuuVQyEzXsSC6+KDRyUCfPttkvJ9cEfROtY7z4NQVW/oUuW/VfwM5O5IyQSxXuUM1GOanIyd/6x86OQeSFnrtD/YxJRClOirtEncnPq6X0vgJeZkreQxaUe3N8zVptAuAQAA", "id": null, "teamId": null, "teamCode": null}