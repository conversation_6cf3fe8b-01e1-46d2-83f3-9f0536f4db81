{"key": "test$sys", "moduleKey": "test", "type": "Folder", "parentKey": "test$__folder__", "path": null, "oid": "2f86b1ddc3d010afe9ba9a978577a4abca27150820cf69a13c811d583bf8a81b", "object": "H4sIAAAAAAAAAKtWSs7IzEkpSs1TssorzcnRUcpOrVSyUipJLS5RKa4sVtJRykvMTQWKPN+8+/nu+S8XtTzt2AYULUgE6inxRiiOj0/Lz0lJLYqPB8nmJJak5RflhqUWFWfmA82uBtqTmpxdXJoLVG9AIQA5qjQ3KbUIZJYeGCrVAi0tyi8ohnmjGGhfbiLcfmMdpZLKApA/3MCuVKoFAKLObN36AAAA", "createdBy": 0, "updatedBy": 0, "createdAt": null, "updatedAt": null, "node": {"schemaVersion": 3, "type": "Folder", "props": null, "key": "test$sys", "name": "系统预制", "children": null, "parentKey": "test$__folder__", "platformVersion": {"number": "0.0.0.0", "checksum": "0000000000000000000000000000000000000000000000000000000000000000"}}}