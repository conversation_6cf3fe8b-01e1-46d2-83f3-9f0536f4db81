{"key": "test$view_p_b1_perm", "moduleKey": "test", "type": "Permission", "parentKey": "test$random-key-1", "path": "/dir2/", "oid": "4729037a818c34dc1d0fd46292668472fc9c0ae0f0c2100c9bc438ea09cede4b", "object": "H4sIAAAAAAAAAKVPuw6DMAz8FWR1hFLajV/owtQ1CsEVEXnJCVQI8e91UNV+QJ3FubPvfBtIpTBGaKEjvciEUIIatRkIHbRuNqaECVfmE8Z0WjS+RBB9IwKS5VknLTKZ8SIUfVN88CBZIN1/myTd4G3FWlVz8Glkqh40Xev8NzI9PdkHUtSerTc+A9UUZ8tjlz8rHzrbHilrnY8HO5uSDxx94zaym5Vf91sJaQ05Wcd5dDzQ/Q2oFDkyLgEAAA==", "createdBy": 0, "updatedBy": 0, "createdAt": null, "updatedAt": null, "node": {"schemaVersion": 3, "type": "Permission", "props": {}, "key": "test$view_p_b1_perm", "name": "view p b1 perm", "path": "/dir2/", "children": null, "access": "Private", "parentKey": "test$random-key-1", "platformVersion": {"number": "0.0.0.0", "checksum": "0000000000000000000000000000000000000000000000000000000000000000"}}}