{"key": "test_portal$__folder__", "moduleKey": "test_portal", "type": "FolderRoot", "parentKey": "test_portal", "path": null, "oid": "18095d8904afdf62eedb4d846d6c6a1b1638b902be12669300fefc0256568131", "object": "H4sIAAAAAAAAAKWPMQ7CMAxF72IxRgiJrQdgYWNgjULriqpOHDnOUFW9O04lGLpib//7+9kr9O+JBsEEXapEDmZcoAPFoj6zaKCT9yPTgOI9OEghovm3XXkwq2k5WF7vx2BzKOjIEp8oZWJjrMbDfi412uzlz2rn1PhCabvOe8NmUOFcvu8U48Xw418d6JIPH2wfR35AwwYBAAA=", "createdBy": 0, "updatedBy": 0, "createdAt": null, "updatedAt": null, "node": {"schemaVersion": 3, "type": "FolderRoot", "props": null, "key": "test_portal$__folder__", "name": "FolderRoot", "children": null, "parentKey": "test_portal", "platformVersion": {"number": "0.0.0.0", "checksum": "0000000000000000000000000000000000000000000000000000000000000000"}}}