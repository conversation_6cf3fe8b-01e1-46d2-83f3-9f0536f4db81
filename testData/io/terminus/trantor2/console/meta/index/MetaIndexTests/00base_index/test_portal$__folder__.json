{"key": "test_portal$__folder__", "type": "FolderRoot", "subType": "", "moduleKey": "test_portal", "name": "FolderRoot", "path": "", "parentKey": "test_portal", "description": "", "access": "Private", "oid": "18095d8904afdf62eedb4d846d6c6a1b1638b902be12669300fefc0256568131", "extOid": "", "structure": {"customizedField": null, "links": [], "refs": [], "innerNodes": [], "viewContainers": []}, "liteProps": {}, "modifiedAt": null, "modifiedBy": 0, "designerObject": "H4sIAAAAAAAAAKWOMQ7DMAhF74I6WlWlbjlAl24dulpuQtQo2FgYD1GUuwdn6JC1sKD/gfdX6L8TDYIJulSJHMy4QAeKRX1m0UAX70emAcV7cJBCRPMfh/JiVtNysHt9ng+bQ0FHlvhGKRMbYzUe9nOp0XZvf1aLU+MHpf26Hg2bQYVzMZSNxWgx/Oh3B7rkU/5tB3blWg0EAQAA", "runtimeObject": "H4sIAAAAAAAAAKWOMQ7DMAhF74I6WlWlbjlAl24dulpuQtQo2FgYD1GUuwdn6JC1sKD/gfdX6L8TDYIJulSJHMy4QAeKRX1m0UAX70emAcV7cJBCRPMfh/JiVtNysHt9ng+bQ0FHlvhGKRMbYzUe9nOp0XZvf1aLU+MHpf26Hg2bQYVzMZSNxWgx/Oh3B7rkU/5tB3blWg0EAQAA", "id": null, "teamId": null, "teamCode": null}