{"key": "test$service1_perm", "type": "Permission", "subType": "", "moduleKey": "test", "name": "service 1 perm", "path": "/dir1/", "parentKey": "test$random-key-2", "description": "", "access": "Private", "oid": "9172183f15b7263390c944671d8553be75dfee1094ccde317d8850c75e150a38", "extOid": "", "structure": {"customizedField": null, "links": [], "refs": [], "innerNodes": [], "viewContainers": []}, "liteProps": {}, "modifiedAt": null, "modifiedBy": 0, "designerObject": "H4sIAAAAAAAAAKVPSw6CQAy9CmlcgojuuIIbVm7NONQwYX5pBxJCuLsdQvQAtpv2vbavbwWlNTJDCx2ZWSWEEvRgbE/oofWTtSWMuAifkNOJkWajsXlGJCejXjkU7oCLpjjwqGQ/3X+LpHwfXCWnquvOp0GoujfU1Lm3Kr0DuQcSmyDKq3yBeuTJydjlz8iPTu6FlG+d94RNRClEcb5KyaLm1Ff9VkJaYnbWiR/DO7p9AJefDN0tAQAA", "runtimeObject": "H4sIAAAAAAAAAKVPSw6CQAy9CmlcgojuuIIbVm7NONQwYX5pBxJCuLsdQvQAtpv2vbavbwWlNTJDCx2ZWSWEEvRgbE/oofWTtSWMuAifkNOJkWajsXlGJCejXjkU7oCLpjjwqGQ/3X+LpHwfXCWnquvOp0GoujfU1Lm3Kr0DuQcSmyDKq3yBeuTJydjlz8iPTu6FlG+d94RNRClEcb5KyaLm1Ff9VkJaYnbWiR/DO7p9AJefDN0tAQAA", "id": null, "teamId": null, "teamCode": null}