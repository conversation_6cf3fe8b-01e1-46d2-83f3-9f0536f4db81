{"key": "test_portal$__menu__", "moduleKey": "test_portal", "type": "MenuRoot", "parentKey": "test_portal", "path": null, "oid": "e29c7ab7269cc87e0d9f5a1ce2bc510cb9f58a75b17a0123654770248d72302c", "object": "H4sIAAAAAAAAAKVPzQrCMAx+l+CxiOBtryBePHgtdcvYWNOUND2MsXc3FRT0anJKvnw/2aCf5jgIJuhSjdHBgit0oFjUZxYN8eA9Yareg4MUCA292nxjVtvkYFy9/JIaEoOOLHRHKTOb/mZe2C+lkt2e/qwWptIDpWkdXw27mQrn8n6lmB+Fj//Zga75K//+BMqd7FIAAQAA", "createdBy": 0, "updatedBy": 0, "createdAt": null, "updatedAt": null, "node": {"schemaVersion": 3, "type": "MenuRoot", "props": null, "key": "test_portal$__menu__", "name": "MenuRoot", "children": null, "parentKey": "test_portal", "platformVersion": {"number": "0.0.0.0", "checksum": "0000000000000000000000000000000000000000000000000000000000000000"}}}