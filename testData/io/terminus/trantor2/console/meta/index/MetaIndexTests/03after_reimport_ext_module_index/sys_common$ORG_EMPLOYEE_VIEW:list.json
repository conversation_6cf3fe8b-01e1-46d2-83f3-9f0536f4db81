{"key": "sys_common$ORG_EMPLOYEE_VIEW:list", "type": "View", "subType": "", "moduleKey": "sys_common", "name": "list", "path": "", "parentKey": "sys_common$ORG_EMPLOYEE_VIEW", "description": "", "access": "Private", "oid": "767c80b8ff9b6b330021872a2144511162927fd334b41bd1c71cc0c36f36948c", "extOid": "d4c110c23720f6bbb6892be4b262b1e59d2a662b8113bea4c4e51ac838ec5a7f", "structure": {"customizedField": null, "links": [{"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-resignationAt", "internalType": "DetailField/Container/离职日期"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedAt", "internalType": "DetailField/Container/更新时间"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_employee_md_PAGING_DATA_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-logs", "internalType": "Logs/Container/日志"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "internalType": "Table/Container/表格"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "internalType": "ImportButton/Button/导入"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "internalType": "Detail/Container/详情"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$user:sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "internalType": "DetailField/Container/更新人"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "internalType": "FormField/Container/创建人"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_SaveDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "internalType": "Detail/Container/详情"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressId", "internalType": "DetailField/Container/地址"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_org_link_cf", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-5JPZoDEYTJahgXeGmcG5P", "internalType": "Field/Container/是否主组织"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_identity_cf:sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "internalType": "Table/Container/表格"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressDetail", "internalType": "FormField/Container/详细地址"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-userId", "internalType": "DetailField/Container/用户"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_struct_md_PAGING_DATA_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code", "internalType": "FormField/Container/员工编码"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "internalType": "FormField/Container/更新人"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_MasterData_SaveDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "internalType": "Button/Button/保存"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "internalType": "DetailField/Container/更新人"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "internalType": "Button/Button/保存"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_BatchDeleteDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-delete", "internalType": "DropdownButton/Button/label"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-edit", "internalType": "Button/Button/编辑"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id", "internalType": "FormField/Container/ID"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detail-sys_common$org_employee_md-logs", "internalType": "Logs/Container/日志"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version", "internalType": "FormField/Container/版本号"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_struct_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_MasterData_EnableDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable", "internalType": "Button/Button/启用"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$user:sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "internalType": "FormField/Container/创建人"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_org_link_cf", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_DisableDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable", "internalType": "Button/Button/停用"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$user:sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "internalType": "FormField/Container/用户"}, {"key": null, "type": null, "targetType": "Permission", "targetKey": "sys_common$ORG_EMPLOYEE_VIEW-kvFTbRyPRuWhc0sg4AJO7_perm_ac", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-kvFTbRyPRuWhc0sg4AJO7", "internalType": "Button/Button/按钮"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "internalType": "ImportButton/Button/导入"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-email", "internalType": "FormField/Container/邮箱"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_DeleteDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete", "internalType": "Button/Button/删除"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "internalType": "ExportButton/Button/导出"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_org_link_cf", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "Fake<PERSON><PERSON>", "targetKey": "GET:/api/gei/template/download", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "internalType": "ImportButton/Button/导入"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-status", "internalType": "DetailField/Container/状态"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$user:sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "internalType": "FormField/Container/更新人"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$user", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "internalType": "DetailField/Container/创建人"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "internalType": "FormField/Container/创建人"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "internalType": "FormField/Container/用户"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-start", "internalType": "DropdownButton/Button/label"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted", "internalType": "FormField/Container/逻辑删除标识"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_identity_cf_FIND_DATA_BY_ID_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_identity_cf", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable", "internalType": "Button/Button/停用"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_identity_cf_PAGING_DATA_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_org_link_cf", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-mobile", "internalType": "DetailField/Container/手机"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$user:sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "internalType": "DetailField/Container/创建人"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_struct_md:sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "View", "targetKey": "show", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "internalType": "Table/Container/表格"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "internalType": "FormField/Container/用户"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_org_link_cf", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform", "internalType": "TableForm/Container/表格表单"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-delete", "internalType": "DropdownButton/Button/label"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$user:sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "internalType": "FormField/Container/创建人"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_struct_md_FIND_DATA_BY_ID_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "Fake<PERSON><PERSON>", "targetKey": "GET:/api/gei/task/import-sub-model", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "internalType": "ImportButton/Button/导入"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_struct_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-email", "internalType": "DetailField/Container/邮箱"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt", "internalType": "FormField/Container/更新时间"}, {"key": null, "type": null, "targetType": "View", "targetKey": "show", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "internalType": "Button/Button/保存"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-Mhe2IM7i9iyThkD26CsaC", "internalType": "FormField/Container/状态"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressDetail", "internalType": "DetailField/Container/详细地址"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-entryAt", "internalType": "FormField/Container/入职日期"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "internalType": "DetailField/Container/创建人"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_employee_md_BATCH_DELETE_DATA_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$user_FIND_DATA_BY_ID_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name", "internalType": "FormField/Container/姓名"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_MasterData_MultiEnableDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-start", "internalType": "DropdownButton/Button/label"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_org_link_cf", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "Fake<PERSON><PERSON>", "targetKey": "GET:/api/gei/task/import-direct-by-oss", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "internalType": "ImportButton/Button/导入"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TEMPLATE_PAGING_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "internalType": "ExportButton/Button/导出"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete", "internalType": "Button/Button/删除"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_CopyDataConverterService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "internalType": "FormGroup/Container/表单组"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$user:sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "internalType": "FormField/Container/更新人"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_employee_md_DELETE_DATA_BY_ID_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "internalType": "FormGroup/Container/表单组"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile", "internalType": "FormField/Container/手机"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-stop", "internalType": "DropdownButton/Button/label"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdAt", "internalType": "DetailField/Container/创建时间"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "internalType": "Table/Container/表格"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable", "internalType": "Button/Button/启用"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-idCard", "internalType": "DetailField/Container/身份证号"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_org_link_cf", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-N2CK9jLOrkwXvk-aNk5FJ", "internalType": "Field/Container/是否主组织"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "internalType": "DetailField/Container/更新人"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_employee_md_MASTER_DATA_MULTI_DISABLE_DATA_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "internalType": "ExportButton/Button/导出"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-NdUI184yh2s4lcc6w4sEg", "internalType": "Field/Container/姓名"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_employee_md_FIND_DATA_BY_ID_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_MultiDisableDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-stop", "internalType": "DropdownButton/Button/label"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$user", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "internalType": "DetailField/Container/更新人"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$user", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "internalType": "FormField/Container/用户"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_struct_md:sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "internalType": "FormField/Container/更新人"}, {"key": null, "type": null, "targetType": "Fake<PERSON><PERSON>", "targetKey": "GET:/api/gei/template/download/v2", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "internalType": "ImportButton/Button/导入"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_employee_md_MASTER_DATA_DISABLE_DATA_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$user_PAGING_DATA_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-lF1QOZEGtkaCHK1RzD8Vw", "internalType": "Field/Container/员工编码"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-name", "internalType": "DetailField/Container/姓名"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$user", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "View", "targetKey": "edit", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-copy", "internalType": "Button/Button/复制"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_BatchDeleteDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-delete", "internalType": "DropdownButton/Button/label"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "internalType": "FormGroup/Container/表单组"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "internalType": "FormField/Container/更新人"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-type", "internalType": "DetailField/Container/员工类型"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "internalType": "DetailField/Container/创建人"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$user", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "internalType": "FormField/Container/创建人"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_employee_md_MASTER_DATA_MULTI_ENABLE_DATA_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-entryAt", "internalType": "DetailField/Container/入职日期"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_identity_cf:sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_MasterData_DisableDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable", "internalType": "Button/Button/停用"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_identity_cf:sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_employee_md_MASTER_DATA_ENABLE_DATA_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "internalType": "FormField/Container/创建人"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "internalType": "Detail/Container/详情"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-idCard", "internalType": "FormField/Container/身份证号"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_SaveDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "internalType": "Button/Button/保存"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type", "internalType": "FormField/Container/员工类型"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_identity_cf", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$user:sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "internalType": "DetailField/Container/创建人"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_identity_cf", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressId", "internalType": "FormField/Container/地址"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_MasterData_SaveDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "internalType": "Detail/Container/详情"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "internalType": "Detail/Container/详情"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_EnableDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable", "internalType": "Button/Button/启用"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TEMPLATE_PAGING_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "internalType": "ImportButton/Button/导入"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "internalType": "DetailField/Container/更新人"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "internalType": "FormGroup/Container/表单组"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "internalType": "ImportButton/Button/导入"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_employee_md_COPY_DATA_CONVERTER_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt", "internalType": "FormField/Container/创建时间"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_org_link_cf", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list", "internalType": "Table/Container/表格"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_employee_md_MASTER_DATA_SAVE_DATA_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "internalType": "DetailField/Container/创建人"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_identity_cf:sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_CopyDataConverterService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "internalType": "FormGroup/Container/表单组"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_DeleteDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete", "internalType": "Button/Button/删除"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-code", "internalType": "DetailField/Container/员工编码"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$user:sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "internalType": "DetailField/Container/更新人"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "internalType": "ImportButton/Button/导入"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "internalType": "ExportButton/Button/导出"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-originOrgId", "internalType": "FormField/Container/所属组织"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_PROGRESS_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "internalType": "ImportButton/Button/导入"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$user", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "internalType": "FormField/Container/更新人"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_struct_md:sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-resignationAt", "internalType": "FormField/Container/离职日期"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_struct_md", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_struct_md:sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_MultiEnableDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-start", "internalType": "DropdownButton/Button/label"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_MasterData_MultiDisableDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-stop", "internalType": "DropdownButton/Button/label"}], "refs": [{"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-resignationAt", "sourceInnerType": "DetailField/Container/离职日期", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedAt", "sourceInnerType": "DetailField/Container/更新时间", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_employee_md_PAGING_DATA_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-logs", "sourceInnerType": "Logs/Container/日志", "targetKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "sourceInnerType": "Table/Container/表格", "targetKey": "sys_common$SYS_PagingDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "sourceInnerType": "ImportButton/Button/导入", "targetKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "sourceInnerType": "Detail/Container/详情", "targetKey": "sys_common$SYS_FindDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "sourceInnerType": "DetailField/Container/更新人", "targetKey": "sys_common$user:sys_common$SYS_PagingDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "sourceInnerType": "FormField/Container/创建人", "targetKey": "sys_common$SYS_FindDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "sourceInnerType": "Detail/Container/详情", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_SaveDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressId", "sourceInnerType": "DetailField/Container/地址", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-5JPZoDEYTJahgXeGmcG5P", "sourceInnerType": "Field/Container/是否主组织", "targetKey": "sys_common$org_employee_org_link_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$org_identity_cf:sys_common$SYS_FindDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "sourceInnerType": "Table/Container/表格", "targetKey": "sys_common$org_employee_md:sys_common$SYS_PagingDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressDetail", "sourceInnerType": "FormField/Container/详细地址", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-userId", "sourceInnerType": "DetailField/Container/用户", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_struct_md_PAGING_DATA_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code", "sourceInnerType": "FormField/Container/员工编码", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "sourceInnerType": "FormField/Container/更新人", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "sourceInnerType": "Button/Button/保存", "targetKey": "sys_common$SYS_MasterData_SaveDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "sourceInnerType": "DetailField/Container/更新人", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$SYS_PagingDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "sourceInnerType": "Button/Button/保存", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-delete", "sourceInnerType": "DropdownButton/Button/label", "targetKey": "sys_common$SYS_BatchDeleteDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-edit", "sourceInnerType": "Button/Button/编辑", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id", "sourceInnerType": "FormField/Container/ID", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detail-sys_common$org_employee_md-logs", "sourceInnerType": "Logs/Container/日志", "targetKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version", "sourceInnerType": "FormField/Container/版本号", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$org_struct_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable", "sourceInnerType": "Button/Button/启用", "targetKey": "sys_common$SYS_MasterData_EnableDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "sourceInnerType": "FormField/Container/创建人", "targetKey": "sys_common$user:sys_common$SYS_FindDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$org_employee_org_link_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable", "sourceInnerType": "Button/Button/停用", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_DisableDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "sourceInnerType": "FormField/Container/用户", "targetKey": "sys_common$user:sys_common$SYS_PagingDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-kvFTbRyPRuWhc0sg4AJO7", "sourceInnerType": "Button/Button/按钮", "targetKey": "sys_common$ORG_EMPLOYEE_VIEW-kvFTbRyPRuWhc0sg4AJO7_perm_ac", "targetType": "Permission", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$SYS_PagingDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "sourceInnerType": "ImportButton/Button/导入", "targetKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-email", "sourceInnerType": "FormField/Container/邮箱", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete", "sourceInnerType": "Button/Button/删除", "targetKey": "sys_common$SYS_DeleteDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "sourceInnerType": "ExportButton/Button/导出", "targetKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$org_employee_org_link_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "sourceInnerType": "ImportButton/Button/导入", "targetKey": "GET:/api/gei/template/download", "targetType": "Fake<PERSON><PERSON>", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-status", "sourceInnerType": "DetailField/Container/状态", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "sourceInnerType": "FormField/Container/更新人", "targetKey": "sys_common$user:sys_common$SYS_PagingDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "sourceInnerType": "DetailField/Container/创建人", "targetKey": "sys_common$user", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "sourceInnerType": "FormField/Container/创建人", "targetKey": "sys_common$SYS_PagingDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "sourceInnerType": "FormField/Container/用户", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-start", "sourceInnerType": "DropdownButton/Button/label", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted", "sourceInnerType": "FormField/Container/逻辑删除标识", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$SYS_FindDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_identity_cf_FIND_DATA_BY_ID_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "sys_common$org_identity_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable", "sourceInnerType": "Button/Button/停用", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_identity_cf_PAGING_DATA_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$org_employee_org_link_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-mobile", "sourceInnerType": "DetailField/Container/手机", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "sourceInnerType": "DetailField/Container/创建人", "targetKey": "sys_common$user:sys_common$SYS_FindDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$org_struct_md:sys_common$SYS_PagingDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "sourceInnerType": "Table/Container/表格", "targetKey": "show", "targetType": "View", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "sourceInnerType": "FormField/Container/用户", "targetKey": "sys_common$SYS_PagingDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform", "sourceInnerType": "TableForm/Container/表格表单", "targetKey": "sys_common$org_employee_org_link_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-delete", "sourceInnerType": "DropdownButton/Button/label", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "sourceInnerType": "FormField/Container/创建人", "targetKey": "sys_common$user:sys_common$SYS_PagingDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_struct_md_FIND_DATA_BY_ID_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "sourceInnerType": "ImportButton/Button/导入", "targetKey": "GET:/api/gei/task/import-sub-model", "targetType": "Fake<PERSON><PERSON>", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$org_struct_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-email", "sourceInnerType": "DetailField/Container/邮箱", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt", "sourceInnerType": "FormField/Container/更新时间", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "sourceInnerType": "Button/Button/保存", "targetKey": "show", "targetType": "View", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-Mhe2IM7i9iyThkD26CsaC", "sourceInnerType": "FormField/Container/状态", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressDetail", "sourceInnerType": "DetailField/Container/详细地址", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-entryAt", "sourceInnerType": "FormField/Container/入职日期", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "sourceInnerType": "DetailField/Container/创建人", "targetKey": "sys_common$SYS_FindDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_employee_md_BATCH_DELETE_DATA_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$user_FIND_DATA_BY_ID_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name", "sourceInnerType": "FormField/Container/姓名", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-start", "sourceInnerType": "DropdownButton/Button/label", "targetKey": "sys_common$SYS_MasterData_MultiEnableDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$org_employee_org_link_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "sourceInnerType": "ImportButton/Button/导入", "targetKey": "GET:/api/gei/task/import-direct-by-oss", "targetType": "Fake<PERSON><PERSON>", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "sourceInnerType": "ExportButton/Button/导出", "targetKey": "sys_common$API_GEI_TEMPLATE_PAGING_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete", "sourceInnerType": "Button/Button/删除", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "sourceInnerType": "FormGroup/Container/表单组", "targetKey": "sys_common$SYS_CopyDataConverterService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "sourceInnerType": "FormField/Container/更新人", "targetKey": "sys_common$user:sys_common$SYS_FindDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_employee_md_DELETE_DATA_BY_ID_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "sourceInnerType": "FormGroup/Container/表单组", "targetKey": "sys_common$SYS_FindDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile", "sourceInnerType": "FormField/Container/手机", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-stop", "sourceInnerType": "DropdownButton/Button/label", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdAt", "sourceInnerType": "DetailField/Container/创建时间", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "sourceInnerType": "Table/Container/表格", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable", "sourceInnerType": "Button/Button/启用", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-idCard", "sourceInnerType": "DetailField/Container/身份证号", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-N2CK9jLOrkwXvk-aNk5FJ", "sourceInnerType": "Field/Container/是否主组织", "targetKey": "sys_common$org_employee_org_link_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "sourceInnerType": "DetailField/Container/更新人", "targetKey": "sys_common$SYS_PagingDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_employee_md_MASTER_DATA_MULTI_DISABLE_DATA_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "sourceInnerType": "ExportButton/Button/导出", "targetKey": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$SYS_FindDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-NdUI184yh2s4lcc6w4sEg", "sourceInnerType": "Field/Container/姓名", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_employee_md_FIND_DATA_BY_ID_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-stop", "sourceInnerType": "DropdownButton/Button/label", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_MultiDisableDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "sourceInnerType": "DetailField/Container/更新人", "targetKey": "sys_common$user", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "sourceInnerType": "FormField/Container/用户", "targetKey": "sys_common$user", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$org_struct_md:sys_common$SYS_FindDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "sourceInnerType": "FormField/Container/更新人", "targetKey": "sys_common$SYS_PagingDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "sourceInnerType": "ImportButton/Button/导入", "targetKey": "GET:/api/gei/template/download/v2", "targetType": "Fake<PERSON><PERSON>", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_employee_md_MASTER_DATA_DISABLE_DATA_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$SYS_PagingDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$user_PAGING_DATA_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-lF1QOZEGtkaCHK1RzD8Vw", "sourceInnerType": "Field/Container/员工编码", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-name", "sourceInnerType": "DetailField/Container/姓名", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "sys_common$user", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-copy", "sourceInnerType": "Button/Button/复制", "targetKey": "edit", "targetType": "View", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-delete", "sourceInnerType": "DropdownButton/Button/label", "targetKey": "sys_common$org_employee_md:sys_common$SYS_BatchDeleteDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "sourceInnerType": "FormGroup/Container/表单组", "targetKey": "sys_common$org_employee_md:sys_common$SYS_FindDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$SYS_FindDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "sourceInnerType": "FormField/Container/更新人", "targetKey": "sys_common$SYS_FindDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-type", "sourceInnerType": "DetailField/Container/员工类型", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "sourceInnerType": "DetailField/Container/创建人", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "sourceInnerType": "FormField/Container/创建人", "targetKey": "sys_common$user", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_employee_md_MASTER_DATA_MULTI_ENABLE_DATA_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-entryAt", "sourceInnerType": "DetailField/Container/入职日期", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$org_identity_cf:sys_common$SYS_FindDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable", "sourceInnerType": "Button/Button/停用", "targetKey": "sys_common$SYS_MasterData_DisableDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$org_identity_cf:sys_common$SYS_PagingDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_employee_md_MASTER_DATA_ENABLE_DATA_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "sourceInnerType": "FormField/Container/创建人", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "sourceInnerType": "Detail/Container/详情", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-idCard", "sourceInnerType": "FormField/Container/身份证号", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "sourceInnerType": "Button/Button/保存", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_SaveDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$SYS_PagingDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type", "sourceInnerType": "FormField/Container/员工类型", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$org_identity_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "sourceInnerType": "DetailField/Container/创建人", "targetKey": "sys_common$user:sys_common$SYS_PagingDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$org_identity_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$SYS_FindDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressId", "sourceInnerType": "FormField/Container/地址", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "sourceInnerType": "Detail/Container/详情", "targetKey": "sys_common$SYS_MasterData_SaveDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "sourceInnerType": "Detail/Container/详情", "targetKey": "sys_common$org_employee_md:sys_common$SYS_FindDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable", "sourceInnerType": "Button/Button/启用", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_EnableDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "sourceInnerType": "ImportButton/Button/导入", "targetKey": "sys_common$API_GEI_TEMPLATE_PAGING_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "sourceInnerType": "DetailField/Container/更新人", "targetKey": "sys_common$SYS_FindDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "sourceInnerType": "FormGroup/Container/表单组", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "sourceInnerType": "ImportButton/Button/导入", "targetKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_employee_md_COPY_DATA_CONVERTER_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt", "sourceInnerType": "FormField/Container/创建时间", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list", "sourceInnerType": "Table/Container/表格", "targetKey": "sys_common$org_employee_org_link_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_employee_md_MASTER_DATA_SAVE_DATA_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "sourceInnerType": "DetailField/Container/创建人", "targetKey": "sys_common$SYS_PagingDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$org_identity_cf:sys_common$SYS_PagingDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "sourceInnerType": "FormGroup/Container/表单组", "targetKey": "sys_common$org_employee_md:sys_common$SYS_CopyDataConverterService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete", "sourceInnerType": "Button/Button/删除", "targetKey": "sys_common$org_employee_md:sys_common$SYS_DeleteDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-code", "sourceInnerType": "DetailField/Container/员工编码", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "sourceInnerType": "DetailField/Container/更新人", "targetKey": "sys_common$user:sys_common$SYS_FindDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "sourceInnerType": "ImportButton/Button/导入", "targetKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "sourceInnerType": "ExportButton/Button/导出", "targetKey": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-originOrgId", "sourceInnerType": "FormField/Container/所属组织", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "sourceInnerType": "ImportButton/Button/导入", "targetKey": "sys_common$API_GEI_TASK_PROGRESS_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "sourceInnerType": "FormField/Container/更新人", "targetKey": "sys_common$user", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$org_struct_md:sys_common$SYS_PagingDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-resignationAt", "sourceInnerType": "FormField/Container/离职日期", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "sys_common$org_struct_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$org_struct_md:sys_common$SYS_FindDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-start", "sourceInnerType": "DropdownButton/Button/label", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_MultiEnableDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-stop", "sourceInnerType": "DropdownButton/Button/label", "targetKey": "sys_common$SYS_MasterData_MultiDisableDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}], "innerNodes": [{"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-page", "innerType": "Page", "innerName": "页面", "innerParentKey": ""}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "innerType": "ColumnPage", "innerName": "双栏页面", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-page"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "innerType": "Table", "innerName": "表格", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-column-page"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions", "innerType": "BatchActions", "innerName": "按钮组", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-new", "innerType": "<PERSON><PERSON>", "innerName": "新建", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch", "innerType": "DropdownButton", "innerName": "批量操作", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "innerType": "ImportButton", "innerName": "导入", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "innerType": "ExportButton", "innerName": "导出", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-kvFTbRyPRuWhc0sg4AJO7", "innerType": "<PERSON><PERSON>", "innerName": "按钮", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-toolbar-actions", "innerType": "ToolbarActions", "innerName": "按钮组", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-logs", "innerType": "Logs", "innerName": "日志", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-toolbar-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-T0dxNmXpp6q8fg04HXRzp", "innerType": "Fields", "innerName": "字段组", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-lF1QOZEGtkaCHK1RzD8Vw", "innerType": "Field", "innerName": "员工编码", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-T0dxNmXpp6q8fg04HXRzp"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-NdUI184yh2s4lcc6w4sEg", "innerType": "Field", "innerName": "姓名", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-T0dxNmXpp6q8fg04HXRzp"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "innerType": "StackView", "innerName": "栈视图", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-column-page"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-empty-box", "innerType": "Box", "innerName": "区块", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-empty", "innerType": "Empty", "innerName": "空白图", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-empty-box"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "innerType": "View", "innerName": "视图", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "innerType": "Page", "innerName": "页面", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-title", "innerType": "Page<PERSON><PERSON>le", "innerName": "页面标题", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-wPS2MJGuQRIvm1fPSsLOu", "innerType": "Status", "innerName": "状态", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-title"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-header", "innerType": "<PERSON><PERSON><PERSON><PERSON>", "innerName": "页头操作栏", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions", "innerType": "Space", "innerName": "间距", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-header"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete", "innerType": "<PERSON><PERSON>", "innerName": "删除", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-copy", "innerType": "<PERSON><PERSON>", "innerName": "复制", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable", "innerType": "<PERSON><PERSON>", "innerName": "停用", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable", "innerType": "<PERSON><PERSON>", "innerName": "启用", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-edit", "innerType": "<PERSON><PERSON>", "innerName": "编辑", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detail-sys_common$org_employee_md-logs", "innerType": "Logs", "innerName": "日志", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "innerType": "Detail", "innerName": "详情", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "innerType": "Tabs", "innerName": "页签", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "innerType": "TabItem", "innerName": "页签项", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "innerType": "DetailGroupItem", "innerName": "详情组元素", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-code", "innerType": "DetailField", "innerName": "员工编码", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-name", "innerType": "DetailField", "innerName": "姓名", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-status", "innerType": "DetailField", "innerName": "状态", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-type", "innerType": "DetailField", "innerName": "员工类型", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-email", "innerType": "DetailField", "innerName": "邮箱", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-mobile", "innerType": "DetailField", "innerName": "手机", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressDetail", "innerType": "DetailField", "innerName": "详细地址", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-entryAt", "innerType": "DetailField", "innerName": "入职日期", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-resignationAt", "innerType": "DetailField", "innerName": "离职日期", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-idCard", "innerType": "DetailField", "innerName": "身份证号", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-userId", "innerType": "DetailField", "innerName": "用户", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressId", "innerType": "DetailField", "innerName": "地址", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-gyeTOSkmC__Zr4767Pu1A", "innerType": "TabItem", "innerName": "页签项", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-customDetailField", "innerType": "CustomDetailField", "innerName": "自定义详情字段", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-gyeTOSkmC__Zr4767Pu1A"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list", "innerType": "Table", "innerName": "表格", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-customDetailField"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list-batch-actions", "innerType": "BatchActions", "innerName": "按钮组", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list-toolbar-actions", "innerType": "ToolbarActions", "innerName": "按钮组", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-LAxXu7V8oaf_BP5O6foRo", "innerType": "Fields", "innerName": "字段组", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-N2CK9jLOrkwXvk-aNk5FJ", "innerType": "Field", "innerName": "是否主组织", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-LAxXu7V8oaf_BP5O6foRo"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "innerType": "Field", "innerName": "组织身份", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-LAxXu7V8oaf_BP5O6foRo"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "innerType": "Field", "innerName": "组织单元", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-LAxXu7V8oaf_BP5O6foRo"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-zR-xbPeH0dUQGisUgdnZD", "innerType": "TabItem", "innerName": "页签项", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-group", "innerType": "DetailGroupItem", "innerName": "详情组元素", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-zR-xbPeH0dUQGisUgdnZD"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "innerType": "DetailField", "innerName": "创建人", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-group"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "innerType": "DetailField", "innerName": "更新人", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-group"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdAt", "innerType": "DetailField", "innerName": "创建时间", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-group"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedAt", "innerType": "DetailField", "innerName": "更新时间", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-group"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-footer", "innerType": "<PERSON>Footer", "innerName": "页面底部", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView", "innerType": "Page", "innerName": "页面", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-page-title", "innerType": "Page<PERSON><PERSON>le", "innerName": "页面标题", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-page-header", "innerType": "<PERSON><PERSON><PERSON><PERSON>", "innerName": "页头操作栏", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "innerType": "FormGroup", "innerName": "表单组", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "innerType": "Tabs", "innerName": "页签", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "innerType": "TabItem", "innerName": "页签项", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "innerType": "FormGroupItem", "innerName": "表单组元素", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code", "innerType": "FormField", "innerName": "员工编码", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name", "innerType": "FormField", "innerName": "姓名", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type", "innerType": "FormField", "innerName": "员工类型", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-email", "innerType": "FormField", "innerName": "邮箱", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile", "innerType": "FormField", "innerName": "手机", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressDetail", "innerType": "FormField", "innerName": "详细地址", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-entryAt", "innerType": "FormField", "innerName": "入职日期", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-resignationAt", "innerType": "FormField", "innerName": "离职日期", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-idCard", "innerType": "FormField", "innerName": "身份证号", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "innerType": "FormField", "innerName": "用户", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressId", "innerType": "FormField", "innerName": "地址", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-Mhe2IM7i9iyThkD26CsaC", "innerType": "FormField", "innerName": "状态", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id", "innerType": "FormField", "innerName": "ID", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "innerType": "FormField", "innerName": "创建人", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "innerType": "FormField", "innerName": "更新人", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt", "innerType": "FormField", "innerName": "创建时间", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt", "innerType": "FormField", "innerName": "更新时间", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version", "innerType": "FormField", "innerName": "版本号", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted", "innerType": "FormField", "innerName": "逻辑删除标识", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-originOrgId", "innerType": "FormField", "innerName": "所属组织", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-JE2jf3T2tYith5FRLD6QN", "innerType": "TabItem", "innerName": "页签项", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-form-field-employeeOrgLinkList", "innerType": "CustomFormField", "innerName": "自定义表单字段", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-JE2jf3T2tYith5FRLD6QN"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform", "innerType": "TableForm", "innerName": "表格表单", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-form-field-employeeOrgLinkList"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-batchActions", "innerType": "BatchActions", "innerName": "按钮组", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-batchActions-delete", "innerType": "<PERSON><PERSON>", "innerName": "删除", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-batchActions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-reordActions", "innerType": "RecordActions", "innerName": "按钮组", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-action-deleteLine", "innerType": "<PERSON><PERSON>", "innerName": "删除", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-reordActions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-MoO66JROlIXfgdLgx0m1p", "innerType": "Fields", "innerName": "字段组", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-5JPZoDEYTJahgXeGmcG5P", "innerType": "Field", "innerName": "是否主组织", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-MoO66JROlIXfgdLgx0m1p"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "innerType": "Field", "innerName": "组织身份", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-MoO66JROlIXfgdLgx0m1p"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "innerType": "Field", "innerName": "组织单元", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-MoO66JROlIXfgdLgx0m1p"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-footer", "innerType": "<PERSON>Footer", "innerName": "页面底部", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-actions", "innerType": "Space", "innerName": "间距", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-footer"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-cancel", "innerType": "<PERSON><PERSON>", "innerName": "取消", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "innerType": "<PERSON><PERSON>", "innerName": "保存", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-page-header", "innerType": "<PERSON><PERSON><PERSON><PERSON>", "innerName": "页头操作栏", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-page"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-page-title", "innerType": "Page<PERSON><PERSON>le", "innerName": "页面标题", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-page"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-page-footer", "innerType": "<PERSON>Footer", "innerName": "页面底部", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-page"}], "viewContainers": [{"viewKey": null, "containerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "containerLabel": "表单组", "modelKey": "sys_common$org_employee_md", "fields": [{"fieldKey": "code", "fieldLabel": "员工编码", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "name", "fieldLabel": "姓名", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "type", "fieldLabel": "员工类型", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "email", "fieldLabel": "邮箱", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "mobile", "fieldLabel": "手机", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "addressDetail", "fieldLabel": "详细地址", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "entryAt", "fieldLabel": "入职日期", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "resignationAt", "fieldLabel": "离职日期", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "idCard", "fieldLabel": "身份证号", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "userId", "fieldLabel": "用户", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "addressId", "fieldLabel": "地址", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "status", "fieldLabel": "状态", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "id", "fieldLabel": "ID", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "created<PERSON>y", "fieldLabel": "创建人", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "updatedBy", "fieldLabel": "更新人", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "createdAt", "fieldLabel": "创建时间", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "updatedAt", "fieldLabel": "更新时间", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "version", "fieldLabel": "版本号", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "deleted", "fieldLabel": "逻辑删除标识", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "originOrgId", "fieldLabel": "所属组织", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "employeeOrgLinkList.isMainOrg", "fieldLabel": "是否主组织", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "employeeOrgLinkList.identityId", "fieldLabel": "组织身份", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "employeeOrgLinkList.orgUnitId", "fieldLabel": "组织单元", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}]}, {"viewKey": null, "containerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "containerLabel": "详情", "modelKey": "sys_common$org_employee_md", "fields": [{"fieldKey": "code", "fieldLabel": "员工编码", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "name", "fieldLabel": "姓名", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "status", "fieldLabel": "状态", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "type", "fieldLabel": "员工类型", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "email", "fieldLabel": "邮箱", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "mobile", "fieldLabel": "手机", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "addressDetail", "fieldLabel": "详细地址", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "entryAt", "fieldLabel": "入职日期", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "resignationAt", "fieldLabel": "离职日期", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "idCard", "fieldLabel": "身份证号", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "userId", "fieldLabel": "用户", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "addressId", "fieldLabel": "地址", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "employeeOrgLinkList.isMainOrg", "fieldLabel": "是否主组织", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "employeeOrgLinkList.identityId", "fieldLabel": "组织身份", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "employeeOrgLinkList.orgUnitId", "fieldLabel": "组织单元", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "created<PERSON>y", "fieldLabel": "创建人", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "updatedBy", "fieldLabel": "更新人", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "createdAt", "fieldLabel": "创建时间", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "updatedAt", "fieldLabel": "更新时间", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}]}, {"viewKey": null, "containerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "containerLabel": "表格", "modelKey": "sys_common$org_employee_md", "fields": [{"fieldKey": "code", "fieldLabel": "员工编码", "targetServices": [{"serviceKey": "sys_common$SYS_PagingDataService", "parentPath": "data.data", "type": "Output"}, {"serviceKey": "sys_common$SYS_DeleteDataByIdService", "parentPath": null, "type": "Input"}]}, {"fieldKey": "name", "fieldLabel": "姓名", "targetServices": [{"serviceKey": "sys_common$SYS_PagingDataService", "parentPath": "data.data", "type": "Output"}, {"serviceKey": "sys_common$SYS_DeleteDataByIdService", "parentPath": null, "type": "Input"}]}]}]}, "liteProps": {"extended": false, "frontendConfig": {"modules": ["base", "terp", "service"]}, "i18nConfig": {"i18nKeySet": ["用户", "状态", "新建员工信息表", "请输入版本号", "详情", "请输入员工编码", "停用成功", "姓名", "保存", "请输入ID", "地址", "用户名", "ID", "创建人", "身份证号", "请输入更新时间", "保存成功!", "请输入姓名", "启用成功", "用户手机", "逻辑删除标识", "确认统一停用吗？", "请输入手机", "请选择", "版本号", "更新时间", "是否主组织", "批量停用", "确认统一启用吗？", "编辑", "所属组织", "选择创建人", "员工信息表", "请输入逻辑删除标识", "确认删除吗？", "批量启用", "员工编码", "复制", "选择更新人", "手机", "系统信息", "新建", "删除成功", "停用", "请输入员工类型", "按钮", "批量删除", "请输入创建时间", "详细地址", "删除", "入职日期", "离职日期", "邮箱", "启用", "批量操作", "用户邮箱", "组织单元", "选择组织单元", "选择组织身份", "主体信息", "确认启用吗？", "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},UNENABLED:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISENABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.text", "更新人", "请输入", "取消", "组织身份", "创建时间", "员工类型", "确认停用吗？"], "i18nScanPaths": ["sys_common$ORG_EMPLOYEE_VIEW-wPS2MJGuQRIvm1fPSsLOu.props.text$", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-name.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.5.label", "sys_common$ORG_EMPLOYEE_VIEW-Mhe2IM7i9iyThkD26CsaC.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.3.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.2.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.5.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.8.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.2.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs.props.items.0.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-batchActions-delete.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-action-cancel.props.label", "sys_common$ORG_EMPLOYEE_VIEW-5JPZoDEYTJahgXeGmcG5P.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-idCard.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs.props.items.1.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressId.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressId.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.0.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressId.props.label", "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable.props.actionConfig.endLogicOtherConfig.1.message", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.1.actionConfig.endLogicOtherConfig.1.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy.props.label", "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-userId.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable.props.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-code.props.componentProps.placeholder", "@exp:sys_common$ORG_EMPLOYEE_VIEW-editView-page-title.props.title", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name.props.label", "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.0.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-status.props.label", "sys_common$ORG_EMPLOYEE_VIEW-NdUI184yh2s4lcc6w4sEg.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.7.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-action-deleteLine.props.label", "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs.props.items.2.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-resignationAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.10.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.2.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable.props.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-lF1QOZEGtkaCHK1RzD8Vw.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.1.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-mobile.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-status.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-resignationAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressId.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-entryAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.1.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-email.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.7.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressDetail.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-new.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-idCard.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-N2CK9jLOrkwXvk-aNk5FJ.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.10.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-copy.props.label", "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-entryAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-Mhe2IM7i9iyThkD26CsaC.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.1.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.2.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-originOrgId.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.2.actionConfig.endLogicOtherConfig.1.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-entryAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.4.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-name.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-mobile.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-idCard.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-idCard.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-N2CK9jLOrkwXvk-aNk5FJ.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-code.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-email.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressDetail.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-entryAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.0.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.2.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs.props.items.0.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete.props.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-NdUI184yh2s4lcc6w4sEg.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-type.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.6.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save.props.actionConfig.endLogicOtherConfig.2.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-lF1QOZEGtkaCHK1RzD8Vw.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.0.actionConfig.endLogicOtherConfig.2.message", "@exp:sys_common$ORG_EMPLOYEE_VIEW-detailView-page-title.props.title", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.0.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-kvFTbRyPRuWhc0sg4AJO7.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs.props.items.1.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable.props.actionConfig.endLogicOtherConfig.1.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-edit.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-type.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.9.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-resignationAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-email.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.0.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.8.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.6.label", "sys_common$ORG_EMPLOYEE_VIEW-5JPZoDEYTJahgXeGmcG5P.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.9.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressDetail.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.3.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.0.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.fields.0.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-email.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete.props.label", "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.1.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete.props.actionConfig.endLogicOtherConfig.1.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.2.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.1.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressDetail.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.4.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-resignationAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-originOrgId.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-userId.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.fields.0.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.1.label"]}, "key": "sys_common$ORG_EMPLOYEE_VIEW:list", "title": "list", "type": "LIST"}, "modifiedAt": null, "modifiedBy": 0, "designerObject": "H4sIAAAAAAAAAO19e3PbRrbnV/GwZqvsunpbkh1VZbwSSclUJJEmKUd+FQskmyQkkmAAUBLt6y3n5ia2J/FNZnInnt04E3trksnOvWMndzNJxsrjy4iU9Nd+he0XgAYIkgAIUJTck6qRCYLdp0+fc/r0Ob8+fSeUK4nlvAyqoblqvVweCW2BRmgupDSUTE6qVKTqr+PJpUx0NbESvxaNZq7Gom/OlUVFDY2EqkIFwFfpp5oAG1Hf6Plr9GpZUAuSXLkKZEWUYM93IBUgt6XUK/DHE33+D1FWr2SBjNoaw/+F7sJOZamm4K6kqiqIVSCnQBnkVPSoG72jeQBfL18Vwc4o854kFzOgUitLDQAylTx9KzR3406oIIJyHnaek/IAEqPgbhbRQ4Uw+e6I8RJmYq+XFFVQ60rP19RGrXdboILo7PVWRcqK5d6NCXkoOooSIYPv2XVVlRvzas/3YJNisSqoUDYcvC3mw4Kc7/laXQFyrP21G2xDjhrpPGO3bFhj02X7jBAxisvFFbG6tUL0qTOZyioUX/iyA85Ajotqo+9hOxwy1In1qqj23R1sZ61Lj6Y+czIQVJBfaAxuZuu1/KC7pKN0oA2UuE5v3hrpbuxAXlR7mTpkugMydNyCnUgL5nB92qbrPTeIfhvEXmqNfLQuKu2nNkMGYBcLcpo4dpp7ecPLh1tOHNJewxutgh3DX12oqyoWQt0jFHJIUcNStSAW0WdQza9IRTEHX46rJehIjuiP8GftzRvaT+GLCaEIluuVGnxXFeQiUBGTYLeYH3lREbJlkP81fPhrzJxddUyW6iq4NEZaOPP666+fuYl+cTN05p//+UzXt5CRvhmCPZWFLIA+Z6j1ydfNvZeoa2Q/5+DIxIogN5DbS5+8KeYRTWgKzdyFfSQ0PsigIm0DOL2qXAd3feF8VlBzJYP3EdhVXtqpts8B/H1WJBZzFQkgpqED54jUgTzcbCiXxsqgWlRLmDUTPVnnksGiCioKM9GGjGQBXAYBlgk7ccDP4DIJJwB2Ah8cPHt++PzPzQdPj/7nn5sfPf5/P35uTBccsFAOISWGLMingLwt5oDRFdxZCZQKysYK5FAZbbX0JuBosJHcFsp1YJ40q64jGQC7NWTaCa13zoh5Ze4My9Uzd405k8FbdYAbp30xP8aGh9Br7jR1LZVZQHMfga2qICKoAh0XEkovCpYEBdhridWvvkweYkNX7cX7WtNbq3DU8EX4UoX+ay5EZrT14KPmbz/H7AC7IAclShvfgjGjoU6WoLM8hxwpYRf9q9TLqgg3p2gSWIPx8B9H9z8ktDNj7Fe8D/Y+3//hXvOjFwf//tUrIuSrgqICGcl3ZhXxOlpF8xuIvN/oT+Bv9ZZlPHFDLsvQ15TVdlHGpPsvym8/eXVFOULmd9htt60k43kbekmWau2CjCnHNJu/aH38aP+nJ05cOh/cNrFSk2QmxhvDn9u9tqAcW+QjliVBmyPdQ1WEbZCqZxfL0g76WAFqSULbkaVo2pD3WHVb2gLzNRE+qsuIg+NCTRwvAnFcRYMUVDCudRC6CxkqKuE6nIyKtTftudYd1Zy2GPd8IpZZisYy6fnUG5lwfG0xtpRZXo8sRTOxxUx4PZWOr2Ziq4l4Mp1JxFNtlOqCeNeY8+aLH5vvfoFZDZmSU62k0cdeKEsko5FYuDcpmNd2/CfPXHVNRx+JJaOw54VrmXgq1Z2Akf7nWlC2xokgj+ZFGeryaLYxKimKMbp61m6AvvSo1LOj2OyH2jrzwrnU+kJmNR6JrvSYtsGYB7iimMxDdHew5gEw/XWbQUccjm6wsulGQe+/dLjD1naz7GZbqNXguulut721vZjOJhuJZP3NUm5CKU7PL8cvuIhswF6y+J00oTkPCgJcikI4YoO8oDhaUKVCgV2WPnh49PvnqFEgV0TsRvRO8tlTmkFNZIQcChyVpB1IFpxQkgA0+KjR1M5Zf2QXRyRGCWMUhnfo8bz+VOPgXct8BqBMZanI0LFCPgWuQvr0Pv6i+ctj9AB2bNUlyHZET5tBtFOSNosowSEWx4V8RayO14SiWC2Owwe2NsofRqqSVM4KcvvcpskXNrOrU7IiNCC3rNrrZbYXJ6/Er0eX1C0hfPmNyeTtyMWrTPARh0otca+aVAVVwzDgEOt8WRQUIxiLVxLtWRfHGOfWc6AklfM4/3344vvDnz9GngQcaknM59FICkJZAYwl+90fm99/cfDjJwdP3zYIpR1T9qSjG2jCd8S8WgrNTU7P+rPOrOXXY5MXpxulKWW6nMvN7kwr0aJ3ZtGgdKDM+svHzY8eGSTSLp2zyRlj0hP53bXKRq02+9bFQnFi+vJG8nbNwhh7QV4FquCTQjEKhEyQaV0pQ5OQlHY0CAVZ3gpiGW4gmRTHiRXuEylqPYmm2dU+iD6697D1/v/pze9v9pp/et8gnXZMSU9FV6CnxRA/6YR4LesbJMuP/uX5wfNvDLq1PvvguZ6GDpLu1sP3W09eGnTrnfZBuDUxHiT9hy++PNh7r/nk6+Zn94xRWCnoYzBG3j5Q4X/3i8O3P4AeVevJ54wQ6X3TAUTm01FmAOenHQzACigIchgHX+7ZDMNKQR+D0fEOgcrUy//Y3/vp8MXbzQ+/N0ahd92HMOk4jCDJx+FLhnC9U0r42vrqQjTJku6E8yykI9C1y16Vew0Aui0FutfQUZoONpu9/RjnY+0UMkAB8gTexbDxcGugoKGooMLEsCo4lR1SRNiHu0VAqkL3at6SyvADqBCvgSpCeKE+6D/Z1AUKn9/RnEe4STfmEBrp1jvvHj37uzFq3M7dETbjYcpUyCAnyflLY2KetSPoWczKawI+ts9YyDR9QN1MTYDADkrbwg3lLZyWwD6qFkzBG06BBhcg/2/jaahAvxXH41xNsuFchzWh7LpRDCLgVqmpDSbeRj8aUQKg5GSxRqd4f+/fmt9/uf/zX6Cp3//hb60/fN169Lz50b+1Pv/i4Mn7ZB7/B/y5WIH8S6mNMp7zEhCLJejLT01N6EoJ/w336z6OYTQr7TJBF/xBH4WikSKU4VIzSsEXoRw0Z1i286ICTREipFAG6JcayaHJiYn/Bj9v1hVVLDRGdfyR9tu7rnb6/c7mTiI1tbq8VL+SjG1XJguJlLISrxujTmloOX3gKFyDAjxn1dd/c/ZOJDm/mJ67g+iduxkqQOenLoOboRH0Fnxw+OjhwVe/3IQjWF+Lrs0vrEQj+ss0eGa83HryV5IaRe9b31bquRxUM+Pt5vf/ZbwdiaWsP2ijBf0AJ6zgD+6eu6HeOnc2DzXn0hhBBJ67NIaTqoT5p3WESK6cKglzmADZ2lFVVPHCQEUDGbQ0fWRIB34wF7pz5yztG+1fUajvZuhm6NyZfzpDH6NG9MfnyL/Ihmz/l2ett18cPvvqpkrUH40GhesUEGUMNjWeplCCv4aOGT8N0xngFMf4vFOGvZo7Q/F6sbwveXwDcrUAmzxxqXsToGqknZS0kGWouRHK1WV08ih0q3eeX3dPCfCpPQ2B5pJ80AJYpo9i3pl8jyoloZor1Uf134+K+UkUZAcF9SoRE+qZ00xMtFpHAqvkJPwRQ+ydstwBZh+1Ra2IjtXWfDhBJhRhAWZf6C3PY+Z36ViuzifxE9SuuT3aE2Il/gF1wEyNrBHppw+xbwd5IguqhDYfa9Er8EUZLfs6H7H+0U8hatA7jU8nMhxfS6XNrh2ZqRUgFLB+9TXZUyGcUYGKZdA+v8aQpfe3JEv1GsF7WN6PJzu/bs1Leba+UOAabmxvwFsOJMsGOc0Hnzb3Xo4f/PjJ4c+/c7fzQLmvMWavoUd+aw3f9x66Wfnzo+aD73yC6NitlCQXOICl0hb2lpBqNE18utZLBvcWJOStT/hmjxMerk6pOoCCugPQ6RpA0GuuF1a36+KpWMucL2U2HOl3dQt0cQrKANIgT/D2zxbB/grYv+FFrw/Y/LlDwuvmj6DQufnz2ZXn9o/aP5N3PLiDjLhfk6tLXPIAd9BZUahuinwD/SpsoG3mOuj9s5ejso511jva8wSiMtvNFJPpqAk5NpTdnoXxFjUvASGPrRkTNr+sPfME7gwqyN2tjAayXqMUiqadj8a/PE6QKLKFJJPqFUnXd2iqJ88oEq4vnvkJ4OvMs94QvuC5pa8OffFLb8UX5FFHjh389rvWPUa+rIstxQ4GzzUKWeyLZ37iLXvppRPEZfBc0wCTfbHNV6RnR745wXoGzzAdqdkXx/wFmXZkmSOYafA8s+JC+2JdIDDXjhx0D3QdgM7q+NT+tNZfiG1ne+cCZBs886yo2L5YGAjIt/NS6xrmGzw7dXhuX3z0F1/cWZmdI4yD55yOD+6Lc/5CmzuLnjNw88CWkv4Z5zuwurP9cwyt9oBJ6xk7H6X4OxJGsTANP4ypoMIyjobN8Ci87v9BLAqS8ejCQjUSC0sTby3Kxdl5o/e0kLX0OlC4pwP+oQ9lsbqVyRVIpsPlKeQewwmOzqE4U7s2FX7jtc2VuLy1s7G9NSqsbc0sLhukONBVKrU0xkk1yrxmMEUkHSkww6ruC3CnM1t/fNH86Mv9H/YO9v71YO8+s4AwlFBOLsTjKz5FKGeqK5sbi6sgVk3XxEYxfCWx2rjqipc5qVyv4HB+iA1ejWh79VtWzrJFN/XdF2YPGTpZR7UvF80VJrvNhdYynQJcgHzVv7nTU6axakEiFl/P+tkeHViE31thl6gRQc6Vuv3O5shBl4NBZp7pXgfDZCom8YVlvOv3RW6m16c2yperQjq9kF1X1xfypc3Cmle5gfwPE9ExKpvCf+nZZ/pZB8qLCk4qjCA9niR/psif8+TPNPkzQ/7Mkj8XyJ+L5M9r9OcT9C9tZ5I2NElbmqRNTdK2Jmljk7S1SdrcJG1vaoLQGhEroKrg7BsemKCqQq4En6mTpk9TdGikfgr810JdEatwAUdpFYMp7FM8p2wX2oMElnj4yapybN1ZO41rPvpD8913rBpnzEU3pVNUuZ5Tqf/yKqmczjQq8iyTe2mcMx1bmd/dqF+4elESCpmFxEx8tiAlJYuO9X/q34MbYHK1zIcTsNytaSE0u7rN7Ik+es5FK1HCRt/sfkpHlwRlvCFF06WdqnMpcKYTX0z3Sj2Lh0RMT77NT2VOfAXC3RwuMGbec1CWhG2+YsxrOSXeZoBHZfj/05A1cAjn4bAqobmpkdAulN4Z+Af+ZPJuV157E6ViA6Tjqa1KOJO5Lk9fmL2QqE8G5Jd74T7sW9u20GQbc1mA2+0es3xVxdwWdU+YcupG2BpHRa02me3bbJMJynn/5UurQWYa7yTy6BV3hrhrvOiY7W/nTS/DIi3zyPDTYn592B21iQ576cOgRYft2yw6rU+/bX3yNRedbkkLhkVauInh5wBEh728o788P9NQwEF2rG+tx98dPf62TeUCiQt3Urj+ucY2FCzXiKhZucb2b+Zan3wqDjQGdzs5uptNgMsT+fUrS6KyXsxXr0fcrfWux0vDNrBtxdSTCTNl1OjX5mH/h739nz4mZ16xh2HryJu++L97qOAz/QWks16FEw+dNGA9FOt5LL1hyZaZNHnZHqpiuOzb3j33p3CGvanvXTrDaakM2L6olPD5cgxRVM6def03Z8T2hs/e7AJ+TwnbLPT9ZmjkzM0uPaPv74yNjaGz1yNn4D9I13fPjaklUD17FtPwa3OPPaFtN0PnaMFG+EMZoBLF6Cy3B/7a7mH6tNMYfFeQJNUKvlvUnvmM9DP3wmoEc9CCSa633nk3hoyShrzVDwDiAxAlaYcSqllu9IgCBy163s/Gb1tk75uhJx/bOeOsmojDDIkGYfZQVaBzndAzl+A/CdQbfvinM47rD7RVHcAFCebgN+SqGpvvvdUj8MKdIUePOrqCzQY7uijJlZNWXlSulwFRYuPsjd6o5ZfofJMo6wWTb1ky7L5EuZ3z3oxB9cL74ysh2Z3r2m+Gi99mPKYXfvuJxvRS/dKJrGu/7Mh7Dck5YO5bcJ1e2H+c9Ts11h+zDFvBnl7YeLz1RLvKsP6r4bIcHeCiXpg/fDVRh0Sy2zCknizEUBRpbecoidYMmKMdgKVe+Dp8tWOHhMdWtKkX5g5DLdshMQJWCKoXdvoLQKUFI8Nan/To6lq9kiWHoeHYwvYEKU4K2LvIsjiec1HR0t1hnDbSKg+ZsbMmt54hw1h6aXOsQBi1dqcmcK1dl/X6gxiu38Pqv5B8n0Po6IP6OISeTmCfY+js/7kSL/ugtetYs5Go1CJ+FmboBVZYMIr1MdX9NOkLBwgN9ISXWi2YrLtMZRWfakazedwbhhLc6skgpSTgdnHagoY7TRpODAl6nqJlElA9ZFQlIVsGTBEHWg/N9DSsTxm5CtxsO9NmoA69FsR9cfP2lUzDsh/PpqHf5WwY6q0HzNPVEpiKrV4QXxMb6dJWZGo2rAjh/rjm/1ljq6DqAtrz3LF5L2uNw5jt9qC91349V7+F0uyoxJjssMgKom3wAL/dMXBwTFbABi/mkNMcLTY4tK5Z7HpAxdqtoa8weRe7pXZA2YBki8PJvMpWDyzZ0MiWDeLMU3bQb7xZN43tBjbrnjox/7zjGnIsISYbFJunsIjfGLZu8t0NwNY9AWD++XDNxDaQMajA7TyYjw5OWGyp0WoAnpRYhVsvoWx0rXurDx+0nvynKRhoENJ1ithfDpu3RS6CcO/Vdp8ho9VBztDRvb3Dn39H7zJ4ev/wxXvGsAyKuk6VbRPDNmeSLBbxYVUPO+Xu82ZueZBz13p4r/nNn6zHcs30uNhU+8hyyNcOB/ARywOD/sanKxNiPQ82I8WZa7V0PDL6mpQ0+g7s+H0QkssevsIRLszULHPu3v2FNHft7k9nL+5G0kuBdXmcCsPQOoQxhl4tyJ+ZMy7tVnBkAuShr6pcGiuDalEt4Z9OhFiPid5fAokowy0ARr4SvKnWDh5RElSkbXBp7Kxt8wiqd+PWOSMg2bkW5sCYz3DdWymEwckMEQIqLSsk5upUYjxV5iWRYsISoy8Zz7G5Oq8uH5RVOMA+4FmVgSTn22Y1iQG57dNqG0fzbYZnlhPXpUj0WnpZKBU3wFIltzSTYIzn6S0g0b5O4VoS5ruX/XAZSrfja9KGWgS70dvhy6XN87krW+6ucud1JYbgkHvXuhId4xuGOM1e9EecaqNT27OLSroMstdXN2MXJ5ajy1Wv4sTLTfByEydOE7uVm/CoiA4TelJ8dnY5GS/HNgrF/Epxd6Iyye40/KlC4c2rMG06ymCRPHJRjAKyHETIWk6uSdR2g15qStjXjgiCAdij0qDYduMy1YzosP8ehooRy9GpzcL59JR6TVRLM4vJlcjslbWQq62kS/YGe4b0FirRL23BrTjWRp+OjToNBtjs/y1TLhnmRYfjmFxpFwdKXVDlIlyDUT1oA0uuNjJf3IfvN/JwshQoGFz0q1+13QPYFXszrPy4NPZWHcgNdPCu1nDAlTB8DXEFKvs2kFUgd2aNqeEe/DGWGwMshHedWLbnQiVJFm8jBrrDxgdwstW3kADd5OeEag4whwcGdPUOmqgepzMRmgv+Ex2tRJWaboYskYAPP2l998DvsDDliiJs+3kZG9xci3nijW9r/9TD3L1uWxsKSKBVtnFhDAsWX7ukjSXM8pTSFc9uEpCShTKNYXf8u96I2ClDSmgXLLqxXTc7H533emuci4tikdwbskeOfru6IRYdWR5jMVTMIuHX5bA2t1fruh3q90a8TrfYlcE2Vn2lnsvBhyH2Xrv9Xz5r/u2P5F67X7m42I78LoBIcWBXBuk9BFquQOvF3DxrA93WKWA8TXKdsOk9ukxbqxkYwF6fihmQ4Ak5Lq+oQm7LPMgU88jGhyYd0MKBRkz68eGzr5CKojGalNjMGPMr9Mo7871z+BV9JGXzdqSfwTI7KfzQMpu9QtMOY1u+FSFw0Z+DkhDWHZ3nvgJVOX/LgnhSN5viIdpxCFpUgx7nfv7s4KP3Ovir0PKqcEFkzlIUZJSkq+aNJQ86HzTSE8oKCnHf5ZqxQKE1ICROXmR8LPQJrlspssDoQHsd32xbfwM+t0WIENax31pqMpiuiR4x6g3o64UZ36vD1NnTPuQbBrVpOe3WCV5kXstYGjUqTLe4jljPttjiK8j9v6gW1A/3LLegt59zZgOJLNsshLYnaloP/3F0/0P9smxTp+arh/V7Ni2ghHYscJcp7TJS8lDvjhKmXWNrnW1yv/2IDVpU54mpkpYmbniCce+6pGhj71QBofXBw6PfPzdI0jKanWB/luPS+vuW476W86n6YS19yKS71seP9n96osuM/polNGsb5LZNQZnCSSP210yfVV//zdk7keT8YnruDrIYczdDBUEs12VUiwrlcOGDw0cPD7765SY0IOtr9KpO/WUa6jJebj35K+kDvW99m/qHxtvN7//LeDsSS1l/0EYL+gGeR1S959wN9dY5WgiIZOzOXRqjJ7tYSTEQOSPa7nTEmnyyzK1FNCjzWO28RcxgCu7TE4JaUno61zuJ1NTq8lL9SjK2XZksJFLKSrw+hpeRMXLOyZcLp41aMbRtstj0aLuH50+bYk9vjs04a9n2sE7ApJ0fMyfnxthsTI/uPECeKQmD6JOZV5yRGZsYq+jbMJ/6IBh+N1Pk4QhCwBIwFaDYe59nD91d9GUgBPBE28e7JsghNlY21hYog5KlFSx3aJSYtIeppwmfhMgxji4g0cUnRgejFhYRc9ebKYDshl5bzNIAjRupLuGJw51kb9LvmdKPuboh09P9YH0z3r1NcKip/QzHUQe2UJxgNI8eYuib2e13ko8CnKSnLZuMrU1UGkqqw4Xc/bRO+ti705r1fk23F0vokkZfJt3d8hGcnLllkNYIxY7bSasPrkFvX1rKAye8+e9gtzbnTPWNGKS2yaLBR/9dcUdMt4WrBm3k7VzKAKw82X274cZafj02eXG6UZpSpsu53OzOtBI9Fm5c8HtF0Y9++7pgOTwQ4KZP2/vV/DRQnZwyh5szu9x/ME6AqVicz6tC5xuBAt4CT04MxGk3rWvt1c2s2/IBeVhe16zy4uSV+PXokrolhC+/MZm8Hbl4dScYoUMhTh9FwNbvC9RZDiwARU+rBrXDMsW3BqEizEy75JnnNdj7sLqtCj6L08Anws7O9+27BRxgGARfaCnaoBekwYwFVaIJeCQXBjISU+FlP1eKKghoSTMF8LyzyPby7eC9pYD3Ab56PggmElCIJei81WDCLR5MWrdM5UC3pA79Ni85n2DyFu747KnUR7Ce8pSPEVKvy+5ARWx6IFpvduQD2y9YE/G+7xgGbr3cp+DdtO4+z+a59WCcgGB2N7l+kq6uhTRobfAnl+x0BRhwMoVJF/iREQrEVXO1UxlMj3Y7iuNwx4YuLunQ+fcERPJhsH0ia+zSfSxQxpfIabeEju9rjNvYqdPrso/DFZsdDFjRsJiBrTyijzGr9pxPTw99yl8MQ1CbGmvzLqejW4YiMLm1xQq5mpDeiXvrpb+uU/d9j8EfoENP6+JInra2F9PZZCORrL9Zyk0oxen55fgFjyavT3CcO7SIX+giPccHFSuoaGXwSRlmofLL0XLrvQbl2bzm/7Lubx7eu0kevP8f1CT1gwHwhFYfsOvUN645OO0YhOD6u6FzgVAL7uROkKj93tvA4diZDpAY34MQtjtMZ2paXdncWFwFsWq6JjaK4SuJ1cbV4MSvN1Daqz/YbSCBInL8jwYMKBPiNp4XGDzN597NCRDv+uVCNY4/mDYYnIfoNYFhv4cJ6uCLe/RGf6cXfLeMPtJ8XKnOIGFrAe4dPaPVPCETB6Gz7fn8AYc+B7OxC/SkqD/HiALwVo9z4dYiWag4Te+CPnO0ThRUMKku5+h9OHmg5GSxxt5H66A2UK+SaUypqWdftZ7+GELl4VC5ZL1oVdfmaekhvcT1s78fffa/Q3p1H1L0bcRRU+aaU0a1xg9aTz+0tsuUoiJF58glu2ytrZoMoIXCIjfXefyZxPxSbG0pE5lPz2dS0eTVWDga0kopESbjGUZ1hEqqWlsFaknKa1+xBT3pHcDaZai2BUKZypyBzCZCTjK80yvNnLAZdda6B8nWruZGfpVP3RBfhCnapzMfV+s52PtXo1/T/S1WuWWuoyBVQ4OTEkzyON5SjndtqEsblXpZFY17gYyLokylibjgDaHg+dh928zrVaK0ew2h8cxLO1VNpvsz1gvz6fDlTCS6Ek1HHZhsU51SG4t8ghRNUQVZbdczrUQX1zOuZz7q2ep8Kh1NEg1bXV9JxzKk8NmrpnNSrV3ltCp9XOW4ygWqcpFY6qTonAh3vqblqfniR1LOkGvJEGpJB0kdF2rieBGI4yrqRVDBOBLvsiTk7cXOvCMOLUXRZUudt8TzNdF+5POJWGYpGsuk51NvZMLxtcXYUmZ5PbIUzcQWM+H1VDq+momtJuLJdCYRT6V93Z47pSeRjEZi4eMggI48EktGYf8L1zLxVCpAMgwhEJStcaLYo3lRBjl1NNsYlRQlAGGw7VSpZ0dxAwOUPsrs1PpCZjUO9xn+M3qAVhns2ljl+zwedLKscidRjW6wduEky2lZKpoY9fiL5i+PuZQGKqWqJJWzguxMTtPkZUf+gwT7KI4L+YpYHa/hKx7H4QNHJpzKcA8b7nNA3Q6tzk67udw7F0nvIpmeyO+uVTZqtdm3LhaKE9OXN5K32f1182+PW8//bhI8kj3rEir3QwRsDwaxdGl3J/DJP32T7+LCQoYPzUd/IKSeSpHodNORvkI8fXD4l/ean/5sdGLcfuSwD+aWqO4M6S+qshhbi5AACtw4xSKBZHhHXNIUjieuEZrg3vJqNInCPsOfeXaGLyFvscqiXZfDNcWjpjCM3za3bm3aY6uOuD38ahjQumC97dQQIXpPCxfsoJcAd80ZV62ZW22+/MPRO1+Z26a3l7ntoX3TdPT428PvP2OYga9sDBDi0eHeWY0e/bIxLp1cOh1KZx8Jq9T81aHIU3U8OsIK4anGJr06HomnVo07RpnGm3/+luR24QyYe6FXWbrva0A6yCKhHHhWx6F/qBAkK+fafYlc+7j2DZv2BbwSkfNcrNidaiwRV4YTrQx9uINDhFzqeCkDK/CnGkXL1fBVVcPhwex2KJvEMEO/1JvrINfBYdNB3zWBo2G4YpwGxTjJyB935/NZ2X8FIEEnOcLuDcmhgU7gG0uyVK+57pYpYmke0cHfGC7hrx02HZ+uTIj1PNiMFGeu1dLxyOhrUrKt7aNn/zA1H1NBxW+eaYMjnLFhYPPddw6+fWrDRkLN8UN49GsSWGU51cAursJchU+fCuNO29bib/aaf3qfKzJXZK7IJ0WRcV1PloH/8vzg+Tdch7kOcx0+KTpMCg2yYvzw/daTU3vAkCsxV+LTp8SmOrDsMF58ebD3XvPJ183P7nGN5hrNNfqkaDS9y47Vl3e/OHz7g9bjL1pPPue6zHWZ6/JJ0WVTjW1mGAdf7nGN5hrNNfrEaTS5fpql/+V/7O/9dPji7eaH33NV5qrMVfmkqDK5AoBdlf/9q9YDrsRcibkSu1biHkBnpGsn7LIG1zE4ky3hkTduS7gtGQaHYLUEpmKrF8TXxEa6tBWZmg0rQphd9X/7Xeseh4JyTeWaemJcd5FdamMRrrxcebnynhTlzcmAXDHKasyDT5t7L/dfcvQJV2WuyoHsvn2t4heytB7yUFnT35iAE4qOLdKoXarMKvyn37Y++ZpbPG7xuMXjFu+0WTzq45lhTNjHaz3+7ujxt9zocaPHjd5J2bFR/8WkzcR/4drMtZlr88nS5m0gK6htJgXy8EHryX9yABNXZK7IJ0iRSXFsNhVydG/v8OffkRrZraf3D1+8xzWaazTX6JOi0ZIsFsVqXC6asESth/ea3/wJDuJg7z7XZ67PXJ+HXJ/Rh7JY3crkCqMqKlfcLgatpz+SsXCF5godmEIvR6c2C+fTU+o1US3NLCZXIrNX1gar0KwuYM3WCsiQ7+FatwK/XREVNrh0eP+vzef/a/8fDwl/yfWSjDTWFVWqIEbjWyYDVndnd+fxYpO8CKwLcel81SLtklZ0cN+p/1YExKIgGY8uLFQjsbA08daiXJyd99WKuOFZR9cAM9DGNSCcHJRz4MZa8IqW3E5wO8HtRC87AdVFrbOzxM8scEvBLQW3FLzALrcW3Fpwa+HRWvAqvtxQcEPBDUVPQ8FLBXNLwS0FtxS8HjE3G9xscLPh/06EFz3mBoMbDG4wnBoMXlmZmw1uNrjZcGk2ePlmbi+4veD2wqm94DWiuaXgloJbCl4EmhsLbiy4sehvKOxZmLLlxAs+EsZNBTcVJ9NUFBsgHU9tVcKZzHV5+sLshUR9cuCmgtWvHD4jRjhETokxfNSPl2GG2h8vY386SMsBWaIZPF4vl1uMU2sxbidHd7MJcHkiv35lSVTWi/nq9UiAFoPRq6JfHoRGBq9C2ZWigZpLXmyXm0tuLrm55ObSjXfJK/Vyi8kt5tBZzOPwnHiZX24KuCk45abAWw2z0ayg5krzOUzTKKk/avIbUOFRbia8mwle0axH069KRbMAaHVQidDgWBkgYoKlw2RNWEX64OHR75+bRHCBfbOzZVyoqyo0gwM3iwKmjBpEOPvcKHKjyI0iN4pebIkMJDnvyCgmQY55NUCrOLOcuC5FotfSy0KpuAGWKrmlmQRL2x9fND/6cv+HPV6jmts6buteQVu3KsVnZ5eT8XJso1DMrxR3JyqT7K6XDNcsVGic3cyWH9vc0u34mrShFsFu9Hb4cmnzfO7KVpGhixgscgiEmy1utrjZ4mbLk9nqkURE4xHzoKqKagOOKbgsp6UjLwlPK62B5D4d0OnvSlAbndqeXVTSZZC9vroZuzixHF2utq0ESMTffYevBHwl4CsBXwmCWgkUVa7nVCi6wa4DejdeVwGDzsDWgK40+rsCrE2F33htcyUub+1sbG+NCmtbM4vLPIrBs92nLdt9ys9iBEJip+NY7HritOuV+d2N+oWrFyWhkFlIzMRnC1JSGoJgyEx1ZXNjcRXEquma2CiGryRWG1d5MIRbP279uPUbQuvHYypDHlOZXp/aKF+uCun0QnZdXV/IlzYLa20LCo+p8AWFLyh8QTkRCwoPzRxHaGZrezGdTTYSyfqbpdyEUpyeX45fYI0oBr+c1iUEKUsXY+mXHvXohqAxKZTRXzgmeiKqZfSIGgb6wkoslUbSpeRKoCJcBbKC5ea8ZT37/wYuj5rUAwIA", "runtimeObject": "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", "id": null, "teamId": null, "teamCode": null}