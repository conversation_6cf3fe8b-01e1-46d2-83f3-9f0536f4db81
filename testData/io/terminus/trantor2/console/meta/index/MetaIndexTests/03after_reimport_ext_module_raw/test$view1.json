{"key": "test$view1", "moduleKey": "test", "type": "View", "parentKey": "test$scene1", "path": null, "oid": "70be9d51deaa11acd73d53a29d5a786562cb9fb1f7fe7a0fd81caeefb28e7d4a", "object": "H4sIAAAAAAAAAKWRS0sDMRDHv0oZPC7F4q1HxVMRC9ZeRJZsHDU0yYbMbB+UfPdOVrtGtGAxOc3r95/HHpTWSARTmEezVoxQgX439iWih6nvrK1ghTuJMxJfrA1uJpLilUPxLcUc3W6VCxZH2R+U1PHsq4A0euwjVvFrG90SI5lW2HvRQb2izknu5T9fbqlzDcbMGvcfkojGNlAv1XqWxj5Uj9M9nTaej1M3xbTXHbN0XmADRmcozzP7vqM61M2kzmFIaYBxAVuoxuJfWfyTFQrWXL2VqJTOu9kp4U/RCtiw/bWUdyH7bx4fFvd3eeEkN3VquPHVkJJLIR0AHST19G8CAAA=", "createdBy": 0, "updatedBy": 0, "createdAt": null, "updatedAt": null, "node": {"schemaVersion": 3, "type": "View", "props": {"content": {"children": [{"children": [{"children": [], "key": "b1", "name": "<PERSON><PERSON>", "props": {"permissionKey": "test$view_p_b1_perm"}}], "key": "t1", "name": "Table", "props": {"permissionKey": "test$view_p_t1_perm"}}], "key": "p1", "name": "Page", "props": {}}, "key": "test$view1", "name": "View Example 1", "permissionKey": "test$view1_perm", "title": "View Example 1", "type": "CUSTOM"}, "key": "test$view1", "name": "View Example 1", "children": null, "access": "Private", "parentKey": "test$scene1", "platformVersion": {"number": "0.0.0.0", "checksum": "0000000000000000000000000000000000000000000000000000000000000000"}}}