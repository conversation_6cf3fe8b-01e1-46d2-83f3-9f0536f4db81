{"key": "test$function_item_2", "moduleKey": "test", "type": "Permission", "parentKey": "test$random-key-2", "path": "/dir1/", "oid": "2ba907dd37979b672a5eefe0391ef6474be1e6ef3e2a6f4924b968fc70c39e08", "object": "H4sIAAAAAAAAAKWPSw6DMAxEr4KsLqH0s+MK3bDqFqXBiIh8kONUQoi716Fqe4AmmzjPnhmvoLTGGKGBlsxTMUIJejS2J/TQ+GRtCRMuwhkjH4bkNZvgO8Pouos0e+VQ6AcUGRQZzEok+PabJeX74CpRq96cR0F1b+hc59oqHgK5O1IUIWhWCYJ6islJ2+nPk5Mm90DKWsf9wiamFGZZfpVnFDenvu7XEniZ82otkjNx/91e8kEwvTABAAA=", "createdBy": 0, "updatedBy": 0, "createdAt": null, "updatedAt": null, "node": {"schemaVersion": 3, "type": "Permission", "props": {}, "key": "test$function_item_2", "name": "function item 2", "path": "/dir1/", "children": null, "access": "Private", "parentKey": "test$random-key-2", "platformVersion": {"number": "0.0.0.0", "checksum": "0000000000000000000000000000000000000000000000000000000000000000"}}}