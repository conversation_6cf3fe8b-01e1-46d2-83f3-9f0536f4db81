{"key": "test$__error_code__", "type": "ErrorCodeRoot", "subType": "", "moduleKey": "test", "name": "ErrorCodeRoot", "path": "", "parentKey": "test", "description": "", "access": "Private", "oid": "aa70bcf2f9ff471ac14dbd6e3179f417d7875a0cfcc06cb5224cb3c06d061424", "extOid": "", "structure": {"customizedField": null, "links": [], "refs": [], "innerNodes": [], "viewContainers": []}, "liteProps": {}, "modifiedAt": null, "modifiedBy": 0, "designerObject": "H4sIAAAAAAAAAKWOsQ7CMAxE/8VijBASW1fExMbAaoXUiKpJHDnOUFX9dxyGLozYi3W2790K4T3FUSjDkFuMDmZaYAClqgdEEmHBwCMhgoPsE9ny2tWLiXdmNbl4+9fb/til6PXFkh4kdWIzXw1EYa4t2dHpz+pRWnqSdK/jt2EzqHCphrKxGi35nX52oEv5zb59ACDeo0wAAQAA", "runtimeObject": "H4sIAAAAAAAAAKWOsQ7CMAxE/8VijBASW1fExMbAaoXUiKpJHDnOUFX9dxyGLozYi3W2790K4T3FUSjDkFuMDmZaYAClqgdEEmHBwCMhgoPsE9ny2tWLiXdmNbl4+9fb/til6PXFkh4kdWIzXw1EYa4t2dHpz+pRWnqSdK/jt2EzqHCphrKxGi35nX52oEv5zb59ACDeo0wAAQAA", "id": null, "teamId": null, "teamCode": null}