{"key": "test$random-key-1", "moduleKey": "test", "type": "Folder", "parentKey": "test$__folder__", "path": "/", "oid": "b5b90139528ac3055ea82e39e514852892054ccfa5c2f8b8f6eea66b5c006b4f", "object": "H4sIAAAAAAAAAKWPOw7DMAiG74I6Jn1uOUCX7l0tNyaKFb+E8RBFuXuxpbYHKCzAD3ywwThbZwgDDKE418GCKwzAmPlAOpjoe6n0F+ggaI8iGUtXyZKWIX78upWaojNISjWVZxFONXSap0j+iZRtFM4mTByXXLx0nP+0elfxL6S669gcdoFSTPnzUhae11/+rQNeU33l3g6G/Q0BR4wyBgEAAA==", "createdBy": 0, "updatedBy": 0, "createdAt": null, "updatedAt": null, "node": {"schemaVersion": 3, "type": "Folder", "props": null, "key": "test$random-key-1", "name": "dir2", "path": "/", "children": null, "parentKey": "test$__folder__", "platformVersion": {"number": "0.0.0.0", "checksum": "0000000000000000000000000000000000000000000000000000000000000000"}}}