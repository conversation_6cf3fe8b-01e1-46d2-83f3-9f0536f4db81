{"key": "test_portal$__error_code__", "moduleKey": "test_portal", "type": "ErrorCodeRoot", "parentKey": "test_portal", "path": null, "oid": "1c8ae3e0b548d22847563587e9e1e23c9dbdf72f1398f46c737ae7dcf4716c09", "object": "H4sIAAAAAAAAAKWPsQ7CMAxE/8VirBASW1fExMbAaoXUiKpJHDnOUFX99zpIMMCIvZ3P9+wF/HMMg1CCPtUQOphohh6UimJmURd2iCTCgp4HQoQOkotknnNTTyZemdXk7CxGL9/7bRKcPljijaSMbKjFsOSnUqN5D39Wu6jGO0nL2r8aVoMK5/L+qhgvug//2IHO+feJdQMX2s6OEAEAAA==", "createdBy": 0, "updatedBy": 0, "createdAt": null, "updatedAt": null, "node": {"schemaVersion": 3, "type": "ErrorCodeRoot", "props": null, "key": "test_portal$__error_code__", "name": "ErrorCodeRoot", "children": null, "parentKey": "test_portal", "platformVersion": {"number": "0.0.0.0", "checksum": "0000000000000000000000000000000000000000000000000000000000000000"}}}