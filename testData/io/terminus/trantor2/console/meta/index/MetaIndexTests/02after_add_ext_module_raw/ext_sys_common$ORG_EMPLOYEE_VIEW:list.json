{"key": "ext_sys_common$ORG_EMPLOYEE_VIEW:list", "moduleKey": "ext_sys_common", "type": "Ext", "parentKey": "ext_sys_common", "path": null, "oid": "ca557a722604f1bd4635e78a028311b67095132f6d618b0b5cf1c42bf7886388", "object": "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", "createdBy": 0, "updatedBy": 0, "createdAt": null, "updatedAt": null, "node": {"schemaVersion": 3, "type": "Ext", "props": {"originType": "View", "snippets": [{"action": "APPEND", "content": [{"children": [], "ext": true, "extProps": {"appended": true}, "key": "sys_common$ORG_EMPLOYEE_VIEW-kvFTbRyPRuWhc0sg4AJO7", "name": "<PERSON><PERSON>", "props": {"actionConfig": {}, "buttonType": "default", "confirmOn": "off", "label": "按钮", "permissionKey": "sys_common$ORG_EMPLOYEE_VIEW-kvFTbRyPRuWhc0sg4AJO7_perm_ac", "showCondition": {}, "type": "default"}, "type": "Widget"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions", "node": "children", "parentKey": null, "rootField": "content", "validScope": "ALL"}, {"action": "EDIT", "content": null, "func": {"params": {"constant": true, "type": "Const"}, "type": "SET_JSON_VALUE"}, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch", "node": "extProps/removed", "parentKey": null, "rootField": "content", "validScope": "ALL"}, {"action": "REMOVE", "content": null, "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch", "node": null, "parentKey": null, "rootField": "content", "validScope": "RUNTIME"}, {"action": "REMOVE", "content": null, "func": null, "key": null, "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-useQuery", "parentKey": null, "rootField": "containerSelect"}, {"action": "EDIT", "content": [{"field": "userId", "selectFields": [{"field": "id", "selectFields": null, "sortOrders": null}, {"field": "username", "selectFields": null, "sortOrders": null}], "sortOrders": null}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "node": "userId", "parentKey": null, "rootField": "containerSelect"}, {"action": "EDIT", "content": [{"field": "employeeOrgLinkList", "selectFields": [{"field": "isMainOrg", "selectFields": null, "sortOrders": null}, {"field": "identityId", "selectFields": [{"field": "id", "selectFields": null, "sortOrders": null}, {"field": "name", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"field": "orgUnitId", "selectFields": [{"field": "id", "selectFields": null, "sortOrders": null}, {"field": "orgName", "selectFields": null, "sortOrders": null}], "sortOrders": null}], "sortOrders": null}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "node": "employeeOrgLinkList", "parentKey": null, "rootField": "containerSelect"}, {"action": "EDIT", "content": [{"field": "userId", "selectFields": [{"field": "id", "selectFields": null, "sortOrders": null}, {"field": "username", "selectFields": null, "sortOrders": null}], "sortOrders": null}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "node": "userId", "parentKey": null, "rootField": "containerSelect"}, {"action": "EDIT", "content": [{"field": "employeeOrgLinkList", "selectFields": [{"field": "isMainOrg", "selectFields": null, "sortOrders": null}, {"field": "identityId", "selectFields": [{"field": "id", "selectFields": null, "sortOrders": null}, {"field": "name", "selectFields": null, "sortOrders": null}], "sortOrders": null}, {"field": "orgUnitId", "selectFields": [{"field": "id", "selectFields": null, "sortOrders": null}, {"field": "orgName", "selectFields": null, "sortOrders": null}], "sortOrders": null}], "sortOrders": null}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "node": "employeeOrgLinkList", "parentKey": null, "rootField": "containerSelect"}, {"action": "REMOVE", "content": null, "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "node": "version", "parentKey": null, "rootField": "containerSelect"}, {"action": "APPEND", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-kvFTbRyPRuWhc0sg4AJO7", "label": "按钮", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}], "func": null, "key": null, "node": null, "parentKey": null, "rootField": "resources"}, {"action": "REMOVE", "content": ["sys_common$ORG_EMPLOYEE_VIEW-E96PvC125HeW2hFhkYT6F", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-logs/logsService", "sys_common$ORG_EMPLOYEE_VIEW-r5sqCH6oz7DYjBUX8isJS", "sys_common$ORG_EMPLOYEE_VIEW-SrjZh0cQ3iEEAHdCsovPM", "sys_common$ORG_EMPLOYEE_VIEW-Z17zjnvlUxUxrctOw5AXO", "sys_common$ORG_EMPLOYEE_VIEW-73krnEG_K4iZ83w2kfRKh", "sys_common$ORG_EMPLOYEE_VIEW-7dhEdFCBhiZLwJsdhGot3", "sys_common$ORG_EMPLOYEE_VIEW-JA-6QZRtf2T-s0Zl5G13i", "sys_common$ORG_EMPLOYEE_VIEW-qMzCNPASjtc88ah-evX4K", "sys_common$ORG_EMPLOYEE_VIEW-JRSbjgArNBRX2sqHbkxQd", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import/download", "sys_common$ORG_EMPLOYEE_VIEW-Ckq--FIuh39tnjKpJ7xNA", "sys_common$ORG_EMPLOYEE_VIEW-qtnqJB0BOQkCtNfpp_Qi1", "sys_common$ORG_EMPLOYEE_VIEW-WDxb5V4fd6YhXND2aStP8", "sys_common$ORG_EMPLOYEE_VIEW-1gGy6bpuNZlFDh-JMI2ZR", "sys_common$ORG_EMPLOYEE_VIEW-7YZiImqhIh_mfe0cKRyl_", "sys_common$ORG_EMPLOYEE_VIEW-SwHvGH-sB_HUC1k2GE4Hw", "sys_common$ORG_EMPLOYEE_VIEW-PEzy6Ljaf6b6YmWodb8HE", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export/exportButtonService", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import/importPredict", "sys_common$ORG_EMPLOYEE_VIEW-vzLg9Kpu2UET7AuFIP35O", "sys_common$ORG_EMPLOYEE_VIEW-Z9OiDoQid5m3ImTZsgAfb", "sys_common$ORG_EMPLOYEE_VIEW-ThnYEz_7MWgm2_AfFwgOC", "sys_common$ORG_EMPLOYEE_VIEW-5_-KsG3GsFUrMTTLf9aT0", "sys_common$ORG_EMPLOYEE_VIEW-nfb8cG0Qcw5rTOdpYOZFR", "sys_common$ORG_EMPLOYEE_VIEW-RMAJN5X30zW8dBXkSMkB7", "sys_common$ORG_EMPLOYEE_VIEW-ecBF_ECAWHl5n1UJLs7VF", "sys_common$ORG_EMPLOYEE_VIEW-KOSbO_jDBzfxPi77YU5UD", "sys_common$ORG_EMPLOYEE_VIEW-kG-QlMEZRLtAzLqkip3iU", "sys_common$ORG_EMPLOYEE_VIEW-kBQKTFUFUg9Xe8cCQsK4G", "sys_common$ORG_EMPLOYEE_VIEW-Bi4-xR4x8rdYsrXixNi8C", "sys_common$ORG_EMPLOYEE_VIEW-s4JzOber7OIiGUawNOdvT", "sys_common$ORG_EMPLOYEE_VIEW-Hb62hVtAi3vC-WIUS2-Q6", "sys_common$ORG_EMPLOYEE_VIEW-detail-sys_common$org_employee_md-logs/logsService", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import/importSub", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import/isCustom", "sys_common$ORG_EMPLOYEE_VIEW-llAMo4r9y9Ve5zFoxKwCj", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import/importSaving", "sys_common$ORG_EMPLOYEE_VIEW-7z86LmDCiZbuA6Es125ax", "sys_common$ORG_EMPLOYEE_VIEW-kLN4tCYlbfepDJNuhudl2", "sys_common$ORG_EMPLOYEE_VIEW-vBbTy3PIcls1A85LYYNUb"], "func": null, "key": null, "node": null, "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "label": "导入", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "/api/gei/template/download", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/import-direct-by-oss", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "/api/gei/task/import-sub-model", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "<PERSON><PERSON>"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "node": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-Mhe2IM7i9iyThkD26CsaC", "label": "状态", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-Mhe2IM7i9iyThkD26CsaC", "node": "sys_common$ORG_EMPLOYEE_VIEW-Mhe2IM7i9iyThkD26CsaC", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdAt", "label": "创建时间", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-zR-xbPeH0dUQGisUgdnZD", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdAt", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdAt", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-edit", "label": "编辑", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-edit", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-edit", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "label": "更新人", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "pre-replace:sys_common$user_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "Service"}, {"key": "pre-replace:sys_common$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "Service"}], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt", "label": "创建时间", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name", "label": "姓名", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-userId", "label": "用户", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-userId", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-userId", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "pre-replace:sys_common$org_employee_md_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "pre-replace:sys_common$org_employee_md_COPY_DATA_CONVERTER_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "label": "表格", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}], "relations": [{"key": "pre-replace:sys_common$org_employee_md_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "node": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-name", "label": "姓名", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-name", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-name", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-copy", "label": "复制", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-copy", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-copy", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-entryAt", "label": "入职日期", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-entryAt", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-entryAt", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-status", "label": "状态", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-status", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-status", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressId", "label": "地址", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressId", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressId", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-delete", "label": "批量删除", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "pre-replace:sys_common$org_employee_md_BATCH_DELETE_DATA_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-delete", "node": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-delete", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-logs", "label": "日志", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-toolbar-actions", "label": "按钮组", "type": "ToolbarActions"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-logs", "node": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-logs", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-start", "label": "批量启用", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "pre-replace:sys_common$org_employee_md_MASTER_DATA_MULTI_ENABLE_DATA_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-start", "node": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-start", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code", "label": "员工编码", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-code", "label": "员工编码", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-code", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-code", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "label": "创建人", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "pre-replace:sys_common$user_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "Service"}, {"key": "pre-replace:sys_common$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "Service"}], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted", "label": "逻辑删除标识", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt", "label": "更新时间", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-idCard", "label": "身份证号", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-idCard", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-idCard", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-mobile", "label": "手机", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-mobile", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-mobile", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "label": "更新人", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-zR-xbPeH0dUQGisUgdnZD", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "pre-replace:sys_common$user_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "Service"}, {"key": "pre-replace:sys_common$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "Service"}], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id", "label": "ID", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "label": "组织单元", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-JE2jf3T2tYith5FRLD6QN", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-form-field-employeeOrgLinkList", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform", "label": "表格表单", "type": "TableForm"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-MoO66JROlIXfgdLgx0m1p", "label": "字段组", "type": "Fields"}], "relations": [{"key": "pre-replace:sys_common$org_struct_md_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}, {"key": "pre-replace:sys_common$org_struct_md_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "node": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list", "label": "表格", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-gyeTOSkmC__Zr4767Pu1A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-customDetailField", "label": "自定义详情字段", "type": "CustomDetailField"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable", "label": "停用", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "pre-replace:sys_common$org_employee_md_MASTER_DATA_DISABLE_DATA_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "label": "保存", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "pre-replace:sys_common$org_employee_md_MASTER_DATA_SAVE_DATA_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detail-sys_common$org_employee_md-logs", "label": "日志", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detail-sys_common$org_employee_md-logs", "node": "sys_common$ORG_EMPLOYEE_VIEW-detail-sys_common$org_employee_md-logs", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-resignationAt", "label": "离职日期", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-resignationAt", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-resignationAt", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "label": "组织身份", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-gyeTOSkmC__Zr4767Pu1A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-customDetailField", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-LAxXu7V8oaf_BP5O6foRo", "label": "字段组", "type": "Fields"}], "relations": [{"key": "pre-replace:sys_common$org_identity_cf_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_identity_cf"}, "type": "Service"}, {"key": "pre-replace:sys_common$org_identity_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_identity_cf"}, "type": "Service"}], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "node": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}], "relations": [{"key": "pre-replace:sys_common$org_employee_md_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressDetail", "label": "详细地址", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressDetail", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressDetail", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-stop", "label": "批量停用", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "pre-replace:sys_common$org_employee_md_MASTER_DATA_MULTI_DISABLE_DATA_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-stop", "node": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-stop", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "label": "组织身份", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-JE2jf3T2tYith5FRLD6QN", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-form-field-employeeOrgLinkList", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform", "label": "表格表单", "type": "TableForm"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-MoO66JROlIXfgdLgx0m1p", "label": "字段组", "type": "Fields"}], "relations": [{"key": "pre-replace:sys_common$org_identity_cf_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_identity_cf"}, "type": "Service"}, {"key": "pre-replace:sys_common$org_identity_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_identity_cf"}, "type": "Service"}], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "node": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-type", "label": "员工类型", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-type", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-type", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type", "label": "员工类型", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "label": "组织单元", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-gyeTOSkmC__Zr4767Pu1A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-customDetailField", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-LAxXu7V8oaf_BP5O6foRo", "label": "字段组", "type": "Fields"}], "relations": [{"key": "pre-replace:sys_common$org_struct_md_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}, {"key": "pre-replace:sys_common$org_struct_md_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "node": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-entryAt", "label": "入职日期", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-entryAt", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-entryAt", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-email", "label": "邮箱", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-email", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-email", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-zR-xbPeH0dUQGisUgdnZD", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedAt", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedAt", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable", "label": "启用", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "pre-replace:sys_common$org_employee_md_MASTER_DATA_ENABLE_DATA_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete", "label": "删除", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "pre-replace:sys_common$org_employee_md_DELETE_DATA_BY_ID_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-idCard", "label": "身份证号", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-idCard", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-idCard", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressDetail", "label": "详细地址", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressDetail", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressDetail", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "label": "用户", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [{"key": "pre-replace:sys_common$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-email", "label": "邮箱", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-email", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-email", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressId", "label": "地址", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressId", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressId", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "label": "导出", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "<PERSON><PERSON>"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "node": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-N2CK9jLOrkwXvk-aNk5FJ", "label": "是否主组织", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-gyeTOSkmC__Zr4767Pu1A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-customDetailField", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list", "label": "表格", "type": "Table"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-LAxXu7V8oaf_BP5O6foRo", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-N2CK9jLOrkwXvk-aNk5FJ", "node": "sys_common$ORG_EMPLOYEE_VIEW-N2CK9jLOrkwXvk-aNk5FJ", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "label": "创建人", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-zR-xbPeH0dUQGisUgdnZD", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-group", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [{"key": "pre-replace:sys_common$user_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "Service"}, {"key": "pre-replace:sys_common$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$user"}, "type": "Service"}], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-resignationAt", "label": "离职日期", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "label": "详情", "type": "Detail"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "label": "详情组元素", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-resignationAt", "node": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-resignationAt", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version", "label": "版本号", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile", "label": "手机", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile", "parentKey": null, "rootField": "resources"}, {"action": "EDIT", "content": [{"description": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-originOrgId", "label": "所属组织", "path": [{"key": "sys_common$ORG_EMPLOYEE_VIEW-page", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "label": "页签项", "type": "TabItem"}, {"key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "label": "表单组元素", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "func": null, "key": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-originOrgId", "node": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-originOrgId", "parentKey": null, "rootField": "resources"}, {"action": "APPEND", "content": ["用户", "状态", "新建员工信息表", "请输入版本号", "详情", "请输入员工编码", "停用成功", "姓名", "保存", "请输入ID", "地址", "用户名", "ID", "创建人", "身份证号", "请输入更新时间", "保存成功!", "请输入姓名", "启用成功", "用户手机", "逻辑删除标识", "确认统一停用吗？", "请输入手机", "请选择", "版本号", "更新时间", "是否主组织", "批量停用", "确认统一启用吗？", "编辑", "所属组织", "选择创建人", "员工信息表", "请输入逻辑删除标识", "确认删除吗？", "批量启用", "员工编码", "复制", "选择更新人", "手机", "系统信息", "新建", "删除成功", "停用", "请输入员工类型", "按钮", "批量删除", "请输入创建时间", "详细地址", "删除", "入职日期", "离职日期", "邮箱", "启用", "批量操作", "用户邮箱", "组织单元", "选择组织单元", "选择组织身份", "主体信息", "确认启用吗？", "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},UNENABLED:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISENABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.text", "更新人", "请输入", "取消", "组织身份", "创建时间", "员工类型", "确认停用吗？"], "func": null, "key": null, "node": null, "parentKey": null, "rootField": "i18nConfig/i18nKeySet"}, {"action": "APPEND", "content": ["sys_common$ORG_EMPLOYEE_VIEW-wPS2MJGuQRIvm1fPSsLOu.props.text$", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-name.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.5.label", "sys_common$ORG_EMPLOYEE_VIEW-Mhe2IM7i9iyThkD26CsaC.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.3.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.2.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.5.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.8.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.2.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs.props.items.0.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-batchActions-delete.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-action-cancel.props.label", "sys_common$ORG_EMPLOYEE_VIEW-5JPZoDEYTJahgXeGmcG5P.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-idCard.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs.props.items.1.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressId.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressId.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.0.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressId.props.label", "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable.props.actionConfig.endLogicOtherConfig.1.message", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.1.actionConfig.endLogicOtherConfig.1.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy.props.label", "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-userId.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable.props.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-code.props.componentProps.placeholder", "@exp:sys_common$ORG_EMPLOYEE_VIEW-editView-page-title.props.title", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name.props.label", "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.0.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-status.props.label", "sys_common$ORG_EMPLOYEE_VIEW-NdUI184yh2s4lcc6w4sEg.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.7.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-action-deleteLine.props.label", "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs.props.items.2.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-resignationAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.10.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.2.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable.props.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-lF1QOZEGtkaCHK1RzD8Vw.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.1.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-mobile.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-status.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-resignationAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressId.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-entryAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.1.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-email.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.7.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressDetail.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-new.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-idCard.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-N2CK9jLOrkwXvk-aNk5FJ.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.10.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-copy.props.label", "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-entryAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-Mhe2IM7i9iyThkD26CsaC.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.1.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.2.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-originOrgId.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.2.actionConfig.endLogicOtherConfig.1.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-entryAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.4.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-name.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-mobile.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-idCard.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-idCard.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-N2CK9jLOrkwXvk-aNk5FJ.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-code.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-email.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressDetail.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-entryAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.0.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.2.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs.props.items.0.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete.props.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-NdUI184yh2s4lcc6w4sEg.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-type.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.6.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save.props.actionConfig.endLogicOtherConfig.2.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-lF1QOZEGtkaCHK1RzD8Vw.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.0.actionConfig.endLogicOtherConfig.2.message", "@exp:sys_common$ORG_EMPLOYEE_VIEW-detailView-page-title.props.title", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.0.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-kvFTbRyPRuWhc0sg4AJO7.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs.props.items.1.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable.props.actionConfig.endLogicOtherConfig.1.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-edit.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-type.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.9.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-resignationAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-email.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.0.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.8.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.6.label", "sys_common$ORG_EMPLOYEE_VIEW-5JPZoDEYTJahgXeGmcG5P.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.9.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressDetail.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.3.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.0.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.fields.0.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-email.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete.props.label", "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.1.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete.props.actionConfig.endLogicOtherConfig.1.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.2.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.1.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressDetail.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.4.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-resignationAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-originOrgId.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-userId.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.fields.0.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.1.label"], "func": null, "key": null, "node": null, "parentKey": null, "rootField": "i18nConfig/i18nScanPaths"}]}, "key": "ext_sys_common$ORG_EMPLOYEE_VIEW:list", "name": "ext_sys_common$ORG_EMPLOYEE_VIEW:list", "children": null, "parentKey": "ext_sys_common", "platformVersion": {"number": "0.0.0.0", "checksum": "0000000000000000000000000000000000000000000000000000000000000000"}}}