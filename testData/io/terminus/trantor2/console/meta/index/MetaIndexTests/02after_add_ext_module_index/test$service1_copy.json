{"key": "test$service1_copy", "type": "ServiceDefinition", "subType": "PROGRAMMABLE", "moduleKey": "test", "name": "Service Example 1 (Copy)", "path": "/dir2/", "parentKey": "test$random-key-1", "description": "", "access": "Private", "oid": "748dbc8d98d320c05a92991ffff6e5c9f2b604e0038d8e82fe8ae4dbf041bf2e", "extOid": "", "structure": {"customizedField": {"isEnabled": true, "isDeleted": null}, "links": [{"key": null, "type": null, "targetType": "Permission", "targetKey": "test$p_f_1", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "DataCondition", "targetKey": "test$p_d_1", "internalKey": "k1", "internalType": "RetrieveDataNode"}], "refs": [{"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "test$p_f_1", "targetType": "Permission", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "k1", "sourceInnerType": "RetrieveDataNode", "targetKey": "test$p_d_1", "targetType": "DataCondition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}], "innerNodes": [{"innerKey": "k1", "innerType": "RetrieveDataNode", "innerName": null, "innerParentKey": ""}], "viewContainers": []}, "liteProps": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"desc": null, "id": null, "input": [], "key": "test$service1_copy", "name": null, "output": [], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "test$p_f_1", "schedulerJob": null, "serviceFinally": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}, "modifiedAt": null, "modifiedBy": 0, "designerObject": "H4sIAAAAAAAAAKVUTW8aMRD9K5HVQyuRprTqJTcKm0hpFrYLbQ9VhMx6AAt/1faibBH/veP9MAtNpUqFk2fGb968N94DoUUBzpFbklm+px7IgBRbLpgFRW5VKcSA7KDCvAfnXzmwe17AcFloU2GpohIwN2/CV8kzlUbA1fDq9RgL3mCFoYjkP58gLFVMy2sEvR7Web/F1A3j9v1NOAvq19rKb2Ad18jhgHyg2LlSYtm7//wFyqVcgQ1Yb+s/OWJTq40LrWCPZLPm1AzP3QQEeGCnQKLoSoSAtyUMiNQMRD1gU2DASu4C+QjSyjZx4sF1M3Ua/zgQBq6I8Oxc9t0wytzCd1wLrRj32Obe6tJ0aUY9HXeZLFLpGWCWbBlAQ+WiMsG/dDZJHkMIHCiHV3+dxmMVdudFxOwaSfrMZTClOerSm9KPsNkmlhi6gSBVd/5Zgq3SINcdB8GiPBbQdGB1JkqmrZ9ZhlsQI16b71tQE+SdSONxoDUVDim6cvUlQOc9nHjNNyPm4C1He8PtYDBYz8EF818omCICOT4N/uIMVzgqOvf0D2+jr05zJRpI+XhLfWDblVGe61IxN/cWB9lUp3j7ws4Y1CLmpQB33ubPuHl5D9b1Hjh8Xwyr7YNeXSzsHVdUiOrkALJKKe6uikyQqXK0qLcNB6Mb2iwJmc4Wy/nXLJvli2RCosztIH0Ljv0HkrKPFySmPR3bULu2WT67z0dpOvr0mAQnwySSxi/Hh8umE1hz1Szx8TeihV9U+gQAAA==", "runtimeObject": "H4sIAAAAAAAAAKVUTW8aMRD9K5HVQyuRprTqJTcKm0hpFrYLbQ9VhMx6AAt/1faibBH/veP9MAtNpUqFk2fGb968N94DoUUBzpFbklm+px7IgBRbLpgFRW5VKcSA7KDCvAfnXzmwe17AcFloU2GpohIwN2/CV8kzlUbA1fDq9RgL3mCFoYjkP58gLFVMy2sEvR7Web/F1A3j9v1NOAvq19rKb2Ad18jhgHyg2LlSYtm7//wFyqVcgQ1Yb+s/OWJTq40LrWCPZLPm1AzP3QQEeGCnQKLoSoSAtyUMiNQMRD1gU2DASu4C+QjSyjZx4sF1M3Ua/zgQBq6I8Oxc9t0wytzCd1wLrRj32Obe6tJ0aUY9HXeZLFLpGWCWbBlAQ+WiMsG/dDZJHkMIHCiHV3+dxmMVdudFxOwaSfrMZTClOerSm9KPsNkmlhi6gSBVd/5Zgq3SINcdB8GiPBbQdGB1JkqmrZ9ZhlsQI16b71tQE+SdSONxoDUVDim6cvUlQOc9nHjNNyPm4C1He8PtYDBYz8EF818omCICOT4N/uIMVzgqOvf0D2+jr05zJRpI+XhLfWDblVGe61IxN/cWB9lUp3j7ws4Y1CLmpQB33ubPuHl5D9b1Hjh8Xwyr7YNeXSzsHVdUiOrkALJKKe6uikyQqXK0qLcNB6Mb2iwJmc4Wy/nXLJvli2RCosztIH0Ljv0HkrKPFySmPR3bULu2WT67z0dpOvr0mAQnwySSxi/Hh8umE1hz1Szx8TeihV9U+gQAAA==", "id": null, "teamId": null, "teamCode": null}