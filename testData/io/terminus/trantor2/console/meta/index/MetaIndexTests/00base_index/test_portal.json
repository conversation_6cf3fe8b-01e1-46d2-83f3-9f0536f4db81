{"key": "test_portal", "type": "<PERSON><PERSON><PERSON>", "subType": "Portal", "moduleKey": "test_portal", "name": "TEST门户", "path": "", "parentKey": "__root__", "description": "", "access": "Private", "oid": "d5a0ed90718c293bc23bd49218dd7316ac03cb5e550d322cf6760c3d2c1accc6", "extOid": "", "structure": {"customizedField": null, "links": [], "refs": [], "innerNodes": [], "viewContainers": []}, "liteProps": {"config": null, "endpointType": "PC", "globalConfig": null, "logoUrl": null, "moduleConfig": null, "nativeModule": true, "relatedModuleKeys": null, "sourceApp": null, "type": "Portal", "version": "1.0.0"}, "modifiedAt": null, "modifiedBy": 0, "designerObject": "H4sIAAAAAAAAAKVQMWoDMRD8y9ZHSEh3XTCpQsAQO+0h69ZnYZ1WrFYHxrjPD/KF/CA/CuQX2ZMtg+tIjTSandHMEYy1mBK0sGQ3GUFowO6c7xkDtCF730CPybKL4kghUMIeD3oQTNJFYjFesWBGVHD1/Lb6/fz6+fhWLBpVkZfC7jomkq4rsOzOQtEb2RKP78ipqB/VHO0+5VEJ9/9c86/yuEGete7KhpOaMsVUrChs3VBTYugjuSCrQ5yDLBc6PnjaGL+44XkaaM2+Xkfqs8dbSjDiJnwtL9AKZ2yAUbNifwa1klTJiTJbfIqxAnL5QG12quXAQ42QtKXRXFt7vA5dPE9/p+D1Y9gBAAA=", "runtimeObject": "H4sIAAAAAAAAAKVQMWoDMRD8y9ZHSEh3XTCpQsAQO+0h69ZnYZ1WrFYHxrjPD/KF/CA/CuQX2ZMtg+tIjTSandHMEYy1mBK0sGQ3GUFowO6c7xkDtCF730CPybKL4kghUMIeD3oQTNJFYjFesWBGVHD1/Lb6/fz6+fhWLBpVkZfC7jomkq4rsOzOQtEb2RKP78ipqB/VHO0+5VEJ9/9c86/yuEGete7KhpOaMsVUrChs3VBTYugjuSCrQ5yDLBc6PnjaGL+44XkaaM2+Xkfqs8dbSjDiJnwtL9AKZ2yAUbNifwa1klTJiTJbfIqxAnL5QG12quXAQ42QtKXRXFt7vA5dPE9/p+D1Y9gBAAA=", "id": null, "teamId": null, "teamCode": null}