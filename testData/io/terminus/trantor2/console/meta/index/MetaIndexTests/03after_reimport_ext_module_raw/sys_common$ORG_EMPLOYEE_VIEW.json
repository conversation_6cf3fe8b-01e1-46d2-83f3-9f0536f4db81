{"key": "sys_common$ORG_EMPLOYEE_VIEW", "moduleKey": "sys_common", "type": "Scene", "parentKey": "sys_common$ungroup", "path": null, "oid": "79b999039590a8f915990eaebf576dea0f8d1793a9f358b385edc24c7aff4af9", "object": "H4sIAAAAAAAAAK1UO0/jQBD+LytKgzhdl84K1hGRlxILdELRarEnYZV9WLtrJAu5pYbu2iuuvfZ+EeJvMEM4HgkxWGC7Wc98830zszOXLDuXKndgWMeUSkVsCRXrMF95nlmtrdkZTX7wZDDuj34mCT/uJScsYkZoQK/bm1+3//7c/f19d32FfwuBccLReoDSLJwtC3JQIsyt08fgvLRIeYn0kC19qRGy/8mHdJX6DBzF2nt4WY2kzhaeqMDkhZUmpFVB4sddBPgMDHStmcsFuWibg1o7buRj3YKDLpStALjOMYoDTAwTeqDp94ZHrHO6DV16FPg2ZFbXs4iN40kyTHn3sNc/aIjzSgUdlDRLns1byiGkD67MQvtUCHtWemnAex6wqpv0dR21KESDd5POd2C51GDovr0lb9ZO4YtWNXJ+6f1oVkl0MsfBk6Fq3/9tdKsvYoEyEeHFjCibCcU62At4Nq/iauEDOJ6LINiz7XHeBvE0TSb8IE5jNF78XwHsGw3q7nQYj6eHo5QmNqwAj56oMB+Q9lhJgfpOm5NvbMTH5mezPFsv4mxNbk0r5Ry0eFpx358cprRrWH0PjyHEbnMFAAA=", "createdBy": 0, "updatedBy": 0, "createdAt": null, "updatedAt": null, "node": {"schemaVersion": 3, "type": "Scene", "props": {"endpointType": "PC", "sceneConfig": {"modelConfig": {"modelKey": "sys_common$org_employee_md", "relations": {"LINK": [{"modelKey": "sys_common$user", "relations": {"LINK": []}}], "PARENT_CHILD": [{"modelKey": "sys_common$org_employee_org_link_cf", "relations": {"LINK": [{"modelKey": "sys_common$org_struct_md", "relations": {"LINK": [{"modelKey": "sys_common$org_business_type_cf", "relations": {}}, {"modelKey": "sys_common$user", "relations": {}}, {"modelKey": "sys_common$org_struct_md", "relations": {}}, {"modelKey": "sys_common$org_dimension_cf", "relations": {}}]}}, {"modelKey": "sys_common$user", "relations": {"LINK": []}}, {"modelKey": "sys_common$org_employee_md", "relations": {"LINK": [{"modelKey": "sys_common$user", "relations": {}}]}}, {"modelKey": "sys_common$org_identity_cf", "relations": {"LINK": [{"modelKey": "sys_common$user", "relations": {}}]}}]}}]}}, "templateConfig": {"local": true, "templateKey": "master_data", "templateType": "MASTER_DATA", "version": "1.0.0-SNAPSHOT"}, "type": "DATA", "usedModelAlias": ["sys_common$org_identity_cf", "sys_common$org_employee_md", "sys_common$org_employee_org_link_cf", "sys_common$user", "sys_common$org_struct_md"]}, "type": "DATA"}, "key": "sys_common$ORG_EMPLOYEE_VIEW", "name": "员工管理", "children": null, "parentKey": "sys_common$ungroup", "platformVersion": {"number": "0.0.0.0", "checksum": "0000000000000000000000000000000000000000000000000000000000000000"}}}