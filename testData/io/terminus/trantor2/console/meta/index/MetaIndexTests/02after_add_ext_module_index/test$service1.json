{"key": "test$service1", "type": "ServiceDefinition", "subType": "PROGRAMMABLE", "moduleKey": "test", "name": "Service Example 1", "path": "/dir1/", "parentKey": "test$random-key-2", "description": "", "access": "Private", "oid": "a07e5a5b360789e6310eadc4977281c194e83223333a1cd9055b46bd9ae172db", "extOid": "", "structure": {"customizedField": {"isEnabled": true, "isDeleted": null}, "links": [{"key": null, "type": null, "targetType": "Permission", "targetKey": "test$p_f_1", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "DataCondition", "targetKey": "test$p_d_1", "internalKey": "k1", "internalType": "RetrieveDataNode"}], "refs": [{"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "test$p_f_1", "targetType": "Permission", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "k1", "sourceInnerType": "RetrieveDataNode", "targetKey": "test$p_d_1", "targetType": "DataCondition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}], "innerNodes": [{"innerKey": "k1", "innerType": "RetrieveDataNode", "innerName": null, "innerParentKey": ""}], "viewContainers": []}, "liteProps": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"desc": null, "id": null, "input": [], "key": "test$service1", "name": null, "output": [], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "test$p_f_1", "schedulerJob": null, "serviceFinally": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}, "modifiedAt": null, "modifiedBy": 0, "designerObject": "H4sIAAAAAAAAAKVUS3PTMBD+Kx0Nx5RSGC69hdrtTKkT4xQ4MJ2MYm0STfRCkjM1mfx3Vn7ITihcSE7aXe1+j5UPhJYlOEduSG75nnogE1JuuWAWFLlRlRATsoMa8x6cf+PA7nkJ11ilqAQML9rIRfpCpRFwEVKG4m3/ebhmqWJaXmKjy/dN3m8xdcW4vb4KZ0H9Wlv5DazjGuceEAOUO1dJLHv3n7+AtZIrsKHX2+ZPjjjUauPCKNgj2Lw9tYS5S0CABzYEUkVXIgS8rWBCpGYgGoJtgQEruQvgY5NOqsSJB9dz6nX9cSAMXBnbs1Opd4O+Xfsea6kV4x7H3FtdmT7NqKe3fSaPUEYGmCVbhqah8qk2wbhsnqSPIQQOlMOrvwZ6rMbpvIw9+0GSvnAZTGmPuvKm8lMctoklhm4gSNWff1Zg6yzIdcdBsCiPBTQdWJOJkmnr55bhFsSI1+b7FlSCuFNpPBJaU+EQoqtWX0LrYtQnXvMtxQK85WhvuB0MBus5uGD+KwUz7ECOz5O/OMMVUkXnnv/9HsbCtNXRO8pvt9QHoH0Z5YWuFHMLb5HDph7i3as6Gd7oV1QC3OmYP+Pm9RVYNyvg8GkxrLYPenW2q3dcUSHqQXxElVFcWxWRIFLlaNksGhKjG9ruB5nNn5aLr3k+L57ShESFOyJj9Y/jt5Gxj2cgZiMdu1C3sXkxvy+mWTb99JgGEwMTSeNH48P50ATWXLX7e/wNMuEEZukEAAA=", "runtimeObject": "H4sIAAAAAAAAAKVUS3PTMBD+Kx0Nx5RSGC69hdrtTKkT4xQ4MJ2MYm0STfRCkjM1mfx3Vn7ITihcSE7aXe1+j5UPhJYlOEduSG75nnogE1JuuWAWFLlRlRATsoMa8x6cf+PA7nkJ11ilqAQML9rIRfpCpRFwEVKG4m3/ebhmqWJaXmKjy/dN3m8xdcW4vb4KZ0H9Wlv5DazjGuceEAOUO1dJLHv3n7+AtZIrsKHX2+ZPjjjUauPCKNgj2Lw9tYS5S0CABzYEUkVXIgS8rWBCpGYgGoJtgQEruQvgY5NOqsSJB9dz6nX9cSAMXBnbs1Opd4O+Xfsea6kV4x7H3FtdmT7NqKe3fSaPUEYGmCVbhqah8qk2wbhsnqSPIQQOlMOrvwZ6rMbpvIw9+0GSvnAZTGmPuvKm8lMctoklhm4gSNWff1Zg6yzIdcdBsCiPBTQdWJOJkmnr55bhFsSI1+b7FlSCuFNpPBJaU+EQoqtWX0LrYtQnXvMtxQK85WhvuB0MBus5uGD+KwUz7ECOz5O/OMMVUkXnnv/9HsbCtNXRO8pvt9QHoH0Z5YWuFHMLb5HDph7i3as6Gd7oV1QC3OmYP+Pm9RVYNyvg8GkxrLYPenW2q3dcUSHqQXxElVFcWxWRIFLlaNksGhKjG9ruB5nNn5aLr3k+L57ShESFOyJj9Y/jt5Gxj2cgZiMdu1C3sXkxvy+mWTb99JgGEwMTSeNH48P50ATWXLX7e/wNMuEEZukEAAA=", "id": null, "teamId": null, "teamCode": null}