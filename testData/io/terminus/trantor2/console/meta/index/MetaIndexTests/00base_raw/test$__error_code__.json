{"key": "test$__error_code__", "moduleKey": "test", "type": "ErrorCodeRoot", "parentKey": "test", "path": null, "oid": "aa70bcf2f9ff471ac14dbd6e3179f417d7875a0cfcc06cb5224cb3c06d061424", "object": "H4sIAAAAAAAAAKWPsQrDMAxE/0V0NKHQLWvJ1K1DV+M6Kg2xLSPLQwj598iFZulYaTvp9E4r+PcURsYEfaohGJhxgR4Ei5ysRWZi62lEa8FAchF1ODT1quKdSFTOTv1yO4xNCk5exPGBXCbS46uC0M+lRl06/1ktSo1P5Har+zRsCmXK5ftHUV50B/9iQJb8m37bAV9+fWkCAQAA", "createdBy": 0, "updatedBy": 0, "createdAt": null, "updatedAt": null, "node": {"schemaVersion": 3, "type": "ErrorCodeRoot", "props": null, "key": "test$__error_code__", "name": "ErrorCodeRoot", "children": null, "parentKey": "test", "platformVersion": {"number": "0.0.0.0", "checksum": "0000000000000000000000000000000000000000000000000000000000000000"}}}