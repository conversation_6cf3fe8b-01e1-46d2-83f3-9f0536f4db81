{"key": "sys_common$ORG_EMPLOYEE_VIEW:list", "type": "View", "subType": "", "moduleKey": "sys_common", "name": "list", "path": "", "parentKey": "sys_common$ORG_EMPLOYEE_VIEW", "description": "", "access": "Private", "oid": "767c80b8ff9b6b330021872a2144511162927fd334b41bd1c71cc0c36f36948c", "extOid": "ca557a722604f1bd4635e78a028311b67095132f6d618b0b5cf1c42bf7886388", "structure": {"customizedField": null, "links": [{"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-resignationAt", "internalType": "DetailField/Container/离职日期"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedAt", "internalType": "DetailField/Container/更新时间"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_employee_md_PAGING_DATA_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-logs", "internalType": "Logs/Container/日志"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "internalType": "Table/Container/表格"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "internalType": "ImportButton/Button/导入"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "internalType": "Detail/Container/详情"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$user:sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "internalType": "DetailField/Container/更新人"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "internalType": "FormField/Container/创建人"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_SaveDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "internalType": "Detail/Container/详情"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressId", "internalType": "DetailField/Container/地址"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_org_link_cf", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-5JPZoDEYTJahgXeGmcG5P", "internalType": "Field/Container/是否主组织"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_identity_cf:sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "internalType": "Table/Container/表格"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressDetail", "internalType": "FormField/Container/详细地址"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-userId", "internalType": "DetailField/Container/用户"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_struct_md_PAGING_DATA_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code", "internalType": "FormField/Container/员工编码"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "internalType": "FormField/Container/更新人"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_MasterData_SaveDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "internalType": "Button/Button/保存"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "internalType": "DetailField/Container/更新人"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "internalType": "Button/Button/保存"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_BatchDeleteDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-delete", "internalType": "DropdownButton/Button/label"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-edit", "internalType": "Button/Button/编辑"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id", "internalType": "FormField/Container/ID"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detail-sys_common$org_employee_md-logs", "internalType": "Logs/Container/日志"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version", "internalType": "FormField/Container/版本号"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_struct_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_MasterData_EnableDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable", "internalType": "Button/Button/启用"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$user:sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "internalType": "FormField/Container/创建人"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_org_link_cf", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_DisableDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable", "internalType": "Button/Button/停用"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$user:sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "internalType": "FormField/Container/用户"}, {"key": null, "type": null, "targetType": "Permission", "targetKey": "sys_common$ORG_EMPLOYEE_VIEW-kvFTbRyPRuWhc0sg4AJO7_perm_ac", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-kvFTbRyPRuWhc0sg4AJO7", "internalType": "Button/Button/按钮"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "internalType": "ImportButton/Button/导入"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-email", "internalType": "FormField/Container/邮箱"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_DeleteDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete", "internalType": "Button/Button/删除"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "internalType": "ExportButton/Button/导出"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_org_link_cf", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "Fake<PERSON><PERSON>", "targetKey": "GET:/api/gei/template/download", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "internalType": "ImportButton/Button/导入"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-status", "internalType": "DetailField/Container/状态"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$user:sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "internalType": "FormField/Container/更新人"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$user", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "internalType": "DetailField/Container/创建人"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "internalType": "FormField/Container/创建人"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "internalType": "FormField/Container/用户"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-start", "internalType": "DropdownButton/Button/label"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted", "internalType": "FormField/Container/逻辑删除标识"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_identity_cf_FIND_DATA_BY_ID_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_identity_cf", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable", "internalType": "Button/Button/停用"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_identity_cf_PAGING_DATA_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_org_link_cf", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-mobile", "internalType": "DetailField/Container/手机"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$user:sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "internalType": "DetailField/Container/创建人"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_struct_md:sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "View", "targetKey": "show", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "internalType": "Table/Container/表格"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "internalType": "FormField/Container/用户"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_org_link_cf", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform", "internalType": "TableForm/Container/表格表单"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-delete", "internalType": "DropdownButton/Button/label"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$user:sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "internalType": "FormField/Container/创建人"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_struct_md_FIND_DATA_BY_ID_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "Fake<PERSON><PERSON>", "targetKey": "GET:/api/gei/task/import-sub-model", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "internalType": "ImportButton/Button/导入"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_struct_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-email", "internalType": "DetailField/Container/邮箱"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt", "internalType": "FormField/Container/更新时间"}, {"key": null, "type": null, "targetType": "View", "targetKey": "show", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "internalType": "Button/Button/保存"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-Mhe2IM7i9iyThkD26CsaC", "internalType": "FormField/Container/状态"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressDetail", "internalType": "DetailField/Container/详细地址"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-entryAt", "internalType": "FormField/Container/入职日期"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "internalType": "DetailField/Container/创建人"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_employee_md_BATCH_DELETE_DATA_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$user_FIND_DATA_BY_ID_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name", "internalType": "FormField/Container/姓名"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_MasterData_MultiEnableDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-start", "internalType": "DropdownButton/Button/label"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_org_link_cf", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "Fake<PERSON><PERSON>", "targetKey": "GET:/api/gei/task/import-direct-by-oss", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "internalType": "ImportButton/Button/导入"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TEMPLATE_PAGING_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "internalType": "ExportButton/Button/导出"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete", "internalType": "Button/Button/删除"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_CopyDataConverterService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "internalType": "FormGroup/Container/表单组"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$user:sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "internalType": "FormField/Container/更新人"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_employee_md_DELETE_DATA_BY_ID_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "internalType": "FormGroup/Container/表单组"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile", "internalType": "FormField/Container/手机"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-stop", "internalType": "DropdownButton/Button/label"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdAt", "internalType": "DetailField/Container/创建时间"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "internalType": "Table/Container/表格"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable", "internalType": "Button/Button/启用"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-idCard", "internalType": "DetailField/Container/身份证号"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_org_link_cf", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-N2CK9jLOrkwXvk-aNk5FJ", "internalType": "Field/Container/是否主组织"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "internalType": "DetailField/Container/更新人"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_employee_md_MASTER_DATA_MULTI_DISABLE_DATA_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "internalType": "ExportButton/Button/导出"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-NdUI184yh2s4lcc6w4sEg", "internalType": "Field/Container/姓名"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_employee_md_FIND_DATA_BY_ID_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_MultiDisableDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-stop", "internalType": "DropdownButton/Button/label"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$user", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "internalType": "DetailField/Container/更新人"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$user", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "internalType": "FormField/Container/用户"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_struct_md:sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "internalType": "FormField/Container/更新人"}, {"key": null, "type": null, "targetType": "Fake<PERSON><PERSON>", "targetKey": "GET:/api/gei/template/download/v2", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "internalType": "ImportButton/Button/导入"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_employee_md_MASTER_DATA_DISABLE_DATA_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$user_PAGING_DATA_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-lF1QOZEGtkaCHK1RzD8Vw", "internalType": "Field/Container/员工编码"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-name", "internalType": "DetailField/Container/姓名"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$user", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "View", "targetKey": "edit", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-copy", "internalType": "Button/Button/复制"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_BatchDeleteDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-delete", "internalType": "DropdownButton/Button/label"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "internalType": "FormGroup/Container/表单组"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "internalType": "FormField/Container/更新人"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-type", "internalType": "DetailField/Container/员工类型"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "internalType": "DetailField/Container/创建人"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$user", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "internalType": "FormField/Container/创建人"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_employee_md_MASTER_DATA_MULTI_ENABLE_DATA_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-entryAt", "internalType": "DetailField/Container/入职日期"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_identity_cf:sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_MasterData_DisableDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable", "internalType": "Button/Button/停用"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_identity_cf:sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_employee_md_MASTER_DATA_ENABLE_DATA_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "internalType": "FormField/Container/创建人"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "internalType": "Detail/Container/详情"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-idCard", "internalType": "FormField/Container/身份证号"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_SaveDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "internalType": "Button/Button/保存"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type", "internalType": "FormField/Container/员工类型"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_identity_cf", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$user:sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "internalType": "DetailField/Container/创建人"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_identity_cf", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressId", "internalType": "FormField/Container/地址"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_MasterData_SaveDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "internalType": "Detail/Container/详情"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "internalType": "Detail/Container/详情"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_EnableDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable", "internalType": "Button/Button/启用"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TEMPLATE_PAGING_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "internalType": "ImportButton/Button/导入"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "internalType": "DetailField/Container/更新人"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "internalType": "FormGroup/Container/表单组"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "internalType": "ImportButton/Button/导入"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_employee_md_COPY_DATA_CONVERTER_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt", "internalType": "FormField/Container/创建时间"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_org_link_cf", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list", "internalType": "Table/Container/表格"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "pre-replace:sys_common$org_employee_md_MASTER_DATA_SAVE_DATA_SERVICE", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "internalType": "DetailField/Container/创建人"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_identity_cf:sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "internalType": "Field/Container/组织身份"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_CopyDataConverterService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "internalType": "FormGroup/Container/表单组"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_DeleteDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete", "internalType": "Button/Button/删除"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-code", "internalType": "DetailField/Container/员工编码"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$user:sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "internalType": "DetailField/Container/更新人"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "internalType": "ImportButton/Button/导入"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "internalType": "ExportButton/Button/导出"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-originOrgId", "internalType": "FormField/Container/所属组织"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_PROGRESS_POST", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "internalType": "ImportButton/Button/导入"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$user", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "internalType": "FormField/Container/更新人"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_struct_md:sys_common$SYS_PagingDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_employee_md", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-resignationAt", "internalType": "FormField/Container/离职日期"}, {"key": null, "type": null, "targetType": "Model", "targetKey": "sys_common$org_struct_md", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_struct_md:sys_common$SYS_FindDataByIdService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "internalType": "Field/Container/组织单元"}, {"key": null, "type": null, "targetType": "FakeSysService", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_MultiEnableDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-start", "internalType": "DropdownButton/Button/label"}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "internalKey": null, "internalType": null}, {"key": null, "type": null, "targetType": "ServiceDefinition", "targetKey": "sys_common$SYS_MasterData_MultiDisableDataService", "internalKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-stop", "internalType": "DropdownButton/Button/label"}], "refs": [{"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-resignationAt", "sourceInnerType": "DetailField/Container/离职日期", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedAt", "sourceInnerType": "DetailField/Container/更新时间", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_employee_md_PAGING_DATA_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-logs", "sourceInnerType": "Logs/Container/日志", "targetKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "sourceInnerType": "Table/Container/表格", "targetKey": "sys_common$SYS_PagingDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "sourceInnerType": "ImportButton/Button/导入", "targetKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "sourceInnerType": "Detail/Container/详情", "targetKey": "sys_common$SYS_FindDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "sourceInnerType": "DetailField/Container/更新人", "targetKey": "sys_common$user:sys_common$SYS_PagingDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "sourceInnerType": "FormField/Container/创建人", "targetKey": "sys_common$SYS_FindDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "sourceInnerType": "Detail/Container/详情", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_SaveDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressId", "sourceInnerType": "DetailField/Container/地址", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-5JPZoDEYTJahgXeGmcG5P", "sourceInnerType": "Field/Container/是否主组织", "targetKey": "sys_common$org_employee_org_link_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$org_identity_cf:sys_common$SYS_FindDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "sourceInnerType": "Table/Container/表格", "targetKey": "sys_common$org_employee_md:sys_common$SYS_PagingDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressDetail", "sourceInnerType": "FormField/Container/详细地址", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-userId", "sourceInnerType": "DetailField/Container/用户", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_struct_md_PAGING_DATA_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code", "sourceInnerType": "FormField/Container/员工编码", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "sourceInnerType": "FormField/Container/更新人", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "sourceInnerType": "Button/Button/保存", "targetKey": "sys_common$SYS_MasterData_SaveDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "sourceInnerType": "DetailField/Container/更新人", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$SYS_PagingDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "sourceInnerType": "Button/Button/保存", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-delete", "sourceInnerType": "DropdownButton/Button/label", "targetKey": "sys_common$SYS_BatchDeleteDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-edit", "sourceInnerType": "Button/Button/编辑", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id", "sourceInnerType": "FormField/Container/ID", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detail-sys_common$org_employee_md-logs", "sourceInnerType": "Logs/Container/日志", "targetKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version", "sourceInnerType": "FormField/Container/版本号", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$org_struct_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable", "sourceInnerType": "Button/Button/启用", "targetKey": "sys_common$SYS_MasterData_EnableDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "sourceInnerType": "FormField/Container/创建人", "targetKey": "sys_common$user:sys_common$SYS_FindDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$org_employee_org_link_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable", "sourceInnerType": "Button/Button/停用", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_DisableDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "sourceInnerType": "FormField/Container/用户", "targetKey": "sys_common$user:sys_common$SYS_PagingDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-kvFTbRyPRuWhc0sg4AJO7", "sourceInnerType": "Button/Button/按钮", "targetKey": "sys_common$ORG_EMPLOYEE_VIEW-kvFTbRyPRuWhc0sg4AJO7_perm_ac", "targetType": "Permission", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$SYS_PagingDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "sourceInnerType": "ImportButton/Button/导入", "targetKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-email", "sourceInnerType": "FormField/Container/邮箱", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete", "sourceInnerType": "Button/Button/删除", "targetKey": "sys_common$SYS_DeleteDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "sourceInnerType": "ExportButton/Button/导出", "targetKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$org_employee_org_link_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "sourceInnerType": "ImportButton/Button/导入", "targetKey": "GET:/api/gei/template/download", "targetType": "Fake<PERSON><PERSON>", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-status", "sourceInnerType": "DetailField/Container/状态", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "sourceInnerType": "FormField/Container/更新人", "targetKey": "sys_common$user:sys_common$SYS_PagingDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "sourceInnerType": "DetailField/Container/创建人", "targetKey": "sys_common$user", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "sourceInnerType": "FormField/Container/创建人", "targetKey": "sys_common$SYS_PagingDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "sourceInnerType": "FormField/Container/用户", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-start", "sourceInnerType": "DropdownButton/Button/label", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted", "sourceInnerType": "FormField/Container/逻辑删除标识", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$SYS_FindDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_identity_cf_FIND_DATA_BY_ID_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "sys_common$org_identity_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable", "sourceInnerType": "Button/Button/停用", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_identity_cf_PAGING_DATA_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$org_employee_org_link_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-mobile", "sourceInnerType": "DetailField/Container/手机", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "sourceInnerType": "DetailField/Container/创建人", "targetKey": "sys_common$user:sys_common$SYS_FindDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$org_struct_md:sys_common$SYS_PagingDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "sourceInnerType": "Table/Container/表格", "targetKey": "show", "targetType": "View", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "sourceInnerType": "FormField/Container/用户", "targetKey": "sys_common$SYS_PagingDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform", "sourceInnerType": "TableForm/Container/表格表单", "targetKey": "sys_common$org_employee_org_link_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-delete", "sourceInnerType": "DropdownButton/Button/label", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "sourceInnerType": "FormField/Container/创建人", "targetKey": "sys_common$user:sys_common$SYS_PagingDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_struct_md_FIND_DATA_BY_ID_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "sourceInnerType": "ImportButton/Button/导入", "targetKey": "GET:/api/gei/task/import-sub-model", "targetType": "Fake<PERSON><PERSON>", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$org_struct_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-email", "sourceInnerType": "DetailField/Container/邮箱", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt", "sourceInnerType": "FormField/Container/更新时间", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "sourceInnerType": "Button/Button/保存", "targetKey": "show", "targetType": "View", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-Mhe2IM7i9iyThkD26CsaC", "sourceInnerType": "FormField/Container/状态", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressDetail", "sourceInnerType": "DetailField/Container/详细地址", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-entryAt", "sourceInnerType": "FormField/Container/入职日期", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "sourceInnerType": "DetailField/Container/创建人", "targetKey": "sys_common$SYS_FindDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_employee_md_BATCH_DELETE_DATA_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$user_FIND_DATA_BY_ID_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name", "sourceInnerType": "FormField/Container/姓名", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-start", "sourceInnerType": "DropdownButton/Button/label", "targetKey": "sys_common$SYS_MasterData_MultiEnableDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$org_employee_org_link_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "sourceInnerType": "ImportButton/Button/导入", "targetKey": "GET:/api/gei/task/import-direct-by-oss", "targetType": "Fake<PERSON><PERSON>", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "sourceInnerType": "ExportButton/Button/导出", "targetKey": "sys_common$API_GEI_TEMPLATE_PAGING_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete", "sourceInnerType": "Button/Button/删除", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "sourceInnerType": "FormGroup/Container/表单组", "targetKey": "sys_common$SYS_CopyDataConverterService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "sourceInnerType": "FormField/Container/更新人", "targetKey": "sys_common$user:sys_common$SYS_FindDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_employee_md_DELETE_DATA_BY_ID_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "sourceInnerType": "FormGroup/Container/表单组", "targetKey": "sys_common$SYS_FindDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile", "sourceInnerType": "FormField/Container/手机", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-stop", "sourceInnerType": "DropdownButton/Button/label", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdAt", "sourceInnerType": "DetailField/Container/创建时间", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "sourceInnerType": "Table/Container/表格", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable", "sourceInnerType": "Button/Button/启用", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-idCard", "sourceInnerType": "DetailField/Container/身份证号", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-N2CK9jLOrkwXvk-aNk5FJ", "sourceInnerType": "Field/Container/是否主组织", "targetKey": "sys_common$org_employee_org_link_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "sourceInnerType": "DetailField/Container/更新人", "targetKey": "sys_common$SYS_PagingDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_employee_md_MASTER_DATA_MULTI_DISABLE_DATA_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "sourceInnerType": "ExportButton/Button/导出", "targetKey": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$SYS_FindDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-NdUI184yh2s4lcc6w4sEg", "sourceInnerType": "Field/Container/姓名", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_employee_md_FIND_DATA_BY_ID_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-stop", "sourceInnerType": "DropdownButton/Button/label", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_MultiDisableDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "sourceInnerType": "DetailField/Container/更新人", "targetKey": "sys_common$user", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "sourceInnerType": "FormField/Container/用户", "targetKey": "sys_common$user", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$org_struct_md:sys_common$SYS_FindDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "sourceInnerType": "FormField/Container/更新人", "targetKey": "sys_common$SYS_PagingDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "sourceInnerType": "ImportButton/Button/导入", "targetKey": "GET:/api/gei/template/download/v2", "targetType": "Fake<PERSON><PERSON>", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_employee_md_MASTER_DATA_DISABLE_DATA_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$SYS_PagingDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$user_PAGING_DATA_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-lF1QOZEGtkaCHK1RzD8Vw", "sourceInnerType": "Field/Container/员工编码", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-name", "sourceInnerType": "DetailField/Container/姓名", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "sys_common$user", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-copy", "sourceInnerType": "Button/Button/复制", "targetKey": "edit", "targetType": "View", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-delete", "sourceInnerType": "DropdownButton/Button/label", "targetKey": "sys_common$org_employee_md:sys_common$SYS_BatchDeleteDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "sourceInnerType": "FormGroup/Container/表单组", "targetKey": "sys_common$org_employee_md:sys_common$SYS_FindDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$SYS_FindDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "sourceInnerType": "FormField/Container/更新人", "targetKey": "sys_common$SYS_FindDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-type", "sourceInnerType": "DetailField/Container/员工类型", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "sourceInnerType": "DetailField/Container/创建人", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "sourceInnerType": "FormField/Container/创建人", "targetKey": "sys_common$user", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_employee_md_MASTER_DATA_MULTI_ENABLE_DATA_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-entryAt", "sourceInnerType": "DetailField/Container/入职日期", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$org_identity_cf:sys_common$SYS_FindDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable", "sourceInnerType": "Button/Button/停用", "targetKey": "sys_common$SYS_MasterData_DisableDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$org_identity_cf:sys_common$SYS_PagingDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_employee_md_MASTER_DATA_ENABLE_DATA_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "sourceInnerType": "FormField/Container/创建人", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "sourceInnerType": "Detail/Container/详情", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-idCard", "sourceInnerType": "FormField/Container/身份证号", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "sourceInnerType": "Button/Button/保存", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_SaveDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$SYS_PagingDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type", "sourceInnerType": "FormField/Container/员工类型", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$org_identity_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "sourceInnerType": "DetailField/Container/创建人", "targetKey": "sys_common$user:sys_common$SYS_PagingDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$org_identity_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$SYS_FindDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressId", "sourceInnerType": "FormField/Container/地址", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "sourceInnerType": "Detail/Container/详情", "targetKey": "sys_common$SYS_MasterData_SaveDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "sourceInnerType": "Detail/Container/详情", "targetKey": "sys_common$org_employee_md:sys_common$SYS_FindDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable", "sourceInnerType": "Button/Button/启用", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_EnableDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "sourceInnerType": "ImportButton/Button/导入", "targetKey": "sys_common$API_GEI_TEMPLATE_PAGING_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "sourceInnerType": "DetailField/Container/更新人", "targetKey": "sys_common$SYS_FindDataByIdService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "sourceInnerType": "FormGroup/Container/表单组", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "sourceInnerType": "ImportButton/Button/导入", "targetKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_employee_md_COPY_DATA_CONVERTER_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt", "sourceInnerType": "FormField/Container/创建时间", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list", "sourceInnerType": "Table/Container/表格", "targetKey": "sys_common$org_employee_org_link_cf", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "pre-replace:sys_common$org_employee_md_MASTER_DATA_SAVE_DATA_SERVICE", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "sourceInnerType": "DetailField/Container/创建人", "targetKey": "sys_common$SYS_PagingDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "sourceInnerType": "Field/Container/组织身份", "targetKey": "sys_common$org_identity_cf:sys_common$SYS_PagingDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "sourceInnerType": "FormGroup/Container/表单组", "targetKey": "sys_common$org_employee_md:sys_common$SYS_CopyDataConverterService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete", "sourceInnerType": "Button/Button/删除", "targetKey": "sys_common$org_employee_md:sys_common$SYS_DeleteDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-code", "sourceInnerType": "DetailField/Container/员工编码", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "sourceInnerType": "DetailField/Container/更新人", "targetKey": "sys_common$user:sys_common$SYS_FindDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "sourceInnerType": "ImportButton/Button/导入", "targetKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "sourceInnerType": "ExportButton/Button/导出", "targetKey": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-originOrgId", "sourceInnerType": "FormField/Container/所属组织", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "sourceInnerType": "ImportButton/Button/导入", "targetKey": "sys_common$API_GEI_TASK_PROGRESS_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "sourceInnerType": "FormField/Container/更新人", "targetKey": "sys_common$user", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$org_struct_md:sys_common$SYS_PagingDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-resignationAt", "sourceInnerType": "FormField/Container/离职日期", "targetKey": "sys_common$org_employee_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "sys_common$org_struct_md", "targetType": "Model", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "sourceInnerType": "Field/Container/组织单元", "targetKey": "sys_common$org_struct_md:sys_common$SYS_FindDataByIdService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-start", "sourceInnerType": "DropdownButton/Button/label", "targetKey": "sys_common$org_employee_md:sys_common$SYS_MasterData_MultiEnableDataService", "targetType": "FakeSysService", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": null, "sourceInnerType": null, "targetKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}, {"value": null, "relativeJsonPath": null, "sourceInnerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch/items/sys_common$ORG_EMPLOYEE_VIEW-sys_common$org_employee_md-multi-stop", "sourceInnerType": "DropdownButton/Button/label", "targetKey": "sys_common$SYS_MasterData_MultiDisableDataService", "targetType": "ServiceDefinition", "targetInnerKey": null, "targetInnerType": null, "groupId": null}], "innerNodes": [{"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-page", "innerType": "Page", "innerName": "页面", "innerParentKey": ""}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-column-page", "innerType": "ColumnPage", "innerName": "双栏页面", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-page"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "innerType": "Table", "innerName": "表格", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-column-page"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions", "innerType": "BatchActions", "innerName": "按钮组", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-new", "innerType": "<PERSON><PERSON>", "innerName": "新建", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch", "innerType": "DropdownButton", "innerName": "批量操作", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-import", "innerType": "ImportButton", "innerName": "导入", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-export", "innerType": "ExportButton", "innerName": "导出", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-kvFTbRyPRuWhc0sg4AJO7", "innerType": "<PERSON><PERSON>", "innerName": "按钮", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-toolbar-actions", "innerType": "ToolbarActions", "innerName": "按钮组", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-logs", "innerType": "Logs", "innerName": "日志", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-toolbar-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-T0dxNmXpp6q8fg04HXRzp", "innerType": "Fields", "innerName": "字段组", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-lF1QOZEGtkaCHK1RzD8Vw", "innerType": "Field", "innerName": "员工编码", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-T0dxNmXpp6q8fg04HXRzp"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-NdUI184yh2s4lcc6w4sEg", "innerType": "Field", "innerName": "姓名", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-T0dxNmXpp6q8fg04HXRzp"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView", "innerType": "StackView", "innerName": "栈视图", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-column-page"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-empty-box", "innerType": "Box", "innerName": "区块", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-empty", "innerType": "Empty", "innerName": "空白图", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-empty-box"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view", "innerType": "View", "innerName": "视图", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page", "innerType": "Page", "innerName": "页面", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-view"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-title", "innerType": "Page<PERSON><PERSON>le", "innerName": "页面标题", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-wPS2MJGuQRIvm1fPSsLOu", "innerType": "Status", "innerName": "状态", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-title"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-header", "innerType": "<PERSON><PERSON><PERSON><PERSON>", "innerName": "页头操作栏", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions", "innerType": "Space", "innerName": "间距", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-header"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete", "innerType": "<PERSON><PERSON>", "innerName": "删除", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-copy", "innerType": "<PERSON><PERSON>", "innerName": "复制", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable", "innerType": "<PERSON><PERSON>", "innerName": "停用", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable", "innerType": "<PERSON><PERSON>", "innerName": "启用", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-edit", "innerType": "<PERSON><PERSON>", "innerName": "编辑", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detail-sys_common$org_employee_md-logs", "innerType": "Logs", "innerName": "日志", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "innerType": "Detail", "innerName": "详情", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs", "innerType": "Tabs", "innerName": "页签", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A", "innerType": "TabItem", "innerName": "页签项", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup", "innerType": "DetailGroupItem", "innerName": "详情组元素", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-eIEeROEBBnDICo0qFrg6A"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-code", "innerType": "DetailField", "innerName": "员工编码", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-name", "innerType": "DetailField", "innerName": "姓名", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-status", "innerType": "DetailField", "innerName": "状态", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-type", "innerType": "DetailField", "innerName": "员工类型", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-email", "innerType": "DetailField", "innerName": "邮箱", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-mobile", "innerType": "DetailField", "innerName": "手机", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressDetail", "innerType": "DetailField", "innerName": "详细地址", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-entryAt", "innerType": "DetailField", "innerName": "入职日期", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-resignationAt", "innerType": "DetailField", "innerName": "离职日期", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-idCard", "innerType": "DetailField", "innerName": "身份证号", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-userId", "innerType": "DetailField", "innerName": "用户", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressId", "innerType": "DetailField", "innerName": "地址", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-gyeTOSkmC__Zr4767Pu1A", "innerType": "TabItem", "innerName": "页签项", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-customDetailField", "innerType": "CustomDetailField", "innerName": "自定义详情字段", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-gyeTOSkmC__Zr4767Pu1A"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list", "innerType": "Table", "innerName": "表格", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-customDetailField"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list-batch-actions", "innerType": "BatchActions", "innerName": "按钮组", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list-toolbar-actions", "innerType": "ToolbarActions", "innerName": "按钮组", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-LAxXu7V8oaf_BP5O6foRo", "innerType": "Fields", "innerName": "字段组", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_org_link_cf-list"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-N2CK9jLOrkwXvk-aNk5FJ", "innerType": "Field", "innerName": "是否主组织", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-LAxXu7V8oaf_BP5O6foRo"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV", "innerType": "Field", "innerName": "组织身份", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-LAxXu7V8oaf_BP5O6foRo"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN", "innerType": "Field", "innerName": "组织单元", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-LAxXu7V8oaf_BP5O6foRo"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-zR-xbPeH0dUQGisUgdnZD", "innerType": "TabItem", "innerName": "页签项", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-group", "innerType": "DetailGroupItem", "innerName": "详情组元素", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-zR-xbPeH0dUQGisUgdnZD"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy", "innerType": "DetailField", "innerName": "创建人", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-group"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy", "innerType": "DetailField", "innerName": "更新人", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-group"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdAt", "innerType": "DetailField", "innerName": "创建时间", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-group"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedAt", "innerType": "DetailField", "innerName": "更新时间", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-group"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page-footer", "innerType": "<PERSON>Footer", "innerName": "页面底部", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-page"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView", "innerType": "Page", "innerName": "页面", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-column-page-stackView"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-page-title", "innerType": "Page<PERSON><PERSON>le", "innerName": "页面标题", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-page-header", "innerType": "<PERSON><PERSON><PERSON><PERSON>", "innerName": "页头操作栏", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "innerType": "FormGroup", "innerName": "表单组", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs", "innerType": "Tabs", "innerName": "页签", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR", "innerType": "TabItem", "innerName": "页签项", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup", "innerType": "FormGroupItem", "innerName": "表单组元素", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-O4m0iudejDg5YpTOD-9oR"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code", "innerType": "FormField", "innerName": "员工编码", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name", "innerType": "FormField", "innerName": "姓名", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type", "innerType": "FormField", "innerName": "员工类型", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-email", "innerType": "FormField", "innerName": "邮箱", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile", "innerType": "FormField", "innerName": "手机", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressDetail", "innerType": "FormField", "innerName": "详细地址", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-entryAt", "innerType": "FormField", "innerName": "入职日期", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-resignationAt", "innerType": "FormField", "innerName": "离职日期", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-idCard", "innerType": "FormField", "innerName": "身份证号", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId", "innerType": "FormField", "innerName": "用户", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressId", "innerType": "FormField", "innerName": "地址", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-Mhe2IM7i9iyThkD26CsaC", "innerType": "FormField", "innerName": "状态", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id", "innerType": "FormField", "innerName": "ID", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy", "innerType": "FormField", "innerName": "创建人", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy", "innerType": "FormField", "innerName": "更新人", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt", "innerType": "FormField", "innerName": "创建时间", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt", "innerType": "FormField", "innerName": "更新时间", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version", "innerType": "FormField", "innerName": "版本号", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted", "innerType": "FormField", "innerName": "逻辑删除标识", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-originOrgId", "innerType": "FormField", "innerName": "所属组织", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form-defaultGroup"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-JE2jf3T2tYith5FRLD6QN", "innerType": "TabItem", "innerName": "页签项", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-form-field-employeeOrgLinkList", "innerType": "CustomFormField", "innerName": "自定义表单字段", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-JE2jf3T2tYith5FRLD6QN"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform", "innerType": "TableForm", "innerName": "表格表单", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-form-field-employeeOrgLinkList"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-batchActions", "innerType": "BatchActions", "innerName": "按钮组", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-batchActions-delete", "innerType": "<PERSON><PERSON>", "innerName": "删除", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-batchActions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-reordActions", "innerType": "RecordActions", "innerName": "按钮组", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-action-deleteLine", "innerType": "<PERSON><PERSON>", "innerName": "删除", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-reordActions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-MoO66JROlIXfgdLgx0m1p", "innerType": "Fields", "innerName": "字段组", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-5JPZoDEYTJahgXeGmcG5P", "innerType": "Field", "innerName": "是否主组织", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-MoO66JROlIXfgdLgx0m1p"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg", "innerType": "Field", "innerName": "组织身份", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-MoO66JROlIXfgdLgx0m1p"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn", "innerType": "Field", "innerName": "组织单元", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-MoO66JROlIXfgdLgx0m1p"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-footer", "innerType": "<PERSON>Footer", "innerName": "页面底部", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-actions", "innerType": "Space", "innerName": "间距", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-footer"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-cancel", "innerType": "<PERSON><PERSON>", "innerName": "取消", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save", "innerType": "<PERSON><PERSON>", "innerName": "保存", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-actions"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-page-header", "innerType": "<PERSON><PERSON><PERSON><PERSON>", "innerName": "页头操作栏", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-page"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-page-title", "innerType": "Page<PERSON><PERSON>le", "innerName": "页面标题", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-page"}, {"innerKey": "sys_common$ORG_EMPLOYEE_VIEW-page-footer", "innerType": "<PERSON>Footer", "innerName": "页面底部", "innerParentKey": "sys_common$ORG_EMPLOYEE_VIEW-page"}], "viewContainers": [{"viewKey": null, "containerKey": "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-form", "containerLabel": "表单组", "modelKey": "sys_common$org_employee_md", "fields": [{"fieldKey": "code", "fieldLabel": "员工编码", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "name", "fieldLabel": "姓名", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "type", "fieldLabel": "员工类型", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "email", "fieldLabel": "邮箱", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "mobile", "fieldLabel": "手机", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "addressDetail", "fieldLabel": "详细地址", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "entryAt", "fieldLabel": "入职日期", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "resignationAt", "fieldLabel": "离职日期", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "idCard", "fieldLabel": "身份证号", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "userId", "fieldLabel": "用户", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "addressId", "fieldLabel": "地址", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "status", "fieldLabel": "状态", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "id", "fieldLabel": "ID", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "created<PERSON>y", "fieldLabel": "创建人", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "updatedBy", "fieldLabel": "更新人", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "createdAt", "fieldLabel": "创建时间", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "updatedAt", "fieldLabel": "更新时间", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "version", "fieldLabel": "版本号", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "deleted", "fieldLabel": "逻辑删除标识", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "originOrgId", "fieldLabel": "所属组织", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "employeeOrgLinkList.isMainOrg", "fieldLabel": "是否主组织", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "employeeOrgLinkList.identityId", "fieldLabel": "组织身份", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}, {"fieldKey": "employeeOrgLinkList.orgUnitId", "fieldLabel": "组织单元", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_CopyDataConverterService", "parentPath": "data", "type": "Output"}, {"serviceKey": "sys_common$SYS_MasterData_SaveDataService", "parentPath": "request", "type": "Input"}]}]}, {"viewKey": null, "containerKey": "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-detail", "containerLabel": "详情", "modelKey": "sys_common$org_employee_md", "fields": [{"fieldKey": "code", "fieldLabel": "员工编码", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "name", "fieldLabel": "姓名", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "status", "fieldLabel": "状态", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "type", "fieldLabel": "员工类型", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "email", "fieldLabel": "邮箱", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "mobile", "fieldLabel": "手机", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "addressDetail", "fieldLabel": "详细地址", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "entryAt", "fieldLabel": "入职日期", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "resignationAt", "fieldLabel": "离职日期", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "idCard", "fieldLabel": "身份证号", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "userId", "fieldLabel": "用户", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "addressId", "fieldLabel": "地址", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "employeeOrgLinkList.isMainOrg", "fieldLabel": "是否主组织", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "employeeOrgLinkList.identityId", "fieldLabel": "组织身份", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "employeeOrgLinkList.orgUnitId", "fieldLabel": "组织单元", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "created<PERSON>y", "fieldLabel": "创建人", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "updatedBy", "fieldLabel": "更新人", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "createdAt", "fieldLabel": "创建时间", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}, {"fieldKey": "updatedAt", "fieldLabel": "更新时间", "targetServices": [{"serviceKey": "sys_common$SYS_FindDataByIdService", "parentPath": "data", "type": "Output"}]}]}, {"viewKey": null, "containerKey": "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md", "containerLabel": "表格", "modelKey": "sys_common$org_employee_md", "fields": [{"fieldKey": "code", "fieldLabel": "员工编码", "targetServices": [{"serviceKey": "sys_common$SYS_PagingDataService", "parentPath": "data.data", "type": "Output"}, {"serviceKey": "sys_common$SYS_DeleteDataByIdService", "parentPath": null, "type": "Input"}]}, {"fieldKey": "name", "fieldLabel": "姓名", "targetServices": [{"serviceKey": "sys_common$SYS_PagingDataService", "parentPath": "data.data", "type": "Output"}, {"serviceKey": "sys_common$SYS_DeleteDataByIdService", "parentPath": null, "type": "Input"}]}]}]}, "liteProps": {"extended": false, "frontendConfig": {"modules": ["base", "terp", "service"]}, "i18nConfig": {"i18nKeySet": ["用户", "状态", "新建员工信息表", "请输入版本号", "详情", "请输入员工编码", "停用成功", "姓名", "保存", "请输入ID", "地址", "用户名", "ID", "创建人", "身份证号", "请输入更新时间", "保存成功!", "请输入姓名", "启用成功", "用户手机", "逻辑删除标识", "确认统一停用吗？", "请输入手机", "请选择", "版本号", "更新时间", "是否主组织", "批量停用", "确认统一启用吗？", "编辑", "所属组织", "选择创建人", "员工信息表", "请输入逻辑删除标识", "确认删除吗？", "批量启用", "员工编码", "复制", "选择更新人", "手机", "系统信息", "新建", "删除成功", "停用", "请输入员工类型", "按钮", "批量删除", "请输入创建时间", "详细地址", "删除", "入职日期", "离职日期", "邮箱", "启用", "批量操作", "用户邮箱", "组织单元", "选择组织单元", "选择组织身份", "主体信息", "确认启用吗？", "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},UNENABLED:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISENABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.text", "更新人", "请输入", "取消", "组织身份", "创建时间", "员工类型", "确认停用吗？"], "i18nScanPaths": ["sys_common$ORG_EMPLOYEE_VIEW-wPS2MJGuQRIvm1fPSsLOu.props.text$", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-name.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.5.label", "sys_common$ORG_EMPLOYEE_VIEW-Mhe2IM7i9iyThkD26CsaC.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.3.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.2.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.5.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.8.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.2.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs.props.items.0.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-batchActions-delete.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-action-cancel.props.label", "sys_common$ORG_EMPLOYEE_VIEW-5JPZoDEYTJahgXeGmcG5P.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-idCard.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs.props.items.1.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressId.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressId.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.0.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressId.props.label", "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable.props.actionConfig.endLogicOtherConfig.1.message", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.1.actionConfig.endLogicOtherConfig.1.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy.props.label", "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-userId.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable.props.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-code.props.componentProps.placeholder", "@exp:sys_common$ORG_EMPLOYEE_VIEW-editView-page-title.props.title", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name.props.label", "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.0.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-status.props.label", "sys_common$ORG_EMPLOYEE_VIEW-NdUI184yh2s4lcc6w4sEg.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.7.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_org_link_cf-tableform-action-deleteLine.props.label", "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs.props.items.2.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-resignationAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.10.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.2.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-enable.props.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-lF1QOZEGtkaCHK1RzD8Vw.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.1.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-mobile.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-status.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-resignationAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressId.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-entryAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.1.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-email.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.7.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressDetail.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-new.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-idCard.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-N2CK9jLOrkwXvk-aNk5FJ.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.10.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-copy.props.label", "sys_common$ORG_EMPLOYEE_VIEW-p-2v6FsTlebZMjI80JEJn.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-name.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-entryAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-Mhe2IM7i9iyThkD26CsaC.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.1.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.2.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-originOrgId.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.2.actionConfig.endLogicOtherConfig.1.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-entryAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.4.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-name.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-mobile.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-idCard.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-idCard.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-N2CK9jLOrkwXvk-aNk5FJ.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-4U2XlHnaTTBbUtUBdhjfN.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdAt.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-createdBy.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedBy.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-code.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-email.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressDetail.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-entryAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.0.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.2.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-defaultTabs.props.items.0.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete.props.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-NdUI184yh2s4lcc6w4sEg.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-type.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.6.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-action-save.props.actionConfig.endLogicOtherConfig.2.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-lF1QOZEGtkaCHK1RzD8Vw.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.0.actionConfig.endLogicOtherConfig.2.message", "@exp:sys_common$ORG_EMPLOYEE_VIEW-detailView-page-title.props.title", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.0.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-kvFTbRyPRuWhc0sg4AJO7.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-defaultTabs.props.items.1.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable.props.actionConfig.endLogicOtherConfig.1.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-edit.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-type.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-createdBy.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.9.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-resignationAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-updatedAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-email.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.0.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.8.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.6.label", "sys_common$ORG_EMPLOYEE_VIEW-5JPZoDEYTJahgXeGmcG5P.props.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.9.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-addressDetail.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-hzONoXtgexEzCHhj3cQkg.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.3.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.0.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.fields.0.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-email.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete.props.label", "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.items.1.actionConfig.beforeLogicConfig.0.text", "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-version.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-delete.props.actionConfig.endLogicOtherConfig.1.message", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.2.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-deleted.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-5nLjXFMeInTpiygCQPMyV.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.1.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-id.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-actions-disable.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-addressDetail.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedBy.props.componentProps.label", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md-batch.props.label", "sys_common$ORG_EMPLOYEE_VIEW-detailView-system-detail-field-updatedAt.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-list-sys_common$org_employee_md.props.filterFields.4.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-mobile.props.rules.0.message", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-resignationAt.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-type.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-originOrgId.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-code.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-detailView-sys_common$org_employee_md-field-userId.props.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.componentProps.placeholder", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.fields.0.label", "sys_common$ORG_EMPLOYEE_VIEW-editView-sys_common$org_employee_md-field-userId.props.editComponentProps.filterFields.1.label"]}, "key": "sys_common$ORG_EMPLOYEE_VIEW:list", "title": "list", "type": "LIST"}, "modifiedAt": null, "modifiedBy": 0, "designerObject": "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", "runtimeObject": "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", "id": null, "teamId": null, "teamCode": null}