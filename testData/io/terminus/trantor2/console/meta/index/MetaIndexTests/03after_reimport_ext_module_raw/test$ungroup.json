{"key": "test$ungroup", "moduleKey": "test", "type": "Folder", "parentKey": "test$__folder__", "path": null, "oid": "307fd1849cbf5cbf04b394ccf05d6988af6497dce2a5a7d35814acd184c812f8", "object": "H4sIAAAAAAAAAKtWSs7IzEkpSs1TssorzcnRUcpOrVSyUipJLS5RKc1LL8ovLVDSUcpLzE0Fij6bs+ppR9vz3S1AoYJEoKYSb4Tq+Pi0/JyU1KL4eJBsTmJJWn5RblhqUXFmPtDwaqBFqcnZxaW5QPUGFAKQi0pzk1KLQGbpgaFSLdDSovyCYpg/ioH25SbC7TfWUSqpLAB5wg3sSqVaALc+mdL7AAAA", "createdBy": 0, "updatedBy": 0, "createdAt": null, "updatedAt": null, "node": {"schemaVersion": 3, "type": "Folder", "props": null, "key": "test$ungroup", "name": "未分组", "children": null, "parentKey": "test$__folder__", "platformVersion": {"number": "0.0.0.0", "checksum": "0000000000000000000000000000000000000000000000000000000000000000"}}}