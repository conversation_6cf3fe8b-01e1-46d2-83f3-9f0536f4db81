{"access": "Private", "key": "test$service1", "name": "Service Example 1", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1i0n4coc1101", "name": "开始", "nextNodeKey": "node_1i0n4cscm103", "props": {"globalVariable": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "SCM_PUR$pur_pr_head_tr", "modelKey": "SCM_PUR$pur_pr_head_tr", "modelName": "ERP_SCM$pur_pr_head_tr"}}], "output": null, "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1i0n4cscm103", "name": "事件", "nextNodeKey": "node_1i0n4coc1102", "props": {"eventKey": "test$PUR_PR_EFFECT", "eventName": null, "inputMapping": [{"id": "1i0sqcaj22", "key": "request", "required": null, "value": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "relatedModel": {"modelAlias": "SCM_PUR$pur_pr_head_tr", "modelKey": "SCM_PUR$pur_pr_head_tr", "modelName": "ERP_SCM$pur_pr_head_tr"}, "valueKey": "request", "valueName": "request"}]}}], "sendAfterTransactionCommitted": true, "type": "EventProperties"}, "type": "EventNode"}, {"desc": null, "id": null, "key": "node_1i0n4coc1102", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "SCM_PUR$pur_pr_head_tr", "modelKey": "SCM_PUR$pur_pr_head_tr", "modelName": "ERP_SCM$pur_pr_head_tr"}}], "key": "test$service1", "name": "PR-生效发送服务", "output": null, "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "SCM_PUR$pur_pr_effective_send_service_perm_ac", "schedulerJob": null, "serviceFinally": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}, "type": "ServiceDefinition"}