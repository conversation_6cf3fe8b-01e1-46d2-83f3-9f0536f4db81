{"access": "Private", "key": "test$SYS_PagingDataService", "name": "Service Example 3", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": null, "desc": null, "id": null, "input": null, "key": "test$SYS_PagingDataService", "name": null, "output": null, "props": null, "type": "ServiceDefinition"}, "serviceDslMd5": null, "serviceName": null, "serviceType": "SYSTEM"}, "type": "ServiceDefinition"}