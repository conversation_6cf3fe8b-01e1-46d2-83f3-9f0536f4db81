{"access": "Private", "key": "test$PRD_ORDER_CREATE_EVENT_SERVICE", "name": "Service Example 3", "props": {"eventProps": null, "isDeleted": null, "isEnabled": null, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_d3d4271994", "name": "开始", "props": {"globalVariable": [{"fieldAlias": "ERP_SCM_PUR_RG_GROUP_BY_PO_CODE_ACTION", "fieldKey": "ERP_SCM_PUR_RG_GROUP_BY_PO_CODE_ACTION", "fieldName": "[采购退货分组]节点出参", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "SCM_PUR$pur_rg_group", "modelKey": "SCM_PUR$pur_rg_group", "modelName": "退货分组"}}], "input": [{"element": {"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "test$pur_pr_head_tr", "modelKey": "test$pur_pr_head_tr", "modelName": "采购订单-ITEM"}}, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Array", "id": null}], "output": [{"element": {"fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "SCM_PUR$pur_rg_group", "modelKey": "SCM_PUR$pur_rg_group", "modelName": "退货分组"}}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_63e1fada7b", "name": "采购退货分组", "props": {"conditionGroup": null, "convert": null, "dataConditionPermissionKey": null, "express": "REQUEST.(test$pur_pr_head_tr)request", "implementation": "SCM_PUR$PUR_RG_GROUP_BY_PO_CODE_ACTION", "implementationName": "采购退货分组", "newAction": true, "output": [{"elements": [{"fieldAlias": "success", "fieldKey": "success", "fieldName": "success", "fieldType": "Boolean", "id": null}, {"element": {"fieldAlias": "-", "fieldKey": "-", "fieldName": "-", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "SCM_PUR$pur_rg_group", "modelKey": "SCM_PUR$pur_rg_group", "modelName": "ERP_SCM$pur_rg_group"}}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null}, {"elements": [], "fieldAlias": "err", "fieldKey": "err", "fieldName": "err", "fieldType": "Object", "id": null}, {"elements": [], "fieldAlias": "info", "fieldKey": "info", "fieldName": "info", "fieldType": "Object", "id": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "ActionResponse", "fieldType": "Object", "id": null}], "outputAssign": {"customAssignments": [{"field": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}, "id": null, "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_63e1fada7b", "valueName": "节点出参"}, {"fieldType": null, "valueKey": "data", "valueName": "ActionResponse"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}, {"field": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "ERP_SCM_PUR_RG_GROUP_BY_PO_CODE_ACTION", "valueName": "[采购退货分组]节点出参"}]}, "id": null, "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_63e1fada7b", "valueName": "节点出参"}, {"fieldType": null, "valueKey": "data", "valueName": "ActionResponse"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}], "outputAssignType": "CUSTOM"}, "relatedModel": null, "requestWriteBack": true, "transactionPropagation": "NOT_SUPPORTED", "type": "SPIProperties"}, "type": "SPINode"}, {"desc": null, "id": null, "key": "node_3f25b7249a", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "id": null, "input": [{"element": {"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "test$pur_pr_head_tr", "modelKey": "test$pur_pr_head_tr", "modelName": "采购订单-ITEM"}}, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Array", "id": null}], "key": "test$PRD_ORDER_CREATE_EVENT_SERVICE", "name": "RG-采购退货分组服务", "output": [{"element": {"fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "SCM_PUR$pur_rg_group", "modelKey": "SCM_PUR$pur_rg_group", "modelName": "退货分组"}}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "inputFieldRules": null, "outputFieldRules": null, "permissionKey": "SCM_PUR$RG_GROUP_BY_PO_CODE_perm_ac", "schedulerJob": null, "serviceFinally": null, "stateMachine": null, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": null, "serviceName": null, "serviceType": null}, "type": "ServiceDefinition"}