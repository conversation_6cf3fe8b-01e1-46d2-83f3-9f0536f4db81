{"access": "Private", "key": "test$view1", "name": "View Example 1", "props": {"content": {"children": [{"children": [{"children": [{"children": [], "key": "ERP_PRD$PRD_WO_VIEW-yb_13CIGMU4pxWCjIpvxd", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-vV6c4PuRT7Ltwbu0aHgeI", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-E7KwioQUzz8jucl8_0Hio", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "ERP_PRD$PRD_WO_VIEW-dGI7CQOlUMXZDhh0YE4B7", "name": "ActionsGroup", "props": {"params": []}, "type": "Layout"}], "key": "ERP_PRD$PRD_WO_VIEW-list-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-list-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [{"children": [{"children": [{"children": [], "key": "ERP_PRD$PRD_WO_VIEW-batch-actions-1-button-1", "name": "<PERSON><PERSON>", "props": {"buttonType": "default", "confirmOn": "off", "eventActions": [{"actions": [{"actionId": "actionurJhvCzp", "actionName": "页面跳转", "config": {"openViewConfig": {"page": {"key": "ERP_PRD$PRD_WO_VIEW:edit", "name": "edit", "type": "View"}, "params": [], "refresh": true, "type": "NewPage"}}, "type": "OpenView"}]}], "label": "新建", "permissionKey": "SCM_PRD$PRD_WO_VIEW-list_perm_ac_z_2_0_0_0", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-xYK4MvYtvuKG0OqsSocQr", "name": "ImportButton", "props": {"defaultImportFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "type": "InvokeService"}, "downloadTemplateV1Flow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download"}, "downloadTemplateV2Flow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download/v2"}, "importActions": [{"enabled": true, "type": "template", "value": "SCM_PRD$PRD_ORDER_IMPORT"}, {"enabled": false, "type": "custom"}], "importFlow": {"modelAlias": "SCM_PRD$prd_order_header_tr", "serviceKey": "SCM_PRD$PRD_ORDER_HEADER_TR_API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "type": "InvokeService"}, "label": "导入", "modelAlias": "SCM_PRD$prd_order_head_import_dto", "permissionKey": "SCM_PRD$PRD_WO_VIEW-xYK4MvYtvuKG0OqsSocQr_perm_ac", "predictFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "type": "InvokeService"}, "queryTaskProcessFlow": {"serviceKey": "sys_common$API_GEI_TASK_PROGRESS_POST", "type": "InvokeService"}, "saveSubServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "type": "InvokeService"}, "templatePagingFlow": {"serviceKey": "sys_common$API_GEI_TEMPLATE_PAGING_POST", "type": "InvokeService"}}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-batch-actions-1-button-3", "name": "ExportButton", "props": {"defaultExportFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}, "exportActions": [{"enabled": true, "type": "custom"}], "exportButtonServiceProps": {"saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "exportButtonTarget": {}, "exportFlow": {"modelAlias": "SCM_PRD$prd_order_header_tr", "serviceKey": "SCM_PRD$PRD_ORDER_HEADER_TR_API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}, "getAliasFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor/struct-node/find-by-alias"}, "label": "导出", "permissionKey": "SCM_PRD$PRD_WO_VIEW-list_perm_ac_z_2_0_0_1", "queryFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}, "serviceProps": {"queryFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "templatePagingFlow": {"serviceKey": "sys_common$API_GEI_TEMPLATE_PAGING_POST", "type": "InvokeService"}}, "type": "Widget"}], "key": "ERP_PRD$PRD_WO_VIEW-eQHFIU_k5mzSpOK-Cx4ms", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-IV3B0Vz_aWpwJTtuDICjg", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-bvWaSUAlHKORoH-yQTzsK", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "ERP_PRD$PRD_WO_VIEW-batch-actions-1", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_PRD$PRD_WO_VIEW-record-actions-1-button-1", "name": "<PERSON><PERSON>", "props": {"buttonType": "default", "confirmOn": "off", "eventActions": [{"actions": [{"actionId": "actionluqHgj6a", "actionName": "页面跳转", "config": {"openViewConfig": {"page": {"key": "ERP_PRD$PRD_WO_VIEW:detail", "name": "detail", "type": "View"}, "params": [{"expression": "record?.id || route.recordId", "name": "recordId", "type": "expression"}], "refresh": true, "type": "NewPage"}}, "type": "OpenView"}]}], "label": "查看", "permissionKey": "SCM_PRD$PRD_WO_VIEW-list_perm_ac_z_2_1_0", "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-record-actions-1-button-2", "name": "<PERSON><PERSON>", "props": {"buttonType": "default", "confirmOn": "off", "eventActions": [{"actions": [{"actionId": "actionKtQ1n55W", "actionName": "页面跳转", "config": {"openViewConfig": {"page": {"key": "ERP_PRD$PRD_WO_VIEW:edit", "name": "edit", "type": "View"}, "params": [{"expression": "record?.id || route.recordId", "name": "recordId", "type": "expression"}], "refresh": true, "type": "NewPage"}}, "type": "OpenView"}]}], "label": "编辑", "permissionKey": "SCM_PRD$PRD_WO_VIEW-list_perm_ac_z_2_1_1", "showCondition": {"conditions": [{"conditions": [{"id": "wnvdy53AjltSx1UtAbBWk", "leftValue": {"fieldType": "Enum", "scope": "row", "title": "状态", "type": "VarValue", "val": "status", "value": "ERP_PRD$prd_order_header_tr.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "DRAFT", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "Q3_FTOV7FUx7LmHZ688et", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "BZIbsQclRJ8zgzgDFRXmE", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-record-actions-1-button-3", "name": "<PERSON><PERSON>", "props": {"buttonType": "default", "confirmOn": "off", "eventActions": [{"actions": [{"actionId": "action7DHEw37_", "config": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "SCM_PRD$prd_order_header_tr"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "ERP_PRD$SYS_DeleteDataByIdService"}, "type": "BindService"}, {"actionId": "actiondodZHrTR", "config": {"target": ["ERP_PRD$PRD_WO_VIEW-list"]}, "type": "Refresh"}, {"actionId": "actionEoeiO0nS", "config": {"message": "删除成功"}, "type": "Message"}]}], "label": "删除", "permissionKey": "SCM_PRD$PRD_WO_VIEW-list_perm_ac_z_2_1_2", "showCondition": {"conditions": [{"conditions": [{"id": "Lmam-uJAkgzgiBeU0cBRo", "leftValue": {"fieldType": "Enum", "scope": "row", "title": "状态", "type": "VarValue", "val": "status", "value": "ERP_PRD$prd_order_header_tr.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "DRAFT", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "7ExpaTVMVCEF0CIvqkfU3", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "TPZKXvwFPZxg9AYla_2hp", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}], "key": "ERP_PRD$PRD_WO_VIEW-record-actions-1", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_PRD$PRD_WO_VIEW-list-ERP_PRD$prd_order_header_tr-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"serviceKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "ERP_PRD$PRD_WO_VIEW-wz9x1TpiyTA4MDT6y-f9l", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "ERP_PRD$PRD_WO_VIEW-toolbar-actions-1", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_PRD$PRD_WO_VIEW-L7k8pSGeIuBLXU0GxBvWT", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "ERP_PRD$PRD_WO_VIEW-bSphSlHqCcsTLUe42B-wW", "name": "TableTitleActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_PRD$PRD_WO_VIEW-RjYeaaJb7y5xqLzzHVv4Z", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "woCode", "modelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "displayComponentType": "Text", "editComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "hidden": false, "label": "生产订单编号", "modelAlias": "SCM_PRD$prd_order_header_tr", "name": "woCode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-lBI6YsH9QH2jk8m8ZHkmV", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "woTypeId", "label": "选择订单类型", "labelField": "typeName", "modelAlias": "SCM_PRD$prd_wo_type_cf", "parentModelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "SCM_PRD$prd_wo_type_cf", "serviceKey": "ERP_PRD$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": ["typeName"], "modelAlias": "SCM_PRD$prd_wo_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "typeName", "parentModelAlias": "SCM_PRD$prd_wo_type_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "订单类型名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "4BgEZN_aFqMD5EYdznnDJ", "trigger": "auto", "valueRules": null}], "name": "typeName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "SCM_PRD$prd_wo_type_cf", "serviceKey": "ERP_PRD$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "SCM_PRD$prd_wo_type_cf", "serviceKey": "test$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "SCM_PRD$prd_wo_type_cf"}, "editComponentType": "RelationSelect", "hidden": false, "label": "订单类型", "modelAlias": "SCM_PRD$prd_wo_type_cf", "name": "woTypeId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-yBDt33Yw5nXQ4Y0CnNflK", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "invOrgId", "label": "选择生产工厂", "labelField": "orgCode", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": ["orgName"], "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgName", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "RlRZ8hHvz_Qoqm9VzTrPv", "trigger": "auto", "valueRules": null}], "name": "orgName", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "test$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_struct_md"}, "editComponentType": "RelationSelect", "hidden": false, "label": "生产工厂", "modelAlias": "sys_common$org_struct_md", "name": "invOrgId", "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-lS-kuya2IW_wis_NoQUJx", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "matId", "label": "选择产品编码", "labelField": "<PERSON><PERSON><PERSON>", "modelAlias": "GEN_MD$gen_mat_md", "parentModelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "GEN_MD$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": ["<PERSON><PERSON><PERSON>"], "modelAlias": "GEN_MD$gen_mat_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "matCode", "parentModelAlias": "GEN_MD$gen_mat_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "物料编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "3etngdBgSjYBu2L-ReL1N", "trigger": "auto", "valueRules": null}], "name": "matCode", "type": "TEXT"}, {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON>", "parentModelAlias": "GEN_MD$gen_mat_md", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "物料名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "Sz5jWCLRek2KDBUWiubnu", "trigger": "auto", "valueRules": null}], "name": "<PERSON><PERSON><PERSON>", "type": "TEXT"}, {"componentProps": {"columns": ["matCode", "<PERSON><PERSON><PERSON>", "genMatTypeCfId", "baseUomId", "weightUomId", "grossWeight", "netWeight", "volumUomId", "matVolum", "cateId", "brandId", "statusId", "batchRelv", "isBatchDetermination", "genCharaClassId", "shelfLife", "charas", "isAutoCountPlan", "cost", "matCodeExt", "status", "atpGroupId", "imageUrl", "remark", "customMat", "bomUseId", "isKitSls", "isCompleteSetDel"], "fieldAlias": "genMatTypeCfId", "labelField": "matTypeName", "modelAlias": "GEN_MD$gen_mat_type_cf", "parentModelAlias": "GEN_MD$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "test$SYS_PagingDataService"}}, "editComponentProps": {}, "editComponentType": "RelationSelect", "label": "物料类型", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "Jvkq2ky-USqXLGUxvSfYn", "trigger": "auto", "valueRules": null}], "name": "genMatTypeCfId", "type": "OBJECT"}, {"componentProps": {"columns": ["matCode", "<PERSON><PERSON><PERSON>", "genMatTypeCfId", "baseUomId", "weightUomId", "grossWeight", "netWeight", "volumUomId", "matVolum", "cateId", "brandId", "statusId", "batchRelv", "isBatchDetermination", "genCharaClassId", "shelfLife", "charas", "isAutoCountPlan", "cost", "matCodeExt", "status", "atpGroupId", "imageUrl", "remark", "customMat", "bomUseId", "isKitSls", "isCompleteSetDel"], "fieldAlias": "baseUomId", "labelField": "uomDesc", "modelAlias": "GEN_MD$gen_uom_type_cf", "parentModelAlias": "GEN_MD$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "test$SYS_PagingDataService"}}, "editComponentProps": {}, "editComponentType": "RelationSelect", "label": "基本计量单位", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "gpbvQjjoVjGC8TMKRFEma", "trigger": "auto", "valueRules": null}], "name": "baseUomId", "type": "OBJECT"}], "findFlow": {"context$": "$context", "modelAlias": "GEN_MD$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "GEN_MD$gen_mat_md", "serviceKey": "test$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "GEN_MD$gen_mat_md"}, "editComponentType": "RelationSelect", "hidden": false, "label": "产品编码", "modelAlias": "ERP_GEN$gen_mat_prd_md", "name": "matId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-5gPyKnFpp8r4MT6zA4M9W", "name": "Field", "props": {"componentProps": {"fieldAlias": "qty", "modelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "editComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "hidden": false, "label": "订单数量", "name": "qty", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-3Iobp71Y-bbedHWFWrhq6", "name": "Field", "props": {"componentProps": {"fieldAlias": "receivedQty", "modelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请输入", "precision": 2}, "displayComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "editComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "hidden": false, "label": "已收货数量", "name": "receivedQty", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-prSHpFTFgh_lDp1TQp2UL", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "prdUomId", "label": "选择生产计量单位", "labelField": "uomDesc", "modelAlias": "GEN_MD$gen_uom_type_cf", "parentModelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "GEN_MD$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "labelField": ["uomDesc"], "modelAlias": "GEN_MD$gen_uom_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "uomType", "modelAlias": "GEN_MD$gen_uom_type_cf", "parentModelAlias": "GEN_MD$gen_uom_type_cf", "placeholder": "请选择"}, "editComponentProps": {}, "editComponentType": "Select", "label": "计量单位类型编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "tKxdrvk6i-24pcAu8Dhhv", "trigger": "auto", "valueRules": null}], "name": "uomType", "type": "SELECT"}, {"componentProps": {"fieldAlias": "uomCode", "parentModelAlias": "GEN_MD$gen_uom_type_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "计量单位编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "jjJdkIEqAIYWiuRRcarrX", "trigger": "auto", "valueRules": null}], "name": "uomCode", "type": "TEXT"}, {"componentProps": {"fieldAlias": "uomDesc", "parentModelAlias": "GEN_MD$gen_uom_type_cf", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "计量单位名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "QR_KxYN9sQ5BWDrufw3EA", "trigger": "auto", "valueRules": null}], "name": "uomDesc", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "GEN_MD$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "GEN_MD$gen_uom_type_cf", "serviceKey": "test$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "GEN_MD$gen_uom_type_cf"}, "editComponentType": "RelationSelect", "hidden": false, "label": "生产计量单位", "modelAlias": "GEN_MD$gen_uom_type_cf", "name": "prdUomId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-glt8uZpUv3THo5lZeR92H", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "status", "modelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请选择"}, "displayComponentProps": {"copyable": false, "enumStyle": "normal", "modelAlias": "SCM_PRD$prd_order_header_tr", "options": [{"color": "warning", "label": "草稿中", "value": "DRAFT"}, {"color": "default", "label": "已提交", "value": "SUBMITTED"}, {"color": "success", "label": "生产完工", "value": "PRODUCTION_COMPLETED"}]}, "displayComponentType": "Enum", "editComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "hidden": false, "label": "订单执行状态", "modelAlias": "SCM_PRD$prd_order_header_tr", "name": "status", "type": "SELECT", "width": 116}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-f2SVIY4-BZp8vJYxU4t-X", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "confirmStatus", "modelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "displayComponentType": "Enum", "editComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "label": "报工状态", "modelAlias": "SCM_PRD$prd_order_header_tr", "name": "confirmStatus", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-p3SU4y7Edv0eHrdEFABIH", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "deliveredStatus", "modelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal", "modelAlias": "SCM_PRD$prd_order_header_tr"}, "displayComponentType": "Enum", "editComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "label": "交货状态", "name": "deliveredStatus", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-kjaqHpScPvzc_-M3s-BEf", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "plannedStartDate", "modelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请选择"}, "displayComponentProps": {"format": "YYYY-MM-DD", "modelAlias": "SCM_PRD$prd_order_header_tr", "quickRanges": []}, "displayComponentType": "Date", "editComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "hidden": false, "label": "计划开始日期", "modelAlias": "SCM_PRD$prd_order_header_tr", "name": "plannedStartDate", "type": "DATE", "width": 134}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-OaNJ5CPz7WYhttBSSvdDu", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "plannedEndDate", "modelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请选择"}, "displayComponentProps": {"format": "YYYY-MM-DD", "modelAlias": "SCM_PRD$prd_order_header_tr", "quickRanges": []}, "displayComponentType": "Date", "editComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "hidden": false, "label": "计划结束日期", "modelAlias": "SCM_PRD$prd_order_header_tr", "name": "plannedEndDate", "type": "DATE", "width": 134}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-bURc13rj_rQBoKnh9EvZl", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "actualStartDate", "modelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请选择"}, "displayComponentProps": {"format": "YYYY-MM-DD", "modelAlias": "SCM_PRD$prd_order_header_tr", "quickRanges": []}, "displayComponentType": "Date", "editComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "hidden": false, "label": "实际开始日期", "modelAlias": "SCM_PRD$prd_order_header_tr", "name": "actualStartDate", "type": "DATE", "width": 134}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-sl8mqDFv1dbDd57EQvlOm", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "actualEndDate", "modelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请选择"}, "displayComponentProps": {"format": "YYYY-MM-DD", "modelAlias": "SCM_PRD$prd_order_header_tr"}, "displayComponentType": "Date", "editComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "hidden": false, "label": "实际结束日期", "modelAlias": "SCM_PRD$prd_order_header_tr", "name": "actualEndDate", "type": "DATE", "width": 134}, "type": "Widget"}], "key": "ERP_PRD$PRD_WO_VIEW-twLDilRvaNxzZUf1cdBCu", "name": "Fields", "props": {}, "type": "Meta"}, {"children": [{"children": [], "key": "ERP_PRD$PRD_WO_VIEW-uqPdtHzO-8ApZS4O7E-jl", "name": "FilterField", "props": {"componentProps": {"fieldAlias": "woCode", "modelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "editComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "editComponentType": "InputText", "filterType": "fuzzy", "hidden": false, "label": "生产订单编号", "name": "woCode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-O2HFzhX4JW-q-Su0LBFS3", "name": "FilterField", "props": {"componentProps": {"fieldAlias": "invOrgId", "label": "选择生产工厂", "labelField": "orgCode", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_struct_md"}, "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "组织编码", "name": "orgCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "组织名称", "name": "orgName", "required": false, "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织编码", "name": "orgCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织名称", "name": "orgName", "required": false, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_struct_md"}}], "serviceKey": "test$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "orgName", "modalProps": {"size": "middle", "width": 720}, "mode": "single", "modelAlias": "sys_common$org_struct_md", "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "ysAI77YsT3NKS4PHn8NY0", "leftValue": {"fieldType": "Text", "title": "业务类型编码集合", "type": "VarValue", "val": "orgBusinessTypeCodes", "value": "sys_common$org_struct_md.orgBusinessTypeCodes", "valueType": "VAR", "varVal": "orgBusinessTypeCodes", "varValue": [{"valueKey": "orgBusinessTypeCodes", "valueName": "orgBusinessTypeCodes"}]}, "operator": "CONTAINS", "rightValue": {"constValue": "INV_ORG", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "CDl4pYXl5kP_B4gFseVhR", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "xt614nWgIurQMW1PhVIHQ", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "生产工厂", "name": "invOrgId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-7Q5t8ey0i6FZuxElP7eqa", "name": "FilterField", "props": {"componentProps": {"fieldAlias": "matId", "label": "选择产品编码", "labelField": "<PERSON><PERSON><PERSON>", "modelAlias": "GEN_MD$gen_mat_md", "parentModelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "GEN_MD$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "GEN_MD$gen_mat_md"}, "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "matCode", "modelAlias": "GEN_MD$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "物料编码", "name": "matCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "GEN_MD$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "物料名称", "name": "<PERSON><PERSON><PERSON>", "required": false, "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "matCode", "modelAlias": "GEN_MD$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "label": "物料编码", "name": "matCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "GEN_MD$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "label": "物料名称", "name": "<PERSON><PERSON><PERSON>", "required": false, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "GEN_MD$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "GEN_MD$gen_mat_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "GEN_MD$gen_mat_md"}}], "serviceKey": "test$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "<PERSON><PERSON><PERSON>", "modalProps": {"size": "middle", "width": 720}, "mode": "single", "modelAlias": "GEN_MD$gen_mat_md", "showFilterFields": true, "showScope": "filter", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "label": "产品编码", "name": "matId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-1uXDRRi4jU2k6Cly2W0et", "name": "FilterField", "props": {"componentProps": {"fieldAlias": "status", "modelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "editComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "editComponentType": "Select", "label": "状态", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "0uToainKLJ00UNgflGgLE", "valueRules": null}], "name": "status", "type": "SELECT", "width": 120}, "type": "Widget"}], "key": "ERP_PRD$PRD_WO_VIEW-6HquOz_u7wrR4vDgPIqH-", "name": "Filter<PERSON>ields", "props": {}, "type": "Meta"}], "key": "ERP_PRD$PRD_WO_VIEW-table-container-ERP_PRD$prd_order_header_tr", "name": "Table", "props": {"acceptFilterQuery": true, "allowClickRowSelect": true, "allowRowSelect": true, "enableSolution": false, "filterFields": [{"componentProps": {"fieldAlias": "woCode", "modelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请输入"}, "displayComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "editComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "editComponentType": "InputText", "filterType": "fuzzy", "hidden": false, "label": "生产订单编号", "name": "woCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "invOrgId", "label": "选择生产工厂", "labelField": "orgCode", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_struct_md"}, "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "组织编码", "name": "orgCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "组织名称", "name": "orgName", "required": false, "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织编码", "name": "orgCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织名称", "name": "orgName", "required": false, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_struct_md"}}], "serviceKey": "test$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "orgName", "modalProps": {"size": "middle", "width": 720}, "mode": "single", "modelAlias": "sys_common$org_struct_md", "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "ysAI77YsT3NKS4PHn8NY0", "leftValue": {"fieldType": "Text", "title": "业务类型编码集合", "type": "VarValue", "val": "orgBusinessTypeCodes", "value": "sys_common$org_struct_md.orgBusinessTypeCodes", "valueType": "VAR", "varVal": "orgBusinessTypeCodes", "varValue": [{"valueKey": "orgBusinessTypeCodes", "valueName": "orgBusinessTypeCodes"}]}, "operator": "CONTAINS", "rightValue": {"constValue": "INV_ORG", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "CDl4pYXl5kP_B4gFseVhR", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "xt614nWgIurQMW1PhVIHQ", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "生产工厂", "name": "invOrgId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "matId", "label": "选择产品编码", "labelField": "<PERSON><PERSON><PERSON>", "modelAlias": "GEN_MD$gen_mat_md", "parentModelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "GEN_MD$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "GEN_MD$gen_mat_md"}, "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "matCode", "modelAlias": "GEN_MD$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "物料编码", "name": "matCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "GEN_MD$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "物料名称", "name": "<PERSON><PERSON><PERSON>", "required": false, "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "matCode", "modelAlias": "GEN_MD$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "label": "物料编码", "name": "matCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "GEN_MD$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "label": "物料名称", "name": "<PERSON><PERSON><PERSON>", "required": false, "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "GEN_MD$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "GEN_MD$gen_mat_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "GEN_MD$gen_mat_md"}}], "serviceKey": "test$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "<PERSON><PERSON><PERSON>", "modalProps": {"size": "middle", "width": 720}, "mode": "single", "modelAlias": "GEN_MD$gen_mat_md", "showFilterFields": true, "showScope": "filter", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "label": "产品编码", "name": "matId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "status", "modelAlias": "SCM_PRD$prd_order_header_tr", "placeholder": "请选择"}, "displayComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "editComponentProps": {"modelAlias": "SCM_PRD$prd_order_header_tr"}, "editComponentType": "Select", "label": "状态", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "0uToainKLJ00UNgflGgLE", "valueRules": null}], "name": "status", "type": "SELECT", "width": 120}], "flow": {"containerKey": "ERP_PRD$PRD_WO_VIEW-table-container-ERP_PRD$prd_order_header_tr", "context$": "$context", "modelAlias": "SCM_PRD$prd_order_header_tr", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "SCM_PRD$prd_order_header_tr"}}, {"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "test$SYS_PagingDataService", "type": "InvokeSystemService"}, "isProFilter": false, "label": "", "mode": "normal", "modelAlias": "SCM_PRD$prd_order_header_tr", "pagination": {"defaultPageSize": 20, "needTotal": true, "pageSizeOptions": [20, 50, 100, 200]}, "selectType": "multiple", "showConfigure": false, "showFilterFields": true, "showScope": "all", "showType": "normal", "sortOrders": [], "subTableConfig": {}, "tableCondition": null, "tableConditionContext$": null, "toolbar": {"search": false}, "useNewFilter": true}, "type": "Container"}, {"children": [], "key": "ERP_PRD$PRD_WO_VIEW-DCgL6hH3bip762hmyHqhG", "name": "PageHeaderAddition", "props": {}, "type": "Layout"}], "key": "ERP_PRD$PRD_WO_VIEW-list", "name": "Page", "props": {"actionConfig": {}, "backResourceTabProcessConfig": "retain", "backgroundTransparent": true, "collectionService": {"createBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK/save"}, "deleteBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK/delete"}, "getCurrentSceneBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK"}}, "eventActions": [], "headerUnderline": true, "params": [], "showFooter": false, "showHeader": false}, "type": "Container"}, "key": "test$view1", "name": "View Example 1", "permissionKey": "test$view1_perm", "title": "View Example 1", "type": "CUSTOM"}, "type": "View"}