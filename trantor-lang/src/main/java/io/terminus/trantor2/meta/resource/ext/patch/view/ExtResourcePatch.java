package io.terminus.trantor2.meta.resource.ext.patch.view;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.auto.service.AutoService;
import com.google.common.collect.Sets;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.config.datamanager.ViewResourceConfig;
import io.terminus.trantor2.utils.ExtJsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Nonnull;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static io.terminus.trantor2.meta.resource.ext.ExtMeta.ActionType.*;

/**
 * <AUTHOR>
 */
@Slf4j
@AutoService(ViewPatch.class)
public class ExtResourcePatch implements ViewPatch {
    @Override
    public String getRootField() {
        return DataManagerView.DataManagerViewFields.resources;
    }

    @Override
    public List<ExtMeta.Snippet> fetchSnippets(@NotNull String metaKey, @NotNull ObjectNode propsNode, @NotNull ObjectNode originalProps, ExtMeta.Props extProps) {
        if (extProps == null || extProps.getOriginType() != MetaType.View || CollectionUtils.isEmpty(extProps.getSnippets())) {
            return Collections.emptyList();
        }
        return getResourceSnippets(propsNode, originalProps);
    }

    @Override
    public void applySnippets(@NotNull String metaKey, ObjectNode propsNode, List<ExtMeta.Snippet> snippets) {
        if (propsNode == null) {
            return;
        }
        JsonNode jsonNode = propsNode.get(DataManagerView.DataManagerViewFields.resources);
        if (jsonNode != null && jsonNode.isArray()) {
            mergeResource((ArrayNode) jsonNode, snippets);
        }
    }


    private List<ExtMeta.Snippet> getResourceSnippets(JsonNode propsNode, ObjectNode originalProps) {
        try {
            return splitResources(
                    (ArrayNode) propsNode.get(DataManagerView.DataManagerViewFields.resources),
                    (ArrayNode) originalProps.get(DataManagerView.DataManagerViewFields.resources));

        } catch (Exception e) {
            log.error("split condition select error", e);
        }
        return Collections.emptyList();
    }

    private void mergeResource(ArrayNode original, List<ExtMeta.Snippet> snippets) {
        if (original == null || snippets == null || snippets.isEmpty()) {
            return;
        }

        snippets.stream().filter(s -> s.getAction() == APPEND).forEach(s -> original.addAll(s.getContent()));

        Set<String> removeKeys = new HashSet<>();
        snippets.stream().filter(s -> s.getAction() == REMOVE).forEach(s -> {
            ArrayNode content = s.getContent();
            if (content != null) {
                content.elements().forEachRemaining(node -> removeKeys.add(node.asText()));
            }
        });

        Map<String, JsonNode> editMap = new HashMap<>();
        snippets.stream().filter(s -> s.getAction() == EDIT).forEach(s -> {
            ArrayNode content = s.getContent();
            if (content != null) {
                String key = s.getKey() != null ? s.getKey() : s.getNode();
                editMap.put(key, s.getContent().get(0));
            }
        });

        for (int i = 0; i < original.size(); i++) {
            JsonNode node = original.get(i);
            if (removeKeys.contains(node.get("key").asText())) {
                original.remove(i);
                i--;
            } else {
                JsonNode editNode = editMap.get(node.get("key").asText());
                if (editNode != null) {
                    original.set(i, editNode);
                }
            }
        }
    }


    @Nonnull
    private List<ExtMeta.Snippet> splitResources(ArrayNode extResources, ArrayNode originalResources) {
        List<ExtMeta.Snippet> snippets = new ArrayList<>();
        if (extResources == null || originalResources == null) {
            return snippets;
        }
        Map<String, ViewResourceConfig> extResourceMap = JsonUtil.fromJson(extResources.toString(), new TypeReference<List<ViewResourceConfig>>() {
        }).stream().collect(Collectors.toMap(ViewResourceConfig::getKey, Function.identity(),
                (k1, k2) -> ViewResourceConfig.ViewResourceType.Button.equals(k2.getType()) ? k2 : k1));
        Map<String, ViewResourceConfig> originalResourceMap = JsonUtil.fromJson(originalResources.toString(), new TypeReference<List<ViewResourceConfig>>() {
        }).stream().collect(Collectors.toMap(ViewResourceConfig::getKey, Function.identity(),
                (k1, k2) -> ViewResourceConfig.ViewResourceType.Button.equals(k2.getType()) ? k2 : k1));

        Set<String> extKeys = extResourceMap.keySet();
        Set<String> originalKeys = originalResourceMap.keySet();

        // add
        List<ViewResourceConfig> add = new ArrayList<>();
        Sets.difference(extKeys, originalKeys).forEach(key -> add.add(extResourceMap.get(key)));
        if (!add.isEmpty()) {
            ExtMeta.Snippet snippet = ExtMeta.Snippet.builder()
                    .action(APPEND)
                    .content(JsonUtil.NON_INDENT.getObjectMapper().valueToTree(add))
                    .build();
            snippets.add(snippet);
        }

        // remove
        Sets.SetView<String> difference = Sets.difference(originalKeys, extKeys);
        if (!difference.isEmpty()) {
            ExtMeta.Snippet snippet = ExtMeta.Snippet.builder()
                    .action(REMOVE)
                    .content(JsonUtil.NON_INDENT.getObjectMapper().valueToTree(difference))
                    .build();
            snippets.add(snippet);
        }

        // edit
        Sets.intersection(extKeys, originalKeys).forEach(key -> {
            ViewResourceConfig ext = extResourceMap.get(key);
            ViewResourceConfig original = originalResourceMap.get(key);
            if (!ext.equals(original)) {
                ExtMeta.Snippet snippet = ExtMeta.Snippet.builder()
                        .action(ExtMeta.ActionType.EDIT)
                        .key(key)
                        .node(key)
                        .content(ExtJsonUtils.createArrayNodeWithObject(ext))
                        .build();
                snippets.add(snippet);
            }
        });
        return snippets;
    }

}
