package io.terminus.trantor2.meta.resource.ext.jsonfunc.params;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.node.ArrayNode;
import lombok.Getter;
import lombok.Setter;

import jakarta.annotation.Nonnull;

import static io.terminus.trantor2.meta.resource.ext.jsonfunc.params.MoveArrayElementParams.ABSOLUTE_POSITION;
import static io.terminus.trantor2.meta.resource.ext.jsonfunc.params.MoveArrayElementParams.RELATIVE_POSITION;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
@JsonSubTypes({
        @JsonSubTypes.Type(value = InsertRelativePosition.class, name = RELATIVE_POSITION),
        @JsonSubTypes.Type(value = InsertAbsolutePosition.class, name = ABSOLUTE_POSITION)
})
public abstract class InsertAtIndexParams implements FuncParam {
    public static final String ABSOLUTE_POSITION = "AbsolutePosition";
    public static final String RELATIVE_POSITION = "RelativePosition";


    /**
     * 基于相对位置/绝对位置处理后的实际下标，负数不处理
     */
    public abstract Integer getIndex(@Nonnull ArrayNode parentNode);
}
