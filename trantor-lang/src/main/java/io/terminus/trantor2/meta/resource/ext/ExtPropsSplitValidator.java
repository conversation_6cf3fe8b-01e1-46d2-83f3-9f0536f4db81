package io.terminus.trantor2.meta.resource.ext;

import com.fasterxml.jackson.databind.JsonNode;

import jakarta.annotation.Nonnull;

/**
 * <AUTHOR>
 */
@FunctionalInterface
public interface ExtPropsSplitValidator {
    /**
     * 拆分扩展片段校验
     *
     * @param jsonNode props 或 props 的第一层子节点，主要看 {@link ExtendableMeta#getExtParams().getPropsExtFieldName()} 是否为空
     * @param snippet  当前片段
     */
    void splitValidate(@Nonnull JsonNode jsonNode, @Nonnull ExtMeta.Snippet snippet);
}
