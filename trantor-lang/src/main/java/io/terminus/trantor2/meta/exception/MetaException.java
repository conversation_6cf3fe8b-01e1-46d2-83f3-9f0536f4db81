package io.terminus.trantor2.meta.exception;

import io.terminus.trantor2.common.exception.TrantorBizException;

import static io.terminus.trantor2.common.exception.ErrorType.META_UNKNOWN_ERROR;

/**
 * <AUTHOR>
 */
public class MetaException extends TrantorBizException {
    private static final long serialVersionUID = -7214242062957144438L;

    public MetaException(String message) {
        super(META_UNKNOWN_ERROR, message, new Object[]{message});
    }
}
