package io.terminus.trantor2.meta.schema;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标记元数据字段为新加字段
 * <AUTHOR>
 **/
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({java.lang.annotation.ElementType.FIELD, ElementType.TYPE})
public @interface TMetaAvailable {
    TPlatformVersion value();
}
