package io.terminus.trantor2.meta.resource.ext.patch.view;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.auto.service.AutoService;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.resource.ext.ExtStep;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.utils.TemplateUtils;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;

import static io.terminus.trantor2.utils.ExtJsonUtils.KEY;

/**
 * <AUTHOR>
 */
@Slf4j
@AutoService(ViewPatch.class)
public class ExtContentViewPatch implements ExtViewContentPatch {
    private static final String EXT = "ext";
    private static final String EXT_PROPS = "extProps";
    private static final String CHILDREN = "children";
    private static final String REMOVED = "removed";
    private static final String EDITED = "edited";
    private static final String APPENDED = "appended";
    private static final String EXT_STEPS = "extSteps";
    private static final String PATH = "path";
    private static final String VALUE = "value";
    @Override
    public List<ExtMeta.Snippet> fetchSnippets(@NotNull String metaKey, @NotNull ObjectNode propsNode, @NotNull ObjectNode originalProps, ExtMeta.Props extProps) {
        JsonNode jsonNode = propsNode.get(DataManagerView.DataManagerViewFields.content);
        if (jsonNode == null || !jsonNode.isObject()) {
            return new ArrayList<>();
        }
        List<ExtStep> extSteps = parseExtSteps(jsonNode);
        ((ObjectNode) jsonNode).set(EXT_STEPS, ObjectJsonUtil.MAPPER.valueToTree(extSteps));
        return new ArrayList<>();
    }

    @Override
    public void applySnippets(@NotNull String metaKey, ObjectNode propsNode, List<ExtMeta.Snippet> snippets) {

    }

    @Override
    public boolean postPatch() {
        return false;
    }

    /**
     * 递归处理 jsonNode，找出所有含有 extProps 属性的 object 节点，构建 extStep，需要 key
     * <p>
     * {
     * "children": [],
     * "key": "batch$tUnCy_8D6VNtW8zZuMxNyP",
     * "name": "Field",
     * "props": {
     * "componentProps": {
     * "fieldAlias": "name",
     * "modelAlias": "batch$test_status_field1",
     * "placeholder": "请输入"
     * },
     * "hidden": false,
     * "label": "名称",
     * "name": "name",
     * "type": "TEXT",
     * "width": 146
     * },
     * "extProps": {
     * "removed": true
     * },
     * "type": "Widget"
     * }
     * </>
     */
    private List<ExtStep> parseExtSteps(JsonNode node) {
        if (isValidNode(node)) {
            return Collections.emptyList();
        }
        List<ExtStep> extSteps = new ArrayList<>();
        if (node.isObject()) {
            processObjectNode(node, null, extSteps);
        } else if (node.isArray()) {
            node.forEach(child -> extSteps.addAll(parseExtSteps(child)));
        }
        return extSteps;
    }

    private List<ExtStep> parseExtSteps(JsonNode parent, int index) {
        if (isValidNode(parent) || !parent.isArray()) {
            return Collections.emptyList();
        }

        JsonNode node = parent.get(index);
        if (isValidNode(node)) {
            return Collections.emptyList();
        }
        List<ExtStep> extSteps = new ArrayList<>();
        if (node.isObject()) {
            JsonNode previousNode = index > 0 ? parent.get(index - 1) : null;
            processObjectNode(node, previousNode, extSteps);
        } else if (node.isArray()) {
            node.forEach(child -> extSteps.addAll(parseExtSteps(child)));
        }
        return extSteps;
    }

    private void processObjectNode(JsonNode node, JsonNode previousNode, List<ExtStep> extSteps) {
        String key = getTextValue(node.get(KEY));
        if (key == null) {
            return;
        }

        boolean skip = false;
        if (node.has(EXT_PROPS)) {
            JsonNode extProps = node.get(EXT_PROPS);
            if (extProps.isObject()) {
                String previousKey = getTextValue(previousNode != null ? previousNode.get(KEY) : null);
                if (extProps.has(EDITED)) {
                    extProps = extProps.get(EDITED);
                    if (extProps.isArray()) {
                        extProps.forEach(extProp -> handleEditedAction((ObjectNode) extProp, key, extSteps));
                    }
                } else {
                    skip = handleRemovedAction(extProps, key, extSteps)
                            || handleAppendedAction(extProps, key, previousKey, extSteps);
                }
            }
        } else if (node.has(EXT) && node.get(EXT).isBoolean() && node.get(EXT).asBoolean()) {
            String previousKey = getTextValue(previousNode != null ? previousNode.get(KEY) : null);
            if (previousKey != null) {
                addAppendedExtSteps(key, previousKey, extSteps);
                skip = true;
            }
        }
        if (!skip && node.has(CHILDREN)) {
            JsonNode children = node.get(CHILDREN);
            for (int i = 0; i < children.size(); i++) {
                extSteps.addAll(parseExtSteps(children, i));
            }
        }
    }

    private boolean isValidNode(JsonNode node) {
        return node == null || node.isNull() || (!node.isArray() && !node.isObject());
    }

    private String getTextValue(JsonNode node) {
        return node != null && node.isTextual() ? node.textValue() : null;
    }

    private boolean handleRemovedAction(JsonNode extP, String key, List<ExtStep> extSteps) {
        JsonNode rmNode = extP.get(REMOVED);
        if (rmNode != null && rmNode.isBoolean() && rmNode.asBoolean()) {
            addRemovedExtSteps(key, extSteps);
            return true;
        }
        return false;
    }

    private boolean handleAppendedAction(JsonNode extP, String key, String preNodeKey, List<ExtStep> extSteps) {
        JsonNode addNode = extP.get(APPENDED);
        if (addNode != null && addNode.isBoolean() && addNode.asBoolean()) {
            addAppendedExtSteps(key, preNodeKey, extSteps);
            return true;
        }
        return false;
    }

    private void handleEditedAction(ObjectNode extProp, String key, List<ExtStep> extSteps) {
        Map<String, Object> dataModel = new HashMap<>(4);
        if (!extProp.has(PATH) || !extProp.has(VALUE)) {
            return;
        }
        if (extProp.get("func").asText("").equals("SET_JSON_VALUE")) {
            dataModel.put("key", key);
            dataModel.put(PATH, extProp.get(PATH).asText());
            dataModel.put("newValue", extProp.get(VALUE).asText());
            TemplateUtils.addExtSteps("EDIT_VIEW_ATTRIBUTE", dataModel, extSteps);
        }
    }

    private void addRemovedExtSteps(String key, List<ExtStep> extSteps) {
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("key", key);
        addExtSteps(ExtMeta.ActionType.REMOVE, dataModel, extSteps);
    }

    private void addAppendedExtSteps(String key, @Nullable String preNodeKey, List<ExtStep> extSteps) {
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("key", key);
        if (preNodeKey != null) {
            dataModel.put("preNodeKey", preNodeKey);
        }
        addExtSteps(ExtMeta.ActionType.APPEND, dataModel, extSteps);
    }

    private void addExtSteps(ExtMeta.ActionType actionType, Map<String, Object> dataModel, List<ExtStep> extSteps) {
        TemplateUtils.addExtSteps(actionType + "_VIEW_NODE", dataModel, extSteps);
    }
}
