package io.terminus.trantor2.meta.resource.ext.patch.scene;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.auto.service.AutoService;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.resource.ext.patch.AbstractStringArrayExtPatch;
import io.terminus.trantor2.scene.config.datamanager.DataManagerSceneConfig;
import io.terminus.trantor2.scene.meta.SceneMeta;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@AutoService(ScenePatch.class)
public class ExtSceneUsedModelAliasPatch extends AbstractStringArrayExtPatch implements ScenePatch {
    @Override
    public String getRootField() {
        return SceneMeta.SceneProps.SceneFieldNames.sceneConfig + "/" + DataManagerSceneConfig.Fields.usedModelAlias;
    }

    @Override
    public List<ExtMeta.Snippet> fetchSnippets(@NotNull String metaKey, @NotNull ObjectNode propsNode, @NotNull ObjectNode originalProps, ExtMeta.Props extProps) {
        if (extProps == null || extProps.getOriginType() != MetaType.Scene) {
            return Collections.emptyList();
        }
        return split(getRootField(), propsNode, originalProps);
    }

    @Override
    public void applySnippets(@NotNull String metaKey, ObjectNode propsNode, List<ExtMeta.Snippet> snippets) {
        merge(getRootField(), propsNode, snippets);
    }
}
