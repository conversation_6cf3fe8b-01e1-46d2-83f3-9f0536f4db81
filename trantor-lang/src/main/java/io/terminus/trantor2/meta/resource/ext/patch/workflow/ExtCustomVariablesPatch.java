package io.terminus.trantor2.meta.resource.ext.patch.workflow;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.auto.service.AutoService;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.resource.ext.patch.AbstractArrayObjectValueExtPatch;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@AutoService(WorkflowPatch.class)
public class ExtCustomVariablesPatch extends AbstractArrayObjectValueExtPatch implements WorkflowPatch {
    @Override
    public String getRootField() {
        return "customVariables";
    }

    @NotNull
    @Override
    public String getArrayMainField() {
        return "fieldKey";
    }

    @Override
    public List<ExtMeta.Snippet> fetchSnippets(@NotNull String metaKey, @NotNull ObjectNode propsNode,
                                               @NotNull ObjectNode originalProps, ExtMeta.Props extProps) {
        return splitArrayObject(getRootField(), propsNode, originalProps);
    }

    @Override
    public void applySnippets(@NotNull String metaKey, ObjectNode propsNode, List<ExtMeta.Snippet> snippets) {
        if (propsNode == null || CollectionUtils.isEmpty(snippets)) {
            return;
        }
        String rootField = getRootField();
        mergeArrayObject(rootField, propsNode, snippets);
    }
}
