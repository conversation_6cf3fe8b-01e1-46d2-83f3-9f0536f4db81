package io.terminus.trantor2.meta.resource.ext.patch.module;

import com.google.auto.service.AutoService;
import io.terminus.trantor2.meta.resource.ext.patch.AbstractMapObjectValueExtPatch;
import io.terminus.trantor2.meta.resource.ext.patch.AbstractReplaceExtPatch;
import io.terminus.trantor2.module.meta.ModuleMeta;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@AutoService(ModulePatch.class)
public class ExtModuleGlobalConfigPatch extends AbstractReplaceExtPatch implements ModulePatch {
    @Override
    public String getRootField() {
        return ModuleMeta.ModuleProps.Fields.globalConfig;
    }
}
