package io.terminus.trantor2.meta.resource.ext.patch.view;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.auto.service.AutoService;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.resource.ext.patch.AbstractStringArrayExtPatch;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@AutoService(ViewPatch.class)
public class ExtFrontendConfigPatch extends AbstractStringArrayExtPatch implements ViewPatch {

    @Override
    public String getRootField() {
        return "frontendConfig/modules";
    }

    @Override
    public List<ExtMeta.Snippet> fetchSnippets(@NotNull String metaKey, @NotNull ObjectNode propsNode, @NotNull ObjectNode originalProps, ExtMeta.Props extProps) {
        if (extProps == null || extProps.getOriginType() != MetaType.View) {
            return Collections.emptyList();
        }
        return split(getRootField(), propsNode, originalProps);
    }

    @Override
    public void applySnippets(@NotNull String metaKey, ObjectNode propsNode, List<ExtMeta.Snippet> snippets) {
        String rootField = getRootField();
        merge(rootField, propsNode, snippets);
    }
}