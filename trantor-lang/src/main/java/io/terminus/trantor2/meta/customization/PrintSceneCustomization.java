package io.terminus.trantor2.meta.customization;

import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.print.PrintDataSourceType;
import io.terminus.trantor2.print.TemplateEffectiveMechanism;
import lombok.Data;

/**
 * <AUTHOR>
 **/
@Data
public class PrintSceneCustomization implements Customization {
    private String modelKey;
    private PrintDataSourceType dataSourceType;
    private TemplateEffectiveMechanism templateEffectiveMechanism;
    private String serviceKey;
    private Long oidId;

    @Override
    public Customization customize(MetaTreeNode node) {
        return ObjectJsonUtil.MAPPER.convertValue(node.getProps(), this.getClass());
    }
}
