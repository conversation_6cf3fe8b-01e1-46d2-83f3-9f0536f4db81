package io.terminus.trantor2.meta.api.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import lombok.Data;

import java.util.Date;

@Data
public class MetaTreeNodeExt extends MetaTreeNode {
    private static final long serialVersionUID = 1763521742154965727L;
    @Deprecated
    private Long id;
    @Deprecated
    private Long appId;
    @Deprecated
    private Long teamId;
    private String teamCode;
    private Long createdBy;
    private Date createdAt;
    private Long updatedBy;
    private Date updatedAt;
    private Long lockedBy;
    private Long posLeft;
    private Long posRight;
    private String oid;

    private Boolean extended;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private Boolean customExt = false;
}
