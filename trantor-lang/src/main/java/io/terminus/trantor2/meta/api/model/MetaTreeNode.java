package io.terminus.trantor2.meta.api.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.Data;

import java.io.Serializable;

@Data
public class MetaTreeNode implements Serializable {
    private static final long serialVersionUID = 1905303067631554583L;

    private String type;
    private String key;
    private String name;
    private String parentPath;
    private String description;
    private ObjectNode props;
    @Deprecated
    private String parentKey;
    private MetaNodeAccessLevel access;

    @JsonIgnore
    public MetaType getMetaType() {
        return MetaType.valueOf(type);
    }
}
