package io.terminus.trantor2.meta.resource.ext.patch.model;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.auto.service.AutoService;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.resource.ext.patch.AbstractReplaceExtPatch;
import io.terminus.trantor2.model.management.meta.domain.DataStructNodeResourceProps;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@AutoService(ModelPatch.class)
public class ExtFieldPropsPatch extends AbstractReplaceExtPatch implements ModelPatch {
    protected static final String KEY = "key";
    private static final String ENCRYPTED_NODE = "props/encrypted";
    private static final String DESENSITIZED_RULE = "props/desensitizedRule";
    private static final String I18N_ENABLED = "props/i18nEnabled";

    @Override
    public String getRootField() {
        return DataStructNodeResourceProps.Fields.children;
    }

    @Override
    public List<ExtMeta.Snippet> fetchSnippets(@NotNull String metaKey, @NotNull ObjectNode propsNode,
                                               @NotNull ObjectNode originalProps, ExtMeta.Props extProps) {
        if (extProps == null || extProps.getOriginType() != MetaType.Model) {
            return Collections.emptyList();
        }
        String rootPtrExpr = "/" + getRootField();
        JsonNode extFields = propsNode.at(rootPtrExpr);
        JsonNode oriFields = originalProps.at(rootPtrExpr);

        if (extFields == null || oriFields == null) {
            return Collections.emptyList();
        }
        List<ExtMeta.Snippet> res = new ArrayList<>();
        if (extFields.isArray() && oriFields.isArray()) {
            Map<String, ObjectNode> oriMap = new HashMap<>();
            Map<String, ObjectNode> extMap = new HashMap<>();
            oriFields.elements().forEachRemaining(node -> Optional.ofNullable(node.get(KEY))
                    .ifPresent(s -> oriMap.put(s.asText(), (ObjectNode) node)));
            extFields.elements().forEachRemaining(node -> Optional.ofNullable(node.get(KEY))
                    .ifPresent(s -> extMap.put(s.asText(), (ObjectNode) node)));
            extMap.forEach((k, v) -> {
                if (oriMap.containsKey(k)) {
                    List<ExtMeta.Snippet> snippets = new ArrayList<>(3);
                    snippets.addAll(split(ENCRYPTED_NODE, v, oriMap.get(k), null)
                            .stream().filter(Objects::nonNull).peek(it -> it.setNode(ENCRYPTED_NODE)).toList());
                    snippets.addAll(split(DESENSITIZED_RULE, v, oriMap.get(k), null)
                            .stream().filter(Objects::nonNull).peek(it -> it.setNode(DESENSITIZED_RULE)).toList());
                    snippets.addAll(split(I18N_ENABLED, v, oriMap.get(k), null)
                            .stream().filter(Objects::nonNull).peek(it -> it.setNode(I18N_ENABLED)).toList());
                    snippets.forEach(it -> it.setKey(k));
                    res.addAll(snippets);
                }
            });
        }
        return res;
    }

    @Override
    public void applySnippets(@NotNull String metaKey, ObjectNode propsNode, List<ExtMeta.Snippet> snippets) {
        snippets = snippets.stream().filter(Objects::nonNull).filter(
                it -> it.getNode().equals(ENCRYPTED_NODE) || it.getNode().equals(DESENSITIZED_RULE) || it.getNode().equals(I18N_ENABLED)
                ).toList();
        if (propsNode == null || CollectionUtils.isEmpty(snippets)) {
            return;
        }
        String rootPtrExpr = "/" + getRootField();
        ArrayNode fields = (ArrayNode) propsNode.at(rootPtrExpr);
        if (fields == null || fields.isEmpty()) {
            return;
        }
        Map<String, ObjectNode> key2FieldMap = new HashMap<>();
        fields.elements().forEachRemaining(node ->
                Optional.ofNullable(node.get(KEY))
                        .ifPresent(k -> key2FieldMap.computeIfAbsent(k.asText(), k1 -> (ObjectNode) node))
        );
        for (ExtMeta.Snippet snippet : snippets) {
            if (snippet == null || snippet.getRootField() == null) {
                continue;
            }
            ObjectNode fieldNode = key2FieldMap.get(snippet.getKey());
            if (snippet.getNode().equals(ENCRYPTED_NODE)) {
                merge(ENCRYPTED_NODE, fieldNode, snippet);
            } else if (snippet.getNode().equals(DESENSITIZED_RULE)) {
                merge(DESENSITIZED_RULE, fieldNode, snippet);
            } else if (snippet.getNode().equals(I18N_ENABLED)) {
                merge(I18N_ENABLED, fieldNode, snippet);
            }
            fieldNode.set(extProps, createEditExtProps());
        }
    }
}
