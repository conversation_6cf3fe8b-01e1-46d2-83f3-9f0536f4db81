package io.terminus.trantor2.meta.resource.ext.patch.module;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.auto.service.AutoService;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import io.terminus.trantor2.meta.resource.ext.patch.AbstractStringArrayExtPatch;
import io.terminus.trantor2.module.meta.ModuleMeta;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@AutoService(ModulePatch.class)
public class ExtRelatedModuleKeysPatch extends AbstractStringArrayExtPatch implements ModulePatch {
    @Override
    public String getRootField() {
        return ModuleMeta.ModuleProps.Fields.relatedModuleKeys;
    }

    @Override
    public void applySnippets(@NotNull String metaKey, ObjectNode propsNode, List<ExtMeta.Snippet> snippets) {
        String rootField = getRootField();
        merge(rootField, propsNode, snippets);
    }

    @Override
    public List<ExtMeta.Snippet> fetchSnippets(@NotNull String metaKey, @NotNull ObjectNode propsNode, @NotNull ObjectNode originalProps, ExtMeta.Props extProps) {
        if (extProps == null || extProps.getOriginType() != MetaType.Module) {
            return Collections.emptyList();
        }
        return split(getRootField(), propsNode, originalProps);
    }
}
