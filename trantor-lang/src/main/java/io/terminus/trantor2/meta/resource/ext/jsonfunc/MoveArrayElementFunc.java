package io.terminus.trantor2.meta.resource.ext.jsonfunc;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import io.terminus.trantor2.meta.resource.ext.jsonfunc.params.MoveArrayElementParams;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
public class MoveArrayElementFunc extends AbstractJsonFunc<MoveArrayElementParams> implements IEditJsonFunc {
    protected static final String KEY = "key";

    @Override
    public JsonFuncType getType() {
        return JsonFuncType.MOVE_ARRAY_ELEMENT;
    }

    @Override
    public void execute(@NotNull String parentKey, @NotNull String path, Map<String, JsonNode> key2ObjectNodMap) {
        if (StringUtils.isEmpty(parentKey) || StringUtils.isEmpty(path) || StringUtils.isBlank(params.getCur())) {
            return;
        }
        JsonNode parentNode = key2ObjectNodMap.get(parentKey);
        if (parentNode == null) {
            return;
        }
        JsonNode fieldNode = parentNode.get(path);
        if (fieldNode == null || !fieldNode.isArray()) {
            return;
        }

        int fromIndex = -1;
        ArrayNode arrayNode = (ArrayNode) fieldNode;
        for (int i = 0; i < arrayNode.size(); i++) {
            if (params.getCur().equals(arrayNode.get(i).get(KEY).asText())) {
                fromIndex = i;
                break;
            }
        }

        Integer toIndex = params.getIndex(arrayNode, fromIndex);
        if (toIndex == null || toIndex < 0) {
            return;
        }
        if (fromIndex < 0 || toIndex >= arrayNode.size() || fromIndex == toIndex) {
            return;
        }
        arrayNode.insert(toIndex, arrayNode.remove(fromIndex));
    }
}
