package io.terminus.trantor2.rule.engine.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 符号枚举
 * <AUTHOR>
 * @createTime 2023/3/6 7:05 下午
 */
@Getter
@AllArgsConstructor
public enum CharacterEnum {
    LEFT_BRACKETS("("),
    RIGHT_BRACKETS(")"),
    LEFT_PARENTHESIS("["),
    RIGHT_PARENTHESIS("]"),
    POINT("."),
    NON("!"),
    SEPARATOR("#"),
    SPACE(" ");

    private String character;

}
