package io.terminus.trantor2.rule.engine.api.message;

/**
 * 规则(RuleEngine) Message
 *
 * <AUTHOR>
 * @since 2023-02-08 13:59:52
 */
public interface RuleEngineMsg {

    /**
     * 规则为空
     */
    String RULE_ENGINE_IS_NULL = "rule.engine.is.null";

    /**
     * 主键为空
     */
    String RULE_ENGINE_ID_IS_NULL = "rule.engine.id.is.null";

    /**
     * 场景key为空
     */
    String RULE_ENGINE_SCENE_KEY_IS_NULL = "rule.engine.scene.key.is.null";

    /**
     * 优先级。值越小优先级越高为空
     */
    String RULE_ENGINE_PRIORITY_IS_NULL = "rule.engine.priority.is.null";

    /**
     * 业务规则id为空
     */
    String RULE_ENGINE_BUSINESS_RULE_ID_IS_NULL = "rule.engine.business.rule.id.is.null";

    /**
     * 条件表达式。为空
     */
    String RULE_ENGINE_CONDITION_EXPRESS_IS_NULL = "rule.engine.condition.express.is.null";

    /**
     * 是否需要动作执行，默认否。HAS_ACTION: 是、NO_ACTION: 否为空
     */
    String RULE_ENGINE_HAS_ACTION_IS_NULL = "rule.engine.has.action.is.null";

    /**
     * 动作执行表达式为空
     */
    String RULE_ENGINE_ACTION_EXPRESS_IS_NULL = "rule.engine.action.express.is.null";

    /**
     * 规则引擎服务异常
     */
    String RULE_ENGINE_INTERNAL_SERVER_ERROR = "rule.engine.internal.server.error";

    /**
     * 规则不存在
     */
    String RULE_ENGINE_NOT_EXIST = "rule.engine.not.exist";;

    /**
     * 条件为空
     */
    String RULE_ENGINE_CONDITION_IS_NULL = "rule.engine.condition.is.null";

}

