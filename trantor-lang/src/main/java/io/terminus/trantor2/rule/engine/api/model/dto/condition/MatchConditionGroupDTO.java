package io.terminus.trantor2.rule.engine.api.model.dto.condition;

import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.rule.engine.api.enums.FieldTypeEnum;
import io.terminus.trantor2.rule.engine.api.enums.LogicOperatorEnum;
import io.terminus.trantor2.rule.engine.api.enums.OperatorEnum;
import io.terminus.trantor2.rule.engine.api.enums.ValueTypeEnum;
import io.terminus.trantor2.rule.engine.api.message.RuleEngineMsg;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 条件组
 * <AUTHOR>
 * @createTime 2023/5/22 5:12 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MatchConditionGroupDTO implements Serializable {

    private static final long serialVersionUID = -3159651250396527617L;

    /**
     * 规则id
     */
    private Long id;

    /**
     * 场景key
     */
    private String sceneKey;

    /**
     * 逻辑运算符
     */
    private LogicOperatorEnum logicOperator;

    /**
     * 条件
     */
    private List<MatchWholeConditionDTO> conditions;

    public void checkParam() {
        Assert.notNull(this.sceneKey, RuleEngineMsg.RULE_ENGINE_SCENE_KEY_IS_NULL);
        Assert.notEmpty(this.conditions, RuleEngineMsg.RULE_ENGINE_CONDITION_IS_NULL);
    }

    public static void main(String[] args) {
        MatchConditionGroupDTO groupDTO = new MatchConditionGroupDTO();
        groupDTO.setSceneKey("test");
        groupDTO.setLogicOperator(LogicOperatorEnum.AND);

        List<MatchWholeConditionDTO> conditions = new ArrayList<>();
        MatchWholeConditionDTO c_1 = new MatchWholeConditionDTO();
        c_1.setLogicOperator(LogicOperatorEnum.DATA);
        MatchConditionDTO matchConditionDTO_1 = new MatchConditionDTO();
        LeftValueDTO left_1 = new LeftValueDTO();
        left_1.setValueType(ValueTypeEnum.MODEL.getValueType());
        ModelValueDTO model_1 = new ModelValueDTO();
        model_1.setModelKey("sls_so_head_tr");
        model_1.setModelAlias("slsSoHeadTr");
        model_1.setModelName("销售订单抬头");
        model_1.setFieldKey("id");
        model_1.setFieldAlias("id");
        model_1.setFieldName("主键");
        model_1.setFieldType(FieldTypeEnum.NUMBER.getFieldType());
        model_1.setIsList(false);
        model_1.setHasChild(false);
        left_1.setModel(model_1);
        matchConditionDTO_1.setLeft(left_1);
        matchConditionDTO_1.setOperator(OperatorEnum.EQ.getOperatorCode());
        RightValueDTO right_1 = new RightValueDTO();
        right_1.setValueType(ValueTypeEnum.CONSTANT.getValueType());
        right_1.setFieldType(FieldTypeEnum.NUMBER.getFieldType());
        right_1.setValue("1");
        matchConditionDTO_1.setRights(Arrays.asList(right_1));
        c_1.setCondition(matchConditionDTO_1);
        conditions.add(c_1);
        MatchWholeConditionDTO c_2 = new MatchWholeConditionDTO();
        c_2.setLogicOperator(LogicOperatorEnum.DATA);
        MatchConditionDTO matchConditionDTO_2 = new MatchConditionDTO();
        LeftValueDTO left_2 = new LeftValueDTO();
        left_2.setValueType(ValueTypeEnum.FACTOR.getValueType());
        FactorValueDTO factorValueDTO = new FactorValueDTO();
        factorValueDTO.setFactorName("当前登录用户");
        factorValueDTO.setFactorKey("USER");
        factorValueDTO.setFieldType(FieldTypeEnum.NUMBER.getFieldType());
        left_2.setFactor(factorValueDTO);
        matchConditionDTO_2.setLeft(left_2);
        matchConditionDTO_2.setOperator(OperatorEnum.IN.getOperatorCode());
        RightValueDTO right_2 = new RightValueDTO();
        right_2.setValueType(ValueTypeEnum.CONSTANT.getValueType());
        right_2.setFieldType(FieldTypeEnum.NUMBER.getFieldType());
        right_2.setValue("1");
        RightValueDTO right_3 = new RightValueDTO();
        right_3.setValueType(ValueTypeEnum.CONSTANT.getValueType());
        right_3.setFieldType(FieldTypeEnum.NUMBER.getFieldType());
        right_3.setValue("2");
        matchConditionDTO_2.setRights(Arrays.asList(right_2, right_3));
        c_2.setCondition(matchConditionDTO_2);
        conditions.add(c_2);

        MatchWholeConditionDTO c_2_1 = new MatchWholeConditionDTO();
        c_2_1.setLogicOperator(LogicOperatorEnum.OR);
        List<MatchWholeConditionDTO> conditions_2 = new ArrayList<>();
        MatchWholeConditionDTO c_2_1_1 = new MatchWholeConditionDTO();
        c_2_1_1.setLogicOperator(LogicOperatorEnum.DATA);
        MatchConditionDTO matchConditionDTO_2_1 = new MatchConditionDTO();
        c_2_1_1.setCondition(matchConditionDTO_2_1);
        conditions_2.add(c_2_1_1);

        MatchWholeConditionDTO c_2_1_2 = new MatchWholeConditionDTO();
        c_2_1_2.setLogicOperator(LogicOperatorEnum.DATA);
        MatchConditionDTO matchConditionDTO_2_2 = new MatchConditionDTO();
        c_2_1_2.setCondition(matchConditionDTO_2_2);
        conditions_2.add(c_2_1_2);

        MatchWholeConditionDTO c_2_1_2_1 = new MatchWholeConditionDTO();
        c_2_1_2_1.setLogicOperator(LogicOperatorEnum.OR);
        List<MatchWholeConditionDTO> conditions_2_1 = new ArrayList<>();
        MatchWholeConditionDTO c_2_1_2_2 = new MatchWholeConditionDTO();
        c_2_1_2_2.setLogicOperator(LogicOperatorEnum.DATA);
        MatchConditionDTO matchConditionDTO_2_2_1 = new MatchConditionDTO();
        c_2_1_2_2.setCondition(matchConditionDTO_2_2_1);
        conditions_2_1.add(c_2_1_2_2);

        MatchWholeConditionDTO c_2_1_2_3 = new MatchWholeConditionDTO();
        c_2_1_2_3.setLogicOperator(LogicOperatorEnum.DATA);
        MatchConditionDTO matchConditionDTO_2_2_2 = new MatchConditionDTO();
        c_2_1_2_3.setCondition(matchConditionDTO_2_2_2);
        conditions_2_1.add(c_2_1_2_3);

        c_2_1_2_1.setConditions(conditions_2_1);



        c_2_1.setConditions(conditions_2);
        conditions.add(c_2_1);

        groupDTO.setConditions(conditions);

        System.out.println(ObjectJsonUtil.serialize(ObjectJsonUtil.MAPPER.valueToTree(groupDTO)));
    }

}

