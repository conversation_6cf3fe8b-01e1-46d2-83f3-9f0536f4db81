package io.terminus.trantor2.rule.engine.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 值类型枚举
 * <AUTHOR>
 * @createTime 2023/3/24 5:48 下午
 */
@Getter
@AllArgsConstructor
public enum ValueTypeEnum {
    CONSTANT("CONSTANT", "固定值"),
    MODEL("MODEL", "模型"),
    FACTOR("FACTOR", "因子"),
    FUNCTION("FUNCTION", "函数");

    /**
     * 值类型
     */
    private String valueType;

    /**
     * 描述
     */
    private String desc;

}
