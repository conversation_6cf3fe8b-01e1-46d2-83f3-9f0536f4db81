//package io.terminus.trantor2.rule.engine.api.model.dto.condition;
//
//import io.terminus.trantor2.rule.engine.api.message.RuleEngineMsg;
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//import org.springframework.util.Assert;
//
//import java.io.Serializable;
//import java.util.List;
//
///**
// * 条件组
// * <AUTHOR>
// * @createTime 2023/3/22 2:28 下午
// */
//@Data
//@NoArgsConstructor
//@AllArgsConstructor
//public class ConditionGroupDTO implements Serializable {
//    private static final long serialVersionUID = 4907274689967022796L;
//
//    /**
//     * 规则id
//     */
//    private Long id;
//
//    /**
//     * 场景key
//     */
//    private String sceneKey;
//
//    /**
//     * 条件
//     */
//    private List<List<ConditionDTO>> conditions;
//
//    public void checkParam() {
//        Assert.notNull(this.sceneKey, RuleEngineMsg.RULE_ENGINE_SCENE_KEY_IS_NULL);
//        Assert.notEmpty(this.conditions, RuleEngineMsg.RULE_ENGINE_CONDITION_IS_NULL);
//    }
//
//}
//
