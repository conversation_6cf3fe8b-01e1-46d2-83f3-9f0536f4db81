package io.terminus.trantor2.doc.api.props;

import io.terminus.trantor2.rule.engine.api.model.dto.condition.ConditionDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class EventActionRelProps {

    private String nextCode;

    private Long conditionId;

//    private String conditionExpress; // TODO: add back when we starting to unify PO and DTO

    private List<List<ConditionDTO>> conditions;

    private String actionType;

    private String sourceCode;

    private String express;

    private String convert;

    private String code;

    private String desc;

    private Boolean enabledParamCheck;

    private Boolean enabledTransaction;
}
