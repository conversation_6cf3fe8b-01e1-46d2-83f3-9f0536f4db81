package io.terminus.trantor2.doc.api.dto;

import io.terminus.trantor2.doc.api.node.EventActionRelNode;
import io.terminus.trantor2.doc.api.node.EventActionRelStateNode;
import io.terminus.trantor2.rule.engine.api.model.dto.condition.ConditionDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class FlowViewDTO implements Serializable{

    private List<Node> nodes;
    private List<Lane> lanes;
    private List<Edge> edges;

    @Data
    public static class Node implements Serializable {
        private String key;
        private String label;
        private Position position;
        private String laneKey;
        private Event props;
        private List<Edge> edges;
    }

    @Data
    public static class Position implements Serializable {
        private Integer x;
        private Integer y;

        public Position() {}

        public Position(Integer x, Integer y) {
            this.x = x;
            this.y = y;
        }
    }

    @Data
    public static class Lane implements Serializable {
        private String key;
        private String label;
    }

    @Data
    public static class Edge implements Serializable {
        private String key;
        private List<EdgeLabel> labels;
        private String source;
        private String target;
    }

    @Data
    public static class EdgeLabel implements Serializable {
        private String label;
        private List<List<ConditionDTO>> conditions;
    }

    @Data
    public static class Event implements Serializable {
        private String code;
        private String name;
        private String modelKey;
        private String module;
        private String moduleName;
        private List<EventActionRelNode> actions;
        private List<EventActionRelStateNode> states;
    }
}
