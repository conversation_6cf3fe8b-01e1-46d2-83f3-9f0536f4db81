package io.terminus.trantor2.doc.api.node;

import io.terminus.trantor.workflow.runtime.v2.model.dto.WorkflowGroupProps;
import io.terminus.trantor2.doc.api.dto.EventModelDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.doc.api.props.ActionProps;
import io.terminus.trantor2.doc.api.props.EventProps;
import io.terminus.trantor2.rule.engine.api.model.dto.condition.ConditionDTO;
import io.terminus.trantor2.service.common.meta.ServiceProps;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "Event Action Rel 关系")
public class EventActionRelNode implements Serializable {

    private static final long serialVersionUID = 7106224918102911245L;

    private Long teamId;

    private Long appId;

    @Schema(description = "所属模块Code")
    private String module;

    @Schema(description = "所属模块名称")
    private String moduleName;

    @Schema(description = "action类型")
    private String actionType;

    @Schema(description = "入参来源编码", nullable = true)
    private String sourceCode;

    @Schema(description = "取值表达式", nullable = true)
    private String express;

    @Schema(description = "转换对象脚本", nullable = true)
    private String convert;

    @Schema(description = "编码", nullable = true)
    private String code;

    @Schema(description = "名称", nullable = true)
    private String name;

    @Schema(description = "返回模型", nullable = true)
    private EventModelDTO returnModel;

    @Schema(description = "条件组", nullable = true)
    private List<List<ConditionDTO>> conditions;

    @Schema(description = "无入参是否报错", nullable = true)
    private Boolean enabledParamCheck;

    @Schema(description = "是否开启事务")
    private Boolean enabledTransaction;

    private ActionProps actionProps;

    private EventProps eventProps;

    private ServiceProps serviceProps;

    private WorkflowGroupProps workflowGroupProps;

}
