package io.terminus.trantor2.doc.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.meta.api.model.MetaNodeAccessLevel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "业务流")
public class FlowDTO implements Serializable {

    private static final long serialVersionUID = 749411324081330741L;

    @Schema(description = "flow id")
    private Long id;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "编码")
    private String key;

    @Schema(description = "描述")
    private String desc;

    @Schema(description = "主体模型")
    private String modelAlias;

    @Schema(description = "主体模型名称")
    private String modelName;

    @Schema(description = "所属模块Code")
    private String module;

    @Schema(description = "所属模块名称")
    private String moduleName;

    @Schema(description = "事件集合")
    private List<EventDTO> events;

    @Schema(description = "公开/私有")
    private MetaNodeAccessLevel access;
}
