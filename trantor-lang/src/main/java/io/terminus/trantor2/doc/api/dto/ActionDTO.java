package io.terminus.trantor2.doc.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.meta.api.model.MetaNodeAccessLevel;
import io.terminus.trantor2.service.dsl.properties.Field;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "Action")
public class ActionDTO implements Serializable {

    private static final long serialVersionUID = 245012325114549421L;

    @Schema(description = "action id")
    private Long id;

    private String bean;

    private String method;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "描述")
    private String desc;

    @Schema(description = "默认顺序")
    private Integer order;

    @Schema(description = "入参类型")
    private String requestType;

    @Schema(description = "出参类型")
    private String responseType;

    @Schema(description = "所属目录标识")
    private String module;

    @Schema(description = "语言类型")
    private String languageType;

    @Schema(description = "groovy脚本")
    private String groovyScript;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "返回模型")
    private EventModelDTO returnModel;

    @Schema(description = "返回是否为数组")
    private Boolean returnList;

    @Schema(description = "公开/私有")
    private MetaNodeAccessLevel access;

    @Schema(description = "入参模型Key")
    private String requestModelKey;

    @Schema(description = "入参的结构")
    private List<ActionFieldDTO> input;

    @Schema(description = "返回值的结构")
    private List<ActionFieldDTO> output;

    private String javaCode;

    private String actionPackage;

    private Date updatedAt;

    private Boolean extended;
    private Boolean customExt;
}
