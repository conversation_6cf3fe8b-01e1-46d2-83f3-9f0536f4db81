package io.terminus.trantor2.doc.api.dto;

import io.terminus.trantor2.meta.api.model.MetaNodeAccessLevel;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PermissionDTO {

    private String modelKey;

    private String modelName;

    private String functionPermissionKey;

    private String dataPermissionKey;

    /**
     * 系统服务访问级别
     */
    private MetaNodeAccessLevel accessLevel;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean extended;

}
