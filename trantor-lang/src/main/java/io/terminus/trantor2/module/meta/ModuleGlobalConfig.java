package io.terminus.trantor2.module.meta;

import lombok.Data;

/**
 * <AUTHOR>
 **/
@Data
public class ModuleGlobalConfig {
    /**
     * 模块级别的多组织字段全局开关，若开启，建模时展示是否开启多组织的选项；若关闭，建模时不展示。
     */
    private Boolean modelFieldShowEnable;
    private Boolean modelFieldSelected;

    /**
     * 模块级别的多租户字段全局开关，若开启，建模时展示是否开启多租户的选项；若关闭，建模时不展示。
     */
    private Boolean showTenantIdSwitch;
    /**
     * 模块级别的多租户字段全局开关开始时，建模时默认是否开启多租户。
     */
    private Boolean defaultTenantIdEnabled;
}
