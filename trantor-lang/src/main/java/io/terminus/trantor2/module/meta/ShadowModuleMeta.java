package io.terminus.trantor2.module.meta;

import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ext.ExtMeta;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: ya<PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/10/16 11:47 AM
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ShadowModuleMeta extends ExtMeta {

    private static final long serialVersionUID = 3389034418309029580L;

    @Override
    public MetaType getMetaType() {
        return MetaType.ExtModule;
    }

    public static class Props extends ExtMeta.Props {
        @Override
        public MetaType getOriginType() {
            return MetaType.Module;
        }
    }

}
