package io.terminus.trantor2.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateExceptionHandler;
import io.terminus.trantor2.meta.resource.ext.ExtStep;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import lombok.SneakyThrows;

import jakarta.annotation.Nonnull;
import java.io.StringWriter;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

/**
 * <AUTHOR>
 */
public class TemplateUtils {
    public static final String KEY = "key";
    public static Configuration cfg = new Configuration(Configuration.DEFAULT_INCOMPATIBLE_IMPROVEMENTS);

    static {
        cfg.setClassForTemplateLoading(TemplateUtils.class, "/templates");
        cfg.setDefaultEncoding("UTF-8");
        cfg.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
        cfg.setLogTemplateExceptions(false);
        cfg.setWrapUncheckedExceptions(true);
        cfg.setFallbackOnNullLoopVariable(false);
        cfg.setTemplateUpdateDelayMilliseconds(24 * 60 * 60 * 1000);
        cfg.setSQLDateAndTimeTimeZone(TimeZone.getDefault());
    }

    @SneakyThrows
    public static void addExtSteps(@Nonnull String templateName, Map<String, Object> dataModel, List<ExtStep> extSteps) {
        Template template = getTemplate(templateName);
        if (template != null) {
            extSteps.addAll(buildExtStepsByTemplate(dataModel, template));
        }
    }


    @SneakyThrows
    public static Template getTemplate(String templateName) {
        return cfg.getTemplate(templateName + ".ftl");
    }

    @SneakyThrows
    private static List<ExtStep> buildExtStepsByTemplate(Map<String, Object> dataModel, Template template) {
        StringWriter out = new StringWriter();
        template.process(dataModel, out);
        return ObjectJsonUtil.MAPPER.readValue(out.toString(), new TypeReference<List<ExtStep>>() {
        });
    }

}
