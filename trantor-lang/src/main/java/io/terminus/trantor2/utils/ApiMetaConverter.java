package io.terminus.trantor2.utils;

import io.terminus.iam.api.dto.permission.FunctionPermissionAssignProps;
import io.terminus.iam.api.enums.permission.AuthorizationEffect;
import io.terminus.iam.api.enums.permission.v2.PermissionType;
import io.terminus.iam.api.request.permission.v2.PermissionAssignCreateParams;
import io.terminus.iam.api.request.permission.v2.PermissionResourceCreateParams;
import io.terminus.iam.api.request.permission.v2.PermissionResourceUpdateParams;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.permission.ApiMeta;
import io.terminus.trantor2.permission.props.AIAgentProps;
import io.terminus.trantor2.permission.props.OpenAIAgentProps;
import io.terminus.trantor2.permission.props.OpenApiProps;
import io.terminus.trantor2.permission.props.OpenServiceProps;
import io.terminus.trantor2.permission.props.ServiceProps;
import io.terminus.trantor2.permission.props.SystemServiceProps;
import io.terminus.trantor2.permission.props.WebApiProps;
import io.terminus.trantor2.permission.props.access.control.AIAgentAccessControl;
import io.terminus.trantor2.permission.props.access.control.ApiAccessControl;
import io.terminus.trantor2.permission.props.access.control.ApiType;
import io.terminus.trantor2.permission.props.access.control.OpenAIAgentAccessControl;
import io.terminus.trantor2.permission.props.access.control.OpenApiAccessControl;
import io.terminus.trantor2.permission.props.access.control.OpenServiceAccessControl;
import io.terminus.trantor2.permission.props.access.control.ResourceAccessControl;
import io.terminus.trantor2.permission.props.access.control.ServiceAccessControl;
import io.terminus.trantor2.permission.props.access.control.SysServiceAccessControl;
import jakarta.validation.constraints.NotNull;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.http.HttpMethod;

import java.util.function.Consumer;

/**
 * <AUTHOR>
 * 2024/7/29 14:15
 **/
@Mapper
public abstract class ApiMetaConverter {

    public static final ApiMetaConverter INSTANCE = Mappers.getMapper(ApiMetaConverter.class);

    // OpenApi资源名称前缀
    private static final String OPEN_API_PREFIX = "OpenAPI:";

    /**
     * HttpMethod转String的映射方法
     */
    protected String map(HttpMethod method) {
        return method == null ? null : method.name();
    }

    /**
     * String转HttpMethod的映射方法
     */
    protected HttpMethod map(String method) {
        return method == null ? null : HttpMethod.valueOf(method);
    }

    @Mapping(target = "apiType", constant = "SystemService")
    public abstract SystemServiceProps convert(SysServiceAccessControl accessControl);

    @Mapping(target = "apiType", constant = "Service")
    public abstract ServiceProps convert(ServiceAccessControl accessControl);

    @Mapping(target = "apiType", constant = "Api")
    public abstract WebApiProps convert(ApiAccessControl accessControl);

    @Mapping(target = "apiType", constant = "OpenService")
    public abstract OpenServiceProps convertToOpenServiceProps(ServiceAccessControl accessControl);

    @Mapping(target = "apiType", constant = "OpenApi")
    public abstract OpenApiProps convertToOpenApiProps(ApiAccessControl accessControl);

    @Mapping(target = "apiType", constant = "OpenService")
    public abstract OpenServiceProps convert(OpenServiceAccessControl accessControl);

    @Mapping(target = "apiType", constant = "OpenApi")
    public abstract OpenApiProps convert(OpenApiAccessControl accessControl);

    @Mapping(target = "apiType", constant = "OpenAIAgent")
    public abstract OpenAIAgentProps convert(OpenAIAgentAccessControl accessControl);

    @Mapping(target = "apiType", constant = "AIAgent")
    public abstract AIAgentProps convert(AIAgentAccessControl accessControl);

    public ApiMeta convert(ResourceAccessControl accessControl, Consumer<ApiMeta> consumer) {
        if (ApiType.SystemService.equals(accessControl.getApiType())) {
            SysServiceAccessControl sysServiceAccessControl = (SysServiceAccessControl) accessControl;
            return convert(sysServiceAccessControl, consumer);
        }
        if (ApiType.Service.equals(accessControl.getApiType())) {
            if (Boolean.TRUE.equals(accessControl.getOpenApi())) {
                // 兼容前端传参
                return convertToOpenServiceMeta((ServiceAccessControl) accessControl, consumer);
            } else {
                return convert((ServiceAccessControl) accessControl, consumer);
            }
        }
        if (ApiType.Api.equals(accessControl.getApiType())) {
            if (Boolean.TRUE.equals(accessControl.getOpenApi())) {
                // 兼容前端传参
                return convertToOpenApiMeta((ApiAccessControl) accessControl, consumer);
            } else {
                return convert((ApiAccessControl) accessControl, consumer);
            }
        }
        if (ApiType.OpenService.equals(accessControl.getApiType())) {
            return convert((OpenServiceAccessControl) accessControl, consumer);
        }
        if (ApiType.OpenApi.equals(accessControl.getApiType())) {
            return convert((OpenApiAccessControl) accessControl, consumer);
        }
        if (ApiType.OpenAIAgent.equals(accessControl.getApiType())) {
            return convert((OpenAIAgentAccessControl) accessControl, consumer);
        }
        if (ApiType.AIAgent.equals(accessControl.getApiType())) {
            return convert((AIAgentAccessControl) accessControl, consumer);
        }

        // Never happen
        throw new TrantorRuntimeException("Illegal apiType: " + accessControl.getApiType());
    }

    private ApiMeta convert(SysServiceAccessControl accessControl, Consumer<ApiMeta> consumer) {
        ApiMeta meta = new ApiMeta();
        meta.setName(accessControl.getResourceName());
        meta.setDescription(accessControl.getResourceDesc());
        meta.setResourceProps(convert(accessControl));
        consumer.accept(meta);
        return meta;
    }

    private ApiMeta convert(ServiceAccessControl accessControl, Consumer<ApiMeta> consumer) {
        ApiMeta meta = new ApiMeta();
        meta.setName(accessControl.getResourceName());
        meta.setDescription(accessControl.getResourceDesc());
        meta.setResourceProps(convert(accessControl));
        consumer.accept(meta);
        return meta;
    }

    private ApiMeta convert(ApiAccessControl accessControl, Consumer<ApiMeta> consumer) {
        ApiMeta meta = new ApiMeta();
        meta.setName(accessControl.getResourceName());
        meta.setDescription(accessControl.getResourceDesc());
        meta.setResourceProps(convert(accessControl));
        consumer.accept(meta);
        return meta;
    }

    private ApiMeta convertToOpenServiceMeta(ServiceAccessControl accessControl, Consumer<ApiMeta> consumer) {
        ApiMeta meta = new ApiMeta();
        meta.setName(OPEN_API_PREFIX + accessControl.getResourceName());
        meta.setDescription(accessControl.getResourceDesc());
        meta.setResourceProps(convertToOpenServiceProps(accessControl));
        consumer.accept(meta);
        return meta;
    }

    private ApiMeta convertToOpenApiMeta(ApiAccessControl accessControl, Consumer<ApiMeta> consumer) {
        ApiMeta meta = new ApiMeta();
        meta.setName(OPEN_API_PREFIX + accessControl.getResourceName());
        meta.setDescription(accessControl.getResourceDesc());
        meta.setResourceProps(convertToOpenApiProps(accessControl));
        consumer.accept(meta);
        return meta;
    }

    private ApiMeta convert(OpenServiceAccessControl accessControl, Consumer<ApiMeta> consumer) {
        ApiMeta meta = new ApiMeta();
        meta.setName(OPEN_API_PREFIX + accessControl.getResourceName());
        meta.setDescription(accessControl.getResourceDesc());
        meta.setResourceProps(convert(accessControl));
        consumer.accept(meta);
        return meta;
    }

    private ApiMeta convert(OpenApiAccessControl accessControl, Consumer<ApiMeta> consumer) {
        ApiMeta meta = new ApiMeta();
        meta.setName(OPEN_API_PREFIX + accessControl.getResourceName());
        meta.setDescription(accessControl.getResourceDesc());
        meta.setResourceProps(convert(accessControl));
        consumer.accept(meta);
        return meta;
    }

    private ApiMeta convert(OpenAIAgentAccessControl accessControl, Consumer<ApiMeta> consumer) {
        ApiMeta meta = new ApiMeta();
        meta.setName(OPEN_API_PREFIX + accessControl.getResourceName());
        meta.setDescription(accessControl.getResourceDesc());
        meta.setResourceProps(convert(accessControl));
        consumer.accept(meta);
        return meta;
    }

    private ApiMeta convert(AIAgentAccessControl accessControl, Consumer<ApiMeta> consumer) {
        ApiMeta meta = new ApiMeta();
        meta.setName(accessControl.getResourceName());
        meta.setDescription(accessControl.getResourceDesc());
        meta.setResourceProps(convert(accessControl));
        consumer.accept(meta);
        return meta;
    }

    public ResourceAccessControl convert(@NotNull ApiMeta apiMeta) {
        ApiType apiType = apiMeta.getResourceProps().getApiType();
        if (ApiType.SystemService.equals(apiType)) {
            SysServiceAccessControl sysServiceResource = new SysServiceAccessControl();
            SystemServiceProps props = (SystemServiceProps) apiMeta.getResourceProps();
            sysServiceResource.setResourceKey(props.getServiceKey());
            sysServiceResource.setModelKey(props.getModelKey());
            convertResourceCommonProps(apiMeta, sysServiceResource);
            return sysServiceResource;
        } else if (ApiType.Service.equals(apiType)) {
            ServiceAccessControl serviceResource = new ServiceAccessControl();
            ServiceProps props = (ServiceProps) apiMeta.getResourceProps();
            serviceResource.setResourceKey(props.getServiceKey());
            convertResourceCommonProps(apiMeta, serviceResource);
            return serviceResource;
        } else if (ApiType.Api.equals(apiType)) {
            ApiAccessControl apiResource = new ApiAccessControl();
            WebApiProps props = (WebApiProps) apiMeta.getResourceProps();
            apiResource.setResourceKey(props.getHttpPath());
            apiResource.setHttpMethod(HttpMethod.valueOf(props.getHttpMethod()));
            convertResourceCommonProps(apiMeta, apiResource);
            apiResource.setPermissionKey(props.getPermissionKey());
            return apiResource;
        } else if (ApiType.OpenService.equals(apiType)) {
            ServiceAccessControl serviceResource = new ServiceAccessControl();
            OpenServiceProps props = (OpenServiceProps) apiMeta.getResourceProps();
            serviceResource.setResourceKey(props.getServiceKey());
            convertResourceCommonProps(apiMeta, serviceResource);
            return serviceResource;
        } else if (ApiType.OpenApi.equals(apiType)) {
            ApiAccessControl apiResource = new ApiAccessControl();
            OpenApiProps props = (OpenApiProps) apiMeta.getResourceProps();
            apiResource.setResourceKey(props.getHttpPath());
            apiResource.setHttpMethod(HttpMethod.valueOf(props.getHttpMethod()));
            convertResourceCommonProps(apiMeta, apiResource);
            apiResource.setPermissionKey(props.getPermissionKey());
            return apiResource;
        } else if (ApiType.OpenAIAgent.equals(apiType)) {
            OpenAIAgentAccessControl agentResource = new OpenAIAgentAccessControl();
            OpenAIAgentProps props = (OpenAIAgentProps) apiMeta.getResourceProps();
            agentResource.setResourceKey(props.getAgentKey());
            convertResourceCommonProps(apiMeta, agentResource);
            return agentResource;
        } else if (ApiType.AIAgent.equals(apiType)) {
            AIAgentAccessControl agentResource = new AIAgentAccessControl();
            AIAgentProps props = (AIAgentProps) apiMeta.getResourceProps();
            agentResource.setResourceKey(props.getAgentKey());
            convertResourceCommonProps(apiMeta, agentResource);
            return agentResource;
        } else {
            // never happened
            throw new TrantorRuntimeException("Invalid apiType: " + apiType);
        }
    }

    public PermissionResourceCreateParams convertToPermissionResourceCreateParams(ApiMeta apiMeta,
                                                                                  Consumer<PermissionResourceCreateParams> consumer) {
        PermissionResourceCreateParams params = new PermissionResourceCreateParams();
        params.setType(apiMeta.getResourceProps().getApiType().name());
        params.setKey(apiMeta.getResourceProps().getResourceKey());
        params.setName(apiMeta.getName());
        params.setDescription(apiMeta.getDescription());
        consumer.accept(params);
        return params;
    }

    public PermissionResourceUpdateParams convertToPermissionResourceUpdateParams(ApiMeta apiMeta,
                                                                                  Consumer<PermissionResourceUpdateParams> consumer) {
        PermissionResourceUpdateParams params = new PermissionResourceUpdateParams();
        params.setType(apiMeta.getResourceProps().getApiType().name());
        params.setKey(apiMeta.getResourceProps().getResourceKey());
        params.setName(apiMeta.getName());
        params.setDescription(apiMeta.getDescription());
        consumer.accept(params);
        return params;
    }

    public PermissionAssignCreateParams convertToPermissionAssignCreateParams(ApiMeta apiMeta,
                                                                              Consumer<PermissionAssignCreateParams> consumer) {
        PermissionAssignCreateParams params = new PermissionAssignCreateParams();
        params.setPermissionType(PermissionType.FUNCTION_PERMISSION);
        params.setEffect(AuthorizationEffect.ALLOW);
        FunctionPermissionAssignProps functionPermissionProps = new FunctionPermissionAssignProps();
        functionPermissionProps.setAuthenticationEnabled(apiMeta.getResourceProps().getAuthenticationEnabled());
        functionPermissionProps.setAuthorizationEvaluateEnabled(apiMeta.getResourceProps().getAuthorizationEvaluateEnabled());
        functionPermissionProps.setOpenApi(apiMeta.getResourceProps().getOpenApi());
        params.setProps(functionPermissionProps);
        consumer.accept(params);
        return params;
    }

    private void convertResourceCommonProps(ApiMeta apiMeta, ResourceAccessControl accessControl) {
        accessControl.setResourceId(apiMeta.getKey());
        accessControl.setResourceType(apiMeta.getResourceProps().getApiType().name());
        accessControl.setResourceName(apiMeta.getName());
        accessControl.setResourceDesc(apiMeta.getDescription());
        accessControl.setAuthenticationEnabled(Boolean.TRUE.equals(apiMeta.getResourceProps().getAuthenticationEnabled()));
        accessControl.setAuthorizationEvaluateEnabled(Boolean.TRUE.equals(apiMeta.getResourceProps().getAuthorizationEvaluateEnabled()));
        accessControl.setOpenApi(Boolean.TRUE.equals(apiMeta.getResourceProps().getOpenApi()));
    }
}
