package io.terminus.trantor2.model.management.meta.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.BaseMeta;
import io.terminus.trantor2.meta.resource.ext.ExtModuleBaseMeta;
import io.terminus.trantor2.meta.resource.ext.patch.ExtPropsPatch;
import io.terminus.trantor2.meta.resource.ext.patch.model.ModelPatch;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.model.management.meta.consts.DataStructType;
import io.terminus.trantor2.model.management.meta.domain.search.SearchModelConfigMeta;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.ServiceLoader;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/22
 */
@Setter
@Getter
@SuperBuilder
@ToString
@NoArgsConstructor
@Schema(description = "模型结构定义")
@FieldNameConstants
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DataStructNode extends ExtModuleBaseMeta<DataStructNodeResourceProps> implements UserInfo {
    private static final int DEFAULT_ALLOCATION_SIZE = 10;
    private static final long serialVersionUID = -417569402244443381L;

    /**
     * 创建人名称
     */
    private String createdByName;

    /**
     * 更新人名称
     */
    private String updatedByName;

    @JsonIgnore
    private DataStructNodeResourceProps resourceProps = new DataStructNodeResourceProps();

    @Override
    public DataStructNodeResourceProps getResourceProps() {
        return resourceProps;
    }

    @Override
    public void setResourceProps(DataStructNodeResourceProps resourceProps) {
        this.resourceProps = resourceProps;
    }

    @Override
    protected List<ExtPropsPatch> getPatches() {
        List<ExtPropsPatch> patches = new ArrayList<>();
        ServiceLoader<ModelPatch> services = ServiceLoader.load(ModelPatch.class);
        services.forEach(patches::add);
        return patches;
    }

    @Override
    public void from(MetaTreeNodeExt node) {
        super.from(node);
        // super.from will set teamId and appId
        if (resourceProps != null && resourceProps.getProps() != null && resourceProps.getProps().getSearchModelConfigMeta() != null) {
            SearchModelConfigMeta searchModelConfigMeta = resourceProps.getProps().getSearchModelConfigMeta();
            searchModelConfigMeta.setAlias(searchModelConfigMeta.generateAlias(super.getTeamCode(), getAlias()));
        }
        // fix relation meta current model alias (redundant, but keep compatible)
        if (resourceProps != null && !CollectionUtils.isEmpty(resourceProps.getChildren())) {
            resourceProps.getChildren().forEach(child -> {
                if (child != null && child.getProps() != null && child.getProps().getRelationMeta() != null) {
                    child.getProps().getRelationMeta().setCurrentModelAlias(getKey());
                    child.getProps().getRelationMeta().setRelationModelKey(child.getProps().getRelationMeta().getRelationModelAlias());
                }
            });
        }
        // TODO: we may add new ability to let user set tableName that not based on meta-key
        if (resourceProps != null && resourceProps.getProps() != null && resourceProps.getProps().getType() == DataStructType.PERSIST) {
            resourceProps.getProps().setTableName(KeyUtil.shortKey(getKey()));
        }
    }

    public static DataStructNode newInstanceFrom(MetaTreeNodeExt node) {
        return BaseMeta.newInstanceFrom(node, DataStructNode.class);
    }

    @JsonProperty("props")
    public DataStructProperties getProps() {
        return getResourceProps().getProps();
    }

    @JsonProperty("props")
    public void setProps(DataStructProperties props) {
        getResourceProps().setProps(props);
    }

    @Deprecated
    public String getAlias() {
        return super.getKey();
    }

    @Deprecated
    public void setAlias(String alias) {
        super.setKey(alias);
        // keep compatible
        getResourceProps().setAlias(alias);
    }

    @Deprecated
    public String getDesc() {
        return super.getDescription();
    }

    @Deprecated
    public void setDesc(String desc) {
        super.setDescription(desc);
    }

    public List<DataStructFieldNode> getChildren() {
        return getResourceProps().getChildren();
    }

    public void setChildren(List<DataStructFieldNode> children) {
        getResourceProps().setChildren(children);
    }

    /**
     * 该模型是否开启搜索
     *
     * @return true：开启；false：关闭
     */
    public boolean isSearchModel() {
        return null != getProps() && getProps().isSearchModel();
    }

    /**
     * 该模型是否为树结构模型
     * <p>
     * 目前判断标准：是否含有isLeaf字段
     *
     * @return true:是，false：否
     */
    @JsonIgnore
    public boolean isTreeModel() {
        return getChildren().stream().anyMatch(meta -> meta.getAlias().equals("isLeaf"));
    }

    /**
     * 该模型是否为业务配置
     */
    public boolean isSetting() {
        return getProps() != null && getProps().getType() == DataStructType.SETTING;
    }

    public Map<String, DataStructFieldNode> toFieldMap() {
        return getChildren().stream().collect(Collectors.toMap(DataStructFieldNode::getAlias, Function.identity()));
    }

    /**
     * 获取树模型的父字段
     */
    @JsonIgnore
    public String getTreeParentField() {
        if (StringUtils.isNotBlank(getProps().getTreeParentField())) {
            return getProps().getTreeParentField();
        }

        String parentFieldKey = null;
        for (DataStructFieldNode field : getChildren()) {
            RelationMeta relation = field.getProps().getRelationMeta();
            // 查找是否为关联模型字段
            if (Objects.nonNull(relation) && Objects.equals(relation.getRelationModelKey(), getKey())) {
                parentFieldKey = field.getAlias();
                break;
            }
        }

        return parentFieldKey;
    }

    @Override
    public MetaType getMetaType() {
        return MetaType.Model;
    }
}
