package io.terminus.trantor2.model.management.meta.desensitization;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * DesensitizationRule
 *
 * <AUTHOR> Created on 2023/6/6 13:58
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DesensitizationRule {

    private String key;
    private String name;
    private String regex;
    private String referenceValue;

    public String desensitized(String text) {
        switch (key) {
            case "DES_PASSWORD":
                return StringUtils.repeat("*", text.length());
            case "DES_ID_CARD":
            case "DES_OTHER":
            case "DES_US_CARD":
                return text.replaceAll(".(?=.{4})", "*");
            case "DES_EMAIL":
                return masked(text, "(.{1,2})(.*)(@.*)", 2);
            case "DES_MOBILE":
                return text.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
            case "DES_TEL":
                return masked(text, "(\\d{3,4})(-)(\\d{4,5})(\\d{3})", 3);
            case "DES_ADDRESS":
                return masked(text, "([\\u4e00-\\u9fa5]+省)?([\\u4e00-\\u9fa5]+市)?(?=([\\u4e00-\\u9fa5]+区))?([\\u4e00-\\u9fa5]+县)?(.*)", 5);
            case "DES_LEGAL_PERSON":
            case "DES_USERNAME":
                int textLength = text.length();
                if (textLength <= 3) {
                    return StringUtils.repeat("*", textLength - 1) + text.substring(textLength - 1);
                } else if (textLength <= 6) {
                    return StringUtils.repeat("*", textLength - 2) + text.substring(textLength - 2);
                } else {
                    return text.charAt(0) + StringUtils.repeat("*", textLength - 2) + text.substring(textLength - 1);
                }
            case "DES_BANK_ACCOUNT":
                return masked(text, "(\\d{4})(.*)(\\d{4})", 2);
            case "DES_TAXPAYER_NUMBER":
                return masked(text, "(\\w{4})(.*)(\\w{3})", 2);
            case "DES_BUSINESS_NUMBER":
                return masked(text, "(\\w{2})(.*)(\\w|\\*){1}", 2);
            case "DES_NICKNAME":
                return text.charAt(0) + StringUtils.repeat("*", text.length() - 2) + text.substring(text.length() - 1);
            default:
                return text.replaceAll(regex, "*");
        }
    }

    private static String masked(String text, String regex, Integer... maskedGroup) {
        Matcher matcher = Pattern.compile(regex).matcher(text);
        LinkedHashMap<Integer, String> groupInfo = new LinkedHashMap<>();
        if (matcher.matches()) {
            for (int i = 1; i <= matcher.groupCount(); i++) {
                groupInfo.put(i, matcher.group(i));
            }
            for (Integer groupIdx : maskedGroup) {
                String source = groupInfo.get(groupIdx);
                String masked = source.replaceAll(".", "*");
                groupInfo.put(groupIdx, masked);
            }
            return join(groupInfo.values());
        }
        return text;
    }

    private static String join(Collection<String> chars) {
        StringBuilder sb = new StringBuilder();
        for (String s : chars) {
            sb.append(StringUtils.defaultString(s));
        }
        return sb.toString();
    }
}
