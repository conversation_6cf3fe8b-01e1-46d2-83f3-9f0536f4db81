package io.terminus.trantor2.model.management.meta.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.model.management.meta.enums.PartitionType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 模型分表配置信息
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Schema(description = "模型分表配置")
public class ModelShardingConfigMeta implements Serializable {

    /**
     * 模型标识
     */
    @Schema(description = "模型标识")
    private String modelAlias;

    /**
     * 逻辑库名
     */
    @Schema(description = "逻辑库名")
    private String logicSchemaName;

    /**
     * 逻辑表名
     */
    @Schema(description = "逻辑表名")
    private String logicTableName;

    /**
     * 是否广播表
     */
    @Schema(description = "是否广播表")
    private Boolean broadcast = false;

    /**
     * 分表数量
     */
    @Schema(description = "分表数量")
    private int tbNumber;

    /**
     * 分表规则
     */
    @Schema(description = "分表规则")
    private PartitionType.TablePartitionTypeEnum tbRule;

    /**
     * 分表键
     */
    @Schema(description = "分表键")
    private String tbPartitionKey;

    /**
     * 分表键DBName
     */
    @Schema(description = "分表键DBName")
    private String tbPartitionKeyDbName;

    /**
     * 分表键类型是否为String类型
     */
    @Schema(description = "分表键类型是否为String类型")
    private boolean tbPartitionKeyStringType;


    /**
     * 分表hash算法截取字符串的开始下标
     */
    @Schema(description = "分表hash算法截取字符串的开始下标")
    private int hashStartIndex;

    /**
     * 分表hash算法截取字符串的结束下标
     */
    @Schema(description = "分表hash算法截取字符串的结束下标")
    private int hashEndIndex;

    /**
     * TableRuleConfiguration Groovy表达式
     *
     * @return 分表规则
     */
    public String toActualDataNodes() {
        return logicSchemaName + "." + logicTableName + "_${0.." + (tbNumber - 1) + "}";
    }

    /**
     * todo 不同分表策略获取物理表的方式不一致
     * 获取物理表名称列表
     *
     * @return 所有的物理表
     */
    public List<String> getPhysicalTableName() {
        List<String> result = new ArrayList<>();
        for (int i = 0; i < tbNumber; i++) {
            result.add(logicTableName + "_" + i);
        }
        return result;
    }

    /**
     * todo 不同分表策略获取物理表的方式不一致
     * 获取物理表名称列表
     *
     * @param tablePrefix 表名前缀
     * @return 物理表集合
     */
    public List<String> getPhysicalTableName(String tablePrefix) {
        List<String> result = new ArrayList<>();
        for (int i = 0; i < tbNumber; i++) {
            result.add(tablePrefix + "_" + i);
        }
        return result;
    }

    /**
     * 获取物理表名，逻辑表名_tbNumber
     *
     * @return 物理表名
     */
    public String getOverFlowPhysicalTableName() {
        return logicTableName + "_" + tbNumber;
    }

    /**
     * 获取所有的物理表
     *
     * @param tableNum
     * @param tableName
     * @return 物理表
     */
    public static List<String> getPhysicalTableName(int tableNum, String tableName) {
        List<String> result = new ArrayList<>();
        for (int i = 0; i < tableNum; i++) {
            result.add(tableName + "_" + i);
        }
        return result;
    }


    /**
     * 获取最后一次发布成功的物理表名称列表
     * @param lastVersionLogicTableName
     * @return 最新的物理表集合
     */
    public List<String> getLastVersionPhysicalTableName(String lastVersionLogicTableName) {
        List<String> result = new ArrayList<>();
        for (int i = 0; i < tbNumber; i++) {
            result.add(lastVersionLogicTableName + "_" + i);
        }
        return result;
    }

    /**
     * 将更名后的表名添加分表后缀
     *
     * @param sourceNode
     * @param replace
     * @return String
     */
    public String renameNode(String sourceNode, String replace) {
        int index = sourceNode.lastIndexOf("_");
        int number = Integer.parseInt(sourceNode.substring(index + 1));
        return replace + "_" + number;
    }

}
