package io.terminus.trantor2.model.management.meta.domain.validation.constraint;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(title = "长度&集合长度约束, 适用于 TEXT、MULTI_TEXT 类型的字段")
public class Size extends Constraint {
    @Schema(description = "最小长度, 默认为 0")
    private Integer min = 0;
    @Schema(description = "最大长度，默认为 Integer.MAX_VALUE")
    private Integer max = Integer.MAX_VALUE;

    @Override
    public ConstraintType getConstraintType() {
        return ConstraintType.SIZE;
    }
}
