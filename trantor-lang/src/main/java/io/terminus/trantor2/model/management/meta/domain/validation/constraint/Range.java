package io.terminus.trantor2.model.management.meta.domain.validation.constraint;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(title = "上下限约束, 适用于 NUMBER、DATE 类型的字段")
public class Range extends Constraint {

    @Schema(description = "最小值")
    private String min;
    @Schema(description = "包含最小值")
    private Boolean inclusiveMin;
    @Schema(description = "最大值")
    private String max;
    @Schema(description = "包含最大值")
    private Boolean inclusiveMax;

    @Override
    public ConstraintType getConstraintType() {
        return ConstraintType.RANGE;
    }
}
