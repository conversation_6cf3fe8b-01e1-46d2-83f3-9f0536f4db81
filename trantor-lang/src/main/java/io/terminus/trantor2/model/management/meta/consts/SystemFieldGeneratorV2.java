package io.terminus.trantor2.model.management.meta.consts;

import java.util.List;

/**
 * 系统字段管理
 */
public enum SystemFieldGeneratorV2 {
    /**
     * id
     */
    ID("id", FieldType.NUMBER),
    /**
     * createdAt
     */
    CREATED_AT("createdAt", FieldType.DATE),
    /**
     * createdBy
     */
    CREATED_BY("createdBy", FieldType.NUMBER),
    /**
     * updatedAt
     */
    UPDATED_AT("updatedAt", FieldType.DATE),
    /**
     * updatedBy
     */
    UPDATED_BY("updatedBy", FieldType.NUMBER),
    /**
     * deleted
     */
    DELETED("deleted", FieldType.NUMBER),
    /**
     * version
     */
    VERSION("version", FieldType.NUMBER),

    ORIGIN_ORG_ID("originOrgId", FieldType.NUMBER),

    REQUEST_ID("requestId", FieldType.TEXT),

    ORDER("orderNumber", FieldType.NUMBER),

    DEFAULT_LOCALE("defaultLocale", FieldType.TEXT);
    /**
     * 字段标识（java 编码使用）
     */
    private String fieldAlias;
    /**
     * 界面展示类型
     */
    private FieldType displayType;

    /**
     * 构造器
     *
     * @param fieldAlias1
     * @param displayType1
     */
    SystemFieldGeneratorV2(String fieldAlias1, FieldType displayType1) {
        this.displayType = displayType1;
        this.fieldAlias = fieldAlias1;
    }

    /**
     * 根据标识获取系统字段定义
     *
     * @param fieldAlias
     * @return SystemFieldGenerator
     */
    public static SystemFieldGeneratorV2 getByFieldAlias(String fieldAlias) {
        for (SystemFieldGeneratorV2 systemField : SystemFieldGeneratorV2.values()) {
            if (systemField.fieldAlias.equals(fieldAlias)) {
                return systemField;
            }
        }
        return null;
    }

    /**
     * 判断数据库字段名是否命中了系统字段标识
     *
     * @param columnName
     * @param supportTypes
     * @return 字段标识
     */
    public static String getFieldAliasWhenMatchSystemField(String columnName, List<String> supportTypes) {
        for (SystemFieldGeneratorV2 systemField : SystemFieldGeneratorV2.values()) {
            if (systemField.fieldAlias.equalsIgnoreCase(columnName.replace("_", ""))
                && supportTypes.contains(systemField.displayType)) {
                return systemField.fieldAlias;
            }
        }
        return columnName;
    }

    /**
     * 打印系统字段标识同展示类型的格式化内容
     *
     * @return 格式化内容
     */
    public static String formatFieldAliasAndDisplayType() {
        StringBuilder buffer = new StringBuilder();
        for (SystemFieldGeneratorV2 systemField : SystemFieldGeneratorV2.values()) {
            buffer.append(systemField.fieldAlias).append("=>").append(systemField.getDisplayType()).append(";");
        }
        return buffer.toString();
    }

    /**
     * 获取系统字段标识
     *
     * @return fieldAlias
     */
    public String getFieldAlias() {
        return fieldAlias;
    }

    /**
     * 获取系统字段展示类型
     *
     * @return displayType
     */
    public FieldType getDisplayType() {
        return displayType;
    }
}
