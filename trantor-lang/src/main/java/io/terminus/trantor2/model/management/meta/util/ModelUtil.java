package io.terminus.trantor2.model.management.meta.util;

import io.terminus.trantor2.model.management.meta.consts.DataStructType;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;

public class ModelUtil {
    /**
     * 是否为业务配置
     * @param model
     * @return
     */
    public static boolean isSetting(DataStructNode model) {
        return model.getProps().getType() == DataStructType.SETTING;
    }
}
