package io.terminus.trantor2.lang.meta;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectReader;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.module.meta.EndpointType;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.nio.file.Path;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
public final class CompPack {

    private static final ObjectReader LATEST_READER = ObjectJsonUtil.MAPPER.readerFor(new TypeReference<Map<String, Info>>() {
    });

    private static final ObjectReader PACK_DATA_READER = ObjectJsonUtil.MAPPER.readerFor(new TypeReference<Behaviors>() {
    });

    private static CompPack instance;

    private final Latest latest;

    /**
     * endpointType + "/" + compName -> Behavior
     */
    private final Map<String, Behavior> behaviorMap;

    public static Behavior getBehavior(String endpointType, String compName) {
        if (instance == null) {
            throw new IllegalStateException("CompPack is not loaded");
        }
        if (endpointType == null) {
            endpointType = EndpointType.PC.name();
        }
        return instance.behaviorMap.get(endpointType + "/" + compName);
    }

    private CompPack(Latest latest, Map<String, Behavior> behaviorMap) {
        this.latest = latest;
        this.behaviorMap = behaviorMap;
    }

    public static Latest readLatest(Path dir) {
        Path path = latestJsonPath(dir);
        try {
            Map<String, Info> infos = LATEST_READER.readValue(path.toFile());
            Latest latest = new Latest();
            latest.setInfos(infos);
            return latest;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void load(Path dir) {
        Latest latest = readLatest(dir);
        Map<String, Behavior> behaviorMap = new HashMap<>();
        List<Info> sortedInfos = latest.getInfos().values().stream().sorted().collect(Collectors.toList());
        for (Info info : sortedInfos) {
            Path behaviorsJsonPath = behaviorsJsonPath(dir, info);
            try {
                Behaviors behaviors = PACK_DATA_READER.readValue(behaviorsJsonPath.toFile());
                for (Behavior behavior : behaviors.getBehaviors()) {
                    String endpointType = info.getEndpointType();
                    if (endpointType == null) {
                        endpointType = "PC";
                    }
                    behaviorMap.put(endpointType + "/" + behavior.getName(), behavior);
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        instance = new CompPack(latest, behaviorMap);
    }

    public static Path latestJsonPath(Path dir) {
        return dir.resolve("latest.json");
    }

    public static String behaviorsJsonUrl(String endpoint, Info info) {
        return endpoint + "/" + info.getPrefix() + "/" + info.getDirname() + "/behaviors.json";
    }

    public static Path behaviorsJsonPath(Path dir, Info info) {
        return dir.resolve(info.getPrefix()).resolve(info.getDirname()).resolve("behaviors.json");
    }

    @Data
    public static final class Latest {
        private Map<String, Info> infos;
    }

    @Data
    public static final class Info implements Comparable<Info> {
        private String prefix;
        private String dirname;
        private String namespace;
        private String endpointType;
        // TODO: scopes
        private Integer priority;

        @Override
        public int compareTo(@NotNull CompPack.Info o) {
            if (this.priority == null && o.priority == null) {
                return 0;
            }
            if (this.priority == null) {
                return 1;
            }
            if (o.priority == null) {
                return -1;
            }
            return this.priority.compareTo(o.priority);
        }
    }

    @Data
    public static final class Behaviors {
        private String namespace;
        private List<Behavior> behaviors;
        private Date updateAt;
    }

    @Data
    public static final class Behavior {
        private String name;
        private String type;
        private String title;
        //private String group; // group maybe array or string
        private String displayNamePath;
        private JsonNode defaultProps;
        private JsonNode staticProps;
        private List<BehaviorPermission> permissions;
    }

    @Data
    public static final class BehaviorPermission {
        private String type;
        private String resourceType;
        private String target; // object in props, like: props.target
        private String key;
        private String label;
        private List<BehaviorPermissionRelation> relations;
    }

    @Data
    public static final class BehaviorPermissionRelation {
        private String type;
        private String key;
    }
}
