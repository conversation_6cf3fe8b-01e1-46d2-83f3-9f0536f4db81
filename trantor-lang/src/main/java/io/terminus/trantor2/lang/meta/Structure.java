package io.terminus.trantor2.lang.meta;

import io.terminus.trantor2.meta.api.dto.MetaLink;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Structure of an Asset
 * <pre>
 * <b>Ref:</b>
 * ---
 * FROM: | key | type | subtype | innerKey | innerType |
 * TO  : | key | type | subtype | innerKey | innerType |
 * GRP : | refGroup |
 * </pre>
 *
 * <AUTHOR>
 */
@Data
public final class Structure {
    private Map<String, Object> customizedField; // TODO: should be a single column
    private final Set<MetaLink> links = new HashSet<>();
    private final List<Ref> refs = new ArrayList<>();
    private final List<InnerNode> innerNodes = new ArrayList<>();
    @Deprecated
    private final List<ViewContainer> viewContainers = new ArrayList<>();

    @Data
    public static final class InnerNode {
        private String innerKey;
        private String innerType;
        private String innerName;
        private String innerParentKey;
        // whether component-level function permission is enabled (from view content: props.permissionEnabled)
        private Boolean permissionEnabled;
    }

    @Data
    public static final class Ref {
        private String value;
        private String relativeJsonPath;
        private String sourceInnerKey;
        private String sourceInnerType;
        private String targetKey;
        private String targetType;
        private String targetInnerKey;
        private String targetInnerType;
        private String groupId;
    }
}
