package io.terminus.trantor2.scene.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * 场景模版类别
 *
 * <AUTHOR>
 */
@Getter
@Schema(description = "场景模版类别", enumAsRef = true, example = "MASTER_DATA")
public enum TemplateType {
    @Schema(description = "主数据")
    MASTER_DATA("主数据"),

    @Schema(description = "业务表单")
    BUSINESS_DOCUMENT("业务表单"),

    @Schema(description = "空白页面")
    EMPTY("空白页面"),

    @Schema(description = "规则配置")
    RULE_CONFIG("规则配置"),
    @Schema(description = "规则简单定义")
    RULE_SIMPLE_DEFINE("规则简单定义"),
    @Schema(description = "规则分配")
    RULE_ALLOCATION("规则分配"),
    @Schema(description = "规则全局")
    RULE_GLOBAL("规则全局"),
    @Schema(description = "资产账户")
    FINE_ACCOUNT("资产账户"),
    @Schema(description = "报表")
    REPORT("报表"),
    @Schema(description = "工作台")
    WORKBENCH("工作台"),
    @Schema(description = "IAM")
    IAM("IAM"),
    @Schema(description = "日志")
    LOG("日志"),
    @Schema(description = "自定义")
    CUSTOM("自定义");

    private final String label;

    TemplateType(String label) {
        this.label = label;
    }
}
