package io.terminus.trantor2.scene.config.i18n;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "场景国际化配置，目前由前端扫描待国际化内容")
@FieldNameConstants
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ViewI18nConfig {
    @Schema(description = "国际化 key 集合")
    private Set<String> i18nKeySet = new HashSet<>();
    @Schema(description = "国际化扫描路径集合，用于前端扫描待国际化内容")
    private Set<String> i18nScanPaths;
    @Schema(title = "国际化资源配置", description = "key-value 形式的国际化资源配置")
    private Map<String, String> i18nResources;
}
