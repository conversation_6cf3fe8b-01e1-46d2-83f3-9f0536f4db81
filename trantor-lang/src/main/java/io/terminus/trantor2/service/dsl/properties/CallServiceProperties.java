package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.service.dsl.enums.Propagation;
import io.terminus.trantor2.service.dsl.enums.VariableType;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 调用通用流程配置
 */
@Setter
@Getter
public class CallServiceProperties extends AbstractProperties implements ErrorConfig {

    private static final long serialVersionUID = 2274934116216399847L;

    /**
     * 服务Key
     */
    private String serviceKey;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 事务
     */
    private Propagation transactionPropagation;

    /**
     * 是否异步执行
     */
    private boolean async;

    /**
     * 请求参数
     */
    private List<FieldEntry> inputMapping;

    /**
     * 出参结构
     */
    private List<Field> output;

    /**
     * 出参赋值
     */
    protected OutputAssign outputAssign;

    @Override
    public void validate(ValidatorContext errorContext) {
        if (serviceKey == null) {
            errorContext.addErrorInfo("调用的编排服务未配置");
        }
        if (inputMapping != null) {
            errorContext.addTag("入参映射");
            inputMapping.forEach(i -> i.validate(errorContext));
            errorContext.removeTag();
        }


        if (output != null) {
            errorContext.addTag("出参定义");
            output.forEach(o -> o.validate(errorContext));
            errorContext.removeTag();

            if (outputAssign != null && outputAssign.isSystemDefault()) {
                errorContext.getGlobalVariable()
                        .addElement(new ObjectField(VariableType.NODE_OUTPUT.formatKey(errorContext.getNodeKey()), output));
            } else {
                errorContext.addVariable(new ObjectField(VariableType.NODE_OUTPUT.formatKey(errorContext.getNodeKey()), output));
            }
        }

        if (outputAssign != null) {
            errorContext.addTag("出参赋值");
            outputAssign.validate(errorContext);
            errorContext.removeTag();
        }
    }
}
