package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.service.dsl.enums.VariableType;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * ConnectorProperties
 *
 * <AUTHOR> Created on 2023/11/6 10:52
 */
@Setter
@Getter
public class ConnectorProperties extends AbstractProperties {

    private static final long serialVersionUID = 8069814239225188731L;

    /**
     * 模板Key
     */
    private String templateKey;

    /**
     * 连接器实列Key
     */
    private String connectorKey;

    /**
     * 连接器实列名称
     */
    private String connectorName;

    /**
     * 连接器服务Key
     */
    private String connectorServiceKey;

    /**
     * 连接器服务名
     */
    private String connectorServiceName;

    /**
     * 入参映射
     */
    private List<StringEntry> inputMapping;

    /**
     * 出参结构，Connector请求是单出参
     */
    private List<Field> output;

    /**
     * 出参赋值
     */
    protected OutputAssign outputAssign;

    @Override
    public void validate(ValidatorContext errorContext) {

        if (connectorKey == null) {
            errorContext.addErrorInfo("连接器未配置");
        }

        if (connectorServiceKey == null) {
            errorContext.addErrorInfo("连接器服务未配置");
        }

        if (inputMapping != null) {
            errorContext.addTag("入参映射");
            inputMapping.forEach(i -> i.validate(errorContext));
            errorContext.removeTag();
        }

        if (output != null) {
            errorContext.addTag("出参定义");
            output.forEach(o -> o.validate(errorContext));
            errorContext.removeTag();

            if (outputAssign != null && outputAssign.isSystemDefault()) {
                errorContext.getGlobalVariable()
                    .addElement(new ObjectField(VariableType.NODE_OUTPUT.formatKey(errorContext.getNodeKey()), output));
            } else {
                errorContext.addVariable(new ObjectField(VariableType.NODE_OUTPUT.formatKey(errorContext.getNodeKey()), output));
            }
        }

        if (outputAssign != null) {
            errorContext.addTag("出参赋值");
            outputAssign.validate(errorContext);
            errorContext.removeTag();
        }

    }
}
