package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * SwitchCaseProperties
 *
 * <AUTHOR> Created on 2023/11/21 14:19
 */
@Setter
@Getter
public class SwitchCaseProperties extends AbstractProperties {
    private static final long serialVersionUID = 588814905751818551L;

    private List<ConstValue> caseValues;

    @Override
    public void validate(ValidatorContext errorContext) {
        if (caseValues == null) {
            errorContext.addErrorInfo("Switch的Case值未配置");
        } else {
            errorContext.addTag("Switch的Case值");
            caseValues.forEach(c -> c.validate(errorContext));
            errorContext.removeTag();
        }
    }
}
