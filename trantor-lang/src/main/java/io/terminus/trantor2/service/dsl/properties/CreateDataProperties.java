package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.service.dsl.enums.VariableType;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 添加数据配置
 *
 * <AUTHOR>
 */
@Deprecated
@Setter
@Getter
public class CreateDataProperties extends DataProcessingProperties {

    private static final long serialVersionUID = 3829280137293437122L;

    /**
     * 字段行列表
     */
    private List<FieldEntry> inputMapping;

    /**
     * 出参赋值
     */
    protected OutputAssign outputAssign;

    @Override
    public void validate(ValidatorContext errorContext) {
        errorContext.addErrorInfo(false, "该节点已升级新版本，请尽快更换新节点");
        if (relatedModel != null) {
            ModelField field = new ModelField(VariableType.NODE_OUTPUT.formatKey(errorContext.getNodeKey()), getRelatedModel());
            errorContext.getGlobalVariable().addElement(field);
        }
    }

}
