package io.terminus.trantor2.service.dsl.properties;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.service.dsl.enums.Operator;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "智能体术语映射-字段值映射")
public class AgentFieldValueTermMapping implements Serializable {

    @Schema(description = "字段 key")
    private String fieldKey;

    @Schema(description = "操作符")
    private Operator operator;

    @Schema(description = "字段值")
    private String fieldValue;
}