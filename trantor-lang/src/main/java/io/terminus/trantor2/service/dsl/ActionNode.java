package io.terminus.trantor2.service.dsl;

import io.terminus.trantor2.service.dsl.properties.ActionProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * ActionNode
 *
 * <AUTHOR> Created on 2023/2/25 19:23
 */
@Getter
@Setter
public class ActionNode extends ServiceNode<ActionProperties> {
    private static final long serialVersionUID = 2357685708761709580L;

    @Override
    public Map<String, Object> getCurrentServiceElementVariables() {
        Map<String, Object> variables = new LinkedHashMap<>(2);
        variables.put("actionCode", props.getImplementation());
        if (props.isNewAction()) {
            variables.put("express(取值表达式)", props.getExpress());
            variables.put("convert(参数转换脚本)", props.getConvert());
            variables.put("conditionGroup", props.getConditionGroup());
        } else {
            if (props.getInputMapping() != null) {
                variables.put("inputMapping(扩展服务入参)", props.getInputMapping());
            }
        }
        return variables;
    }
}
