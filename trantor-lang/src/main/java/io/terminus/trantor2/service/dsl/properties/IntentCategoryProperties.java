package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

/**
 * IntentCategoryProperties
 *
 * <AUTHOR> Created on 2025/8/7 09:41
 */

@Setter
@Getter
public class IntentCategoryProperties extends AbstractProperties {

    @Serial
    private static final long serialVersionUID = 4264392278680152560L;

    /**
     * 意图名
     */
    private String intentName;

    /**
     * 意图描述
     */
    private String intentDesc;

    @Override
    public void validate(ValidatorContext errorContext) {

    }
}
