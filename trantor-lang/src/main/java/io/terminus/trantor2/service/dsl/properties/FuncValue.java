package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.service.dsl.enums.FieldType;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;

/**
 * 函数值
 *
 * <AUTHOR> Created on 2023/7/5 19:54
 */
@Setter
@Getter
public class FuncValue extends Value {
    private static final long serialVersionUID = -7635994169946932236L;

    /**
     * 函数表达
     */
    private String funcExpression;

    /**
     * 字段类型
     */
    private FieldType fieldType;

    @Override
    public FieldType getFieldType() {
        return fieldType;
    }

    @Override
    public String toString() {
        return funcExpression;
    }

    @Override
    public void validate(ValidatorContext errorContext) {
        if (funcExpression == null) {
            errorContext.addErrorInfo("函数未配置");
        } else {
            if (funcExpression.contains("${${")) {
                errorContext.addErrorInfo("函数变量使用不正确，使用了双层的‘${${’，请修改。");
            }
        }
    }
}
