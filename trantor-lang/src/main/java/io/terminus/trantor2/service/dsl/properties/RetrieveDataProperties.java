package io.terminus.trantor2.service.dsl.properties;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.terminus.trantor2.service.dsl.enums.FieldType;
import io.terminus.trantor2.service.dsl.enums.RetrieveDataType;
import io.terminus.trantor2.service.dsl.enums.VariableType;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 查找数据配置
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class RetrieveDataProperties extends DataProcessingProperties {

    private static final long serialVersionUID = 3453764225324543687L;

    /**
     * 查询类型
     */
    private RetrieveDataType dataType = RetrieveDataType.MODEL;

    /**
     * 分页设置
     */
    private Value pageable;

    /**
     * 动态条件(既可以是简化版条件，也可以是复杂版条件)
     */
    private Value dynamicCondition;

    /**
     * 条件范围, 和{@link #pageable}里的动态条件组是“且”的关系
     */
    private ConditionGroup conditionGroup;

    /**
     * 排序规则配置
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Deprecated
    private SortOrder sortOrder;

    /**
     * 排序规则配置
     */
    private List<SortOrder> sortOrders;

    /**
     * 当数据为空时，是否停止流程
     */
    protected boolean stopWhenDataEmpty;

    /**
     * 关联的子模型查询
     */
    protected List<QueryRelatedModel> subQueryRelatedModels;

    /**
     * 查询字段（0430的定义，之后的版本去除该字段）
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Deprecated
    protected List<QueryField> queryFields;

    /**
     * 查询字段（支持*）
     */
    protected QueryModelFields queryModelFields;

    /**
     * 查询的最大行数
     */
    protected Long maximum;

    /**
     * 是否脱敏
     */
    private boolean desensitized = true;

    /**
     * 业务数据国际化
     */
    private boolean dataI18n;

    /**
     * 出参赋值
     */
    protected OutputAssign outputAssign;

    public QueryModelFields getQueryModelFields() {
        if (getModelKey() == null) {
            return null;
        }
        if (queryModelFields == null) {
            // 330的定义
            if (queryFields == null || queryFields.isEmpty()) {
                queryModelFields = new QueryModelFields();
                queryModelFields.setModelKey(getModelKey());
                queryModelFields.setAllFields(true);
                queryModelFields.setQueryFields(convertQueryFields(subQueryRelatedModels));
            }
            // 430的定义
            else {
                if (subQueryRelatedModels == null) {
                    subQueryRelatedModels = new ArrayList<>();
                }
                queryModelFields = new QueryModelFields();
                queryModelFields.setModelKey(getModelKey());
                queryModelFields.setQueryFields(convertQueryFields(queryFields, subQueryRelatedModels));
            }
        }
        if (queryModelFields != null && queryModelFields.getModelKey() == null) {
            queryModelFields.setModelKey(getModelKey());
        }
        return queryModelFields;
    }


    private List<QueryField> convertQueryFields(List<QueryRelatedModel> subQueryRelatedModels) {
        if (subQueryRelatedModels == null || subQueryRelatedModels.isEmpty()) {
            return null;
        }

        List<QueryField> queryFields = new ArrayList<>(subQueryRelatedModels.size());

        for (QueryRelatedModel queryRelatedModel : subQueryRelatedModels) {
            QueryField queryField = new QueryField(queryRelatedModel.getFieldKey());

            QueryModelFields queryModelFields = new QueryModelFields();
            queryModelFields.setModelKey(queryRelatedModel.getModelKey());
            queryModelFields.setAllFields(true);
            queryModelFields.setQueryFields(convertQueryFields(queryRelatedModel.getSubQueryRelatedModels()));

            queryField.setQueryModelFields(queryModelFields);

            queryFields.add(queryField);
        }

        return queryFields;
    }

    private List<QueryField> convertQueryFields(List<QueryField> queryFields, List<QueryRelatedModel> parentSubQueryModels) {
        List<QueryField> newQueryFields = new ArrayList<>();
        for (QueryField queryField : queryFields) {
            // 关联模型：这里有问题，因为把所有的字段都当做关联模型了
            QueryRelatedModel queryRelatedModel = new QueryRelatedModel();
            queryRelatedModel.setFieldKey(queryField.getFieldKey());
            queryRelatedModel.setSubQueryRelatedModels(new ArrayList<>());
            queryRelatedModel.setConditionGroup(queryField.getConditionGroup());
            parentSubQueryModels.add(queryRelatedModel);

            // 新的查询字段
            QueryField newQueryField = new QueryField(queryField.getFieldKey());
            if (queryField.getSubModelFields() != null && !queryField.getSubModelFields().isEmpty()) {
                QueryModelFields subQueryModelFields = new QueryModelFields();
                subQueryModelFields.setQueryFields(convertQueryFields(queryField.getSubModelFields(), queryRelatedModel.getSubQueryRelatedModels()));
                newQueryField.setQueryModelFields(subQueryModelFields);
            }

            newQueryFields.add(newQueryField);
        }
        return newQueryFields;
    }

    public List<SortOrder> getSortOrders() {
        if (sortOrders != null) {
            return sortOrders;
        }
        if (sortOrder != null) {
            List<SortOrder> orders = new ArrayList<>(1);
            orders.add(sortOrder);
            return orders;
        }
        return null;
    }

    @Override
    public void validate(ValidatorContext errorContext) {
        super.validate(errorContext);

        if (dataType == RetrieveDataType.PAGING) {
            if (pageable == null) {
                errorContext.addErrorInfo("分页设置未配置");
            } else {
                errorContext.addTag("分页设置");
                pageable.validate(errorContext);
                errorContext.removeTag();
            }
        } else {
            if ((conditionGroup == null || conditionGroup.isEmpty()) && dynamicCondition == null) {
                errorContext.addErrorInfo("查询条件未配置");
            }
            if (dynamicCondition != null) {
                errorContext.addTag("动态条件");
                dynamicCondition.validate(errorContext);
                errorContext.removeTag();
            }
        }

        if (conditionGroup != null && conditionGroup.isNotEmpty()) {
            errorContext.addTag("查询条件");
            conditionGroup.validate(errorContext);
            errorContext.removeTag();
        }

        if (sortOrders != null && !sortOrders.isEmpty()) {
            errorContext.addTag("排序设置");
            sortOrders.forEach(o -> o.validate(errorContext));
            errorContext.removeTag();
        }

        if (subQueryRelatedModels != null && !subQueryRelatedModels.isEmpty()) {
            errorContext.addTag("关联模型配置");
            subQueryRelatedModels.forEach(o -> o.validate(errorContext));
            errorContext.removeTag();
        }

        if (queryModelFields == null) {
            errorContext.addErrorInfo("查询字段未配置");
        } else if (!queryModelFields.isAllFields()
                && (queryModelFields.getQueryFields() == null || queryModelFields.getQueryFields().isEmpty())) {
            errorContext.addErrorInfo("查询字段未配置");
        } else {
            errorContext.addTag("查询字段配置");
            queryModelFields.validate(errorContext);
            errorContext.removeTag();
        }

        if (relatedModel != null) {
            ModelField field = new ModelField(VariableType.NODE_OUTPUT.formatKey(errorContext.getNodeKey()), getRelatedModel());

            if (isPaging()) {
                List<Field> elements = new ArrayList<>(2);
                elements.add(new BaseField("total", FieldType.Number));
                elements.add(new ArrayField("data", field));

                if (outputAssign != null && outputAssign.isSystemDefault()) {
                    errorContext.getGlobalVariable().addElement(new ObjectField(field.getFieldKey(), elements));
                } else {
                    errorContext.addVariable(new ObjectField(field.getFieldKey(), elements));
                }
            } else if (isArray()) {
                if (outputAssign != null && outputAssign.isSystemDefault()) {
                    errorContext.getGlobalVariable().addElement(new ArrayField(field.getFieldKey(), field));
                } else {
                    errorContext.addVariable(new ArrayField(field.getFieldKey(), field));
                }
            } else {
                if (outputAssign != null && outputAssign.isSystemDefault()) {
                    errorContext.getGlobalVariable().addElement(field);
                } else {
                    errorContext.addVariable(field);
                }
            }
        }

        if (outputAssign != null) {
            errorContext.addTag("出参赋值");
            outputAssign.validate(errorContext);
            errorContext.removeTag();
        }
    }

    @JsonIgnore
    public boolean isArray() {
        return RetrieveDataType.ARRAY.equals(dataType);
    }

    @JsonIgnore
    public boolean isPaging() {
        return RetrieveDataType.PAGING.equals(dataType);
    }
}
