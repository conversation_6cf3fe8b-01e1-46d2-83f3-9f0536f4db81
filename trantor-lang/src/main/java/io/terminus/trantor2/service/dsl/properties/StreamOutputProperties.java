package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;

/**
 * StreamOutputProperties
 *
 * <AUTHOR> Created on 2025/6/20 11:04
 */
@Setter
@Getter
public class StreamOutputProperties extends AbstractProperties {
    private static final long serialVersionUID = 5209542739903777238L;

    private String output;

    @Override
    public void validate(ValidatorContext errorContext) {

    }
}
