package io.terminus.trantor2.service.dsl.properties.validation;

import io.terminus.trantor2.service.dsl.ActionNode;
import io.terminus.trantor2.service.dsl.DataProcessingNode;
import io.terminus.trantor2.service.dsl.EndNode;
import io.terminus.trantor2.service.dsl.RetrieveDataNode;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.dsl.ServiceElement;
import io.terminus.trantor2.service.dsl.StartNode;
import io.terminus.trantor2.service.dsl.enums.VariableType;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.dsl.properties.ObjectField;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public class ValidatorContext {

    private final Map<String, ErrorContext> contextMap = new LinkedHashMap<>();

    @Getter
    private final List<Field> variables = new ArrayList<>();

    @Getter
    public final List<Consumer<ValidatorContext>> globalValidators = new ArrayList<>();

    @Setter
    @Getter
    private BiConsumer<String, Consumer<String>> sqlValidator;

    private final Deque<ErrorContext> queue;
    private ErrorContext curContext;
    private ServiceElement<?> curNode;

    @Getter
    private boolean hasDataHandle;
    @Getter
    private boolean hasInput;
    @Getter
    private boolean hasNode;

    public ValidatorContext() {
        this.queue = new LinkedList<>();
    }

    public void setCurNode(ServiceElement<?> curNode) {
        this.curNode = curNode;
        if (curNode instanceof StartNode) {
            this.hasInput = CollectionUtils.isNotEmpty(((StartNode) curNode).getProps().getInput());
        }
        if (!(curNode instanceof ServiceDefinition) && !(curNode instanceof StartNode) && !(curNode instanceof EndNode)) {
            this.hasNode = true;
        }
        if (hasDataHandle()) {
            this.hasDataHandle = true;
        }
        this.curContext = contextMap.computeIfAbsent(curNode.getKey(), k -> new ErrorContext(k, ""));
        this.queue.clear();
        this.queue.addFirst(this.curContext);
    }

    private boolean hasDataHandle() {
        return ((curNode instanceof DataProcessingNode) && !(curNode instanceof RetrieveDataNode))
                || curNode instanceof ActionNode;
    }

    public Map<String, List<ErrorInfo>> getErrorContext() {
        Map<String, List<ErrorInfo>> errorMap = new HashMap<>();

        contextMap.forEach((k, c) -> {
            List<ErrorInfo> errorInfos = new ArrayList<>();
            collectErrorInfo("", c, errorInfos);
            if (!errorInfos.isEmpty()) {
                errorMap.put(k, errorInfos);
            }
        });

        return errorMap;
    }

    private void collectErrorInfo(String title, ErrorContext context, List<ErrorInfo> errorInfos) {

        String label = (title + context.getTitle()).isEmpty() ? "" : title + context.getTitle() + " > ";

        if (!context.getErrorInfosMap().isEmpty()) {
            context.getErrorInfosMap().forEach(m -> {
                String fullMsg = label + m.getErrorMsg();
                m.setErrorMsg(fullMsg);
                errorInfos.add(m);
            });
        }

        if (!context.getChildren().isEmpty()) {
            for (ErrorContext ctx : context.getChildren()) {
                collectErrorInfo(label, ctx, errorInfos);
            }
        }
    }

    public String getNodeKey() {
        return curNode.getKey();
    }

    public void addErrorInfo(String errorMsg) {
        addErrorInfo(true, errorMsg);
    }

    public void addErrorInfo(boolean strongPrompt, String errorMsg) {
        this.curContext.addErrorInfo(new ErrorInfo(strongPrompt, errorMsg));
    }

    public void addTag(String tag) {
        ErrorContext context = this.curContext.createChildContext(tag);
        this.queue.addFirst(context);
        this.curContext = context;
    }

    public void removeTag() {
        this.queue.pop();
        this.curContext = queue.peekFirst();
    }

    public void addVariable(Field field) {
        this.variables.add(field);
    }

    public void addGlobalValidator(Consumer<ValidatorContext> validator) {
        this.globalValidators.add(validator);
    }

    public ObjectField getGlobalVariable() {
        ObjectField objectField = (ObjectField) variables.stream().filter(v -> v.getFieldKey().equals(VariableType.GLOBAL.getKey()))
                .findFirst().orElse(null);
        if (objectField == null) {
            objectField = new ObjectField(VariableType.GLOBAL.getKey(), VariableType.GLOBAL.getName());
            variables.add(objectField);
        }
        return objectField;
    }
}
