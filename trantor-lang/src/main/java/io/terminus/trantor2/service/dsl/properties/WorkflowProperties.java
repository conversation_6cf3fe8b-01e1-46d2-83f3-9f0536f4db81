package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * WorkflowProperties
 *
 * <AUTHOR> Created on 2024/2/28 14:09
 */
@Setter
@Getter
public class WorkflowProperties extends AbstractProperties {

    private static final long serialVersionUID = 2400076091444977706L;

    /**
     * 审批流组标识
     */
    private String workflowGroupKey;

    /**
     * 审批流组名称
     */
    private String workflowGroupName;

    /**
     * 模型Key
     */
    private String modelKey;

    /**
     * 审批流模型入参
     */
    private Value inputMapping;

    /**
     * 审批流自定义变量
     */
    private List<FieldEntry> customVariables;

    @Override
    public void validate(ValidatorContext errorContext) {
        if (workflowGroupKey == null) {
            errorContext.addErrorInfo("审批流组未配置");
        }

        if (inputMapping != null) {
            errorContext.addTag("审批流模型入参");
            inputMapping.validate(errorContext);
            errorContext.removeTag();
        }

        if (customVariables != null && !customVariables.isEmpty()) {
            errorContext.addTag("审批流自定义变量");
            customVariables.forEach(fieldEntry -> fieldEntry.validate(errorContext));
            errorContext.removeTag();
        }
    }
}
