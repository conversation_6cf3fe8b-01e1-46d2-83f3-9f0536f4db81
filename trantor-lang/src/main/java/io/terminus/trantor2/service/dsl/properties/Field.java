package io.terminus.trantor2.service.dsl.properties;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.terminus.trantor2.service.dsl.BaseElement;
import io.terminus.trantor2.service.dsl.enums.FieldType;
import io.terminus.trantor2.service.dsl.properties.validation.Validator;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 字段
 *
 * <AUTHOR>
 */
@Setter
@Getter
@NoArgsConstructor
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME, visible = true,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "fieldType")
@JsonSubTypes({
        @JsonSubTypes.Type(value = BaseField.class, name = "Text"),
        @JsonSubTypes.Type(value = BaseField.class, name = "RichText"),
        @JsonSubTypes.Type(value = BaseField.class, name = "Boolean"),
        @JsonSubTypes.Type(value = BaseField.class, name = "Number"),
        @JsonSubTypes.Type(value = BaseField.class, name = "RangeTime"),
        @JsonSubTypes.Type(value = BaseField.class, name = "RangeDate"),
        @JsonSubTypes.Type(value = BaseField.class, name = "Email"),
        @JsonSubTypes.Type(value = BaseField.class, name = "Attachment"),
        @JsonSubTypes.Type(value = EnumField.class, name = "Enum"),
        @JsonSubTypes.Type(value = DateField.class, name = "Date"),
        @JsonSubTypes.Type(value = DateTimeField.class, name = "DateTime"),
        @JsonSubTypes.Type(value = TimeField.class, name = "Time"),
        @JsonSubTypes.Type(value = ArrayField.class, name = "Array"),
        @JsonSubTypes.Type(value = ModelField.class, name = "Model"),
        @JsonSubTypes.Type(value = ObjectField.class, name = "Object"),
        @JsonSubTypes.Type(value = ObjectField.class, name = "Pageable"),
        @JsonSubTypes.Type(value = ObjectField.class, name = "Paging"),
        @JsonSubTypes.Type(value = ObjectField.class, name = "FileStream"),
        @JsonSubTypes.Type(value = ObjectField.class, name = "ConditionGroup"),
        @JsonSubTypes.Type(value = ObjectField.class, name = "ConditionItems")
})
@ToString(of = {"fieldKey", "fieldName", "fieldType"})
public abstract class Field extends BaseElement implements Validator, Serializable {

    private static final long serialVersionUID = 6398847133098854338L;

    /**
     * 字段 Key
     */
    protected String fieldKey;

    /**
     * 字段别名
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Deprecated
    private String fieldAlias;

    /**
     * 字段名
     */
    protected String fieldName;

    /**
     * 字段类型
     */
    @JsonProperty
    protected FieldType fieldType;

    /**
     * 字段描述
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String description;

    /**
     * 是否必填
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected Boolean required;

    /**
     * 默认值
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected Object defaultValue;

    public Field(String fieldKey, FieldType fieldType) {
        this(fieldKey, fieldKey, fieldType);
    }

    public Field(String fieldKey, String fieldName, FieldType fieldType) {
        this.fieldKey = fieldKey;
        this.fieldName = fieldName;
        this.fieldType = fieldType;
    }

    /**
     * @deprecated 使用 {@link #getFieldKey()}
     */
    @Deprecated
    public String getFieldAlias() {
        return getFieldKey();
    }

    public static Field ofByFieldType(Value value) {
        if (value != null) {
            return ofByFieldType(value.getFieldType());
        }
        return null;
    }

    public static Field ofByFieldType(FieldType fieldType) {
        return ofByFieldType("-", fieldType);
    }

    public static Field ofByFieldType(String fieldKey, FieldType fieldType) {
        if (Objects.isNull(fieldType)) {
            return null;
        }
        switch (fieldType) {
            case Object:
            case Pageable:
            case Paging:
                return new ObjectField(fieldKey, fieldKey);
            case Model:
                return new ModelField(fieldKey, null);
            case Array:
                return new ArrayField(fieldKey, null);
            default:
                return new BaseField(fieldKey, fieldType);
        }
    }

    public String getFieldKey() {
        if (fieldKey != null) {
            return fieldKey;
        }
        this.fieldKey = fieldAlias;
        return fieldKey;
    }

    public String getFieldName() {
        return fieldName != null ? fieldName : fieldKey;
    }

    @Override
    public void validate(ValidatorContext errorContext) {
        simpleValidate(errorContext);
    }

    public void simpleValidate(ValidatorContext errorContext) {
        boolean fieldKeyEmpty = getFieldKey() == null || getFieldKey().isEmpty();
        boolean fieldNameEmpty = getFieldName() == null || getFieldName().isEmpty();
        if (fieldKeyEmpty) {
            if (!fieldNameEmpty) {
                errorContext.addErrorInfo(String.format("字段名称为“%s”的字段标识未配置", getFieldName()));
            } else {
                errorContext.addErrorInfo("字段标识未配置");
            }
        }
        if (fieldNameEmpty) {
            if (!fieldKeyEmpty) {
                errorContext.addErrorInfo(String.format("字段标识为“%s”的字段名称未配置", getFieldKey()));
            } else {
                errorContext.addErrorInfo("字段名称未配置");
            }
        }
    }

    public static Field convertFieldFromClassType(String classType, String fieldKey, String modelKey) {
        if (StringUtils.isBlank(classType) || Void.class.getName().equals(classType)) {
            return null;
        }

        if (classType.startsWith("java.util.List")) {
            if (StringUtils.isNotBlank(modelKey)) {
                return new ArrayField(fieldKey, fieldKey, new ModelField("element", new RelatedModel(modelKey)));
            } else {
                return new ArrayField(fieldKey, fieldKey, new ObjectField("element"));
            }
        }

        if (classType.startsWith("java.util.Map")) {
            return new ObjectField(fieldKey, fieldKey);
        }

        if (classType.startsWith("java.util.Date")) {
            return new BaseField(fieldKey, FieldType.DateTime);
        }

        if (classType.startsWith("java.lang")) {
            try {
                Class<?> parameterType = Class.forName(classType);
                if (CharSequence.class.isAssignableFrom(parameterType)) {
                    return new BaseField(fieldKey, FieldType.Text);
                } else if (parameterType.isArray()) {
                    return new ArrayField(fieldKey, fieldKey, new ObjectField("element"));
                } else if (parameterType == Date.class) {
                    return new BaseField(fieldKey, FieldType.DateTime);
                } else if (parameterType == Boolean.class || parameterType == boolean.class) {
                    return new BaseField(fieldKey, FieldType.Boolean);
                } else if (parameterType == long.class
                        || parameterType == int.class
                        || parameterType == short.class
                        || parameterType == float.class
                        || parameterType == double.class) {
                    return new BaseField(fieldKey, FieldType.Number);
                } else if (Number.class.isAssignableFrom(parameterType)) {
                    return new BaseField(fieldKey, FieldType.Number);
                }
            } catch (ClassNotFoundException ignore) {
            }
        }

        if (StringUtils.isNotBlank(modelKey)) {
            return new ModelField(fieldKey, new RelatedModel(modelKey));
        } else {
            return new ObjectField(fieldKey, fieldKey);
        }
    }
}

