package io.terminus.trantor2.service.dsl.properties.validation;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ErrorInfo implements Serializable {

    private static final long serialVersionUID = 2230985213282299167L;

    private String errorMsg;

    private boolean strongPrompt;

    public ErrorInfo(boolean strong, String errorMsg) {
        this.errorMsg = errorMsg;
        this.strongPrompt = strong;
    }
}
