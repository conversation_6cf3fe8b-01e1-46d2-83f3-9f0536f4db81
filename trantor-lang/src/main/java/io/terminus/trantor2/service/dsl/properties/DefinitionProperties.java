package io.terminus.trantor2.service.dsl.properties;

import io.terminus.iam.api.response.permission.FieldRule;
import io.terminus.trantor2.service.dsl.enums.Propagation;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.List;

/**
 * DefinitionProperties
 *
 * <AUTHOR> Created on 2025/9/8 20:25
 */
@Setter
@Getter
public abstract class DefinitionProperties extends AbstractProperties {
    @Serial
    private static final long serialVersionUID = -2956773872147577096L;

    /**
     * 事务
     */
    protected Propagation transactionPropagation;

    /**
     * 功能权限项Key
     */
    protected String permissionKey;

    /**
     * 入参字段权限规则
     */
    protected List<FieldRule> inputFieldRules;

    /**
     * 出参字段权限规则
     */
    protected List<FieldRule> outputFieldRules;

    /**
     * 服务的finally
     */
    protected List<ErrorConfig> serviceFinally;
}
