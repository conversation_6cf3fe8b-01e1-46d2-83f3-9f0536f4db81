package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * 多条数据添加配置
 *
 * <AUTHOR>
 * @date 2022/10/20
 */
@Deprecated
@Setter
@Getter
public class MultiCreateDataProperties extends CreateDataProperties {

    private static final long serialVersionUID = 7664077487780282226L;

    /**
     * 数据源模型
     */
    private VarValue datasource;

    @Override
    public void validate(ValidatorContext errorContext) {
        errorContext.addErrorInfo("该节点不在支持");
    }
}
