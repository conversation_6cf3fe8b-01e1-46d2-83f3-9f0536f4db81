package io.terminus.trantor2.service.common.meta;

import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ModuleBaseMeta;
import io.terminus.trantor2.meta.resource.ResourceProps;
import io.terminus.trantor2.service.common.enums.DigitalStaffType;
import io.terminus.trantor2.service.dsl.enums.BasicAbility;
import io.terminus.trantor2.service.dsl.enums.GeneralProfessionalSkill;
import io.terminus.trantor2.service.dsl.enums.ReasoningType;
import io.terminus.trantor2.service.dsl.properties.Skill;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * AIAgentMeta
 *
 * <AUTHOR> Created on 2025/3/13 17:13
 */
@Setter
@Getter
public class AIDigitalStaffMeta extends ModuleBaseMeta<AIDigitalStaffMeta.Props> {
    private static final long serialVersionUID = 7371081674553617873L;

    @Override
    public MetaType getMetaType() {
        return MetaType.AIDigitalStaff;
    }

    @Data
    public static class Props implements ResourceProps {
        /**
         * 类型
         */
        private DigitalStaffType type;

        /**
         * 核心职责
         */
        private String coreResponsibilities;

        /**
         * 基础能力（勾选项）
         */
        private List<BasicAbility> basicAbilities;

        /**
         * 通用专业技能（勾选项）
         */
        private List<GeneralProfessionalSkill> generalProfessionalSkills;

        /**
         * 技能列表
         */
        private List<Skill> skills;

        /**
         * 推理类型（当选择 DEEP_REASONING 时需要设置）
         */
        private ReasoningType reasoningType;

        /**
         * 是否为启用状态
         */
        private Boolean isEnabled = true;
    }
}
