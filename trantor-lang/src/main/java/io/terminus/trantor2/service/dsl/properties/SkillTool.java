package io.terminus.trantor2.service.dsl.properties;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.terminus.trantor2.service.dsl.enums.ToolType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * SkillTool
 *
 * <AUTHOR> Created on 2025/3/15 23:14
 */
@Setter
@Getter
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME, visible = true,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "type"
)
@JsonSubTypes({
        @JsonSubTypes.Type(value = ServiceTool.class, name = "service"),
        @JsonSubTypes.Type(value = McpTool.class, name = "mcp"),
        @JsonSubTypes.Type(value = HttpTool.class, name = "http")
})
@ToString(of = {"type"})
public class SkillTool extends Tool {
    private static final long serialVersionUID = 863241933743020263L;

    protected ToolType type = ToolType.Service;
}
