package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Deprecated
@Setter
@Getter
public class UpdateDataProperties extends DataProcessingProperties {

    private static final long serialVersionUID = -6535673843239678110L;

    /**
     * 字段行列表
     */
    private List<FieldEntry> inputMapping;

    /**
     * 更新的条件
     */
    private ConditionGroup conditionGroup;

    @Override
    public void validate(ValidatorContext errorContext) {
        errorContext.addErrorInfo(false, "该节点已升级新版本，请尽快更换新节点");
    }

}
