package io.terminus.trantor2.service.dsl.properties;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * ConvertDataProperties
 *
 * <AUTHOR> Created on 2023/7/6 15:23
 */
@Setter
@Getter
public class ConvertDataProperties extends AbstractProperties {
    private static final long serialVersionUID = 5020744873886200258L;

    private VarValue sourceValue;
    private ConditionGroup sourceConditionGroup;

    private VarValue targetVariable;
    private VarValue targetLoopVariable;
    private ConditionGroup targetConditionGroup;

    private FieldAssignConfig existsConfig;
    private FieldAssignConfig notExistsConfig;

    @Override
    public void validate(ValidatorContext errorContext) {
        if (sourceValue == null) {
            errorContext.addErrorInfo("源单据变量未配置");
        } else {
            errorContext.addTag("源单据变量");
            sourceValue.validate(errorContext);
            errorContext.removeTag();
        }

        if (targetVariable == null) {
            errorContext.addErrorInfo("目标单据变量未配置");
        } else {
            errorContext.addTag("目标单据变量");
            targetVariable.validate(errorContext);
            errorContext.removeTag();
        }
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FieldAssignConfig {
        private List<ArrayFieldAssignConfig> arrayFieldConfigs;
        private OtherFieldAssignConfig otherFieldConfig;
    }

    @Setter
    @Getter
    public static class ArrayFieldAssignConfig {
        private String fieldKey;
        private RelatedModel loopRelatedModel;
        private VarValue loopVariable;
        private ConditionGroup filterConditionGroup;
        @ArraySchema(schema = @Schema(implementation = ArrayFieldAssignConfig.class))
        private List<ArrayFieldAssignConfig> arrayFieldConfigs;
        private OtherFieldAssignConfig otherFieldConfig;
    }

    @Setter
    @Getter
    public static class OtherFieldAssignConfig {
        private List<StringEntry> fieldMapping;
    }
}
