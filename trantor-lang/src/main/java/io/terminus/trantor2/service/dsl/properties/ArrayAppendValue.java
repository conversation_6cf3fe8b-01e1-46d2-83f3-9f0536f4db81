package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.service.dsl.enums.FieldType;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 集合追加值，该变量只会返回一条数据值
 *
 * <AUTHOR> Created on 2023/7/20 17:46
 */
@Setter
@Getter
public class ArrayAppendValue extends Value {

    private static final long serialVersionUID = 8307844173835236140L;

    /**
     * 变量值，追加的单条数据
     */
    private VarValue appendValue;

    /**
     * 当该字段不为空，则表明集合的追加的一条数据
     */
    private List<AssignEntry> singleItemFieldMapping;

    /**
     * 字段类型
     */
    private FieldType fieldType;

    @Override
    public void validate(ValidatorContext errorContext) {
        if (appendValue != null) {
            errorContext.addTag("数组追加右值");
            appendValue.validate(errorContext);
            errorContext.removeTag();
        }
        if (singleItemFieldMapping != null) {
            errorContext.addTag("数组自定义追加右值");
            singleItemFieldMapping.forEach(i -> i.validate(errorContext));
            errorContext.removeTag();
        }
    }
}
