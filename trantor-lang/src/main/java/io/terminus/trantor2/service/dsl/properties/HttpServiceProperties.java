package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.meta.schema.TMetaAvailable;
import io.terminus.trantor2.meta.schema.TPlatformVersion;
import io.terminus.trantor2.service.dsl.enums.BodyType;
import io.terminus.trantor2.service.dsl.enums.HttpMethod;
import io.terminus.trantor2.service.dsl.enums.HttpType;
import io.terminus.trantor2.service.dsl.enums.VariableType;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * HttpServiceProperties
 *
 * <AUTHOR> Created on 2023/2/21 15:21
 */
@Setter
@Getter
public class HttpServiceProperties extends AbstractProperties {
    private static final long serialVersionUID = 489657189049300873L;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 请求URl
     */
    private String url;

    /**
     * 请求方法, GET, POST
     */
    private HttpMethod method = HttpMethod.POST;

    /**
     * 请求头
     */
    private List<StringEntry> headers;

    /**
     * 路径参数
     */
    private List<StringEntry> pathVariables;

    /**
     * 入参映射
     */
    private List<StringEntry> inputMapping;

    /**
     * body体的类型
     */
    private BodyType bodyType = BodyType.VALUE;

    /**
     * 入参body
     */
    private Value body;

    /**
     * 入参body
     */
    private String jsonBody;

    /**
     * HTTP请求类型
     */
    @TMetaAvailable(TPlatformVersion.V240930)
    private HttpType httpType;

    /**
     * 是否stream模式
     */
    @Deprecated
    private Boolean stream;

    /**
     * 出参结构，HTTP请求是单出参
     */
    private List<Field> output;

    /**
     * 出参赋值
     */
    protected OutputAssign outputAssign;

    public HttpType getHttpType() {
        if (httpType != null) {
            return httpType;
        }
        // 兼容一下旧的stream模式
        if (Boolean.TRUE.equals(stream)) {
            httpType = HttpType.STREAM;
            stream = null;
        }
        return httpType;
    }

    @Override
    public void validate(ValidatorContext errorContext) {
        if (url == null || url.isEmpty()) {
            errorContext.addErrorInfo("HTTP请求地址未配置");
        }

        if (headers != null) {
            errorContext.addTag("HTTP请求头");
            headers.forEach(h -> h.validate(errorContext));
            errorContext.removeTag();
        }

        if (inputMapping != null) {
            errorContext.addTag("HTTP请求入参");
            inputMapping.forEach(i -> i.validate(errorContext));
            errorContext.removeTag();
        }

        if (url != null && url.contains("{") && url.contains("}") && (pathVariables == null || pathVariables.isEmpty())) {
            errorContext.addErrorInfo("HTTP请求地址中存在未配置的路径参数");
        }

        if (pathVariables != null) {
            errorContext.addTag("HTTP请求路径参数");
            pathVariables.forEach(p -> p.validate(errorContext));
            errorContext.removeTag();
        }

        if (bodyType == BodyType.VALUE && body != null) {
            errorContext.addTag("HTTP请求body");
            body.validate(errorContext);
            errorContext.removeTag();
        }

        if (output != null) {
            errorContext.addTag("HTTP请求出参");
            output.forEach(o -> o.validate(errorContext));
            errorContext.removeTag();

            if (outputAssign != null && outputAssign.isSystemDefault()) {
                errorContext.getGlobalVariable()
                        .addElement(new ObjectField(VariableType.NODE_OUTPUT.formatKey(errorContext.getNodeKey()), output));
            } else {
                errorContext.addVariable(new ObjectField(VariableType.NODE_OUTPUT.formatKey(errorContext.getNodeKey()), output));
            }
        }

        if (outputAssign != null) {
            errorContext.addTag("出参赋值");
            outputAssign.validate(errorContext);
            errorContext.removeTag();
        }
    }
}
