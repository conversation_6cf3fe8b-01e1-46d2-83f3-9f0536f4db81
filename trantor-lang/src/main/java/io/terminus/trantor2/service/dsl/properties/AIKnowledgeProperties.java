package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.service.dsl.enums.VariableType;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * AIKnowledgeProperties
 *
 * <AUTHOR> Created on 2023/9/12 14:47
 */
@Setter
@Getter
public class AIKnowledgeProperties extends AbstractProperties {
    private static final long serialVersionUID = 4738446017308788811L;

    /**
     * 知识库
     */
    private List<Knowledge> knowledgeList;

    /**
     * 相似度
     */
    private Float similarity;

    /**
     * 单次搜索数量
     */
    private Integer searchCount;

    /**
     * 检索器类型
     */
    private String retrieverType;

    /**
     * 空搜索回复内容
     */
    private String emptyReturn;

    /**
     * 输出参数定义
     */
    private List<Field> output;
    /**
     * 出参赋值
     */
    protected OutputAssign outputAssign;

    /**
     * 知识库
     */
    @Data
    public static class Knowledge {
        private String code;
        private String name;
    }

    @Override
    public void validate(ValidatorContext errorContext) {
        if (knowledgeList == null || knowledgeList.isEmpty()) {
            errorContext.addErrorInfo("知识库未配置");
        }

        if (similarity == null) {
            errorContext.addErrorInfo("相似度未配置");
        }

        if (searchCount == null) {
            errorContext.addErrorInfo("单次搜索数量未配置");
        }

        if (output != null) {
            errorContext.addTag("知识库搜索出参");
            output.forEach(o -> o.validate(errorContext));
            errorContext.removeTag();

            if (outputAssign != null && outputAssign.isSystemDefault()) {
                errorContext.getGlobalVariable()
                    .addElement(new ObjectField(VariableType.NODE_OUTPUT.formatKey(errorContext.getNodeKey()), output));
            } else {
                errorContext.addVariable(new ObjectField(VariableType.NODE_OUTPUT.formatKey(errorContext.getNodeKey()), output));
            }
        }

        if (outputAssign != null) {
            errorContext.addTag("出参赋值");
            outputAssign.validate(errorContext);
            errorContext.removeTag();
        }
    }
}
