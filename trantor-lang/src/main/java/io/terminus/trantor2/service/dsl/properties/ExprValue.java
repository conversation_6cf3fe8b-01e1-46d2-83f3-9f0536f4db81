package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.service.dsl.enums.FieldType;
import io.terminus.trantor2.service.dsl.properties.validation.Validator;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 前端表达式值
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class ExprValue extends Value implements Validator, Serializable {
    private static final long serialVersionUID = -6824519235586140554L;

    private String exprValue;
    private FieldType fieldType;

    @Override
    public void validate(ValidatorContext errorContext) {
    }
}
