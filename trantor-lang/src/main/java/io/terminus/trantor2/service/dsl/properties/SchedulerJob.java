package io.terminus.trantor2.service.dsl.properties;

import lombok.Data;

import java.io.Serializable;

@Data
public class SchedulerJob implements Serializable {
    private static final long serialVersionUID = -1419392492588100L;

    /**
     * 是否启用调度作业任务
     */
    private boolean enable;
    /**
     * 调度作业标识
     */
    private String jobKey;
    /**
     * 调度作业名称
     */
    private String jobName;
    /**
     * 调度作业类型
     */
    private String jobType;
    /**
     * 调度作业描述
     */
    private String desc;
    /**
     * 调度表达式（Crontab/延迟时间表达式）
     */
    private String expression;
    /**
     * 调度作业参数
     */
    private String params;
    /**
     * 调度执行器类型
     */
    private String execType = "TService";
}
