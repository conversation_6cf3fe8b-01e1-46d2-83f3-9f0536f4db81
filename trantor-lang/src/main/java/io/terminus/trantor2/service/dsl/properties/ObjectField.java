package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.service.dsl.enums.FieldType;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 对象类型字段
 *
 * <AUTHOR>
 * @date 2022/11/08
 */
@Setter
@Getter
@NoArgsConstructor
public class ObjectField extends AbstractObjectField {

    private static final long serialVersionUID = -2975079121880290163L;

    /**
     * 子字段类型配置
     */
    protected List<Field> elements;

    public ObjectField(String fieldKey, String fieldName) {
        this(fieldKey, fieldName, (List<Field>) null);
    }

    public ObjectField(String fieldKey, List<Field> elements) {
        this(fieldKey, fieldKey, elements);
    }

    public ObjectField(String fieldKey, Field... elements) {
        this(fieldKey, fieldKey, Arrays.stream(elements).collect(Collectors.toList()));
    }

    public ObjectField(FieldType fieldType, String fieldKey, Field... elements) {
        this(fieldType, fieldKey, fieldKey, Arrays.stream(elements).collect(Collectors.toList()));
    }

    public ObjectField(String fieldKey, String fieldName, Field... elements) {
        this(fieldKey, fieldName, Arrays.stream(elements).collect(Collectors.toList()));
    }

    public ObjectField(String fieldKey, String fieldName, List<Field> elements) {
        this(FieldType.Object, fieldKey, fieldName, elements);
    }

    public ObjectField(FieldType fieldType, String fieldKey, String fieldName, List<Field> elements) {
        this.fieldKey = fieldKey;
        this.fieldName = fieldName;
        this.fieldType = fieldType;
        this.elements = elements;
    }

    public void addElement(Field e) {
        if (e == null) {
            return;
        }
        if (elements == null) {
            elements = new ArrayList<>();
        }
        elements.add(e);
    }

    public Field getElement(String fieldKey) {
        if (elements == null) {
            return null;
        }
        return elements.stream().filter(f -> f.getFieldKey().equals(fieldKey)).findFirst().orElse(null);
    }

    @Override
    public void validate(ValidatorContext errorContext) {
        super.validate(errorContext);
        // 允许element为空，当不为空的时候，需要校验字段是否合法
        if (fieldType == FieldType.Pageable || fieldType == FieldType.Paging) {
            // 如果是Pageable类型，则跳过校验
            return;
        }
        if (elements != null) {
            errorContext.addTag("子字段");
            elements.forEach(f -> f.validate(errorContext));
            errorContext.removeTag();
        }
    }
}
