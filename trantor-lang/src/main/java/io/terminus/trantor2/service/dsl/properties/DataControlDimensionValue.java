package io.terminus.trantor2.service.dsl.properties;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.permission.PermissionMeta;
import io.terminus.trantor2.service.dsl.enums.FieldType;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.*;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 数据权限-数据控权维度
 *
 * <AUTHOR>
 * 2024/6/17 11:26
 **/
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据控权维度值对象")
public class DataControlDimensionValue extends Value implements Serializable {

    @Schema(description = "字段类型")
    private FieldType fieldType;

    /**
     * 数据控权维度标识，由于数据控权维度使用{@link PermissionMeta}存储，因此等价于 permissionKey
     */
    @Schema(description = "数据控权维度标识")
    private String dataControlDimensionKey;

    @Schema(description = "数据控权维度名称")
    private String dataControlDimensionName;

    @Schema(description = "数据控权维度数据范围")
    private List<ValueRange> valueRanges;

    @Override
    public FieldType getFieldType() {
        return fieldType;
    }

    @Override
    public void validate(ValidatorContext errorContext) {
        if (Objects.isNull(dataControlDimensionKey)) {
            errorContext.addErrorInfo("数据控权维度未配置");
        }
    }

    @Setter
    @Getter
    @EqualsAndHashCode
    public static class ValueRange implements Serializable {

        @Schema(description = "实际值")
        private Object value;
        @Schema(description = "展示标签，该参数只会冗余展示使用，存在与真实业务数据不一致情况")
        private String label;
    }
}
