package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.service.dsl.properties.validation.Validator;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * QueryModelFields
 *
 * <AUTHOR> Created on 2024/5/23 15:55
 */
@Setter
@Getter
public class QueryModelFields implements Validator {

    /**
     * 需要查询的模型
     */
    private String modelKey;

    /**
     * 代表查询所有的基本类型字段（关联模型仅查询出id）
     */
    private boolean allFields;

    /**
     * 模型的字段
     */
    private List<QueryField> queryFields;

    @Override
    public void validate(ValidatorContext errorContext) {
        if (queryFields != null && !queryFields.isEmpty()) {
            errorContext.addTag("[" + modelKey + "]模型字段配置");
            queryFields.forEach(queryField -> queryField.validate(errorContext));
            errorContext.removeTag();
        }
        if (!allFields && (queryFields == null || queryFields.isEmpty())) {
            errorContext.addErrorInfo("[" + modelKey + "]模型的查询字段未配置");
        }
    }
}
