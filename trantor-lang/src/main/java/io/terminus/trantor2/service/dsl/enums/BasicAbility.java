package io.terminus.trantor2.service.dsl.enums;

/**
 * 基础能力
 */
public enum BasicAbility {
    // 听（通过音频理解用户需求）
    LISTEN_AUDIO,

    // 说（通过语音回答用户问题）
    SPEAK_AUDIO,

    // 读（读取文字）
    READ_TEXT,

    // 读（读取图片）
    READ_IMAGE,

    // 读（读取视频）
    READ_VIDEO,

    // 读（读取常见办公文档）
    READ_DOCUMENT,

    // 写（生成文字）
    WRITE_TEXT,

    // 写（生成图片）
    WRITE_IMAGE,

    // 写（生成视频）
    WRITE_VIDEO,

    // 写（生成常见办公文档）
    WRITE_DOCUMENT,
}
