package io.terminus.trantor2.service.dsl.properties;

import io.terminus.trantor2.service.dsl.enums.VariableType;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * StartProperties
 *
 * <AUTHOR> Created on 2023/2/16 20:34
 */
@Setter
@Getter
public class StartProperties extends AbstractProperties {
    private static final long serialVersionUID = 7194713440670853107L;

    /**
     * 入参结构
     */
    private List<Field> input;

    /**
     * 出参数结构
     */
    private List<Field> output;

    /**
     * 全局变量
     */
    private List<Field> globalVariable;

    public void addOutputFields(List<Field> outputFields) {
        if (outputFields == null || outputFields.isEmpty()) {
            return;
        }
        if (this.output == null) {
            this.output = new ArrayList<>(outputFields.size());
        }
        for (Field out : outputFields) {
            if (this.output.stream().noneMatch(o -> o.getFieldKey().equals(out.getFieldKey()))) {
                this.output.add(out);
            }
        }
    }

    @Override
    public void validate(ValidatorContext errorContext) {
        if (input != null) {
            errorContext.addTag("服务入参");
            input.forEach(f -> f.validate(errorContext));
            errorContext.removeTag();
            errorContext.addVariable(new ObjectField(VariableType.REQUEST.getKey(), input));
        }
        if (globalVariable != null) {
            errorContext.addTag("全局变量");
            globalVariable.forEach(f -> f.validate(errorContext));
            errorContext.removeTag();
            errorContext.addVariable(new ObjectField(VariableType.GLOBAL.getKey(), globalVariable));
        }
        if (output != null) {
            errorContext.addTag("服务出参");
            output.forEach(f -> f.validate(errorContext));
            errorContext.removeTag();
            errorContext.addVariable(new ObjectField(VariableType.OUTPUT.getKey(), output));
        }
    }
}
