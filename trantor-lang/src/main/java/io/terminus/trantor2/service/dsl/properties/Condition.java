package io.terminus.trantor2.service.dsl.properties;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.terminus.trantor2.service.dsl.BaseElement;
import io.terminus.trantor2.service.dsl.properties.validation.Validator;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    property = "type")
@JsonSubTypes({
    @JsonSubTypes.Type(value = ConditionGroup.class, name = "ConditionGroup"),
    @JsonSubTypes.Type(value = ConditionLeaf.class, name = "ConditionLeaf"),
    @JsonSubTypes.Type(value = ConditionVariable.class, name = "ConditionVariable"),
})
public abstract class Condition extends BaseElement implements Validator, Serializable {

    private static final long serialVersionUID = 5727056789308433096L;

}
