package io.terminus.trantor2.iam.service.impl;

import io.terminus.iam.api.request.application.ApplicationAuthorizationBatchUpdateConditionParams;
import io.terminus.iam.api.request.application.ApplicationAuthorizationBatchUpdateParams;
import io.terminus.iam.api.request.application.ApplicationAuthorizationListParams;
import io.terminus.iam.api.request.application.ApplicationAuthorizationPagingParams;
import io.terminus.iam.api.request.application.ApplicationAuthorizationParams;
import io.terminus.iam.api.response.PageResult;
import io.terminus.iam.api.response.application.ApplicationAuthorization;
import io.terminus.trantor2.common.iam.TrantorIAMClientFactory;
import io.terminus.trantor2.iam.exception.IAMInvokedException;
import io.terminus.trantor2.iam.service.TrantorIAMApplicationAuthorizationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> qianjin
 * @since : 2023/7/11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrantorIAMApplicationAuthorizationServiceImpl implements TrantorIAMApplicationAuthorizationService {

    private final TrantorIAMClientFactory trantorIAMClientFactory;


    @Override
    public PageResult<ApplicationAuthorization> pagingByApplicationIdAndPrincipalName(ApplicationAuthorizationPagingParams params) {
        try {
            PageResult<ApplicationAuthorization> execute = trantorIAMClientFactory.getIAMClient().applicationAuthorizationManagementClient().paging(params).execute();
            return execute;
        } catch (Exception e) {
            log.error("iam sdk application authorization paging method exec error", e);
            throw new IAMInvokedException(e);
        }
    }

    @Override
    public List<ApplicationAuthorization> addAuthorizationList(ApplicationAuthorizationListParams params) {
        try {
            List<ApplicationAuthorization> execute = trantorIAMClientFactory.getIAMClient().applicationAuthorizationManagementClient().batchAdd(params).execute();
            return execute;
        } catch (Exception e) {
            log.error("iam sdk application authorization batch add method exec error", e);
            throw new IAMInvokedException(e);
        }
    }

    @Override
    public Boolean updateAuthorizationList(ApplicationAuthorizationBatchUpdateParams params) {
        try {
            return trantorIAMClientFactory.getIAMClient().applicationAuthorizationManagementClient().batchUpdate(params).execute();
        } catch (Exception e) {
            log.error("iam sdk application authorization batch update method exec error", e);
            throw new IAMInvokedException(e);
        }
    }

    @Override
    public Boolean updateAuthorizationListByCondition(ApplicationAuthorizationBatchUpdateConditionParams params) {
        try {
            return trantorIAMClientFactory.getIAMClient().applicationAuthorizationManagementClient().batchUpdateByCondition(params).execute();
        } catch (Exception e) {
            log.error("iam sdk application authorization batch update by condition method exec error", e);
            throw new IAMInvokedException(e);
        }
    }

    @Override
    public Boolean updateAuthorizationStatus(ApplicationAuthorizationParams params, Boolean targetStatus) {
        try {
            Boolean result;
            if (targetStatus) {
                result = trantorIAMClientFactory.getIAMClient().applicationAuthorizationManagementClient().statusEnabled(params).execute();
            } else {
                result = trantorIAMClientFactory.getIAMClient().applicationAuthorizationManagementClient().statusDisabled(params).execute();
            }
            return result;
        } catch (Exception e) {
            log.error("iam sdk application authorization update status method exec error", e);
            throw new IAMInvokedException(e);
        }
    }

    @Override
    public Boolean updateAuthorizationEnabled(ApplicationAuthorizationParams params, Boolean targetStatus) {
        try {
            Boolean result;
            if (targetStatus) {
                result = trantorIAMClientFactory.getIAMClient().applicationAuthorizationManagementClient().authorizationEnabled(params).execute();
            } else {
                result = trantorIAMClientFactory.getIAMClient().applicationAuthorizationManagementClient().authorizationDisabled(params).execute();
            }
            return result;
        } catch (Exception e) {
            log.error("iam sdk application authorization update authorization method exec error", e);
            throw new IAMInvokedException(e);
        }
    }

    @Override
    public Boolean deleteAuthorizationList(ApplicationAuthorizationListParams params) {
        try {
            Boolean execute = trantorIAMClientFactory.getIAMClient().applicationAuthorizationManagementClient().batchDelete(params).execute();
            return execute;
        } catch (Exception e) {
            log.error("iam sdk application authorization batch delete method exec error", e);
            throw new IAMInvokedException(e);
        }
    }

    @Override
    public Boolean deleteAuthorization(ApplicationAuthorizationParams params) {
        try {
            Boolean execute = trantorIAMClientFactory.getIAMClient().applicationAuthorizationManagementClient().delete(params).execute();
            return execute;
        } catch (Exception e) {
            log.error("iam sdk application authorization delete method exec error", e);
            throw new IAMInvokedException(e);
        }
    }
}
