package io.terminus.trantor2.iam.dto.webhook;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.iam.api.request.webhook.WebhookPageParams;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <EMAIL>
 * 2024/3/27 08:23
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "webhook分页请求参数")
public class WebhookPagingRequest extends WebhookPageParams {

    @Schema(description = "门户code")
    private String portalCode;
}
