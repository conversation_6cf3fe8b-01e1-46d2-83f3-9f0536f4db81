package io.terminus.trantor2.iam.cache;

import lombok.Getter;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.Serializable;
import java.util.Arrays;

/**
 * 参考{@link org.springframework.cache.interceptor.SimpleKey}
 * <p>
 * 允许通过对象数组构建缓存key对象，在执行缓存检索时，会遍历数组对象获取每个元数hashCode从而计算最终整个数组对象的hashCode
 * 推荐的入参：基础类型或其封装类型、字符串类型、枚举，及上述类型的集合或映射
 * 不允许的入参：对象类型及对象集合或映射
 * <p>
 * A simple key as returned from the {@link org.springframework.cache.interceptor.SimpleKeyGenerator}.
 *
 * <AUTHOR>
 * <AUTHOR>
 * @see org.springframework.cache.interceptor.SimpleKeyGenerator
 * @since 4.0
 */
public class Cache<PERSON>ey implements Serializable {

    /**
     * An empty key.
     */
    public static final CacheKey EMPTY = new CacheKey();

    @Getter
    private final Object[] params;

    // Effectively final, just re-calculated on deserialization
    private transient int hashCode;

    /**
     * Create a new {@link CacheKey} instance.
     *
     * @param elements the elements of the key
     */
    private CacheKey(Object... elements) {
        Assert.notNull(elements, "Elements must not be null");
        this.params = elements.clone();
        // Pre-calculate hashCode field
        this.hashCode = Arrays.deepHashCode(this.params);
    }

    public static CacheKey of(Object... params) {
        if (params.length == 0) {
            return CacheKey.EMPTY;
        }
        return new CacheKey(params);
    }

    @Override
    public boolean equals(@Nullable Object other) {
        return (this == other ||
                (other instanceof CacheKey && Arrays.deepEquals(this.params, ((CacheKey) other).params)));
    }

    @Override
    public final int hashCode() {
        // Expose pre-calculated hashCode field
        return this.hashCode;
    }

    @Override
    public String toString() {
        return getClass().getSimpleName() + " [" + StringUtils.arrayToCommaDelimitedString(this.params) + "]";
    }

    private void readObject(ObjectInputStream ois) throws IOException, ClassNotFoundException {
        ois.defaultReadObject();
        // Re-calculate hashCode field on deserialization
        this.hashCode = Arrays.deepHashCode(this.params);
    }
}
