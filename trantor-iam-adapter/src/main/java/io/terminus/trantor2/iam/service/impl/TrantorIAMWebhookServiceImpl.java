package io.terminus.trantor2.iam.service.impl;

import io.terminus.iam.api.request.webhook.WebhookDebugParams;
import io.terminus.iam.api.request.webhook.WebhookHistoryPageParams;
import io.terminus.iam.api.request.webhook.WebhookPageParams;
import io.terminus.iam.api.request.webhook.WebhookSaveParams;
import io.terminus.iam.api.response.PageResult;
import io.terminus.iam.api.response.webhook.WebhookHistoryResult;
import io.terminus.iam.api.response.webhook.WebhookResult;
import io.terminus.iam.api.response.webhook.WebhookTypeResult;
import io.terminus.trantor2.common.iam.TrantorIAMClientFactory;
import io.terminus.trantor2.iam.exception.IAMInvokedException;
import io.terminus.trantor2.iam.service.TrantorIAMWebhookService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by hedy on 2024/7/31.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TrantorIAMWebhookServiceImpl implements TrantorIAMWebhookService {

    private final TrantorIAMClientFactory trantorIAMClientFactory;


    @Override
    public PageResult<WebhookResult> pagingWebhook(WebhookPageParams params) {
        try {
            return trantorIAMClientFactory.getIAMClient().webhookClient().paging(params).execute();
        } catch (Exception e) {
            log.error("iam sdk pagingWebhook method exec error", e);
            throw new IAMInvokedException(e);
        }
    }

    @Override
    public PageResult<WebhookHistoryResult> pagingWebhookHistory(WebhookHistoryPageParams params) {
        try {
            return trantorIAMClientFactory.getIAMClient().webhookClient().pageWebhookHistory(params).execute();
        } catch (Exception e) {
            log.error("iam sdk pagingWebhookHistory method exec error", e);
            throw new IAMInvokedException(e);
        }
    }

    @Override
    public List<WebhookTypeResult> allWebhookEvent() {
        try {
            return trantorIAMClientFactory.getIAMClient().webhookClient().allEvent().execute();
        } catch (Exception e) {
            log.error("iam sdk allWebhookEvent method exec error", e);
            throw new IAMInvokedException(e);
        }
    }

    @Override
    public WebhookResult saveWebhook(WebhookSaveParams params) {
        try {
            return trantorIAMClientFactory.getIAMClient().webhookClient().save(params).execute();
        } catch (Exception e) {
            log.error("iam sdk saveWebhook method exec error", e);
            throw new IAMInvokedException(e);
        }
    }

    @Override
    public String webhookDebug(WebhookDebugParams params) {
        try {
            return trantorIAMClientFactory.getIAMClient().webhookClient().debug(params).execute();
        } catch (Exception e) {
            log.error("iam sdk webhookDebug method exec error", e);
            throw new IAMInvokedException(e);
        }
    }

    @Override
    public WebhookResult findWebhookById(Long id) {
        try {
            return trantorIAMClientFactory.getIAMClient().webhookClient().findById(id).execute();
        } catch (Exception e) {
            log.error("iam sdk findWebhookById method exec error", e);
            throw new IAMInvokedException(e);
        }
    }

    @Override
    public Boolean webhookEnable(Long id) {
        try {
            return trantorIAMClientFactory.getIAMClient().webhookClient().enable(id).execute();
        } catch (Exception e) {
            log.error("iam sdk webhookEnable method exec error", e);
            throw new IAMInvokedException(e);
        }
    }

    @Override
    public Boolean webhookDisable(Long id) {
        try {
            return trantorIAMClientFactory.getIAMClient().webhookClient().disable(id).execute();
        } catch (Exception e) {
            log.error("iam sdk webhookDisable method exec error", e);
            throw new IAMInvokedException(e);
        }
    }

    @Override
    public Boolean webhookDelete(Long id) {
        try {
            return trantorIAMClientFactory.getIAMClient().webhookClient().delete(id).execute();
        } catch (Exception e) {
            log.error("iam sdk webhookDelete method exec error", e);
            throw new IAMInvokedException(e);
        }
    }

    @Override
    public Boolean historyRetry(Long historyId) {
        try {
            return trantorIAMClientFactory.getIAMClient().webhookClient().historyRetry(historyId).execute();
        } catch (Exception e) {
            log.error("iam sdk historyRetry method exec error", e);
            throw new IAMInvokedException(e);
        }
    }
}
