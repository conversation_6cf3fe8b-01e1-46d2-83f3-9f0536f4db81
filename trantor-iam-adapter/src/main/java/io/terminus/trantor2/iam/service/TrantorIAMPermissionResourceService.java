package io.terminus.trantor2.iam.service;

import io.terminus.iam.api.request.permission.v2.PermissionResourceCreateParams;
import io.terminus.iam.api.request.permission.v2.PermissionResourceDeleteOrUpdateOrCreateParams;
import io.terminus.iam.api.request.permission.v2.PermissionResourceDeleteParams;
import io.terminus.iam.api.request.permission.v2.PermissionResourceFindParams;
import io.terminus.iam.api.request.permission.v2.PermissionResourceUpdateParams;
import io.terminus.iam.api.response.PageResult;
import io.terminus.iam.api.response.permission.v2.PermissionResource;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.Collection;
import java.util.List;

/**
 * 权限资源服务
 */
public interface TrantorIAMPermissionResourceService {

    /**
     * 创建权限资源
     *
     * @param paramsList 创建参数
     * @return 权限资源列表
     */
    List<PermissionResource> create(@Valid @NotEmpty Collection<PermissionResourceCreateParams> paramsList);

    /**
     * 先移除重复已存在记录后再新建
     *
     * @param paramsList 创建参数
     * @return 权限资源列表
     */
    Collection<PermissionResource> removeDuplicatedAndThenCreatePermissionResource(@Valid @NotEmpty Collection<PermissionResourceCreateParams> paramsList);

    /**
     * 集删除、修改、创建一体的聚合接口，保证操作原子性
     *
     * @param params 请求参数
     */
    void deleteOrUpdateOrCreate(PermissionResourceDeleteOrUpdateOrCreateParams params);

    /**
     * 查询权限资源
     *
     * @param params 查询参数
     * @return 权限资源列表
     */
    PageResult<PermissionResource> find(PermissionResourceFindParams params);

    /**
     * 查询权限资源
     *
     * @param params 查询参数
     * @return 权限资源列表
     */
    List<PermissionResource> findList(PermissionResourceFindParams params);

    /**
     * 更新权限资源
     *
     * @param paramsList 更新参数
     */
    void update(@Valid @NotEmpty List<PermissionResourceUpdateParams> paramsList);

    /**
     * 删除权限资源
     *
     * @param params 删除参数
     */
    void delete(PermissionResourceDeleteParams params);
}
