package io.terminus.trantor2.iam.cache;

import io.terminus.iam.api.response.role.Role;
import io.terminus.trantor2.properties.PermissionProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * 2024/6/14 17:59
 **/
@Component
public class RoleAssignCache extends AbstractIAMCache<CacheKey, List<Role>> {

    public RoleAssignCache(PermissionProperties permissionProperties, IAMAdapterEventPublisher publisher) {
        super(permissionProperties, publisher);
    }

    @Override
    public Type type() {
        return Type.ROLE_ASSIGN;
    }
}
