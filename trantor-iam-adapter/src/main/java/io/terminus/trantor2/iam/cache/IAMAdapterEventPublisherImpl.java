package io.terminus.trantor2.iam.cache;

import io.terminus.trantor2.common.event.Event;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * <AUTHOR>
 * 2024/9/25 21:23
 **/
@Component
public class IAMAdapterEventPublisherImpl implements IAMAdapterEventPublisher, ApplicationEventPublisherAware {

    private static final String IAM_ADAPTER_EVENT_TOPIC = "IAM_ADAPTER_EVENT_TOPIC";

    private final RTopic topic;
    private ApplicationEventPublisher publisher;

    public IAMAdapterEventPublisherImpl(RedissonClient redissonClient) {
        this.topic = redissonClient.getTopic(IAM_ADAPTER_EVENT_TOPIC);
    }

    @Override
    public void publish(Event event) {
        topic.publishAsync(event);
    }

    @PostConstruct
    public void init() {
        //meta change
        topic.addListener(IAMAdapterCacheEvent.class, (charSequence, event) -> publisher.publishEvent(event));
    }

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.publisher = applicationEventPublisher;
    }
}
