package io.terminus.trantor2.iam.exception;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.NoI18nException;
import io.terminus.trantor2.common.exception.TrantorBizException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class UserHandleException extends TrantorBizException implements NoI18nException {

    public UserHandleException(String message) {
        super(ErrorType.USER_HANDLE_ERROR, message);
    }
}
