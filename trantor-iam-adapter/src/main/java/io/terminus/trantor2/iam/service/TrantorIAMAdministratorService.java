package io.terminus.trantor2.iam.service;

import io.terminus.iam.api.request.user.AdministratorParams;

import java.util.List;

/**
 * iam管理员服务
 * <p>
 * Created by hedy on 2024/7/31.
 */
public interface TrantorIAMAdministratorService {

    /**
     * 验证用户是否是管理员
     *
     * @param userId        用户id
     * @param applicationId iam应用id
     * @return 是否是管理员
     */
    Boolean verifyUserIsAdministrator(Long userId, Long applicationId);

    /**
     * 查询管理员列表
     *
     * @param administratorParams 查询参数
     * @return 管理员列表
     */
    List<Long> listAdministrators(AdministratorParams administratorParams);

    /**
     * 创建管理员
     *
     * @param params 创建参数
     */
    void createAdministrator(AdministratorParams params);

    /**
     * 删除管理员
     *
     * @param params 删除参数
     */
    void deleteAdministrator(AdministratorParams params);

    /**
     * 删除用户的管理员身份
     *
     * @param userId        用户id
     * @param applicationId 应用id
     */
    void deleteUserIsAdministrator(Long userId, Long applicationId);
}
