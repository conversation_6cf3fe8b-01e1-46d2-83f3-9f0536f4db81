package io.terminus.trantor2.iam.dto.usergroup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Collection;

/**
 * <AUTHOR>
 * 2025/1/13 14:35
 **/
@Data
@Schema(description = "分配用户组请求参数")
public class AssignUserGroupRequest implements Serializable {

    @Schema(description = "分配用户组id")
    private Collection<Long> assignUserGroupIds;

    @Schema(description = "移除用户组id")
    private Collection<Long> removeUserGroupIds;
}
