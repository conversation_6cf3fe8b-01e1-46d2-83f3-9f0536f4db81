package io.terminus.trantor2.iam.exception;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.EventBlockingException;
import io.terminus.trantor2.common.exception.NoI18nException;
import io.terminus.trantor2.common.exception.TrantorBizException;

/**
 * <AUTHOR>
 * 2024/5/26 19:00
 **/
public class PermissionHandleException extends TrantorBizException implements EventBlockingException, NoI18nException {

    public PermissionHandleException(Throwable throwable) {
        super(ErrorType.PERMISSION_HANDLE_ERROR, throwable.getMessage(), new Object[]{throwable.getMessage()});
    }

    public PermissionHandleException(String message) {
        super(ErrorType.PERMISSION_HANDLE_ERROR, message, new Object[]{message});
    }

    public PermissionHandleException(String message, Throwable throwable) {
        super(ErrorType.PERMISSION_HANDLE_ERROR, message, throwable, new Object[]{message});
    }
}
