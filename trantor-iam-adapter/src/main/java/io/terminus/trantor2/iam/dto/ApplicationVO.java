package io.terminus.trantor2.iam.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> qianjin
 * @since : 2024/1/11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApplicationVO implements Serializable {

    /**
     * 唯一标识符
     */
    private Long id;

    /**
     * 标识
     */
    private String key;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String desc;


}
