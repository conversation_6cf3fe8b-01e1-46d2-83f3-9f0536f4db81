package io.terminus.trantor2.iam.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> qianjin
 * @since : 2025/4/30
 */
@NoArgsConstructor
@Data
public class UserLoginOnceCodeParam {
    /**
     * 被登录的用户id
     */
    private Long targetUserId;

    /**
     * 登录目标应用key
     */
    private String targetIamAppKey;

    /**
     * 登录之后的重定向地址
     */
    private String redirectUrl;
}
