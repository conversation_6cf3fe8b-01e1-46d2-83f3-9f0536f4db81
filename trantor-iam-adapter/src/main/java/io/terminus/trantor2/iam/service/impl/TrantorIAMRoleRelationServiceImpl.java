package io.terminus.trantor2.iam.service.impl;

import io.terminus.iam.api.exception.IAMException;
import io.terminus.iam.api.request.role.BatchRoleRelationCreateParams;
import io.terminus.iam.api.request.role.BatchRoleRelationDeleteParams;
import io.terminus.iam.api.request.role.RoleRelationCreateParams;
import io.terminus.iam.api.request.role.RoleRelationDeleteOrCreateParams;
import io.terminus.iam.api.request.role.RoleRelationDeleteParams;
import io.terminus.iam.api.request.role.RoleRelationFindParams;
import io.terminus.iam.api.request.role.UserRoleRelationCreateParams;
import io.terminus.iam.api.request.role.UserRoleRelationFindParams;
import io.terminus.iam.api.response.application.ApplicationDetails;
import io.terminus.iam.api.response.role.Role;
import io.terminus.iam.api.response.role.RoleRelation;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.iam.TrantorIAMClientFactory;
import io.terminus.trantor2.iam.cache.IAMCache;
import io.terminus.trantor2.iam.cache.PermissionCacheManager;
import io.terminus.trantor2.iam.cache.RoleAssignCache;
import io.terminus.trantor2.iam.exception.IAMInvokedException;
import io.terminus.trantor2.iam.exception.RoleHandleException;
import io.terminus.trantor2.iam.service.TrantorIAMRoleRelationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by hedy on 2024/7/31.
 */

@Slf4j
@Component
@RequiredArgsConstructor
public class TrantorIAMRoleRelationServiceImpl implements TrantorIAMRoleRelationService {

    private final TrantorIAMClientFactory trantorIAMClientFactory;
    private final PermissionCacheManager permissionCacheManager;

    @Override
    public List<RoleRelation> findRoleRelation(RoleRelationFindParams params) {
        try {
            return trantorIAMClientFactory.getIAMClient().rolerelationClient().listRole(params).execute();
        } catch (IAMException e) {
            throw e;
        } catch (Exception e) {
            throw new TrantorRuntimeException(e);
        }
    }

    @Override
    public Boolean createRoleRelation(RoleRelationCreateParams params) {
        try {
            return trantorIAMClientFactory.getIAMClient().rolerelationClient().createRoleRelation(params).execute();
        } catch (IAMException e) {
            throw e;
        } catch (Exception e) {
            throw new TrantorRuntimeException(e);
        } finally {
            getRoleAssignCache().invalidateAllLocalAndRemote();
        }
    }

    @Override
    public void deleteRoleRelation(RoleRelationDeleteParams params) {
        try {
            trantorIAMClientFactory.getIAMClient().rolerelationClient().deleteRoleRelation(params).execute();
        } catch (IAMException e) {
            throw e;
        } catch (Exception e) {
            throw new TrantorRuntimeException(e);
        } finally {
            getRoleAssignCache().invalidateAllLocalAndRemote();
        }
    }

    @Override
    public Boolean createUserRoleRelation(UserRoleRelationCreateParams userRoleRelationCreateParams) {
        try {
            return trantorIAMClientFactory.getIAMClient().rolerelationClient().createUserRoleRelation(userRoleRelationCreateParams).execute();
        } catch (Exception e) {
            log.error("createUserRoleRelation error", e);
            throw new RoleHandleException(e.getMessage());
        } finally {
            getRoleAssignCache().invalidateAllLocalAndRemote();
        }
    }

    public Boolean batchCreateUserRoleRelation(BatchRoleRelationCreateParams batchRoleRelationCreateParams) {
        try {
            return trantorIAMClientFactory.getIAMClient().rolerelationClient().batchCreateRoleRelation(batchRoleRelationCreateParams).execute();
        } catch (Exception e) {
            log.error("batchCreateUserRoleRelation error", e);
            throw new RoleHandleException(e.getMessage());
        } finally {
            getRoleAssignCache().invalidateAllLocalAndRemote();
        }
    }


    public void deleteUserRoleRelation(Long userId, Long roleId) {
        RoleRelationDeleteParams params = new RoleRelationDeleteParams();
        params.setEndpointId(findIamApplicationId());
        params.setRoleId(roleId);
        if (userId != null) {
            params.setBizType("user");
            params.setBizId(userId.toString());
        }
        try {
            trantorIAMClientFactory.getIAMClient().rolerelationClient().deleteRoleRelation(params).execute();
        } catch (Exception e) {
            log.error("createUserRoleRelation error", e);
            throw new RoleHandleException(e.getMessage());
        } finally {
            getRoleAssignCache().invalidateAllLocalAndRemote();
        }
    }


    public void batchDeleteUserRoleRelation(BatchRoleRelationDeleteParams batchRoleRelationDeleteParams) {
        try {
            trantorIAMClientFactory.getIAMClient().rolerelationClient().batchDeleteRoleRelation(batchRoleRelationDeleteParams).execute();
        } catch (Exception e) {
            log.error("createUserRoleRelation error", e);
            throw new RoleHandleException(e.getMessage());
        } finally {
            getRoleAssignCache().invalidateAllLocalAndRemote();
        }
    }


    public Boolean flushUserRoleRelation(UserRoleRelationCreateParams userRoleRelationCreateParams) {
        try {
            return trantorIAMClientFactory.getIAMClient().rolerelationClient().flushToNewUserRoleRelation(userRoleRelationCreateParams).execute();
        } catch (Exception e) {
            log.error("flushUserRoleRelation error", e);
            throw new RoleHandleException(e.getMessage());
        } finally {
            getRoleAssignCache().invalidateAllLocalAndRemote();
        }
    }


    public List<RoleRelation> findRoleRelationByUserId(Long userId) {
        try {
            return trantorIAMClientFactory.getIAMClient().rolerelationClient().findRoleRelationByUserId(userId).execute();
        } catch (Exception e) {
            log.error("findRoleRelationByUserId error", e);
            throw new RoleHandleException(e.getMessage());
        }
    }

    public List<Role> findRoleByUserIdAndApplicationId(@NotNull Long userId, @NotNull Long applicationId) {
        try {
            UserRoleRelationFindParams params = new UserRoleRelationFindParams();
            params.setUserId(userId);
            params.setEndpointId(applicationId);
            return trantorIAMClientFactory.getIAMClient().rolerelationClient().findRoleByUserId(params).execute();
        } catch (Exception e) {
            log.error("findRoleByUserId error", e);
            throw new RoleHandleException(e.getMessage());
        }
    }


    public List<Role> findRoleByUserIdAndScope(@NotNull Long userId, @NotNull UserRoleRelationFindParams.RelationScope relationScope) {
        try {
            UserRoleRelationFindParams params = new UserRoleRelationFindParams();
            params.setUserId(userId);
            params.setRelationScope(relationScope);
            return trantorIAMClientFactory.getIAMClient().rolerelationClient().findRoleByUserId(params).execute();
        } catch (Exception e) {
            log.error("findRoleByUserId error", e);
            throw new RoleHandleException(e.getMessage());
        }
    }


    public List<Role> findRoleByUserId(@NotNull Long userId) {
        try {
            UserRoleRelationFindParams params = new UserRoleRelationFindParams();
            params.setUserId(userId);
            return trantorIAMClientFactory.getIAMClient().rolerelationClient().findRoleByUserId(params).execute();
        } catch (Exception e) {
            log.error("findRoleByUserId error", e);
            throw new RoleHandleException(e.getMessage());
        }
    }


    public List<RoleRelation> listRoleRelation(Long applicationId) {
        try {
            RoleRelationFindParams params = new RoleRelationFindParams();
            params.setEndpointId(applicationId);
            return trantorIAMClientFactory.getIAMClient().rolerelationClient().listRole(params).execute();
        } catch (Exception e) {
            log.error("iam sdk roleRelation method exec error", e);
            throw new IAMInvokedException(e);
        }
    }

    public Long findIamApplicationId() {
        try {
            ApplicationDetails app = trantorIAMClientFactory.getIAMClient().applications().getApplicationDetails().execute();
            return app.getId();
        } catch (Exception e) {
            log.error("iam sdk getApplicationDetails method exec error", e);
            throw new IAMInvokedException(e);
        }
    }

    public void batchCreateRoleRelation(List<RoleRelationCreateParams> params) {
        try {
            trantorIAMClientFactory.getIAMClient().rolerelationClient().batchCreateRoleRelation(params).execute();
        } catch (Exception e) {
            log.error("iam sdk createRoleRelationWithCondition method exec error", e);
            throw new IAMInvokedException(e);
        } finally {
            getRoleAssignCache().invalidateAllLocalAndRemote();
        }
    }

    public void batchDeleteRoleRelation(BatchRoleRelationDeleteParams params) {
        try {
            trantorIAMClientFactory.getIAMClient().rolerelationClient().batchDeleteRoleRelation(params).execute();
        } catch (Exception e) {
            log.error("iam sdk deleteRoleRelationWithCondition method exec error", e);
            throw new IAMInvokedException(e);
        } finally {
            getRoleAssignCache().invalidateAllLocalAndRemote();
        }
    }

    @Override
    public void removeOrAssignBizObjectsToRole(@NotNull Long roleId,
                                               @NotNull String bizType,
                                               Collection<Long> removeBizIds,
                                               Collection<Long> assignBizIds) throws Exception {
        if (CollectionUtils.isEmpty(removeBizIds) && CollectionUtils.isEmpty(assignBizIds)) {
            return;
        }
        try {
            // 查询当前应用信息
            ApplicationDetails applicationDetails = trantorIAMClientFactory.getIAMClient().applications().getApplicationDetails().execute();
            RoleRelationDeleteOrCreateParams params = new RoleRelationDeleteOrCreateParams();
            if (!CollectionUtils.isEmpty(removeBizIds)) {
                // 用户移除用户组
                RoleRelationDeleteParams deleteParams = new RoleRelationDeleteParams();
                deleteParams.setRoleIds(Collections.singletonList(roleId));
                deleteParams.setBizTypes(Collections.singleton(bizType));
                deleteParams.setBizIds(removeBizIds.stream().map(String::valueOf).distinct().collect(Collectors.toList()));
                params.setDeleteParams(deleteParams);
            }
            if (!CollectionUtils.isEmpty(assignBizIds)) {
                // 用户分配增量用户组
                RoleRelationFindParams roleRelationFindParams = new RoleRelationFindParams();
                roleRelationFindParams.setRoleIds(Collections.singletonList(roleId));
                roleRelationFindParams.setBizType(bizType);
                List<RoleRelation> roleRelations = trantorIAMClientFactory.getIAMClient().rolerelationClient().listRole(roleRelationFindParams).execute();
                Set<Long> bizIdsByRoleId = roleRelations.stream()
                        .filter(roleRelation -> bizType.equals(roleRelation.getBizType())
                                && roleId.equals(roleRelation.getRoleId()))
                        .map(RoleRelation::getBizId)
                        .map(Long::valueOf)
                        .collect(Collectors.toSet());
                // 过滤出未分配的用户组id
                Set<Long> toAssignBizIds = assignBizIds.stream()
                        .filter(bizId -> !bizIdsByRoleId.contains(bizId)).collect(Collectors.toSet());
                if (!CollectionUtils.isEmpty(toAssignBizIds)) {
                    List<RoleRelationCreateParams> createParamsList = new ArrayList<>();
                    for (Long toAssignBizId : toAssignBizIds) {
                        RoleRelationCreateParams createParams = new RoleRelationCreateParams();
                        createParams.setRoleIds(Collections.singletonList(roleId));
                        createParams.setBizType(bizType);
                        createParams.setBizId(String.valueOf(toAssignBizId));
                        createParams.setEndpointId(applicationDetails.getId());
                        createParamsList.add(createParams);
                    }
                    params.setCreateParams(createParamsList);
                }
            }
            if (Objects.nonNull(params.getDeleteParams()) || !CollectionUtils.isEmpty(params.getCreateParams())) {
                trantorIAMClientFactory.getIAMClient().rolerelationClient().deleteOrCreate(params).execute();
            }
        } finally {
            getRoleAssignCache().invalidateAllLocalAndRemote();
        }
    }

    @Override
    public void removeOrAssignRolesToBizObject(@NotNull String bizType,
                                               @NotNull Long bizId,
                                               Collection<Long> removeRoleIds,
                                               Collection<Long> assignRoleIds) throws Exception {
        if (CollectionUtils.isEmpty(removeRoleIds) && CollectionUtils.isEmpty(assignRoleIds)) {
            return;
        }
        try {
            String bizIdStr = String.valueOf(bizId);
            // 查询当前应用信息
            ApplicationDetails applicationDetails = trantorIAMClientFactory.getIAMClient().applications().getApplicationDetails().execute();
            RoleRelationDeleteOrCreateParams params = new RoleRelationDeleteOrCreateParams();
            if (!CollectionUtils.isEmpty(removeRoleIds)) {
                // 业务对象移除角色
                RoleRelationDeleteParams deleteParams = new RoleRelationDeleteParams();
                deleteParams.setRoleIds(new ArrayList<>(removeRoleIds));
                deleteParams.setBizTypes(Collections.singleton(bizType));
                deleteParams.setBizIds(Collections.singletonList(bizIdStr));
                params.setDeleteParams(deleteParams);
            }
            if (!CollectionUtils.isEmpty(assignRoleIds)) {
                // 业务对象分配角色
                RoleRelationFindParams roleRelationFindParams = new RoleRelationFindParams();
                roleRelationFindParams.setBizType(bizType);
                roleRelationFindParams.setBizIds(Collections.singletonList(bizIdStr));
                List<RoleRelation> roleRelations = trantorIAMClientFactory.getIAMClient().rolerelationClient().listRole(roleRelationFindParams).execute();
                Set<Long> roleIdsByBizId = roleRelations.stream()
                        .filter(roleRelation -> bizType.equals(roleRelation.getBizType())
                                && bizIdStr.equals(roleRelation.getBizId()))
                        .map(RoleRelation::getRoleId)
                        .collect(Collectors.toSet());
                // 过滤出未分配的角色id
                Set<Long> toAssignRoleIds = assignRoleIds.stream()
                        .filter(roleId -> !roleIdsByBizId.contains(roleId)).collect(Collectors.toSet());
                if (!CollectionUtils.isEmpty(toAssignRoleIds)) {
                    RoleRelationCreateParams createParams = new RoleRelationCreateParams();
                    createParams.setRoleIds(new ArrayList<>(toAssignRoleIds));
                    createParams.setBizType(bizType);
                    createParams.setBizId(bizIdStr);
                    createParams.setEndpointId(applicationDetails.getId());
                    params.setCreateParams(Collections.singleton(createParams));
                }
            }
            if (Objects.nonNull(params.getDeleteParams()) || !CollectionUtils.isEmpty(params.getCreateParams())) {
                trantorIAMClientFactory.getIAMClient().rolerelationClient().deleteOrCreate(params).execute();
            }
        } finally {
            getRoleAssignCache().invalidateAllLocalAndRemote();
        }
    }

    private RoleAssignCache getRoleAssignCache() {
        return permissionCacheManager.getCache(IAMCache.Type.ROLE_ASSIGN);
    }
}
