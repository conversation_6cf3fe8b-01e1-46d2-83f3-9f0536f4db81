package io.terminus.trantor2.iam.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import io.terminus.iam.api.dto.condition.ConditionOperator;
import io.terminus.iam.api.enums.application.ApplicationFieldEnum;
import io.terminus.iam.api.exception.IAMException;
import io.terminus.iam.api.request.application.ApplicationDynamicPagingParams;
import io.terminus.iam.api.request.application.ApplicationParams;
import io.terminus.iam.api.request.application.MFATypeEnum;
import io.terminus.iam.api.response.application.Application;
import io.terminus.iam.api.response.application.ApplicationDetails;
import io.terminus.iam.api.response.oauth2.OAuth2AccessClient;
import io.terminus.iam.sdk.toolkit.invoker.Call;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.iam.TrantorIAMClientFactory;
import io.terminus.trantor2.iam.dto.ApplicationVO;
import io.terminus.trantor2.iam.exception.IAMInvokedException;
import io.terminus.trantor2.iam.mapper.TrantorApplicationConverter;
import io.terminus.trantor2.iam.service.TrantorIAMApplicationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by hedy on 2024/7/31.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrantorIAMApplicationServiceImpl implements TrantorIAMApplicationService {

    /**
     * iam类型转换mapper
     */

    /**
     * iam client
     */
    private final TrantorIAMClientFactory trantorIAMClientFactory;
    private final TrantorApplicationConverter applicationConverter;

    @Override
    public ApplicationDetails findIamApplicationDetails() {
        try {
            Call<ApplicationDetails> application = trantorIAMClientFactory.getIAMClient().applications().getApplicationDetails();
            return application.execute();
        } catch (IAMException e){
            throw e;
        }catch (Exception e) {
            log.error("find iam application details failed", e);
            throw new TrantorRuntimeException("find iam application details failed");
        }
    }

    @Override
    public Application create(String key, String name, Long iamEndPointId, String userSourceKey, String loginUrl, String loginCallbackUrl, List<String> mfaType) {
        ApplicationParams applicationParams = convertApplicationParams(key, name, iamEndPointId, userSourceKey, loginUrl, loginCallbackUrl, mfaType);
        try {
            Application app = trantorIAMClientFactory.getIAMClient().applications().findByKey(applicationParams.getKey()).execute();
            if (app != null) {
                return app;
            }
            Call<Application> application = trantorIAMClientFactory.getIAMClient().applications().create(applicationParams);
            return application.execute();
        } catch (IAMException e) {
            throw new TrantorRuntimeException("创建IAM应用失败，可能是IAM标识重复，请更改标识后重试");
        } catch (Exception e) {
            log.error("create iam endpoint failed", e);
            throw new TrantorRuntimeException("create iam endpoint failed");
        }
    }

    @Override
    public Application findById(Long id) {
        try {
            Call<Application> application = trantorIAMClientFactory.getIAMClient().applications().findById(id);
            return application.execute();
        } catch (IAMException e){
            throw e;
        } catch (Exception e) {
            log.error("find iam application failed", e);
            throw new TrantorRuntimeException("find iam application failed");
        }
    }

    @Override
    public OAuth2AccessClient findMasterAccessClientByAppId(Long id) {
        try {
            Call<OAuth2AccessClient> client = trantorIAMClientFactory.getIAMClient().applications().findMasterAccessClientByAppId(id);
            return client.execute();
        } catch (IAMException e){
            throw e;
        } catch (Exception e) {
            log.error("find master access client by app id failed", e);
            throw new TrantorRuntimeException(e);
        }
    }

    @Override
    public List<ApplicationVO> findIamApplicationListByIds(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) return new ArrayList<>();
        try {
            ApplicationDynamicPagingParams pagingParams = new ApplicationDynamicPagingParams();
            pagingParams.setFieldName(ApplicationFieldEnum.ID);
            pagingParams.setOperator(ConditionOperator.IN);
            pagingParams.setValues(Arrays.asList(ids.toArray()));
            return applicationConverter.convert(trantorIAMClientFactory.getIAMClient().applications().findApplicationListByDynamic(pagingParams).execute());
        } catch (IAMException e){
            throw e;
        } catch (Exception e) {
            log.error("find iam application list failed", e);
            throw new TrantorRuntimeException(e);
        }
    }

    @Override
    public List<ApplicationVO> findAllIamApplications() {
        try {
            ApplicationDynamicPagingParams pagingParams = new ApplicationDynamicPagingParams();
            pagingParams.setFieldName(ApplicationFieldEnum.ID);
            pagingParams.setOperator(ConditionOperator.IS_NOT_NULL);
            return applicationConverter.convert(trantorIAMClientFactory.getIAMClient().applications().findApplicationListByDynamic(pagingParams).execute());
        } catch (IAMException e){
            throw e;
        } catch (Exception e) {
            log.error("find iam application list failed", e);
            throw new TrantorRuntimeException(e);
        }
    }

    @Override
    public void update(String key, String name, Long applicationId, String userSourceKey, String loginUrl, String loginCallbackUrl, List<String> mfaType) {
        ApplicationParams applicationParams = convertApplicationParams(key, name, applicationId, userSourceKey, loginUrl, loginCallbackUrl, mfaType);
        try {
            Call<Boolean> call = trantorIAMClientFactory.getIAMClient().applications().update(applicationParams);
            call.execute();
        } catch (IAMException e){
            throw e;
        } catch (Exception e) {
            log.error("update iam endpoint failed", e);
            throw new TrantorRuntimeException("update iam endpoint failed");
        }
    }

    private ApplicationParams convertApplicationParams(String key, String name, Long iamEndPointId, String userSourceKey, String loginUrl, String loginCallbackUrl, List<String> mfaType) {
        ApplicationParams applicationParams = new ApplicationParams();
        applicationParams.setId(iamEndPointId);
        applicationParams.setKey(key);
        applicationParams.setName(name);
        applicationParams.setUserPoolKey(userSourceKey);
        applicationParams.setLoginUrl(loginUrl);
        applicationParams.setHomeUrl(loginCallbackUrl);
        if (mfaType != null && !mfaType.isEmpty()) {
            applicationParams.setMfaType(MFATypeEnum.valueOf(mfaType.get(0)));
        } else {
            applicationParams.setMfaType(MFATypeEnum.none);
        }
        return applicationParams;
    }

    /**
     * 删除iam endpoint
     *
     * @param id
     */
    @Override
    public void delete(Long id) {
        try {
            Call<Boolean> call = trantorIAMClientFactory.getIAMClient().applications().deleteById(id);
            call.execute();
        } catch (IAMException e){
            throw e;
        } catch (Exception e) {
            log.error("delete iam endpoint failed", e);
            throw new TrantorRuntimeException("delete iam endpoint failed");
        }
    }

    @Override
    public Long getCurrentApplication() {
        try {
            ApplicationDetails app = trantorIAMClientFactory.getIAMClient().applications().getApplicationDetails().execute();
            return app.getId();
        } catch (IAMException e){
            throw e;
        } catch (Exception e) {
            log.error("iam sdk getApplicationDetails method exec error", e);
            throw new IAMInvokedException(e);
        }
    }

}
