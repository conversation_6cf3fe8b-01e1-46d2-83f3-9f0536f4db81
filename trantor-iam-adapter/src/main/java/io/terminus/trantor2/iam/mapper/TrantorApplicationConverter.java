package io.terminus.trantor2.iam.mapper;

import io.terminus.iam.api.response.application.Application;
import io.terminus.trantor2.iam.dto.ApplicationVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * Created by hedy on 2023/3/9.
 */
@Mapper(componentModel = "spring")
public interface TrantorApplicationConverter {

    ApplicationVO convert(Application application);

    List<ApplicationVO> convert(List<Application> application);

}
