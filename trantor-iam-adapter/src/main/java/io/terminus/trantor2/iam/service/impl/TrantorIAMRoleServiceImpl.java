package io.terminus.trantor2.iam.service.impl;

import io.terminus.iam.api.enums.role.RoleRelationBizType;
import io.terminus.iam.api.exception.IAMException;
import io.terminus.iam.api.request.role.DefaultRoleFindParams;
import io.terminus.iam.api.request.role.DefaultRoleParams;
import io.terminus.iam.api.request.role.RoleByIdsFindParams;
import io.terminus.iam.api.request.role.RoleByKeyFindParams;
import io.terminus.iam.api.request.role.RoleCopyParams;
import io.terminus.iam.api.request.role.RoleCreateParams;
import io.terminus.iam.api.request.role.RoleFindParams;
import io.terminus.iam.api.request.role.RolePageParams;
import io.terminus.iam.api.request.role.RoleRelationFindParams;
import io.terminus.iam.api.request.role.RoleUpdateParams;
import io.terminus.iam.api.request.role.UserRoleRelationFindParams;
import io.terminus.iam.api.request.usergroup.UserGroupFindParams;
import io.terminus.iam.api.response.PageResult;
import io.terminus.iam.api.response.role.Role;
import io.terminus.iam.api.response.role.RoleRelation;
import io.terminus.iam.api.response.user.User;
import io.terminus.iam.api.response.user.UserGroup;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.iam.TrantorIAMClientFactory;
import io.terminus.trantor2.iam.cache.IAMCache;
import io.terminus.trantor2.iam.cache.PermissionCacheManager;
import io.terminus.trantor2.iam.dto.user.AssignUserRequest;
import io.terminus.trantor2.iam.dto.usergroup.AssignUserGroupRequest;
import io.terminus.trantor2.iam.exception.IAMInvokedException;
import io.terminus.trantor2.iam.exception.RoleHandleException;
import io.terminus.trantor2.iam.service.TrantorIAMRoleRelationService;
import io.terminus.trantor2.iam.service.TrantorIAMRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.validation.constraints.NotNull;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by hedy on 2024/7/31.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TrantorIAMRoleServiceImpl implements TrantorIAMRoleService {

    private final TrantorIAMClientFactory trantorIAMClientFactory;
    private final PermissionCacheManager permissionCacheManager;
    private final TrantorIAMRoleRelationService iamRoleRelationService;

    @Override
    public List<Role> findRoleByUserId(@NotNull Long userId, @NotNull UserRoleRelationFindParams.RelationScope relationScope) {
        UserRoleRelationFindParams params = new UserRoleRelationFindParams();
        params.setUserId(userId);
        params.setRelationScope(relationScope);
        try {
            return trantorIAMClientFactory.getIAMClient().rolerelationClient().findRoleByUserId(params).execute();
        } catch (IAMException e) {
            throw e;
        } catch (Exception e) {
            log.error("findRoleByUserId error", e);
            throw new TrantorRuntimeException(e.getMessage());
        }
    }

    @Override
    public List<Role> findRoleRelation(RoleFindParams roleFindParams) {
        try {
            return trantorIAMClientFactory.getIAMClient().roleClient().listRole(roleFindParams).execute();
        } catch (IAMException e) {
            throw e;
        } catch (Exception e) {
            throw new TrantorRuntimeException("findRoleRelation error", e);
        }
    }

    @Override
    public Long createRole(RoleCreateParams roleCreateParams) {
        try {
            RoleByKeyFindParams roleByKeyFindParams = new RoleByKeyFindParams();
            roleByKeyFindParams.setSourceApplicationId(roleCreateParams.getSourceApplicationId());
            roleByKeyFindParams.setKey(roleCreateParams.getKey());
            Role role = trantorIAMClientFactory.getIAMClient().roleClient().getRoleByKey(roleByKeyFindParams).execute();
            if (role != null) {
                return role.getId();
            }
            return trantorIAMClientFactory.getIAMClient().roleClient().createRole(roleCreateParams).execute();
        } catch (Exception e) {
            log.error("createRole error", e);
            throw new RoleHandleException(e.getMessage());
        }
    }

    @Override
    public Boolean updateRole(RoleUpdateParams roleUpdateParams) {
        try {
            return trantorIAMClientFactory.getIAMClient().roleClient().updateRoleById(roleUpdateParams).execute();
        } catch (Exception e) {
            log.error("updateRole error", e);
            throw new RoleHandleException(e.getMessage());
        }
    }

    @Override
    public Boolean deleteRole(Long roleId) {
        try {
            trantorIAMClientFactory.getIAMClient().roleClient().deleteRoleById(roleId).execute();
            return true;
        } catch (Exception e) {
            log.error("deleteRole error", e);
            throw new RoleHandleException(e.getMessage());
        } finally {
            permissionCacheManager.getCache(IAMCache.Type.ROLE_ASSIGN).invalidateAllLocalAndRemote();
        }
    }

    @Override
    public PageResult<Role> pagingRole(RolePageParams rolePageParams) {
        try {
            return trantorIAMClientFactory.getIAMClient().roleClient().pageRole(rolePageParams).execute();
        } catch (Exception e) {
            log.error("pagingRole error", e);
            throw new RoleHandleException(e.getMessage());
        }
    }

    @Override
    public List<Role> queryRole(RoleFindParams roleFindParams) {
        try {
            return trantorIAMClientFactory.getIAMClient().roleClient().listRole(roleFindParams).execute();
        } catch (Exception e) {
            log.error("queryRole error", e);
            throw new RoleHandleException(e.getMessage());
        }
    }

    @Override
    public Role findRoleByKey(RoleByKeyFindParams roleByKeyFindParams) {
        try {
            return trantorIAMClientFactory.getIAMClient().roleClient().getRoleByKey(roleByKeyFindParams).execute();
        } catch (Exception e) {
            log.error("findRoleByKey error", e);
            throw new RoleHandleException(e.getMessage());
        }
    }

    @Override
    public List<Long> createDefaultRole(DefaultRoleParams params) {
        try {
            return trantorIAMClientFactory.getIAMClient().roleClient().createDefaultRole(params).execute();
        } catch (Exception e) {
            log.error("createDefaultRole error", e);
            throw new RoleHandleException(e.getMessage());
        }
    }

    @Override
    public List<Long> replaceAllDefaultRole(DefaultRoleParams params) {
        try {
            return trantorIAMClientFactory.getIAMClient().roleClient().replaceAllDefaultRole(params).execute();
        } catch (Exception e) {
            log.error("replaceAllDefaultRole error", e);
            throw new RoleHandleException(e.getMessage());
        }
    }

    @Override
    public Boolean deleteDefaultRole(DefaultRoleParams params) {
        try {
            return trantorIAMClientFactory.getIAMClient().roleClient().deleteDefaultRole(params).execute();
        } catch (Exception e) {
            log.error("deleteDefaultRole error", e);
            throw new RoleHandleException(e.getMessage());
        }
    }

    @Override
    public List<Role> findDefaultRole(DefaultRoleFindParams params) {
        try {
            return trantorIAMClientFactory.getIAMClient().roleClient().findDefaultRole(params).execute();
        } catch (Exception e) {
            log.error("deleteDefaultRole error", e);
            throw new RoleHandleException(e.getMessage());
        }
    }

    @Override
    public Boolean copyRoleAndPermissionGrant(RoleCopyParams params) {
        try {
            return trantorIAMClientFactory.getIAMClient().roleClient().copyRoleAndPermissionGrant(params).execute();
        } catch (Exception e) {
            log.error("iam sdk copyRoleAndPermissionGrant method exec error", e);
            throw new IAMInvokedException(e);
        }
    }

    @Override
    public List<Role> findRoleByIds(@NotNull RoleByIdsFindParams roleByIdsFindParams) {
        try {
            return trantorIAMClientFactory.getIAMClient().roleClient().findRoleByIds(roleByIdsFindParams).execute();
        } catch (Exception e) {
            log.error("findRoleByIds error", e);
            throw new RoleHandleException(e.getMessage());
        }
    }

    @Override
    public Collection<UserGroup> findUserGroupsByRoleId(@NotNull Long roleId) {
        try {
            RoleRelationFindParams params = new RoleRelationFindParams();
            params.setBizType(RoleRelationBizType.USER_GROUP_ROLE);
            params.setRoleId(roleId);
            List<RoleRelation> roleRelations = trantorIAMClientFactory.getIAMClient().rolerelationClient().listRole(params).execute();
            if (CollectionUtils.isEmpty(roleRelations)) {
                return Collections.emptyList();
            }
            Set<Long> userGroupIds = roleRelations.stream().map(RoleRelation::getBizId).map(Long::valueOf).collect(Collectors.toSet());
            UserGroupFindParams userGroupFindParams = new UserGroupFindParams();
            userGroupFindParams.setIds(userGroupIds);
            return trantorIAMClientFactory.getIAMClient().userGroups().findAll(userGroupFindParams).execute();
        } catch (Exception e) {
            throw new TrantorRuntimeException(e);
        }
    }

    @Override
    public void removeOrAssignUserGroups(@NotNull Long roleId, @NotNull AssignUserGroupRequest request) {
        try {
            iamRoleRelationService.removeOrAssignBizObjectsToRole(roleId, RoleRelationBizType.USER_GROUP_ROLE, request.getRemoveUserGroupIds(), request.getAssignUserGroupIds());
        } catch (Exception e) {
            throw new TrantorRuntimeException("TrantorIAMRoleServiceImpl.removeOrAssignUserGroups error", e);
        }
    }

    @Override
    public List<User> findUsersByRoleId(@NotNull Long roleId) {
        try {
            RoleRelationFindParams params = new RoleRelationFindParams();
            params.setBizType(RoleRelationBizType.USER_ROLE);
            params.setRoleId(roleId);
            List<RoleRelation> roleRelations = trantorIAMClientFactory.getIAMClient().rolerelationClient().listRole(params).execute();
            if (CollectionUtils.isEmpty(roleRelations)) {
                return Collections.emptyList();
            }
            Set<Long> userIds = roleRelations.stream()
                    .filter(roleRelation -> RoleRelationBizType.USER_ROLE.equals(roleRelation.getBizType()))
                    .map(RoleRelation::getBizId)
                    .map(Long::valueOf)
                    .collect(Collectors.toSet());
            return trantorIAMClientFactory.getIAMClient().users().findByIds(userIds).execute();
        } catch (Exception e) {
            throw new TrantorRuntimeException("TrantorIAMRoleServiceImpl.findUsersByRoleId error", e);
        }
    }

    @Override
    public void removeOrAssignUsers(@NotNull Long roleId, @NotNull AssignUserRequest request) {
        try {
            iamRoleRelationService.removeOrAssignBizObjectsToRole(roleId, RoleRelationBizType.USER_ROLE, request.getRemoveUserIds(), request.getAssignUserIds());
        } catch (Exception e) {
            throw new TrantorRuntimeException("TrantorIAMRoleServiceImpl.removeOrAssignUsers error", e);
        }
    }
}
