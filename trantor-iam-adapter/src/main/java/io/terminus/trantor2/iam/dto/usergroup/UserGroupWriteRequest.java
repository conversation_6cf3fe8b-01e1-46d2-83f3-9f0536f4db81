package io.terminus.trantor2.iam.dto.usergroup;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.iam.api.request.CreateOps;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/1/8 13:44
 **/
@Data
@Schema(description = "用户组创建请求参数")
public class UserGroupWriteRequest implements Serializable {

    @Schema(description = "用户组标识")
    @NotBlank(groups = CreateOps.class)
    private String key;

    @Schema(description = "用户组名称")
    @NotBlank(groups = CreateOps.class)
    private String name;

    @Schema(description = "用户组描述")
    private String desc;

    @Schema(description = "用户组类型", hidden = true)
    private String type;

    @Schema(description = "门户标识")
    private String portalCode;
}
