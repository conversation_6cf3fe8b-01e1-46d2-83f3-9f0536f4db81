version: "1.1"
name: ""
stages:
  - stage:
      - git-checkout:
          alias: repo
          params:
            depth: 1
  - stage:
      - unit-test:
          alias: unit-test
          params:
            context: ${repo}/
      - sonar:
          alias: sonar
          version: "1.0"
          params:
            code_dir: ${repo}
            delete_project: true
            language: java
            quality_gate:
              - error: "0"
                metric: new_bugs
                op: GT
              - error: "0"
                metric: new_vulnerabilities
                op: GT
              - error: "0"
                metric: new_code_smells
                op: GT
      - java-dependency-check:
          alias: java-dependency-check
          params:
            auto_update_nvd: true
            code_dir: ${repo}
            fail_build_on_cvss: 7
            goal: aggregate
  - stage:
      - custom-script:
          alias: docker-rm-testcontainers
          description: 清理测试容器
          commands:
            - docker rm -f $(docker ps -aq -f label=trantor-test=true) || echo 'not rm'
            - docker rm -f $(docker ps -aq -f ancestor=registry.cn-hangzhou.aliyuncs.com/terminus/trantor-mysql:5.7.32) || echo 'not rm'
