package io.terminus.trantor2.test.tool.nexus;

import org.jetbrains.annotations.NotNull;
import org.junit.ClassRule;
import org.springframework.http.*;
import org.springframework.http.client.support.BasicAuthenticationInterceptor;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.web.client.RestTemplate;
import org.testcontainers.containers.Container;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.utility.DockerImageName;
import org.testcontainers.utility.TestcontainersConfiguration;

import java.io.IOException;

import static io.terminus.trantor2.test.tool.TestContainerConstant.*;

/**
 * 自动创建两个 maven 仓库
 *
 * <AUTHOR>
 */
//CHECKSTYLE:OFF
public interface NexusSpringTest {
    @ClassRule
    GenericContainer<?> nexusContainer = new GenericContainer<>(
            DockerImageName.parse(NEXUS_IMAGE))
            .withReuse(true)
            .withLabel(TRANTOR_TEST_CONTAINER_LABEL_KEY, TRANTOR_TEST_CONTAINER_LABEL_VALUE)
            .withPrivilegedMode(true)
            .withExposedPorts(8081);

    @DynamicPropertySource
    static void nexusProperties(DynamicPropertyRegistry registry) throws IOException, InterruptedException {
        TestcontainersConfiguration.getInstance().updateUserConfig("testcontainers.reuse.enable", "true");
        nexusContainer.start();

        Container.ExecResult catResult = nexusContainer.execInContainer("cat", "/nexus-data/admin.password");
        String pwd = catResult.getStdout();
        int exitCode = catResult.getExitCode();
        if (exitCode == 0) {
            registry.add("trantor2.nexus.local.password", () -> pwd);
            registry.add("trantor2.nexus.pub.password", () -> pwd);
        }
        String host = "http://127.0.0.1:" + nexusContainer.getMappedPort(8081);
        registry.add("trantor2.nexus.pub.host", () -> host);
        registry.add("trantor2.nexus.pub.username", () -> "admin");
        registry.add("trantor2.nexus.pub.repository", () -> "trantor2-artifact");
        registry.add("trantor2.nexus.local.host", () -> host);
        registry.add("trantor2.nexus.local.username", () -> "admin");
        registry.add("trantor2.nexus.local.repository", () -> "trantor2-artifact-private");
        registry.add("trantor2.nexus.local.enabled", () -> true);
        dealWithMavenRepo(host, pwd);
    }

    static void dealWithMavenRepo(String host, String pwd) {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.getInterceptors().add(new BasicAuthenticationInterceptor("admin", pwd));

        if (needCreateMavenRepo("trantor2-artifact", host, restTemplate)) {
            createMavenRepo("trantor2-artifact", host, restTemplate);
        }
        if (needCreateMavenRepo("trantor2-artifact-private", host, restTemplate)) {
            createMavenRepo("trantor2-artifact-private", host, restTemplate);
        }
        System.out.println("**** nexus host: " + host + ", user name: admin, password: " + pwd + " *****");

    }

    static boolean needCreateMavenRepo(String repo, String host, RestTemplate restTemplate) {
        try {
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    host + "/service/rest/v1/repositories/maven/hosted/" + repo,
                    HttpMethod.GET,
                    null,
                    String.class
            );
            if (responseEntity.getStatusCode() == HttpStatus.OK) {
                return false;
            }
        } catch (Exception ignore) {
        }
        return true;
    }

    static void createMavenRepo(String repo, String host, RestTemplate restTemplate) {
        try {
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    host + "/service/rest/v1/repositories/maven/hosted/" + repo,
                    HttpMethod.GET,
                    null,
                    String.class
            );
            if (responseEntity.getStatusCode() == HttpStatus.OK) {
                return;
            }
        } catch (Exception ignore) {
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("accept", MediaType.APPLICATION_JSON_VALUE);

        HttpEntity<String> requestEntity = getStringHttpEntity(repo, headers);
        restTemplate.exchange(
                host + "/service/rest/v1/repositories/maven/hosted",
                HttpMethod.POST,
                requestEntity,
                String.class
        );
    }

    @NotNull
    static HttpEntity<String> getStringHttpEntity(String repo, HttpHeaders headers) {
        String requestBody = "{\n" +
                "  \"name\": \"" + repo + "\",\n" +
                "  \"online\": true,\n" +
                "  \"storage\": {\n" +
                "    \"blobStoreName\": \"default\",\n" +
                "    \"strictContentTypeValidation\": true,\n" +
                "    \"writePolicy\": \"allow\"\n" +
                "  },\n" +
                "  \"cleanup\": {\n" +
                "    \"policyNames\": [\n" +
                "      \"string\"\n" +
                "    ]\n" +
                "  },\n" +
                "  \"component\": {\n" +
                "    \"proprietaryComponents\": true\n" +
                "  },\n" +
                "  \"maven\": {\n" +
                "    \"versionPolicy\": \"MIXED\",\n" +
                "    \"layoutPolicy\": \"PERMISSIVE\",\n" +
                "    \"contentDisposition\": \"ATTACHMENT\"\n" +
                "  }\n" +
                "}";
        return new HttpEntity<>(requestBody, headers);
    }
}
//CHECKSTYLE:ON
