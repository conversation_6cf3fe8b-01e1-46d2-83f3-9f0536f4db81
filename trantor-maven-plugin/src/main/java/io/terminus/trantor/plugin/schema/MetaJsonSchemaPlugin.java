package io.terminus.trantor.plugin.schema;

import io.terminus.trantor2.console.util.MetaJsonSchemaGenerator;
import io.terminus.trantor2.properties.management.nexus.NexusProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.maven.plugin.AbstractMojo;
import org.apache.maven.plugins.annotations.Mojo;
import org.apache.maven.plugins.annotations.Parameter;

import java.io.File;

/**
 * <AUTHOR>
 **/
@Slf4j
@Mojo(name = "meta-schema")
public class MetaJsonSchemaPlugin extends AbstractMojo {
    @Parameter(defaultValue = "${basedir}")
    private String baseDir;
    @Parameter(name = "trantor.center.host")
    private String centerHost;
    @Parameter(name = "trantor.center.username")
    private String userName;
    @Parameter(name = "trantor.center.password")
    private String password;
    @Parameter(name = "trantor.center.repo")
    private String repoName;

    @Parameter(name = "trantor.branch")
    private String branch;

    @Override
    public void execute() {
        String path = baseDir + "/src/main/resources/schema.zip";
        if (generateSchema(path)) {
            publishSchema(path);
        }
    }

    private void publishSchema(String path) {
        if (centerHost == null || userName == null || password == null || repoName == null || branch == null || !branch.startsWith("release/")) {
            return;
        }
        NexusProperties nexusProperties = new NexusProperties();
        nexusProperties.setHost(centerHost);
        nexusProperties.setUsername(userName);
        nexusProperties.setPassword(password);
        nexusProperties.setRepository(repoName);

        log.info("开始发布JSON Schema文件");
        try {
            String version = branch.replaceFirst("release/", "") + "-SNAPSHOT";
            File file = new File(path);
            if (!file.exists()) {
                log.warn("JSON Schema文件不存在: {}", path);
                return;
            }
            MetaSchemaCenter metaSchemaCenter = new MetaSchemaCenter(nexusProperties, version, file);
            metaSchemaCenter.publish();
        } catch (Exception e) {
            log.warn("JSON Schema发布失败: {}", e.getMessage());
            return;
        }
        log.info("JSON Schema发布完成");
    }

    private boolean generateSchema(String path) {
        log.info("开始构建元数据JSON Schema");
        try {
            MetaJsonSchemaGenerator.generateMetaJsonSchema(path);
        } catch (Exception e) {
            log.warn("JSON Schema构建失败: {}", e.getMessage());
            return false;
        }
        log.info("JSON Schema构建完成, 文件路径: {}", path);
        return true;
    }
}
