package io.terminus.trantor.plugin.schema;

import cn.hutool.core.io.FileUtil;
import io.terminus.trantor2.nexus.dto.Maven;
import io.terminus.trantor2.nexus.service.AbstractNexusApiClient;
import io.terminus.trantor2.nexus.service.NexusApiClient;
import io.terminus.trantor2.properties.management.nexus.NexusProperties;
import org.springframework.core.io.InputStreamResource;
import org.springframework.web.client.RestTemplate;

import java.io.File;

/**
 * <AUTHOR>
 **/
public class MetaSchemaCenter {
    private final static String GROUP_ID = "io.terminus.trantor2.schema";
    private final static String ARTIFACT_ID = "trantor-meta-schema";
    private final static String EXTENSION = "zip";

    private final NexusApiClient apiClient;
    private final Maven maven;

    public MetaSchemaCenter(NexusProperties nexusProperties, String version, File file) {
        this.apiClient = new NexusApiClientImpl(nexusProperties, new RestTemplate());
        this.maven = Maven.builder()
                .version(version)
                .groupId(GROUP_ID)
                .artifactId(ARTIFACT_ID)
                .extension(EXTENSION)
                .resource(new InputStreamResource(FileUtil.getInputStream(file)))
                .build();
    }

    public void publish() {
        apiClient.uploadMaven(maven);
    }

    private static class NexusApiClientImpl extends AbstractNexusApiClient {
        protected NexusApiClientImpl(NexusProperties nexusProperties, RestTemplate restTemplate) {
            super(nexusProperties, restTemplate);
        }

        @Override
        public Boolean getLocal() {
            return false;
        }
    }
}
