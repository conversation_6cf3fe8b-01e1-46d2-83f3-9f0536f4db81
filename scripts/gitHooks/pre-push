#!/bin/bash

protected_branch="master"
feature_branch="origin/feature/v2.5"

# shellcheck disable=SC2162
# shellcheck disable=SC2001
# shellcheck disable=SC2086
# shellcheck disable=SC2027
# shellcheck disable=SC2154
# shellcheck disable=SC2034
while read local_ref local_sha remote_ref remote_sha
do
    local_branch=$(echo "$local_ref" | sed -e 's,.*/\(.*\),\1,')

    remote_branch=$(echo $remote_ref | sed -e 's,.*/\(.*\),\1,')

    if [ "$remote_branch" = "$protected_branch" ]; then
        if git merge-base --fork-point $feature_branch $local_branch >/dev/null 2>&1; then
            contains_feature="yes"
        else
            contains_feature="no"
        fi

        merged_feature=$(git log $local_branch ^$feature_branch --ancestry-path --merges | wc -l)

        if [ "$contains_feature" = "yes" ] || [ "$merged_feature" -gt 0 ] || [ "$rebased_feature" = "yes" ]; then
            echo "⚠️ 检测到即将推送的更改可能来自 "$protected_branch" 分支，这不符合 Trantor 代码分支管理流程。"
            echo "🤡 如果这是一个误报，可以通过以下命令跳过这个钩子："
            echo "git push --no-verify"
            exit 1
        fi
    fi
done
exit 0