#!/bin/bash

function help() {
    echo "Usage: ./scripts/git-erda-create-mr.sh <target_branch> [<assigneeId>]"
    echo "Create MR from current branch to target branch."
    echo "  target_branch: target branch to merge into."
    echo "  assigneeId [optional]: assignee id of MR. If not specified, MR will be assigned to the default assignee."
    echo "Example: ./scripts/git-erda-create-mr.sh master 5011"
    exit
}

if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    help
fi


. $(dirname "$0")/git-erda-common.sh


target_branch=$1
if [[ -z "$target_branch" ]]; then
    echo "target branch must be specified."
    exit
fi


# get current branch
current_branch=${current_branch:-$(git branch --show-current)}
if [[ -z "$current_branch" ]]; then
    echo "You are not on a branch, please checkout a branch before running this script."
    exit
fi

# prevent if uncommitted or untracked changes
if [[ $(git status --porcelain) ]]; then
    echo "There are uncommitted or untracked changes, please commit or stash them before running this script."
    #exit 1
fi

# get first commit of current branch from target branch, as mr message
first_commit=$(git merge-base $target_branch $current_branch)
mr_message=$(git log --reverse --pretty=format:"%s" $first_commit..$current_branch)
mr_message=${mr_message//\"/\\\"}
mr_title=$(echo "$mr_message" | head -n 1)

# if exist $mr_title_suffix, append it to mr_title
if [[ ! -z "$mr_title_suffix" ]]; then
    mr_title="${mr_title} ${mr_title_suffix}"
fi

assigneeId=$2

echo "Creating MR from $current_branch to $target_branch with title: $mr_title"
echo "MR message: $mr_message"
echo "AssigneeId: $assigneeId"

# push current branch to remote
git push origin $current_branch

# title, description, sourceBranch, targetBranch, assigneeId, removeSourceBranch=true
req_body=$(cat <<EOF
{
    "title": "$mr_title",
    "description": "${mr_message//$'\n'/\\n}",
    "sourceBranch": "$current_branch",
    "targetBranch": "$target_branch",
    "assigneeId": "$assigneeId",
    "removeSourceBranch": true
}
EOF
)

# create MR
send_request POST "merge-requests" "$req_body"
