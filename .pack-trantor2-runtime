# --build-arg EXTRACTED_LAYERS_PATH=./trantor-runtime/trantor-runtime-starter/target/extracted-layers
Dockerfile
entrypoint.sh
#trantor-runtime/trantor-runtime-starter/target/trantor2-runtime.jar
trantor-runtime/trantor-runtime-starter/target/extracted-layers/dependencies/ # :packdeps
trantor-runtime/trantor-runtime-starter/target/extracted-layers/spring-boot-loader/
trantor-runtime/trantor-runtime-starter/target/extracted-layers/snapshot-dependencies/
trantor-runtime/trantor-runtime-starter/target/extracted-layers/application/
.checksum.txt
