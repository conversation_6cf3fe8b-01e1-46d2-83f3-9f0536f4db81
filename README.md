# Getting Started

### Modules

```
.
├── trantor-api-common          // 公共包，仅限于外部工程需要依赖的对象，如terp需要用到的
├── trantor-impl-common         // 公共包, trantor内部公用的一些工具类以及公用的模型等
├── trantor-engines             // 所有引擎的父模块
│   ├── trantor-code-engine         // 取号规则
│   ├── trantor-connector-engine    // 连接器
│   ├── trantor-doc-engine          // 操作事件
│   ├── trantor-flow-engine         // 审批流
│   ├── trantor-print-engine        // 打印
│   ├── trantor-model-engine        // 模型引擎
│   ├── trantor-module-engine       // 模块管理
│   ├── trantor-permission-engine   // 权限管理
│   ├── trantor-rule-engine         // 规则引擎
│   ├── trantor-service-engine      // 服务引擎
│   ├── trantor-state-engine        // 状态机
│   └── trantor-scene-engine        // 场景引擎, 所有场景的管理以及生成
│       ├── trantor-scene-api-common   // 场景引擎对外提供接口和对象定义，保证兼容性，只能依赖trantor-api-common
│       ├── trantor-scene-impl-common  // 场景引擎实现共同的依赖，只能依赖trantor-impl-common、trantor-scene-api-common
│       └── trantor-scene-management   // 场景引擎面向控制台管理端的功能
│       └── trantor-scene-runtime      // 场景引擎面向运行态的功能实现
├── trantor-console             // 控制台的父模块
│   └── trantor-console-starter // 控制台的启动器，会依赖engines中需要控制台用到的management
├── trantor-runtime             // 运行态的父模块
│   └── trantor-runtime-sdk     // 运行态的客户端sdk，依赖各个engines/trantor-xxx-runtime-sdk，外部工程如terp的源码只依赖它
│   └── trantor-runtime-impl    // 运行态的启动器，依赖各个engines/trantor-xxx-runtime-impl，实现自动配置功能，外部工程如terp在运行时需要依赖它
├── trantor-test-tool           // 全局的测试工具类, 例如集成类的测试, 具体功能的测试跟随模块走, 不在此模块内
│   └── test-container          // 本地运行单测，需要用到的一些容器化的自动操作工具类
```

### Reference Documentation
For further reference, please consider the following sections:

* [Official Apache Maven documentation](https://maven.apache.org/guides/index.html)
* [Spring Boot Maven Plugin Reference Guide](https://docs.spring.io/spring-boot/docs/2.7.1/maven-plugin/reference/html/)
* [Create an OCI image](https://docs.spring.io/spring-boot/docs/2.7.1/maven-plugin/reference/html/#build-image)
* [Spring Web](https://docs.spring.io/spring-boot/docs/2.7.1/reference/htmlsingle/#web)
* [Spring Data JPA](https://docs.spring.io/spring-boot/docs/2.7.1/reference/htmlsingle/#data.sql.jpa-and-spring-data)
* [Liquibase Migration](https://docs.spring.io/spring-boot/docs/2.7.1/reference/htmlsingle/#howto.data-initialization.migration-tool.liquibase)
* [Validation](https://docs.spring.io/spring-boot/docs/2.7.1/reference/htmlsingle/#io.validation)
* [Spring Boot Actuator](https://docs.spring.io/spring-boot/docs/2.7.1/reference/htmlsingle/#actuator)
* [Consul Discovery](https://docs.spring.io/spring-cloud-consul/docs/current/reference/html/#spring-cloud-consul-discovery)

### Guides
The following guides illustrate how to use some features concretely:

* [Building a RESTful Web Service](https://spring.io/guides/gs/rest-service/)
* [Serving Web Content with Spring MVC](https://spring.io/guides/gs/serving-web-content/)
* [Building REST services with Spring](https://spring.io/guides/tutorials/rest/)
* [Accessing Data with JPA](https://spring.io/guides/gs/accessing-data-jpa/)
* [Accessing data with MySQL](https://spring.io/guides/gs/accessing-data-mysql/)
* [Validation](https://spring.io/guides/gs/validating-form-input/)
* [Building a RESTful Web Service with Spring Boot Actuator](https://spring.io/guides/gs/actuator-service/)

