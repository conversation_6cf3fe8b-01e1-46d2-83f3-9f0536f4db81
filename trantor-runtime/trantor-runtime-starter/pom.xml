<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>io.terminus.trantor2</groupId>
        <artifactId>trantor-runtime</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>trantor-runtime-starter</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-runtime-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>xyz.hedy.gravity</groupId>
            <artifactId>gravity-spring-boot-starter</artifactId>
            <version>${gravity.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
<!--        todo: copied from terminus-rocketmq-starter, remove later -->
<!--        <dependency>-->
<!--            <groupId>org.apache.rocketmq</groupId>-->
<!--            <artifactId>rocketmq-client</artifactId>-->
<!--            <version>4.9.0</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.apache.rocketmq</groupId>-->
<!--            <artifactId>rocketmq-tools</artifactId>-->
<!--            <version>4.9.0</version>-->
<!--            <exclusions>-->
<!--                &lt;!&ndash; 使用springboot 指定的版本 &ndash;&gt;-->
<!--                <exclusion>-->
<!--                    <artifactId>logback-classic</artifactId>-->
<!--                    <groupId>ch.qos.logback</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>logback-core</artifactId>-->
<!--                    <groupId>ch.qos.logback</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>fastjson</artifactId>-->
<!--                    <groupId>com.alibaba</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>com.alibaba</artifactId>-->
<!--                    <groupId>fastjson</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
<!--        todo: force add it, don't know why need it-->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-data-33</artifactId>
            <version>${redisson.version}</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <dependencies>
                <dependency>
                    <groupId>xyz.hedy.gravity</groupId>
                    <artifactId>gravity-spring-boot-starter</artifactId>
                    <version>${gravity.version}</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>test</id>
            <dependencies>
                <dependency>
                    <groupId>xyz.hedy.gravity</groupId>
                    <artifactId>gravity-spring-boot-starter</artifactId>
                    <version>${gravity.version}</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>staging</id>
            <dependencies>
                <dependency>
                    <groupId>xyz.hedy.gravity</groupId>
                    <artifactId>gravity-spring-boot-starter</artifactId>
                    <version>${gravity.version}</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>prod</id>
        </profile>
    </profiles>
    <build>
        <finalName>trantor2-runtime</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <id>extract-layers</id>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <echo message="Extracting layers from ${project.build.directory}/${project.build.finalName}.jar" />
                                <mkdir dir="${project.build.directory}/extracted-layers" />
                                <exec executable="java" dir="${project.build.directory}">
                                    <arg value="-Djarmode=layertools" />
                                    <arg value="-jar" />
                                    <arg value="${project.build.finalName}.jar" />
                                    <arg value="extract" />
                                    <arg value="--destination" />
                                    <arg value="extracted-layers" />
                                </exec>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
