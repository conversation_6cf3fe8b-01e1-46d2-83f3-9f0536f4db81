create table `model_data_source_config_meta`
(
  `id`               bigint not null comment '主键',
  `created_at`       datetime(6) comment '创建时间',
  `created_by`       bigint comment '创建人ID',
  `updated_at`       datetime(6) comment '修改时间',
  `updated_by`       bigint comment '修改人ID',
  `version`          integer NOT NULL DEFAULT '0' COMMENT '乐观锁',
  `team_id`          bigint comment '所属团队ID',
  `data_source_name` varchar(255) NOT NULL DEFAULT '' COMMENT '数据源标识,唯一',
  `db_host`          varchar(255) COMMENT 'host',
  `db_name`          varchar(255) COMMENT '数据库名称',
  `db_password`      varchar(255) COMMENT '密码',
  `db_port`          integer COMMENT '端口',
  `db_type`          integer COMMENT '数据源类型，0-mysql、1-drds、2-oracle、3-h2',
  `db_username`      varchar(255) COMMENT '用户名',
  `extra_config`     mediumtext COMMENT '扩展字段',
  primary key (`id`)
) engine = InnoDB;

create table `model_meta`
(
  `id`               bigint not null comment '主键',
  `created_at`       datetime(6) comment '创建时间',
  `created_by`       bigint comment '创建人ID',
  `updated_at`       datetime(6) comment '修改时间',
  `updated_by`       bigint comment '修改人ID',
  `version`          integer NOT NULL DEFAULT '0' COMMENT '乐观锁',
  `team_id`          bigint comment '所属团队ID',
  `app_id`           bigint comment '所属应用ID',
  `config`           mediumtext comment '模型配置',
  `data_source_name` varchar(255) comment '模型所属数据源dataSourceName',
  `description`      varchar(255) comment '模型描述',
  `fields`           mediumtext comment '模型字段集',
  `model_name`             varchar(255) comment '模型名称',
  `model_alias`             varchar(255) comment '模型标识',
  `status`           integer comment '模型状态，0-草稿、1-生效、2-删除',
  `table_name`       varchar(255) comment '模型映射物理表名',
  `type`             integer comment '模型类型，0-在线模型、1-映射模型、2-虚拟模型',
  primary key (`id`)
) engine = InnoDB;

create table `model_sequence_meta`
(
  `id`           bigint not null comment '主键',
  `created_at`   datetime(6) comment '创建时间',
  `created_by`   bigint comment '创建人ID',
  `updated_at`   datetime(6) comment '修改时间',
  `updated_by`   bigint comment '修改人ID',
  `version`      integer NOT NULL DEFAULT '0' COMMENT '乐观锁',
  `team_id`      bigint comment '所属团队ID',
  `app_id`       bigint comment '所属应用ID',
  `max_id`       bigint NOT NULL DEFAULT '0' COMMENT '最大ID',
  `model_alias`   varchar(255) NOT NULL COMMENT '模型标识',
  `prefix`       varchar(255) DEFAULT '' COMMENT '模板',
  `start_number` bigint NOT NULL DEFAULT '0' COMMENT '起始数字',
  `step`         integer DEFAULT '2000' COMMENT '步长',
  `type`         varchar(255) NOT NULL DEFAULT '' COMMENT '类型',
  `zero_fill`    integer DEFAULT '0' COMMENT '补零位数',
  primary key (`id`)
) engine = InnoDB;

create table `model_subscribe_meta`
(
  `id`         bigint not null comment '主键',
  `created_at` datetime(6) comment '创建时间',
  `created_by` bigint comment '创建人ID',
  `updated_at` datetime(6) comment '修改时间',
  `updated_by` bigint comment '修改人ID',
  `version`    integer NOT NULL DEFAULT '0' COMMENT '乐观锁',
  `team_id`    bigint comment '所属团队ID',
  `app_id`     bigint comment '所属应用ID',
  `extra`      varchar(255) COMMENT '扩展字段',
  `model_alias` varchar(255) NOT NULL COMMENT '模型标识',
  `type`       varchar(255) COMMENT '事件类型',
  primary key (`id`)
) engine = InnoDB;

create table `hibernate_sequences`
(
  `sequence_name` varchar(255) not null,
  `next_val`      bigint,
  primary key (`sequence_name`)
) engine = InnoDB;

insert into `hibernate_sequences`(`sequence_name`, `next_val`)
values ('model_data_source_config_meta', 0);

insert into `hibernate_sequences`(`sequence_name`, `next_val`)
values ('model_meta', 0);

insert into `hibernate_sequences`(`sequence_name`, `next_val`)
values ('model_subscribe_meta', 0);

insert into `hibernate_sequences`(`sequence_name`, `next_val`)
values ('model_sequence_meta', 0);

alter table `model_meta` add unique index uk_model_name_unique(`team_id`,`app_id`,`model_alias`);
alter table `model_data_source_config_meta` add unique index uk_dataSourceName_unique(`team_id`,`data_source_name`);
alter table `model_subscribe_meta` add unique index uk_modelAlias_unique(`team_id`, `app_id`, `model_alias`);

alter table `model_meta` add index idx_model_type_index(`team_id`,`app_id`,`type`,`model_alias`);
