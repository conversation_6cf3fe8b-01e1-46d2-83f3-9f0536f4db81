# H2单元测试配置文件
spring:
  jpa:
    show-sql: true
    open-in-view: false
    hibernate:
      ddl-auto: 'none' # none, create-only, drop-and-create, update, create-drop
  sql:
    init:
      schema-locations:
        - classpath*:testdata/h2/schema.sql
logging:
  level:
    org:
      springframework:
        jdbc:
          datasource:
            init: debug

terminus:
  mqServerAddress: ${MQ_SERVER_ADDRESS:127.0.0.1:9876}
  topic: ${MQ_TOPIC:TRANTOR2_TOPIC}
  producerGroup: ${PRODUCER_GROUP:TRANTOR2Producer}
  consumerGroup: ${CONSUMER_GROUP:TRANTOR2Consumer}
  clientType: ${MQ_CLIENT_TYPE:ROCKETMQ}
  aliyun:
    accessKey: ${ROCKET_MQ_ALIYUN_ACCESS_KEY:}
    secretKey: ${ROCKET_MQ_ALIYUN_SECRET_KEY:}

workflow:
  oneService: true
  mock: false
  user: ${WORKFLOW_USER_MOCK:false}
  organization:
    mock: ${WORKFLOW_ORG_MOCK:true}
  component:
    mock: ${WORKFLOW_COMPONENT_MOCK:true}
  sessionBean: ${FLOW_USER_SESSION:userCenterSessions}
  operate:
    auth:
      open: ${FLOW_ADMIN_AUTH_OPEN:false}
  currentUser:
    check: true
  message.sender.job:
    cron: 0 0/1 * * * ?
  trantorModelEventTopic: ${TRANTOR_MODEL_EVENT_TOPIC:TRANTOR_MODEL_EVENT_DEFAULT}

trantor2:
  model:
    data:
      transaction:
        timeout: ${TRANSACTION_TIMEOUT:60000}
