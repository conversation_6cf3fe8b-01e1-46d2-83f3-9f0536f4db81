package io.terminus.trantor2.runtime.filter;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.context.MessageSource;
import org.springframework.context.support.ResourceBundleMessageSource;

import java.util.Locale;

/**
 * I18nTest
 *
 * <AUTHOR> Created on 2024/3/12 19:53
 */
public class I18nTest {

    private MessageSource messageSource;

    @BeforeEach
    public void setUp() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        messageSource.setBasenames("i18n/trantor/messages");
        messageSource.setDefaultEncoding("UTF-8");
        messageSource.setCacheSeconds(600);
        messageSource.setUseCodeAsDefaultMessage(true);
        messageSource.setDefaultLocale(Locale.SIMPLIFIED_CHINESE);
        this.messageSource = messageSource;

    }

    @Test
    public void testMessage() {
        String zhMessage = messageSource.getMessage("V0005", new Object[]{"Test_service"}, null);
        System.out.println(zhMessage);
        Assertions.assertEquals(zhMessage, "'Test_service'服务未启用");
    }

    @Test
    public void testEnMessage() {
        String enMessage = messageSource.getMessage("V0005", new Object[]{"Test_service"}, Locale.US);
        Assertions.assertEquals(enMessage, "Service 'Test_service' is not enabled");

    }

    @Test
    public void testZHMessage() {
        String enMessage = messageSource.getMessage("V0003", new Object[]{"a.b"}, null);
        System.out.println(enMessage);
        Assertions.assertEquals(enMessage, "'$.a.b'的值非对象类型值");
    }

    @Test
    public void testMessage_D0008() {
        String zhMessage = messageSource.getMessage("D0008", null, Locale.SIMPLIFIED_CHINESE);
        Assertions.assertEquals(zhMessage, "团队下不存在数据源，请配置数据源");

    }

    @Test
    public void testEnMessage_D0008() {
        String enMessage = messageSource.getMessage("D0008", null, Locale.US);
        Assertions.assertEquals(enMessage, "No data source exists under the team, please configure the data source");

    }

    @Test
    public void testMessage_V0401() {
        String zhMessage = messageSource.getMessage("V0401", new Object[]{"a", "b"}, Locale.SIMPLIFIED_CHINESE);
        Assertions.assertEquals(zhMessage, "当前a的数据有b状态，不能进行删除");

    }

    @Test
    public void testEnMessage_V0401() {
        String enMessage = messageSource.getMessage("V0401", new Object[]{"a", "b"}, Locale.US);
        Assertions.assertEquals(enMessage, "Current a data has b status, cannot delete");

    }

    @Test
    public void testfrMessage_V0401() {
        String enMessage = messageSource.getMessage("V0401", new Object[]{"a", "b"}, Locale.CANADA_FRENCH);
        Assertions.assertEquals(enMessage, "当前a的数据有b状态，不能进行删除");

    }

    @Test
    public void testMessage_V0324() {
        String zhMessage = messageSource.getMessage("V0324", new Object[]{"a", "b"}, Locale.SIMPLIFIED_CHINESE);
        System.out.println(zhMessage);
        Assertions.assertTrue(StringUtils.contains(zhMessage, "{0}"));
    }
}
