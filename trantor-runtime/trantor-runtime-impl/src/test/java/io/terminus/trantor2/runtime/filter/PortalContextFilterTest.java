package io.terminus.trantor2.runtime.filter;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.common.filter.TrantorRequestWrapper;
import io.terminus.trantor2.module.runtime.service.PortalService;
import io.terminus.trantor2.module.util.LicenseThread;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.net.URL;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * <AUTHOR>
 **/
class PortalContextFilterTest {

    @Test
    void obtainPortal() throws IOException {
        PortalService portalService = Mockito.mock(PortalService.class);
        LicenseThread licenseThread = Mockito.mock(LicenseThread.class);
        Mockito.when(portalService.findPortalByDomainName("terp-test.app.terminus.io")).thenReturn(portalByDomainName());
        Mockito.when(portalService.findPortalByCode("test")).thenReturn(portalByCode());
        TrantorRequestWrapper requestWrapper = Mockito.mock(TrantorRequestWrapper.class);
        MockHttpServletResponse response = new MockHttpServletResponse();
        Mockito.when(requestWrapper.obtainRequestRefererURL()).thenReturn(new URL("https://terp-test.app.terminus.io"));

        PortalContextFilter filter = new PortalContextFilter(portalService, licenseThread);

        Portal portal = ReflectionTestUtils.invokeMethod(filter, "obtainPortal", requestWrapper);
        assertNull(portal.getPath());
        assertEquals(portal.getObtainType(), Portal.ObtainType.refererHost);
        assertEquals(portal.getLoginCallbackUrl(), "https://terp-test.app.terminus.io");

        Mockito.when(portalService.findPortalByDomainName("terp-test.app.terminus.io")).thenReturn(null);
        Mockito.when(requestWrapper.obtainPortalKeyByReferer()).thenReturn("test");
        portal = ReflectionTestUtils.invokeMethod(filter, "obtainPortal", requestWrapper);
        assertEquals(portal.getPath(), "test");
        assertEquals(portal.getObtainType(), Portal.ObtainType.refererPath);
        assertEquals(portal.getLoginCallbackUrl(), "https://terp-test.app.terminus.io/test");

        TrantorContext.init();
        filter.doFilter(requestWrapper, response, ((servletRequest, servletResponse) -> {
            assertEquals("test", TrantorContext.getCurrentPortal().getCode());
        }));

    }

    private Portal portalByDomainName() {
        return Portal.builder()
                .id(1L)
                .teamCode("test")
                .teamId(1L)
                .code("test")
                .accessKey("ak")
                .accessSecret("as")
                .loginUrl("https://iam.test.com")
                .loginCallbackUrl("https://terp-test.app.terminus.io")
                .obtainType(Portal.ObtainType.refererHost)
                .build();
    }

    private Portal portalByDomainNamePath() {
        return Portal.builder()
                .id(1L)
                .teamCode("test")
                .teamId(1L)
                .code("test")
                .accessKey("ak")
                .accessSecret("as")
                .loginUrl("https://iam.test.com")
                .loginCallbackUrl("https://terp-test.app.terminus.io/path")
                .obtainType(Portal.ObtainType.refererHost)
                .build();
    }

    private Portal portalByCode() {
        return Portal.builder()
                .id(1L)
                .teamCode("test")
                .teamId(1L)
                .code("test")
                .accessKey("ak")
                .accessSecret("as")
                .loginUrl("https://iam.test.com")
                .loginCallbackUrl("https://terp-test.app.terminus.io/test")
                .path("test")
                .obtainType(Portal.ObtainType.refererPath)
                .build();
    }
}
