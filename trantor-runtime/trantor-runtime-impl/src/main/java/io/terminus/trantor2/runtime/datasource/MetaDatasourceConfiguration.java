package io.terminus.trantor2.runtime.datasource;

import com.blazebit.persistence.Criteria;
import com.blazebit.persistence.CriteriaBuilderFactory;
import com.blazebit.persistence.spi.CriteriaBuilderConfiguration;
import com.zaxxer.hikari.HikariDataSource;
import io.terminus.trantor2.common.internal.OnTrantor2StandaloneModeCondition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.*;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import java.util.Properties;

/**
 * 元信息数据源配置
 * TODO 运行态非元信息表暂时放这里加载，后续需要移到 {@link RuntimeDatasourceConfiguration} 并将对应的 bean 设置为 @Primary
 *
 * <AUTHOR>
 */
@Configuration
@EnableJpaRepositories(
    basePackages = {
        "io.terminus.trantor2.meta.**",
        "io.terminus.trantor2.datasource.repository",
        "io.terminus.trantor2.module.repository",
        "io.terminus.trantor2.service.management.**",
        "io.terminus.trantor2.connector.**"
    },
    entityManagerFactoryRef = "metaEntityManagerFactory",
    transactionManagerRef = "metaTransactionManager")
@Conditional(OnTrantor2StandaloneModeCondition.class)
public class MetaDatasourceConfiguration {
    public static final String[] packagesToScan = {
        "io.terminus.trantor2.meta.**",
        // TODO 运行态旧的非元信息表暂时放这里加载, 需要数据迁移到运行态数据库
        "io.terminus.trantor2.datasource.entity.**",
        "io.terminus.trantor2.service.management.model",
        "io.terminus.trantor2.module.entity.**",
        "io.terminus.trantor2.search.domain.**",
        "io.terminus.trantor2.connector.**"
    };
    @Autowired
    private Environment env;

    @Bean
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.meta")
    public DataSourceProperties metaDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Primary
    @Bean(name = "metaDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.meta.hikari")
    public HikariDataSource metaDataSource(@Qualifier("metaDataSourceProperties") DataSourceProperties properties) {
        return properties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }

    @Primary
    @Bean(name = "metaEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean metaEntityManagerFactory(@Qualifier("metaDataSource") HikariDataSource metaDataSource) {
        LocalContainerEntityManagerFactoryBean em
            = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(metaDataSource);
        return RuntimeDatasourceConfiguration.getLocalContainerEntityManagerFactoryBean(em, packagesToScan, env);
    }

    @Bean
    @Scope(ConfigurableBeanFactory.SCOPE_SINGLETON)
    public CriteriaBuilderFactory createCriteriaBuilderFactory(@Qualifier("metaEntityManagerFactory") EntityManagerFactory metaEntityManagerFactory) {
        CriteriaBuilderConfiguration config = Criteria.getDefault();
        return config.createCriteriaBuilderFactory(metaEntityManagerFactory);
    }

    @Primary
    @Bean
    public PlatformTransactionManager metaTransactionManager(@Qualifier("metaEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        JpaTransactionManager jpaTransactionManager = new JpaTransactionManager(entityManagerFactory);
        Properties properties = new Properties();
        properties.setProperty("hibernate.dialect", "org.springframework.orm.jpa.vendor.HibernateJpaDialect");
        jpaTransactionManager.setJpaProperties(properties);
        return jpaTransactionManager;
    }

    @Primary
    @Bean(name = "metaEntityManager")
    public EntityManager entityManager(@Qualifier("metaEntityManagerFactory") EntityManagerFactory factory) {
        return factory.createEntityManager();
    }
}
