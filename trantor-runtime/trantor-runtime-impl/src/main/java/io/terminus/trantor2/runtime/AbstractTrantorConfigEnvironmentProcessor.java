package io.terminus.trantor2.runtime;


import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.boot.env.YamlPropertySourceLoader;
import org.springframework.core.Ordered;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractTrantorConfigEnvironmentProcessor implements EnvironmentPostProcessor, Ordered {
    protected final AtomicBoolean initialized = new AtomicBoolean(false);

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        if (initialized.compareAndSet(false, true)) {
            System.setProperty("otel.exporter.otlp.metrics.temporality.preference", "DELTA");
            System.setProperty("otel.exporter.otlp.protocol", "http/protobuf");
//            System.setProperty("otel.exporter.otlp.endpoint", "http://otel-collector.trantor-collector.svc.cluster.local:4318");

            MutablePropertySources propertySources = environment.getPropertySources();
            if (!propertySources.contains(configFileName())) {
                // 加载 trantor-config.yml
                loadTrantorConfig(propertySources);
            }
        }
    }

    protected void loadTrantorConfig(MutablePropertySources propertySources) {
        DefaultResourceLoader resourceLoader = new DefaultResourceLoader(null);
        Resource trantorConfigResource = null;
        for (String filePath : configFilePath()) {
            trantorConfigResource = resourceLoader.getResource(filePath);
            if (trantorConfigResource.exists()) {
                break;
            }
        }
        if (trantorConfigResource == null) {
            return;
        }
        List<PropertySource<?>> sources;
        try {
            sources = new YamlPropertySourceLoader().load(configFileName(), trantorConfigResource);
        } catch (IOException e) {
            log.error("Can not load property source file of [{}]", configFileName());
            return;
        }
        if (sources.isEmpty()) {
            log.error("Can not load property source file of [{}]", configFileName());
            return;
        }
        if (sources.size() > 1) {
            log.warn("Loaded more than one source files of [{}], only get first.", trantorConfigResource);
        }
        propertySources.addLast(sources.get(0));
    }

    protected abstract String configFileName();

    protected abstract String[] configFilePath();
}
