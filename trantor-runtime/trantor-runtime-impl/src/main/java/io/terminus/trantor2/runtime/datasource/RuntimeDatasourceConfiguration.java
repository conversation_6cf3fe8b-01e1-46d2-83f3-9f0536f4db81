package io.terminus.trantor2.runtime.datasource;

import com.zaxxer.hikari.HikariDataSource;
import io.terminus.trantor2.common.internal.Internal;
import io.terminus.trantor2.common.internal.OnTrantor2StandaloneModeCondition;
import org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import java.util.HashMap;
import java.util.Properties;

/**
 * <AUTHOR>
 */

@Configuration
@EnableJpaRepositories(
    basePackages = {
        "io.terminus.trantor2.**.runtime.**"
    },
    entityManagerFactoryRef = "runtimeEntityManagerFactory",
    transactionManagerRef = "runtimeTransactionManager")
@Internal
@Conditional(OnTrantor2StandaloneModeCondition.class)
public class RuntimeDatasourceConfiguration {
    public final static String[] packagesToScan = {
        "io.terminus.trantor2.runtime",
        "io.terminus.trantor2.**.runtime.**"
    };
    @Autowired
    private Environment env;

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.trantor-runtime")
    public DataSourceProperties runtimeDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "runtimeDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.trantor-runtime.hikari")
    public HikariDataSource runtimeDataSource(@Qualifier("runtimeDataSourceProperties") DataSourceProperties properties) {
        return properties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }

    @Bean(name = "runtimeEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean runtimeEntityManager(@Qualifier("runtimeDataSource") HikariDataSource runtimeDataSource) {
        LocalContainerEntityManagerFactoryBean em
            = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(runtimeDataSource);
        return getLocalContainerEntityManagerFactoryBean(em, packagesToScan, env);
    }

    @Bean(name = "runtimeEntityManager")
    public EntityManager entityManager(@Qualifier("runtimeEntityManagerFactory") EntityManagerFactory factory) {
        return factory.createEntityManager();
    }

    @Bean(name = "runtimeTransactionManager")
    public JpaTransactionManager runtimeTransactionManager(@Qualifier("runtimeEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        JpaTransactionManager jpaTransactionManager = new JpaTransactionManager(entityManagerFactory);
        Properties properties = new Properties();
        properties.setProperty("hibernate.dialect", "org.springframework.orm.jpa.vendor.HibernateJpaDialect");
        jpaTransactionManager.setJpaProperties(properties);
        return jpaTransactionManager;
    }

    @NotNull
    public static LocalContainerEntityManagerFactoryBean getLocalContainerEntityManagerFactoryBean(LocalContainerEntityManagerFactoryBean em, String[] packagesToScan, Environment env) {
        em.setPackagesToScan(packagesToScan);
        HibernateJpaVendorAdapter vendorAdapter
            = new HibernateJpaVendorAdapter();
        em.setJpaVendorAdapter(vendorAdapter);
        HashMap<String, Object> properties = new HashMap<>();
        properties.put("hibernate.implicit_naming_strategy", SpringImplicitNamingStrategy.class.getName());
        properties.put("hibernate.physical_naming_strategy", CamelCaseToUnderscoresNamingStrategy.class.getName());
        properties.put("hibernate.dialect",
            env.getProperty("spring.jpa.database-platform"));
        properties.put("hibernate.hbm2ddl.auto",
            env.getProperty("spring.jpa.hibernate.ddl-auto"));
        em.setJpaPropertyMap(properties);
        return em;
    }
}
