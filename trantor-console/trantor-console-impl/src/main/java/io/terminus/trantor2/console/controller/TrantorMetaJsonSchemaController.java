package io.terminus.trantor2.console.controller;

import cn.hutool.core.io.FileUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.console.util.MetaJsonSchemaGenerator;
import jodd.net.URLCoder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 **/
@Slf4j
@Tag(name = "元数据json schema", description = "meta schema")
@RestController
@RequestMapping("/api/trantor/devops/meta/json/schema")
@RequiredArgsConstructor
public class TrantorMetaJsonSchemaController {

    @GetMapping("/download")
    public ResponseEntity<ByteArrayResource> downloadMetaJsonSchema() {
        Path path = Paths.get(MetaJsonSchemaGenerator.META_JSON_SCHEMA_PATH_ZIP);
        if (!Files.exists(path)) {
            MetaJsonSchemaGenerator.generateMetaJsonSchema();
        }
        File zip = new File(MetaJsonSchemaGenerator.META_JSON_SCHEMA_PATH_ZIP);
        ByteArrayResource resource = new ByteArrayResource(FileUtil.readBytes(zip));

        HttpHeaders headers = new HttpHeaders();
        String encodedFileName = URLCoder.encode("schema.zip");
        // RFC 5987 格式
        String contentDisposition = "attachment; filename=\"" + encodedFileName + "\"; filename*=utf-8''" + encodedFileName;
        headers.add(HttpHeaders.CONTENT_DISPOSITION, contentDisposition);
        headers.add(HttpHeaders.CONTENT_TYPE, "application/octet-stream");
        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(resource);
    }
}
