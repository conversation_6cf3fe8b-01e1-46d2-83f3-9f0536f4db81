package io.terminus.trantor2.console.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.meta.platform.TrantorVersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Tag(name = "Trantor版本信息")
@RestController
@RequestMapping("/api/trantor/console")
public class TrantorVersionController {

    @Autowired
    private TrantorVersionService trantorVersionService;

    @GetMapping(value = "/version")
    @Operation(summary = "获取版本信息")
    public Response<String> getTrantorVersion() {
        return Response.ok(trantorVersionService.getTrantorVersionWithDate());
    }

}
