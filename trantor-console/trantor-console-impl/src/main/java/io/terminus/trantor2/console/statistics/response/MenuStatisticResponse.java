package io.terminus.trantor2.console.statistics.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 运维态模型统计响应
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MenuStatisticResponse extends StatisticsBaseResponse {
    @Schema(description = "菜单路径")
    private String path;

    @Schema(description = "总访问次数")
    private long totalVisits;

    @Schema(description = "总访问人数")
    private long totalUsers;
}
