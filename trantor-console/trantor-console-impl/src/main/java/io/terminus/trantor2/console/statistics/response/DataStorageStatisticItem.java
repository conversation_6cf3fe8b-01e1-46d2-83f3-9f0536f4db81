package io.terminus.trantor2.console.statistics.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DataStorageStatisticItem implements Serializable {
    @Schema(description = "数据源名称")
    private String datasourceName;

    @Schema(description = "数据库名称")
    private String databaseName;

    @Schema(description = "应用模块名称列表(逗号分隔)")
    private List<String> moduleNames;

    @Schema(description = "数据源数据存储占用空间(GB)")
    private Double used;
}