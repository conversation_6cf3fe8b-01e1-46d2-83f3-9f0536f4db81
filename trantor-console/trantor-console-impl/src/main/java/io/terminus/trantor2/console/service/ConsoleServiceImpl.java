package io.terminus.trantor2.console.service;

import io.terminus.trantor2.console.vo.Console;
import io.terminus.trantor2.properties.management.ConsolePublicMarketProperties;
import io.terminus.trantor2.properties.management.nexus.LocalNexusProperties;
import io.terminus.trantor2.properties.management.nexus.NexusConfigProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
public class ConsoleServiceImpl implements ConsoleService {
    @Autowired
    private Environment environment;
    @Autowired
    private ConsolePublicMarketProperties publicMarketProperties;
    @Autowired
    private NexusConfigProperties nexusConfigProperties;
    private Console console;

    @PostConstruct
    public void init() {
        console = buildConsole();
    }

    @Override
    public Console getConsole() {
        return console;
    }

    private Console buildConsole() {
        String workspace = environment.getProperty("DICE_WORKSPACE");
        String projectName = environment.getProperty("DICE_PROJECT_NAME");
        String orgName = environment.getProperty("DICE_ORG_NAME");
        return Console.builder()
                .publicMarketDevMode(publicMarketProperties.getOrganization().equals(orgName)
                        && publicMarketProperties.getProject().equals(projectName)
                        && publicMarketProperties.getEnv().equals(workspace))
                .localMarketDevMode(Optional.ofNullable(nexusConfigProperties.getLocal())
                        .map(LocalNexusProperties::getDevEnabled).orElse(false))
                .build();
    }
}
