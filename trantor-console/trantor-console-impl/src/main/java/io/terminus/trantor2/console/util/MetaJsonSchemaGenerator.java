package io.terminus.trantor2.console.util;

import cn.hutool.core.io.FileUtil;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.victools.jsonschema.generator.Option;
import com.github.victools.jsonschema.generator.OptionPreset;
import com.github.victools.jsonschema.generator.SchemaGenerator;
import com.github.victools.jsonschema.generator.SchemaGeneratorConfig;
import com.github.victools.jsonschema.generator.SchemaGeneratorConfigBuilder;
import com.github.victools.jsonschema.generator.SchemaVersion;
import com.github.victools.jsonschema.module.jackson.JacksonModule;
import com.google.common.collect.Lists;
import io.terminus.trantor2.common.utils.VersionUtil;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.resource.ResourceBaseMeta;
import io.terminus.trantor2.meta.resource.ResourceProps;
import io.terminus.trantor2.meta.schema.TMetaAvailable;
import io.terminus.trantor2.meta.schema.TMetaDeprecate;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AssignableTypeFilter;

import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 **/
@Slf4j
public class MetaJsonSchemaGenerator {
    public static final String META_JSON_SCHEMA_PATH_ZIP = "/trantor-meta-schema.zip";
    public static final String META_JSON_SCHEMA_CHANGE_LOG = "/trantor-meta-schema-change-log";

    private static final String CHANGE_LOG_TIPS = "该分析结果仅供参考\n";
    private static final String CHANGE_LOG_FORMAT = "当前版本元数据结构最低支持%s版本\n";
    private static final String META_SOURCE_PROPS_FIELD = "resourceProps";
    private static final String LOWEST_SUPPORT_VERSION = "2.5.24.0630";

    public static void generateMetaChangeLog() {
        generateMetaChangeLog(META_JSON_SCHEMA_CHANGE_LOG);
    }

    public static void generateMetaChangeLog(String fileName) {

        List<String> metas = findImplementations(ResourceBaseMeta.class, "io.terminus.trantor2");

        List<MetaClassSchemaChangeLog> changeLogs = new ArrayList<>();
        for (String meta : metas) {
            changeLogs.addAll(collectMetaChangeLog(meta));
        }

        String maxVersion = changeLogs.stream().map(MetaClassSchemaChangeLog::getVersion).max(VersionUtil::compareVersions).orElse(LOWEST_SUPPORT_VERSION);
        String content = changeLogs.stream().map(MetaClassSchemaChangeLog::getChangeLog).collect(Collectors.joining("\n"));

        FileUtil.writeBytes((CHANGE_LOG_TIPS + String.format(CHANGE_LOG_FORMAT, maxVersion) + content + "\n").getBytes(StandardCharsets.UTF_8), fileName);
    }

    private static List<MetaClassSchemaChangeLog> collectMetaChangeLog(String meta) {
        try {
            Class<?> clazz = Class.forName(meta);
            ResourceBaseMeta<?> resource = (ResourceBaseMeta<?>) clazz.getDeclaredConstructor().newInstance();
            Class<?> type = getPropsClass(clazz);
            if (type == null) {
                log.warn("can not find props field in {}", meta);
                return Lists.newArrayList();
            }
            List<MetaClassSchemaChangeLog> changeLogs = collectMetaChangeLog(resource.getMetaType(), type, META_SOURCE_PROPS_FIELD, new HashSet<>());

            if (clazz.isAnnotationPresent(TMetaAvailable.class)) {
                String startVersion = clazz.getAnnotation(TMetaAvailable.class).value().getLabel();
                MetaClassSchemaChangeLog changeLog = new MetaClassSchemaChangeLog(resource.getMetaType(), true, startVersion);
                changeLogs.add(changeLog);
            }
            if (clazz.isAnnotationPresent(TMetaDeprecate.class)) {
                String endVersion = clazz.getAnnotation(TMetaDeprecate.class).value().getLabel();
                MetaClassSchemaChangeLog changeLog = new MetaClassSchemaChangeLog(resource.getMetaType(), false, endVersion);
                changeLogs.add(changeLog);
            }

            return changeLogs;
        } catch (Exception e) {
            log.error("generate meta change log error, meta {}", meta, e);
        }
        return Lists.newArrayList();
    }

    private static List<MetaClassSchemaChangeLog> collectMetaChangeLog(MetaType metaType, Class<?> clazz, String fieldPath, Set<Class<?>> visited)  {
        List<MetaClassSchemaChangeLog> changeLogs = new ArrayList<>();
        if (visited.contains(clazz)) {
            return changeLogs;
        }
        visited.add(clazz);

        // clazz是接口或者抽象类，则递归查找实现类
        if (clazz.isInterface() || Modifier.isAbstract(clazz.getModifiers())) {
            findImplementations(clazz, "io.terminus.trantor2").forEach(impl -> {
                try {
                    changeLogs.addAll(collectMetaChangeLog(metaType, Class.forName(impl), fieldPath, visited));
                } catch (ClassNotFoundException e) {
                    log.error("can not find class {}", impl, e);
                }
            });
            return changeLogs;
        }

        while (clazz != null && clazz != Object.class) {
            for (Field field : clazz.getDeclaredFields()) {
                String currentPath = fieldPath.isEmpty() ? field.getName() : fieldPath + "." + field.getName();
                if (field.isAnnotationPresent(TMetaAvailable.class)) {
                    String startVersion = field.getAnnotation(TMetaAvailable.class).value().getLabel();
                    MetaFieldSchemaChangeLog changeLog = new MetaFieldSchemaChangeLog(metaType, true, startVersion, currentPath);
                    changeLogs.add(changeLog);
                }
                if (field.isAnnotationPresent(TMetaDeprecate.class)) {
                    String endVersion = field.getAnnotation(TMetaDeprecate.class).value().getLabel();
                    MetaFieldSchemaChangeLog changeLog = new MetaFieldSchemaChangeLog(metaType, false, endVersion, currentPath);
                    changeLogs.add(changeLog);
                }
                if (field.getType().isEnum()) {
                    List<MetaEnumSchemaChangeLog> changeLog = collectEnumChangeLog(metaType, field.getType(), currentPath);
                    changeLogs.addAll(changeLog);
                }
                if (!field.getType().isPrimitive() && !field.getType().isEnum() && field.getType().getName().startsWith("io.terminus.trantor2")) {
                    changeLogs.addAll(collectMetaChangeLog(metaType, field.getType(), currentPath, visited));
                }
            }
            clazz = clazz.getSuperclass();
        }
        return changeLogs;
    }

    private static List<MetaEnumSchemaChangeLog> collectEnumChangeLog(MetaType metaType, Class<?> type, String currentPath) {
        List<MetaEnumSchemaChangeLog> changeLogs = new ArrayList<>();
        if (!type.isEnum()) {
            return changeLogs;
        }

        Arrays.stream(type.getEnumConstants()).forEach(item -> {
            if (item instanceof Enum) {
                Enum<?> enumItem = (Enum<?>) item;
                Class<?> declaringClass = enumItem.getDeclaringClass();
                try {
                    Field field = declaringClass.getField(enumItem.name());
                    if (field.isAnnotationPresent(TMetaAvailable.class)) {
                        String startVersion = field.getAnnotation(TMetaAvailable.class).value().getLabel();
                        MetaEnumSchemaChangeLog changeLog = new MetaEnumSchemaChangeLog(metaType, true, startVersion, currentPath, enumItem.name());
                        changeLogs.add(changeLog);
                    }
                    if (field.isAnnotationPresent(TMetaDeprecate.class)) {
                        String endVersion = field.getAnnotation(TMetaDeprecate.class).value().getLabel();
                        MetaEnumSchemaChangeLog changeLog = new MetaEnumSchemaChangeLog(metaType, false, endVersion, currentPath, enumItem.name());
                        changeLogs.add(changeLog);
                    }
                } catch (Exception e) {
                    log.error("error when collect enum change log", e);
                }
            }
        });
        return changeLogs;
    }

    private static Field findPropsField(Class<?> clazz) {
        while (clazz != null && clazz != Object.class) {
            for (Field field : clazz.getDeclaredFields()) {
                if (ResourceProps.class.isAssignableFrom(field.getType())) {
                    return field;
                }
            }
            clazz = clazz.getSuperclass();
        }

        return null;
    }

    private static Class<?> getPropsClass(Class<?> clazz) {
        Type genericSuperclass = clazz.getGenericSuperclass();
        if (!(genericSuperclass instanceof ParameterizedType)) {
            genericSuperclass = clazz.getSuperclass().getGenericSuperclass();
        }
        ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;
        return (Class<?>) parameterizedType.getActualTypeArguments()[0];
    }

    public static void generateMetaJsonSchema() {
        generateMetaJsonSchema(META_JSON_SCHEMA_PATH_ZIP);
    }

    public static void generateMetaJsonSchema(String basePath) {
        Path path = Paths.get(basePath);

        List<String> metas = findImplementations(ResourceBaseMeta.class, "io.terminus.trantor2");

        try (
                OutputStream out = Files.newOutputStream(path);
                ZipOutputStream zos = new ZipOutputStream(out)
        ) {
            for (String meta : metas) {
                generateJsonSchemaFile(meta, zos);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void generateJsonSchemaFile(String metaClass, ZipOutputStream zos) {
        try {
            Class<?> clazz = Class.forName(metaClass);
            String simpleName = clazz.getSimpleName();
            String fileName = simpleName + ".json";

            SchemaGeneratorConfigBuilder configBuilder = new SchemaGeneratorConfigBuilder(SchemaVersion.DRAFT_2020_12, OptionPreset.PLAIN_JSON);
            configBuilder.with(Option.EXTRA_OPEN_API_FORMAT_VALUES);
            configBuilder.with(new JacksonModule());
            SchemaGeneratorConfig config = configBuilder.build();
            SchemaGenerator generator = new SchemaGenerator(config);
            ObjectNode jsonNodes = generator.generateSchema(clazz);

            String schema = ObjectJsonUtil.serializeIndent(jsonNodes);

            ZipEntry zipEntry = new ZipEntry(fileName);
            zos.putNextEntry(zipEntry);
            byte[] bytes = schema.getBytes(StandardCharsets.UTF_8);
            zos.write(bytes, 0, bytes.length);
            zos.closeEntry();
        } catch (Exception e) {
            log.error("meta class not found: {}", metaClass, e);
        }
    }

    public static List<String> findImplementations(Class<?> interfaceClass, String basePackage) {
        ClassPathScanningCandidateComponentProvider scanner = new ClassPathScanningCandidateComponentProvider(false);
        scanner.addIncludeFilter(new AssignableTypeFilter(interfaceClass));

        List<String> list = new ArrayList<>();
        Set<BeanDefinition> beanDefinitions = scanner.findCandidateComponents(basePackage);
        for (BeanDefinition beanDefinition : beanDefinitions) {
            list.add(beanDefinition.getBeanClassName());
        }
        return list;
    }

    @Getter
    @AllArgsConstructor
    private static class MetaClassSchemaChangeLog {
        private final MetaType metaType;
        private final boolean isAvailable;
        private final String version;

        public String getChangeLog() {
            return "[" + getMetaType().getLabel() + "]" +
                    "元数据：" +
                    "从" +
                    getVersion() +
                    "版本开始" +
                    (isAvailable() ? "支持" : "废弃");
        }
    }

    @Getter
    private static class MetaFieldSchemaChangeLog extends MetaClassSchemaChangeLog {
        private final String fieldPath;

        public MetaFieldSchemaChangeLog(MetaType metaType, boolean isAvailable, String version, String fieldPath) {
            super(metaType, isAvailable, version);
            this.fieldPath = fieldPath;
        }

        public String getChangeLog() {
            return "[" + getMetaType().getLabel() + "]" +
                    "元数据schema变化：" +
                    "字段[" + getFieldPath() + "]，从" +
                    getVersion() +
                    "版本开始" +
                    (isAvailable() ? "支持" : "废弃");
        }
    }

    @Getter
    private static class MetaEnumSchemaChangeLog extends MetaFieldSchemaChangeLog {
        private final String enumName;

        public MetaEnumSchemaChangeLog(MetaType metaType, boolean isAvailable, String version, String fieldPath, String enumName)  {
            super(metaType, isAvailable, version, fieldPath);
            this.enumName = enumName;
        }

        public String getChangeLog() {
            return "[" + getMetaType().getLabel() + "]" +
                    "元数据schema变化：" +
                    "字段[" + getFieldPath() + "]枚举值[" +
                    getEnumName() + "]，从" +
                    getVersion() +
                    "版本开始" +
                    (isAvailable() ? "支持" : "废弃");
        }
    }
}
