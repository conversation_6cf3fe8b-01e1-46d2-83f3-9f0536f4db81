package io.terminus.trantor2.trigger.service.impl;

import io.terminus.trantor2.trigger.meta.TriggerMeta;
import io.terminus.trantor2.trigger.service.TriggerQueryService;

import java.util.List;

/**
 * <AUTHOR>
 */
public class TriggerQueryServiceImpl implements TriggerQueryService {

    @Override
    public List<TriggerMeta> findAllModules(Object object) {
        return null;
    }

    @Override
    public TriggerMeta findByKey(String key) {
        return null;
    }

    @Override
    public TriggerMeta findById(Long id) {
        return null;
    }

    @Override
    public String findKeyById(Long id) {
        return null;
    }

}
