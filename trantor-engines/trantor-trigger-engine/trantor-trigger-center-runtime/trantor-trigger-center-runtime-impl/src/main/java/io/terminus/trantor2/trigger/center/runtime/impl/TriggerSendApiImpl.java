package io.terminus.trantor2.trigger.center.runtime.impl;

import io.terminus.common.event.enums.MessageSendType;
import io.terminus.common.event.model.TrantorMessage;
import io.terminus.common.event.producer.EventProducer;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.trigger.center.runtime.api.TriggerSendApi;
import io.terminus.trantor2.trigger.center.runtime.model.TriggerSendRequest;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
public class TriggerSendApiImpl implements TriggerSendApi {

    private final EventProducer eventProducer;

    @Override
    public Response<String> send(TriggerSendRequest request) {
        TrantorMessage message = new TrantorMessage();
        message.setTeam(request.getTeamKey());
        message.setModule(request.getModuleKey());
        message.setModelKey(request.getRelatedModelKey());
        message.setSendType(MessageSendType.IMMEDIATE);
        message.setTopic(request.getTopic());
        message.setTags(request.getTag());
        message.setBody(request.getBody());

        String UUID = eventProducer.send(message);
        return Response.ok(UUID);
    }

    @Override
    public Response<String> transactionSend(TriggerSendRequest request) {
        TrantorMessage message = new TrantorMessage();
        message.setTeam(request.getTeamKey());
        message.setModule(request.getModuleKey());
        message.setModelKey(request.getRelatedModelKey());
        message.setSendType(MessageSendType.TRANSACTION);
        message.setTopic(request.getTopic());
        message.setTags(request.getTag());
        message.setBody(request.getBody());
//        message.addProperty("modelKey", "keyValue");

        String UUID = eventProducer.send(message);
        return Response.ok(UUID);
    }

    @Override
    public Response<Boolean> commit(String UUID) {
        return Response.ok(eventProducer.commit(UUID));
    }

    @Override
    public Response<Boolean> rollback(String UUID) {
        return Response.ok(eventProducer.rollback(UUID));
    }
}
