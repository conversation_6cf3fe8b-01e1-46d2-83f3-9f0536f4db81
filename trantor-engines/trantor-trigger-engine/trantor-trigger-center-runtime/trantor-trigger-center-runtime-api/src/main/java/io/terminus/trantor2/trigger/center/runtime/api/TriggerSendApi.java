package io.terminus.trantor2.trigger.center.runtime.api;

import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.trigger.center.runtime.model.TriggerSendRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(value = "trigger-center")
public interface TriggerSendApi {

    @PostMapping(value = "/api/trantor/trigger/internal/send")
    Response<String> send(@RequestBody TriggerSendRequest request);

    @PostMapping(value = "/api/trantor/trigger/internal/transaction/send")
    Response<String> transactionSend(@RequestBody TriggerSendRequest request);

    @PostMapping(value = "/api/trantor/trigger/internal/transaction/send/commit")
    Response<Boolean> commit(@RequestBody String request);

    @PostMapping(value = "/api/trantor/trigger/internal/transaction/send/rollback")
    Response<Boolean> rollback(@RequestBody String request);

}
