package io.terminus.trantor2.trigger.runtime.handler;

import io.terminus.common.rocketmq.exception.MessageHandlerException;
import io.terminus.common.rocketmq.listener.MessageHandler;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.trigger.runtime.api.model.TriggerEvent;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @date 2022/2/17
 */
@Slf4j
public class TriggerReflectionHandler implements MessageHandler<TriggerEvent> {

    private final Object bean;
    private final Method method;
    private final Type type;
    private final int parameterCount;

    public TriggerReflectionHandler(Object bean, Method method, Type type) {
        this.bean = bean;
        this.method = method;
        this.type = type;
        this.parameterCount = method.getParameterCount();
    }

    @Override
    public void process(TriggerEvent event, String msgId) {
        Object data = null;
        try {
            if (type instanceof Class<?>) {
                data = JsonUtil.NON_INDENT.getObjectMapper().convertValue(event.getParams(), (Class) type);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        try {
            if (parameterCount > 1) {
                // 支持第二个参数作为 msgId
                method.invoke(bean, data, msgId);
            } else {
                method.invoke(bean, data);
            }
        } catch (IllegalAccessException e) {
            throw new MessageHandlerException(e.getMessage(), e);
        } catch (InvocationTargetException e) {
            Throwable target = e.getTargetException();
            if (target instanceof RuntimeException) {
                throw (RuntimeException) target;
            }
            throw new MessageHandlerException(target.getMessage(), e);
        }
    }
}
