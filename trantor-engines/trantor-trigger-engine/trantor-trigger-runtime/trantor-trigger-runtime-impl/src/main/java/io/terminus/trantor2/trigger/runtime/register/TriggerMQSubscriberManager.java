package io.terminus.trantor2.trigger.runtime.register;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.terminus.common.rocketmq.common.ConsumeMode;
import io.terminus.common.rocketmq.common.MessageModel;
import io.terminus.common.rocketmq.consumer.ConsumerManager;
import io.terminus.common.rocketmq.consumer.TerminusMQConsumer;
import io.terminus.common.rocketmq.core.message.decoder.MessageDecoder;
import io.terminus.common.rocketmq.entity.Consumer;
import io.terminus.common.rocketmq.entity.Subscription;
import io.terminus.trantor2.meta.api.dto.ModuleInfo;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.context.MetaContext;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.service.TeamService;
import io.terminus.trantor2.properties.TriggerMQProperties;
import io.terminus.trantor2.service.engine.executor.ServiceExecutor;
import io.terminus.trantor2.trigger.meta.EventDefinitionMeta;
import io.terminus.trantor2.trigger.meta.TriggerMeta;
import io.terminus.trantor2.trigger.repository.EventDefinitionRepo;
import io.terminus.trantor2.trigger.repository.TriggerRepo;
import io.terminus.trantor2.trigger.runtime.annotation.EventListener;
import io.terminus.trantor2.trigger.runtime.annotation.Trigger;
import io.terminus.trantor2.trigger.runtime.api.TriggerConsumerRegister;
import io.terminus.trantor2.trigger.runtime.api.TriggerGroupStrategy;
import io.terminus.trantor2.trigger.runtime.api.model.TriggerEvent;
import io.terminus.trantor2.trigger.runtime.handler.TriggerHandler;
import io.terminus.trantor2.trigger.runtime.handler.TriggerReflectionHandler;
import io.terminus.trantor2.trigger.runtime.utils.TriggerKeyUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.BeansException;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.core.annotation.Order;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Trigger 注册类
 */
@RequiredArgsConstructor
public class TriggerMQSubscriberManager implements TriggerConsumerRegister, ApplicationContextAware {

    private final MessageDecoder decoder;
    private final TriggerMQProperties properties;
    private final TriggerGroupStrategy groupStrategy;
    private final TriggerRepo triggerRepo;
    private final EventDefinitionRepo eventDefinitionRepo;
    private final ConsumerManager consumerManager;
    private final ServiceExecutor serviceExecutor;
    private final TeamService teamService;

    private ConfigurableApplicationContext applicationContext;
    private List<TerminusMQConsumer> terminusMQConsumers = Lists.newArrayList();

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = (ConfigurableApplicationContext) applicationContext;
    }

    @org.springframework.context.event.EventListener(classes = ApplicationReadyEvent.class)
    @Order
    @Override
    public void registerAll() {
        List<Consumer> consumers = Lists.newArrayList();

        // load trigger from console
        loadConsumerFromTriggerMeta(consumers);

        // load trigger from annotation
        loadConsumerFromAnnotation(consumers);

        this.terminusMQConsumers = consumerManager.register(consumers);
    }

    @Override
    public void register(List<TriggerMeta> triggers) {
        Map<String, Map<String, Set<TriggerMeta>>> consumerMap = group(triggers);

        for (Map.Entry<String, Map<String, Set<TriggerMeta>>> entry : consumerMap.entrySet()) {
            String consumerGroup = entry.getKey();
            Map<String, Set<TriggerMeta>> topics = entry.getValue();

            TerminusMQConsumer existConsumer = terminusMQConsumers.stream().filter(consumer -> StringUtils.equals(consumer.getConsumerGroup(), consumerGroup)).findAny().orElse(null);


            // If this trigger is not registered, register it.
            if (existConsumer == null) {
                for (Map.Entry<String, Set<TriggerMeta>> topicEntry : topics.entrySet()) {
                    String topic = topicEntry.getKey();
                    Set<TriggerMeta> teamTriggers = topicEntry.getValue();

                    TerminusMQConsumer terminusMQConsumer = consumerManager.register(loadConsumer(consumerGroup, topic, teamTriggers));
                    if (CollectionUtils.isEmpty(terminusMQConsumers)) {
                        terminusMQConsumers = Lists.newArrayList();
                    }
                    terminusMQConsumers.add(terminusMQConsumer);
                }
            }
        }
    }

    @Override
    public void stop(List<TriggerMeta> triggers) {
        Map<String, Map<String, Set<TriggerMeta>>> consumerMap = group(triggers);

        for (Map.Entry<String, Map<String, Set<TriggerMeta>>> entry : consumerMap.entrySet()) {
            String consumerGroup = entry.getKey();

            TerminusMQConsumer existConsumer = terminusMQConsumers.stream().filter(consumer -> StringUtils.equals(consumer.getConsumerGroup(), consumerGroup)).findAny().orElse(null);

            // If this trigger is already registered, stop it.
            if (existConsumer != null) {
                existConsumer.stop();
                // @See DefaultMQPushConsumerImpl
                // MQ 存在防二次启动机制，所以只能移除，新建消费者重新注册
                terminusMQConsumers.remove(existConsumer);
            }
        }
    }

    private void loadConsumerFromTriggerMeta(List<Consumer> consumers) {
        // group by team code
        Map<String, List<ModuleInfo>> teamModules = MetaContext.getCurrentDeployModules().stream().collect(Collectors.groupingBy(ModuleInfo::getTeamCode));

        List<TriggerMeta> localTriggers = Lists.newArrayListWithCapacity(MetaContext.getCurrentDeployModules().size() * 4);

        teamModules.forEach((String teamKey, List<ModuleInfo> modules) -> {
            // find team all triggers
            List<TriggerMeta> triggers = triggerRepo.findAll(Field.type().equal(MetaType.Trigger.name()), ResourceContext.newResourceCtx(teamKey, null));

            triggers = triggers.stream().filter(TriggerMeta::isEnable).collect(Collectors.toList());

            // current deploy module keys
            Set<String> moduleKeys = modules.stream().map(ModuleInfo::getKey).collect(Collectors.toSet());

            // filter local services triggers
            localTriggers.addAll(triggers.stream().filter(triggerMeta -> {
                String moduleKey = KeyUtil.moduleKey(triggerMeta.getResourceProps().getRelatedServiceKey());
                return moduleKeys.contains(moduleKey);
            }).collect(Collectors.toList()));
        });

        loadConsumers(consumers, localTriggers);
    }

    private void loadConsumers(List<Consumer> consumers, List<TriggerMeta> triggers) {
        // group mq consumerGroup by strategy
        Map<String, Map<String, Set<TriggerMeta>>> consumerMap = group(triggers);
        for (Map.Entry<String, Map<String, Set<TriggerMeta>>> entry : consumerMap.entrySet()) {
            String consumerGroup = entry.getKey();
            Map<String, Set<TriggerMeta>> topics = entry.getValue();
            for (Map.Entry<String, Set<TriggerMeta>> topicEntry : topics.entrySet()) {
                String topic = topicEntry.getKey();
                Set<TriggerMeta> teamTriggers = topicEntry.getValue();
                if (CollectionUtils.isEmpty(teamTriggers)) {
                    continue;
                }

                Consumer consumer = loadConsumer(consumerGroup, topic, teamTriggers);
                consumers.add(consumer);
            }
        }
    }

    /**
     * consumerGroup - topic - tags
     *
     * @param triggers Trigger meta list
     */
    Map<String, Map<String, Set<TriggerMeta>>> group(List<TriggerMeta> triggers) {
        return groupStrategy.group(triggers);
    }

    Consumer loadConsumer(String consumerGroup, String topic, Set<TriggerMeta> triggers) {
        // 创建Consumer
        Consumer consumer = new Consumer();
        consumer.setConsumerGroup(consumerGroup);
        if (StringUtils.isBlank(consumer.getConsumerGroup())) {
            return null;
        }
        consumer.setConsumeMode(ConsumeMode.CONCURRENTLY);
        consumer.setMessageMode(MessageModel.CLUSTERING);
        consumer.setConsumeThreadNums(properties.getPoolThreads());
        consumer.setSubscriptions(loadSubscription(topic, triggers));
        consumer.setFilters(Lists.newArrayList());

        return consumer;
    }

    List<Subscription> loadSubscription(String topic, Set<TriggerMeta> triggers) {
        List<Subscription> subscriptions = new ArrayList<>(triggers.size());

        for (TriggerMeta triggerMeta : triggers) {
            Subscription subscription = new Subscription();
            subscription.setTopic(topic);
            subscription.setTags(Sets.newHashSet(TriggerKeyUtil.getTag(triggerMeta)));
            subscription.setMessageHandler(new TriggerHandler(teamService, serviceExecutor, properties.isLoggerEnable(), triggerMeta));
            subscription.setMessageDecoder(decoder);
            subscriptions.add(subscription);
        }

        return subscriptions;
    }

    void loadConsumerFromAnnotation(List<Consumer> consumers) {
        // group by team code
        Map<String, List<ModuleInfo>> teamModules = MetaContext.getCurrentDeployModules().stream().collect(Collectors.groupingBy(ModuleInfo::getTeamCode));

        List<EventDefinitionMeta> existEventDefinitions = Lists.newArrayListWithCapacity(MetaContext.getCurrentDeployModules().size() * 4);

        teamModules.forEach((String teamKey, List<ModuleInfo> modules) -> {
            List<EventDefinitionMeta> eventDefinitionMetas = eventDefinitionRepo.findAll(Field.type().equal(MetaType.EventDefinition.name()), ResourceContext.newResourceCtx(teamKey, null));
            existEventDefinitions.addAll(eventDefinitionMetas);
        });

        Map<String, EventDefinitionMeta> eventDefMap = existEventDefinitions.stream()
            .collect(Collectors.toMap(TriggerKeyUtil::getTag, event -> event));

        Map<String, Object> consumerMap = this.applicationContext.getBeansWithAnnotation(Trigger.class);
        for (Map.Entry<String, Object> entry : consumerMap.entrySet()) {
            Object bean = entry.getValue();
            Consumer consumer = loadConsumer(bean, eventDefMap);
            if (null != consumer) {
                consumers.add(consumer);
            }
        }
    }

    Consumer loadConsumer(Object bean, Map<String, EventDefinitionMeta> eventDefMap) {
        // 解析方法的话需要从正在的bean解析
        Class<?> clazz = AopProxyUtils.ultimateTargetClass(bean);
        Method[] methods = clazz.getDeclaredMethods();
        if (methods.length == 0) {
            return null;
        }

        List<Subscription> subscriptions = loadConsumerMethods(bean, methods, eventDefMap);
        // 没有订阅关系， 不用响应 consumer
        if (subscriptions.isEmpty()) {
            return null;
        }

        // 从类解析  mq元数据
        Trigger trigger = AnnotatedElementUtils.findMergedAnnotation(clazz, Trigger.class);
        Consumer consumer = new Consumer();
        consumer.setConsumerGroup(getConsumerGroup(trigger));
        if (StringUtils.isBlank(consumer.getConsumerGroup())) {
            return null;
        }
        consumer.setMessageMode(trigger.messageMode());
        consumer.setConsumeMode(trigger.consumeMode());
        consumer.setConsumeThreadNums(trigger.consumeThreadNums());
        consumer.setSubscriptions(subscriptions);
        consumer.setFilters(Stream.of(trigger.filters()).collect(Collectors.toList()));
        return consumer;
    }

    private List<Subscription> loadConsumerMethods(Object bean, Method[] methods, Map<String, EventDefinitionMeta> eventDefMap) {
        // 一般一个 consumer 不会有这么多 subscription， 所以这里设置数量为 5
        List<Subscription> subscriptions = new ArrayList<>(5);
        for (Method method : methods) {
            // MQSubscribe subscribe = method.getAnnotation(MQSubscribe.class);
            // 支持元注解(anno-meta)特性
            EventListener eventListener = AnnotatedElementUtils.findMergedAnnotation(method, EventListener.class);
            if (eventListener == null) {
                continue;
            }
            EventDefinitionMeta eventDefinition = eventDefMap.get(TriggerKeyUtil.getTag(eventListener));
            if (eventDefinition == null) {
                continue;
            }

            Subscription subscription = new Subscription();
            subscription.setTopic(TriggerKeyUtil.getTopic(eventDefinition));
            subscription.setTags(getTags(eventListener));
            // 这里可能会有问题， 得拿代理bean的方法，后续发现问题再调整下吧
            subscription.setMessageHandler(new TriggerReflectionHandler(bean, method, getParameterClass(method)));
            subscription.setParameterClass(TriggerEvent.class);
            subscription.setMessageDecoder(getMessageDecoder(eventListener));
            subscriptions.add(subscription);
        }
        return subscriptions;
    }

    private Type getParameterClass(Method method) {
        Type type = method.getGenericParameterTypes()[0];
        if (!(type instanceof Class || type instanceof ParameterizedType)) {
            throw new RuntimeException("parameter type is unknown!");
        }
        return type;
    }

    private String getConsumerGroup(Trigger trigger) {
        if (trigger.key() == null) {
            return null;
        }
        if (trigger.key().startsWith("${") && trigger.key().endsWith("}")) {
            return resolveProperty(trigger.key());
        }
        return TriggerKeyUtil.getConsumerGroup(properties.getGroupPrefix(), trigger);
    }

    private Set<String> getTags(EventListener consumer) {
        return Sets.newHashSet(TriggerKeyUtil.getTag(consumer));
    }

    private String resolveProperty(String property) {
        return applicationContext.getEnvironment().resolveRequiredPlaceholders(property);
    }

    private MessageDecoder getMessageDecoder(EventListener consumer) {
        Class<? extends MessageDecoder>[] messageDecoderClass = consumer.messageDecoder();
        if (messageDecoderClass.length > 0) {
            return applicationContext.getBean(messageDecoderClass[0]);
        }
        return decoder;
    }

}
