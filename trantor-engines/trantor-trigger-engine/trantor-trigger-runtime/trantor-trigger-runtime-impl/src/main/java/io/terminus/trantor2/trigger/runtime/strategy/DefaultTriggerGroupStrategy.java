package io.terminus.trantor2.trigger.runtime.strategy;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.terminus.trantor2.trigger.meta.TriggerMeta;
import io.terminus.trantor2.trigger.runtime.api.TriggerGroupStrategy;
import io.terminus.trantor2.properties.TriggerMQProperties;
import io.terminus.trantor2.trigger.runtime.utils.TriggerKeyUtil;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Trigger 分组策略
 *
 * <AUTHOR>
 * @ConsumerGroup Trigger_${Team.code}_${Trigger.key}
 * @Topic Default topic from properties
 * @Tag ${Team.code}_${EventDefinition.key}
 */
@RequiredArgsConstructor
public class DefaultTriggerGroupStrategy implements TriggerGroupStrategy {

    private final TriggerMQProperties properties;

    @Override
    public Map<String, Map<String, Set<TriggerMeta>>> group(List<TriggerMeta> triggers) {
        return triggers.stream().collect(
            Collectors.toMap(
                triggerMeta -> TriggerKeyUtil.getConsumerGroup(properties.getGroupPrefix(), triggerMeta),
                triggerMeta -> {
                    Map<String, Set<TriggerMeta>> topics = Maps.newHashMap();
                    // TODO 元数据分页接口，还无法透传出props信息，所以无法传递topic到trigger中，暂时先取配置信息。
                    topics.put(properties.getTopic(), Sets.newHashSet(triggerMeta));
                    return topics;
                }
            )
        );
    }

}
