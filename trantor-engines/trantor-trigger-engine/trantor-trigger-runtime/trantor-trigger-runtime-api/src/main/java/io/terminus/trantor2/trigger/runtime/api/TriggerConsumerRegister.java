package io.terminus.trantor2.trigger.runtime.api;

import io.terminus.trantor2.trigger.meta.TriggerMeta;

import java.util.List;

/**
 * 触发器消费者注册器
 *
 * <AUTHOR>
 */
public interface TriggerConsumerRegister {

    /**
     * Register all triggers
     */
    void registerAll();

    /**
     * If these triggers are not registered, register them.
     */
    void register(List<TriggerMeta> triggers);

    /**
     * If these triggers are already registered, stop them.
     */
    void stop(List<TriggerMeta> triggers);

}
