package io.terminus.trantor2.trigger.runtime.api;

import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.trigger.runtime.api.model.TriggerLog;
import io.terminus.trantor2.trigger.runtime.api.model.TriggerLogQueryRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(value = "trigger-log")
public interface TriggerSendLogApi {

    @PostMapping(value = "/api/trantor/trigger/internal/query/log")
    Response<TriggerLog> queryLog(@RequestBody TriggerLogQueryRequest request);

}
