package io.terminus.trantor2.connector.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import jakarta.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.Map;

/**
 * 连接器(服务)调用base Request
 */
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public abstract class CtCallBaseRequest {
    /**
     * 请求标识，用以跟踪请求
     */
    @Schema(description = "请求标识")
    private String requestId;

    /**
     * 连接器服务(api)Key
     */
    @Schema(description = "服务标识", required = true)
    @NotBlank(message = "服务标识不能为空")
    private String apiKey;
    /**
     * 调用入参
     */
    @Schema(description = "调用入参")
    private Map<String, Object> input = new HashMap<>();

    /**
     * 参数校验
     */
    public abstract void checkParams();
}
