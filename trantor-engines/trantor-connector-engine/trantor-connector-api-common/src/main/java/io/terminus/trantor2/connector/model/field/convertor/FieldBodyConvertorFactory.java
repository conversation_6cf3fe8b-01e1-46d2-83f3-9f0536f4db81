package io.terminus.trantor2.connector.model.field.convertor;


import io.terminus.trantor2.connector.model.field.FormatType;

import java.util.HashMap;
import java.util.Map;

@SuppressWarnings("unused")
public class FieldBodyConvertorFactory {
    static private final Map<FormatType, FieldBodyConvertor> fieldConvertorMap;

    static {
        fieldConvertorMap = new HashMap<>();
        fieldConvertorMap.put(FormatType.SOAP, new SoapXmlConvertor());
        fieldConvertorMap.put(FormatType.XML, new XmlConvertor());
        fieldConvertorMap.put(FormatType.JSON, new JsonConvertor());
    }

    static public FieldBodyConvertor getFieldConvertor(FormatType formatType) {
        return fieldConvertorMap.get(formatType);
    }
}
