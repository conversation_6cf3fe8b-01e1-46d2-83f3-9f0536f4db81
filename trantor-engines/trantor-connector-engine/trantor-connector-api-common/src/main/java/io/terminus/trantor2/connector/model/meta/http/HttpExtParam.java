package io.terminus.trantor2.connector.model.meta.http;

import com.google.common.collect.Lists;
import io.terminus.trantor2.connector.model.field.CtBaseField;
import io.terminus.trantor2.connector.model.field.CtField;
import io.terminus.trantor2.service.dsl.enums.FieldType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.List;

/**
 * Http扩展参数
 */
@FieldNameConstants()
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HttpExtParam {
    private static final int DEFAULT_MAX_TOTAL = 20;
    private static final int DEFAULT_CONNECT_TIMEOUT = 15;
    private static final int DEFAULT_SOCKET_TIMEOUT = 30;
    private static final int DEFAULT_CONNECTION_REQUEST_TIMEOUT = 10;

    private static final List<CtField> HTTP_EXT_PARAM_FIELDS;
    static {
        CtField isSinglePool = new CtBaseField(Fields.singlePool, "是否使用专有连接池", FieldType.Boolean);
        isSinglePool.setDescription("无特殊场景，建议不要配置");
        isSinglePool.setDefaultValue(Boolean.FALSE);
        //
        CtField maxTotal = new CtBaseField(Fields.maxTotal, "最大连接数", FieldType.Number);
        maxTotal.setDefaultValue(DEFAULT_MAX_TOTAL);
        //
        CtField connectionRequestTimeout = new CtBaseField(Fields.connectionRequestTimeout, "从连接池获取连接超时时间(秒)", FieldType.Number);
        connectionRequestTimeout.setDefaultValue(DEFAULT_CONNECTION_REQUEST_TIMEOUT);
        //
        CtField connectTimeout = new CtBaseField(Fields.connectTimeout, "连接超时(秒)", FieldType.Number);
        connectTimeout.setDefaultValue(DEFAULT_CONNECT_TIMEOUT);
        //
        CtField socketTimeout = new CtBaseField(Fields.socketTimeout, "读取超时(秒)", FieldType.Number);
        socketTimeout.setDefaultValue(DEFAULT_SOCKET_TIMEOUT);
        //
        HTTP_EXT_PARAM_FIELDS = Lists.newArrayList(isSinglePool, maxTotal, connectionRequestTimeout, connectTimeout, socketTimeout);
        for (CtField httpExtParamField : HTTP_EXT_PARAM_FIELDS) {
            httpExtParamField.setRequired(true);
        }
    }
    /**
     * 是否使用专有连接池
     */
    private Boolean singlePool = Boolean.FALSE;
    /**
     * 最大连接数
     */
    private Integer maxTotal = DEFAULT_MAX_TOTAL;
    /**
     * 单个host最大连接数
     */
    private Integer maxPerRoute = DEFAULT_MAX_TOTAL;
    /**
     * 连接超时(单位:秒)
     */
    private Integer connectTimeout = DEFAULT_CONNECT_TIMEOUT;
    /**
     * 读取(socket)超时(单位:秒)
     */
    private Integer socketTimeout = DEFAULT_SOCKET_TIMEOUT;
    /**
     * 获取连接超时时间
     */
    private Integer connectionRequestTimeout = DEFAULT_CONNECTION_REQUEST_TIMEOUT;

    /**
     * @return 扩展参数定义(Fields)
     */
    public static List<CtField> getHttpExtParamFields() {
        return HTTP_EXT_PARAM_FIELDS;
    }
}
