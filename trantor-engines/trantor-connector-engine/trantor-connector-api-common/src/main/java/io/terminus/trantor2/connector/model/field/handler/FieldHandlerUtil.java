package io.terminus.trantor2.connector.model.field.handler;

import io.terminus.trantor2.connector.model.field.CtField;
import io.terminus.trantor2.service.dsl.enums.FieldType;
import lombok.experimental.UtilityClass;

import java.util.*;

@UtilityClass
public class FieldHandlerUtil {
    private static final Map<FieldType, FieldHandler> fieldHandlerMap;

    static {
        fieldHandlerMap = new HashMap<>();
        BaseFieldHandler baseFieldHandler = new BaseFieldHandler();
        fieldHandlerMap.put(FieldType.Text, baseFieldHandler);
        fieldHandlerMap.put(FieldType.RichText, baseFieldHandler);
        fieldHandlerMap.put(FieldType.Boolean, baseFieldHandler);
        fieldHandlerMap.put(FieldType.Number, baseFieldHandler);
        fieldHandlerMap.put(FieldType.RangeTime, baseFieldHandler);
        fieldHandlerMap.put(FieldType.RangeDate, baseFieldHandler);
        fieldHandlerMap.put(FieldType.Email, baseFieldHandler);
        fieldHandlerMap.put(FieldType.Attachment, baseFieldHandler);
        fieldHandlerMap.put(FieldType.Enum, baseFieldHandler);
        fieldHandlerMap.put(FieldType.Time, baseFieldHandler);
        fieldHandlerMap.put(FieldType.Model, baseFieldHandler);
        //
        MapHandler mapHandler = new MapHandler();
        fieldHandlerMap.put(FieldType.Object, mapHandler);
        fieldHandlerMap.put(FieldType.Pageable, mapHandler);
        fieldHandlerMap.put(FieldType.Paging, mapHandler);
        //
        fieldHandlerMap.put(FieldType.Array, new ArrayHandler());
        //
        fieldHandlerMap.put(FieldType.Date, new DateHandler());
        fieldHandlerMap.put(FieldType.DateTime, new DateTimeHandler());
    }

    /**
     * 参数处理
     *
     * @param field   参数配置
     * @param val     原始值
     * @param isInput 入参/出参?
     * @return 转换值
     */
    public static Object handle(CtField field, Object val, boolean isInput) {
        FieldHandler fieldHandler = Optional.ofNullable(field)
            .map(CtField::getFieldType)
            .map(fieldHandlerMap::get)
            .orElse(null);
        if (Objects.isNull(fieldHandler)) {
            return val;
        }
        return isInput ? fieldHandler.handleInput(field, val) : fieldHandler.handleOutput(field, val);
    }

    /**
     * field type -> class
     *
     * @param fieldType field type
     * @param isInput   input/output?
     * @return class
     */
    public static Class<?> getClassFromFieldType(FieldType fieldType, boolean isInput) {
        switch (fieldType) {
            case Array:
            case Attachment:
            case Enum:
                return List.class;
            case Object:
            case Model:
            case Pageable:
            case Paging:
                return Map.class;
            case Boolean:
                return Boolean.class;
            case Number:
                return Number.class;
            case Date:
            case DateTime:
                return isInput ? Number.class : String.class;
            default:
                return String.class;
        }
    }

}
