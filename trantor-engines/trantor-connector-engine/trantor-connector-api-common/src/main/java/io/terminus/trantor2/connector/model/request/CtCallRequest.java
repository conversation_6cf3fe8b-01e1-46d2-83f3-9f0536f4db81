package io.terminus.trantor2.connector.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.common.exception.ValidationException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.springframework.util.StringUtils;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 连接器(服务)调用Request
 * (静态调用，通过teamId+instKey加载连接器实例)
 */
@Schema(description = "连接器(服务)静态调用请求")
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class CtCallRequest extends CtCallBaseRequest{
    /**
     * 团队id
     */
    @Schema(description = "团队Id")
    @NotNull(message = "团队Id不能为空")
    private Long teamId;
    /**
     * 连接器实例key
     */
    @Schema(description = "实例标识", required = true)
    @NotBlank(message = "实例标识不能为空")
    private String instKey;
    /**
     * check params
     */
    @Override
    public void checkParams() {
        List<String> fields = new ArrayList<>();
        if (Objects.isNull(this.getTeamId())) {
            fields.add("团队ID");
        }
        if (!StringUtils.hasText(this.getInstKey())) {
            fields.add("实例标识");
        }
        if (!StringUtils.hasText(this.getApiKey())) {
            fields.add("服务标识");
        }
        if (!fields.isEmpty()) {
            String str = String.join(",", fields);
            throw new ValidationException(str + "不能为空");
        }
    }
}
