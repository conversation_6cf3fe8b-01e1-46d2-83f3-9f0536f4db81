package io.terminus.trantor2.connector.model.field;

import cn.hutool.core.util.StrUtil;
import io.terminus.trantor2.service.dsl.enums.FieldType;
import org.apache.commons.collections.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ctField -> other
 */
public enum CtFieldConvertor {

    /**
     * 产生java代码
     * 将ctField树转化为java pojo对象
     */
    TO_JAVA {
        private StringBuilder appendIndentation(StringBuilder sb, int level) {
            sb.append(StrUtil.repeat("    ",Math.max(0, level)));
            return sb;
        }

        private String capitalize(String name) {
            return Character.toUpperCase(name.charAt(0)) + name.substring(1);
        }


        private StringBuilder newClassBuilder(String className, Integer depthLevel) {
            StringBuilder classBuilder = new StringBuilder();
            appendIndentation(classBuilder, depthLevel).append("@Data").append("\n");
            if (depthLevel == 0) {
                // 第一层
                classBuilder.append("public class ").append(className).append(" {\n\n");
            } else {
                appendIndentation(classBuilder, depthLevel).append("public static class ").append(className).append(" {\n\n");
            }
            return classBuilder;
        }

        private String getJavaTypeFromBaseFieldType(FieldType fieldType) {
            switch (fieldType) {
                case Text:
                    return "String";
                case Number:
                    return "BigDecimal";
                case Date:
                case DateTime:
                    return "Date";
                case Boolean:
                    return "Boolean";
                default:
                    return "Object";

            }
        }

        private StringBuilder newBaseFieldBuilder(String fieldName, FieldType fieldType) {
            StringBuilder fieldBuilder = new StringBuilder();
            fieldBuilder.append("private ");
            fieldBuilder.append(getJavaTypeFromBaseFieldType(fieldType)).append("  ").append(fieldName);
            fieldBuilder.append(";\n\n");
            return fieldBuilder;
        }


        private StringBuilder newObjectFieldBuilder(String className, String fieldName) {
            StringBuilder fieldBuilder = new StringBuilder();
            fieldBuilder.append("private ");
            fieldBuilder.append(className).append("  ").append(fieldName);
            fieldBuilder.append(";\n\n");
            return fieldBuilder;
        }

        private StringBuilder newArrayFieldBuilder(String className, String fieldName) {
            StringBuilder fieldBuilder = new StringBuilder();
            fieldBuilder.append("private ");
            fieldBuilder.append("List<");
            fieldBuilder.append(className);
            fieldBuilder.append("> ").append(fieldName);
            fieldBuilder.append(";\n\n");
            return fieldBuilder;
        }


        private StringBuilder recursionGenerate(CtField ctField, String fieldName, Integer depLevel) {
            if (ctField == null) return new StringBuilder();
            if (ctField instanceof CtObjectField) {
                CtObjectField ctObjectField = (CtObjectField) ctField;
                if (CollectionUtils.isNotEmpty(ctObjectField.getElements())) {
                    //ctObject 有多个elements 应该是一个多节点的树
                    String capitalized = capitalize(fieldName);
                    StringBuilder classBuilder = newClassBuilder(capitalized, depLevel);
                    //按照 其他 object array 的顺序排序
                    List<CtField> sorted = ctObjectField.getElements().stream()
                            .sorted(Comparator.comparing(field -> {
                                switch (field.getFieldType()) {
                                    case Array:
                                        return 3;
                                    case Object:
                                        return 2;
                                    default:
                                        return 1;
                                }
                            }))
                            .collect(Collectors.toList());
                    for (CtField element : sorted) {
                        appendIndentation(classBuilder, depLevel + 1);
                        if (FieldType.Object.equals(element.getFieldType())) {
                            classBuilder.append(newObjectFieldBuilder(capitalize(element.getFieldKey()), element.getFieldKey()));
                        }
                        StringBuilder subBuilder = recursionGenerate(element, element.getFieldKey(), depLevel + 1);
                        classBuilder.append(subBuilder);
                    }
                    appendIndentation(classBuilder, depLevel).append("}\n\n");
                    return classBuilder;
                }
            } else if (ctField instanceof CtArrayField) {
                CtArrayField ctArrayField = (CtArrayField) ctField;
                CtField element = ctArrayField.getElement();
                if (element != null) {
                    //ctObject 有1个element 是一个单节点的树
                    //array的派生类
                    if (element instanceof CtObjectField) {
                        String arrayFieldName = ctArrayField.getFieldKey();
                        StringBuilder arrayFieldBuilder = newArrayFieldBuilder(capitalize(arrayFieldName), arrayFieldName);
                        //数组下面的子元素类型名称
                        arrayFieldBuilder.append(recursionGenerate(element, arrayFieldName, depLevel));
                        return arrayFieldBuilder;
                    }else{
                        String arrayFieldName = ctArrayField.getFieldKey();
                        return newArrayFieldBuilder(getJavaTypeFromBaseFieldType(element.getFieldType()), element.getFieldKey());
                    }

                }
            } else {
                //其他
                return newBaseFieldBuilder(ctField.getFieldKey(), ctField.getFieldType());
            }
            return new StringBuilder();
        }


        @Override
        public String generate(CtField ctField) {
            StringBuilder stringBuilder = recursionGenerate(ctField, ctField.getFieldKey(), 0);
            return stringBuilder.toString();
        }
    };


    public abstract String generate(CtField ctField);

}
