package io.terminus.trantor2.connector.model.meta.auth;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

/**
 * access token请求模版
 */
@Getter
@Setter
@FieldNameConstants
public class TokenAuth extends BaseAuth {
    /**
     * access token请求
     */
    private TokenRequest accessTokenRequest;
    /**
     * api请求后，判定认证错误表达式(js), 注册变量为ctx.output.xxx
     * 认证错误将强制刷新accessToken，并重新执行api请求
     */
    private String invalidTokenExpression;

    public TokenAuth() {
        super.setAuthType(AuthType.TOKEN);
    }

    @Override
    public void setAuthType(AuthType authType) {
    }
}
