package io.terminus.trantor2.connector.utils;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.connector.exception.CtException;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.graalvm.polyglot.Context;
import org.graalvm.polyglot.HostAccess;
import org.graalvm.polyglot.Value;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

@UtilityClass
@Slf4j
public class JsUtil {
    private static final String SCRIPT_NAME = "js";
    private static final String PARAM_CONTEXT = "ctx";

    private static final String JAVA_LIBS = "var HttpFunctions = Java.type('io.terminus.trantor2.connector.model.meta.http.HttpGlobalFunctions');\n";

//    private static final Cache<String, CompiledScript> COMPILED_SCRIPT_CACHE;
//    static {
//        COMPILED_SCRIPT_CACHE =  Caffeine.newBuilder()
//            .expireAfterAccess(1, TimeUnit.HOURS)
//            .maximumSize(100)
//            .build();
//    }

    private static final Map<String, String> options = new HashMap<>();

    static {
        options.put("js.ecmascript-version", "2020");
        options.put("engine.WarnInterpreterOnly", "false");
        options.put("js.nashorn-compat", "true");
    }

    private static String getScriptCode(String scriptCode, boolean isScript) {
        String execCode = JAVA_LIBS + scriptCode;
        if (isScript) {
            execCode = "function exec(ctx) {\n"
                + execCode
                + "}\n"
                +
                "exec(ctx)";
        }
        return execCode;
    }


    /**
     * 执行脚本，用于需要脚本动态实现的上下文参数处理。
     *
     * @param scriptCode script code
     * @param context    context
     */
    public static void executeScriptCode(String scriptCode, Map<String, Object> context) {
        doExecute(scriptCode, context, true);
    }

    /**
     * 执行表达式
     *
     * @param expression expression(js)
     * @param context    context
     * @return js exec res
     */
    public static Object executeExpression(String expression, Map<String, Object> context) {
        return doExecute(expression, context, false);
    }

    /**
     * 执行js
     *
     * @param sourceCode source code
     * @param context    context
     * @param isScript   js script code?(script/expression)
     * @return js exec res
     */
    private static Object doExecute(String sourceCode, Map<String, Object> context, boolean isScript) {

        try {
            //绑定ctx
            Context engine = CtContext.getEngineContextOrDefault(() -> {
                Thread.currentThread().setContextClassLoader(JsUtil.class.getClassLoader());
                return Context.newBuilder(SCRIPT_NAME)
                    .allowExperimentalOptions(true)
                    .options(options)
                    .allowAllAccess(true)
                    .allowCreateThread(false)
                    .allowCreateProcess(false)
                    .allowHostAccess(HostAccess.ALL)
                    .allowHostClassLoading(true)
                    .allowHostClassLookup(f -> true)
                    .build();
            });
            engine.getBindings(SCRIPT_NAME).putMember(PARAM_CONTEXT, context);
            Value eval = engine.eval(SCRIPT_NAME, getScriptCode(sourceCode, isScript));
            return getValue(eval);
        } catch (Exception e) {
            log.error("reqId:{},ct,js execute error,code:{}, context:{}", CtContext.getReqId(), sourceCode, context, e);
            throw new CtException(ErrorType.CONNECTOR_INVOKE_ERROR, "js execute error", e);
        }

    }

    private static Object getValue(Value result) {
        if (result.isString()) {
            return result.asString();
        } else if (result.isNumber()) {
            return BigDecimal.valueOf(result.asDouble());
        } else if (result.isBoolean()) {
            return result.asBoolean();
        } else if (result.isNull()) {
            return null;
        } else if (result.hasArrayElements()) {
            List<Object> lst = new ArrayList<>();
            for (int i = 0; i < result.getArraySize(); i++) {
                lst.add(getValue(result.getArrayElement(i)));
            }
            return lst;
        } else if (result.hasMembers()) {
            Map<String, Object> map = new HashMap<>();
            for (String key : result.getMemberKeys()) {
                map.put(key, getValue(result.getMember(key)));
            }
            return map;
        } else {
            return result.toString();
        }
    }

    public static void main(String[] args) throws ExecutionException, InterruptedException {
        System.out.println(JsUtil.executeExpression("", new HashMap<>()));
        Object o = JsUtil.executeExpression("[{a:[1,2,4]}]", new HashMap<>());
        System.out.println(o);
    }
}
