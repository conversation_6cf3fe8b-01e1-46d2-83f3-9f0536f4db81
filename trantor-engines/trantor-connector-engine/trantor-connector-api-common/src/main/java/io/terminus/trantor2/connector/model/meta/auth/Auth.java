package io.terminus.trantor2.connector.model.meta.auth;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.terminus.trantor2.connector.model.field.CtField;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * (http)认证配置
 *
 * <AUTHOR>
 * @since 2023/9/26
 */
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME, visible = true,
    include = JsonTypeInfo.As.EXISTING_PROPERTY,
    property = "authType")
@JsonSubTypes({
    @JsonSubTypes.Type(value = BaseAuth.class, name = "BASIC"),
    @JsonSubTypes.Type(value = BaseAuth.class, name = "DIGEST"),
    @JsonSubTypes.Type(value = BaseAuth.class, name = "APIKEY"),
    @JsonSubTypes.Type(value = TokenAuth.class, name = "TOKEN")
})
@Getter
@Setter
public abstract class Auth implements Serializable {
    /**
     * 认证类型
     */
    private AuthType authType;

    /**
     * 返回认证对应的默认字段属性(fields)
     *
     * @return list<field>
     */
    @JsonIgnore
    public List<CtField> getAuthDefaultFields() {
        return this.authType.getDefaultFields();
    }
}
