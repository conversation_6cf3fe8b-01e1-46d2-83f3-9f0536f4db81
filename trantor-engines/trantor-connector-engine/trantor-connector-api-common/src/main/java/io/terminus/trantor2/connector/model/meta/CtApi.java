package io.terminus.trantor2.connector.model.meta;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.connector.model.field.CtField;
import io.terminus.trantor2.connector.utils.ParamUtil;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 连接器服务
 */
@Schema(description = "连接器服务")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class CtApi {
    @Schema(description = "服务标识", required = true)
    private String key;
    /**
     * 名称
     */
    @Schema(description = "服务名称", required = true)
    private String name;
    /**
     * 描述
     */
    @Schema(description = "服务描述")
    private String description;
    /**
     * 连接器模版key
     */
    @Schema(description = "模版标识", required = true)
    private String ctTempKey;
    /**
     * 入参,兼容服务定义
     */
    @Schema(description = "服务入参", required = true)
    private List<CtField> input;
    /**
     * 出参,兼容服务定义
     */
    @Schema(description = "服务出参", required = true)
    private CtField output;
    /**
     * 服务请求
     */
    @Schema(description = "服务请求, 仅适用http类模版")
    private String apiRequest;
    /**
     * 返回样本数据, 用以在线解析/参考
     */
    @Schema(description = "样本数据, 仅适用http类模版")
    private String sampleOutput;
    /**
     * 服务分组tag
     */
    @Schema(description = "服务分组")
    private String tag;

    public void checkParams() {
        List<String> fields = new ArrayList<>();
        if (!StringUtils.hasText(this.key)) {
            fields.add("服务标识");
        }
        if (!StringUtils.hasText(this.name)) {
            fields.add("服务名称");
        }
        if (!StringUtils.hasText(this.ctTempKey)) {
            fields.add("模版标识");
        }
        if (Objects.isNull(this.output)) {
            fields.add("服务出参");
        } else {
            ParamUtil.checkField(this.output);
        }

        if (!fields.isEmpty()) {
            String str = String.join(",", fields);
            throw new ValidationException(str + "不能为空");
        }

        if (CollectionUtils.isNotEmpty(this.input)) {
            this.input.forEach(ParamUtil::checkField);
        }
    }

    /**
     * full key
     * @return full key
     */
    public String fullKey() {
        return this.ctTempKey + ":" + this.key;
    }
}
