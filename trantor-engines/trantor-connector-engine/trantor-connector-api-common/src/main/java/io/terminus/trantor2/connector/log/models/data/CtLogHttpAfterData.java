package io.terminus.trantor2.connector.log.models.data;


import io.terminus.trantor2.connector.log.enums.CtLogStage;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.apache.http.Header;
import org.apache.http.client.methods.CloseableHttpResponse;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * http的log
 */
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class CtLogHttpAfterData implements CtLogData {

    private Integer statusCode;

    private Map<String, String> headers;

    private String responseBody;

    @Override
    public CtLogStage stage() {
        return CtLogStage.HTTP_AFTER;
    }

    public static CtLogHttpAfterData newData(CloseableHttpResponse response, String resBody) {
        CtLogHttpAfterData ctLogHttpAfterData = new CtLogHttpAfterData();
        ctLogHttpAfterData.setStatusCode(response.getStatusLine().getStatusCode());
        Map<String, String> headers = Arrays.stream(response.getAllHeaders()).collect(Collectors.toMap(Header::getName, Header::getValue, (v1, v2) -> v2));
        ctLogHttpAfterData.setHeaders(headers);
        ctLogHttpAfterData.setResponseBody(resBody);
        return ctLogHttpAfterData;
    }

}
