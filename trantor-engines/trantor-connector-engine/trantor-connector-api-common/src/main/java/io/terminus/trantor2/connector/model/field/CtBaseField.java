package io.terminus.trantor2.connector.model.field;


import io.terminus.trantor2.service.dsl.enums.FieldType;
import io.terminus.trantor2.service.dsl.properties.BaseField;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * ct base field
 */
@Getter
@Setter
@NoArgsConstructor
public class CtBase<PERSON>ield extends BaseField implements CtField {
    /**
     * 字段格式
     */
    private FormatType formatType = FormatType.JSON;
    /**
     * soap字段属性
     */
    private SoapProps soapProps;

    public CtBaseField(String fieldKey, String fieldName, FieldType fieldType) {
        super(fieldKey, fieldName, fieldType);
    }

    public CtBaseField(String fieldKey, FieldType fieldType) {
        super(fieldKey, fieldType);
    }

    public CtBaseField(String fieldKey, FieldType fieldType, FormatType formatType) {
        this(fieldKey, fieldKey, fieldType, formatType);
    }

    public CtBaseField(String fieldKey, String fieldName, FieldType fieldType, FormatType formatType) {
        super(fieldKey, fieldName, fieldType);
        this.formatType = formatType;
    }

}
