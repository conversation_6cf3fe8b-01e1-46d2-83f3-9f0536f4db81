package io.terminus.trantor2.connector.model.meta.http;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;
@SuppressWarnings({"unused", "DuplicatedCode"})
public class HttpRequest {
    @Getter
    private Integer index;
    private String name;
    @Getter
    private String comment;
    @Getter
    private List<String> tags;
    @Getter
    private HttpMethod method;
    @Getter
    private String requestLine;
    private List<HttpHeader> headers;
    private boolean bodyStarted = false;
    @Getter
    private List<String> bodyLines;
    @Getter
    private List<String> preScriptLines = new ArrayList<>();
    @Getter
    private final List<Integer> lineNumbers = new ArrayList<>();
    @Getter
    @Setter
    private String bodyText;
    private String preScriptCode;
    private String jsTestCode;
    private HttpRequestTarget requestTarget;
    @Getter
    private final List<String> requestLines = new ArrayList<>();

    public HttpRequest() {
    }

    public HttpRequest(Integer index) {
        this.index = index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name == null ? index.toString() : name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void addRequestLine(String line) {
        requestLines.add(line);
    }

    public String getRequestCode() {
        return StringUtils.join(requestLines, "\n");
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public void addTag(String tag) {
        if (this.tags == null) {
            this.tags = new ArrayList<>();
        }
        this.tags.add(tag);
    }

    public boolean containsTag(String name) {
        if (this.tags != null && !this.tags.isEmpty()) {
            for (String tag : tags) {
                if (tag.equalsIgnoreCase(name)) {
                    return true;
                }
            }
        }
        return false;
    }

    public void setMethod(HttpMethod method) {
        this.method = method;
    }

    public void setRequestLine(String requestLine) {
        this.requestLine = requestLine;
    }

    public void appendRequestLine(String requestPart) {
        if (requestLine == null) {
            requestLine = requestPart.trim();
        } else {
            requestLine = requestLine + requestPart.trim();
        }
    }

    public HttpRequestTarget getRequestTarget() {
        if (requestTarget == null && method != null && requestLine != null) {
            requestTarget = HttpRequestTarget.valueOf(method.getName(), requestLine);
        }
        return requestTarget;
    }

    public void setRequestTarget(HttpRequestTarget requestTarget) {
        this.requestTarget = requestTarget;
    }

    @NotNull
    public List<HttpHeader> getHeaders() {
        return headers == null ? Collections.emptyList() : headers;
    }

    public void setHeaders(List<HttpHeader> headers) {
        this.headers = headers;
    }

    public Map<String, String> getHeadersMap() {
        if (headers == null || headers.isEmpty()) {
            return new HashMap<>();
        } else {
            return headers.stream().collect(Collectors.toMap(HttpHeader::getName, HttpHeader::getValue));
        }
    }

    @Nullable
    public String getHeader(String name) {
        if (this.headers != null) {
            for (HttpHeader header : headers) {
                if (header.getName().equalsIgnoreCase(name)) {
                    return header.getValue();
                }
            }
        }
        return null;
    }

    @NotNull
    public String getHeader(String name, @NotNull String defaultValue) {
        if (this.headers != null) {
            for (HttpHeader header : headers) {
                if (header.getName().equalsIgnoreCase(name)) {
                    return header.getValue();
                }
            }
        }
        return defaultValue;
    }

    public void addHttpHeader(String name, String value) {
        if (headers == null) {
            headers = new ArrayList<>();
        }
        if (name.equalsIgnoreCase("authorization")) {
            // Convert `username password` or `username:password` to Base64
            if (value.startsWith("Basic ")) {
                String token = value.substring(6).trim();
                if (token.contains(" ") || token.contains(":")) {
                    String text = token.replace(" ", ":");
                    value = "Basic " + Base64.getEncoder().encodeToString(text.getBytes(StandardCharsets.UTF_8));
                }
            }
        } else if (name.equalsIgnoreCase("host") || name.equalsIgnoreCase("uri")) {
            HttpRequestTarget requestTarget = getRequestTarget();
            if (requestTarget == null) {
                requestTarget = HttpRequestTarget.valueOf("UNKNOWN", "/");
                this.setRequestTarget(requestTarget);
            }
            requestTarget.setHostOrUriHeader(name, value);
        }
        this.headers.add(new HttpHeader(name, value));
    }

    /**
     * direct add, with no logic
     * @param header header
     */
    public void addHttpHeader(HttpHeader header) {
        if (headers == null) {
            headers = new ArrayList<>();
        }
        headers.add(header);
    }

    public void replaceHeader(String name, String value) {
        if (headers != null) {
            HttpHeader header = null;
            for (HttpHeader httpHeader : headers) {
                if (httpHeader.getName().equalsIgnoreCase(name)) {
                    header = httpHeader;
                    break;
                }
            }
            if (header != null) {
                this.headers.remove(header);
            }
            addHttpHeader(name, value);
        }
    }

    public void addLineNumber(int lineNumber) {
        this.lineNumbers.add(lineNumber);
    }

    public boolean containsLineNumber(int lineNumber) {
        return this.lineNumbers.contains(lineNumber);
    }

    public void setBodyLines(List<String> bodyLines) {
        this.bodyLines = bodyLines;
    }

    public void addBodyLine(String line) {
        if (this.bodyLines == null) {
            bodyLines = new ArrayList<>();
        }
        this.bodyLines.add(line);
    }

    public void addPreScriptLine(String line) {
        if (this.preScriptLines == null) {
            preScriptLines = new ArrayList<>();
        }
        this.preScriptLines.add(line);
    }

    public void setPreScriptCode(String preScriptCode) {
        this.preScriptCode = preScriptCode;
    }

    @Nullable
    public String getJavaScriptTestCode() {
        return this.jsTestCode;
    }

    @Nullable
    public String getPreScriptCode() {
        return preScriptCode;
    }

    public byte[] bodyBytes() {
        if (Objects.isNull(this.bodyText)) {
            return new byte[]{};
        }
        return this.bodyText.getBytes(StandardCharsets.UTF_8);
    }

    public boolean containsArgsHeader() {
        if (this.headers == null || this.headers.isEmpty()) {
            return false;
        }
        final Map<String, String> argsHeaders = headers.stream()
                .filter(httpHeader -> httpHeader.getName().toLowerCase().startsWith("x-args-"))
                .collect(Collectors.toMap(httpHeader -> httpHeader.getName().toLowerCase(), HttpHeader::getValue));
        return !argsHeaders.isEmpty();
    }

    public String convertToDoubleQuoteString(String text) {
        String escapedText = StringUtils.replace(text, "\"", "\\\"");
        escapedText = StringUtils.replace(escapedText, "\n", "\\n");
        escapedText = StringUtils.replace(escapedText, "\r", "");
        return "\"" + escapedText + "\"";
    }

    public String wrapJsonValue(String value) {
        if (Objects.equals(value, "true") || Objects.equals(value, "false") || Objects.equals(value, "null")) {
            return value;
        } else if (value.startsWith("\"") || value.startsWith("{") || value.startsWith("[")) {
            return value;
        } else if (value.contains("\"")) {
            return convertToDoubleQuoteString(value);
        } else {
            try {
                Double.parseDouble(value);
                return value;
            } catch (Exception ignore) {
                return "\"" + value + "\"";
            }
        }
    }

    @Nullable
    public String[] getBasicAuthorization() {
        final String header = this.getHeader("Authorization");
        if (header != null && header.startsWith("Basic ")) {
            final String base64Text = header.substring(6).trim();
            return new String(Base64.getDecoder().decode(base64Text), StandardCharsets.UTF_8).split("[: ]");
        }
        return null;
    }

    public boolean isFilled() {
        return method != null && requestLine != null;
    }

    public boolean isHostOrUriAvailable() {
        if (headers != null && !headers.isEmpty()) {
            for (HttpHeader header : headers) {
                final String headerName = header.getName();
                if (headerName.equalsIgnoreCase("Host") || headerName.equalsIgnoreCase("URI")) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean isBodyEmpty() {
        return bodyLines == null || bodyLines.isEmpty();
    }

    @SuppressWarnings("BooleanMethodIsAlwaysInverted")
    public boolean isRequestStarted() {
        return requestLine != null;
    }

    @SuppressWarnings("BooleanMethodIsAlwaysInverted")
    public boolean isBodyStarted() {
        return bodyStarted;
    }

    public void setBodyStarted(boolean bodyStarted) {
        if (method != null) {
            this.bodyStarted = bodyStarted;
        }
    }

    public void cleanBody() {
        cleanBody(null);
    }

    /**
     * clean body: extract javascript test code, redirect response etc
     */
    public void cleanBody(@Nullable Path httpFilePath) {
        //clean body
        if (bodyLines != null && !bodyLines.isEmpty()) {
            List<String> lines = new ArrayList<>();
            for (String bodyLine : bodyLines.subList(0, bodyLines.size())) {
                if (!bodyLine.startsWith("<>")) {
                    lines.add(bodyLine);
                }
            }
            // extract js code block
            int jsScriptStartOffset = lines.size();
            int jsScriptEndOffset = -1;
            for (int i = 0; i < lines.size(); i++) {
                String line = lines.get(i);
                if (line.startsWith("> {%")) {
                    jsScriptStartOffset = i;
                }
                if (line.equals("%}") && i > jsScriptStartOffset) {
                    jsScriptEndOffset = i;
                    break;
                }
            }
            if (jsScriptEndOffset > 0) { // javascript test code found
                this.jsTestCode = String.join(System.lineSeparator(), lines.subList(jsScriptStartOffset + 1, jsScriptEndOffset));
                List<String> cleanLines = new ArrayList<>();
                cleanLines.addAll(lines.subList(0, jsScriptStartOffset));
                cleanLines.addAll(lines.subList(jsScriptEndOffset + 1, lines.size()));
                lines = cleanLines;
            }

            // process body from lines
            if (!lines.isEmpty()) {
                //remove empty lines after body
                while (lines.get(lines.size() - 1).isEmpty()) {
                    lines.remove(lines.size() - 1);
                    if (lines.isEmpty()) {
                        break;
                    }
                }
                if (!lines.isEmpty()) {
                    String content = String.join(System.lineSeparator(), lines);
                    String contentType = getHeader("Content-Type");
                    // Fix https://youtrack.jetbrains.com/issue/IDEA-281753/Support-formatting-for-POST-request-body-for-application-x-www-f
                    if (contentType != null && contentType.equalsIgnoreCase("application/x-www-form-urlencoded") && content.contains("\n")) {
                        content = StringUtils.replace(content, "\n", "");
                        content = StringUtils.replace(content, "\r", "");
                    }
                    this.bodyText = content;
                }
            }
        }
    }

    public boolean match(String targetName) {
        if (targetName.startsWith("line:")) {
            int lineNumber = Integer.parseInt(targetName.substring(5));
            if (lineNumber == 0) {
                lineNumber = 1;
            }
            return this.containsLineNumber(lineNumber);
        }
        return targetName.equalsIgnoreCase(this.name)
                || Objects.equals(targetName, this.index.toString());
    }
}
