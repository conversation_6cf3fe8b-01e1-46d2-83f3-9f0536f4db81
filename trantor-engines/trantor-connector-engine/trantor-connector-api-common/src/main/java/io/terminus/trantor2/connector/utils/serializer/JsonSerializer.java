package io.terminus.trantor2.connector.utils.serializer;

import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.common.utils.TimestampLocalDateTimeDeserializer;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Map;

@Slf4j
public class JsonSerializer {

    public static final JsonConvertorIndent JSON_CONVERTOR_INDENT = new JsonConvertorIndent();

    private static JsonFactory jsonFactoryAllowComments() {
        // 支持在JSON配置文件中写注释，格式如下
        // -  /* 注释 */
        // -  // 注释
        JsonFactory jsonFactory = new JsonFactory();
        jsonFactory.enable(JsonParser.Feature.ALLOW_COMMENTS);
        return jsonFactory;
    }

    public interface Jsonable {
        ObjectMapper getObjectMapper();

        default <T> T fromJson(String json, TypeReference<T> beanTypeReference) throws JsonProcessingException {
            return getObjectMapper().readValue(json, beanTypeReference);
        }

        default <T> T fromJson(String json, Class<T> bean) throws JsonProcessingException {
            return getObjectMapper().readValue(json, bean);
        }

        default String toJson(Object o) throws JsonProcessingException {
            return getObjectMapper().writeValueAsString(o);
        }

        @SuppressWarnings("unchecked")
        default Map<String, Object> toMap(Object o) {
            return getObjectMapper().convertValue(o, Map.class);
        }


    }

    /**
     * 生成的JSON字符串不包含缩进，但包含取值为null或[]的字段
     * <pre>
     * {"stringField": "value", "nullField": null,"emptyStringField": "","emptyListField": []}
     * </pre>
     */
    public static class JsonConvertorIndent implements Jsonable {
        private static final ObjectMapper JSON_CONVERTOR_INDENT_MAPPER;

        static {
            JSON_CONVERTOR_INDENT_MAPPER = new ObjectMapper(jsonFactoryAllowComments());
            JavaTimeModule javaTimeModule = new JavaTimeModule();
            javaTimeModule.addDeserializer(LocalDateTime.class, TimestampLocalDateTimeDeserializer.INSTANCE);
            JSON_CONVERTOR_INDENT_MAPPER.registerModule(javaTimeModule);
            JSON_CONVERTOR_INDENT_MAPPER.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
            JSON_CONVERTOR_INDENT_MAPPER.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        }

        @Override
        public ObjectMapper getObjectMapper() {
            return JSON_CONVERTOR_INDENT_MAPPER;
        }
    }


    public static JsonNode readJson(String jsonStr) {
        try {
            return JSON_CONVERTOR_INDENT.getObjectMapper().readTree(jsonStr);
        } catch (Exception e) {
//            e.printStackTrace();
            throw new ValidationException(String.format("Json反序列化报错: %s", e.getMessage()));
        }
    }

    public static Object readJsonToObj(String jsonStr) {
        try {
            return JSON_CONVERTOR_INDENT.getObjectMapper().readValue(jsonStr, Object.class);
        } catch (Exception e) {
            log.error("readJsonToObj err: {}", e.getMessage(), e);
            throw new ValidationException(String.format("json反序列化报错: %s", e.getMessage()));
        }
    }

    public static String toJson(Object obj) {
        try {
            return JSON_CONVERTOR_INDENT.toJson(obj);
        } catch (Exception e) {
            log.error("toJson err: {}", e.getMessage(), e);
            throw new ValidationException(String.format("json序列化报错: %s", e.getMessage()));
        }
    }

    public static void main(String[] args) {
        Object o = readJsonToObj("\"3\"");
        System.out.println(o);
    }
}
