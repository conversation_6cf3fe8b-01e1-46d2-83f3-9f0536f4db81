package io.terminus.trantor2.connector.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.Map;

/**
 * 连接器测试调用请求
 */
@Schema(description = "连接器测试调用请求")
@Getter
@Setter
public class CtTestCallRequest {
    /**
     * 连接器实例key
     */
    @Schema(description = "实例标识", required = true)
    @NotBlank(message = "实例标识不能为空")
    private String instKey;
    /**
     * 连接器服务(api)Key
     */
    @Schema(description = "服务标识", required = true)
    @NotBlank(message = "服务标识不能为空")
    private String apiKey;
    /**
     * 调用入参
     */
    @Schema(description = "调用入参")
    private Map<String, Object> input = new HashMap<>();
}
