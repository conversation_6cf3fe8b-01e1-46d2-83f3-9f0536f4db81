package io.terminus.trantor2.connector.utils;

import cn.hutool.core.util.RandomUtil;
import com.google.common.collect.Sets;
import io.terminus.trantor2.service.dsl.enums.FieldType;

import java.util.Collections;
import java.util.Set;

public class CommonUtil {

    public static Set<FieldType> Connector_Field_Types = Sets.newHashSet(FieldType.Boolean, FieldType.Object, FieldType.Array, FieldType.Text, FieldType.Number, FieldType.DateTime,FieldType.Password);

    public static final String HTTP_FILE_SUFFIX = ".http";

    public static final String TEMP_FILE_SUFFIX = "-temp.json";

    public static final String API_FILE_SUFFIX = "-api.json";

    private static final String BASE_CHAR_RANDOM_NUMBER = "abcdefghijklmnopqrstuvwxyz0123456789";

    public static boolean isConnectorFieldType(FieldType fieldType) {
        return Connector_Field_Types.contains(fieldType);
    }

    public static String standardHttpFileSuffix(String tempKey) {
        return String.format("%s%s", tempKey, HTTP_FILE_SUFFIX);
    }

    public static String standardTempFileSuffix(String tempKey) {
        return String.format("%s%s", tempKey, TEMP_FILE_SUFFIX);
    }

    public static String standardApiFileSuffix(String apiKey) {
        return String.format("%s%s", apiKey, API_FILE_SUFFIX);
    }

    public static String offsetText(int offset) {
        return String.join("", Collections.nCopies(offset, "  "));
    }

    public static String randomKey(int length) {
        return RandomUtil.randomString(BASE_CHAR_RANDOM_NUMBER, length);
    }

    public static String reformKey(String oldKey) {
        int oldLength = oldKey.length();
        int leftLength = 50 - oldLength;
        if (leftLength <= 0) {
            //完全替换
            return randomKey(8);
        }
        String appendStr;
        if (leftLength == 1) {
            appendStr = randomKey(1);
        } else {
            appendStr = "_" + randomKey(Math.min(leftLength - 1, 8));
        }
        return oldKey + appendStr;
    }
}
