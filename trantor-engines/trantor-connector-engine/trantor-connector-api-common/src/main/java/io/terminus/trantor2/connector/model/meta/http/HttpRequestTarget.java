package io.terminus.trantor2.connector.model.meta.http;

import lombok.Getter;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@SuppressWarnings("unused")
public class HttpRequestTarget {
    @Getter
    private String method;
    @Getter
    private String requestLine;
    @Getter
    private String fragment;
    @Getter
    private String host;
    @Getter
    private String pathAbsolute;
    @Getter
    private int port;
    @Getter
    private String query;
    @Getter
    private String schema;
    private URI uri;

    public void setFragment(String fragment) {
        this.fragment = fragment;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public void setPathAbsolute(String pathAbsolute) {
        this.pathAbsolute = pathAbsolute;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public void setSchema(String schema) {
        this.schema = schema;
    }

    public URI getUri() {
        if (uri != null) {
            return uri;
        }
        StringBuilder builder = new StringBuilder();
        if (schema == null) {
                schema = "http";
        }
        builder.append(schema).append("://");
        builder.append(host);
        if (port > 0) {
            builder.append(":").append(port);
        }
        if (pathAbsolute != null) {
            String encodedPath = "";
            try {
                encodedPath = URLEncoder.encode(pathAbsolute, StandardCharsets.UTF_8.name()).replaceAll("%2F", "/");
            } catch (UnsupportedEncodingException ignored) {
            }
            if (encodedPath.startsWith("/")) {
                builder.append(encodedPath);
            } else {
                builder.append("/").append(encodedPath);
            }
        }
        return URI.create(builder.toString());
    }

    public void setUri(URI uri) {
        this.uri = uri;
    }

    public void setHostOrUriHeader(String headerName, String headerValue) {
        if (this.uri == null) {
            if (headerValue.contains("://")) { // URI
                final URI uri = URI.create(headerValue);
                if (pathAbsolute == null) {
                    this.pathAbsolute = host;
                }
                this.host = uri.getHost();
                this.schema = uri.getScheme();
                this.port = uri.getPort();
                final String rawPath = uri.getRawPath();
                if (rawPath != null && !rawPath.equals("/")) {
                    if (pathAbsolute == null) {
                        this.pathAbsolute = rawPath;
                    } else {
                        if (!(rawPath.endsWith("/") || this.pathAbsolute.startsWith("/"))) {
                            this.pathAbsolute = rawPath + "/" + this.pathAbsolute;
                        } else {
                            this.pathAbsolute = rawPath + this.pathAbsolute;
                        }
                    }
                }
            } else if (headerValue.contains(":")) { // host and port
                final String[] parts = headerValue.split(":", 2);
                if (pathAbsolute == null) {
                    this.pathAbsolute = host;
                }
                this.host = parts[0];
                this.port = Integer.parseInt(parts[1]);
            } else {  // host only
                if (pathAbsolute == null) {
                    this.pathAbsolute = host;
                }
                this.host = headerValue;
            }
        }
    }

    public static HttpRequestTarget valueOf(String method, String requestLine) {
        final HttpRequestTarget requestTarget = new HttpRequestTarget();
        requestTarget.method = method;
        requestTarget.requestLine = requestLine;
        String requestUri = requestLine;
        if (requestLine.contains(" HTTP/")) {  // request line with protocol `GET /index.html HTTP/1.1`
            requestUri = requestLine.substring(0, requestLine.lastIndexOf(" "));
            final String protocol = requestLine.substring(requestLine.lastIndexOf(" ") + 1);
            if (protocol.contains("HTTPS")) {
                requestTarget.schema = "https://";
            }
        }
        if (!requestUri.contains("://")) { //correct uri without schema
            if (requestUri.contains(":") || requestUri.indexOf('/') > 0) { // uri without schema
                if (HttpMethod.HTTP_METHODS.contains(method)) {
                    requestUri = "http://" + requestUri;
                }
            }
        }
        if (requestUri.contains("://")) {  //standard URL
            final URI uri = URI.create(requestUri);
            requestTarget.uri = uri;
            requestTarget.schema = uri.getScheme();
            requestTarget.host = uri.getHost();
            requestTarget.port = uri.getPort();
            requestTarget.pathAbsolute = uri.getRawPath();
            requestTarget.query = uri.getRawQuery();
            requestTarget.fragment = uri.getFragment();
        } else if (requestUri.startsWith("/")) { //path
            requestTarget.pathAbsolute = requestUri;
        } else {
            requestTarget.host = requestUri;
        }
        return requestTarget;
    }
}
