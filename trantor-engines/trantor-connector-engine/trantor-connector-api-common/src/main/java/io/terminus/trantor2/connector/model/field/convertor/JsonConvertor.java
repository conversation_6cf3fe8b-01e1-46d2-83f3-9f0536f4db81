package io.terminus.trantor2.connector.model.field.convertor;

import cn.hutool.core.util.StrUtil;
import io.terminus.trantor2.connector.model.field.CtField;
import io.terminus.trantor2.connector.model.field.CtFieldHelper;
import io.terminus.trantor2.connector.model.field.FormatType;
import io.terminus.trantor2.connector.utils.serializer.JsonSerializer;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class JsonConvertor implements FieldBodyConvertor {


    @Override
    public CtField toBodyCtField(String body) {
        if (StrUtil.isEmpty(body)) {
            return null;
        }
        Object data = JsonSerializer.readJsonToObj(body);
        return CtFieldHelper.objToBodyCtField(data, getFormatType());
    }

    @Override
    public FormatType getFormatType() {
        return FormatType.JSON;
    }
}
