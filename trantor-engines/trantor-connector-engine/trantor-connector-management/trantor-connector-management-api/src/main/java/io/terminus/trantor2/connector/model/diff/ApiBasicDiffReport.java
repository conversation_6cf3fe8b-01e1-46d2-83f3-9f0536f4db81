package io.terminus.trantor2.connector.model.diff;

import io.terminus.trantor2.connector.utils.CommonUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ApiBasicDiffReport {


    private List<DiffDetail> diffDetails; //detail

    public String format(int offset) {
        if (CollectionUtils.isEmpty(diffDetails)) return "";
        StringBuilder sb = new StringBuilder();
        Map<DiffType, List<DiffDetail>> diffTypeGroups = diffDetails.stream()
                .sorted(Comparator.comparing(diffDetail -> diffDetail.getDiffType().getRank()))
                .collect(Collectors.groupingBy(DiffDetail::getDiffType, LinkedHashMap::new, Collectors.toList()));
        for (Map.Entry<DiffType, List<DiffDetail>> entry : diffTypeGroups.entrySet()) {
            sb.append(CommonUtil.offsetText(offset + 1)).append(String.format("%s:", entry.getKey())).append("\n");
            List<DiffDetail> details = entry.getValue();
            for (int i = 0; i < details.size(); i++) {
                sb.append(CommonUtil.offsetText(offset + 2)).append(String.format("%d.\n%s\n", i + 1, details.get(i).format(offset + 2)));
            }
        }
        return sb.toString();

    }
}
