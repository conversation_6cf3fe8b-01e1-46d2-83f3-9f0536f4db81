package io.terminus.trantor2.connector.model.functions.auto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.connector.model.field.FormatType;
import lombok.*;

import jakarta.validation.constraints.NotBlank;

/**
 * 出入参生成
 */
@Schema(description = "请求参数生成请求")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ArgsGenerateRequest {


    @Schema(description = "原始数据", required = true)
    @NotBlank
    private String raw;


    @Schema(description = "格式类型")
    private FormatType formatType = FormatType.JSON;
}
