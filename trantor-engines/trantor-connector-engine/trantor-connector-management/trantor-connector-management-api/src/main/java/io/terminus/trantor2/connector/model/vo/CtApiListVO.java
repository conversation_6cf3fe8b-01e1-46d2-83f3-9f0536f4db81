package io.terminus.trantor2.connector.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.connector.model.meta.CtApi;
import lombok.*;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Schema(description = "(连接器)服务列表VO")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CtApiListVO {
    @Schema(description = "服务标识", required = true)
    private String key;
    /**
     * 名称
     */
    @Schema(description = "服务名称", required = true)
    private String name;
    /**
     * 描述
     */
    @Schema(description = "服务描述")
    private String description;
    /**
     * 连接器模版key
     */
    @Schema(description = "模版标识", required = true)
    private String ctTempKey;
    /**
     * 服务分组tag
     */
    @Schema(description = "服务分组")
    private String tag;

    public static CtApiListVO convert(CtApi api) {
        CtApiListVO apiListVO = new CtApiListVO();
        BeanUtils.copyProperties(api, apiListVO);
        return apiListVO;
    }

    public static List<CtApiListVO> convert(List<CtApi> apiList) {
        if (Objects.isNull(apiList)) {
            return new ArrayList<>();
        }
        return apiList.stream()
            .map(CtApiListVO::convert)
            .collect(Collectors.toList());
    }

    public static Paging<CtApiListVO> convert(Paging<CtApi> paging) {
        Paging<CtApiListVO> pagingNew = new Paging<>();
        pagingNew.setTotal(paging.getTotal());
        pagingNew.setData(CtApiListVO.convert(paging.getData()));
        return pagingNew;
    }
}
