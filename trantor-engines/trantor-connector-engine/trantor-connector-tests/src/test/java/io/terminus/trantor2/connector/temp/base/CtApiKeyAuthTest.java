package io.terminus.trantor2.connector.temp.base;

import io.terminus.trantor2.connector.CtBaseIntegrationTests;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

public class CtApiKeyAuthTest extends CtBaseIntegrationTests {
    @Test
    public void testApi() {
        testApi(TestType.JSON);
        testApi(TestType.XFORM);
        testApi(TestType.TEXT);
        testApi(TestType.FORM_DATA);
    }

    private void testApi(TestType testType) {
        Map<String, Object> input = new HashMap<>();
        input.put("id", 10001);
        input.put("content", "Hello, world!");
        //
        Map<?, ?> res = this.executeApiAndUnwrapMap(testType.getApiKey(), input);
        Assertions.assertEquals(((Map<?, ?>) res.get("headers")).get("Header-Auth-Key"), "headerXXXXXX");
        Assertions.assertEquals(((Map<?, ?>) res.get("args")).get("param-auth-key"), "paramXXXXXX");
        //
        testType.assertRes(res);
    }

    @Override
    protected String getTempOssUrl() {
        return "https://terminus-new-trantor.oss-cn-hangzhou.aliyuncs.com/trantor2/console/connector/apiKeyAuthTemp1.zip";
    }

    @Override
    protected String getTempKey() {
        return "apiKeyAuthTemp1";
    }

    /**
     * @return 模版参数
     */
    @Override
    protected Map<String, Object> getTempParams() {
        Map<String, Object> ctParams = new HashMap<>();
        ctParams.put("host", "examples.http-client.intellij.net");
        ctParams.put("headerAuthKey", "headerXXXXXX");
        ctParams.put("paramAuthKey", "paramXXXXXX");
        return ctParams;
    }

    private enum TestType {
        JSON {
            @Override
            public String getApiKey() {
                return "jsonApi1";
            }

            @Override
            public void assertRes(Map<?, ?> res) {
                Assertions.assertEquals(((Map<?, ?>) res.get("json")).get("id"), 10001);
                Assertions.assertEquals(((Map<?, ?>) res.get("headers")).get("Auth-Data-Val"), "jsval");
                Assertions.assertEquals(((Map<?, ?>) res.get("headers")).get("Input-Val"), "jsval");
            }
        },
        XFORM {
            @Override
            public String getApiKey() {
                return "formApi1";
            }

            @Override
            public void assertRes(Map<?, ?> res) {
                Assertions.assertEquals(((Map<?, ?>) res.get("form")).get("id"), "10001");
            }
        },
        TEXT {
            @Override
            public String getApiKey() {
                return "textApi1";
            }

            @Override
            public void assertRes(Map<?, ?> res) {
                Assertions.assertEquals(res.get("data"), "Hello, world!");
            }
        },
        FORM_DATA {
            @Override
            public String getApiKey() {
                return "formDataApi1";
            }

            @Override
            public void assertRes(Map<?, ?> res) {
                Assertions.assertEquals(((Map<?, ?>) res.get("form")).get("id"), "10001");
            }
        };
        abstract public String getApiKey();
        abstract public void assertRes(Map<?, ?> res);
    }
}
