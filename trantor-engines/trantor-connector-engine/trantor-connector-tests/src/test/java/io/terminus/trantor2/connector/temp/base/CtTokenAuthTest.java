package io.terminus.trantor2.connector.temp.base;

import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.connector.CtBaseIntegrationTests;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

@SuppressWarnings("unchecked")
public class CtTokenAuthTest extends CtBaseIntegrationTests {
    private boolean isExpiredInConst;

    @Test
    public void testApi() {
        testApi(TestType.JSON);
        testApi(TestType.XFORM);
        isExpiredInConst = true;
        init();
        testApi(TestType.JSON);
        testApi(TestType.XFORM);
    }

    private void testApi(TestType testType) {
        Map<String, Object> input = new HashMap<>();
        input.put("id", 10001);
        input.put("content", "Hello, world!");
        //
        Response<Object> response = this.executeApi(testType.getApiKey(), input);
        Assertions.assertTrue(response.isSuccess());
        Map<String, Object> res = (Map<String, Object>)response.getData();
        Assertions.assertEquals(((Map<?, ?>) res.get("headers")).get("Authorization"), "Bearer clientXXXXXX-secretXXXXXX");
        //
        testType.assertRes(res);
    }

    @Override
    protected String getTempOssUrl() {
        if (isExpiredInConst) {
            return "https://terminus-new-trantor.oss-cn-hangzhou.aliyuncs.com/trantor2/console/connector/tokenAuthTemp2.zip";
        }
        return "https://terminus-new-trantor.oss-cn-hangzhou.aliyuncs.com/trantor2/console/connector/tokenAuthTemp1.zip";
    }

    @Override
    protected String getTempKey() {
        if (isExpiredInConst) {
            return "tokenAuthTemp2";
        }
        return "tokenAuthTemp1";
    }

    /**
     * @return 模版参数
     */
    @Override
    protected Map<String, Object> getTempParams() {
        Map<String, Object> ctParams = new HashMap<>();
        ctParams.put("host", "examples.http-client.intellij.net");
        ctParams.put("client_id", "clientXXXXXX");
        ctParams.put("client_secret", "secretXXXXXX");
        if (!isExpiredInConst) {
            ctParams.put("expire", 900);
        }
        return ctParams;
    }

    private enum TestType {
        JSON {
            @Override
            public String getApiKey() {
                return "jsonApi1";
            }

            @Override
            public void assertRes(Map<String, Object> res) {
                Assertions.assertEquals(((Map<?, ?>) res.get("json")).get("id"), 10001);
                Assertions.assertEquals(((Map<?, ?>) res.get("headers")).get("Auth-Data-Val"), "jsval");
                Assertions.assertEquals(((Map<?, ?>) res.get("headers")).get("Input-Val"), "jsval");
            }
        },
        XFORM {
            @Override
            public String getApiKey() {
                return "formApi1";
            }

            @Override
            public void assertRes(Map<String, Object> res) {
                Assertions.assertEquals(((Map<?, ?>) res.get("form")).get("id"), "10001");
            }
        };
        abstract public String getApiKey();
        abstract public void assertRes(Map<String, Object> res);
    }
}
