package io.terminus.trantor2.module.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.iam.api.request.user.UserPagingParams;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "全功能用户")
public class AllPermissionUserPagingRequest extends UserPagingParams {

    @Schema(description = "门户code", required = true)
    @NotNull(message = "portalCode can not be null")
    private String portalCode;
}
