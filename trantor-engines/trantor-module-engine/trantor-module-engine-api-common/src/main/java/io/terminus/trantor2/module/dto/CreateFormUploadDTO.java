package io.terminus.trantor2.module.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateFormUploadDTO {
    private String objectName;
    private String endpoint;
    private String bucket;
    private Boolean privateAccess;

    private String formUrl;
    private String ossAccessKeyId;
    private String policy;
    private String signature;
}
