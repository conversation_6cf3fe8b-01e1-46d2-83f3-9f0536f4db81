package io.terminus.trantor2.module.service;

import io.terminus.trantor2.module.meta.Variable;

import java.util.List;

/**
 * 提示词变量服务接口
 */
public interface PromptVariableService {

    /**
     * 查询指定团队和模块的提示词变量列表
     *
     * @param teamId    团队ID
     * @param moduleKey 模块Key
     * @return 提示词变量列表
     */
    List<Variable> queryPromptVariable(Long teamId, String moduleKey);

    /**
     * 查找指定提示词变量的值
     *
     * @param teamId    团队ID
     * @param moduleKey 模块Key
     * @param key       变量Key
     * @return 变量值，不存在则返回null
     */
    String findPromptVariableValue(Long teamId, String moduleKey, String key);
}