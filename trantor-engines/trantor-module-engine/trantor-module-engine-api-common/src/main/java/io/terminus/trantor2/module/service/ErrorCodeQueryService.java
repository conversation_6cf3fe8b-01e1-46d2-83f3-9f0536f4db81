package io.terminus.trantor2.module.service;

import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.module.meta.ErrorCodeMeta;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface ErrorCodeQueryService {
    @Nonnull
    default Optional<ErrorCodeMeta> findOneByKey(@Nonnull String key) {
        return findOneByKey(key, ResourceContext.ctxFromThreadLocal());
    }

    @Nonnull
    Optional<ErrorCodeMeta> findOne<PERSON>y<PERSON>ey(@Nonnull String key, @Nullable ResourceContext ctx);
}
