package io.terminus.trantor2.module.service;

import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.model.dto.ModuleDTO;

import java.util.Collection;

/**
 * 废弃
 * console 使用 {@link io.terminus.trantor2.module.service.ModuleManageService}
 * runtime 使用 {@link io.terminus.trantor2.module.service.ModuleRuntimeQueryService}
 * <AUTHOR>
 */
@Deprecated
public interface ModuleQueryService {

    ModuleMeta findByKey(String key);

    Collection<ModuleMeta> findAllByKeysIn(Collection<String> keys);

    ModuleDTO queryPortalRelation(String key);

    @Deprecated
    ModuleMeta findById(Long id);

    String findKeyById(Long id);
}
