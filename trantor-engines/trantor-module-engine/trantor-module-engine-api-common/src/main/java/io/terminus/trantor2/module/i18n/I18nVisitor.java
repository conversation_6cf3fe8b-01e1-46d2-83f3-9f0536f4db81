package io.terminus.trantor2.module.i18n;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaType;
import jakarta.annotation.Nullable;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */
public interface I18nVisitor {
    default Set<String> visit(Collection<String> moduleKeys) {
        if (CollectionUtils.isEmpty(moduleKeys)) {
            return Collections.emptySet();
        }
        if (moduleKeys.size() < 12) {
            return bitchVisit(moduleKeys);
        } else {
            Set<String> result = visit(Field.type().equal(getType().name()), moduleKeys::contains);
            return result.stream()
                    .filter(key -> key != null && key.length() <= 512)
                    .collect(Collectors.toSet());
        }
    }

    default Set<String> bitchVisit(Collection<String> moduleKeys) {
        Set<String> resultSet = new HashSet<>();
        List<String> moduleKeyList = new ArrayList<>(moduleKeys);
        int batchSize = 3;
        TrantorContext.Context tctx = TrantorContext.copy();
        IntStream.range(0, (moduleKeyList.size() + batchSize - 1) / batchSize)
                .parallel()
                .forEach(i -> {
                    TrantorContext.init();
                    TrantorContext.setContext(tctx);

                    List<String> currentBatch = moduleKeyList.subList(i * batchSize,
                            Math.min((i + 1) * batchSize, moduleKeyList.size()));

                    List<Cond> moduleCondList = new ArrayList<>(currentBatch.size());
                    for (String moduleKey : currentBatch) {
                        moduleCondList.add(Field.key().like(moduleKey + "$%"));
                    }
                    Cond cond = Cond.and(Field.type().equal(getType().name()),
                            Cond.or(moduleCondList.toArray(new Cond[0])));
                    resultSet.addAll(visit(cond));
                    TrantorContext.clear();
                });
        return resultSet;
    }

    default Set<String> visit(Cond cond) {
        Set<String> result = visit(cond, null);
        return result.stream()
                .filter(key -> key != null && key.length() <= 512)
                .collect(Collectors.toSet());
    }

    default boolean moduleResource() {
        return true;
    }
    MetaType getType();

    Set<String> visit(Cond cond, @Nullable Predicate<String> predicate);

}
