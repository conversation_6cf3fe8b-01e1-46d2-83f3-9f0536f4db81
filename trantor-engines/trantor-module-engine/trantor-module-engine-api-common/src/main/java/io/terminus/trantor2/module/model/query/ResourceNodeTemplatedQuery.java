package io.terminus.trantor2.module.model.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public final class ResourceNodeTemplatedQuery {
    private String currentModuleKey;
    private ModuleRelationQueryMode moduleRelationQueryMode;
    private QueryAccessLevel queryAccessLevel;
    private QueryTemplate queryTemplate;
    private Map<String, String> queryParams;

    /**
     * The fuzzy value.
     * <p>
     * [optional]
     */
    private String fuzzyValue;

    /**
     * The type of the node.
     * <p>
     * [optional]
     */
    private String type;

    /**
     * The parent folder key.
     * <p>
     * [optional]
     */
    private String folderKey;
}
