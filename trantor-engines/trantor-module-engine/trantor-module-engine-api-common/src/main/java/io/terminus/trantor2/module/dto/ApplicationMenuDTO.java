package io.terminus.trantor2.module.dto;

import io.terminus.trantor2.module.meta.MenuMeta;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ApplicationMenuDTO {

    /**
     * 应用id
     */
    private Long appId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 应用标识
     */
    private String appCode;

    /**
     * 团队id
     */
    private Long teamId;

    /**
     * 团队名称
     */
    private String teamName;

    /**
     * 菜单
     */
    private List<MenuMeta> menus;

    /**
     * 工作台
     */
    private List<MenuMeta> workbenches = new ArrayList<>();

    /**
     * 安装状态
     */
    private InstallStatus status;

    @Deprecated
    private Boolean createSync = true;

    private Boolean syncMenu = true;

    public enum InstallStatus {
        /**
         * 安装中
         */
        INSTALLING,
        /**
         * 安装成功
         */
        INSTALLED
    }
}
