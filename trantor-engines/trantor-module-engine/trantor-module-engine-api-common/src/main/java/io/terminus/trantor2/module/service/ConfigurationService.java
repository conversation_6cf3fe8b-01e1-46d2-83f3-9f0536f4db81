package io.terminus.trantor2.module.service;

import io.terminus.trantor2.module.meta.ConfigType;
import io.terminus.trantor2.module.meta.EnvConfig;
import io.terminus.trantor2.module.meta.ModuleIamConfig;

import java.util.Map;

/**
 * @author: ya<PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/8/9 11:30 AM
 **/
public interface ConfigurationService {
    void checkModuleIamConfig(ModuleIamConfig config, Long teamId, String teamCode, String moduleKey);

    void deleteConfig(Long teamId, String metaKey);

    void cleanConfig(Long teamId);

    Long getIamEndpointId(Long teamId, String moduleKey);

    Map<String, ModuleIamConfig> queryAllModuleIamConfig(Long teamId);

    <T extends EnvConfig> void save(T config, Long teamId, String metaKey, ConfigType type);

    <T extends EnvConfig> T query(Long teamId, String metaKey, ConfigType type);

    <T extends EnvConfig> Map<String, T> queryAll(Long teamId, ConfigType configType);
}
