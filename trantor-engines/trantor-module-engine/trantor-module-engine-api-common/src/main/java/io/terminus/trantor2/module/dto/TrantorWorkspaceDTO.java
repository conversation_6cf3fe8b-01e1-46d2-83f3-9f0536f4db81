package io.terminus.trantor2.module.dto;

import lombok.Data;

import java.util.List;

@Data
public class TrantorWorkspaceDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 标识
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * logo存储地址
     */
    private String logoUrl;

    /**
     * 安装的应用
     */
    private List<TrantorApplicationDTO> installedApps;

    /**
     * 登录配置
     */
    private WorkspaceLoginConfig loginConfig;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 描述
     */
    private String description;

    /**
     * iam应用端id
     */
    private Long iamEndpointId;
}
