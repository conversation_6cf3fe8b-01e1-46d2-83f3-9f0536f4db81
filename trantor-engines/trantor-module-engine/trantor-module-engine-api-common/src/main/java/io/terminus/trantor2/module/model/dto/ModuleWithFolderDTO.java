package io.terminus.trantor2.module.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.meta.objects.tree.folder.Folder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(title = "模块及其文件夹")
public class ModuleWithFolderDTO {
    private String teamCode;
    private String key;
    private String name;
    private List<Folder> folders;
}
