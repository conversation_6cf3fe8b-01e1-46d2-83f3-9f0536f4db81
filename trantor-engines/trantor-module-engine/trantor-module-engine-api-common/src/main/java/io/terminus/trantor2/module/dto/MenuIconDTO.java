package io.terminus.trantor2.module.dto;

import com.google.common.collect.Lists;
import io.terminus.trantor2.module.meta.MenuIcon;
import io.terminus.trantor2.module.meta.MenuIconStatus;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Data
public class MenuIconDTO {
    private String packageKey;

    private String key;
    private String name;

    private String normalUrl;
    private String hoverUrl;
    private String selectedUrl;

    public List<MenuIcon> convert() {
        List<MenuIcon> icons = new ArrayList<>();

        if (StringUtils.isNotBlank(normalUrl)) {
            MenuIcon icon = new MenuIcon();
            icon.setKey(key);
            icon.setName(name);
            icon.setUri(normalUrl);
            icon.setStatus(MenuIconStatus.NORMAL);
            icons.add(icon);
        }

        if (StringUtils.isNotBlank(hoverUrl)) {
            MenuIcon icon = new MenuIcon();
            icon.setKey(key);
            icon.setName(name);
            icon.setUri(hoverUrl);
            icon.setStatus(MenuIconStatus.HOVER);
            icons.add(icon);
        }

        if (StringUtils.isNotBlank(selectedUrl)) {
            MenuIcon icon = new MenuIcon();
            icon.setKey(key);
            icon.setName(name);
            icon.setUri(selectedUrl);
            icon.setStatus(MenuIconStatus.SELECTED);
            icons.add(icon);
        }

        return icons;
    }

    public static List<MenuIconDTO> convert(List<MenuIcon> icons) {
        if (CollectionUtils.isEmpty(icons)) {
            return Lists.newArrayList();
        }

        List<MenuIconDTO> dto = new ArrayList<>();

        Map<String, List<MenuIcon>> group = icons.stream().collect(Collectors.groupingBy(MenuIcon::getKey));
        for (List<MenuIcon> list : group.values()) {
            MenuIconDTO menuIconVO = new MenuIconDTO();
            for (MenuIcon icon : list) {
                menuIconVO.setKey(icon.getKey());
                menuIconVO.setName(icon.getName());
                switch (icon.getStatus()) {
                    case NORMAL:menuIconVO.setNormalUrl(icon.getUri());break;
                    case HOVER:menuIconVO.setHoverUrl(icon.getUri());break;
                    case SELECTED:menuIconVO.setSelectedUrl(icon.getUri());break;
                }
            }
            dto.add(menuIconVO);
        }
        return dto;
    }
}
