package io.terminus.trantor2.module.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "全功能用户请求参数")
public class AllPermissionUserRequest {

    @Schema(description = "userIds", required = true)
    @NotEmpty(message = "userIds can not be empty")
    private List<Long> userIds;

    @Schema(description = "门户code", required = true)
    @NotNull(message = "portalCode can not be null")
    private String portalCode;
}
