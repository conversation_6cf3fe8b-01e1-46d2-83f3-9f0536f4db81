package io.terminus.trantor2.module;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"io.terminus.trantor2"})
@ImportAutoConfiguration({FeignAutoConfiguration.class})
@EnableAspectJAutoProxy(exposeProxy = true, proxyTargetClass = true)
public class ConsoleTestApplication {
    public static void main(String[] args) {
        SpringApplication.run(ConsoleTestApplication.class, args);
    }
}
