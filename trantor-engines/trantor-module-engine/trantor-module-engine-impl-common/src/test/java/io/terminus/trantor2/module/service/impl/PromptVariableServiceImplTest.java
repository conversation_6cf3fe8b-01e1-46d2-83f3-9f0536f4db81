package io.terminus.trantor2.module.service.impl;

import com.google.common.collect.Lists;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.resource.ResourceBaseMeta;
import io.terminus.trantor2.module.meta.PromptVariableMeta;
import io.terminus.trantor2.module.meta.Variable;
import io.terminus.trantor2.module.repository.PromptVariableRepo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 提示词变量运行时服务测试
 */
@ExtendWith(MockitoExtension.class)
class PromptVariableServiceImplTest {

    @Mock
    private PromptVariableRepo promptVariableRepo;

    @InjectMocks
    private PromptVariableServiceImpl promptVariableServiceImpl;

    private String testModuleKey;
    private String testPromptVariableKey;
    private Long testTeamId;

    @BeforeEach
    void setUp() {
        testModuleKey = "test_module";
        testPromptVariableKey = "test_module$prompt_variable__";
        testTeamId = 1L;
    }

    @Test
    void testQueryPromptVariable_WithValidModuleKey_ShouldReturnVariables() {
        // Given
        Variable variable1 = new Variable();
        variable1.setKey("prompt1");
        variable1.setValue("value1");

        Variable variable2 = new Variable();
        variable2.setKey("prompt2");
        variable2.setValue("value2");

        List<Variable> variables = Lists.newArrayList(variable1, variable2);

        PromptVariableMeta.Props props = new PromptVariableMeta.Props();
        props.setVariables(variables);

        PromptVariableMeta meta = new PromptVariableMeta();
        meta.setResourceProps(props);

        when(promptVariableRepo.getPromptVariableKey(testModuleKey)).thenReturn(testPromptVariableKey);
        when(promptVariableRepo.findOneByKey(eq(testPromptVariableKey), any(ResourceContext.class)))
            .thenReturn(Optional.of(meta));

        // When
        List<Variable> result = promptVariableServiceImpl.queryPromptVariable(testTeamId, testModuleKey);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("prompt1", result.get(0).getKey());
        assertEquals("value1", result.get(0).getValue());
        assertEquals("prompt2", result.get(1).getKey());
        assertEquals("value2", result.get(1).getValue());
    }

    @Test
    void testQueryPromptVariable_WithNonExistentModule_ShouldReturnEmptyList() {
        // Given
        when(promptVariableRepo.getPromptVariableKey(testModuleKey)).thenReturn(testPromptVariableKey);
        when(promptVariableRepo.findOneByKey(eq(testPromptVariableKey), any(ResourceContext.class)))
            .thenReturn(Optional.empty());

        // When
        List<Variable> result = promptVariableServiceImpl.queryPromptVariable(testTeamId, testModuleKey);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindPromptVariableValue_WithValidKey_ShouldReturnValue() {
        // Given
        Variable variable1 = new Variable();
        variable1.setKey("prompt1");
        variable1.setValue("value1");

        Variable variable2 = new Variable();
        variable2.setKey("prompt2");
        variable2.setValue("value2");

        List<Variable> variables = Lists.newArrayList(variable1, variable2);

        PromptVariableMeta.Props props = new PromptVariableMeta.Props();
        props.setVariables(variables);

        PromptVariableMeta meta = new PromptVariableMeta();
        meta.setResourceProps(props);

        when(promptVariableRepo.getPromptVariableKey(testModuleKey)).thenReturn(testPromptVariableKey);
        when(promptVariableRepo.findOneByKey(eq(testPromptVariableKey), any(ResourceContext.class)))
            .thenReturn(Optional.of(meta));

        // When
        String result = promptVariableServiceImpl.findPromptVariableValue(testTeamId, testModuleKey, "prompt1");

        // Then
        assertEquals("value1", result);
    }

    @Test
    void testFindPromptVariableValue_WithInvalidKey_ShouldReturnNull() {
        // Given
        Variable variable = new Variable();
        variable.setKey("prompt1");
        variable.setValue("value1");

        List<Variable> variables = Lists.newArrayList(variable);

        PromptVariableMeta.Props props = new PromptVariableMeta.Props();
        props.setVariables(variables);

        PromptVariableMeta meta = new PromptVariableMeta();
        meta.setResourceProps(props);

        when(promptVariableRepo.getPromptVariableKey(testModuleKey)).thenReturn(testPromptVariableKey);
        when(promptVariableRepo.findOneByKey(eq(testPromptVariableKey), any(ResourceContext.class)))
            .thenReturn(Optional.of(meta));

        // When
        String result = promptVariableServiceImpl.findPromptVariableValue(testTeamId, testModuleKey, "nonexistent");

        // Then
        assertNull(result);
    }

    @Test
    void testFindPromptVariableValue_WithEmptyVariables_ShouldReturnNull() {
        // Given
        when(promptVariableRepo.getPromptVariableKey(testModuleKey)).thenReturn(testPromptVariableKey);
        when(promptVariableRepo.findOneByKey(eq(testPromptVariableKey), any(ResourceContext.class)))
            .thenReturn(Optional.empty());

        // When
        String result = promptVariableServiceImpl.findPromptVariableValue(testTeamId, testModuleKey, "prompt1");

        // Then
        assertNull(result);
    }

    @Test
    void testFindPromptVariableValue_WithNullKey_ShouldReturnNull() {
        // Given
        Variable variable = new Variable();
        variable.setKey("prompt1");
        variable.setValue("value1");

        List<Variable> variables = Lists.newArrayList(variable);

        PromptVariableMeta.Props props = new PromptVariableMeta.Props();
        props.setVariables(variables);

        PromptVariableMeta meta = new PromptVariableMeta();
        meta.setResourceProps(props);

        when(promptVariableRepo.getPromptVariableKey(testModuleKey)).thenReturn(testPromptVariableKey);
        when(promptVariableRepo.findOneByKey(eq(testPromptVariableKey), any(ResourceContext.class)))
            .thenReturn(Optional.of(meta));

        // When
        String result = promptVariableServiceImpl.findPromptVariableValue(testTeamId, testModuleKey, null);

        // Then
        assertNull(result);
    }

    @Test
    void testQueryPromptVariable_ShouldCallRepositoryWithCorrectParameters() {
        // Given
        when(promptVariableRepo.getPromptVariableKey(testModuleKey)).thenReturn(testPromptVariableKey);
        when(promptVariableRepo.findOneByKey(eq(testPromptVariableKey), any(ResourceContext.class)))
            .thenReturn(Optional.empty());

        // When
        promptVariableServiceImpl.queryPromptVariable(testTeamId, testModuleKey);

        // Then
        verify(promptVariableRepo, times(1)).getPromptVariableKey(testModuleKey);
        verify(promptVariableRepo, times(1)).findOneByKey(eq(testPromptVariableKey), any(ResourceContext.class));
    }
}