package io.terminus.trantor2.module;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.terminus.trantor2.test.tool.ResourceHelper;

import jakarta.annotation.Nonnull;

/**
 * <AUTHOR>
 */
public class ConsoleTestResourceHelper {
    private static final String RESOURCE_PATH = "json/";
    private static final ObjectMapper mapper = new ObjectMapper();


    public static <T> T readValueFromResource(ObjectMapper mapper, @Nonnull String name, Class<T> valueType) throws JsonProcessingException {
        String json = ResourceHelper.getResourceAsString(ConsoleTestResourceHelper.class, RESOURCE_PATH + name);
        return mapper.readValue(json, valueType);
    }

    public static <T> T readValueFromResource(@Nonnull String name, Class<T> valueType) throws JsonProcessingException {
        String json = ResourceHelper.getResourceAsString(ConsoleTestResourceHelper.class, RESOURCE_PATH + name);
        return mapper.readValue(json, valueType);
    }
}
