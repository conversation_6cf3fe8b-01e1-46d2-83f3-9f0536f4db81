package io.terminus.trantor2.module.querytree;

import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.querytree.DataRetrieveBackend;
import io.terminus.trantor2.meta.resource.TreeBaseMeta;
import io.terminus.trantor2.scene.config.datamanager.DataManagerSceneConfig;
import io.terminus.trantor2.scene.meta.DataManagerViewMeta;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.scene.repo.SceneRepo;
import io.terminus.trantor2.scene.repo.ViewRepo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class SceneWithThinViewDataRetrieveBackend implements DataRetrieveBackend<String, SceneMeta> {
    private final SceneRepo sceneRepo;
    private final ViewRepo viewRepo;

    @Override
    public Map<String, SceneMeta> getAll(Collection<String> keys) {
        if (keys.isEmpty()) {
            return Collections.emptyMap();
        }
        // TODO: copied from io.terminus.trantor2.scene.management.service.SceneManagerQueryServiceImpl.findAllByKeys
        // TODO: `thin` means only view.resources are included in the scene, but we need to refactor to use fresh-new index asset
        ResourceContext resourceCtx = ResourceContext.ctxFromThreadLocal();
        Map<String, String> view2SceneKeyMap = new HashMap<>();
        List<DataManagerViewMeta> views = viewRepo.findAll(Field.parentKey().in(keys), resourceCtx);
        Map<String, List<DataManagerViewMeta>> parentMap = views.stream().collect(Collectors.groupingBy(TreeBaseMeta::getParentKey));

        Map<String, SceneMeta> sceneMap = sceneRepo.findAll(sceneSpec().and(Field.key().in(keys)), resourceCtx)
                .stream().filter(Objects::nonNull)
                .peek(scene -> {
                    if (!CollectionUtils.isEmpty(parentMap.get(scene.getKey())))  {
                        parentMap.get(scene.getKey())
                                .forEach(view -> view2SceneKeyMap.put(view.getKey(), scene.getKey()));
                    }
                })
                .collect(Collectors.toMap(SceneMeta::getKey, s -> s));

        if (CollectionUtils.isEmpty(sceneMap)) {
            return Collections.emptyMap();
        }
        views.forEach(view -> Optional.ofNullable(sceneMap.get(view2SceneKeyMap.get(view.getKey())))
                .ifPresent(scene -> {
                    if (scene.getSceneConfig() instanceof DataManagerSceneConfig) {
                        ((DataManagerSceneConfig) scene.getSceneConfig()).addView(view.getResourceProps());
                    }
                }));
        return sceneMap;
    }

    private Cond sceneSpec() {
        return Field.type().equal(MetaType.Scene.name());
    }
}
