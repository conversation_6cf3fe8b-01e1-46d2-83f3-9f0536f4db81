package io.terminus.trantor2.module.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 规则引擎因子库VO
 * <AUTHOR>
 * @createTime 2023/2/22 3:40 下午
 */
@Schema(description = "RuleEngineFactorModelFieldVO")
@Data
public class RuleEngineFactorModelFieldVO implements Serializable {

    private static final long serialVersionUID = 5614508805466811161L;

    @Schema(description = "属性类型")
    private String fieldType;

    @Schema(description = "属性key")
    private String fieldKey;

    @Schema(description = "属性名称")
    private String fieldName;

    @Schema(description = "属性配置")
    private ModelConfig modelConfig;

    @Data
    @Schema(description = "ModelConfig")
    public class ModelConfig {

        @Schema(description = "前端配置")
        private FrontendConfig frontendConfig;

        @Data
        @Schema(description = "FrontendConfig")
        public class FrontendConfig {

            @Schema(description = "是否多选")
            private Boolean multiSelect;

            @Schema(description = "选项")
            private List<Option> options;

            @Data
            @Schema(description = "Option")
            public class Option {

                @Schema(description = "选项标识")
                private String label;

                @Schema(description = "选项值")
                private String value;
            }
        }
    }
}
