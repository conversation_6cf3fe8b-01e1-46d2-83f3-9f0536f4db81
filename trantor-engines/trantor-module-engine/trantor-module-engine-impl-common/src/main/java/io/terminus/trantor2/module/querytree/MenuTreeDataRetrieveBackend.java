package io.terminus.trantor2.module.querytree;

import io.terminus.trantor2.meta.querytree.DataRetrieveBackend;
import io.terminus.trantor2.module.meta.MenuTreeMeta;
import io.terminus.trantor2.module.service.MenuQueryService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class MenuTreeDataRetrieveBackend implements DataRetrieveBackend<String, MenuTreeMeta> {
    // TODO: MenuQueryService is deprecated
    private final MenuQueryService menuService;

    @Override
    public Map<String, MenuTreeMeta> getAll(Collection<String> keys) {
        if (keys.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<String, MenuTreeMeta> result = new HashMap<>();
        for (String portalKey : keys) {
            MenuTreeMeta menuTree = menuService.getMenuTreeMeta(portalKey);
            if (menuTree != null) {
                result.put(portalKey, menuTree);
            }
        }
        return result;
    }
}
