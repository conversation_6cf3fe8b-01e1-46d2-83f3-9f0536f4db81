package io.terminus.trantor2.module.environment;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.module.meta.ConfigType;
import io.terminus.trantor2.module.meta.PortalAnnouncementConfig;
import io.terminus.trantor2.module.repository.ConfigurationRepository;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> qianjin
 * @since : 2025/8/19
 */
@Component
public class PortalAnnouncementConfigHandler extends AbstractConfigHandler<PortalAnnouncementConfig> {
    public PortalAnnouncementConfigHandler(ConfigurationRepository configurationRepository) {
        super(configurationRepository);
    }

    @Override
    protected PortalAnnouncementConfig convert(String config) {
        return JsonUtil.fromJson(config, PortalAnnouncementConfig.class);
    }

    @Override
    public ConfigType configType() {
        return ConfigType.Portal_Announcement;
    }
}