package io.terminus.trantor2.module.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/trantor/devops/common")
public class DevopsController {
    @GetMapping("/dice-env")
    public Map<String, String> getEnvironmentVariables() {
        Map<String, String> envVars = System.getenv();
        return envVars.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith("DICE_"))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }
}
