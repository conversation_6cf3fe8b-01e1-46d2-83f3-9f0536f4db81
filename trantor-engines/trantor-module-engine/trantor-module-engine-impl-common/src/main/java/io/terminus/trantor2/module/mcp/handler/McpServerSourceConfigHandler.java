package io.terminus.trantor2.module.mcp.handler;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.module.mcp.config.McpServerSourceListConfig;
import io.terminus.trantor2.module.environment.AbstractConfigHandler;
import io.terminus.trantor2.module.meta.ConfigType;
import io.terminus.trantor2.module.repository.ConfigurationRepository;
import org.springframework.stereotype.Component;

/**
 * MCP Server源配置处理器
 */
@Component
public class McpServerSourceConfigHandler extends AbstractConfigHandler<McpServerSourceListConfig> {

    public McpServerSourceConfigHandler(ConfigurationRepository configurationRepository) {
        super(configurationRepository);
    }

    @Override
    protected McpServerSourceListConfig convert(String config) {
        return JsonUtil.fromJson(config, McpServerSourceListConfig.class);
    }

    @Override
    public ConfigType configType() {
        return ConfigType.MCP_Server_Source;
    }
}
