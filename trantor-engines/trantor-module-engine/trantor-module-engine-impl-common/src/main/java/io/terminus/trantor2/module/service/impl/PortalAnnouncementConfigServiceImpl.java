package io.terminus.trantor2.module.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.module.meta.ConfigType;
import io.terminus.trantor2.module.meta.PortalAnnouncementConfig;
import io.terminus.trantor2.module.meta.PortalAnnouncementConfigData;
import io.terminus.trantor2.module.service.ConfigurationService;
import io.terminus.trantor2.module.service.PortalAnnouncementConfigService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR> qianjin
 * @since : 2025/8/19
 */
@AllArgsConstructor
@Service
public class PortalAnnouncementConfigServiceImpl implements PortalAnnouncementConfigService {

    private final ConfigurationService configurationService;

    @Override
    public List<PortalAnnouncementConfigData> queryAll(Long teamId, String moduleKey) {
        List<PortalAnnouncementConfigData> res = new ArrayList<>();
        PortalAnnouncementConfig config = this.getConfigData(teamId, moduleKey);
        if (config != null) {
            res = new ArrayList<>(config.values());
        }
        return res;
    }

    @Override
    public List<PortalAnnouncementConfigData> queryEnabled(Long teamId, String moduleKey) {
        List<PortalAnnouncementConfigData> res = new ArrayList<>();
        PortalAnnouncementConfig config = this.getConfigData(teamId, moduleKey);
        if (config != null) {
            res = config.values().stream()
                    .filter(PortalAnnouncementConfigData::getEnabled)
                    .toList();
        }
        return res;
    }

    @Override
    public Boolean check(Long teamId, String moduleKey) {
        PortalAnnouncementConfig config = this.getConfigData(teamId, moduleKey);
        // 判断是否有数据，是否开启
        return config != null && !config.isEmpty() && config.values().stream().anyMatch(PortalAnnouncementConfigData::getEnabled);
    }

    @Override
    public PortalAnnouncementConfigData updateStatus(Long teamId, String moduleKey, String configKey, Boolean enabled) {
        PortalAnnouncementConfig portalAnnouncementConfig = this.getConfigData(teamId, moduleKey);
        if (portalAnnouncementConfig != null) {
            PortalAnnouncementConfigData configData = portalAnnouncementConfig.get(configKey);
            if (configData != null) {
                configData.setEnabled(enabled);
                configurationService.save(portalAnnouncementConfig, teamId, moduleKey, this.getPortalAnnouncementConfigType());
                return configData;
            }
        }
        throw new TrantorRuntimeException("Portal announcement config not found or update failed");
    }

    @Override
    public List<PortalAnnouncementConfigData> save(Long teamId, String moduleKey, PortalAnnouncementConfigData configData) {
        PortalAnnouncementConfig portalAnnouncementConfig = this.getConfigData(teamId, moduleKey);
        if (portalAnnouncementConfig == null) {
            portalAnnouncementConfig = new PortalAnnouncementConfig();
        }
        if (CharSequenceUtil.isBlank(configData.getKey())) {
            // 如果没有设置key，则是新的数据，生成一个唯一的key
            configData.setKey(this.getConfigKey(teamId, moduleKey));
            configData.setEnabled(true);
        }
        portalAnnouncementConfig.put(configData.getKey(), configData);
        configurationService.save(portalAnnouncementConfig, teamId, moduleKey, this.getPortalAnnouncementConfigType());
        return new ArrayList<>(portalAnnouncementConfig.values());
    }

    @Override
    public void delete(Long teamId, String moduleKey, String configKey) {
        PortalAnnouncementConfig portalAnnouncementConfig = this.getConfigData(teamId, moduleKey);
        if (portalAnnouncementConfig != null) {
            portalAnnouncementConfig.remove(configKey);
            configurationService.save(portalAnnouncementConfig, teamId, moduleKey, this.getPortalAnnouncementConfigType());
        }
    }

    private PortalAnnouncementConfig getConfigData(Long teamId, String moduleKey) {
        return configurationService.query(teamId, moduleKey, this.getPortalAnnouncementConfigType());
    }

    private String getConfigKey(Long teamId, String moduleKey) {
        return teamId + "_" + moduleKey + "_" + UUID.randomUUID();
    }

    private ConfigType getPortalAnnouncementConfigType() {
        return ConfigType.Portal_Announcement;
    }
}