package io.terminus.trantor2.module.repository;

import io.terminus.trantor2.meta.api.repository.ResourceRepository;
import io.terminus.trantor2.module.meta.ModuleVariableMeta;

/**
 * @author: ya<PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/10/12 11:45 AM
 **/
public interface ModuleVariableRepo extends ResourceRepository<ModuleVariableMeta> {
    String MODULE_VARIABLE_META_KEY_SUFFIX = "$variable__";

    default String getVariableKey(String moduleKey) {
        return moduleKey + MODULE_VARIABLE_META_KEY_SUFFIX;
    }
}
