package io.terminus.trantor2.module.service;

import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.module.entity.Position;
import io.terminus.trantor2.module.environment.EnvConfigHandler;
import io.terminus.trantor2.module.exception.WorkspaceInvalidException;
import io.terminus.trantor2.module.meta.ConfigType;
import io.terminus.trantor2.module.meta.EnvConfig;
import io.terminus.trantor2.module.meta.ModuleIamConfig;
import io.terminus.trantor2.module.repository.ConfigurationRepository;
import io.terminus.trantor2.module.util.ModuleUtil;
import io.terminus.trantor2.module.util.ObjectUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static io.terminus.trantor2.common.exception.ErrorType.WORKSPACE_CONFIG_DOMAIN_USED;

/**
 * @author: yang<PERSON>qiang
 * @date: 2023/8/8 3:08 PM
 **/
@Slf4j
public abstract class AbstractConfigurationService implements ConfigurationService {
    protected final Map<ConfigType, EnvConfigHandler<?>> handlerMap;
    protected final ConfigurationRepository repository;

    public AbstractConfigurationService(List<EnvConfigHandler<?>> handlers, ConfigurationRepository repository) {
        handlerMap = handlers.stream().collect(Collectors.toMap(EnvConfigHandler::configType, Function.identity()));
        this.repository = repository;
    }

    @Override
    public void checkModuleIamConfig(ModuleIamConfig config, Long teamId, String teamCode, String moduleKey) {
        String loginUrl = config.getLoginUrl().replaceFirst("^(http[s]?://)", "");
        String loginCallbackUrl = config.getLoginCallbackUrl().replaceFirst("^(http[s]?://)", "");


        List<ModuleIamConfig> configs = repository.findAllByType(ConfigType.Module_IAM).stream()
            .filter(item -> !(Objects.equals(teamId, item.getTeamId()) && Objects.equals(moduleKey, item.getMetaKey())))
            .map(item -> JsonUtil.fromJson(item.getConfig(), ModuleIamConfig.class))
            .collect(Collectors.toList());

        checkLoginCallbackUrl(loginCallbackUrl, teamCode, moduleKey, configs);

        configs.stream()
            .filter(iamConfig -> {
                    String itemLoginUrl = iamConfig.getLoginUrl().replaceFirst("^(http[s]?://)", "");
                    String itemLoginCallbackUrl = iamConfig.getLoginCallbackUrl().replaceFirst("^(http[s]?://)", "");
                    return Objects.equals(loginUrl, itemLoginUrl) || Objects.equals(loginCallbackUrl, itemLoginCallbackUrl);
                }
            )
            .findAny()
            .ifPresent(item -> {
                log.error("login url:{} or login call back url:{} is already exist", config.getLoginUrl(), config.getLoginCallbackUrl());
                String message = String.format("%s, %s", WORKSPACE_CONFIG_DOMAIN_USED.getMessage(), "domain name is already used!");
                throw new WorkspaceInvalidException(WORKSPACE_CONFIG_DOMAIN_USED, message);
            });
    }

    public ModuleIamConfig queryModuleIamConfig(Long teamId, String moduleKey) {
        return query(teamId, moduleKey, ConfigType.Module_IAM);
    }

    @Override
    public Map<String, ModuleIamConfig> queryAllModuleIamConfig(Long teamId) {
        return queryAll(teamId, ConfigType.Module_IAM);
    }

    public Long getIamEndpointId(Long teamId, String moduleKey) {
        return Optional.ofNullable(queryModuleIamConfig(teamId, moduleKey)).map(ModuleIamConfig::getIamEndPointId).orElse(null);
    }

    @Override
    public void deleteConfig(Long teamId, String metaKey) {
        for (ConfigType type : ConfigType.values()) {
            delete(teamId, metaKey, type);
        }
    }

    @Override
    public void cleanConfig(Long teamId) {
        repository.deleteByTeamId(teamId);
    }

    private void delete(Long teamId, String metaKey, ConfigType type) {
        handlerMap.get(type).delete(Position.builder()
            .teamId(teamId)
            .metaKey(metaKey)
            .build());
    }

    private void checkLoginCallbackUrl(String loginCallbackUrl, String teamCode, String moduleKey, List<ModuleIamConfig> configs) {
        if (loginCallbackUrl.endsWith("/")) {
            loginCallbackUrl = loginCallbackUrl.substring(0, loginCallbackUrl.length() - 1);
        }
        if (loginCallbackUrl.contains("/")) {
            String path = loginCallbackUrl.split("/")[1];
            checkUrlPath(path, teamCode, moduleKey);
            String domain = loginCallbackUrl.split("/")[0];
            checkDomain(domain, configs);
            return;
        }

        checkDomainIgnorePath(loginCallbackUrl, configs);
    }

    private void checkDomainIgnorePath(String domain, List<ModuleIamConfig> configs) {
        configs.stream()
            .filter(config -> {
                String url = ObjectUtils.cleanDomain(config.getLoginCallbackUrl());
                return Objects.equals(url, domain);
            })
            .findAny()
            .ifPresent(item -> {
                throw new TrantorRuntimeException("该门户地址不可使用，请修改后重新配置");
            });
    }

    private void checkDomain(String domain, List<ModuleIamConfig> configs) {
        configs.stream()
            .filter(config -> {
                String url = config.getLoginCallbackUrl().replaceFirst("^(http[s]?://)", "");
                if (url.endsWith("/")) {
                    url = url.substring(0, url.length() - 1);
                }
                if (url.contains("/")) {
                    return false;
                }
                return Objects.equals(url, domain);
            })
            .findAny()
            .ifPresent(item -> {
                throw new TrantorRuntimeException("该门户地址不可使用，请修改后重新配置");
            });
    }

    private void checkUrlPath(String path, String teamCode, String moduleKey) {
        String code = ModuleUtil.jointKey(moduleKey, teamCode);
        if (Objects.equals(path, moduleKey) || Objects.equals(path, code)) {
            return;
        }

        throw new TrantorRuntimeException("该门户地址不可使用，请修改后重新配置");
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T extends EnvConfig> T query(Long teamId, String metaKey, ConfigType type) {
        return (T) handlerMap.get(type).query(Position.builder()
            .teamId(teamId)
            .metaKey(metaKey)
            .build());
    }

    @Override
    public <T extends EnvConfig> void save(T config, Long teamId, String metaKey, ConfigType type) {
        if (config == null) {
            return;
        }
        handlerMap.get(type).save(config,
            Position.builder()
                .teamId(teamId)
                .metaKey(metaKey)
                .build());
    }

    @Override
    public <T extends EnvConfig> Map<String, T> queryAll(Long teamId, ConfigType configType) {
        Map<String, T> configs = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        Map<String, ?> raw = handlerMap.get(configType).queryAll(Position.builder().teamId(teamId).build());
        if (raw != null) {
            raw.forEach((key, value) -> {
                configs.put(key, (T) value);
            });
        }
        return configs;
    }
}
