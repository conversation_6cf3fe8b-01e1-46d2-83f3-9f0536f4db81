package io.terminus.trantor2.module.filehandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class FileHandlerFactory {
    private final List<FileHandler> fileHandlers;
    private Map<String, FileHandler> handlerMap;

    @PostConstruct
    public void init() {
        handlerMap = fileHandlers.stream().collect(Collectors.toMap(FileHandler::getFileType, Function.identity()));
    }

    public FileHandler getFileHandler(String fileType) {
        return handlerMap.get(fileType);
    }
}
