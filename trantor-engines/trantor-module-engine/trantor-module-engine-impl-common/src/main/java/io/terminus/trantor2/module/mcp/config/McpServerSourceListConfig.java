package io.terminus.trantor2.module.mcp.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.module.meta.EnvConfig;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * MCP Server源列表配置
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Schema(description = "MCP Server源列表配置")
public class McpServerSourceListConfig implements EnvConfig, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * MCP Server源列表
     */
    @Schema(description = "MCP Server源列表")
    private List<McpServerSourceConfig> sources;
}