package io.terminus.trantor2.module.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> qianjin
 * @since : 2023/7/14
 */
@Data
public class ApplicationAuthorizationPagingDTO implements Serializable {

    private String portalKey;

    private String principalName;
    private String email;
    private String mobile;
    private String nickname;

    private Boolean principalAuthorizationStatus;
    private Boolean principalAuthorizationEnabled;

    private Integer no;
    private Integer size;

}
