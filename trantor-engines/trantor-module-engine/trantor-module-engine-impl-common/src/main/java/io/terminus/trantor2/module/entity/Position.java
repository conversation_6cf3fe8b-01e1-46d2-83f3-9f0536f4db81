package io.terminus.trantor2.module.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;

/**
 * @author: ya<PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/8/8 3:26 PM
 **/
@Data
@Builder
@Embeddable
@NoArgsConstructor
@AllArgsConstructor
public class Position {
    @Column
    private Long teamId;
    @Column(name = "moduleKey")
    private String metaKey;
}
