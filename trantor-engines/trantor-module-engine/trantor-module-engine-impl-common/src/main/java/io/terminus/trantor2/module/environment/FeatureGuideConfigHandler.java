package io.terminus.trantor2.module.environment;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.module.meta.ConfigType;
import io.terminus.trantor2.module.meta.FeatureGuideConfig;
import io.terminus.trantor2.module.repository.ConfigurationRepository;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> qianjin
 * @since : 2025/7/22
 */
@Component
public class FeatureGuideConfigHandler extends AbstractConfigHandler<FeatureGuideConfig> {
    public FeatureGuideConfigHandler(ConfigurationRepository configurationRepository) {
        super(configurationRepository);
    }

    @Override
    protected FeatureGuideConfig convert(String config) {
        return JsonUtil.fromJson(config, FeatureGuideConfig.class);
    }

    @Override
    public ConfigType configType() {
        return ConfigType.Feature_Guide;
    }
}
