package io.terminus.trantor2.module.model.dto.role;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 2023/7/10 2:27 下午
 **/
@Data
@Schema(description = "默认角色请求参数")
public class DefaultRoleRequest implements Serializable {

    @Schema(description = "角色id列表", required = true)
    @NotEmpty
    private List<Long> roleIds;
}
