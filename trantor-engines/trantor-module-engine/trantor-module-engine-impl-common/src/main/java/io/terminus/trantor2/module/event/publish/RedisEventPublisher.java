package io.terminus.trantor2.module.event.publish;

import io.terminus.trantor2.common.event.Event;
import io.terminus.trantor2.common.event.TrantorEventPublisher;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;

import static io.terminus.trantor2.common.event.Event.TRANTOR2_EVENT;


/**
 * <AUTHOR>
 */
@Slf4j
public class RedisEventPublisher extends TrantorEventPublisher {
    private final RTopic topic;

    public RedisEventPublisher(RedissonClient redissonClient) {
        this.topic = redissonClient.getTopic(TRANTOR2_EVENT);
    }

    @Override
    public void publishRemoteAsync(Event event) {
        if (log.isDebugEnabled()) {
            log.debug("publish event: {} to  redis topic [{}]", event, TRANTOR2_EVENT);
        }
        topic.publishAsync(event).thenAcceptAsync(result -> {
            if (log.isDebugEnabled()) {
                log.debug("publish message {} success", event);
            }
        }).exceptionally(ex -> {
            log.error("publish message failed: {}", ex.getMessage());
            return null;
        });
    }

    @Override
    public void publishRemoteSync(Event event) {
        if (log.isDebugEnabled()) {
            log.debug("publish event: {} to  redis topic [{}]", event, TRANTOR2_EVENT);
        }
        topic.publish(event);
    }
}
