package io.terminus.trantor2.module.runtime.event;

import io.terminus.trantor2.common.event.Event;
import io.terminus.trantor2.module.runtime.cache.PreferenceCacheKey;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
public class PreferenceCacheInvalidateEvent implements Event {
    private static final long serialVersionUID = -181619130396983277L;
    private PreferenceCacheKey cacheKey;

    public static PreferenceCacheInvalidateEvent of(PreferenceCacheKey cacheKey) {
        PreferenceCacheInvalidateEvent event = new PreferenceCacheInvalidateEvent();
        event.setCacheKey(cacheKey);
        return event;
    }
}
