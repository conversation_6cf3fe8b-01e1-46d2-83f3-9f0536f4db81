package io.terminus.trantor2.module.runtime.request;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 **/
@Data
public class CreateUploadRequest {
    private String path;

    private String fileName;

    private Boolean privateAccess;

    private String viewCode;		// 场景页面key
    private String modelCode;		// 模型code

    private Map<String, String> headers;

    public String getPath() {
        if (StringUtils.isBlank(path)) {
            return null;
        }
        return path.startsWith("/") ? path.substring(1) : path;
    }
}
