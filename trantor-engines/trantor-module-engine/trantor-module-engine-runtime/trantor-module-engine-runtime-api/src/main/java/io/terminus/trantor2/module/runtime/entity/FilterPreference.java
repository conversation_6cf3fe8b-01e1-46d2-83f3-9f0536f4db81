package io.terminus.trantor2.module.runtime.entity;

import lombok.*;
import lombok.extern.slf4j.Slf4j;

import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;

/**
 * <AUTHOR>
 */
@Slf4j
@Generated
@Entity
@Getter
@Setter
@ToString
@NoArgsConstructor
@DiscriminatorValue("FILTER")
public class FilterPreference extends Preference<FilterPreferenceData> {
    private static final long serialVersionUID = 1526679236846508465L;

    @Override
    public PreferenceType getType() {
        return PreferenceType.FILTER;
    }
}
