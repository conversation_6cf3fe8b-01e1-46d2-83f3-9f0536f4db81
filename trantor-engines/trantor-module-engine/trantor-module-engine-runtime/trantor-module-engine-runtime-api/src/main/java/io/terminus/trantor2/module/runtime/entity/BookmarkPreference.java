package io.terminus.trantor2.module.runtime.entity;

import lombok.*;
import lombok.extern.slf4j.Slf4j;

import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;

/**
 * <AUTHOR>
 */
@Slf4j
@Generated
@Entity
@Getter
@Setter
@ToString
@NoArgsConstructor
@DiscriminatorValue("BOOKMARK")
public class BookmarkPreference extends Preference<BookmarkPreferenceData> {
    private static final long serialVersionUID = 6923367484811282459L;

    @Override
    public PreferenceType getType() {
        return PreferenceType.BOOKMARK;
    }
}
