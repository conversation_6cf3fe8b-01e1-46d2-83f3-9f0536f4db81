package io.terminus.trantor2.module.runtime.cache;

import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.module.meta.I18nLanguagePack;
import io.terminus.trantor2.module.util.I18nProperties;

import jakarta.annotation.Nonnull;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface PortalI18nCache {

    I18nProperties getI18nPropertiesFromOSS(@Nonnull Portal portal, @Nonnull String lang);


    Map<String, String> getI18nResourcesFromMiddleware(@Nonnull Portal portal, @Nonnull String lang);

    void invalidate(@Nonnull String teamCode, @Nonnull String portalCode, @Nonnull I18nLanguagePack languagePack);
}
