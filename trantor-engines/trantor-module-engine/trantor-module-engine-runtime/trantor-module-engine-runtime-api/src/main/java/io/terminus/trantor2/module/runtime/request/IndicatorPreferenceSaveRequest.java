package io.terminus.trantor2.module.runtime.request;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.module.runtime.entity.IndicatorPreferenceData;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(title = "指标卡个性化配置保存请求")
public class IndicatorPreferenceSaveRequest implements PreferenceSaveRequest {
    private static final long serialVersionUID = 4182814185127982752L;

    @Schema(description = "指标卡个性化配置范围，以场景 key::视图 key::指标卡组key 用 :: 分隔")
    @NotNull(message = "scope must not be null")
    private String scope;

    @Schema(description = "指标卡个性化配置内容")
    private List<IndicatorPreferenceData.Indicator> indicators;
}
