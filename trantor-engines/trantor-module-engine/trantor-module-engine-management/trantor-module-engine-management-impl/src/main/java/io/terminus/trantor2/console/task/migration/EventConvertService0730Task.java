package io.terminus.trantor2.console.task.migration;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.console.service.impl.ErrorCodeManagerService;
import io.terminus.trantor2.doc.api.dto.EventDTO;
import io.terminus.trantor2.doc.api.dto.EventModelDTO;
import io.terminus.trantor2.doc.api.dto.NoticeDTO;
import io.terminus.trantor2.doc.api.node.EventActionRelNode;
import io.terminus.trantor2.doc.api.props.ActionProps;
import io.terminus.trantor2.doc.constant.DocEngineMetaTypeConstant;
import io.terminus.trantor2.doc.constant.NoticePlaceHolderType;
import io.terminus.trantor2.doc.service.DocEngineEventManagementService;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaNodeAccessLevel;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.service.QueryOp;
import io.terminus.trantor2.meta.blob.MetaBlob;
import io.terminus.trantor2.meta.blob.MetaBlobRepo;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.StepHelper;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.meta.ErrorCodeMeta;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.repository.ModuleRepo;
import io.terminus.trantor2.rule.engine.api.enums.ValueTypeEnum;
import io.terminus.trantor2.rule.engine.api.model.dto.condition.ConditionDTO;
import io.terminus.trantor2.rule.engine.api.model.dto.condition.FactorValueDTO;
import io.terminus.trantor2.rule.engine.api.model.dto.condition.LeftValueDTO;
import io.terminus.trantor2.rule.engine.api.model.dto.condition.ModelValueDTO;
import io.terminus.trantor2.rule.engine.api.model.dto.condition.RightValueDTO;
import io.terminus.trantor2.service.common.enums.ServiceType;
import io.terminus.trantor2.service.common.meta.ServiceMeta;
import io.terminus.trantor2.service.common.meta.ServiceProps;
import io.terminus.trantor2.service.dsl.ActionNode;
import io.terminus.trantor2.service.dsl.CallEventServiceNode;
import io.terminus.trantor2.service.dsl.CallServiceNode;
import io.terminus.trantor2.service.dsl.ConditionElseNode;
import io.terminus.trantor2.service.dsl.EndNode;
import io.terminus.trantor2.service.dsl.ErrorNode;
import io.terminus.trantor2.service.dsl.ExclusiveBranchNode;
import io.terminus.trantor2.service.dsl.ExclusiveConditionNode;
import io.terminus.trantor2.service.dsl.NoticeNode;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.dsl.ServiceNode;
import io.terminus.trantor2.service.dsl.StartNode;
import io.terminus.trantor2.service.dsl.ValidationNode;
import io.terminus.trantor2.service.dsl.WorkflowNode;
import io.terminus.trantor2.service.dsl.enums.FieldType;
import io.terminus.trantor2.service.dsl.enums.LogicOperator;
import io.terminus.trantor2.service.dsl.enums.Operator;
import io.terminus.trantor2.service.dsl.enums.OutputAssignType;
import io.terminus.trantor2.service.dsl.enums.Propagation;
import io.terminus.trantor2.service.dsl.enums.ValueType;
import io.terminus.trantor2.service.dsl.enums.VariableType;
import io.terminus.trantor2.service.dsl.properties.ActionProperties;
import io.terminus.trantor2.service.dsl.properties.ArrayField;
import io.terminus.trantor2.service.dsl.properties.AssignEntry;
import io.terminus.trantor2.service.dsl.properties.BaseField;
import io.terminus.trantor2.service.dsl.properties.CallEventServiceProperties;
import io.terminus.trantor2.service.dsl.properties.CallServiceProperties;
import io.terminus.trantor2.service.dsl.properties.Condition;
import io.terminus.trantor2.service.dsl.properties.ConditionElseProperties;
import io.terminus.trantor2.service.dsl.properties.ConditionGroup;
import io.terminus.trantor2.service.dsl.properties.ConditionLeaf;
import io.terminus.trantor2.service.dsl.properties.ConstValue;
import io.terminus.trantor2.service.dsl.properties.EndProperties;
import io.terminus.trantor2.service.dsl.properties.ErrorProperties;
import io.terminus.trantor2.service.dsl.properties.ExclusiveBranchProperties;
import io.terminus.trantor2.service.dsl.properties.ExclusiveConditionProperties;
import io.terminus.trantor2.service.dsl.properties.FieldEntry;
import io.terminus.trantor2.service.dsl.properties.ModelField;
import io.terminus.trantor2.service.dsl.properties.NoticeProperties;
import io.terminus.trantor2.service.dsl.properties.ObjectField;
import io.terminus.trantor2.service.dsl.properties.OutputAssign;
import io.terminus.trantor2.service.dsl.properties.RelatedModel;
import io.terminus.trantor2.service.dsl.properties.ServiceProperties;
import io.terminus.trantor2.service.dsl.properties.StartProperties;
import io.terminus.trantor2.service.dsl.properties.StateMachine;
import io.terminus.trantor2.service.dsl.properties.StringEntry;
import io.terminus.trantor2.service.dsl.properties.User;
import io.terminus.trantor2.service.dsl.properties.ValidationProperties;
import io.terminus.trantor2.service.dsl.properties.Value;
import io.terminus.trantor2.service.dsl.properties.VarValue;
import io.terminus.trantor2.service.dsl.properties.WorkflowProperties;
import io.terminus.trantor2.service.engine.impl.component.ModelMetaQuery;
import io.terminus.trantor2.service.management.model.vo.ServiceDoc;
import io.terminus.trantor2.service.management.repo.ServiceRepo;
import io.terminus.trantor2.task.TaskOutput;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 把事件转成服务Task
 *
 * <AUTHOR> Created on 2024/7/26 15:26
 */
@SuppressWarnings("Duplicates")
@Slf4j
@Component
public class EventConvertService0730Task extends BaseTask<EventConvertService0730Task.Options> {

    @Autowired
    private MetaBlobRepo metaBlobRepo;
    @Autowired
    private ServiceRepo serviceRepo;
    @Autowired
    private DocEngineEventManagementService docEngineEventManagementService;
    @Autowired
    private ModuleRepo moduleRepo;
    @Autowired
    private ErrorCodeManagerService errorCodeManagerService;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
        private AtomicLong uuid;
        private List<String> eventKeys;
    }

    @Override
    public void preCheck(Options opts, TaskContext ctx) {
        super.preCheck(opts, ctx);
    }

    @SuppressWarnings("all")
    @Override
    public void exec(Options opts, TaskOutput output, TaskContext ctx) {
        QueryOp q = metaQueryService.queryInTeam(ctx.getTeamCode());
        List<MetaBlob.Base> all = metaBlobRepo.findAll(ctx.getTeamId(), MetaBlob.Base.class);
        ResourceContext rscCtx = ResourceContext.newResourceCtx(ctx.getTeamCode(), ctx.getUserId());

        List<ModuleMeta> allModules = moduleRepo.findAll(Cond.all(), rscCtx);
        Set<String> allowedModuleKeys = allModules.stream()
                .filter(ModuleMeta::isNativeModule)
                .map(it -> it.getKey())
                .collect(Collectors.toSet());
        if (allowedModuleKeys.isEmpty()) {
            return;
        }

        // helper for handle step result
        StepHelper stepHelper = StepHelper.newHelper(output);

        for (MetaBlob.Base nodeId : all) {
            if (!ImmutableSet.of(
                    MetaType.Event.name()
            ).contains(nodeId.getType())) {
                continue;
            }

            if (CollectionUtils.isNotEmpty(opts.getEventKeys()) && !opts.getEventKeys().contains(nodeId.getKey())) {
                continue;
            }

            String moduleKey = KeyUtil.moduleKey(nodeId.getKey());
            boolean isNative = allowedModuleKeys.contains(moduleKey);
            if (!isNative) {
                output.log("skip non-native module: " + moduleKey);
                continue;
            }

            MetaTreeNodeExt node = q.findOne(Field.key().equal(nodeId.getKey())).orElse(null);
            if (node == null || node.getProps() == null || !node.getProps().isObject()) {
                continue;
            }

            stepHelper.exec(node.getKey(), detail -> {
                ServiceMeta serviceMeta = serviceRepo.findOneByKey(node.getKey() + "_SERVICE", rscCtx).get();
                EventDTO eventMeta = docEngineEventManagementService.queryById(node.getId());

                Context context = new Context(opts.getUuid(), eventMeta, serviceMeta, rscCtx);
                ServiceProps serviceProps = convert(context);

                serviceMeta.setAccess(eventMeta.getAccess());
                serviceMeta.setResourceProps(serviceProps);
                serviceRepo.update(serviceMeta, rscCtx);
            });
        }

        stepHelper.complete();
    }

    private ServiceProps convert(Context context) {
        ServiceDefinition definition = context.getDefinition();
        resetDefinition(definition);
        // 事务设置
        definition.getProps().setTransactionPropagation(convertPropagation(context.getEventMeta().getEnabledTransaction()));
        // 状态机
        definition.getProps().setStateMachine(convertStateMachine(context));
        // 开始节点
        definition.getChildren().add(convertStartNode(context));
        // 调用action、调用事件、调用服务、调用审批流、调用校验 节点
        definition.getChildren().addAll(convertActionNode(context));
        // 通知节点
        definition.getChildren().addAll(convertNoticeNode(context));
        // 结束节点
        definition.getChildren().add(convertEndNode(context));

        return context.getServiceMeta().getResourceProps();
    }

    private List<ServiceNode<?>> convertActionNode(Context context) {
        if (CollectionUtils.isEmpty(context.getEventMeta().getActions())) {
            return Collections.emptyList();
        }

        List<ServiceNode<?>> nodes = new ArrayList<>(context.getEventMeta().getActions().size());
        for (EventActionRelNode action : context.getEventMeta().getActions()) {
            switch (action.getActionType()) {
                case DocEngineMetaTypeConstant.ACTION:
                    ServiceNode<?> callActionNode = convertCallActionNode(action, context);

                    // 如果有前置动作参数校验，则构建排他分支
                    // 规则：如果当前节点的参数来源前一个节点，如果设置了参数校验，则抛出异常，如果不设置参数校验，则不执行该节点
                    if (StringUtils.isNotBlank(action.getSourceCode())) {
                        callActionNode = boxExclusiveBranchNodeForSourceActionResultNull(action, callActionNode, action.getEnabledParamCheck(), context);
                    }

                    nodes.add(callActionNode);
                    break;
                case DocEngineMetaTypeConstant.EVENT:
                    ServiceNode<?> callEventServiceNode = convertCallEventServiceNode(action, action.getCode(), context);

                    // 如果有前置动作参数校验，则构建排他分支
                    // 规则：如果当前节点的参数来源前一个节点，如果设置了参数校验，则抛出异常，如果不设置参数校验，则不执行该节点
                    if (StringUtils.isNotBlank(action.getSourceCode())) {
                        callEventServiceNode = boxExclusiveBranchNodeForSourceActionResultNull(action, callEventServiceNode, action.getEnabledParamCheck(), context);
                    }

                    nodes.add(callEventServiceNode);
                    break;
                case DocEngineMetaTypeConstant.SERVICE:
                    ServiceNode<?> callServiceNode;
                    if (action.getServiceProps().getServiceType() == ServiceType.EVENT) {
                        String eventCode = StringUtils.removeEnd(action.getCode(), "_SERVICE");
                        callServiceNode = convertCallEventServiceNode(action, eventCode, context);
                    } else {
                        callServiceNode = convertCallServiceNode(action, context);

                        // 如果有转换脚本，或者有循环取值表达式，则套一个业务事件壳服务
                        if (needBoxCallEventServiceNode(action)) {
                            callServiceNode = boxCallEventServiceNodeWithCallServiceNode(action, (CallServiceNode) callServiceNode, context);
                        }
                        // 如果没有套壳(套壳会把条件设置到壳服务的节点里)，且有条件，构建排他分支
                        else if (CollectionUtils.isNotEmpty(action.getConditions())) {
                            callServiceNode = boxExclusiveBranchNode(convertActionExpress(action, context), action.getConditions(), callServiceNode, context);
                        }
                    }

                    // 如果有前置动作参数校验，则构建排他分支
                    // 规则：如果当前节点的参数来源前一个节点，如果设置了参数校验，则抛出异常，如果不设置参数校验，则不执行该节点
                    if (StringUtils.isNotBlank(action.getSourceCode())) {
                        callServiceNode = boxExclusiveBranchNodeForSourceActionResultNull(action, callServiceNode, action.getEnabledParamCheck(), context);
                    }

                    nodes.add(callServiceNode);
                    break;
                case DocEngineMetaTypeConstant.APPROVAL_WORKFLOW:
                    ServiceNode<?> approvalNode = convertApprovalNode(action, context);

                    // 如果有转换脚本，或者有循环取值表达式，则套一个业务事件壳服务
                    if (needBoxCallEventServiceNode(action)) {
                        approvalNode = boxCallEventServiceNodeWithApprovalNode(action, (WorkflowNode) approvalNode, context);
                    }
                    // 如果没有套壳(套壳会把条件设置到壳服务的节点里)，且有条件，构建排他分支
                    else if (CollectionUtils.isNotEmpty(action.getConditions())) {
                        approvalNode = boxExclusiveBranchNode(convertActionExpress(action, context), action.getConditions(), approvalNode, context);
                    }

                    // 如果有前置动作参数校验，则构建排他分支
                    // 规则：如果当前节点的参数来源前一个节点，如果设置了参数校验，则抛出异常，如果不设置参数校验，则不执行该节点
                    if (StringUtils.isNotBlank(action.getSourceCode())) {
                        approvalNode = boxExclusiveBranchNodeForSourceActionResultNull(action, approvalNode, action.getEnabledParamCheck(), context);
                    }

                    nodes.add(approvalNode);
                    break;
                case DocEngineMetaTypeConstant.VALIDATION:
                    ServiceNode<?> validationNode = convertValidationNode(action, context);

                    // 对 express循环取值的，不做处理，看已有数据，没有类似数据
                    if (needBoxCallEventServiceNode(action)) {
                        throw new IllegalArgumentException("事件中‘执行规则校验’不应该有循环取值表达式或者转换脚本");
                    }
                    // 如果没有套壳(套壳会把条件设置到壳服务的节点里)，且有条件，构建排他分支
                    else if (CollectionUtils.isNotEmpty(action.getConditions())) {
                        validationNode = boxExclusiveBranchNode(convertActionExpress(action, context), action.getConditions(), validationNode, context);
                    }

                    // 如果有前置动作参数校验，则构建排他分支
                    // 规则：如果当前节点的参数来源前一个节点，如果设置了参数校验，则抛出异常，如果不设置参数校验，则不执行该节点
                    if (StringUtils.isNotBlank(action.getSourceCode())) {
                        validationNode = boxExclusiveBranchNodeForSourceActionResultNull(action, validationNode, action.getEnabledParamCheck(), context);
                    }

                    nodes.add(validationNode);
                    break;
            }
        }

        return nodes;
    }

    private ServiceNode<?> boxExclusiveBranchNodeForSourceActionResultNull(EventActionRelNode action, ServiceNode<?> realNode, Boolean enabledParamCheck, Context context) {
        ExclusiveBranchNode node = new ExclusiveBranchNode();
        node.setKey(context.generateNodeKey());
        node.setName("排他分支");
        node.setProps(new ExclusiveBranchProperties());
        node.setChildren(new ArrayList<>(2));

        ExclusiveConditionNode exclusiveConditionNode = new ExclusiveConditionNode();
        exclusiveConditionNode.setKey(context.generateNodeKey());
        exclusiveConditionNode.setName("[" + context.getActionName(action.getSourceCode()) + "]节点出参不为空");
        exclusiveConditionNode.setChildren(new ArrayList<>(1));

        ExclusiveConditionProperties exclusiveConditionProperties = new ExclusiveConditionProperties();

        ConditionLeaf conditionLeaf = new ConditionLeaf();

        VarValue value = new VarValue();
        value.setFieldType(FieldType.Object);
        value.setValueType(ValueType.VAR);

        List<VarValue.VarStage> varValue = new ArrayList<>(4);
        varValue.add(new VarValue.VarStage(VariableType.GLOBAL.getKey(), VariableType.GLOBAL.getName()));
        varValue.add(new VarValue.VarStage(StringUtils.replace(action.getSourceCode(), "$", "_"),
                "[" + context.getActionName(action.getSourceCode()) + "]节点出参"));

        value.setVarValue(varValue);

        conditionLeaf.setLeftValue(value);
        conditionLeaf.setOperator(Operator.IS_NOT_NULL);

        ConditionGroup conditionGroup = new ConditionGroup();
        conditionGroup.setLogicOperator(LogicOperator.AND);
        conditionGroup.setConditions(Lists.newArrayList(new ConditionGroup(Lists.newArrayList(conditionLeaf), LogicOperator.AND)));
        exclusiveConditionProperties.setConditionGroup(conditionGroup);
        exclusiveConditionNode.setProps(exclusiveConditionProperties);

        exclusiveConditionNode.getChildren().add(realNode);

        node.getChildren().add(exclusiveConditionNode);

        ErrorNode errorNode = null;
        if (Boolean.TRUE.equals(enabledParamCheck)) {
            // else 则抛出异常
            errorNode = new ErrorNode();
            errorNode.setKey(context.generateNodeKey());
            errorNode.setName("[" + context.getActionName(action.getSourceCode()) + "]节点出参为空");

            String errorCode = KeyUtil.moduleKey(action.getCode()) + "$EXT_SERVICE_RESULT_IS_NULL";
            String errorName = "扩展服务(${extServiceKey})结果为空";

            // 保存错误码
            saveErrorCodeMetaIfAbsent(errorCode, errorName, context);

            ErrorProperties properties = new ErrorProperties();
            properties.setErrorCode(errorCode);
            properties.setErrorMsg(errorName);

            StringEntry errEntry = new StringEntry();
            errEntry.setKey("extServiceKey");

            ConstValue constValue = new ConstValue();
            constValue.setConstValue(action.getSourceCode());
            constValue.setFieldType(FieldType.Text);
            errEntry.setValue(constValue);

            properties.setPlaceholderMapping(Lists.newArrayList(errEntry));
            errorNode.setProps(properties);
        }

        node.getChildren().add(convertElseConditionNode(errorNode, context));

        return node;
    }

    private void saveErrorCodeMetaIfAbsent(String errorCode, String errorName, Context context) {
        ResourceContext rscCtx = context.getRscCtx();
        if (errorCodeManagerService.findOneByKey(errorCode, rscCtx).isPresent()) {
            return;
        }
        String moduleKey = KeyUtil.moduleKey(errorCode);
        String parentKey = context.getRootErrorCodeKey(moduleKey, (k) -> metaQueryService.queryInApp(
                new MetaEditAndQueryContext(null, rscCtx.getUserId(), rscCtx.getTeamCode(), moduleKey)).findErrorCodeRootKey());
        ErrorCodeMeta meta = new ErrorCodeMeta();
        meta.setKey(errorCode);
        meta.setParentKey(parentKey);
        meta.setName(KeyUtil.shortKey(errorCode));

        ErrorCodeMeta.Props props = new ErrorCodeMeta.Props();
        props.setErrorMsg(errorName);
        meta.setResourceProps(props);
        errorCodeManagerService.save(meta, rscCtx);
    }

    private static List<VarValue.VarStage> convertVarValueFromExpress(String express, Context context) {
        String[] arr = StringUtils.split(express, "\\.");
        List<VarValue.VarStage> stages = new ArrayList<>(arr.length);

        for (String ex : arr) {
            ex = StringUtils.replace(ex, "[]", "");

            String modelKey = null;
            String fieldKey = ex;

            if (ex.contains("(")) {
                String[] split = StringUtils.split(ex, "()");
                modelKey = split[0];
                fieldKey = split[1];
            }

            String fieldName = fieldKey;
            if (VariableType.GLOBAL.getKey().equals(fieldKey)) {
                fieldName = VariableType.GLOBAL.getName();
            } else {
                String actionName = context.getActionNameOrNull(fieldKey, (t) -> StringUtils.replace(t, "$", "_"));
                if (StringUtils.isNotBlank(actionName)) {
                    fieldName = "[" + actionName + "]节点出参";
                }
            }

            stages.add(new VarValue.VarStage(fieldKey, fieldName, modelKey));
        }

        return stages;
    }

    private ActionNode convertCallActionNode(EventActionRelNode action, Context context) {
        ActionNode actionNode = new ActionNode();
        actionNode.setKey(context.generateNodeKey());
        actionNode.setName(action.getName());

        ActionProperties actionProperties = new ActionProperties();
        actionProperties.setNewAction(true);
        actionProperties.setImplementation(action.getCode());
        actionProperties.setImplementationName(action.getName());
        actionProperties.setConvert(action.getConvert());
        actionProperties.setExpress(convertActionExpress(action, context));
        actionProperties.setConditionGroup(convertConditionGroup(actionProperties.getExpress(), action.getConditions(), context));
        actionProperties.setTransactionPropagation(convertPropagation(action.getEnabledTransaction()));
        actionProperties.setOutput(convertActionOutputField(action.getActionProps()));
        if (context.hasOutput() && CollectionUtils.isNotEmpty(actionProperties.getOutput())) {

            io.terminus.trantor2.service.dsl.properties.Field field = actionProperties.getOutput().get(0);

            VarValue left = new VarValue();
            left.setFieldType(field.getFieldType());
            left.setValueType(ValueType.VAR);
            left.setVarValue(new ArrayList<>(2));
            left.getVarValue().add(new VarValue.VarStage(VariableType.OUTPUT.getKey(), VariableType.OUTPUT.getName()));
            left.getVarValue().add(new VarValue.VarStage("data", "data"));

            VarValue right = new VarValue();
            right.setFieldType(field.getFieldType());
            right.setValueType(ValueType.VAR);
            right.setVarValue(new ArrayList<>(3));
            right.getVarValue().add(new VarValue.VarStage(VariableType.NODE_OUTPUT.formatKey(actionNode.getKey()), "节点出参"));
            right.getVarValue().add(new VarValue.VarStage("data", "ActionResponse"));
            right.getVarValue().add(new VarValue.VarStage("data", "data"));

            AssignEntry assignEntry = new AssignEntry();
            assignEntry.setField(left);
            assignEntry.setValue(right);

            io.terminus.trantor2.service.dsl.properties.Field globalField;
            if (action.getReturnModel() != null && action.getReturnModel().getKey() != null) {
                globalField = new ModelField(StringUtils.replace(action.getCode(), "$", "_"),
                        "[" + action.getName() + "]节点出参",
                        new RelatedModel(action.getReturnModel().getKey(), action.getReturnModel().getName()));
            } else {
                globalField = new ObjectField(StringUtils.replace(action.getCode(), "$", "_"),
                        "[" + action.getName() + "]节点出参");
            }

            context.getDefinition().addGlobalVariable(globalField);

            VarValue global = new VarValue();
            global.setFieldType(globalField.getFieldType());
            global.setValueType(ValueType.VAR);
            global.setVarValue(new ArrayList<>(2));
            global.getVarValue().add(new VarValue.VarStage(VariableType.GLOBAL.getKey(), VariableType.GLOBAL.getName()));
            global.getVarValue().add(new VarValue.VarStage(globalField.getFieldKey(), globalField.getFieldName()));

            AssignEntry assignEntry2 = new AssignEntry();
            assignEntry2.setField(global);
            assignEntry2.setValue(right);

            OutputAssign outputAssign = new OutputAssign();
            outputAssign.setOutputAssignType(OutputAssignType.CUSTOM);
            outputAssign.setCustomAssignments(Lists.newArrayList(assignEntry, assignEntry2));

            actionProperties.setOutputAssign(outputAssign);
        }

        actionNode.setProps(actionProperties);
        return actionNode;
    }

    private String convertActionExpress(EventActionRelNode action, Context context) {
        StringBuilder expression = new StringBuilder();
        // 如果参数是上一个action的输出
        if (StringUtils.isNotBlank(action.getSourceCode())) {
            expression.append(VariableType.GLOBAL.getKey()).append(".");
            String modelKey = context.getActionReturnModelKey(action.getSourceCode());
            if (StringUtils.isNotBlank(modelKey)) {
                expression.append("(").append(modelKey).append(")");
            }
            expression.append(StringUtils.replace(action.getSourceCode(), "$", "_"));
        } else {
            expression.append(VariableType.REQUEST.getKey()).append(".");
            String modelKey = context.getEventRequestModelKey();
            if (StringUtils.isNotBlank(modelKey)) {
                expression.append("(").append(modelKey).append(")");
            }
            expression.append("request");
        }
        if (StringUtils.isNotBlank(action.getExpress())) {
            if (action.getExpress().startsWith("[]")) {
                expression.append(action.getExpress());
            } else {
                expression.append(".").append(action.getExpress());
            }
        }
        return expression.toString();
    }

    private ValidationNode convertValidationNode(EventActionRelNode action, Context context) {
        ValidationNode validationNode = new ValidationNode();
        validationNode.setKey(context.generateNodeKey());
        validationNode.setName(action.getName());

        ValidationProperties.ValidationRule rule = new ValidationProperties.ValidationRule();
        VarValue value = convertVarValue(action, context);
        String modelKey = value.getVarValue().get(value.getVarValue().size() - 1).getModelAlias();
        rule.setModelAlias(modelKey);
        rule.setValidateValue(value);
        rule.setRules(Lists.newArrayList(new ValidationProperties.RelatedRule(action.getCode(), action.getName())));

        ValidationProperties validationProperties = new ValidationProperties();
        validationProperties.setValidationRules(Lists.newArrayList(rule));

        validationNode.setProps(validationProperties);
        return validationNode;
    }

    public CallEventServiceNode convertCallEventServiceNode(EventActionRelNode action, String eventCode, Context context) {
        CallEventServiceNode callEventServiceNode = new CallEventServiceNode();
        callEventServiceNode.setKey(context.generateNodeKey());
        callEventServiceNode.setName(action.getName());

        CallEventServiceProperties callEventServiceProperties = new CallEventServiceProperties();
        callEventServiceProperties.setServiceKey(eventCode + "_SERVICE");
        callEventServiceProperties.setServiceName(action.getName());
        callEventServiceProperties.setTransactionPropagation(convertPropagation(action.getEnabledTransaction()));
        callEventServiceProperties.setConvert(action.getConvert());
        callEventServiceProperties.setExpress(convertActionExpress(action, context));
        callEventServiceProperties.setConditionGroup(convertConditionGroup(callEventServiceProperties.getExpress(), action.getConditions(), context));
        callEventServiceProperties.setOutput(convertOutput(action.getEventProps().getReturnModel(),
                action.getEventProps().getReturnModelArrayWhether()));

        if (context.hasOutput() && CollectionUtils.isNotEmpty(callEventServiceProperties.getOutput())) {

            io.terminus.trantor2.service.dsl.properties.Field field = callEventServiceProperties.getOutput().get(0);

            VarValue left = new VarValue();
            left.setFieldType(field.getFieldType());
            left.setValueType(ValueType.VAR);
            left.setVarValue(new ArrayList<>(2));
            left.getVarValue().add(new VarValue.VarStage(VariableType.OUTPUT.getKey(), VariableType.OUTPUT.getName()));
            left.getVarValue().add(new VarValue.VarStage("data", "data"));

            VarValue right = new VarValue();
            right.setFieldType(field.getFieldType());
            right.setValueType(ValueType.VAR);
            right.setVarValue(new ArrayList<>(2));
            right.getVarValue().add(new VarValue.VarStage(VariableType.NODE_OUTPUT.formatKey(callEventServiceNode.getKey()), "节点出参"));
            right.getVarValue().add(new VarValue.VarStage("data", "data"));

            AssignEntry assignEntry = new AssignEntry();
            assignEntry.setField(left);
            assignEntry.setValue(right);

            io.terminus.trantor2.service.dsl.properties.Field globalField;
            if (action.getReturnModel() != null && action.getReturnModel().getKey() != null) {
                globalField = new ModelField(StringUtils.replace(action.getCode(), "$", "_"),
                        "[" + action.getName() + "]节点出参",
                        new RelatedModel(action.getReturnModel().getKey(), action.getReturnModel().getName()));
            } else {
                globalField = new ObjectField(StringUtils.replace(action.getCode(), "$", "_"),
                        "[" + action.getName() + "]节点出参");
            }
            context.getDefinition().addGlobalVariable(globalField);

            VarValue global = new VarValue();
            global.setFieldType(globalField.getFieldType());
            global.setValueType(ValueType.VAR);
            global.setVarValue(new ArrayList<>(2));
            global.getVarValue().add(new VarValue.VarStage(VariableType.GLOBAL.getKey(), VariableType.GLOBAL.getName()));
            global.getVarValue().add(new VarValue.VarStage(globalField.getFieldKey(), globalField.getFieldName()));

            AssignEntry assignEntry2 = new AssignEntry();
            assignEntry2.setField(global);
            assignEntry2.setValue(right);

            OutputAssign outputAssign = new OutputAssign();
            outputAssign.setOutputAssignType(OutputAssignType.CUSTOM);
            outputAssign.setCustomAssignments(Lists.newArrayList(assignEntry, assignEntry2));

            callEventServiceProperties.setOutputAssign(outputAssign);
        }

        callEventServiceNode.setProps(callEventServiceProperties);
        return callEventServiceNode;
    }

    private WorkflowNode convertApprovalNode(EventActionRelNode action, Context context) {
        WorkflowNode approvalNode = new WorkflowNode();
        approvalNode.setKey(context.generateNodeKey());
        approvalNode.setName(action.getName());

        WorkflowProperties approvalProperties = new WorkflowProperties();
        approvalProperties.setWorkflowGroupKey(action.getCode());
        approvalProperties.setWorkflowGroupName(action.getName());
        approvalProperties.setModelKey(action.getWorkflowGroupProps().getRelatedModel().getModelKey());
        approvalProperties.setInputMapping(convertVarValue(action, context));

        approvalNode.setProps(approvalProperties);
        return approvalNode;
    }

    private CallEventServiceNode boxCallEventServiceNodeWithCallServiceNode(EventActionRelNode action, CallServiceNode realNode, Context context) {
        CallEventServiceNode callEventServiceNode = new CallEventServiceNode();
        callEventServiceNode.setKey(context.generateNodeKey());
        callEventServiceNode.setName("调用" + action.getName() + "壳业务事件");

        // 这里新增一个壳服务
        ServiceDefinition boxDefinition = createBoxEventServiceWithCallServiceNode(action, realNode, context);

        CallEventServiceProperties properties = new CallEventServiceProperties();
        properties.setServiceKey(boxDefinition.getKey());
        properties.setServiceName(boxDefinition.getName());
        properties.setTransactionPropagation(convertPropagation(action.getEnabledTransaction()));
        properties.setConvert(action.getConvert());
        properties.setExpress(convertActionExpress(action, context));
        properties.setConditionGroup(convertConditionGroup(properties.getExpress(), action.getConditions(), context));
        properties.setOutput(realNode.getProps().getOutput());

        if (context.hasOutput() && CollectionUtils.isNotEmpty(properties.getOutput())) {

            VarValue left = new VarValue();
            left.setFieldType(FieldType.Object);
            left.setValueType(ValueType.VAR);
            left.setVarValue(new ArrayList<>(2));
            left.getVarValue().add(new VarValue.VarStage(VariableType.OUTPUT.getKey(), VariableType.OUTPUT.getName()));
            left.getVarValue().add(new VarValue.VarStage("data", "data"));

            VarValue right = new VarValue();
            right.setFieldType(FieldType.Object);
            right.setValueType(ValueType.VAR);
            right.setVarValue(new ArrayList<>(2));
            right.getVarValue().add(new VarValue.VarStage(VariableType.NODE_OUTPUT.formatKey(callEventServiceNode.getKey()), "节点出参"));

            // 原来事件这样干的，不知道为啥，先这样做，不然转换的业务执行执行会有问题，见EventServiceActionExecutor
            Optional<io.terminus.trantor2.service.dsl.properties.Field> dataField = properties.getOutput()
                    .stream().filter(f -> "data".equals(f.getFieldKey())).findFirst();
            if (dataField.isPresent()) {
                right.getVarValue().add(new VarValue.VarStage("data", "data"));
            }

            AssignEntry assignEntry = new AssignEntry();
            assignEntry.setField(left);
            assignEntry.setValue(right);

            ObjectField globalField = new ObjectField(StringUtils.replace(action.getCode(), "$", "_"),
                    "[" + action.getName() + "]节点出参");
            context.getDefinition().addGlobalVariable(globalField);

            VarValue global = new VarValue();
            global.setFieldType(FieldType.Object);
            global.setValueType(ValueType.VAR);
            global.setVarValue(new ArrayList<>(2));
            global.getVarValue().add(new VarValue.VarStage(VariableType.GLOBAL.getKey(), VariableType.GLOBAL.getName()));
            global.getVarValue().add(new VarValue.VarStage(globalField.getFieldKey(), globalField.getFieldName()));

            AssignEntry assignEntry2 = new AssignEntry();
            assignEntry2.setField(global);
            assignEntry2.setValue(right);

            OutputAssign outputAssign = new OutputAssign();
            outputAssign.setOutputAssignType(OutputAssignType.CUSTOM);
            outputAssign.setCustomAssignments(Lists.newArrayList(assignEntry, assignEntry2));

            properties.setOutputAssign(outputAssign);
        }

        callEventServiceNode.setProps(properties);

        return callEventServiceNode;
    }

    private ServiceDefinition createBoxEventServiceWithCallServiceNode(EventActionRelNode action, CallServiceNode realNode, Context context) {
        String key = realNode.getProps().getServiceKey() + "_BOX_SERVICE";
        String name = realNode.getName() + "壳服务";

        ServiceDefinition definition = new ServiceDefinition(key, name);
        definition.setDesc("是为解决“" + action.getName() + "”业务事件调用编排服务中有循环取值表达式或有转换脚本问题而创建的壳业务事件");
        definition.setProps(new ServiceProperties());
        definition.setChildren(new ArrayList<>(3));

        // 开始节点
        StartNode startNode = new StartNode();
        startNode.setKey(context.generateNodeKey());
        startNode.setName("开始");

        String modelKey = realNode.getProps().getInputMapping().stream().filter(t -> t.getField().getFieldKey().equals("modelKey"))
                .findFirst().map(f -> (String) ((ConstValue) f.getValue()).getConstValue()).orElse(null);

        StartProperties properties = new StartProperties();
        properties.setInput(Lists.newArrayList(new ModelField("request", new RelatedModel(modelKey))));
        properties.setOutput(realNode.getProps().getOutput());
        startNode.setProps(properties);

        // 调用服务节点
        CallServiceNode callServiceNode = new CallServiceNode();
        callServiceNode.setKey(context.generateNodeKey());
        callServiceNode.setName(realNode.getName());

        CallServiceProperties callServiceProperties = new CallServiceProperties();
        callServiceProperties.setServiceKey(realNode.getProps().getServiceKey());
        callServiceProperties.setServiceName(realNode.getProps().getServiceName());
        callServiceProperties.setTransactionPropagation(realNode.getProps().getTransactionPropagation());
        callServiceProperties.setOutput(realNode.getProps().getOutput());

        callServiceNode.setProps(callServiceProperties);

        // 需要赋值给出参
        if (CollectionUtils.isNotEmpty(realNode.getProps().getOutput())) {
            OutputAssign outputAssign = new OutputAssign();
            outputAssign.setOutputAssignType(OutputAssignType.CUSTOM);
            List<AssignEntry> assignEntries = new ArrayList<>(realNode.getProps().getOutput().size());

            for (io.terminus.trantor2.service.dsl.properties.Field field : realNode.getProps().getOutput()) {
                AssignEntry assignEntry = new AssignEntry();

                VarValue left = new VarValue();
                left.setFieldType(field.getFieldType());
                left.setValueType(ValueType.VAR);
                left.setVarValue(new ArrayList<>(2));
                left.getVarValue().add(new VarValue.VarStage(VariableType.OUTPUT.getKey(), VariableType.OUTPUT.getName()));
                left.getVarValue().add(new VarValue.VarStage(field.getFieldKey(), field.getFieldName()));

                VarValue right = new VarValue();
                right.setFieldType(field.getFieldType());
                right.setValueType(ValueType.VAR);
                right.setVarValue(new ArrayList<>(2));
                right.getVarValue().add(new VarValue.VarStage(VariableType.NODE_OUTPUT.formatKey(callServiceNode.getKey()), "节点出参"));
                right.getVarValue().add(new VarValue.VarStage(field.getFieldKey(), field.getFieldName()));

                assignEntry.setField(left);
                assignEntry.setValue(right);

                assignEntries.add(assignEntry);
            }

            outputAssign.setCustomAssignments(assignEntries);

            callServiceProperties.setOutputAssign(outputAssign);
        }

        //        paramMap.put(REQUEST, param);
        //        paramMap.put(MODEL_KEY, actionExecuteNode.getActionModelKey());

        FieldEntry fieldEntry = new FieldEntry();
        fieldEntry.setField(new io.terminus.trantor2.service.dsl.properties.ObjectField("request"));
        VarValue value = new VarValue();
        value.setFieldType(FieldType.Model);
        value.setValueType(ValueType.VAR);
        value.setVarValue(new ArrayList<>(2));
        value.getVarValue().add(new VarValue.VarStage(VariableType.REQUEST.getKey(), VariableType.REQUEST.getName()));
        value.getVarValue().add(new VarValue.VarStage("request", "request"));
        fieldEntry.setValue(value);

        FieldEntry modelKeyFieldEntry = new FieldEntry();
        modelKeyFieldEntry.setField(new io.terminus.trantor2.service.dsl.properties.BaseField("modelKey", FieldType.Text));
        modelKeyFieldEntry.setValue(new ConstValue(FieldType.Text, modelKey));

        callServiceProperties.setInputMapping(Lists.newArrayList(fieldEntry, modelKeyFieldEntry));

        definition.getChildren().add(startNode);
        definition.getChildren().add(callServiceNode);
        definition.getChildren().add(convertEndNode(context));

        saveEventService(definition, context);

        return definition;
    }

    private CallEventServiceNode boxCallEventServiceNodeWithApprovalNode(EventActionRelNode action, WorkflowNode realNode, Context context) {
        CallEventServiceNode callEventServiceNode = new CallEventServiceNode();
        callEventServiceNode.setKey(context.generateNodeKey());
        callEventServiceNode.setName("调用" + action.getName() + "壳业务事件");

        // 这里新增一个壳服务
        ServiceDefinition definition = createBoxEventServiceWithApprovalNode(action, realNode, context);

        CallEventServiceProperties properties = new CallEventServiceProperties();
        properties.setServiceKey(definition.getKey());
        properties.setServiceName(definition.getName());
        properties.setTransactionPropagation(convertPropagation(action.getEnabledTransaction()));
        properties.setConvert(action.getConvert());
        properties.setExpress(convertActionExpress(action, context));
        properties.setConditionGroup(convertConditionGroup(properties.getExpress(), action.getConditions(), context));

        callEventServiceNode.setProps(properties);

        return callEventServiceNode;
    }

    private ServiceDefinition createBoxEventServiceWithApprovalNode(EventActionRelNode action, WorkflowNode realNode, Context context) {
        String key = realNode.getProps().getWorkflowGroupKey() + "_BOX_SERVICE";
        String name = realNode.getName() + "壳服务";

        ServiceDefinition definition = new ServiceDefinition(key, name);
        definition.setDesc("是为解决“" + action.getName() + "”业务事件调用审批流中有循环取值表达式或有转换脚本问题而创建的壳业务事件");
        definition.setProps(new ServiceProperties());
        definition.setChildren(new ArrayList<>(3));

        // 开始节点
        StartNode startNode = new StartNode();
        startNode.setKey(context.generateNodeKey());
        startNode.setName("开始");

        StartProperties properties = new StartProperties();
        properties.setInput(Lists.newArrayList(new ModelField("request", new RelatedModel(realNode.getProps().getModelKey()))));
        startNode.setProps(properties);

        // 调用审批流节点
        WorkflowNode approvalNode = new WorkflowNode();
        approvalNode.setKey(context.generateNodeKey());
        approvalNode.setName(realNode.getName());

        WorkflowProperties approvalProperties = new WorkflowProperties();
        approvalProperties.setWorkflowGroupKey(realNode.getProps().getWorkflowGroupKey());
        approvalProperties.setWorkflowGroupName(realNode.getProps().getWorkflowGroupName());
        approvalProperties.setModelKey(realNode.getProps().getModelKey());

        // 入参映射
        VarValue value = new VarValue();
        value.setFieldType(FieldType.Model);
        value.setValueType(ValueType.VAR);
        List<VarValue.VarStage> varStages = new ArrayList<>(2);
        varStages.add(new VarValue.VarStage(VariableType.REQUEST.getKey(), VariableType.REQUEST.getName()));
        varStages.add(new VarValue.VarStage("request", "request"));
        value.setVarValue(varStages);
        approvalProperties.setInputMapping(value);

        approvalNode.setProps(approvalProperties);

        definition.getChildren().add(startNode);
        definition.getChildren().add(approvalNode);
        definition.getChildren().add(convertEndNode(context));

        saveEventService(definition, context);

        return definition;
    }

    private void saveEventService(ServiceDefinition definition, Context context) {
        ServiceMeta serviceMeta = new ServiceMeta();
        serviceMeta.setKey(definition.getKey());
        serviceMeta.setName(definition.getName());
        serviceMeta.setParentKey(context.getEventMeta().getModule());
        serviceMeta.setTeamCode(context.getRscCtx().getTeamCode());
        serviceMeta.setAccess(MetaNodeAccessLevel.Public);
        if (definition.getDesc() != null) {
            serviceMeta.setDescription(JsonUtil.toJson(new ServiceDoc(definition.getDesc())));
        }

        ServiceProps props = new ServiceProps();
        props.setServiceName(definition.getName());
        props.setServiceType(ServiceType.EVENT);
        props.setIsEnabled(true);
        props.setServiceDslJson(definition);

        serviceMeta.setResourceProps(props);

        // 先删除在新增
        serviceRepo.deleteByKey(definition.getKey(), context.getRscCtx());
        serviceRepo.create(serviceMeta, context.getRscCtx());
    }

    private CallServiceNode convertCallServiceNode(EventActionRelNode action, Context context) {
        CallServiceNode callServiceNode = new CallServiceNode();
        callServiceNode.setKey(context.generateNodeKey());
        callServiceNode.setName(action.getName());

        CallServiceProperties callServiceProperties = new CallServiceProperties();
        callServiceProperties.setServiceKey(action.getCode());
        callServiceProperties.setServiceName(action.getName());
        callServiceProperties.setTransactionPropagation(convertPropagation(action.getEnabledTransaction()));

//        paramMap.put(REQUEST, param);
//        paramMap.put(MODEL_KEY, actionExecuteNode.getActionModelKey());

        FieldEntry fieldEntry = new FieldEntry();
        fieldEntry.setField(new io.terminus.trantor2.service.dsl.properties.ObjectField("request"));
        VarValue value = convertVarValue(action, context);
        fieldEntry.setValue(value);

        FieldEntry modelKeyFieldEntry = new FieldEntry();
        modelKeyFieldEntry.setField(new io.terminus.trantor2.service.dsl.properties.ObjectField("modelKey"));
        String modelKey = value.getVarValue().get(value.getVarValue().size() - 1).getModelAlias();
        modelKeyFieldEntry.setValue(new ConstValue(FieldType.Text, modelKey));

        callServiceProperties.setInputMapping(Lists.newArrayList(fieldEntry, modelKeyFieldEntry));
        callServiceProperties.setOutput(action.getServiceProps().getServiceDslJson().getOutput());

        if (context.hasOutput() && CollectionUtils.isNotEmpty(callServiceProperties.getOutput())) {

            VarValue left = new VarValue();
            left.setFieldType(FieldType.Object);
            left.setValueType(ValueType.VAR);
            left.setVarValue(new ArrayList<>(2));
            left.getVarValue().add(new VarValue.VarStage(VariableType.OUTPUT.getKey(), VariableType.OUTPUT.getName()));
            left.getVarValue().add(new VarValue.VarStage("data", "data"));

            VarValue right = new VarValue();
            right.setFieldType(FieldType.Object);
            right.setValueType(ValueType.VAR);
            right.setVarValue(new ArrayList<>(2));
            right.getVarValue().add(new VarValue.VarStage(VariableType.NODE_OUTPUT.formatKey(callServiceNode.getKey()), "节点出参"));

            // 原来事件这样干的，不知道为啥，先这样做，不然转换的业务执行执行会有问题，见EventServiceActionExecutor
            Optional<io.terminus.trantor2.service.dsl.properties.Field> dataField = callServiceProperties.getOutput()
                    .stream().filter(f -> "data".equals(f.getFieldKey())).findFirst();
            if (dataField.isPresent()) {
                right.getVarValue().add(new VarValue.VarStage("data", "data"));
            }

            AssignEntry assignEntry = new AssignEntry();
            assignEntry.setField(left);
            assignEntry.setValue(right);

            ObjectField globalField = new ObjectField(StringUtils.replace(action.getCode(), "$", "_"),
                    "[" + action.getName() + "]节点出参");
            context.getDefinition().addGlobalVariable(globalField);

            VarValue global = new VarValue();
            global.setFieldType(FieldType.Object);
            global.setValueType(ValueType.VAR);
            global.setVarValue(new ArrayList<>(2));
            global.getVarValue().add(new VarValue.VarStage(VariableType.GLOBAL.getKey(), VariableType.GLOBAL.getName()));
            global.getVarValue().add(new VarValue.VarStage(globalField.getFieldKey(), globalField.getFieldName()));

            AssignEntry assignEntry2 = new AssignEntry();
            assignEntry2.setField(global);
            assignEntry2.setValue(right);

            OutputAssign outputAssign = new OutputAssign();
            outputAssign.setOutputAssignType(OutputAssignType.CUSTOM);
            outputAssign.setCustomAssignments(Lists.newArrayList(assignEntry, assignEntry2));

            callServiceProperties.setOutputAssign(outputAssign);
        }

        callServiceNode.setProps(callServiceProperties);
        return callServiceNode;
    }

    private VarValue convertVarValue(EventActionRelNode action, Context context) {
        VarValue value = new VarValue();
        value.setFieldType(FieldType.Object);
        value.setValueType(ValueType.VAR);

        List<VarValue.VarStage> stages = new ArrayList<>(4);
        if (StringUtils.isNotBlank(action.getSourceCode())) {
            stages.add(new VarValue.VarStage(VariableType.GLOBAL.getKey(), VariableType.GLOBAL.getName()));
            stages.add(new VarValue.VarStage(StringUtils.replace(action.getSourceCode(), "$", "_"),
                    "[" + context.getActionName(action.getSourceCode()) + "]节点出参",
                    context.getActionReturnModelKey(action.getSourceCode())));
        } else {
            stages.add(new VarValue.VarStage(VariableType.REQUEST.getKey(), VariableType.REQUEST.getName()));
            stages.add(new VarValue.VarStage("request", "request", context.getEventRequestModelKey()));
        }

        if (StringUtils.isNotBlank(action.getExpress())) {
            stages.addAll(convertVarValueFromExpress(action.getExpress(), context));
        }

        value.setVarValue(stages);

        return value;
    }

    private boolean needBoxCallEventServiceNode(EventActionRelNode action) {
        return StringUtils.isNotBlank(action.getConvert())
                || (StringUtils.isNotBlank(action.getExpress()) && action.getExpress().contains("[]"));
    }

    private static List<io.terminus.trantor2.service.dsl.properties.Field> convertActionOutputField(ActionProps actionProps) {
        String responseType = actionProps.getResponseType();
        if (StringUtils.isNotBlank(responseType) && !Void.class.getTypeName().equals(responseType)) {
            EventModelDTO returnModel = actionProps.getReturnModel();
            String modelKey = null;
            if (returnModel != null) {
                modelKey = returnModel.getKey();
            }
            // action总是会在外层套一个Response
            io.terminus.trantor2.service.dsl.properties.Field output = convertField(responseType, "data", modelKey);
            io.terminus.trantor2.service.dsl.properties.Field success = new BaseField("success", FieldType.Boolean);
            io.terminus.trantor2.service.dsl.properties.Field err = new ObjectField("err");
            io.terminus.trantor2.service.dsl.properties.Field info = new ObjectField("info");
            if (output != null) {
                return Lists.newArrayList(new ObjectField("data", "ActionResponse", success, output, err, info));
            }
        }
        return null;
    }

    @SuppressWarnings("SameParameterValue")
    private static io.terminus.trantor2.service.dsl.properties.Field convertField(String classType, String defaultName, String modelKey) {
        return io.terminus.trantor2.service.dsl.properties.Field.convertFieldFromClassType(classType, defaultName, modelKey);
    }

    private List<ServiceNode<?>> convertNoticeNode(Context context) {
        if (CollectionUtils.isEmpty(context.getEventMeta().getNotices())) {
            return Collections.emptyList();
        }

        // 过滤掉空的通知
        List<NoticeDTO> notices = context.getEventMeta().getNotices().stream()
                .filter(n -> StringUtils.isNotBlank(n.getNoticeSceneCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notices)) {
            return Collections.emptyList();
        }

        List<ServiceNode<?>> noticeNodes = new ArrayList<>(notices.size());

        for (NoticeDTO noticeDTO : notices) {
            ServiceNode<?> noticeNode = convertNoticeNode(noticeDTO, context);
            if (CollectionUtils.isNotEmpty(noticeDTO.getConditions())) {
                noticeNodes.add(boxExclusiveBranchNode("REQUEST.request", noticeDTO.getConditions(), noticeNode, context));
            } else {
                noticeNodes.add(noticeNode);
            }
        }

        return noticeNodes;
    }

    private ExclusiveBranchNode boxExclusiveBranchNode(String expression, List<List<ConditionDTO>> conditions, ServiceNode<?> realNode, Context context) {
        ExclusiveBranchNode node = new ExclusiveBranchNode();
        node.setKey(context.generateNodeKey());
        node.setName("排他分支");
        node.setProps(new ExclusiveBranchProperties());
        node.setChildren(new ArrayList<>(2));

        ExclusiveConditionNode exclusiveConditionNode = new ExclusiveConditionNode();
        exclusiveConditionNode.setKey(context.generateNodeKey());
        exclusiveConditionNode.setName(realNode.getName() + "判断条件");
        exclusiveConditionNode.setChildren(new ArrayList<>(1));

        ExclusiveConditionProperties exclusiveConditionProperties = new ExclusiveConditionProperties();
        exclusiveConditionProperties.setConditionGroup(convertConditionGroup(expression, conditions, context));
        exclusiveConditionNode.setProps(exclusiveConditionProperties);

        exclusiveConditionNode.getChildren().add(realNode);

        node.getChildren().add(exclusiveConditionNode);
        node.getChildren().add(convertElseConditionNode(null, context));

        return node;
    }

    private ConditionElseNode convertElseConditionNode(ServiceNode<?> node, Context context) {
        ConditionElseNode conditionElseNode = new ConditionElseNode();
        conditionElseNode.setKey(context.generateNodeKey());
        conditionElseNode.setName("else");
        conditionElseNode.setProps(new ConditionElseProperties());
        if (node != null) {
            conditionElseNode.setChildren(new ArrayList<>(1));
            conditionElseNode.getChildren().add(node);
        }
        return conditionElseNode;
    }

    private NoticeNode convertNoticeNode(NoticeDTO noticeDTO, Context context) {
        NoticeProperties properties = new NoticeProperties();
        properties.setNoticeSceneCode(noticeDTO.getNoticeSceneCode());
        properties.setNoticeSceneName(noticeDTO.getNoticeSceneName());

        // 占位符
        if (MapUtils.isNotEmpty(noticeDTO.getPlaceholderExpressMap())) {
            List<StringEntry> entries = new ArrayList<>();
            noticeDTO.getPlaceholderExpressMap().forEach((k, v) -> {
                StringEntry entry = new StringEntry();
                entry.setKey(k);
                if (NoticePlaceHolderType.VARIABLE.equals(v.getType())) {
                    VarValue value = new VarValue();
                    value.setFieldType(FieldType.Text);
                    value.setValueType(ValueType.VAR);
                    value.setVarValue(convertVarValueFromRequest(v.getValue()));
                    entry.setValue(value);
                } else {
                    ConstValue value = new ConstValue();
                    value.setFieldType(FieldType.Text);
                    value.setConstValue(v.getValue());
                    entry.setValue(value);
                }
                entries.add(entry);
            });
            properties.setInputMapping(entries);
        }

        //指定用户
        if (CollectionUtils.isNotEmpty(noticeDTO.getRecipientIds())) {
            properties.setReceiverUsers(noticeDTO.getRecipientIds().stream().map(User::new).collect(Collectors.toList()));
        }

        // 邮箱
        if (StringUtils.isNotBlank(noticeDTO.getEmailExpress())) {
            VarValue value = new VarValue();
            value.setFieldType(FieldType.Text);
            value.setValueType(ValueType.VAR);
            value.setVarValue(convertVarValueFromRequest(noticeDTO.getEmailExpress()));
            properties.setReceiverEmails(Collections.singletonList(value));
        }

        // 用户标识
        if (StringUtils.isNotBlank(noticeDTO.getRecipientExpress())) {
            VarValue value = new VarValue();
            value.setFieldType(FieldType.Number);
            value.setValueType(ValueType.VAR);
            value.setVarValue(convertVarValueFromRequest(noticeDTO.getRecipientExpress()));
            properties.setReceiverUserIds(Collections.singletonList(value));
        }

        // 手机号码
        if (StringUtils.isNotBlank(noticeDTO.getPhoneExpress())) {
            VarValue value = new VarValue();
            value.setFieldType(FieldType.Text);
            value.setValueType(ValueType.VAR);
            value.setVarValue(convertVarValueFromRequest(noticeDTO.getPhoneExpress()));
            properties.setReceiverMobiles(Collections.singletonList(value));
        }

        NoticeNode node = new NoticeNode();
        node.setKey(context.generateNodeKey());
        node.setName(noticeDTO.getNoticeSceneName());
        node.setProps(properties);
        return node;
    }

    private List<VarValue.VarStage> convertVarValueFromRequest(String value) {
        List<VarValue.VarStage> varStages = new ArrayList<>(4);
        varStages.add(new VarValue.VarStage(VariableType.REQUEST.getKey(), VariableType.REQUEST.getName()));
        varStages.add(new VarValue.VarStage("request", "request"));
        String[] arr = StringUtils.split(value, "\\.");
        for (String s : arr) {
            varStages.add(new VarValue.VarStage(s, s));
        }
        return varStages;
    }

    @SuppressWarnings("all")
    private void resetDefinition(ServiceDefinition definition) {
        definition.setHeadNodeKeys(null);
        definition.setChildren(new ArrayList<>());
        definition.setInput(null);
        definition.setOutput(null);
    }

    private StateMachine convertStateMachine(Context context) {
        if (CollectionUtils.isEmpty(context.getEventMeta().getStates())) {
            return null;
        }

        StateMachine stateMachine = new StateMachine();
        stateMachine.setStateVerify(Boolean.TRUE.equals(context.getEventMeta().getEnabledStatusVerify()));
        stateMachine.setStates(context.getEventMeta().getStates().stream().map(state -> {
            StateMachine.StateConfig stateConfig = new StateMachine.StateConfig();
            stateConfig.setModelKey(state.getModelKey());
            stateConfig.setFieldKey(state.getFieldKey());
            stateConfig.setSourceState(state.getSourceState());
            stateConfig.setTargetState(state.getTargetState());
            stateConfig.setConditionGroup(convertConditionGroupForStateMachine(state.getConditions()));
            return stateConfig;
        }).collect(Collectors.toList()));
        return stateMachine;
    }

    private ConditionGroup convertConditionGroupForStateMachine(List<List<ConditionDTO>> conditions) {
        if (CollectionUtils.isEmpty(conditions)) {
            return null;
        }

        // 内层是且，外层是或
        List<Condition> newConditions = new ArrayList<>(conditions.size());
        for (List<ConditionDTO> dtos : conditions) {
            if (CollectionUtils.isEmpty(dtos)) {
                continue;
            }

            List<Condition> conditionList = new ArrayList<>(dtos.size());
            for (ConditionDTO dto : dtos) {
                ConditionLeaf conditionLeaf = new ConditionLeaf();
                conditionLeaf.setId(dto.getKey());

                // 左值
                LeftValueDTO left = dto.getLeft();
                if (ValueTypeEnum.MODEL.getValueType().equals(left.getValueType())) {
                    conditionLeaf.setLeftValue(convertVarValueFromModelVarForStateMachine(left.getModel()));
                } else if (ValueTypeEnum.FACTOR.getValueType().equals(left.getValueType())) {
                    conditionLeaf.setLeftValue(convertSysVarValue(left.getFactor()));
                }

                // 操作符
                conditionLeaf.setOperator(Operator.valueOf(dto.getOperator()));

                // 右值
                RightValueDTO right = dto.getRight();
                List<RightValueDTO> rights = dto.getRights();
                if (right != null) {
                    conditionLeaf.setRightValue(convertValueForStateMachine(right));
                } else if (CollectionUtils.isNotEmpty(rights)) {
                    conditionLeaf.setRightValues(rights.stream().map(this::convertValueForStateMachine).collect(Collectors.toList()));
                }

                conditionList.add(conditionLeaf);
            }

            ConditionGroup conditionGroup = new ConditionGroup();
            conditionGroup.setLogicOperator(LogicOperator.AND);
            conditionGroup.setConditions(conditionList);
            newConditions.add(conditionGroup);
        }

        ConditionGroup conditionGroup = new ConditionGroup();
        conditionGroup.setLogicOperator(LogicOperator.OR);
        conditionGroup.setConditions(newConditions);

        return conditionGroup;
    }

    private ConditionGroup convertConditionGroup(String expression, List<List<ConditionDTO>> conditions, Context context) {
        if (CollectionUtils.isEmpty(conditions)) {
            return null;
        }

        // 内层是且，外层是或
        List<Condition> newConditions = new ArrayList<>(conditions.size());
        for (List<ConditionDTO> dtos : conditions) {
            if (CollectionUtils.isEmpty(dtos)) {
                continue;
            }

            List<Condition> conditionList = new ArrayList<>(dtos.size());
            for (ConditionDTO dto : dtos) {
                ConditionLeaf conditionLeaf = new ConditionLeaf();
                conditionLeaf.setId(dto.getKey());

                // 左值
                LeftValueDTO left = dto.getLeft();
                if (ValueTypeEnum.MODEL.getValueType().equals(left.getValueType())) {
                    conditionLeaf.setLeftValue(convertVarValueFromModelVar(expression, left.getModel(), context));
                } else if (ValueTypeEnum.FACTOR.getValueType().equals(left.getValueType())) {
                    conditionLeaf.setLeftValue(convertSysVarValue(left.getFactor()));
                }

                // 操作符
                conditionLeaf.setOperator(Operator.valueOf(dto.getOperator()));

                // 右值
                RightValueDTO right = dto.getRight();
                List<RightValueDTO> rights = dto.getRights();
                if (right != null) {
                    conditionLeaf.setRightValue(convertValue(expression, right, context));
                } else if (CollectionUtils.isNotEmpty(rights)) {
                    conditionLeaf.setRightValues(rights.stream().map(r -> convertValue(expression, r, context)).collect(Collectors.toList()));
                }

                conditionList.add(conditionLeaf);
            }

            ConditionGroup conditionGroup = new ConditionGroup();
            conditionGroup.setLogicOperator(LogicOperator.AND);
            conditionGroup.setConditions(conditionList);
            newConditions.add(conditionGroup);
        }

        ConditionGroup conditionGroup = new ConditionGroup();
        conditionGroup.setLogicOperator(LogicOperator.OR);
        conditionGroup.setConditions(newConditions);

        return conditionGroup;
    }

    private Value convertValue(String expression, RightValueDTO right, Context context) {
        if (ValueTypeEnum.CONSTANT.getValueType().equals(right.getValueType())) {
            ConstValue constValue = new ConstValue();
            constValue.setFieldType(ModelMetaQuery.convertFieldType(right.getFieldType()));
            constValue.setConstValue(right.getValue());
            return constValue;
        } else if (ValueTypeEnum.FACTOR.getValueType().equals(right.getValueType())) {
            return convertSysVarValue(right.getFactor());
        } else if (ValueTypeEnum.MODEL.getValueType().equals(right.getValueType())) {
            return convertVarValueFromModelVar(expression, right.getModel(), context);
        }
        return null;
    }

    private Value convertValueForStateMachine(RightValueDTO right) {
        if (ValueTypeEnum.CONSTANT.getValueType().equals(right.getValueType())) {
            ConstValue constValue = new ConstValue();
            constValue.setFieldType(ModelMetaQuery.convertFieldType(right.getFieldType()));
            constValue.setConstValue(right.getValue());
            return constValue;
        } else if (ValueTypeEnum.FACTOR.getValueType().equals(right.getValueType())) {
            return convertSysVarValue(right.getFactor());
        } else if (ValueTypeEnum.MODEL.getValueType().equals(right.getValueType())) {
            return convertVarValueFromModelVarForStateMachine(right.getModel());
        }
        return null;
    }

    private VarValue convertSysVarValue(FactorValueDTO factor) {
        VarValue varValue = new VarValue();
        varValue.setValueType(ValueType.VAR);
        varValue.setFieldType(ModelMetaQuery.convertFieldType(factor.getFieldType()));
        varValue.setVarValue(Lists.newArrayList(
                new VarValue.VarStage(VariableType.SYS.getKey(), VariableType.SYS.getName()),
                new VarValue.VarStage(factor.getKey(), factor.getFactorName())
        ));
        return varValue;
    }

    private VarValue convertVarValueFromModelVar(String expression, ModelValueDTO model, Context context) {
        VarValue varValue = new VarValue();
        varValue.setValueType(ValueType.VAR);
        List<VarValue.VarStage> stages = new ArrayList<>();

        if (StringUtils.isBlank(expression)) {
            stages.add(new VarValue.VarStage(VariableType.REQUEST.getKey(), VariableType.REQUEST.getName()));
            stages.add(new VarValue.VarStage("request", "request"));
        } else {
            stages.addAll(convertVarValueFromExpress(expression, context));
        }

        if (model != null) {
            convertModelVarValueStages(model, stages);
        }
        varValue.setFieldType(stages.get(stages.size() - 1).getFieldType());
        varValue.setVarValue(stages);
        return varValue;
    }

    private VarValue convertVarValueFromModelVarForStateMachine(ModelValueDTO model) {
        VarValue varValue = new VarValue();
        varValue.setValueType(ValueType.VAR);
        List<VarValue.VarStage> stages = new ArrayList<>();
        if (model != null) {
            convertModelVarValueStages(model, stages);
        }
        varValue.setFieldType(stages.get(stages.size() - 1).getFieldType());
        varValue.setVarValue(stages);
        return varValue;
    }

    private void convertModelVarValueStages(ModelValueDTO model, List<VarValue.VarStage> stages) {
        VarValue.VarStage stage = new VarValue.VarStage(model.getFieldAlias(), model.getFieldName());
        stage.setFieldType(ModelMetaQuery.convertFieldType(model.getFieldType()));
        stage.setModelAlias(model.getModelAlias());
        if (model.getChildModel() != null) {
            stage.setRelatedModel(new RelatedModel(model.getChildModel().getModelKey(), model.getChildModel().getModelName()));
        }
        stages.add(stage);
        if (model.getChildModel() != null) {
            convertModelVarValueStages(model.getChildModel(), stages);
        }
    }

    private StartNode convertStartNode(Context context) {
        StartNode startNode = new StartNode();
        startNode.setKey(context.generateNodeKey());
        startNode.setName("开始");

        StartProperties properties = new StartProperties();
        properties.setInput(convertInput(context.getEventMeta().getModel(), context.getEventMeta().getModelArrayWhether()));
        properties.setOutput(convertOutput(context.getEventMeta().getReturnModel(), context.getEventMeta().getReturnModelArrayWhether()));
        startNode.setProps(properties);
        return startNode;
    }

    private List<io.terminus.trantor2.service.dsl.properties.Field> convertInput(EventModelDTO model, Boolean arrayWhether) {
        if (model != null && model.getKey() != null) {
            io.terminus.trantor2.service.dsl.properties.Field input = new ModelField("request",
                    new RelatedModel(model.getKey(), model.getName()));
            if (Boolean.TRUE.equals(arrayWhether)) {
                return Lists.newArrayList(new ArrayField("request", "request", input));
            } else {
                return Lists.newArrayList(input);
            }
        }
        return null;
    }

    private List<io.terminus.trantor2.service.dsl.properties.Field> convertOutput(EventModelDTO returnModel, Boolean arrayWhether) {
        io.terminus.trantor2.service.dsl.properties.Field output;
        if (returnModel != null && returnModel.getKey() != null) {
            output = new ModelField("data", new RelatedModel(returnModel.getKey(), returnModel.getName()));
        } else {
            output = new ObjectField("data");
        }
        if (Boolean.TRUE.equals(arrayWhether)) {
            return Lists.newArrayList(new ArrayField("data", output));
        } else {
            return Lists.newArrayList(output);
        }
    }

    private EndNode convertEndNode(Context context) {
        EndNode endNode = new EndNode();
        endNode.setProps(new EndProperties());
        endNode.setKey(context.generateNodeKey());
        endNode.setName("结束");
        return endNode;
    }

    private Propagation convertPropagation(Boolean enabledTransaction) {
        return Boolean.TRUE.equals(enabledTransaction) ? Propagation.REQUIRED : Propagation.NOT_SUPPORTED;
    }

    @Data
    @AllArgsConstructor
    public static class Context {
        private AtomicLong uuid;
        private EventDTO eventMeta;
        private ServiceMeta serviceMeta;
        private ResourceContext rscCtx;

        private final Map<String, String> rootErrorCodeKeyCache = new ConcurrentHashMap<>();

        public ServiceDefinition getDefinition() {
            return serviceMeta.getResourceProps().getServiceDslJson();
        }

        private String generateNodeKey() {
            if (uuid != null) {
                return String.format("node_%s", uuid.incrementAndGet());
            }
            String key = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 10);
            return String.format("node_%s", key);
        }

        public boolean hasOutput() {
            return CollectionUtils.isNotEmpty(serviceMeta.getResourceProps().getServiceDslJson().getOutput());
        }

        public String getEventRequestModelKey() {
            String modelKey = null;
            if (eventMeta.getModel() != null && StringUtils.isNotBlank(eventMeta.getModel().getKey())) {
                modelKey = eventMeta.getModel().getKey();
            }
            return modelKey;
        }

        public String getActionReturnModelKey(String actionCode) {
            String modelKey = null;
            EventActionRelNode sourceAction = eventMeta.getActions().stream().filter(t -> actionCode.equals(t.getCode()))
                    .findFirst().orElse(null);
            if (sourceAction != null && sourceAction.getReturnModel() != null && StringUtils.isNotBlank(sourceAction.getReturnModel().getKey())) {
                modelKey = sourceAction.getReturnModel().getKey();
            }
            return modelKey;
        }

        public String getActionName(String code) {
            String actionName = null;
            EventActionRelNode sourceAction = eventMeta.getActions().stream().filter(t -> code.equals(t.getCode()))
                    .findFirst().orElse(null);
            if (sourceAction != null) {
                actionName = sourceAction.getName();
            }
            return StringUtils.defaultIfBlank(actionName, code);
        }

        public String getActionNameOrNull(String code, Function<String, String> convert) {
            String actionName = null;
            EventActionRelNode sourceAction = eventMeta.getActions().stream().filter(t -> code.equals(convert.apply(t.getCode())))
                    .findFirst().orElse(null);
            if (sourceAction != null) {
                actionName = sourceAction.getName();
            }
            return actionName;
        }

        public String getRootErrorCodeKey(String moduleKey, Function<String, String> mappingFunction) {
            return rootErrorCodeKeyCache.computeIfAbsent(moduleKey, mappingFunction);
        }
    }
}
