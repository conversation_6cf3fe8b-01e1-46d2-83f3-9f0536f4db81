package io.terminus.trantor2.console.rest;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.console.api.vo.I18nLanguageType;
import io.terminus.trantor2.console.service.I18nManageService;
import io.terminus.trantor2.console.service.impl.I18nAITranslationService;
import io.terminus.trantor2.module.meta.I18nConfig;
import io.terminus.trantor2.module.meta.I18nLanguagePack;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Tag(name = "门户国际化配置管理")
@Slf4j
@RestController
@RequestMapping("/api/trantor/console/i18n")
@RequiredArgsConstructor
public class PortalI18nController {
    private final I18nManageService i18nManageService;
    private final I18nAITranslationService i18nAITranslationService;

    @Operation(summary = "开关国际化")
    @PostMapping("/switch")
    public Response<Void> switchI18n() {
        i18nManageService.switchI18n();
        return Response.ok();
    }

    @Operation(summary = "多语言配置")
    @GetMapping("/config")
    public Response<I18nConfig> config() {
        I18nConfig config = i18nManageService.getConfig();
        config.getLanguages().forEach(it -> it.setDownloadUrl(null));
        return Response.ok(config);
    }

    @Operation(summary = "添加语言")
    @PostMapping("/language/add")
    public Response<I18nLanguagePack> addLanguages(@RequestParam String key, @RequestParam String name) {
        return Response.ok(i18nManageService.addLanguage(key, name));
    }

    @Operation(summary = "重命名语言")
    @PostMapping("/language/rename")
    public Response<Void> rename(@RequestParam String key, @RequestParam String name) {
        i18nManageService.renameLanguage(key, name);
        return Response.ok();
    }

    @GetMapping("/language/types")
    @Operation(summary = "语言类型")
    public Response<List<I18nLanguageType>> i18nEnums() {
        return Response.ok(i18nManageService.languageTypes());
    }

    @Operation(summary = "上传语言包")
    @PostMapping("/language/pack/{key}")
    public Response<Void> upload(@PathVariable String key, @RequestParam("file") MultipartFile languagePack) {
        i18nManageService.uploadLanguagePack(key, languagePack);
        return Response.ok();
    }

    @Operation(summary = "下载语言包")
    @PostMapping("/language/pack/download/{key}")
    public ResponseEntity<ByteArrayResource> download(@PathVariable String key) {
        Map.Entry<String, ByteArrayResource> file = i18nManageService.downloadLanguagePack(key);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + file.getKey());

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(file.getValue());
    }

    @Operation(summary = "下载智能翻译语言包")
    @PostMapping("/language/pack/translate/download/{key}")
    public ResponseEntity<ByteArrayResource> downloadAITranslate(@PathVariable String key) {
        Map.Entry<String, ByteArrayResource> file = i18nAITranslationService.downloadLanguagePack(key);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + file.getKey());

        return ResponseEntity.ok()
                             .headers(headers)
                             .contentType(MediaType.APPLICATION_OCTET_STREAM)
                             .body(file.getValue());
    }

    @Operation(summary = "异步智能翻译语言包")
    @PostMapping("/language/pack/translate/{key}")
    public Response<String> aiTranslate(@PathVariable String key) {
        TrantorContext.Context context = TrantorContext.copy();
        Map.Entry<String, ByteArrayResource> file = i18nManageService.downloadLanguagePack(key);
        // 还原被清理的上下文
        if (Objects.isNull(TrantorContext.getContext())) {
            TrantorContext.init();
            TrantorContext.setContext(context);
        }
        String i18nContent = new String(file.getValue().getByteArray(), StandardCharsets.UTF_8);
        String translateKey = i18nAITranslationService.aiTranslation(key, i18nContent);
        TrantorContext.clear();
        return Response.ok(translateKey);
    }

    @GetMapping("/language/pack/translate-progress/{key}")
    @Operation(summary = "智能翻译进度")
    public Response<Double> aiTranslateProgress(@PathVariable String key) {
        Double currentTranslateProgress = i18nAITranslationService.aiTranslationProgress(key);
        return Response.ok(currentTranslateProgress);
    }

    @PostMapping("/language/delete/{key}")
    @Operation(summary = "删除语言")
    public Response<Void> deleteLanguages(@PathVariable String key) {
        i18nManageService.deleteLanguage(key);
        return Response.ok();
    }

    @PostMapping("/language/enabled/{key}")
    @Operation(summary = "启用语言")
    public Response<Void> enabledLanguages(@PathVariable String key) {
        i18nManageService.enabledLanguage(key);
        return Response.ok();
    }

    @PostMapping("/language/disabled/{key}")
    @Operation(summary = "禁用语言")
    public Response<Void> disabledLanguages(@PathVariable String key) {
        i18nManageService.disabledLanguage(key);
        return Response.ok();
    }
}
