package io.terminus.trantor2.console.rest;

import cn.hutool.core.text.CharSequenceUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.iam.api.response.PageResult;
import io.terminus.iam.api.response.webhook.*;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.iam.dto.webhook.WebhookDebugRequest;
import io.terminus.trantor2.iam.dto.webhook.WebhookHistoryPagingRequest;
import io.terminus.trantor2.iam.dto.webhook.WebhookPagingRequest;
import io.terminus.trantor2.iam.dto.webhook.WebhookSaveRequest;
import io.terminus.trantor2.console.service.TrantorWebhookService;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.service.ConfigurationService;
import io.terminus.trantor2.module.service.ModuleManageService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Tag(name = "webhook管理")
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/trantor/console/webhook")
public class TrantorWebhookController {

    private final TrantorWebhookService trantorWebhookService;
    private final ModuleManageService moduleService;
    private final ConfigurationService configurationService;

    @GetMapping("/paging")
    @Operation(summary = "分页查询Webhook信息")
    public Response<PageResult<WebhookResult>> pagingWebhook(WebhookPagingRequest params) {
        Long iamEndpointIdByPortalCode = getIamEndpointIdByPortalCode(params.getPortalCode());
        params.setAppId(iamEndpointIdByPortalCode);
        return Response.ok(trantorWebhookService.pagingWebhook(params));
    }

    @GetMapping("/paging-history")
    @Operation(summary = "分页查询Webhook记录")
    public Response<PageResult<WebhookHistoryResult>> pagingWebhookHistory(WebhookHistoryPagingRequest params) {
        Long iamEndpointIdByPortalCode = getIamEndpointIdByPortalCode(params.getPortalCode());
        params.setAppId(iamEndpointIdByPortalCode);
        return Response.ok(trantorWebhookService.pagingWebhookHistory(params));
    }

    @PostMapping("/save")
    @Operation(summary = "保存Webhook信息")
    public Response<WebhookResult> saveWebhook(@RequestBody WebhookSaveRequest params) {
        Long iamEndpointIdByPortalCode = getIamEndpointIdByPortalCode(params.getPortalCode());
        params.setAppId(iamEndpointIdByPortalCode);
        return Response.ok(trantorWebhookService.saveWebhook(params));
    }

    @PostMapping("/debug")
    @Operation(summary = "调试Webhook")
    public Response<String> webhookDebug(@RequestBody WebhookDebugRequest params) {
        Long iamEndpointIdByPortalCode = getIamEndpointIdByPortalCode(params.getPortalCode());
        params.setAppId(iamEndpointIdByPortalCode);
        return Response.ok(trantorWebhookService.webhookDebug(params));
    }

    @GetMapping("/all-events-type")
    @Operation(summary = "所有WebhookType事件分组")
    public Response<List<WebhookTypeResult>> allWebhookEventType() {
        return Response.ok(trantorWebhookService.allWebhookEventType());
    }

    @GetMapping("/{id}/find")
    @Operation(summary = "通过id查询Webhook信息")
    public Response<WebhookResult> findWebhookById(@PathVariable Long id) {
        return Response.ok(trantorWebhookService.findWebhookById(id));
    }

    @PostMapping("/{id}/enable")
    @Operation(summary = "通过id启用Webhook")
    public Response<Boolean> webhookEnable(@PathVariable Long id) {
        return Response.ok(trantorWebhookService.webhookEnable(id));
    }

    @PostMapping("/{id}/disable")
    @Operation(summary = "通过id禁用Webhook")
    public Response<Boolean> webhookDisable(@PathVariable Long id) {
        return Response.ok(trantorWebhookService.webhookDisable(id));
    }

    @PostMapping("/{id}/delete")
    @Operation(summary = "通过id删出Webhook")
    public Response<Boolean> webhookDelete(@PathVariable Long id) {
        return Response.ok(trantorWebhookService.webhookDelete(id));
    }

    @PostMapping("/{historyId}/history-retry")
    @Operation(summary = "通过historyId重试")
    public Response<Boolean> historyRetry(@PathVariable Long historyId) {
        return Response.ok(trantorWebhookService.historyRetry(historyId));
    }

    @GetMapping("/all-events")
    @Operation(summary = "获取事件列表")
    public Response<List<WebhookEventResult>> allWebhookEvent() {
        return Response.ok(trantorWebhookService.allWebhookEvent());
    }

    private Long getIamEndpointIdByPortalCode(String portalCode) {
        if (CharSequenceUtil.isEmpty(portalCode)) {
            return null;
        }
        ModuleMeta moduleMeta = moduleService.findByKey(portalCode);
        if (!moduleMeta.getTeamCode().equals(TrantorContext.getTeamCode())) {
            throw new TrantorRuntimeException("It is forbidden to operate other team webhook");
        }
        return configurationService.getIamEndpointId(moduleMeta.getTeamId(), moduleMeta.getKey());
    }
}
