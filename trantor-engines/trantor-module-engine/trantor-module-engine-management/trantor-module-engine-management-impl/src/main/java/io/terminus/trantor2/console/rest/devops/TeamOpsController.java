package io.terminus.trantor2.console.rest.devops;

import io.terminus.iam.api.request.role.RoleByKeyFindParams;
import io.terminus.iam.api.request.role.RoleCreateParams;
import io.terminus.iam.api.request.role.UserRoleRelationCreateParams;
import io.terminus.iam.api.request.user.UserPagingParams;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.console.service.impl.TrantorTeamServiceImpl;
import io.terminus.trantor2.iam.service.TrantorIAMApplicationService;
import io.terminus.trantor2.iam.service.TrantorIAMRoleRelationService;
import io.terminus.trantor2.iam.service.TrantorIAMRoleService;
import io.terminus.trantor2.iam.service.TrantorIAMUserService;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.management.service.EditorMetaEditService;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.entity.FuncSwitches;
import io.terminus.trantor2.module.entity.TrantorTeamEntity;
import io.terminus.trantor2.module.repository.TrantorTeamRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

import static io.terminus.trantor2.console.service.impl.TrantorTeamServiceImpl.TEAM_MEMBER_MANAGEMENT_ROLE;
import static io.terminus.trantor2.console.service.impl.TrantorTeamServiceImpl.TEAM_MEMBER_MANAGEMENT_ROLE_TYPE;

/**
 * @author: yangyuqiang
 * @date: 2023/9/7 1:56 PM
 **/
@Slf4j
@Tag("team维护接口")
@RestController
@AllArgsConstructor
@RequestMapping(path = "/api/trantor/devops/team")
public class TeamOpsController {
    private final TrantorTeamRepository teamRepository;
    private final TrantorIAMRoleService trantorIAMRoleService;
    private final TrantorIAMApplicationService trantorIAMApplicationService;
    private final TrantorIAMRoleRelationService trantorIAMRoleRelationService;
    private final TrantorIAMUserService trantorIAMUserService;
    private final MetaQueryService queryService;
    private final EditorMetaEditService editService;
    private final TrantorTeamServiceImpl teamService;

    @PostMapping("/init-role")
    public void initRole() {
        mockContext();
        Long iamApplicationId = trantorIAMApplicationService.getCurrentApplication();
        teamRepository.findAll().forEach(team -> {
            Long teamRoleId = findTeamRoleId(team.getCode(), iamApplicationId);
            if (teamRoleId != null) {
                return;
            }
            RoleCreateParams params = new RoleCreateParams();
            params.setSourceApplicationId(iamApplicationId);
            params.setKey(team.getCode() + TEAM_MEMBER_MANAGEMENT_ROLE);
            params.setName(team.getCode() + "团队管理角色");
            params.setRoleTypeKey(TEAM_MEMBER_MANAGEMENT_ROLE_TYPE);
            trantorIAMRoleService.createRole(params);
        });
    }

    /**
     * 初始化 Console 项目成员
     *
     * @param userPagingParams 用户查询参数，可以指定符合查询条件的用户成为项目成员，未指定表示所有用户都成为项目成员
     */
    @PostMapping("/init-member")
    public void initMember(@RequestBody UserPagingParams userPagingParams) {
        mockContext();
        List<Long> roleIds = new LinkedList<>();
        Long iamApplicationId = trantorIAMApplicationService.getCurrentApplication();
        teamRepository.findAll().forEach(team -> {
            Long teamRoleId = findTeamRoleId(team.getCode(), iamApplicationId);
            if (teamRoleId == null) {
                RoleCreateParams params = new RoleCreateParams();
                params.setSourceApplicationId(iamApplicationId);
                params.setKey(team.getCode() + TEAM_MEMBER_MANAGEMENT_ROLE);
                params.setName(team.getCode() + "团队管理角色");
                params.setRoleTypeKey(TEAM_MEMBER_MANAGEMENT_ROLE_TYPE);
                teamRoleId = trantorIAMRoleService.createRole(params);
            }
            roleIds.add(teamRoleId);
        });
        trantorIAMUserService.findAll(userPagingParams).stream().map(User::getId).forEach(userId -> {
            UserRoleRelationCreateParams params = new UserRoleRelationCreateParams();
            params.setEndpointId(iamApplicationId);
            params.setRoleIds(roleIds);
            params.setUserId(userId);
            trantorIAMRoleRelationService.createUserRoleRelation(params);
        });

    }

    @PostMapping("/init-sys")
    public void initSystem(@RequestParam Long userId) {
        User user = new User();
        user.setId(userId);
        TrantorContext.setCurrentUser(user);
        TrantorTeamEntity team = teamRepository.findByCode(KeyUtil.SYS_TEAM_KEY);
        if (Objects.isNull(team)) {
            teamService.initSystemTeam();
        }
    }

    @PostMapping("/branch-func-switch")
    public void branchFuncSwitch(@RequestParam String teamCode) {
        User user = new User();
        user.setId(1L);
        TrantorContext.setCurrentUser(user);
        TrantorTeamEntity team = teamRepository.findByCode(teamCode);
        FuncSwitches funcSwitches = team.getFuncSwitches();
        funcSwitches.setBranchEnable(!funcSwitches.getBranchEnable());
        teamRepository.save(team);
    }

    @PostMapping("/enhance-func-switch")
    public void enhanceFuncSwitch(@RequestParam String teamCode) {
        User user = new User();
        user.setId(1L);
        TrantorContext.setCurrentUser(user);
        TrantorTeamEntity team = teamRepository.findByCode(teamCode);
        FuncSwitches funcSwitches = team.getFuncSwitches();
        funcSwitches.setEnhanceEnable(!funcSwitches.getEnhanceEnable());
        teamRepository.save(team);
    }

    private void mockContext() {
        User user = new User();
        user.setId(1L);
        TrantorContext.setCurrentUser(user);
    }

    private Long findTeamRoleId(String teamCode, Long iamEndpointId) {
        RoleByKeyFindParams roleByKeyFindParams = new RoleByKeyFindParams();
        roleByKeyFindParams.setKey(teamCode + TEAM_MEMBER_MANAGEMENT_ROLE);
        roleByKeyFindParams.setSourceApplicationId(iamEndpointId);
        try {
            return trantorIAMRoleService.findRoleByKey(roleByKeyFindParams).getId();
        } catch (Exception e) {
            return null;
        }
    }
}
