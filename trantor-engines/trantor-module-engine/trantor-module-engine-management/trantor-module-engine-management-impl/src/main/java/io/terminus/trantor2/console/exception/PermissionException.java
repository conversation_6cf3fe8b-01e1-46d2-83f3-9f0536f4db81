package io.terminus.trantor2.console.exception;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorBizException;

/**
 * <AUTHOR>
 */
public class PermissionException extends TrantorBizException {

    private static final long serialVersionUID = 1870858080152836619L;

    public PermissionException(ErrorType errorType) {
        super(errorType);
    }

    public PermissionException(ErrorType errorType, Object[] args) {
        super(errorType, args);
    }

}
