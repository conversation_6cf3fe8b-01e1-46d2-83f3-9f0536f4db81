package io.terminus.trantor2.console.listener.handler;

import io.terminus.trantor2.module.meta.MenuMeta;

import java.util.List;

/**
 * <AUTHOR>
 * 2022/10/27 4:10 下午
 */
public interface MenuEventHandler {
    /**
     * afterCreateMenu
     *
     * @param menu
     */
    void afterCreateMenu(MenuMeta menu);

    /**
     * beforeDeleteMenu
     *
     * @param menu
     */
    void beforeDeleteMenu(MenuMeta menu);

    /**
     * unbind route
     */
    void unbindRoute(String teamCode, String sceneKey);

    /**
     * bind route changed
     */
    void update(MenuMeta before, MenuMeta after);

    /**
     * route resource changed, rebuild route permission
     *
     * @param menus      菜单集合
     * @param needDelete 是否需要删除权限
     */
    void updatePermission(List<MenuMeta> menus, boolean needDelete);
}
