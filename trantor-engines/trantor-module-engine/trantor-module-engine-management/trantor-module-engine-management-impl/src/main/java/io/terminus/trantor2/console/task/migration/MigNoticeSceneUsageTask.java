package io.terminus.trantor2.console.task.migration;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.blob.MetaBlob;
import io.terminus.trantor2.meta.blob.MetaBlobRepo;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.service.common.meta.ServiceMeta;
import io.terminus.trantor2.service.dsl.HasChildrenNode;
import io.terminus.trantor2.service.dsl.NoticeNode;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.dsl.ServiceElement;
import io.terminus.trantor2.service.dsl.properties.NoticeProperties;
import io.terminus.trantor2.service.dsl.properties.StringEntry;
import io.terminus.trantor2.service.management.repo.ServiceRepo;
import io.terminus.trantor2.task.TaskOutput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Since 24.0630 version
 *
 * <AUTHOR>
 */
@Component
public class MigNoticeSceneUsageTask extends BaseTask<MigNoticeSceneUsageTask.Options> {

    @Autowired
    private MetaBlobRepo metaBlobRepo;

    @Autowired
    private ServiceRepo serviceRepo;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
    }

    @Override
    public void exec(Options opts, TaskOutput output, TaskContext ctx) {
        List<MetaBlob.Base> all = metaBlobRepo.findAll(ctx.getTeamId(), MetaBlob.Base.class);
        for (MetaBlob.Base node : all) {
            if (!ImmutableSet.of(
                MetaType.ServiceDefinition.name(),
                MetaType.WorkflowGroup.name()
            ).contains(node.getType())) {
                continue;
            }

            if (MetaType.ServiceDefinition.name().equals(node.getType())) {
                try {
                    changeForServiceDefinition(node, ctx);
                } catch (Exception e) {
                    System.out.println("AAA");
                }
            }
        }
    }

    private void changeForServiceDefinition(MetaBlob.Base node, TaskContext ctx) {
        Optional<ServiceMeta> serviceMetaOpt = serviceRepo.findOneByKey(node.getKey(), ResourceContext.newResourceCtx(ctx.getTeamCode(), ctx.getUserId()));
        if (!serviceMetaOpt.isPresent()) {
            return;
        }

        ServiceMeta serviceMeta = serviceMetaOpt.get();
        ServiceDefinition serviceDefinition = serviceMeta.getResourceProps().getServiceDslJson();
        if (serviceDefinition == null || CollectionUtils.isEmpty(serviceDefinition.getChildren())) {
            return;
        }
        boolean isNeedUpdate = false;
        for (ServiceElement<?> serviceNode : serviceDefinition.getChildren()) {
            isNeedUpdate = isNeedUpdate || findNoticeNode(serviceNode);
        }

        if (isNeedUpdate) {
            serviceRepo.update(serviceMeta, ResourceContext.newResourceCtx(ctx.getTeamCode(), ctx.getUserId()));
        }
    }

    private Boolean findNoticeNode(ServiceElement serviceNode) {
        boolean isNeedUpdate = false;
        if (serviceNode == null) {
            return false;
        }
        if (serviceNode instanceof NoticeNode) {
            NoticeProperties properties = ((NoticeNode) serviceNode).getProps();
            if (KeyUtil.isInvalidKey(properties.getNoticeSceneCode())) {
                properties.setNoticeSceneCode(KeyUtil.newKeyUnderModule("ERP_GEN", properties.getNoticeSceneCode()));
            } else {
                return false;
            }
            Map<String, List<StringEntry>> placeholderMapping = properties.getPlaceholderMapping();
            if (MapUtils.isNotEmpty(placeholderMapping)) {
                List<StringEntry> stringEntries = placeholderMapping.values().stream()
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(stringEntries)) {
                    return isNeedUpdate;
                }

                List<StringEntry> inputMapping = properties.getInputMapping();
                if (CollectionUtils.isEmpty(inputMapping)) {
                    // 去重
                    List<StringEntry> newStringEntries = Lists.newArrayList();
                    Set<String> key = Sets.newHashSet();
                    stringEntries.forEach(stringEntry -> {
                        if (key.contains(stringEntry.getKey())) {
                            return;
                        }
                        newStringEntries.add(stringEntry);
                        key.add(stringEntry.getKey());
                    });
                    properties.setInputMapping(newStringEntries);
                } else {
                    Set<String> existKeys = inputMapping.stream()
                        .map(StringEntry::getKey)
                        .collect(Collectors.toSet());
                    stringEntries.forEach(stringEntry -> {
                        if (existKeys.contains(stringEntry.getKey())) {
                            return;
                        }
                        inputMapping.add(stringEntry);
                        existKeys.add(stringEntry.getKey());
                    });
                }
                isNeedUpdate = true;
            }
        } else if (serviceNode instanceof HasChildrenNode) {
            if (CollectionUtils.isNotEmpty(((HasChildrenNode<?>) serviceNode).getChildren())) {
                for (ServiceElement<?> childNode : ((HasChildrenNode<?>) serviceNode).getChildren()) {
                    isNeedUpdate = isNeedUpdate || findNoticeNode(childNode);
                }
            }
        }
        return isNeedUpdate;
    }
}

