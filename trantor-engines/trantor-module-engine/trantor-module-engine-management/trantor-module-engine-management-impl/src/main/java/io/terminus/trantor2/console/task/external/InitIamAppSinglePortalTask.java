package io.terminus.trantor2.console.task.external;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.console.listener.handler.MenuEventHandler;
import io.terminus.trantor2.ide.repository.ApiMetaRepo;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.exception.MetaTaskException;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskDefine;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.meta.*;
import io.terminus.trantor2.module.repository.ModuleRepo;
import io.terminus.trantor2.module.service.ConfigurationService;
import io.terminus.trantor2.module.service.MenuConsoleQueryService;
import io.terminus.trantor2.module.service.ModuleManageService;
import io.terminus.trantor2.permission.ApiMeta;
import io.terminus.trantor2.permission.management.api.service.AccessControlManageService;
import io.terminus.trantor2.permission.management.api.service.IPermissionService;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public final class InitIamAppSinglePortalTask extends BaseTask<InitIamAppSinglePortalTask.Options> {
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
        private String portalKey;
    }

    @Autowired
    private ConfigurationService configurationService;

    @Autowired
    private ModuleManageService moduleService;

    @Autowired
    private ModuleRepo moduleRepo;

    @Autowired
    private MenuConsoleQueryService menuQueryService;

    @Autowired
    private IPermissionService permissionService;

    @Autowired
    private List<MenuEventHandler> menuEventHandlerList;

    @Autowired
    private ApiMetaRepo apiMetaRepo;

    @Autowired
    private AccessControlManageService accessControlManageService;

    @Override
    public void preCheck(Options opts, TaskContext ctx) {
        super.preCheck(opts, ctx);
        if (opts.getPortalKey() == null) {
            throw new RuntimeException("portalKey is required");
        }
    }

    @Override
    public void exec(Options opts, List<TaskDefine> subTasks, List<String> outputs, TaskContext ctx) {
        ResourceContext resCtx = ResourceContext.newResourceCtx(ctx.getTeamCode(), ctx.getUserId());
        ModuleMeta portal = moduleRepo.findOneByKey(opts.getPortalKey(), resCtx).orElseThrow(() -> new MetaTaskException("portalKey not found"));
        if (!Objects.equals(portal.getResourceProps().getType(), ModuleType.Portal)) {
            throw new MetaTaskException("portalKey is not a portal");
        }
        if (configurationService.query(ctx.getTeamId(), opts.getPortalKey(), ConfigType.Module_IAM) != null) {
            log.info("[InitIamAppSinglePortalTask] iam config already exists, skip, moduleKey: {}", portal.getKey());
        } else {
            moduleService.createIamConfig(portal);
            log.info("[InitIamAppSinglePortalTask] init iam config finish, moduleKey: {}", portal.getKey());
        }

        // sync permissions for portal menus and views
        initMenuPermission(portal, outputs);

        // sync api access controls
        log.info("Start syncing permissions for portal api-access-control");
        List<ApiMeta> apiMetas = apiMetaRepo.getPortalApiMetas(portal.getKey());
        if (CollectionUtils.isNotEmpty(apiMetas)) {
            accessControlManageService.syncApiMetaToIAM(portal.getKey(), apiMetas);
        }
        log.info("Finish syncing, apiMetas: {}", JsonUtil.toJson(apiMetas));
    }

    private void initMenuPermission(ModuleMeta module, List<String> outputs) {
        if (TrantorContext.getContext() == null) {
            TrantorContext.init();
            TrantorContext.setTeamId(module.getTeamId());
            TrantorContext.setTeamCode(module.getTeamCode());
        }
        TrantorContext.setModuleId(module.getId());

        try {
            initModuleMenuPermission(module, outputs);
            log.info("[InitIamAppSinglePortalTask] init menu permission finish, moduleKey: {}", module.getKey());
        } finally {
            TrantorContext.setModuleId(null); // clear
        }
    }

    private void initModuleMenuPermission(ModuleMeta module, List<String> outputs) {
        log.info("Start syncing permission for all portal menu and ref scene view");
        List<MenuMeta> menus = menuQueryService.flatMenuTreeNodesByModule(module.getKey());
        // sync permission for v2
        menuEventHandlerList.forEach(handler -> handler.updatePermission(menus, false));
        log.info("Finish syncing, menus: {}", JsonUtil.toJson(menus));
        // sync permission for v1
//        permissionService.createPermissionGroup(module);
//        List<String> errors = permissionService.syncModuleFunctionPermissions(module, menus);
//        outputs.addAll(errors);
    }
}
