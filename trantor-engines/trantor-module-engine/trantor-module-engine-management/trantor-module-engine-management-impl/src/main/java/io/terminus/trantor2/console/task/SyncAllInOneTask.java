package io.terminus.trantor2.console.task;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.terminus.trantor2.console.task.external.ExternalAllSync;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.MoveTarget;
import io.terminus.trantor2.meta.api.dto.MoveTargetType;
import io.terminus.trantor2.meta.api.dto.PathFilter;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.exception.MetaTaskException;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskDefine;
import io.terminus.trantor2.meta.management.task.TaskService;
import io.terminus.trantor2.meta.management.task.primitive.FixMetaTask;
import io.terminus.trantor2.meta.management.task.primitive.RebuildIndexTask;
import io.terminus.trantor2.meta.management.util.UploadUtil;
import io.terminus.trantor2.meta.util.ZipFileUtil;
import io.terminus.trantor2.meta.api.dto.Manifest;
import io.terminus.trantor2.meta.task.ResetIndexTask;
import io.terminus.trantor2.meta.task.SnapshotTask;
import io.terminus.trantor2.meta.task.SyncObjectTask;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.properties.TrantorTaskProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@TaskService(displayName = "元数据全量同步", visible = true)
public final class SyncAllInOneTask extends BaseTask<SyncAllInOneTask.Options> {
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
        /**
         * current platform recover
         */
        private String snapshotOid;
        /**
         * for {@link SyncObjectTask} sub-task
         */
        private String downloadUrl;

        /**
         * for {@link ResetIndexTask} sub-task
         */
        @JsonAlias({"resetModuleKeys", "moduleKeys"})
        private List<String> resetModuleKeys;

        /**
         * if path is not empty, moduleKeys must only one item
         */
        private String path;

        /**
         * for {@link ModulePostInstallTask} sub-task
         */
        @Deprecated
        private Boolean resetModuleForInstall = Boolean.FALSE;

        /**
         * for {@link ExternalAllSync} sub-task
         */
        @JsonAlias({"ddlAutoUpdate", "autoDDL"})
        private Boolean ddlAutoUpdate = Boolean.FALSE;

        private Boolean esIndexEnabled = Boolean.TRUE;
        private Boolean permAuthViewEnabled = Boolean.TRUE;  // 是否重新构建权限授权视图
        private Boolean iamSyncEnabled = Boolean.TRUE;  // 是否执行iam权限同步

        private String securityCode;
    }

    @Autowired
    private TrantorTaskProperties trantorTaskProperties;

    @Override
    public void preCheck(Options opts, TaskContext ctx) {
        super.preCheck(opts, ctx);
        if (CollectionUtils.isEmpty(opts.getResetModuleKeys())) {
            // sync all need to check security code
            Map<String, String> securityCodes = trantorTaskProperties.getSecurityCodes();
            if (!CollectionUtils.isEmpty(securityCodes) && securityCodes.containsKey(ctx.getTeamCode())) {
                String securityCode = securityCodes.get(ctx.getTeamCode());
                if (!StringUtils.hasText(securityCode) || !securityCode.equals(opts.getSecurityCode())) {
                    throw new MetaTaskException("security code not match");
                }
            }
        }
        if (ctx.getParentTaskDef() == null) {
            // which means this task is root task
            // TODO: check exactly parent task
            if (opts.getResetModuleForInstall()) {
                throw new MetaTaskException("resetModuleForInstall is not allowed, please use InstallAndUpgradeAppTask");
            }
        }

        // if path is given, moduleKeys must only one item
        if (StringUtils.hasText(opts.getPath()) && (opts.getResetModuleKeys() == null || opts.getResetModuleKeys().size() != 1)) {
            throw new MetaTaskException("path is not empty, moduleKeys must only one item");
        }
    }

    @Override
    public void exec(Options opts, List<TaskDefine> subTasks, List<String> outputs, TaskContext ctx) {
        // step0: parse/validate artifact
        String oid = opts.getSnapshotOid();
        java.nio.file.Path local = null;
        boolean isPatch = false;
        if (StringUtils.hasText(opts.getDownloadUrl())) {
            local = ZipFileUtil.downloadFromURL(opts.getDownloadUrl());
            Manifest manifest = ZipFileUtil.readManifest(local);
            String pkgType = manifest == null ? null : manifest.getPackageType();
            // Treat as PATCH only when explicitly marked; otherwise default to FULL-like flow
            isPatch = Manifest.PACKAGE_TYPE_PATCH.equals(pkgType);
            // No strict validation on package type; allow legacy or unknown types and proceed
            oid = manifest == null ? null : manifest.getSnapshotOid();
            if (!StringUtils.hasText(oid)) {
                oid = UploadUtil.parseOidFromUrl(opts.getDownloadUrl());
            }
        }
        if (!StringUtils.hasText(oid)) {
            throw new MetaTaskException("oid not found in downloadUrl");
        }

        // step0.1: snapshot before sync
        {
            SnapshotTask.Options snapshotOpts = new SnapshotTask.Options();
            subTasks.add(
                new TaskDefine(SnapshotTask.class, snapshotOpts, "", true)
            );
        }

        // step1: sync object
        {
            SyncObjectTask.Options syncObjectOpts = new SyncObjectTask.Options();
            if (local != null) {
                syncObjectOpts.setLocalPath(local.toString());
                syncObjectOpts.setDeleteWhenFinish(Boolean.TRUE);
            } else {
                syncObjectOpts.setDownloadUrl(opts.getDownloadUrl());
            }
            subTasks.add(
                new TaskDefine(SyncObjectTask.class, syncObjectOpts, "", true)
            );
        }
        // step2: reset index
        if (isPatch) {
            // Patch package: reset by keys from patch.json
            List<String> metaKeys = ZipFileUtil.readMetaKeys(local);
            if (CollectionUtils.isEmpty(metaKeys)) {
                throw new MetaTaskException("metaKeys not found in patch package");
            }
            ResetIndexTask.Options resetIndexOpts = new ResetIndexTask.Options();
            resetIndexOpts.setRootOid(oid);
            resetIndexOpts.setResetKeys(metaKeys);
            subTasks.add(new TaskDefine(ResetIndexTask.class, resetIndexOpts, "", true));
        } else {
            final Set<String> moduleKeys = new HashSet<>();
            final MoveTarget target;
            if (CollectionUtils.isEmpty(opts.getResetModuleKeys())) {
                target = null;
            } else {
                for (String moduleKey : opts.getResetModuleKeys()) {
                    if (StringUtils.hasText(moduleKey)) {
                        moduleKeys.add(moduleKey);
                    }
                }
                MetaTreeNodeExt root = metaQueryService.queryInTeam(ctx.getTeamId()).findOne(Field.type().equal(MetaType.Root.name()))
                    .orElseThrow(() -> new MetaTaskException("root not found"));
                if (root.getKey() == null) {
                    throw new MetaTaskException("root key is null");
                }
                target = new MoveTarget(root.getKey(), MoveTargetType.ChildLast);
            }

            ResetIndexTask.Options resetIndexOpts = new ResetIndexTask.Options();
            resetIndexOpts.setRootOid(oid);
            if (!moduleKeys.isEmpty()) {
                List<String> resetModules = new ArrayList<>(moduleKeys);
                if (!opts.getResetModuleForInstall()) {
                    if (StringUtils.hasText(opts.getPath())) {
                        resetIndexOpts.setResetPaths(new ArrayList<>());
                        resetIndexOpts.getResetPaths().add(new PathFilter(moduleKeys.stream().findFirst().get(), opts.getPath()));
                    }
                    // non-install mode should care ExtModule
                    moduleKeys.stream().map(KeyUtil::extKey).forEach(resetModules::add);
                }
                //reset module
                resetIndexOpts.setResetModules(resetModules);
            }
            resetIndexOpts.setResetOnTarget(target);
            subTasks.add(
                    new TaskDefine(ResetIndexTask.class, resetIndexOpts, "", true)
            );
            for (String moduleKey : moduleKeys) {
                if (StringUtils.hasText(moduleKey) && opts.getResetModuleForInstall()) {
                    ModulePostInstallTask.Options modulePostInstallOpts = new ModulePostInstallTask.Options();
                    modulePostInstallOpts.setModuleKey(moduleKey);
                    modulePostInstallOpts.setSnapshotOid(oid);
                    subTasks.add(
                        new TaskDefine(ModulePostInstallTask.class, modulePostInstallOpts, "", true)
                    );
                }
            }
        }

        // step2.4: TEMP fix Meta
        {
            FixMetaTask.Options fixMetaOpts = new FixMetaTask.Options();
            subTasks.add(
                new TaskDefine(FixMetaTask.class, fixMetaOpts, "", true)
            );
        }

        // step2.5: snapshot after sync
        {
            SnapshotTask.Options snapshotOpts = new SnapshotTask.Options();
            subTasks.add(
                new TaskDefine(SnapshotTask.class, snapshotOpts, "", true)
            );
        }

        // step2.6: rebuild index
        {
            RebuildIndexTask.Options rebuildIndexOpts = new RebuildIndexTask.Options();
            subTasks.add(
                new TaskDefine(RebuildIndexTask.class, rebuildIndexOpts, "", true)
            );
        }

        // step3: external all sync
        ExternalAllSync.Options externalAllSyncOpts = new ExternalAllSync.Options();
        externalAllSyncOpts.setDdlAutoUpdate(opts.getDdlAutoUpdate());
        externalAllSyncOpts.setEsIndexEnabled(opts.getEsIndexEnabled());
        externalAllSyncOpts.setPermAuthViewEnabled(opts.getPermAuthViewEnabled());
        externalAllSyncOpts.setIamSyncEnabled(opts.getIamSyncEnabled());
        // For PATCH, leave modules empty; for FULL, modules were handled above and not tracked here, so pass empty as well to cover cross-module changes when needed
        externalAllSyncOpts.setModules(new ArrayList<>());
        subTasks.add(
            new TaskDefine(ExternalAllSync.class, externalAllSyncOpts, "", true)
        );
    }
}
