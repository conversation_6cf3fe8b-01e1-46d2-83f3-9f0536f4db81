package io.terminus.trantor2.console.job;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.console.util.IAMUtil;
import io.terminus.trantor2.module.meta.ConfigType;
import io.terminus.trantor2.module.service.ConfigurationService;
import io.terminus.trantor2.module.service.TeamService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 **/
@Slf4j
@Component
@AllArgsConstructor
@ConditionalOnProperty(name = "trantor2.task.migration.simple-job.enabled", havingValue = "true")
public class IamApplicationKeyMigrationJob  implements CommandLineRunner {
    private final TeamService teamService;
    private final ConfigurationService configurationService;

    @Override
    public void run(String... args) throws Exception {
        for (Long teamId : teamService.getAllTeamIds()) {
            String teamCode = teamService.getTeamCode(teamId);
            initCtx(teamId, teamCode, 1L);
            configurationService.queryAllModuleIamConfig(teamId).forEach((k, v) -> {
                String appKey = IAMUtil.jointIAMAppKey(k, teamCode);
                if (!Objects.equals(appKey, v.getApplicationKey())) {
                    v.setApplicationKey(appKey);
                    configurationService.save(v, teamId, k, ConfigType.Module_IAM);
                }
            });
        }
    }

    private void initCtx(Long teamId, String teamCode, Long userId) {
        if (TrantorContext.getContext() == null) {
            TrantorContext.init();
        }
        User user = new User();
        user.setId(userId);
        TrantorContext.setCurrentUser(user);
        TrantorContext.setTeamId(teamId);
        TrantorContext.setTeamCode(teamCode);
    }
}
