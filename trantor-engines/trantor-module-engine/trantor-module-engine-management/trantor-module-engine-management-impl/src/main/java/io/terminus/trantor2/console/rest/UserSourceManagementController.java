package io.terminus.trantor2.console.rest;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.module.dto.UserSourceDTO;
import io.terminus.trantor2.module.query.UserSourceQuery;
import io.terminus.trantor2.console.service.TrantorUserSourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;


@Tag(name = "用户源管理")
@RestController
@RequestMapping(path = "/api/trantor/console/userSource")
public class UserSourceManagementController {
    @Autowired
    private TrantorUserSourceService trantorUserSourceService;

    @PostMapping("/create")
    @Operation(summary  = "新建用户源")
    public Response<Long> create(@RequestBody UserSourceDTO userSourceDTO) {
        return Response.ok(trantorUserSourceService.create(userSourceDTO));
    }

    @PostMapping("/update")
    @Operation(summary  = "编辑用户源")
    public Response<Void> update(@RequestBody UserSourceDTO userSourceDTO) {
        trantorUserSourceService.update(userSourceDTO);
        return Response.ok();
    }

    @GetMapping("/getByUserSourceId")
    @Operation(summary  = "根据用户源ID获取用户源信息")
    public Response<UserSourceDTO> getById(@RequestParam Long id) {
        return Response.ok(trantorUserSourceService.getById(id));
    }

    @PostMapping("/delete")
    @Operation(summary  = "根据用户源ID删除用户源")
    public Response<Void> delete(@RequestParam Long id) {
        trantorUserSourceService.delete(id);
        return Response.ok();
    }

    @GetMapping("/query")
    @Operation(summary  = "根据查询条件查询用户源")
    public Response<List<UserSourceDTO>> query(UserSourceQuery query) {
        //无条件查询
        if (Objects.isNull(query)) {
            query = new UserSourceQuery();
        }
        return Response.ok(trantorUserSourceService.query(query));
    }
}
