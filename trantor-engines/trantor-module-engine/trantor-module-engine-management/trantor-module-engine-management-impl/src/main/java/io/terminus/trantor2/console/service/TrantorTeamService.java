package io.terminus.trantor2.console.service;

import io.terminus.iam.api.request.user.UserPagingParams;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.module.dto.TrantorTeamDTO;

import java.util.List;

public interface TrantorTeamService {

    Response<Long> create(TrantorTeamDTO teamDTO);

    Response<TrantorTeamDTO> update(TrantorTeamDTO teamDTO);

    Response<TrantorTeamDTO> getByTeamId(Long teamId);

    Response<TrantorTeamDTO> getByUserGroupId(Long userGroupId);

    Response<List<TrantorTeamDTO>> list();

    Response<Boolean> delete(Long id);

    @Deprecated
    void addMember(String teamCode, List<Long> userIds);

    @Deprecated
    void removeMember(String teamCode, List<Long> userIds);

    @Deprecated
    Paging<User> listMember(String teamCode, UserPagingParams params);

    @Deprecated
    Paging<User> listMemberNotIn(String teamCode, UserPagingParams params);

    void installSystemApp(Long teamId, String teamCode);
}
