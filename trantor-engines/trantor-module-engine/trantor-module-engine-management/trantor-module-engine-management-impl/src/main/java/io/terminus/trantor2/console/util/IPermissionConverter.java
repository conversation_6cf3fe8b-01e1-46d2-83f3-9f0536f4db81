package io.terminus.trantor2.console.util;

import io.terminus.iam.api.response.permission.v2.Permission;
import io.terminus.iam.api.response.permission.v2.PermissionResource;
import io.terminus.trantor2.console.api.vo.ConsolePermissionResourceVO;
import io.terminus.trantor2.console.api.vo.ConsolePermissionVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * Created by hedy on 2023/3/9.
 */
@Mapper(componentModel = "spring")
public interface IPermissionConverter {

    ConsolePermissionVO convert(Permission permission);

    @Mapping(target = "resourceName", source = "name")
    @Mapping(target = "resourceKey", source = "key")
    @Mapping(target = "resourceType", source = "type")
    ConsolePermissionResourceVO permissionResourceToConsolePermissionResourceVO(PermissionResource permissionResource);

    List<ConsolePermissionResourceVO> permissionResourceListToConsolePermissionResourceVOList(List<PermissionResource> permissionResourceList);

    List<ConsolePermissionVO> copyConsolePermissionVO(List<ConsolePermissionVO> permissionVOS);

}
