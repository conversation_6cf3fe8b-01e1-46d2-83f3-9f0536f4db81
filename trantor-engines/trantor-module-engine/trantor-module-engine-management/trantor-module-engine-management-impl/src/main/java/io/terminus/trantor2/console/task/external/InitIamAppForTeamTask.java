package io.terminus.trantor2.console.task.external;

import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskDefine;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.repository.ModuleRepo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public final class InitIamAppForTeamTask extends BaseTask<InitIamAppForTeamTask.Options> {
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
    }

    @Autowired
    private ModuleRepo moduleRepo;

    @Override
    public void preCheck(Options opts, TaskContext ctx) {
        super.preCheck(opts, ctx);
    }

    @Override
    public void exec(Options opts, List<TaskDefine> subTasks, List<String> outputs, TaskContext ctx) {
        List<ModuleMeta> portals = findAllByTeamId(ctx.getTeamCode(), ctx.getUserId());
        for (ModuleMeta portal : portals) {
            InitIamAppSinglePortalTask.Options subOpts = new InitIamAppSinglePortalTask.Options();
            subOpts.setPortalKey(portal.getKey());
            subTasks.add(
                new TaskDefine(InitIamAppSinglePortalTask.class, subOpts, "", false)
            );
        }
    }

    private List<ModuleMeta> findAllByTeamId(String teamCode, Long userId) {
        ResourceContext ctx = ResourceContext.newResourceCtx(teamCode, userId);
        return moduleRepo.findAllPortalByTeam(ctx);
    }
}
