package io.terminus.trantor2.console.listener.handler;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.console.service.impl.MenuTreeManagerService;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.meta.MenuTreeMeta;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.model.dto.MenuTreeSaveRequest;
import io.terminus.trantor2.module.service.MenuConsoleQueryService;
import io.terminus.trantor2.module.service.ModuleManageService;
import io.terminus.trantor2.permission.management.api.service.IPermissionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static io.terminus.trantor2.scene.constants.SceneConsts.SCENE_KEY;


/**
 * <AUTHOR>
 * 2022/10/27 4:15 下午
 */
@Slf4j
//@Component
@AllArgsConstructor
public class SyncMenuHandler implements MenuEventHandler {
    private final MenuTreeManagerService menuManagerService;
    private final MenuConsoleQueryService menuQueryService;
    private final IPermissionService permissionService;
    private final ModuleManageService moduleService;

    /**
     * 同步新增菜单
     */
    @Override
    public void afterCreateMenu(MenuMeta menu) {
        log.info("listened menu {} create, try to create function permission", menu.getKey());
        ModuleMeta module = moduleService.findByKey(menu.getAppCode());
        permissionService.createMenuFunctionPermission(module, menu);
        log.info("menu permission create success");
    }

    /**
     * 同步删除菜单
     */
    @Override
    public void beforeDeleteMenu(MenuMeta menu) {
        log.info("listened menu {} delete, try to delete function permission", menu.getKey());
        ModuleMeta module = moduleService.findByKey(menu.getAppCode());
        permissionService.deleteMenuFunctionPermission(module, menu);
        log.info("menu permission delete success");
    }

    @Override
    public void unbindRoute(String teamCode, String sceneKey) {
        List<MenuMeta> menus = menuQueryService.findAllBySceneKeyAndTeam(sceneKey, teamCode);
        if (CollectionUtils.isEmpty(menus)) {
            return;
        }
        unbindRoute(menus);
    }

    @Override
    public void update(MenuMeta before, MenuMeta after) {
        log.info("listened menu {} update", before.getKey());
        ModuleMeta module = moduleService.findByKey(KeyUtil.moduleKey(before.getKey()));
        if (!before.getLabel().equals(after.getLabel())) {
            log.info("listened menu {} rename", before.getKey());
            permissionService.renameMenuFunctionPermission(module, after);
        }
        if (after.hasRoute()) {
            boolean sameRoute = Objects.equals(after.getRouteType(), before.getRouteType())
                && (after.getRouteConfig().equals(before.getRouteConfig())
                || Objects.equals(after.getRouteConfig(SCENE_KEY), before.getRouteConfig(SCENE_KEY)));
            if (!sameRoute) {
                log.info("listened menu {} bind route changed, before: {}, after: {}",
                    before.getKey(), before.getRouteConfig(SCENE_KEY), after.getRouteConfig(SCENE_KEY));
                log.info("start to delete menu {} function permissions", before.getKey());
                permissionService.deleteMenuFunctionPermission(module, before);
                log.info("start to create menu {} function permissions", before.getKey());
                permissionService.createMenuFunctionPermission(module, after);
            }
        } else if (before.hasRoute()) {
            unbindRoute(Collections.singletonList(before));
        }
        log.info("menu update success");
    }

    @Override
    public void updatePermission(List<MenuMeta> menus, boolean needDelete) {
        if (CollectionUtils.isEmpty(menus)) {
            return;
        }
        for (MenuMeta menu : menus) {
            log.info("listened menu {} update permission", menu.getKey());
            ModuleMeta module = moduleService.findByKey(menu.getAppCode());
            permissionService.updateMenuFunctionPermissions(module, menu);
        }
        log.info("menu update permission success");
    }

    private void unbindRoute(List<MenuMeta> menus) {
        log.info("listened menus {} unbind route", menus.stream().map(MenuMeta::getKey));
        ModuleMeta module = moduleService.findByKey(TrantorContext.getModuleKey());
        permissionService.deleteMenuFunctionPermission(module, menus);
        for (MenuMeta menu : menus) {
            String portalCode = menu.getAppCode();
            MenuTreeMeta menuTree = menuQueryService.getMenuTreeMeta(portalCode);
            menuTree.findMenuByKey(menu.getKey()).ifPresent(node -> {
                node.setRouteType(MenuMeta.RouteType.None);
                node.setRouteConfig(null);
            });
            menuManagerService.saveTree(MenuTreeSaveRequest.builder()
                .saveType(MenuTreeSaveRequest.SaveType.UPDATE)
                .menus(menuTree.getMenus())
                .curNodeKey(menu.getKey()).build());
        }
        log.info("menus unbind route success");
    }
}
