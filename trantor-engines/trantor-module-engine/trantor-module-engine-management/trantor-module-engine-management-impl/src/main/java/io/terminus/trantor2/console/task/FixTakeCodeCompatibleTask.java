package io.terminus.trantor2.console.task;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.service.QueryOp;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskDefine;
import io.terminus.trantor2.meta.util.EditUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public final class FixTakeCodeCompatibleTask extends BaseTask<FixTakeCodeCompatibleTask.Options> {
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
    }

    @Override
    public void exec(Options opts, List<TaskDefine> subTasks, List<String> outputs, TaskContext ctx) {
        QueryOp q = metaQueryService.queryInTeam(ctx.getTeamCode());
        List<MetaTreeNodeExt> all = q.findAll(Field.type().equal(MetaType.TakeCode.name()));
        for (MetaTreeNodeExt takeCode : all) {
            if (takeCode.getProps() == null) {
                continue;
            }
            boolean updated = tryFixOne(takeCode.getProps());
            if (updated) {
                outputs.add("Fix One: " + takeCode.getKey());
                metaEditService.submitOp(new MetaEditAndQueryContext(ctx.getTeamCode(), ctx.getTeamId(), ctx.getUserId()),
                    EditUtil.updateNodeOp(takeCode));
            }
        }
    }

    private boolean tryFixOne(ObjectNode takeCodeProps) {
        if (takeCodeProps == null || !takeCodeProps.isObject()) {
            return false;
        }
        boolean isNew = takeCodeProps.has("datas");
        boolean isOld = takeCodeProps.has("rules");
        if (isNew && isOld) {
            // already fixed
            return false;
        }
        if (isNew) {
            JsonNode datas = takeCodeProps.get("datas");
            if (!datas.isArray()) {
                return false;
            }
            ArrayNode list = (ArrayNode) datas;
            if (list.isEmpty()) {
                return false;
            }
            JsonNode old = list.get(0);
            if (old == null || !old.isObject()) {
                return false;
            }
            // for each old.fields();
            Iterator<Map.Entry<String, JsonNode>> fields = old.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> next = fields.next();
                if (takeCodeProps.has(next.getKey())) {
                    continue;
                }
                takeCodeProps.set(next.getKey(), next.getValue().deepCopy());
            }
            return true;
        }
        if (isOld) {
            ArrayNode datas = takeCodeProps.arrayNode();
            datas.add(takeCodeProps.deepCopy());
            takeCodeProps.set("datas", datas);
            return true;
        }
        return false;
    }
}
