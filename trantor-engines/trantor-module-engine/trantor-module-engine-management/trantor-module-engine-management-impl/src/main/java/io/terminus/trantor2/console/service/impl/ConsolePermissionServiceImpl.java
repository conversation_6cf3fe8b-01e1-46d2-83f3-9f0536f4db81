package io.terminus.trantor2.console.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.terminus.iam.api.enums.permission.PermissionGrantTargetType;
import io.terminus.iam.api.enums.permission.v2.PermissionType;
import io.terminus.iam.api.enums.role.RoleRelationBizType;
import io.terminus.iam.api.request.permission.v2.PermissionAssignFindParams;
import io.terminus.iam.api.request.permission.v2.PermissionFindParams;
import io.terminus.iam.api.request.permission.v2.PermissionResourceFindParams;
import io.terminus.iam.api.request.role.BatchRoleRelationDeleteParams;
import io.terminus.iam.api.request.role.RoleByIdsFindParams;
import io.terminus.iam.api.request.role.RoleFindParams;
import io.terminus.iam.api.request.role.RoleRelationCreateParams;
import io.terminus.iam.api.request.role.RoleRelationFindParams;
import io.terminus.iam.api.response.application.ApplicationDetails;
import io.terminus.iam.api.response.permission.v2.Permission;
import io.terminus.iam.api.response.permission.v2.PermissionAssign;
import io.terminus.iam.api.response.permission.v2.PermissionResource;
import io.terminus.iam.api.response.role.Role;
import io.terminus.iam.api.response.role.RoleRelation;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.console.api.vo.ConsolePermissionVO;
import io.terminus.trantor2.console.enums.UserRelationTypeEnum;
import io.terminus.trantor2.console.service.ConsolePermissionService;
import io.terminus.trantor2.console.util.IAMUtil;
import io.terminus.trantor2.iam.service.TrantorIAMApplicationService;
import io.terminus.trantor2.iam.service.TrantorIAMPermissionAssignService;
import io.terminus.trantor2.iam.service.TrantorIAMPermissionResourceService;
import io.terminus.trantor2.iam.service.TrantorIAMPermissionService;
import io.terminus.trantor2.iam.service.TrantorIAMRoleRelationService;
import io.terminus.trantor2.iam.service.TrantorIAMRoleService;
import io.terminus.trantor2.console.util.IPermissionConverter;
import io.terminus.trantor2.iam.exception.RoleHandleException;
import io.terminus.trantor2.module.util.RoleRelationConditionUtils;
import io.terminus.trantor2.module.util.RoleUtils;
import io.terminus.trantor2.permission.api.common.consts.PermissionConstants;
import io.terminus.trantor2.permission.management.api.service.IPermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <EMAIL>
 * 2024/5/20 10:47
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class ConsolePermissionServiceImpl implements ConsolePermissionService {


    @Autowired
    private TrantorIAMApplicationService trantorIAMApplicationService;

    @Autowired
    private TrantorIAMRoleService trantorIAMRoleService;

    @Autowired
    private TrantorIAMRoleRelationService trantorIAMRoleRelationService;

    @Autowired
    private TrantorIAMPermissionService trantorIAMPermissionService;

    @Autowired
    private TrantorIAMPermissionResourceService trantorIAMPermissionResourceService;

    @Autowired
    private TrantorIAMPermissionAssignService trantorIAMPermissionAssignService;
    @Autowired
    private IPermissionService permissionService;
    @Autowired
    private IPermissionConverter permissionConverter;

    Map<Long, List<ConsolePermissionVO>> PERMISSION_LIST_MAP = new HashMap<>();


    @Override
    public void addProjectRole(List<Long> userIds, List<Long> roleIds, List<String> projectKeys) {
        updateRoleRelation(UserRelationTypeEnum.TEAM, userIds, roleIds, projectKeys, new ArrayList<>());
    }

    @Override
    public void removeProjectRole(List<Long> userIds, List<Long> roleIds, List<String> projectKeys) {
        updateRoleRelation(UserRelationTypeEnum.TEAM, userIds, roleIds, new ArrayList<>(), projectKeys);
    }

    @Override
    public void addModuleRole(List<Long> userIds, List<Long> roleIds, List<String> moduleKeys) {
        updateRoleRelation(UserRelationTypeEnum.MODULE, userIds, roleIds, moduleKeys, new ArrayList<>());
    }

    @Override
    public void removeModuleRole(List<Long> userIds, List<Long> roleIds, List<String> moduleKeys) {
        updateRoleRelation(UserRelationTypeEnum.MODULE, userIds, roleIds, new ArrayList<>(), moduleKeys);
    }

    /**
     * 更新角色关联关系，构建condition
     * 1. findRoleRelation
     * 2. 查不到新增，查得到更新
     * 3. 包装condition
     * 4. 根据条件更新condition
     *
     * @param typeEnum   UserRelationTypeEnum
     * @param userIds    用户Ids
     * @param roleIds    角色Ids
     * @param createKeys 创建的keys
     * @param deleteKeys 移除的keys
     */
    private void updateRoleRelation(UserRelationTypeEnum typeEnum, List<Long> userIds, List<Long> roleIds, List<String> createKeys, List<String> deleteKeys) {
        // 1. userIds findRoleRelation
        List<RoleRelation> roleRelations = getRoleRelations(userIds, roleIds);
        // key: userId
        Map<String, List<RoleRelation>> roleRelationsMap = Optional.ofNullable(roleRelations).orElse(Lists.newArrayList()).stream()
            .collect(Collectors.groupingBy(RoleRelation::getBizId));

        List<RoleRelationCreateParams> conditionParamsList = new ArrayList<>();
        List<RoleRelationCreateParams> roleRelationCreateParamsList = new ArrayList<>();

        for (Long userId : userIds) {
            List<RoleRelation> userRoleRelations = roleRelationsMap.get(userId.toString());
            // key: roleId
            Map<Long, RoleRelation> roleRelationMap = Optional.ofNullable(userRoleRelations).orElse(Lists.newArrayList()).stream().
                collect(Collectors.toMap(RoleRelation::getRoleId, roleRelation -> roleRelation));
            for (Long roleId : roleIds) {
                RoleRelation roleRelation = roleRelationMap.get(roleId);
                if (roleRelation == null) {
                    // 包装condition
                    RoleRelationCreateParams roleRelationCreateWithConditionParams = new RoleRelationCreateParams();
                    roleRelationCreateWithConditionParams.setBizType(RoleRelationBizType.USER_ROLE);
                    roleRelationCreateWithConditionParams.setBizId(String.valueOf(userId));
                    roleRelationCreateWithConditionParams.setRoleIds(Lists.newArrayList(roleId));
                    String wrapCondition = RoleRelationConditionUtils.wrap(typeEnum.getKey(), Sets.newHashSet(createKeys));
                    roleRelationCreateWithConditionParams.setCondition(wrapCondition);
                    conditionParamsList.add(roleRelationCreateWithConditionParams);
                } else {
                    Set<String> unwrap = RoleRelationConditionUtils.unwrap(typeEnum.getKey(), roleRelation.getCondition());
                    unwrap.addAll(createKeys);
                    deleteKeys.forEach(unwrap::remove);
                    roleRelation.setCondition(RoleRelationConditionUtils.wrap(typeEnum.getKey(), unwrap));
                    RoleRelationCreateParams params = new RoleRelationCreateParams();
                    params.setBizId(roleRelation.getBizId());
                    params.setBizType(roleRelation.getBizType());
                    params.setEndpointId(roleRelation.getEndpointId());
                    params.setRoleIds(Collections.singletonList(roleRelation.getRoleId()));
                    params.setCondition(roleRelation.getCondition());
                    roleRelationCreateParamsList.add(params);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(conditionParamsList)) {
            trantorIAMRoleRelationService.batchCreateRoleRelation(conditionParamsList);
        }
        if (CollectionUtils.isNotEmpty(roleRelationCreateParamsList)) {
            removeAndCreateRoleRelation(roleRelationCreateParamsList);
        }

    }

    /**
     * 先删除，再创建
     *
     * @param roleRelationCreateParamsList 待创建列表
     */
    @Override
    public void removeAndCreateRoleRelation(List<RoleRelationCreateParams> roleRelationCreateParamsList) {
        roleRelationCreateParamsList.forEach(x -> {
            BatchRoleRelationDeleteParams deleteParams = new BatchRoleRelationDeleteParams();
            deleteParams.setBizIds(Collections.singletonList(Long.valueOf(x.getBizId())));
            deleteParams.setBizType(RoleRelationBizType.USER_ROLE);
            deleteParams.setRoleIds(x.getRoleIds());
            trantorIAMRoleRelationService.batchDeleteRoleRelation(deleteParams);
        });
        trantorIAMRoleRelationService.batchCreateRoleRelation(roleRelationCreateParamsList);
    }

    @Override
    public List<RoleRelation> getRoleRelations(List<Long> userIds, List<Long> roleIds) {
        RoleRelationFindParams roleRelationFindParams = new RoleRelationFindParams();
        roleRelationFindParams.setBizType(RoleRelationBizType.USER_ROLE);
        roleRelationFindParams.setRoleIds(roleIds);
        roleRelationFindParams.setBizIds(userIds.stream().map(String::valueOf).collect(Collectors.toList()));
        return trantorIAMRoleRelationService.findRoleRelation(roleRelationFindParams);
    }

    @Override
    public Map<Long, List<Role>> findRoleByUserIdsAndProjectKey(@NotNull Set<Long> userIds, UserRelationTypeEnum typeEnum, String projectKey) {
        try {
            // 初始化输入参数并调用iam服务获取roleRelations
            List<RoleRelation> roleRelationList = getRoleRelations(typeEnum, Lists.newArrayList(userIds));

            // 根据projectKey进行过滤并构建roleIds
            Set<Long> roleIds = roleRelationList.stream().filter(roleRelation -> Objects.nonNull(projectKey) && RoleRelationConditionUtils.checkIn(roleRelation.getCondition(), typeEnum.getKey(), projectKey)).map(RoleRelation::getRoleId).collect(Collectors.toSet());

            // 判断roleIds是否为空，如果为空，则没必要查询角色信息
            if (roleIds.isEmpty()) {
                return Collections.emptyMap();
            }

            // 获取RoleId对应的Role
            RoleByIdsFindParams roleByIdsFindParams = new RoleByIdsFindParams();
            roleByIdsFindParams.setIds(new ArrayList<>(roleIds));
            List<Role> roleList = trantorIAMRoleService.findRoleByIds(roleByIdsFindParams);
            Map<Long, Role> roleMap = roleList.stream().collect(Collectors.toMap(Role::getId, Function.identity()));

            // 构建userId到role的映射
            return roleRelationList.stream()
                .filter(roleRelation -> RoleRelationConditionUtils.checkIn(roleRelation.getCondition(), typeEnum.getKey(), projectKey))
                .collect(Collectors.groupingBy(roleRelation -> Long.parseLong(roleRelation.getBizId()), Collectors.mapping(roleRelation -> roleMap.get(roleRelation.getRoleId()), Collectors.toList())));

        } catch (Exception e) {
            log.error("findRoleByUserIds error", e);
            throw new RoleHandleException(e.getMessage());
        }
    }

    /**
     * 根据UserIdModuleKeys获取 moduleKey 角色列表 kv
     *
     * @param userId     用户Id
     * @param moduleKeys 模块keys
     * @return moduleKey 角色列表 kv
     */
    @Override
    public Map<String, List<Role>> findRoleByUserIdAndModuleKeys(@NotNull Long userId, List<String> moduleKeys) {
        try {
            // 初始化map来存储结果
            Map<String, List<Role>> res = new HashMap<>();

            // 调用iam服务获取roleRelations
            List<RoleRelation> roleRelationList = getRoleRelations(UserRelationTypeEnum.MODULE, Lists.newArrayList(userId));

            // 用流操作提取所有的roleIds
            Set<Long> roleIds = roleRelationList.stream().map(RoleRelation::getRoleId).collect(Collectors.toSet());

            // 若roleIds为空，直接返回空map
            if (!roleIds.isEmpty()) {
                // 获取RoleId对应的Role
                RoleByIdsFindParams roleByIdsFindParams = new RoleByIdsFindParams();
                roleByIdsFindParams.setIds(new ArrayList<>(roleIds));
                List<Role> roleList = trantorIAMRoleService.findRoleByIds(roleByIdsFindParams);
                Map<Long, Role> roleMap = roleList.stream().collect(Collectors.toMap(Role::getId, Function.identity()));

                // 对roleRelations和moduleKeys进行遍历，填充res
                for (RoleRelation roleRelation : roleRelationList) {
                    Set<String> moduleKeySet = RoleRelationConditionUtils.unwrap(UserRelationTypeEnum.MODULE.getKey(), roleRelation.getCondition());
                    // 遍历每个moduleKey
                    for (String moduleKey : moduleKeys) {
                        // 如果条件包含moduleKey，则添加到res中
                        if (moduleKeySet.contains(moduleKey)) {
                            Role role = roleMap.get(roleRelation.getRoleId());
                            if (role != null) {
                                // 如果map里没有这个key，就创建一个空列表
                                res.computeIfAbsent(moduleKey, k -> new ArrayList<>()).add(role);
                            }
                        }
                    }
                }
            }
            return res;
        } catch (Exception e) {
            log.error("findRoleByUserIds error", e);
            throw new RoleHandleException(e.getMessage());
        }
    }

    @Override
    public List<RoleRelation> getRoleRelations(UserRelationTypeEnum typeEnum, List<Long> userIds) {
        return getRoleRelations(userIds, getRoleIds(typeEnum));
    }


    @Override
    public List<ConsolePermissionVO> handleFunctionPermission(Long userId) {
        ApplicationDetails iamApplicationDetails = trantorIAMApplicationService.findIamApplicationDetails();
        // 查询目标菜单下权限点
        List<ConsolePermissionVO> permissionList = PERMISSION_LIST_MAP.computeIfAbsent(iamApplicationDetails.getId(), endpointId -> {
            PermissionFindParams permissionFindParams = new PermissionFindParams();
            permissionFindParams.setNamespaces(Collections.singletonList(IAMUtil.TRANTOR_CONSOLE_NAME_SPACE));
            permissionFindParams.setTypes(Collections.singletonList(PermissionType.FUNCTION_PERMISSION));
            List<Permission> permission = trantorIAMPermissionService.findPermission(permissionFindParams);
            List<Long> permissionIds = permission.stream().map(Permission::getId).collect(Collectors.toList());
            List<PermissionResource> permissionResource = new ArrayList<>();
                ListUtil.partition(permissionIds, PermissionConstants.PARTITION_SIZE).forEach(subPermissionIds -> {
                PermissionResourceFindParams permissionResourceFindParams = new PermissionResourceFindParams();
                permissionResourceFindParams.setNamespaces(Collections.singletonList(IAMUtil.TRANTOR_CONSOLE_NAME_SPACE));
                permissionResourceFindParams.setPermissionIds(subPermissionIds);
                permissionResource.addAll(trantorIAMPermissionResourceService.find(permissionResourceFindParams).getData());
            });
            Map<Long, List<PermissionResource>> permissionIdPermissionResourceMap = permissionResource.stream().collect(Collectors.groupingBy(PermissionResource::getPermissionId));
            List<ConsolePermissionVO> consolePermissionVOS = new ArrayList<>();
            permission.forEach(p -> consolePermissionVOS.add(convert(p, permissionIdPermissionResourceMap.get(p.getId()))));
            return consolePermissionVOS;
        });
        // 检测是否开启功能权限鉴权及当前登陆用户是否是管理员
        if (!permissionService.needExecFunctionPermission(iamApplicationDetails.getId())) {
            List<ConsolePermissionVO> result = permissionConverter.copyConsolePermissionVO(permissionList);
            result.forEach(permission -> permission.setGranted(true));
            return result;
        }
        // 过滤出用户已授权的当前门户的功能权限列表
        return findConsoleUserAuthorizedFunctionPermissionWithinPermissions(userId, permissionList, iamApplicationDetails.getId());

    }

    /**
     * 查询Console指定权限范围的用户已授权权限集合
     *
     * @param userId         用户id
     * @param permissionList 指定权限范围
     * @return 范围内的用户已授权权限集合
     */
    private List<ConsolePermissionVO> findConsoleUserAuthorizedFunctionPermissionWithinPermissions(@NotNull Long userId, @NotEmpty List<ConsolePermissionVO> permissionList, Long appId) {
        if (CollectionUtils.isEmpty(permissionList)) {
            return Collections.emptyList();
        }
        List<ConsolePermissionVO> result = permissionConverter.copyConsolePermissionVO(permissionList);

        // 获取用户绑定角色列表
        List<Role> roleList = findRoleByUserIdWithStringEqualsCondition(userId, UserRelationTypeEnum.TEAM.getKey(), TrantorContext.getTeamCode());
        List<String> roleIds = roleList.stream().map(Role::getId).filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList());
        // 如果是模块内部接口，需要校验模块权限
        if (StringUtils.isNotBlank(TrantorContext.getModuleKey())) {
            List<String> moduleRoles = Optional.of(findRoleByUserIdWithStringEqualsCondition(userId, UserRelationTypeEnum.MODULE.getKey(),
                RoleUtils.getModuleValue(TrantorContext.getTeamCode(), TrantorContext.getModuleKey())).stream()
                .map(Role::getId).filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList())).orElse(Lists.newArrayList());
            roleIds.addAll(moduleRoles);
        }
        if (CollectionUtils.isEmpty(roleIds)) {
            result.forEach(permission -> permission.setGranted(false));
            return result;
        }
        List<PermissionAssign> permissionAssign = new ArrayList<>();
        ListUtil.partition(roleIds, PermissionConstants.PARTITION_SIZE).forEach(subRoleIds -> {
            PermissionAssignFindParams permissionAssignFindParams = new PermissionAssignFindParams();
            permissionAssignFindParams.setPermissionTypes(Collections.singletonList(PermissionType.FUNCTION_PERMISSION));
            permissionAssignFindParams.setTargetTypes(Collections.singletonList(PermissionGrantTargetType.ROLE));
            permissionAssignFindParams.setTargetIds(subRoleIds);
            permissionAssignFindParams.setEffectiveApplicationIds(Collections.singletonList(appId));
            permissionAssign.addAll(trantorIAMPermissionAssignService.findPermissionAssign(permissionAssignFindParams));
        });
        if (CollectionUtils.isEmpty(permissionAssign)) {
            result.forEach(permission -> permission.setGranted(false));
            return result;
        }

        // 取出用户已授权的功能权限id集合
        Set<Long> grantedPermissionIds = permissionAssign.stream().map(PermissionAssign::getPermissionId).collect(Collectors.toSet());
        // 过滤出用户已授权的当前门户的功能权限列表 -> 改成更改权限状态
        result.forEach(permission -> {
            permission.setGranted(grantedPermissionIds.contains(permission.getId()));
        });
        return result;
    }

    /**
     * 根据用户Id 过滤roleRelation的符合StringEquals匹配条件的角色
     *
     * @param userId 用户Id
     * @param key    StringEquals要匹配的key
     * @param value  匹配的key对应的value值
     * @return 符合StringEqualsCondition条件的角色关联的角色信息
     */
    private List<Role> findRoleByUserIdWithStringEqualsCondition(@NotNull Long userId, String key, String value) {
        try {
            RoleRelationFindParams roleRelationFindParams = new RoleRelationFindParams();
            roleRelationFindParams.setBizType(RoleRelationBizType.USER_ROLE);
            roleRelationFindParams.setBizId(String.valueOf(userId));
            List<RoleRelation> roleRelationList = trantorIAMRoleRelationService.findRoleRelation(roleRelationFindParams);
            List<Long> roleIds = roleRelationList.stream().filter(roleRelation -> RoleRelationConditionUtils.checkIn(roleRelation.getCondition(), key, value)).map(RoleRelation::getRoleId).collect(Collectors.toList());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(roleIds)) {
                RoleByIdsFindParams params = new RoleByIdsFindParams();
                params.setIds(roleIds);
                return trantorIAMRoleService.findRoleByIds(params);
            } else {
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("findRoleByUserId error", e);
            throw new RoleHandleException(e.getMessage());
        }
    }

    private List<Long> getRoleIds(UserRelationTypeEnum typeEnum) {
        RoleFindParams roleFindParams = new RoleFindParams();
        Long iamAppIdForConsole = trantorIAMApplicationService.findIamApplicationDetails().getId();
        roleFindParams.setSourceApplicationIds(Collections.singleton(iamAppIdForConsole));
        roleFindParams.setRoleTypeKeys(Collections.singleton(UserRelationTypeEnum.TEAM.equals(typeEnum) ? IAMUtil.TEAM_ROLE_TYPE_KEY : IAMUtil.MODULE_ROLE_TYPE_KEY));
        List<Role> roles = trantorIAMRoleService.queryRole(roleFindParams);
        return roles.stream().map(Role::getId).collect(Collectors.toList());
    }

    private ConsolePermissionVO convert(Permission permission, List<PermissionResource> permissionResourceList) {
        ConsolePermissionVO convert = permissionConverter.convert(permission);
        convert.setPermissionResources(permissionConverter.permissionResourceListToConsolePermissionResourceVOList(permissionResourceList));
        return convert;
    }
}
