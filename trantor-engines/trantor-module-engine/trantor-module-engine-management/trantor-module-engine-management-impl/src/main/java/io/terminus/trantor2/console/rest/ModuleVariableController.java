package io.terminus.trantor2.console.rest;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.console.api.vo.ModuleVariableVO;
import io.terminus.trantor2.console.api.vo.VariableGroupItemVO;
import io.terminus.trantor2.console.api.vo.VariableVO;
import io.terminus.trantor2.console.service.impl.ModuleVariableService;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: ya<PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/10/12 2:18 PM
 **/
@Tag(name = "模块变量管理")
@RestController
@AllArgsConstructor
@RequestMapping(path = "/api/trantor/console/module/var")
public class ModuleVariableController {
    private final ModuleVariableService variableService;

    @GetMapping("/list")
    @Operation(summary = "查询")
    public Response<List<VariableVO>> list(@RequestParam String moduleKey) {
        return Response.ok(variableService.list(moduleKey));
    }

    @GetMapping("/global/list")
    @Operation(summary = "全局查询-模块视角")
    public Response<List<ModuleVariableVO>> list(@RequestParam Long teamId) {
        return Response.ok(variableService.list(teamId));
    }

    @GetMapping("/global/group")
    @Operation(summary = "全局查询-全局视角")
    public Response<Map<String, List<VariableGroupItemVO>>> groupByKey(@RequestParam Long teamId) {
        return Response.ok(variableService.groupByKey(teamId));
    }

    @PostMapping("/save")
    @Operation(summary = "保存")
    public Response<Void> save(@RequestBody ModuleVariableVO variable) {
        variableService.save(variable);
        return Response.ok();
    }

    @PostMapping("/batch-save")
    @Operation(summary = "保存")
    public Response<Void> batchSave(@RequestBody List<ModuleVariableVO> variables) {
        variableService.batchSave(variables);
        return Response.ok();
    }
}
