package io.terminus.trantor2.console.rest;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.console.service.impl.MenuTreeManagerService;
import io.terminus.trantor2.console.task.ImportMenuTask;
import io.terminus.trantor2.console.task.external.InitIamAppSinglePortalTask;
import io.terminus.trantor2.meta.management.task.StartTaskResult;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskDefine;
import io.terminus.trantor2.meta.management.task.TaskManager;
import io.terminus.trantor2.meta.task.TaskExecResult;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.model.dto.MenuTreeSaveRequest;
import io.terminus.trantor2.module.service.OSSService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Tag(name = "菜单")
@RestController
@RequestMapping("/api/trantor/menu")
@RequiredArgsConstructor
public class MenuController {
    private final MenuTreeManagerService menuTreeService;
    private final TaskManager taskManager;
    private final OSSService ossService;

    @GetMapping("/tree/{portalCode}")
    @Operation(summary = "获取门户菜单树菜单树")
    public Response<List<MenuMeta>> getTree(@PathVariable String portalCode) {
        return Response.ok(menuTreeService.getTree(portalCode));
    }

    @PostMapping("/tree/save")
    @Operation(summary = "保存门户菜单树")
    public Response<Void> saveTree(@Valid @RequestBody MenuTreeSaveRequest request) {
        menuTreeService.saveTree(request);
        return Response.ok();
    }

    @PostMapping("/import")
    @Operation(summary = "导入菜单")
    public Response<TaskExecResult> importMenu(@Parameter(description = "CSV文件") @RequestParam("file") MultipartFile file) {
        String portalCode = Optional.ofNullable(TrantorContext.getModuleKey())
                .orElseThrow(() -> new ValidationException("current portal code not found"));

        if (file.isEmpty() || file.getOriginalFilename() == null) {
            throw new ValidationException("文件为空");
        }
        if (!file.getOriginalFilename().toLowerCase().endsWith(".csv")) {
            throw new ValidationException("只支持CSV文件格式");
        }

        ImportMenuTask.Options opts = new ImportMenuTask.Options();
        try {
            String objectName = ossService.migrateFileIn(
                    "menu/import/" + TrantorContext.getTeamCode() + "/" + portalCode+ "/" + file.getOriginalFilename(),
                    file.getInputStream(),
                    file.getContentType(),
                    null
            );
            opts.setCsvObjectName(objectName);
        } catch (Exception e) {
            throw new ValidationException("文件上传失败");
        }
        opts.setPortalCode(portalCode);

        TaskContext taskContext = new TaskContext(TrantorContext.getTeamId(), TrantorContext.getTeamCode(), TrantorContext.getCurrentUserId(), false);
        InitIamAppSinglePortalTask.Options subOpts = new InitIamAppSinglePortalTask.Options();
        subOpts.setPortalKey(portalCode);
        taskContext.setSubTasks(Collections.singletonList(new TaskDefine(InitIamAppSinglePortalTask.class, subOpts, "", false)
        ));

        StartTaskResult startTaskResult = taskManager.startAsyncTask(new TaskDefine(ImportMenuTask.class, opts, "", true), taskContext);
        return Response.ok(new TaskExecResult(startTaskResult.getTaskRunId(), TrantorContext.getTeamId()));
    }

    @PostMapping("/export")
    @Operation(summary = "导出菜单")
    public ResponseEntity<ByteArrayResource> export(@Parameter(description = "文件类型，默认为 csv")
                                                    @RequestParam(value = "fileType", defaultValue = "csv") String fileType) {
        Map.Entry<String, ByteArrayResource> file = menuTreeService.export(fileType);

        HttpHeaders headers = new HttpHeaders();
        String fileName = file.getKey();
        String encodedFileName = fileName;
        try {
            encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()).replaceAll("\\+", "%20");
        } catch (Exception e) {
            // ignore
        }
        // RFC 5987 格式
        String contentDisposition = "attachment; filename=\"" + encodedFileName + "\"; filename*=utf-8''" + encodedFileName;
        headers.add(HttpHeaders.CONTENT_DISPOSITION, contentDisposition);
        headers.add(HttpHeaders.CONTENT_TYPE, "application/octet-stream");
        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(file.getValue());
    }
}
