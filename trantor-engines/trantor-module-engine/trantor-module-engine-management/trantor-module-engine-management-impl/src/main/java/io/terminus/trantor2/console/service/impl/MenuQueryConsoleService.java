package io.terminus.trantor2.console.service.impl;

import cn.hutool.core.util.NumberUtil;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.meta.MenuTreeMeta;
import io.terminus.trantor2.module.repository.MenuTreeRepo;
import io.terminus.trantor2.module.service.MenuConsoleQueryService;
import io.terminus.trantor2.module.service.ModuleManagerQueryService;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import jakarta.annotation.Nonnull;
import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class MenuQueryConsoleService implements MenuConsoleQueryService {
    private final MenuTreeRepo menuTreeRepo;
    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    private final ModuleManagerQueryService moduleQueryService;

    @Override
    @Nonnull
    public <T extends Serializable> @NotNull MenuTreeMeta getMenuTreeMeta(T portalIdOrCode) {
        String portalCode = NumberUtil.isLong(portalIdOrCode.toString()) ?
                moduleQueryService.findKeyById(Long.parseLong(portalIdOrCode.toString())) : portalIdOrCode.toString();

        return menuTreeRepo.findOneByKey(KeyUtil.newKeyUnderModule(portalCode, MenuTreeMeta.MENU_TREE_SHORT_KEY),
                ResourceContext.ctxFromThreadLocal()).orElse(new MenuTreeMeta());
    }

    @Override
    public Collection<MenuMeta> findMenusByKeys(@Nonnull Collection<String> keys) {
        Map<String, MenuMeta> foundNodes = new HashMap<>();
        Set<String> processedPortalCodes = new HashSet<>();

        for (String key : keys) {
            String portalCode = KeyUtil.moduleKey(key);
            if (processedPortalCodes.contains(portalCode)) {
                continue;
            }
            Optional<MenuTreeMeta> menuTree = menuTreeRepo.findOneByKey(
                    KeyUtil.newKeyUnderModule(portalCode, MenuTreeMeta.MENU_TREE_SHORT_KEY),
                    ResourceContext.ctxFromThreadLocal());

            menuTree.ifPresent(menuTreeMeta ->
                    menuTreeMeta.findMenusByKeys(keys).forEach(menuMeta -> foundNodes.put(menuMeta.getKey(), menuMeta)));
            processedPortalCodes.add(portalCode);
        }
        return foundNodes.values();
    }


    @Override
    public List<MenuMeta> findAllBySceneKeyAndTeam(@Nonnull String sceneKey, String teamCode) {
        List<MenuTreeMeta> menuTrees = menuTreeRepo.findAll(Cond.and(), ResourceContext.newResourceCtx(teamCode, TrantorContext.getCurrentUserId()));
        List<MenuMeta> res = new ArrayList<>(menuTrees.size());
        for (MenuTreeMeta menuTree : menuTrees) {
            menuTree.findMenuBoundScene(sceneKey).ifPresent(res::add);
        }
        return res;
    }
}
