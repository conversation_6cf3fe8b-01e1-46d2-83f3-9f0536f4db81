package io.terminus.trantor2.console.rest;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.console.api.vo.ConsolePermissionVO;
import io.terminus.trantor2.console.service.ConsolePermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * 证书相关接口
 * 查询、上传
 */
@Tag(name = "权限")
@RestController
@RequestMapping("/api/trantor/console/permission")
public class TrantorPermissionController {

    @Autowired
    private ConsolePermissionService consolePermissionService;

    @GetMapping("/list")
    @Operation(summary = "查询角色权限")
    public Response<List<ConsolePermissionVO>> queryPermissionList() {
        return Response.ok(consolePermissionService.handleFunctionPermission(TrantorContext.getCurrentUserId()));
    }

}
