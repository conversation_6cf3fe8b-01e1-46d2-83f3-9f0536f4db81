package io.terminus.trantor2.console.service.impl;

import io.terminus.iam.api.dto.permission.FunctionPermissionAssignProps;
import io.terminus.iam.api.enums.permission.AuthorizationEffect;
import io.terminus.iam.api.enums.permission.PermissionGrantTargetType;
import io.terminus.iam.api.enums.permission.v2.PermissionType;
import io.terminus.iam.api.request.permission.v2.PermissionAssignCreateParams;
import io.terminus.iam.api.request.permission.v2.PermissionAssignDeleteOrCreateParams;
import io.terminus.iam.api.request.permission.v2.PermissionAssignFindParams;
import io.terminus.iam.api.request.permission.v2.PermissionCreateParams;
import io.terminus.iam.api.request.permission.v2.PermissionDeleteOrUpdateOrCreateParams;
import io.terminus.iam.api.request.permission.v2.PermissionFindParams;
import io.terminus.iam.api.request.permission.v2.PermissionResourceCreateParams;
import io.terminus.iam.api.request.permission.v2.PermissionResourceDeleteOrUpdateOrCreateParams;
import io.terminus.iam.api.request.permission.v2.PermissionResourceFindParams;
import io.terminus.iam.api.request.permission.v2.PermissionResourceUpdateParams;
import io.terminus.iam.api.request.permission.v2.PermissionUpdateParams;
import io.terminus.iam.api.response.permission.v2.Permission;
import io.terminus.iam.api.response.permission.v2.PermissionAssign;
import io.terminus.iam.api.response.permission.v2.PermissionResource;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.console.service.TrantorIamManageService;
import io.terminus.trantor2.iam.dto.ApplicationConfig;
import io.terminus.trantor2.iam.service.TrantorIAMApplicationConfigService;
import io.terminus.trantor2.iam.service.TrantorIAMPermissionAssignService;
import io.terminus.trantor2.iam.service.TrantorIAMPermissionResourceService;
import io.terminus.trantor2.iam.service.TrantorIAMPermissionService;
import io.terminus.trantor2.iam.utils.IAMNamespaceGenerator;
import io.terminus.trantor2.ide.repository.PermissionRepo;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.resource.BaseMeta;
import io.terminus.trantor2.meta.resource.ResourceBaseMeta;
import io.terminus.trantor2.module.meta.SystemConfig;
import io.terminus.trantor2.module.service.ConfigurationService;
import io.terminus.trantor2.permission.PermissionMeta;
import io.terminus.trantor2.permission.api.common.dto.ACLResourceType;
import io.terminus.trantor2.service.common.meta.AIAgentMeta;
import io.terminus.trantor2.service.management.repo.AIAgentRepo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> qianjin
 * @since : 2024/8/14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrantorIamManageServiceImpl implements TrantorIamManageService {

    private final ConfigurationService configurationService;
    private final TrantorIAMApplicationConfigService trantorIAMApplicationConfigService;
    private final AIAgentRepo agentRepo;
    private final TrantorIAMPermissionResourceService iamPermissionResourceService;
    private final TrantorIAMPermissionService iamPermissionService;
    private final TrantorIAMPermissionAssignService iamPermissionAssignService;
    private final PermissionRepo permissionRepo;

    /**
     * 更新IAM主题配置
     *
     * @param portalKey  门户key
     * @param themeValue 主题值
     */
    @Override
    public void updateIamTheme(String portalKey, String themeValue) {
        try {
            Long iamAppId = configurationService.getIamEndpointId(TrantorContext.getTeamId(), portalKey);
            ApplicationConfig applicationConfig = trantorIAMApplicationConfigService.findByIAMAppId(iamAppId);
            applicationConfig.setTheme(themeValue);
            trantorIAMApplicationConfigService.updateConfigByIAMAppId(iamAppId, applicationConfig);
        } catch (Exception e) {
            // 更新失败不影响业务
            log.error("update iam theme failed", e);
        }
    }

    @Override
    public void syncPortalAIAgentsToIAM(@NotNull String portalKey,
                                        @Nullable SystemConfig previousSystemConfig,
                                        @NotNull SystemConfig currentSystemConfig) {
        if (Objects.isNull(currentSystemConfig.getTopConfigs())) {
            return;
        }
        // 更新前的旧ai配置
        Set<String> previousAIAgentKeysInConfig = new HashSet<>();
        if (Objects.nonNull(previousSystemConfig)) {
            previousAIAgentKeysInConfig.addAll(
                    previousSystemConfig.getTopConfigs().stream()
                            .filter(topConfig -> "ai".equalsIgnoreCase(topConfig.getKey()))
                            .map(SystemConfig.TopConfig::getConfigs)
                            .filter(Objects::nonNull)
                            .flatMap(Collection::stream)
                            .filter(c -> !"scene".equals(c.getType()))
                            .map(SystemConfig.Config::getKey)
                            .collect(Collectors.toSet())
            );
        }
        // 更新后的新ai配置
        Set<String> newAIAgentKeysInConfig = currentSystemConfig.getTopConfigs().stream()
                .filter(topConfig -> "ai".equalsIgnoreCase(topConfig.getKey()))
                .map(SystemConfig.TopConfig::getConfigs)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(c -> !"scene".equals(c.getType()))
                .map(SystemConfig.Config::getKey)
                .collect(Collectors.toSet());
        // 求差集得到要删除的智能体，旧智能体集合 - 新智能体集合
        previousAIAgentKeysInConfig.removeAll(newAIAgentKeysInConfig);
        List<AIAgentMeta> deleteAgentMetas = agentRepo.findAllByKeys(previousAIAgentKeysInConfig, ResourceContext.ctxFromThreadLocal());
        List<AIAgentMeta> createOrUpdateAgentMetas = agentRepo.findAllByKeys(newAIAgentKeysInConfig, ResourceContext.ctxFromThreadLocal());
        syncPortalAIAgentsToIAM(portalKey, deleteAgentMetas, createOrUpdateAgentMetas);
    }

    private void syncPortalAIAgentsToIAM(@NotNull String portalKey,
                                         @NotNull List<AIAgentMeta> deleteAgentMetas,
                                         @NotNull List<AIAgentMeta> createOrUpdateAgentMetas) {
        Long iamAppId = configurationService.getIamEndpointId(TrantorContext.getTeamId(), portalKey);

        Set<String> createOrUpdateAgentPermKeys = createOrUpdateAgentMetas.stream()
                .map(ResourceBaseMeta::getResourceProps)
                .map(AIAgentMeta.Props::getPermissionKey)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // todo 未处理移除智能体的同步逻辑
        Set<String> deleteAgentPermKeys = deleteAgentMetas.stream()
                .map(ResourceBaseMeta::getResourceProps)
                .map(AIAgentMeta.Props::getPermissionKey)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(createOrUpdateAgentPermKeys) && CollectionUtils.isEmpty(deleteAgentPermKeys)) {
            return;
        }

        // 获取IAM中的智能体权限信息
        Map<String, Permission> iamPermissionKeyMap = getIamPermissionKeyMap(createOrUpdateAgentPermKeys);

        // 同步权限项
        PermissionDeleteOrUpdateOrCreateParams permissionDeleteOrUpdateOrCreateParams
                = convertToPermissionParams(iamAppId, createOrUpdateAgentPermKeys, iamPermissionKeyMap);
        if (Objects.nonNull(permissionDeleteOrUpdateOrCreateParams)) {
            iamPermissionService.deleteOrUpdateOrCreate(permissionDeleteOrUpdateOrCreateParams);
        }

        // 同步资源
        PermissionResourceDeleteOrUpdateOrCreateParams resourceDeleteOrUpdateOrCreateParams
                = convertToResourceParams(iamAppId, createOrUpdateAgentMetas, iamPermissionKeyMap);
        if (Objects.nonNull(resourceDeleteOrUpdateOrCreateParams)) {
            iamPermissionResourceService.deleteOrUpdateOrCreate(resourceDeleteOrUpdateOrCreateParams);
        }

        // 同步门户授权
        PermissionAssignDeleteOrCreateParams permissionAssignDeleteOrCreateParams
                = convertToPermissionAssignParams(iamAppId, createOrUpdateAgentPermKeys, iamPermissionKeyMap);
        if (Objects.nonNull(permissionAssignDeleteOrCreateParams)) {
            iamPermissionAssignService.deleteOrCreatePermissionAssign(permissionAssignDeleteOrCreateParams);
        }
    }

    private PermissionAssignDeleteOrCreateParams convertToPermissionAssignParams(@NotNull Long iamAppId,
                                                                                 @NotNull Set<String> agentPermKeys,
                                                                                 @NotNull Map<String, Permission> iamPermissionKeyMap) {
        Set<Long> iamPermIds = agentPermKeys.stream()
                .map(iamPermissionKeyMap::get)
                .filter(Objects::nonNull)
                .map(Permission::getId)
                .collect(Collectors.toSet());
        PermissionAssignFindParams findParams = new PermissionAssignFindParams();
        findParams.setEffectiveApplicationIds(Collections.singleton(iamAppId));
        findParams.setPermissionTypes(Collections.singleton(PermissionType.FUNCTION_PERMISSION));
        findParams.setPermissionIds(iamPermIds);
        findParams.setTargetTypes(Collections.singleton(PermissionGrantTargetType.APPLICATION));
        findParams.setTargetIds(Collections.singleton(String.valueOf(iamAppId)));
        List<PermissionAssign> permissionAssigns = iamPermissionAssignService.findPermissionAssign(findParams);
        Map<String, PermissionAssign> iamPermissionAssignMap = permissionAssigns.stream()
                .collect(Collectors.toMap(PermissionAssign::getPermissionKey, Function.identity()));

        List<PermissionAssignCreateParams> iamPermissionAssignCreateParamsList = new ArrayList<>();
        for (String agentPermKey : agentPermKeys) {
            if (!iamPermissionAssignMap.containsKey(agentPermKey)) {
                PermissionAssignCreateParams createParams = new PermissionAssignCreateParams();
                createParams.setSourceApplicationId(iamAppId);
                createParams.setEffectiveApplicationId(iamAppId);
                createParams.setPermissionId(iamPermissionKeyMap.get(agentPermKey).getId());
                createParams.setPermissionType(PermissionType.FUNCTION_PERMISSION);
                createParams.setPermissionKey(agentPermKey);
                createParams.setEffect(AuthorizationEffect.ALLOW);
                createParams.setTargetType(PermissionGrantTargetType.APPLICATION);
                createParams.setTargetId(String.valueOf(iamAppId));
                FunctionPermissionAssignProps props = new FunctionPermissionAssignProps();
                props.setAuthenticationEnabled(Boolean.TRUE);
                props.setAuthorizationEvaluateEnabled(Boolean.TRUE);
                props.setOpenApi(Boolean.FALSE);
                createParams.setProps(props);
                iamPermissionAssignCreateParamsList.add(createParams);
            }
        }
        if (CollectionUtils.isNotEmpty(iamPermissionAssignCreateParamsList)) {
            PermissionAssignDeleteOrCreateParams deleteOrCreateParams = new PermissionAssignDeleteOrCreateParams();
            deleteOrCreateParams.setCreateParamsList(iamPermissionAssignCreateParamsList);
            return deleteOrCreateParams;
        }
        return null;
    }

    private PermissionResourceDeleteOrUpdateOrCreateParams convertToResourceParams(@NotNull Long iamAppId,
                                                                                   @NotNull List<AIAgentMeta> createOrUpdateAgentMetas,
                                                                                   @NotNull Map<String, Permission> iamPermissionKeyMap) {
        PermissionResourceFindParams findParams = new PermissionResourceFindParams();
        String resourceNamespace = IAMNamespaceGenerator.generatePermissionResourceNamespace(TrantorContext.getTeamCode());
        findParams.setNamespaces(Collections.singleton(resourceNamespace));
        findParams.setTypes(Collections.singleton(ACLResourceType.AIAgent.name()));
        List<String> agentKeys = createOrUpdateAgentMetas.stream().map(BaseMeta::getKey).collect(Collectors.toList());
        findParams.setKeys(agentKeys);
        List<PermissionResource> permissionResources = iamPermissionResourceService.findList(findParams);
        Map<String, PermissionResource> iamResourceKeyMap = permissionResources.stream()
                .collect(Collectors.toMap(PermissionResource::getKey, Function.identity()));

        List<PermissionResourceCreateParams> iamResourceCreateParamsList = new ArrayList<>();
        List<PermissionResourceUpdateParams> iamResourceUpdateParamsList = new ArrayList<>();
        for (AIAgentMeta agentMeta : createOrUpdateAgentMetas) {
            if (iamResourceKeyMap.containsKey(agentMeta.getKey())) {
                PermissionResource iamResource = iamResourceKeyMap.get(agentMeta.getKey());
                PermissionResourceUpdateParams updateParams = new PermissionResourceUpdateParams();
                updateParams.setId(iamResource.getId());
                updateParams.setName(agentMeta.getName());
                updateParams.setDescription(agentMeta.getDescription());
                String agentPermKey = agentMeta.getResourceProps().getPermissionKey();
                if (Objects.nonNull(agentPermKey)) {
                    Long newResPermId = iamPermissionKeyMap.get(agentPermKey).getId();
                    if (!Objects.equals(newResPermId, iamResource.getPermissionId())) {
                        updateParams.setPermissionId(newResPermId);
                    }
                }
                iamResourceUpdateParamsList.add(updateParams);
            } else {
                PermissionResourceCreateParams createParams = new PermissionResourceCreateParams();
                createParams.setSourceAppId(iamAppId);
                createParams.setNamespace(resourceNamespace);
                createParams.setType(ACLResourceType.AIAgent.name());
                createParams.setKey(agentMeta.getKey());
                createParams.setName(agentMeta.getName());
                createParams.setDescription(agentMeta.getDescription());
                String agentPermKey = agentMeta.getResourceProps().getPermissionKey();
                if (Objects.nonNull(agentPermKey)) {
                    createParams.setPermissionId(iamPermissionKeyMap.get(agentPermKey).getId());
                }
                iamResourceCreateParamsList.add(createParams);
            }
        }
        if (CollectionUtils.isNotEmpty(iamResourceCreateParamsList)
                || CollectionUtils.isNotEmpty(iamResourceUpdateParamsList)) {
            PermissionResourceDeleteOrUpdateOrCreateParams deleteOrUpdateOrCreateParams = new PermissionResourceDeleteOrUpdateOrCreateParams();
            deleteOrUpdateOrCreateParams.setCreateParamsList(iamResourceCreateParamsList);
            deleteOrUpdateOrCreateParams.setUpdateParamsList(iamResourceUpdateParamsList);
            return deleteOrUpdateOrCreateParams;
        }
        return null;
    }

    private Map<String, Permission> getIamPermissionKeyMap(@NotNull Set<String> agentPermKeys) {
        PermissionFindParams permissionFindParams = new PermissionFindParams();
        String permissionNamespace = IAMNamespaceGenerator.generatePermissionNamespace(TrantorContext.getTeamCode());
        permissionFindParams.setNamespaces(Collections.singleton(permissionNamespace));
        permissionFindParams.setTypes(Collections.singleton(PermissionType.FUNCTION_PERMISSION));
        permissionFindParams.setKeys(agentPermKeys);
        List<Permission> permissionList = iamPermissionService.findPermission(permissionFindParams);
        return permissionList.stream()
                .collect(Collectors.toMap(Permission::getKey, Function.identity()));
    }

    private PermissionDeleteOrUpdateOrCreateParams convertToPermissionParams(@NotNull Long iamAppId,
                                                                             @NotNull Set<String> agentPermKeys,
                                                                             @NotNull Map<String, Permission> iamPermissionKeyMap) {
        List<PermissionMeta> permissionMetas = permissionRepo.findAllByKeys(agentPermKeys, ResourceContext.ctxFromThreadLocal());
        List<PermissionCreateParams> iamPermissionCreateParamsList = new ArrayList<>();
        List<PermissionUpdateParams> iamPermissionUpdateParamsList = new ArrayList<>();
        for (PermissionMeta permissionMeta : permissionMetas) {
            if (iamPermissionKeyMap.containsKey(permissionMeta.getKey())) {
                PermissionUpdateParams updateParams = new PermissionUpdateParams();
                updateParams.setId(iamPermissionKeyMap.get(permissionMeta.getKey()).getId());
                updateParams.setName(permissionMeta.getName());
                updateParams.setDescription(permissionMeta.getDescription());
                iamPermissionUpdateParamsList.add(updateParams);
            } else {
                PermissionCreateParams createParams = new PermissionCreateParams();
                createParams.setSourceAppId(iamAppId);
                String permissionNamespace = IAMNamespaceGenerator.generatePermissionNamespace(TrantorContext.getTeamCode());
                createParams.setNamespace(permissionNamespace);
                createParams.setType(PermissionType.FUNCTION_PERMISSION);
                createParams.setKey(permissionMeta.getKey());
                createParams.setName(permissionMeta.getName());
                createParams.setDescription(permissionMeta.getDescription());
                createParams.setEnabled(Boolean.TRUE);
                iamPermissionCreateParamsList.add(createParams);
            }
        }
        if (CollectionUtils.isNotEmpty(iamPermissionCreateParamsList)
                || CollectionUtils.isNotEmpty(iamPermissionUpdateParamsList)) {
            PermissionDeleteOrUpdateOrCreateParams deleteOrUpdateOrCreateParams = new PermissionDeleteOrUpdateOrCreateParams();
            deleteOrUpdateOrCreateParams.setCreateParamsList(iamPermissionCreateParamsList);
            deleteOrUpdateOrCreateParams.setUpdateParamsList(iamPermissionUpdateParamsList);
            return deleteOrUpdateOrCreateParams;
        }
        return null;
    }
}
