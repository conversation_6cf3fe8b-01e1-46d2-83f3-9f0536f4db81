package io.terminus.trantor2.console.rest;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.user.UserVO;
import io.terminus.trantor2.console.service.TrantorAdministratorSourceService;
import io.terminus.trantor2.module.model.query.AdministratorPagingRequest;
import io.terminus.trantor2.module.model.query.AdministratorRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@Tag(name = "门户管理员")
@RestController
@RequestMapping(path = "/api/trantor/console/admin")
public class TrantorAdministratorController {

    @Autowired
    private TrantorAdministratorSourceService trantorAdministratorSourceService;

    @GetMapping("/list")
    @Operation(summary  = "获取管理员列表")
    public Response<Paging<UserVO>> list(AdministratorPagingRequest administratorPagingRequest) {
        return Response.ok(trantorAdministratorSourceService.pageAdministratorIn(administratorPagingRequest));
    }

    @GetMapping("/list-not-in")
    @Operation(summary  = "获取未加入管理员列表")
    public Response<Paging<UserVO>> listNotIn(AdministratorPagingRequest administratorPagingRequest) {
        return Response.ok(trantorAdministratorSourceService.pageAdministratorNotIn(administratorPagingRequest));
    }


    @PostMapping("/add")
    @Operation(summary  = "添加管理员")
    public Response<Boolean> add(@RequestBody AdministratorRequest administratorRequest) {
        return Response.ok(trantorAdministratorSourceService.createAdministrator(administratorRequest));
    }

    @PostMapping("/remove")
    @Operation(summary  = "移除管理员")
    public Response<Boolean> remove(@RequestBody AdministratorRequest administratorRequest) {
        return Response.ok(trantorAdministratorSourceService.deleteAdministrator(administratorRequest));
    }
}
