package io.terminus.trantor2.console.service;

import io.terminus.trantor2.module.dto.UserSourceDTO;
import io.terminus.trantor2.module.query.UserSourceQuery;

import java.util.List;

/**
 * <AUTHOR>
 * 2022/8/25 3:56 下午
 */
public interface TrantorUserSourceService {

    Long create(UserSourceDTO userSourceDTO);

    void update(UserSourceDTO userSourceDTO);

    UserSourceDTO getById(Long id);

    void delete(Long id);

    List<UserSourceDTO> query(UserSourceQuery query);
}
