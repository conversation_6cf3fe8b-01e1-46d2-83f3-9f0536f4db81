package io.terminus.trantor2.console.i18n;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.module.i18n.I18nVisitor;
import io.terminus.trantor2.module.meta.ConfigType;
import io.terminus.trantor2.module.meta.SystemConfig;
import io.terminus.trantor2.module.service.ConfigurationService;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SystemConfigI18nVisitor implements I18nVisitor {
    private final ConfigurationService configurationService;

    @Override
    public MetaType getType() {
        return null;
    }

    @Override
    public boolean moduleResource() {
        return false;
    }

    @Override
    public Set<String> visit(Collection<String> moduleKeys) {
        if (CollectionUtils.isEmpty(moduleKeys)) {
            return new HashSet<>(2);
        }
        String key = moduleKeys.stream()
                .findFirst()
                .orElse(null);

        SystemConfig systemConfig = configurationService.query(TrantorContext.getTeamId(), key, ConfigType.System_Config);
        Set<String> res = new HashSet<>();
        res.addAll(systemConfig.getTopConfigs()
                .stream().map(SystemConfig.TopConfig::getName)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet()));
        systemConfig.getCommonConfigs().forEach((k, c) -> {
            if (c instanceof ArrayNode) {
                ArrayNode arrayNode = (ArrayNode) c;
                arrayNode.forEach(node -> {
                    if (node.has("name")) {
                        res.add(node.get("name").asText());
                    }
                });
            } else if (c instanceof ObjectNode && c.has("name")) {
                res.add(c.get("name").asText());
            }

        });
        return res;
    }

    @Override
    public Set<String> visit(Cond cond, @Nullable Predicate<String> predicate) {
        return new HashSet<>(2);
    }
}
