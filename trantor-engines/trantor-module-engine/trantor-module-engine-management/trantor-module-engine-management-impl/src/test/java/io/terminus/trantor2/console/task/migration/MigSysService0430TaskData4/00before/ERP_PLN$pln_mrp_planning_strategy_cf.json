{"type": "Model", "name": "计划策略配置", "parentKey": "ERP_PLN", "children": [], "props": {"desc": null, "alias": "ERP_PLN$pln_mrp_planning_strategy_cf", "props": {"type": "PERSIST", "config": {"self": false, "system": false, "persist": false, "selfRelationFieldAlias": null}, "mainField": "strategy_code", "tableName": "pln_mrp_planning_strategy_cf", "searchModel": false, "mainFieldAlias": "strategyCode", "physicalDelete": false, "shardingConfig": {"enabled": false, "shardingFields": null, "shardingSuffixLength": 3}, "originOrgIdEnabled": true}, "children": [{"ext": false, "key": "strategy_code", "name": "策略编码", "type": "DataStructField", "alias": "strategyCode", "props": {"length": 20, "unique": false, "comment": "策略编码", "required": true, "encrypted": false, "fieldType": "TEXT", "columnName": "strategy_code", "compositeKey": true, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "strategy_name", "name": "策略名称", "type": "DataStructField", "alias": "strategyName", "props": {"length": 20, "unique": false, "comment": "策略名称", "required": true, "encrypted": false, "fieldType": "TEXT", "columnName": "strategy_name", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "strategy_type", "name": "策略类型", "type": "DataStructField", "alias": "strategyType", "props": {"length": 256, "unique": false, "comment": "策略类型", "dictPros": {"dictValues": [{"label": "标准需求计划", "value": "STANDARD"}, {"label": "再订货点计划", "value": "REORDER"}, {"label": "不执行MRP", "value": "NONE"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "strategy_type", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "demand_period_type", "name": "需求期间", "type": "DataStructField", "alias": "demandPeriodType", "props": {"length": 256, "unique": false, "comment": "需求期间", "dictPros": {"dictValues": [{"label": "考虑全部期间需求", "value": "ALL"}, {"label": "进考虑提前期内", "value": "WITHIN_LEAD_TIME"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "demand_period_type", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "supply_item_list", "name": "供应元素配置列表", "type": "DataStructField", "alias": "supplyItemList", "props": {"unique": false, "comment": "供应元素配置列表", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "supply_item_list", "compositeKey": false, "relationMeta": {"sync": true, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "ERP_PLN$pln_mrp_planning_strategy_supply_item_cf", "relationModelKey": "ERP_PLN$pln_mrp_planning_strategy_supply_item_cf", "currentModelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "relationModelAlias": "ERP_PLN$pln_mrp_planning_strategy_supply_item_cf", "linkModelFieldAlias": "plnMrpPlanningStrategyCfId", "currentModelFieldAlias": "supplyItemList"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "demand_item_list", "name": "需求元素配置列表", "type": "DataStructField", "alias": "demandItemList", "props": {"unique": false, "comment": "需求元素配置列表", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "demand_item_list", "compositeKey": false, "relationMeta": {"sync": true, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "ERP_PLN$pln_mrp_planning_strategy_demand_item_cf", "relationModelKey": "ERP_PLN$pln_mrp_planning_strategy_demand_item_cf", "currentModelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "relationModelAlias": "ERP_PLN$pln_mrp_planning_strategy_demand_item_cf", "linkModelFieldAlias": "plnMrpPlanningStrategyCfId", "currentModelFieldAlias": "demandItemList"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "consumption_item_list", "name": "消耗配置列表", "type": "DataStructField", "alias": "consumptionItemList", "props": {"unique": false, "comment": "消耗配置列表", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "consumption_item_list", "compositeKey": false, "relationMeta": {"sync": true, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "ERP_PLN$pln_mrp_planning_strategy_consumption_item_cf", "relationModelKey": "ERP_PLN$pln_mrp_planning_strategy_consumption_item_cf", "currentModelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "relationModelAlias": "ERP_PLN$pln_mrp_planning_strategy_consumption_item_cf", "linkModelFieldAlias": "plnMrpPlanningStrategyCfId", "currentModelFieldAlias": "consumptionItemList"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "id", "name": "ID", "type": "DataStructField", "alias": "id", "props": {"length": 20, "unique": true, "comment": "ID", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "id", "compositeKey": false, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "created_by", "name": "创建人", "type": "DataStructField", "alias": "created<PERSON>y", "props": {"length": 20, "unique": true, "comment": "创建人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "created_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_PLN$user", "currentModelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "relationModelAlias": "ERP_PLN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "created<PERSON>y"}, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "updated_by", "name": "更新人", "type": "DataStructField", "alias": "updatedBy", "props": {"length": 20, "unique": true, "comment": "更新人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "updated_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_PLN$user", "currentModelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "relationModelAlias": "ERP_PLN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "updatedBy"}, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "created_at", "name": "创建时间", "type": "DataStructField", "alias": "createdAt", "props": {"unique": true, "comment": "创建时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "created_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "updated_at", "name": "更新时间", "type": "DataStructField", "alias": "updatedAt", "props": {"unique": true, "comment": "更新时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "updated_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "version", "name": "版本号", "type": "DataStructField", "alias": "version", "props": {"length": 20, "unique": true, "comment": "版本号", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "version", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "deleted", "name": "逻辑删除标识", "type": "DataStructField", "alias": "deleted", "props": {"length": 20, "unique": true, "comment": "逻辑删除标识", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "deleted", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "origin_org_id", "name": "所属组织", "type": "DataStructField", "alias": "originOrgId", "props": {"length": 20, "unique": true, "comment": "所属组织", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "origin_org_id", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}]}}