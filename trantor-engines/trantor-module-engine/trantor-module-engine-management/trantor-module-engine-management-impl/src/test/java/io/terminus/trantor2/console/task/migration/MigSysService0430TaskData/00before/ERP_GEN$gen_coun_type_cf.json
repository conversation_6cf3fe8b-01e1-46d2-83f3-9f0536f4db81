{"type": "Model", "name": "国家配置表", "parentKey": "ERP_GEN", "children": [], "props": {"desc": null, "alias": "ERP_GEN$gen_coun_type_cf", "props": {"type": "PERSIST", "config": {"self": false, "system": false, "persist": false, "selfRelationFieldAlias": null}, "mainField": "coun_name", "tableName": "gen_coun_type_cf", "searchModel": false, "mainFieldAlias": "coun<PERSON><PERSON>", "physicalDelete": false, "originOrgIdEnabled": true}, "children": [{"ext": false, "key": "coun_code", "name": "国家代码", "type": "DataStructField", "alias": "counCode", "appId": 46980, "props": {"length": 32, "unique": false, "comment": "国家代码", "required": true, "encrypted": false, "fieldType": "TEXT", "columnName": "coun_code", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "coun_name", "name": "名称", "type": "DataStructField", "alias": "coun<PERSON><PERSON>", "appId": 46980, "props": {"length": 64, "unique": false, "comment": "名称", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "coun_name", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "language_id", "name": "语言", "type": "DataStructField", "alias": "languageId", "appId": 46980, "props": {"unique": false, "comment": "语言", "required": true, "encrypted": false, "fieldType": "OBJECT", "columnName": "language_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_language_type_cf", "currentModelAlias": "ERP_GEN$gen_coun_type_cf", "relationModelAlias": "ERP_GEN$gen_language_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "languageId"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "curr_id", "name": "币种", "type": "DataStructField", "alias": "currId", "appId": 46980, "props": {"unique": false, "comment": "币种", "required": true, "encrypted": false, "fieldType": "OBJECT", "columnName": "curr_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_curr_type_cf", "currentModelAlias": "ERP_GEN$gen_coun_type_cf", "relationModelAlias": "ERP_GEN$gen_curr_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "currId"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "timezone_id", "name": "时区", "type": "DataStructField", "alias": "timezoneId", "appId": 46980, "props": {"length": 256, "unique": false, "comment": "时区", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "timezone_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_timezone_type_cf", "currentModelAlias": "ERP_GEN$gen_coun_type_cf", "relationModelAlias": "ERP_GEN$gen_timezone_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "timezoneId"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "default_relv", "name": "是否默认", "type": "DataStructField", "alias": "defaultRelv", "appId": 46980, "props": {"length": 1, "unique": false, "comment": "是否默认", "required": false, "encrypted": false, "fieldType": "BOOL", "columnName": "default_relv", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "id", "name": "ID", "type": "DataStructField", "alias": "id", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "ID", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "id", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "created_by", "name": "创建人", "type": "DataStructField", "alias": "created<PERSON>y", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "创建人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "created_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_coun_type_cf", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "created<PERSON>y"}, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "updated_by", "name": "更新人", "type": "DataStructField", "alias": "updatedBy", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "更新人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "updated_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_coun_type_cf", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "updatedBy"}, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "created_at", "name": "创建时间", "type": "DataStructField", "alias": "createdAt", "appId": 46980, "props": {"unique": true, "comment": "创建时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "created_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "updated_at", "name": "更新时间", "type": "DataStructField", "alias": "updatedAt", "appId": 46980, "props": {"unique": true, "comment": "更新时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "updated_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "version", "name": "版本号", "type": "DataStructField", "alias": "version", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "版本号", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "version", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "deleted", "name": "逻辑删除标识", "type": "DataStructField", "alias": "deleted", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "逻辑删除标识", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "deleted", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "origin_org_id", "name": "所属组织", "type": "DataStructField", "alias": "originOrgId", "appId": 46980, "props": {"unique": false, "comment": "所属组织", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "origin_org_id", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}]}}