{"access": "Public", "parentKey": "CON", "name": "搜索价格协议行事件", "type": "Event", "key": "CON$PRICE_AGT_ITEM_SEARCH_EVENT", "props": {"returnModel": {"key": "CON$con_pri_agreement_item_tr"}, "returnModelArrayWhether": false, "model": {"key": "CON$tsrm_price_agt_search_dto"}, "enabledStatusVerify": false, "modelArrayWhether": false, "enabledTransaction": true, "relations": [{"actionType": "Action", "code": "CON$TSRM_PRICE_AGT_SEARCH_ACTION", "enabledParamCheck": false}], "states": []}}