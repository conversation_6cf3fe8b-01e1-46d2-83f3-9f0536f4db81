{"type": "ServiceDefinition", "name": "现金流使用组织关联表-分页数据服务", "access": "Private", "parentKey": "ERP_FI", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "serviceDslJson": {"children": [{"id": null, "key": "node_1h9a9hm2n23", "name": "开始", "nextNodeKey": "node_1h9aa6nhd27", "props": {"globalVariable": null, "input": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "conditionGroup", "fieldKey": "conditionGroup", "fieldName": "条件组", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "conditionItems", "fieldKey": "conditionItems", "fieldName": "简化版条件组", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "element": null, "fieldAlias": "sortOrders", "fieldKey": "sortOrders", "fieldName": "字段排序", "fieldType": "Array", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "pageNo", "fieldKey": "pageNo", "fieldName": "页码", "fieldType": "Number", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "pageSize", "fieldKey": "pageSize", "fieldName": "每页数量", "fieldType": "Number", "id": null, "required": null}], "fieldAlias": "pageable", "fieldKey": "pageable", "fieldName": "pageable", "fieldType": "Pageable", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}], "output": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "total", "fieldKey": "total", "fieldName": "total", "fieldType": "Number", "id": null, "required": null}, {"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Model", "id": null, "modelKey": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "relatedModel": {"modelAlias": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "modelKey": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "modelName": "现金流使用组织关联表"}, "relation": null, "required": null}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null, "required": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Paging", "id": null, "required": null}], "type": "StartProperties"}, "type": "StartNode"}, {"id": null, "key": "node_1h9aa6nhd27", "name": "查询数据", "nextNodeKey": "node_1h9a9hm2n24", "props": {"conditionGroup": null, "dataType": "PAGING", "desensitized": true, "maximum": null, "outputAssign": {"customAssignments": [{"field": {"fieldType": "Paging", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "data", "valueName": "data"}]}, "id": "1h9pl7l121", "operator": "EQ", "value": {"fieldType": "Paging", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "NODE_OUTPUT_node_1h9aa6nhd27", "valueName": "出参结构体"}]}}], "outputAssignType": "CUSTOM"}, "pageable": {"fieldType": "Pageable", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "pageable", "valueName": "pageable"}]}, "queryModelFields": {"allFields": true, "modelKey": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "queryFields": null}, "relatedModel": {"modelAlias": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "modelKey": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "modelName": "现金流使用组织关联表"}, "sortOrders": [{"fieldAlias": "id", "sortType": "DESC"}], "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "type": "RetrieveDataNode"}, {"id": null, "key": "node_1h9a9hm2n24", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "headNodeKeys": ["node_1h9a9hm2n23"], "id": null, "input": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "conditionGroup", "fieldKey": "conditionGroup", "fieldName": "条件组", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "conditionItems", "fieldKey": "conditionItems", "fieldName": "简化版条件组", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "element": null, "fieldAlias": "sortOrders", "fieldKey": "sortOrders", "fieldName": "字段排序", "fieldType": "Array", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "pageNo", "fieldKey": "pageNo", "fieldName": "页码", "fieldType": "Number", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "pageSize", "fieldKey": "pageSize", "fieldName": "每页数量", "fieldType": "Number", "id": null, "required": null}], "fieldAlias": "pageable", "fieldKey": "pageable", "fieldName": "pageable", "fieldType": "Pageable", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}], "key": "ERP_FI$FIN_GLM_AB_CFT_ORG_ASS_CF_PAGING_DATA_SERVICE", "name": "现金流使用组织关联表-分页数据服务", "output": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "total", "fieldKey": "total", "fieldName": "total", "fieldType": "Number", "id": null, "required": null}, {"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Model", "id": null, "modelKey": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "relatedModel": {"modelAlias": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "modelKey": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "modelName": "现金流使用组织关联表"}, "relation": null, "required": null}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null, "required": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Paging", "id": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}}