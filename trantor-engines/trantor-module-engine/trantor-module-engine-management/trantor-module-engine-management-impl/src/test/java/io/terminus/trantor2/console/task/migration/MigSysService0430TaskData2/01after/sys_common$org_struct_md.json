{"type": "Model", "name": "组织架构表", "parentKey": "sys_common", "children": [], "props": {"desc": null, "alias": "sys_common$org_struct_md", "props": {"type": "PERSIST", "config": {"self": true, "system": false, "persist": false, "selfRelationFieldAlias": "comOrgId"}, "mainField": "org_name", "tableName": "org_struct_md", "searchModel": false, "mainFieldAlias": "orgName", "physicalDelete": false, "shardingConfig": {"enabled": false, "shardingFields": null, "shardingSuffixLength": 3}, "orderNumberEnabled": false, "originOrgIdEnabled": true}, "children": [{"ext": false, "key": "org_code", "name": "组织编码", "type": "DataStructField", "alias": "orgCode", "props": {"length": 64, "unique": false, "comment": "组织编码", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "org_code", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "org_name", "name": "组织名称", "type": "DataStructField", "alias": "orgName", "props": {"length": 128, "unique": false, "comment": "组织名称", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "org_name", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "org_enable_date", "name": "启用日期", "type": "DataStructField", "alias": "orgEnableDate", "props": {"unique": false, "comment": "启用日期", "required": false, "encrypted": false, "fieldType": "DATE", "columnName": "org_enable_date", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "org_status", "name": "状态", "type": "DataStructField", "alias": "orgStatus", "props": {"length": 256, "unique": false, "comment": "状态", "dictPros": {"dictValues": [{"label": "草稿", "value": "DRAFT"}, {"label": "已启用", "value": "ENABLED"}, {"label": "待启用", "value": "UNENABLED"}, {"label": "已停用", "value": "DISABLED"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "org_status", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "is_leaf", "name": "是否叶子节点", "type": "DataStructField", "alias": "<PERSON><PERSON><PERSON><PERSON>", "props": {"length": 1, "unique": false, "comment": "是否叶子节点", "required": false, "encrypted": false, "fieldType": "BOOL", "columnName": "is_leaf", "compositeKey": false, "defaultValue": true, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "def1", "name": "预留字段1", "type": "DataStructField", "alias": "def1", "props": {"length": 256, "unique": false, "comment": "预留字段1", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "def1", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "def2", "name": "预留字段2", "type": "DataStructField", "alias": "def2", "props": {"length": 256, "unique": false, "comment": "预留字段2", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "def2", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "def3", "name": "预留字段3", "type": "DataStructField", "alias": "def3", "props": {"length": 256, "unique": false, "comment": "预留字段3", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "def3", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "def4", "name": "预留字段4", "type": "DataStructField", "alias": "def4", "props": {"length": 256, "unique": false, "comment": "预留字段4", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "def4", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "def5", "name": "预留字段5", "type": "DataStructField", "alias": "def5", "props": {"length": 256, "unique": false, "comment": "预留字段5", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "def5", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "def6", "name": "预留字段6", "type": "DataStructField", "alias": "def6", "props": {"length": 256, "unique": false, "comment": "预留字段6", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "def6", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "def7", "name": "预留字段7", "type": "DataStructField", "alias": "def7", "props": {"length": 256, "unique": false, "comment": "预留字段7", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "def7", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "def8", "name": "预留字段8", "type": "DataStructField", "alias": "def8", "props": {"length": 256, "unique": false, "comment": "预留字段8", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "def8", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "def9", "name": "预留字段9", "type": "DataStructField", "alias": "def9", "props": {"length": 256, "unique": false, "comment": "预留字段9", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "def9", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "def10", "name": "预留字段10", "type": "DataStructField", "alias": "def10", "props": {"length": 256, "unique": false, "comment": "预留字段10", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "def10", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "def11", "name": "预留字段11", "type": "DataStructField", "alias": "def11", "props": {"length": 256, "unique": false, "comment": "预留字段11", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "def11", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "def12", "name": "预留字段12", "type": "DataStructField", "alias": "def12", "props": {"length": 256, "unique": false, "comment": "预留字段12", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "def12", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "def13", "name": "预留字段13", "type": "DataStructField", "alias": "def13", "props": {"length": 256, "unique": false, "comment": "预留字段13", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "def13", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "def14", "name": "预留字段14", "type": "DataStructField", "alias": "def14", "props": {"length": 256, "unique": false, "comment": "预留字段14", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "def14", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "def15", "name": "预留字段15", "type": "DataStructField", "alias": "def15", "props": {"length": 256, "unique": false, "comment": "预留字段15", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "def15", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "def16", "name": "预留字段16", "type": "DataStructField", "alias": "def16", "props": {"length": 256, "unique": false, "comment": "预留字段16", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "def16", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "def17", "name": "预留字段17", "type": "DataStructField", "alias": "def17", "props": {"length": 256, "unique": false, "comment": "预留字段17", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "def17", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "def18", "name": "预留字段18", "type": "DataStructField", "alias": "def18", "props": {"length": 256, "unique": false, "comment": "预留字段18", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "def18", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "def19", "name": "预留字段19", "type": "DataStructField", "alias": "def19", "props": {"length": 256, "unique": false, "comment": "预留字段19", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "def19", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "def20", "name": "预留字段20", "type": "DataStructField", "alias": "def20", "props": {"length": 256, "unique": false, "comment": "预留字段20", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "def20", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "org_dimension_code", "name": "组织维度编码", "type": "DataStructField", "alias": "orgDimensionCode", "props": {"length": 64, "unique": false, "comment": "组织维度编码", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "org_dimension_code", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "attachment1", "name": "附件预留字段1", "type": "DataStructField", "alias": "attachment1", "props": {"length": 4000, "unique": false, "comment": "附件预留字段1", "required": false, "encrypted": false, "fieldType": "ATTACHMENT", "columnName": "attachment1", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "attachmentProps": {"multi": true}}}, {"ext": false, "key": "attachment2", "name": "附件预留字段2", "type": "DataStructField", "alias": "attachment2", "props": {"length": 4000, "unique": false, "comment": "附件预留字段2", "required": false, "encrypted": false, "fieldType": "ATTACHMENT", "columnName": "attachment2", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "attachmentProps": {"multi": true}}}, {"ext": false, "key": "org_sort", "name": "排序", "type": "DataStructField", "alias": "orgSort", "props": {"unique": false, "comment": "排序", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "org_sort", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "org_business_type_code", "name": "业务类型编码", "type": "DataStructField", "alias": "orgBusinessTypeCode", "props": {"length": 64, "unique": false, "comment": "业务类型编码", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "org_business_type_code", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "org_business_type_id", "name": "业务类型", "type": "DataStructField", "alias": "orgBusinessTypeId", "props": {"unique": false, "comment": "业务类型", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "org_business_type_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "sys_common$org_business_type_cf", "currentModelAlias": "sys_common$org_struct_md", "relationModelAlias": "sys_common$org_business_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "orgBusinessTypeId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "org_dimension_id", "name": "组织维度", "type": "DataStructField", "alias": "orgDimensionId", "props": {"unique": false, "comment": "组织维度", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "org_dimension_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "sys_common$org_dimension_cf", "currentModelAlias": "sys_common$org_struct_md", "relationModelAlias": "sys_common$org_dimension_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "orgDimensionId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "org_parent_id", "name": "父组织", "type": "DataStructField", "alias": "orgParentId", "props": {"unique": false, "comment": "父组织", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "org_parent_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "sys_common$org_struct_md", "currentModelAlias": "sys_common$org_struct_md", "relationModelAlias": "sys_common$org_struct_md", "linkModelFieldAlias": null, "currentModelFieldAlias": "orgParentId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "partner_id", "name": "合作伙伴", "type": "DataStructField", "alias": "partnerId", "props": {"unique": false, "comment": "合作伙伴", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "partner_id", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "com_org_id", "name": "公司组织", "type": "DataStructField", "alias": "comOrgId", "props": {"unique": false, "comment": "公司组织", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "com_org_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "sys_common$org_struct_md", "currentModelAlias": "sys_common$org_struct_md", "relationModelAlias": "sys_common$org_struct_md", "linkModelFieldAlias": null, "currentModelFieldAlias": "comOrgId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "org_business_type_ids", "name": "业务类型集合", "type": "DataStructField", "alias": "orgBusinessTypeIds", "props": {"length": 256, "unique": false, "comment": "业务类型集合", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "org_business_type_ids", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "org_business_type_codes", "name": "业务类型编码集合", "type": "DataStructField", "alias": "orgBusinessTypeCodes", "props": {"length": 256, "unique": false, "comment": "业务类型编码集合", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "org_business_type_codes", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "id", "name": "ID", "type": "DataStructField", "alias": "id", "props": {"length": 20, "unique": true, "comment": "ID", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "id", "compositeKey": false, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "created_by", "name": "创建人", "type": "DataStructField", "alias": "created<PERSON>y", "props": {"length": 20, "unique": true, "comment": "创建人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "created_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "sys_common$user", "currentModelAlias": "sys_common$org_struct_md", "relationModelAlias": "sys_common$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "created<PERSON>y"}, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "updated_by", "name": "更新人", "type": "DataStructField", "alias": "updatedBy", "props": {"length": 20, "unique": true, "comment": "更新人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "updated_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "sys_common$user", "currentModelAlias": "sys_common$org_struct_md", "relationModelAlias": "sys_common$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "updatedBy"}, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "created_at", "name": "创建时间", "type": "DataStructField", "alias": "createdAt", "props": {"unique": true, "comment": "创建时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "created_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "updated_at", "name": "更新时间", "type": "DataStructField", "alias": "updatedAt", "props": {"unique": true, "comment": "更新时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "updated_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "version", "name": "版本号", "type": "DataStructField", "alias": "version", "props": {"length": 20, "unique": true, "comment": "版本号", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "version", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "deleted", "name": "逻辑删除标识", "type": "DataStructField", "alias": "deleted", "props": {"length": 20, "unique": true, "comment": "逻辑删除标识", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "deleted", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "origin_org_id", "name": "所属组织", "type": "DataStructField", "alias": "originOrgId", "props": {"length": 20, "unique": true, "comment": "所属组织", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "origin_org_id", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}]}}