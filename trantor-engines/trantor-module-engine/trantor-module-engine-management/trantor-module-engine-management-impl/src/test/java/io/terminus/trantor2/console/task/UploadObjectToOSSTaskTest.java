package io.terminus.trantor2.console.task;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.console.service.impl.SourceLibraryService;
import io.terminus.trantor2.meta.api.dto.Manifest;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskDefine;
import io.terminus.trantor2.meta.task.ResetIndexTask;
import io.terminus.trantor2.meta.task.SyncObjectTask;
import io.terminus.trantor2.module.util.OSSConstant;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Random;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.CALLS_REAL_METHODS;
import static org.mockito.Mockito.mockStatic;

/**
 * <AUTHOR>
 */
class UploadObjectToOSSTaskTest extends BaseTaskTests {

    @Autowired
    private SourceLibraryService sourceLibraryService;

    @Test
    void test() {
        testOne(false);
    }

    @Test
    void testV2() {
        testOne(true);
    }

    void testOne(boolean v2) {
        assertMetaState(teamId, "meta_test_state/upload_oss/base0");
        // make a first snapshot to make sure latter snapshot has `lastSnapshotOids`
        String base0_oid = doSnapshotV2();

        try (MockedStatic<UUID> u = mockStatic(UUID.class, CALLS_REAL_METHODS)) {
            u.when(UUID::randomUUID).thenReturn(new UUID(0, 0));
            MultipartFile file = new MockMultipartFile("file", "test.txt", "text/plain", "test".getBytes());
            TrantorContext.setModuleKey("module1");
            String url = sourceLibraryService.upload(file, OSSConstant.CONSOLE_FILE_PREFIX);
            TrantorContext.setModuleKey(null); // clear current moduleKey

            assertMetaState(teamId, "meta_test_state/upload_oss/upload01");

            String upload01_oid;
            if (v2) {
                upload01_oid = doSnapshotV2();
            } else {
                upload01_oid = doSnapshot();
            }
            String downloadUrl;
            {
                UploadObjectToOSSTask.Options uploadOpts = new UploadObjectToOSSTask.Options();
                uploadOpts.setRootOid(upload01_oid);
                uploadOpts.setV2(v2);
                TaskResult taskResult = runTask(
                        new TaskDefine(UploadObjectToOSSTask.class, uploadOpts, "", true),
                        new TaskContext(this.teamId, teamCode, userId, false)
                );
                assertEquals(1, taskResult.getResult().getFiles().size());
                assertEquals("下载", taskResult.getResult().getFiles().get(0).getName());
                if (v2) {
                    assertEquals("上传元数据到OSS (v2)", taskResult.getRunDisplayName());
                } else {
                    assertEquals("上传元数据到OSS", taskResult.getRunDisplayName());
                }

                List<String> outputs = taskResult.getOutputs();
                assertEquals(2, outputs.size());
                assertEquals("DownloadUrl:", outputs.get(0));
                downloadUrl = outputs.get(1);

                if (v2) {
                    Manifest manifest = assertArtifactState(downloadUrl, "artifact_test_state/upload_oss/upload01");
                    assertEquals(upload01_oid, manifest.getSnapshotOid());
                }
            }

            {
                Random r = new Random();
                long newTeamId = r.nextLong();
                String newTeamCode = "newTeamCode-" + newTeamId;
                createNewTeam(newTeamId, newTeamCode);

                SyncObjectTask.Options syncOpts = new SyncObjectTask.Options();
                syncOpts.setRootOid(upload01_oid);
                syncOpts.setDownloadUrl(downloadUrl);
                TaskResult taskResult = runTask(
                        new TaskDefine(SyncObjectTask.class, syncOpts, "", true),
                        new TaskContext(newTeamId, newTeamCode, userId, false)
                );

                List<String> outputs = taskResult.getOutputs();
                if (v2) {
                    assertEquals(13, outputs.size());
                    assertEquals("Manifest:", outputs.get(0));
                    assertEquals("- version: 2.5", outputs.get(1));
                    assertEquals("  snapshotOid: 4cf2caa35bda193f8477ece0df0138c35c82acbce02fe7f7a7574822904da1dd", outputs.get(2));
                    assertEquals("", outputs.get(3));
                    assertEquals("Library:", outputs.get(4));
                    assertEquals("- imported: trantor2/console/00000000-0000-0000-0000-000000000000/test.txt", outputs.get(5));
                    assertEquals("  contentType: text/plain", outputs.get(6));
                    assertEquals("", outputs.get(7));
                    assertEquals("Objects:", outputs.get(8));
                    assertEquals("- total: 4", outputs.get(9));
                    assertEquals("  alreadyExist: 1", outputs.get(10)); // root is already exist
                    assertEquals("  newCreated: 3", outputs.get(11));
                    assertEquals("  badOid: 0", outputs.get(12));
                } else {
                    assertEquals(4, outputs.size());
                    assertEquals("- total: 4", outputs.get(0));
                    assertEquals("  alreadyExist: 1", outputs.get(1));
                    assertEquals("  newCreated: 3", outputs.get(2));
                    assertEquals("  badOid: 0", outputs.get(3));
                }

                ResetIndexTask.Options resetIndexOpts = new ResetIndexTask.Options();
                resetIndexOpts.setRootOid(upload01_oid);
                runTask(
                        new TaskDefine(ResetIndexTask.class, resetIndexOpts, "", true),
                        new TaskContext(newTeamId, newTeamCode, userId, false)
                );

                assertMetaState(newTeamId, "meta_test_state/upload_oss/upload01");
            }
        }
    }
}
