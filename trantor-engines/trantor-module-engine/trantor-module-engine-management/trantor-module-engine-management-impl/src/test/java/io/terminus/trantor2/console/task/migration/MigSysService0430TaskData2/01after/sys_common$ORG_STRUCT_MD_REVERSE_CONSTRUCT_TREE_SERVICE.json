{"type": "ServiceDefinition", "name": "组织架构表-反向构建树服务", "access": "Public", "parentKey": "sys_common", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1hoobbnvl1", "name": "开始", "nextNodeKey": "node_1hookqur23", "props": {"globalVariable": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "treeNodeMap", "fieldKey": "treeNodeMap", "fieldName": "treeNodeMap", "fieldType": "Object", "id": null, "required": null}], "input": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Pageable", "id": null, "required": null}], "output": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Object", "id": null, "required": null}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null, "required": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1hookqur23", "name": "查询数据", "nextNodeKey": "node_1hop922a029", "props": {"conditionGroup": {"conditions": [], "id": "Cx3lxuQtNX5AWHbG8P7JD", "logicOperator": "OR", "type": "ConditionGroup"}, "dataConditionPermissionKey": "null", "dataType": "PAGING", "desensitized": true, "maximum": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pageable": {"fieldType": "Pageable", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "request", "valueName": "request"}]}, "queryModelFields": {"allFields": true, "modelKey": "sys_common$org_struct_md", "queryFields": null}, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "组织架构表"}, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "type": "RetrieveDataNode"}, {"children": [{"desc": null, "id": null, "key": "node_1hor502ps44", "name": "查询父节点", "props": {"inputMapping": [{"field": {"defaultValue": null, "description": null, "fieldAlias": "depth", "fieldKey": "depth", "fieldName": "depth", "fieldType": "Number", "id": null, "required": null}, "id": "1hor507u145", "value": {"constValue": "1", "fieldType": "Number", "id": null, "type": "ConstValue"}}, {"field": {"defaultValue": null, "description": null, "fieldAlias": "currentNode", "fieldKey": "currentNode", "fieldName": "currentNode", "fieldType": "Model", "id": null, "modelKey": "sys_common$org_struct_md", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "sys_common$org_struct_md"}, "relation": null, "required": null}, "id": "1hor507u146", "value": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "LOOP_node_1hop922a029", "valueName": "[循环]循环变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "组织架构表"}, "valueKey": "_item", "valueName": "循环元素"}]}}, {"field": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "treeNodeMap", "fieldKey": "treeNodeMap", "fieldName": "treeNodeMap", "fieldType": "Object", "id": null, "required": null}, "id": "1hor507u147", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "treeNodeMap", "valueName": "treeNodeMap"}]}}], "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "outputAssign": {"customAssignments": [{"field": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "treeNodeMap", "valueName": "treeNodeMap"}]}, "id": "1hor512sq48", "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_1hor502ps44", "valueName": "出参结构体"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}], "outputAssignType": "CUSTOM"}, "serviceKey": "sys_common$ORG_STRUCT_MD_REVERSE_CONSTRUCT_TREE_SERVICE_FIND_PARENT_NODES", "serviceName": "组织架构表-反向构建树-查询父节点服务", "transactionPropagation": "NOT_SUPPORTED", "type": "CallServiceProperties"}, "type": "CallServiceNode"}], "desc": null, "headNodeKeys": ["node_1hor502ps44"], "id": null, "key": "node_1hop922a029", "name": "循环", "nextNodeKey": "node_1hop9t5e737", "props": {"loopData": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "NODE_OUTPUT_node_1hookqur23", "valueName": "[查询数据]节点出参"}, {"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "组织架构表"}, "valueKey": "data", "valueName": "data"}]}, "loopElement": {"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "modelKey": "sys_common$org_struct_md", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "组织架构表"}, "relation": null, "required": null}, "loopType": "DATASET_LOOP", "stopWhenDataEmpty": false, "type": "LoopProperties"}, "type": "LoopNode"}, {"desc": null, "id": null, "key": "node_1hop9t5e737", "name": "获取 rootNodes", "nextNodeKey": "node_1hopbfpgg39", "props": {"inputMapping": [{"field": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "treeNodeMap", "fieldKey": "treeNodeMap", "fieldName": "treeNodeMap", "fieldType": "Object", "id": null, "required": null}, "id": "1hop9ua6k38", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "treeNodeMap", "valueName": "treeNodeMap"}]}}], "language": "JS", "output": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Text", "id": null, "required": null}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null, "required": null}], "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "script": "  var ArrayList = Java.type('java.util.ArrayList');  var HashMap = Java.type('java.util.HashMap');  var rootNodes = new ArrayList();  var uniqueMap = new HashMap();  for each (var element in treeNodeMap.entrySet()) {    var value = element.getValue();    if (value.get('orgParentId') === null && uniqueMap.get(value.get('id')) === null) {      rootNodes.add(value);      uniqueMap.put(value.get('id'), value);    }  }  return rootNodes;", "scriptEngine": null, "type": "ScriptProperties"}, "type": "ScriptNode"}, {"desc": null, "id": null, "key": "node_1hopbfpgg39", "name": "获取 childNodeWithParentIdKeyMap", "nextNodeKey": "node_1hopbko2441", "props": {"inputMapping": [{"field": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "treeNodeMap", "fieldKey": "treeNodeMap", "fieldName": "treeNodeMap", "fieldType": "Object", "id": null, "required": null}, "id": "1hopbgji840", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "treeNodeMap", "valueName": "treeNodeMap"}]}}], "language": "JS", "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "child_node_with_parent_id_key_map", "fieldKey": "child_node_with_parent_id_key_map", "fieldName": "childNodeWithParentIdKeyMap", "fieldType": "Object", "id": null, "required": null}], "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "script": "  var ArrayList = Java.type('java.util.ArrayList');  var HashMap = Java.type('java.util.HashMap');  var uniqueMap = new HashMap();  var childNodeWithParentIdKeyMap = new HashMap();  for each (var element in treeNodeMap.entrySet()) {    var value = element.getValue();    if (value.get('orgParentId') !== null && uniqueMap.get(value.get('id')) === null) {      var pid = value.get('orgParentId').get('id');      var childNodes = childNodeWithParentIdKeyMap.get(pid);      if (childNodes === null) {        childNodes = new ArrayList();      }      childNodes.add(value);      uniqueMap.put(value.get('id'), value);      childNodeWithParentIdKeyMap.put(pid, childNodes);    }  }  return childNodeWithParentIdKeyMap;", "scriptEngine": null, "type": "ScriptProperties"}, "type": "ScriptNode"}, {"desc": null, "id": null, "key": "node_1hopbko2441", "name": "递归建树", "nextNodeKey": "node_1hoobbnvl2", "props": {"inputMapping": [{"field": {"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Object", "id": null, "required": null}, "fieldAlias": "parent_nodes", "fieldKey": "parent_nodes", "fieldName": "parentNodes", "fieldType": "Array", "id": null, "required": null}, "id": "1hopblm4r44", "value": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "NODE_OUTPUT_node_1hop9t5e737", "valueName": "[获取 rootNodes]节点出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}, {"field": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "child_node_with_parent_id_key_map", "fieldKey": "child_node_with_parent_id_key_map", "fieldName": "childNodeWithParentIdKeyMap", "fieldType": "Object", "id": null, "required": null}, "id": "1hopblm4r45", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "NODE_OUTPUT_node_1hopbfpgg39", "valueName": "[获取 childNodeWithParentIdKeyMap]节点出参"}, {"fieldType": null, "valueKey": "child_node_with_parent_id_key_map", "valueName": "childNodeWithParentIdKeyMap"}]}}], "output": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Object", "id": null, "required": null}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null, "required": null}], "outputAssign": {"customAssignments": [{"field": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}, "id": "1hopbprio47", "operator": "EQ", "value": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_1hopbko2441", "valueName": "出参结构体"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}], "outputAssignType": "CUSTOM"}, "serviceKey": "sys_common$ORG_STRUCT_MD_REVERSE_CONSTRUCT_TREE_SERVICE_BUILD_TREE", "serviceName": "组织架构表-反向构建树-递归建树服务", "transactionPropagation": "NOT_SUPPORTED", "type": "CallServiceProperties"}, "type": "CallServiceNode"}, {"desc": null, "id": null, "key": "node_1hoobbnvl2", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "headNodeKeys": ["node_1hoobbnvl1"], "id": null, "input": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Pageable", "id": null, "required": null}], "key": "sys_common$ORG_STRUCT_MD_REVERSE_CONSTRUCT_TREE_SERVICE", "name": "组织架构表-反向构建树服务", "output": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Object", "id": null, "required": null}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "fieldRules": null, "permissionKey": "sys_common$ORG_STRUCT_MD_RETRIEVE_PERMISSION", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}}