{"type": "View", "name": "创建盘点计划", "parentKey": "ERP_SCM", "children": [], "props": {"conditionGroups": {"BJKMdjVFCiIZrNRNLRcD9": {"conditions": [{"conditions": [{"id": "Ta4jLeHC5vldrIpp-iRNC", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgBusinessTypeCode", "fieldName": "orgBusinessTypeCode", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgBusinessTypeCode", "valueName": "orgBusinessTypeCode"}]}, "operator": "EQ", "rightValue": {"constValue": "INV_ORG", "fieldType": "Text", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "jv9wq6SOLOTOlIbmEumaW", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgStatus", "fieldName": "orgStatus", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "2ZZBNbUMUxGI4ihFp6NRy", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "BJKMdjVFCiIZrNRNLRcD9", "logicOperator": "OR", "type": "ConditionGroup"}, "QkAY9u1bYJkadzZLnivTC": {"conditions": [{"conditions": [{"id": "M8Y4t9ixwj1gpNf2RCQta", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgDimensionId", "fieldName": "orgDimensionId", "fieldType": null, "modelAlias": null, "relatedModel": null}, {"fieldKey": "orgDimensionCode", "fieldName": "orgDimensionCode", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgDimensionId", "valueName": "orgDimensionId"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgDimensionCode", "valueName": "orgDimensionCode"}]}, "operator": "EQ", "rightValue": {"constValue": "SCM_ORG_GRP", "fieldType": "Text", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "Rr-lRVKiy0xvytl5dLn96", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgBusinessTypeCode", "fieldName": "orgBusinessTypeCode", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgBusinessTypeCode", "valueName": "orgBusinessTypeCode"}]}, "operator": "EQ", "rightValue": {"constValue": "INV_LOC", "fieldType": "Text", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "OdjeRKZwyOXwyjLi1dzDc", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgStatus", "fieldName": "orgStatus", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "J540ZcHRWIw42-3wlw5-i", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgParentId", "fieldName": "orgParentId", "fieldType": null, "modelAlias": null, "relatedModel": null}, {"fieldKey": "id", "fieldName": "id", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgParentId", "valueName": "orgParentId"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldPaths": [{"fieldKey": "invOrgId", "fieldName": "invOrgId", "fieldType": null, "modelAlias": null, "relatedModel": null}, {"fieldKey": "id", "fieldName": "id", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "invOrgId", "valueName": "invOrgId"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "id", "valueName": "id"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "EOGQeiTUDQwXLLlRrRW0U", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "QkAY9u1bYJkadzZLnivTC", "logicOperator": "OR", "type": "ConditionGroup"}, "xYR0BGWRHWgt5a8x6CCPp": {"conditions": [{"conditions": [{"id": "hz0ViDSnXrjvH4cJi-d1m", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgStatus", "fieldName": "orgStatus", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "Kzy1zPgw2z58iYTy1taH3", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgBusinessTypeCode", "fieldName": "orgBusinessTypeCode", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgBusinessTypeCode", "valueName": "orgBusinessTypeCode"}]}, "operator": "EQ", "rightValue": {"constValue": "COM_ORG", "fieldType": "Text", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "zO_5A7heYfxKSPovXLCfG", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "xYR0BGWRHWgt5a8x6CCPp", "logicOperator": "OR", "type": "ConditionGroup"}}, "containerSelect": {"ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md": [{"field": "version", "selectFields": null}, {"field": "planCode", "selectFields": null}, {"field": "planName", "selectFields": null}, {"field": "distributeRule", "selectFields": null}, {"field": "comOrgId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "orgName", "selectFields": null}]}, {"field": "effectDate", "selectFields": null}, {"field": "expireDate", "selectFields": null}, {"field": "cycleUnit", "selectFields": null}, {"field": "cycleNum", "selectFields": null}, {"field": "status", "selectFields": null}, {"field": "seq", "selectFields": null}, {"field": "lastCountDate", "selectFields": null}, {"field": "lastScheduleDate", "selectFields": null}, {"field": "nextScheduleDate", "selectFields": null}, {"field": "note", "selectFields": null}, {"field": "isAddRowAllowed", "selectFields": null}, {"field": "isZeroInvCount", "selectFields": null}, {"field": "isPostBlock", "selectFields": null}, {"field": "isTransitInvCount", "selectFields": null}, {"field": "isAutoApproveCountPlan", "selectFields": null}, {"field": "invOrgLink", "selectFields": [{"field": "invOrgId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "orgName", "selectFields": null}]}, {"field": "invLocId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "orgName", "selectFields": null}]}]}, {"field": "abcGroupItemLink", "selectFields": [{"field": "strategyGroupItemId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "groupName", "selectFields": null}]}, {"field": "countNum", "selectFields": null}, {"field": "countRatio", "selectFields": null}, {"field": "isToleranceControl", "selectFields": null}, {"field": "varianceTolerance", "selectFields": null}]}, {"field": "mustMatLink", "selectFields": [{"field": "strategyGroupItemId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "groupName", "selectFields": null}]}, {"field": "matId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "<PERSON><PERSON><PERSON>", "selectFields": null}]}, {"field": "isToleranceControl", "selectFields": null}, {"field": "varianceTolerance", "selectFields": null}]}]}, "content": {"children": [{"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑盘点计划\" : \"创建盘点计划\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-ID", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-创建人", "name": "FormField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "ERP_SCM$user", "parentModelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$user_PAGING_DATA_SERVICE"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-更新人", "name": "FormField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "ERP_SCM$user", "parentModelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$user_PAGING_DATA_SERVICE"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-创建时间", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-更新时间", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-版本号", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-逻辑删除标识", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-计划编码", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "planCode", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "initialValue": null, "label": "盘点计划编码", "lookup": [{"action": "get", "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": true, "readOnly": true, "required": false}, "key": "ERP_SCM$INV_INV_PLAN_VIEW-gBe2dOwwcQqU9hxmJkYhg", "operator": "SERVICE", "valueRules": {"scope": null, "serviceParams": {"entryNewParams": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_SCM$inv_count_strategy_md"}}, {"elements": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"type": "const", "value": "ERP_SCM$inv_count_plan"}}, {"fieldAlias": "formParams", "fieldName": "formParams", "fieldType": "Object"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "outParams": "data", "outType": null}, "title": "(系统)调用取号规则服务", "type": "SERVICE", "val": "ERP_SCM$INV_COUNT_STRATEGY_MD_INVOKE_CODE_RULE_SERVICE", "value": "ERP_SCM$INV_COUNT_STRATEGY_MD_INVOKE_CODE_RULE_SERVICE"}}], "name": "planCode", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-计划名称", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "planName", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "initialValue": null, "label": "盘点计划名称", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "ERP_SCM$INV_INV_PLAN_VIEW-_MiokZHEtwRSdS44aF_jA", "operator": null, "valueRules": null}], "name": "planName", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-分配规则", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "distributeRule", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "MultiSelect", "hidden": false, "initialValue": [], "label": "分配规则", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "ERP_SCM$INV_INV_PLAN_VIEW-gRSV7YzqD5qaM98iSubDe", "operator": null, "valueRules": null}], "name": "distributeRule", "rules": [], "type": "MULTISELECT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-公司", "name": "FormField", "props": {"componentProps": {"columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status"], "fieldAlias": "comOrgId", "label": "选择公司", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE"}}, "displayComponentProps": {"labelField": ["orgName"], "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "isRelationColumn": true, "label": "组织编码", "name": "orgCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "isRelationColumn": true, "label": "组织名称", "name": "orgName", "required": false, "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentType": "InputText", "filterType": "exact", "hidden": false, "label": "组织编码", "name": "orgCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织名称", "name": "orgName", "required": false, "type": "TEXT", "width": 120}], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "orgStatus", "fieldName": "orgStatus", "fieldType": "Text", "valueConfig": {"type": "const", "value": "ENABLED"}}, {"fieldAlias": "orgBusinessTypeCodes", "fieldName": "orgBusinessTypeCodes", "fieldType": "Text", "valueConfig": {"type": "const", "value": "COM_ORG"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "type": "InvokeService"}, "labelField": ["orgName"], "mainField": "name", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "sys_common$org_struct_md", "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "hz0ViDSnXrjvH4cJi-d1m", "leftValue": {"fieldType": "Enum", "title": "状态", "type": "VarValue", "val": "orgStatus", "value": "sys_common$org_struct_md.orgStatus", "valueType": "VAR", "varVal": "orgStatus", "varValue": [{"valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "Kzy1zPgw2z58iYTy1taH3", "leftValue": {"fieldType": "Text", "title": "业务类型编码", "type": "VarValue", "val": "orgBusinessTypeCode", "value": "sys_common$org_struct_md.orgBusinessTypeCode", "valueType": "VAR", "varVal": "orgBusinessTypeCode", "varValue": [{"valueKey": "orgBusinessTypeCode", "valueName": "orgBusinessTypeCode"}]}, "operator": "EQ", "rightValue": {"constValue": "COM_ORG", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "zO_5A7heYfxKSPovXLCfG", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "xYR0BGWRHWgt5a8x6CCPp", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": "$context"}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "公司", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "ERP_SCM$INV_INV_PLAN_VIEW-BTTMK9p1EpHRKw6LZM2nQ", "operator": null, "valueRules": null}], "name": "comOrgId", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-生效日期", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "effectDate", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请选择"}, "displayComponentType": "Date", "editComponentType": "DatePicker", "hidden": false, "initialValue": null, "label": "生效日期", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "ERP_SCM$INV_INV_PLAN_VIEW-G6lf6cAvNQDzqOrLjB6TY", "operator": null, "valueRules": null}], "name": "effectDate", "rules": [], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-失效日期", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "expireDate", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请选择"}, "displayComponentType": "Date", "editComponentType": "DatePicker", "hidden": false, "initialValue": null, "label": "失效日期", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "ERP_SCM$INV_INV_PLAN_VIEW-6W7y0Hb6fiZY-3WDm7n1o", "operator": null, "valueRules": null}], "name": "expireDate", "rules": [], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-周期单位", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "cycleUnit", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal", "precision": null}, "displayComponentType": "Enum", "editComponentProps": {}, "editComponentType": "Select", "hidden": false, "initialValue": null, "label": "周期单位", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "ERP_SCM$INV_INV_PLAN_VIEW-2QyP0kWe5dxT7oc7cv8gg", "operator": null, "valueRules": null}], "name": "cycleUnit", "rules": [], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-间隔周期", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "cycleNum", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请输入", "precision": null}, "displayComponentProps": {"precision": null}, "displayComponentType": "Number", "editComponentProps": {"precision": null, "shape": "line"}, "editComponentType": "InputNumber", "hidden": false, "initialValue": null, "label": "间隔周期", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "ERP_SCM$INV_INV_PLAN_VIEW-HZd_QOD8uuuCIVwGh3zyP", "operator": null, "valueRules": null}], "name": "cycleNum", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-状态", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "status", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal", "precision": null}, "displayComponentType": "Enum", "editComponentProps": {"precision": null}, "editComponentType": "Select", "hidden": false, "initialValue": "INACTIVE", "label": "状态", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": true, "readOnly": false, "required": false}, "key": "ERP_SCM$INV_INV_PLAN_VIEW-rLf0nU1rDmyupCj3SMHDV", "operator": null, "valueRules": null}], "name": "status", "rules": [], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-序号", "name": "FormField", "props": {"componentProps": {"defaultValue": 1, "fieldAlias": "seq", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请输入", "precision": null}, "displayComponentProps": {"precision": 0}, "displayComponentType": "Number", "editComponentProps": {"precision": 0, "shape": "line"}, "editComponentType": "InputNumber", "hidden": false, "initialValue": 1, "label": "序号", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": false, "required": false}, "key": "ERP_SCM$INV_INV_PLAN_VIEW-lb6QOHbByOVhS604uCkbK", "operator": null, "valueRules": null}], "name": "seq", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-最近盘点时间", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "lastCountDate", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请选择"}, "displayComponentType": "Date", "editComponentType": "DatePicker", "hidden": false, "initialValue": null, "label": "最近盘点时间", "name": "lastCountDate", "rules": [], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-上次计划日期", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "lastScheduleDate", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请选择"}, "displayComponentType": "Date", "editComponentType": "DatePicker", "hidden": false, "initialValue": null, "label": "上次计划日期", "name": "lastScheduleDate", "rules": [], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-下次计划日期", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "nextScheduleDate", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请选择"}, "displayComponentType": "Date", "editComponentType": "DatePicker", "hidden": false, "initialValue": null, "label": "下次计划日期", "lookup": [{"action": null, "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "ERP_SCM$INV_INV_PLAN_VIEW-WADUCs_gob6Hbqs6S-uy1", "operator": null, "valueRules": null}], "name": "nextScheduleDate", "rules": [], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-备注", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "note", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "initialValue": null, "label": "备注", "name": "note", "rules": [], "type": "TEXT"}, "type": "Widget"}], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "name": "FormGroupItem", "props": {"title": false}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-是否允许增加盘点行", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "isAddRowAllowed", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "hidden": false, "initialValue": null, "label": "是否允许增加盘点行", "name": "isAddRowAllowed", "rules": [], "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-零库存参与盘点", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "isZeroInvCount", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "hidden": false, "initialValue": null, "label": "零库存参与盘点", "name": "isZeroInvCount", "rules": [], "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-冻结出入库", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "isPostBlock", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "hidden": false, "initialValue": null, "label": "冻结出入库", "name": "isPostBlock", "rules": [], "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-在途物料不参与盘点", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "isTransitInvCount", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "hidden": false, "initialValue": null, "label": "在途物料不参与盘点", "name": "isTransitInvCount", "rules": [], "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-是否自动审核并生成物料", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "isAutoApproveCountPlan", "modelAlias": "ERP_SCM$inv_count_strategy_md", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "hidden": false, "initialValue": null, "label": "生成盘点方案时自动审核", "name": "isAutoApproveCountPlan", "rules": [], "type": "BOOL"}, "type": "Widget"}], "key": "ERP_SCM$INV_INV_PLAN_VIEW-Y8YSruI1X81lpkGvhJp15", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "条件设置"}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-invOrgLink-3-layout-meta-table-layout-BatchActions-1", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-LdX4GnsyrF14yikbgU9pG", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "SystemAction", "name": "remove"}]}, "confirmOn": "off", "label": "删除", "type": "default"}, "type": "Widget"}], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-invOrgLink-3-layout-meta-table-layout-BatchActions-2", "name": "RecordActions", "props": {"width": 120}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-byVa3r2cJz8GFciavEaK5", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_SCM$inv_count_strategy_inv_org_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "ID必填", "required": true}], "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-lkrABWnZDLb2H6v8xr1L6", "name": "Field", "props": {"componentProps": {"columns": ["code", "name", "comOrgId", "languageId", "area", "tele", "fax", "mail", "phone", "addressId", "addressDetail", "timezoneId", "postcode", "coordinate", "vendCode", "status", "purOrgId", "slsOrgId", "slsCustInfoId", "purVendInfoId", "orgSlsDcId"], "fieldAlias": "invOrgId", "label": "选择库存组织", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_SCM$inv_count_strategy_inv_org_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE"}}, "displayComponentProps": {"labelField": ["orgName"], "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"columns": ["orgDimensionCode", "orgDimensionName", "orgDimensionDescribe", "status"], "fieldAlias": "orgDimensionId", "label": "选择组织维度id", "labelField": "orgDimensionCode", "modelAlias": "sys_common$org_dimension_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$org_dimension_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "sys_common$org_dimension_cf_PAGING_DATA_SERVICE"}}, "hidden": true, "isRelationColumn": true, "label": "组织维度id", "name": "orgDimensionId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["code", "name", "attrClass", "status", "remark"], "fieldAlias": "attrGroupId", "label": "选择属性分组", "labelField": "name", "modelAlias": "ERP_GEN$gen_attr_group_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$gen_attr_group_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$gen_attr_group_cf_PAGING_DATA_SERVICE"}}, "hidden": true, "isRelationColumn": true, "label": "属性分组", "name": "attrGroupId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "组织编码", "name": "orgCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "组织名称", "name": "orgName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["orgDimensionId", "attrGroupId", "orgCode", "orgName", "orgParentId", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "attrGroupCode"], "fieldAlias": "orgParentId", "label": "选择父组织id", "labelField": "orgCode", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE"}}, "hidden": true, "isRelationColumn": true, "label": "父组织id", "name": "orgParentId", "required": false, "type": "SELFRELATION", "width": 120}, {"componentProps": {"fieldAlias": "orgEnableDate", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "启用日期", "name": "orgEnableDate", "required": false, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "orgStatus", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "状态", "name": "orgStatus", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"defaultValue": true, "fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "initialValue": true, "isRelationColumn": true, "label": "是否叶子节点", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"fieldAlias": "def1", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段1", "name": "def1", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def2", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段2", "name": "def2", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def3", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段3", "name": "def3", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def4", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段4", "name": "def4", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def5", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段5", "name": "def5", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def6", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段6", "name": "def6", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def7", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段7", "name": "def7", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def8", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段8", "name": "def8", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def9", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段9", "name": "def9", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def10", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段10", "name": "def10", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def11", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段11", "name": "def11", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def12", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段12", "name": "def12", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def13", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段13", "name": "def13", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def14", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段14", "name": "def14", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def15", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段15", "name": "def15", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def16", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段16", "name": "def16", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def17", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段17", "name": "def17", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def18", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段18", "name": "def18", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def19", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段19", "name": "def19", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def20", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段20", "name": "def20", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgDimensionCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "组织维度编码", "name": "orgDimensionCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "attachment1", "modelAlias": "sys_common$org_struct_md", "placeholder": "请上传"}, "hidden": true, "isRelationColumn": true, "label": "附件预留字段1", "name": "attachment1", "required": false, "type": "ATTACHMENT", "width": 120}, {"componentProps": {"fieldAlias": "attachment2", "modelAlias": "sys_common$org_struct_md", "placeholder": "请上传"}, "hidden": true, "isRelationColumn": true, "label": "附件预留字段2", "name": "attachment2", "required": false, "type": "ATTACHMENT", "width": 120}, {"componentProps": {"fieldAlias": "attrGroupCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "属性分组编码", "name": "attrGroupCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "id", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "ERP_SCM$user", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$user_PAGING_DATA_SERVICE"}}, "hidden": true, "isRelationColumn": true, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "ERP_SCM$user", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$user_PAGING_DATA_SERVICE"}}, "hidden": true, "isRelationColumn": true, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "orgBusinessTypeCodes", "fieldName": "orgBusinessTypeCodes", "fieldType": "Text", "valueConfig": {"type": "const", "value": "INV_ORG"}}, {"fieldAlias": "orgStatus", "fieldName": "orgStatus", "fieldType": "Text", "valueConfig": {"type": "const", "value": "ENABLED"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "type": "InvokeService"}, "labelField": ["orgName"], "modalProps": {"size": "middle", "width": 720}, "modelAlias": "sys_common$org_struct_md", "reverseConstructFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [], "fieldAlias": "request", "fieldName": "request", "fieldType": "Pageable"}], "serviceKey": "sys_common$ORG_STRUCT_MD_REVERSE_CONSTRUCT_TREE_SERVICE", "type": "InvokeService"}, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "Ta4jLeHC5vldrIpp-iRNC", "leftValue": {"fieldType": "Text", "title": "业务类型编码", "type": "VarValue", "val": "orgBusinessTypeCode", "value": "sys_common$org_struct_md.orgBusinessTypeCode", "valueType": "VAR", "varVal": "orgBusinessTypeCode", "varValue": [{"valueKey": "orgBusinessTypeCode", "valueName": "orgBusinessTypeCode"}]}, "operator": "EQ", "rightValue": {"constValue": "INV_ORG", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "jv9wq6SOLOTOlIbmEumaW", "leftValue": {"fieldType": "Enum", "title": "状态", "type": "VarValue", "val": "orgStatus", "value": "ERP_GEN$org_struct_md.orgStatus", "valueType": "VAR", "varVal": "orgStatus", "varValue": [{"valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "2ZZBNbUMUxGI4ihFp6NRy", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "BJKMdjVFCiIZrNRNLRcD9", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": "$context"}, "editComponentType": "RelationSelect", "hidden": false, "label": "库存组织", "name": "invOrgId", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-_SrzmLgy4HIm1pZnDJT85", "name": "Field", "props": {"componentProps": {"columns": ["code", "name", "languageId", "area", "tele", "fax", "mail", "phone", "addressId", "addressDetail", "timezoneId", "postcode", "coordinate", "orgId"], "fieldAlias": "invLocId", "label": "选择库存地点", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_SCM$inv_count_strategy_inv_org_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE"}}, "displayComponentProps": {"labelField": ["orgName"], "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"columns": ["orgDimensionCode", "orgDimensionName", "orgDimensionDescribe", "status"], "fieldAlias": "orgDimensionId", "label": "选择组织维度id", "labelField": "orgDimensionCode", "modelAlias": "sys_common$org_dimension_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$org_dimension_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "sys_common$org_dimension_cf_PAGING_DATA_SERVICE"}}, "hidden": true, "isRelationColumn": true, "label": "组织维度id", "name": "orgDimensionId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["code", "name", "attrClass", "status", "remark"], "fieldAlias": "attrGroupId", "label": "选择属性分组", "labelField": "name", "modelAlias": "ERP_GEN$gen_attr_group_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$gen_attr_group_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$gen_attr_group_cf_PAGING_DATA_SERVICE"}}, "hidden": true, "isRelationColumn": true, "label": "属性分组", "name": "attrGroupId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "isRelationColumn": true, "label": "组织编码", "name": "orgCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "isRelationColumn": true, "label": "组织名称", "name": "orgName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["orgDimensionId", "attrGroupId", "orgCode", "orgName", "orgParentId", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "attrGroupCode"], "fieldAlias": "orgParentId", "label": "选择父组织id", "labelField": "orgCode", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE"}}, "hidden": true, "isRelationColumn": true, "label": "父组织id", "name": "orgParentId", "required": false, "type": "SELFRELATION", "width": 120}, {"componentProps": {"fieldAlias": "orgEnableDate", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "启用日期", "name": "orgEnableDate", "required": false, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "orgStatus", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": false, "isRelationColumn": true, "label": "状态", "name": "orgStatus", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"defaultValue": true, "fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "initialValue": true, "isRelationColumn": true, "label": "是否叶子节点", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"fieldAlias": "def1", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段1", "name": "def1", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def2", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段2", "name": "def2", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def3", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段3", "name": "def3", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def4", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段4", "name": "def4", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def5", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段5", "name": "def5", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def6", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段6", "name": "def6", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def7", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段7", "name": "def7", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def8", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段8", "name": "def8", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def9", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段9", "name": "def9", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def10", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段10", "name": "def10", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def11", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段11", "name": "def11", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def12", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段12", "name": "def12", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def13", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段13", "name": "def13", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def14", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段14", "name": "def14", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def15", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段15", "name": "def15", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def16", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段16", "name": "def16", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def17", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段17", "name": "def17", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def18", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段18", "name": "def18", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def19", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段19", "name": "def19", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def20", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段20", "name": "def20", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgDimensionCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "组织维度编码", "name": "orgDimensionCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "attachment1", "modelAlias": "sys_common$org_struct_md", "placeholder": "请上传"}, "hidden": true, "isRelationColumn": true, "label": "附件预留字段1", "name": "attachment1", "required": false, "type": "ATTACHMENT", "width": 120}, {"componentProps": {"fieldAlias": "attachment2", "modelAlias": "sys_common$org_struct_md", "placeholder": "请上传"}, "hidden": true, "isRelationColumn": true, "label": "附件预留字段2", "name": "attachment2", "required": false, "type": "ATTACHMENT", "width": 120}, {"componentProps": {"fieldAlias": "attrGroupCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "属性分组编码", "name": "attrGroupCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "id", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "ERP_SCM$user", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$user_PAGING_DATA_SERVICE"}}, "hidden": true, "isRelationColumn": true, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "ERP_SCM$user", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$user_PAGING_DATA_SERVICE"}}, "hidden": true, "isRelationColumn": true, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "orgDimensionCode", "fieldName": "orgDimensionCode", "fieldType": "Text", "valueConfig": {"type": "const", "value": "SCM_ORG_GRP"}}, {"fieldAlias": "orgBusinessTypeCodes", "fieldName": "orgBusinessTypeCodes", "fieldType": "Text", "valueConfig": {"type": "const", "value": "INV_LOC"}}, {"fieldAlias": "orgStatus", "fieldName": "orgStatus", "fieldType": "Text", "valueConfig": {"type": "const", "value": "ENABLED"}}, {"fieldAlias": "parentId", "fieldName": "parentId", "fieldType": "Text", "valueConfig": {"action": {"name": "getData", "selector": "invOrgId.id", "target": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-invOrgLink-3-layout-meta-table"}, "type": "action"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "type": "InvokeService"}, "labelField": ["orgName"], "modalProps": {"size": "middle", "width": 720}, "modelAlias": "sys_common$org_struct_md", "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "M8Y4t9ixwj1gpNf2RCQta", "leftValue": {"fieldType": "Text", "title": "组织维度id.维度编码", "type": "VarValue", "val": "orgDimensionId.orgDimensionCode", "value": "ERP_GEN$org_struct_md.orgDimensionId.orgDimensionCode", "valueType": "VAR", "varVal": "orgDimensionId.orgDimensionCode", "varValue": [{"valueKey": "orgDimensionId", "valueName": "orgDimensionId"}, {"valueKey": "orgDimensionCode", "valueName": "orgDimensionCode"}]}, "operator": "EQ", "rightValue": {"constValue": "SCM_ORG_GRP", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "Rr-lRVKiy0xvytl5dLn96", "leftValue": {"fieldType": "Text", "title": "业务类型编码", "type": "VarValue", "val": "orgBusinessTypeCode", "value": "sys_common$org_struct_md.orgBusinessTypeCode", "valueType": "VAR", "varVal": "orgBusinessTypeCode", "varValue": [{"valueKey": "orgBusinessTypeCode", "valueName": "orgBusinessTypeCode"}]}, "operator": "EQ", "rightValue": {"constValue": "INV_LOC", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "OdjeRKZwyOXwyjLi1dzDc", "leftValue": {"fieldType": "Enum", "title": "状态", "type": "VarValue", "val": "orgStatus", "value": "ERP_GEN$org_struct_md.orgStatus", "valueType": "VAR", "varVal": "orgStatus", "varValue": [{"valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "J540ZcHRWIw42-3wlw5-i", "leftValue": {"fieldType": "Number", "title": "父组织.ID", "type": "VarValue", "val": "orgParentId.id", "value": "sys_common$org_struct_md.orgParentId.id", "valueType": "VAR", "varVal": "orgParentId.id", "varValue": [{"valueKey": "orgParentId", "valueName": "orgParentId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"fieldType": "Number", "scope": "row", "target": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-invOrgLink-3-layout-meta-table", "title": "invOrgId.id", "type": "VarValue", "val": "invOrgId.id", "value": "invOrgId.id", "valueType": "VAR", "varVal": "invOrgId.id", "varValue": [{"valueKey": "invOrgId", "valueName": "invOrgId"}, {"valueKey": "id", "valueName": "id"}]}, "type": "ConditionLeaf"}], "id": "EOGQeiTUDQwXLLlRrRW0U", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "QkAY9u1bYJkadzZLnivTC", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": "$context"}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "库存地点", "name": "invLocId", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}], "key": "ERP_SCM$INV_INV_PLAN_VIEW-0Vfpg0VLlvKRYxNUnCz_F", "name": "Fields", "props": {}, "type": "Meta"}], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-invOrgLink-3-layout-meta-table", "name": "TableForm", "props": {"creatorPosition": "bottom", "fieldName": "invOrgLink", "hideCreator": false, "hideDefaultDelete": true, "label": "表格表单", "modelAlias": "ERP_SCM$inv_count_strategy_inv_org_link_tr", "subTableEnabled": false}}], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-invOrgLink-3-layout-meta", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "invOrgLink"}, "type": "Meta"}], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-invOrgLink-for-3-layout", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "盘点范围-仓库范围"}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta-table-layout-BatchActions-1", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-8grWXg6zOvDUEvk4No43l", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "\n(function handle(){\n  const adjust = async ($, context, cb) => {\n  const sKey = 'TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta-table'\n  const tKey = 'TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta-table'\n  console.log('-----',context)\n  const { invokeServiceByKey } = context\n  const inP = $(sKey).action('getData')\n  const data = await invokeServiceByKey('ERP_SCM$INV_GET_COUNT_STRATEGY_MAT_EVENT_SERVICE', {\n    request: {abcGroupItemLinks: inP},\n  })\n  $(tKey).action('setData', data?.data || [])\n}\nsystemActions.remove($context)()\nadjust($, $context)\n})()"}, "confirmOn": "off", "label": "删除", "type": "default"}, "type": "Widget"}], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta-table-layout-BatchActions-2", "name": "RecordActions", "props": {"width": 120}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-L4Q0_xIK6vbsIGuCFwwNK", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_SCM$inv_count_strategy_group_item_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "ID必填", "required": true}], "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-fD2NPY9dpphrMqe5PvkFv", "name": "Field", "props": {"componentProps": {"columns": ["groupName", "groupRate", "groupValue", "countNum", "isToleranceControl", "varianceTolerance", "note", "invAbcGroupStrategyHeadCfId"], "fieldAlias": "strategyGroupItemId", "label": "选择方案分组行", "labelField": "groupName", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "parentModelAlias": "ERP_SCM$inv_count_strategy_group_item_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$inv_abc_group_strategy_item_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$inv_abc_group_strategy_item_cf_PAGING_DATA_SERVICE"}}, "displayComponentProps": {"labelField": "groupName", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "isRelationColumn": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "ERP_SCM$user", "parentModelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$user_PAGING_DATA_SERVICE"}}, "hidden": true, "initialValue": null, "isRelationColumn": true, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "ERP_SCM$user", "parentModelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$user_PAGING_DATA_SERVICE"}}, "hidden": true, "initialValue": null, "isRelationColumn": true, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "isRelationColumn": true, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "isRelationColumn": true, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["code", "name", "note", "status", "comOrgId", "dateFrom", "dateTo", "matTypeId", "groupIndex", "indexSetMode"], "fieldAlias": "invAbcGroupStrategyHeadCfId", "label": "选择invAbcGroupStrategyHeadCfId", "labelField": "code", "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "parentModelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$inv_abc_group_strategy_head_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$inv_abc_group_strategy_head_cf_PAGING_DATA_SERVICE"}}, "displayComponentProps": {"labelField": "name"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "ERP_SCM$user", "parentModelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$user_PAGING_DATA_SERVICE"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "ERP_SCM$user", "parentModelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$user_PAGING_DATA_SERVICE"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "code", "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "分组策略编码", "name": "code", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "name", "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "分组策略名称", "name": "name", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "note", "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "备注", "name": "note", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "status", "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "状态", "name": "status", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status", "counId"], "fieldAlias": "comOrgId", "label": "选择公司", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE"}}, "hidden": false, "initialValue": null, "label": "公司", "name": "comOrgId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "dateFrom", "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "开始日期", "name": "dateFrom", "required": false, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "dateTo", "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "结束日期", "name": "dateTo", "required": false, "type": "DATE", "width": 120}, {"componentProps": {"columns": ["matTypeCode", "matTypeName"], "fieldAlias": "matTypeId", "label": "选择物料类型", "labelField": "matTypeCode", "modelAlias": "ERP_GEN$gen_mat_type_cf", "parentModelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$gen_mat_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$gen_mat_type_cf_PAGING_DATA_SERVICE"}}, "hidden": false, "initialValue": null, "label": "物料类型", "name": "matTypeId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "groupIndex", "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "分组指标", "name": "groupIndex", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "indexSetMode", "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "指标设置方式", "name": "indexSetMode", "required": false, "type": "SELECT", "width": 120}], "labelField": "name", "mainField": "code", "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "分组策略名称", "name": "invAbcGroupStrategyHeadCfId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "groupName", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "分组名称", "name": "groupName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "groupRate", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "分组比例", "name": "groupRate", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "groupValue", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "分组数值", "name": "groupValue", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "countNum", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "盘点次数", "name": "countNum", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "isToleranceControl", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "是否控制容差", "name": "isToleranceControl", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "varianceTolerance", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请输入", "precision": null}, "displayComponentProps": {"precision": null}, "displayComponentType": "Number", "editComponentProps": {"precision": null, "shape": "line"}, "editComponentType": "InputNumber", "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "盘点容差", "name": "varianceTolerance", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "note", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "备注", "name": "note", "required": false, "type": "TEXT", "width": 120}], "flow": {}, "labelField": "groupName", "mainField": "groupName", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "events": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-XDxwDL2gLGeM-u6BjcmEs", "name": "onChange", "source": "const adjust = async ($, context, cb) => {\n  const sKey =\n    'ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta-table'\n  const tKey =\n    'ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-mustMatLink-for-3-layout'\n  const { invokeService } = context\n  const inP = $(sKey).action('getData')\n  console.log('---', context, inP)\n  const curRecord = context.record\n  const reInp = inP?.map((item) => {\n   if(curRecord._row_id_ === item._row_id_ && curRecord.strategyGroupItemId?.id !== item.strategyGroupItemId?.id){\n        return {\n          ...item,\n          isToleranceControl: item.strategyGroupItemId.isToleranceControl,\n          varianceTolerance:\n          item.varianceTolerance || item.strategyGroupItemId?.varianceTolerance,\n        }\n   }else{\n     return item\n   }\n  })\n  const data = await invokeService(\n    'ERP_SCM$INV_GET_COUNT_STRATEGY_MAT_EVENT_SERVICE',\n    {\n      request: {\n        abcGroupItemLinks: reInp,\n      },\n    },\n  )\n  $(tKey).action('setData', data?.data || [])\n}\n\nadjust($, $context)\n"}], "hidden": false, "initialValue": null, "label": "方案分组行", "name": "strategyGroupItemId", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-j<PERSON>han_89RQEvB5P7PiD4", "name": "Field", "props": {"componentProps": {"fieldAlias": "countNum", "modelAlias": "ERP_SCM$inv_count_strategy_group_item_link_tr", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "hidden": false, "label": "盘点次数（次/年）", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "ERP_SCM$INV_INV_PLAN_VIEW-NSUNtoAPlSDbavyaC5iOy", "valueRules": null}], "name": "countNum", "required": false, "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-ylWuHhk1GQONGusocRAdS", "name": "Field", "props": {"componentProps": {"fieldAlias": "countRatio", "modelAlias": "ERP_SCM$inv_count_strategy_group_item_link_tr", "placeholder": "请输入", "precision": 2}, "displayComponentType": "Number", "editComponentType": "InputNumber", "hidden": false, "label": "抽盘比例（%）", "name": "countRatio", "required": false, "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-CR4CD3Fq3L8zTIet7pKhR", "name": "Field", "props": {"componentProps": {"fieldAlias": "isToleranceControl", "modelAlias": "ERP_SCM$inv_count_strategy_group_item_link_tr", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "events": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-XDxwDL2gLGeM-u6BjcmEs", "name": "onChange", "source": "const adjust = async ($, context, cb) => {\n  const sKey = 'TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta-table'\n  const tKey = 'TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta-table'\n  \n  const { invokeService } = context\n  const inP = $(sKey).action('getData')\n  if(inP?.length){\n  const data = await invokeService('ERP_SCM$INV_GET_COUNT_STRATEGY_MAT_EVENT_SERVICE', {\n    request: {abcGroupItemLinks: inP},\n  })\n  $(tKey).action('setData', data?.data || [])  \n  }\n}\n\nadjust($, $context)"}], "hidden": false, "label": "是否控制容差", "name": "isToleranceControl", "required": false, "rules": [], "type": "BOOL", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-h8ttuhUzko3YTIbkcu_NY", "name": "Field", "props": {"componentProps": {"fieldAlias": "varianceTolerance", "modelAlias": "ERP_SCM$inv_count_strategy_group_item_link_tr", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentType": "InputNumber", "hidden": false, "label": "盘点容差", "name": "varianceTolerance", "required": false, "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}], "key": "ERP_SCM$INV_INV_PLAN_VIEW-3WUWe4h5KDckrll_r6voS", "name": "Fields", "props": {}, "type": "Meta"}], "displayName": "ABC分组", "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta-table", "name": "TableForm", "props": {"creatorPosition": "bottom", "fieldName": "abcGroupItemLink", "hideCreator": false, "hideDefaultDelete": true, "label": "表格表单", "modelAlias": "ERP_SCM$inv_count_strategy_group_item_link_tr", "subTableEnabled": false}}], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "abcGroupItemLink"}, "type": "Meta"}], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-abcGroupItemLink-for-3-layout", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "ABC分组"}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta-table-layout-BatchActions-1", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-CxVt-MPPnG1NdbQGKY1Bx", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "SystemAction", "name": "remove"}]}, "confirmOn": "off", "label": "删除", "type": "default"}, "type": "Widget"}], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta-table-layout-BatchActions-2", "name": "RecordActions", "props": {"width": 120}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-gaK0NHmqZSaoF_pHkUVao", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_SCM$inv_count_strategy_must_mat_link_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-3xonPOSIrxOvN200L0UCI", "name": "Field", "props": {"componentProps": {"columns": ["groupName", "groupRate", "groupValue", "countNum", "isToleranceControl", "varianceTolerance", "note", "invAbcGroupStrategyHeadCfId"], "fieldAlias": "strategyGroupItemId", "label": "选择abc分组方案行", "labelField": "groupName", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "parentModelAlias": "ERP_SCM$inv_count_strategy_must_mat_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$inv_abc_group_strategy_item_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$inv_abc_group_strategy_item_cf_PAGING_DATA_SERVICE"}}, "displayComponentProps": {"labelField": "groupName"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "ERP_SCM$user", "parentModelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$user_PAGING_DATA_SERVICE"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "ERP_SCM$user", "parentModelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$user_PAGING_DATA_SERVICE"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "groupName", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "分组名称", "name": "groupName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "groupRate", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "分组比例", "name": "groupRate", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "groupValue", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "分组数值", "name": "groupValue", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "countNum", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "盘点次数", "name": "countNum", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "isToleranceControl", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "是否控制容差", "name": "isToleranceControl", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "varianceTolerance", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "盘点容差", "name": "varianceTolerance", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "note", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "备注", "name": "note", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["code", "name", "note", "status", "comOrgId", "dateFrom", "dateTo", "matTypeId", "groupIndex", "indexSetMode"], "fieldAlias": "invAbcGroupStrategyHeadCfId", "label": "选择invAbcGroupStrategyHeadCfId", "labelField": "code", "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "parentModelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$inv_abc_group_strategy_head_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$inv_abc_group_strategy_head_cf_PAGING_DATA_SERVICE"}}, "hidden": true, "initialValue": null, "label": "invAbcGroupStrategyHeadCfId", "name": "invAbcGroupStrategyHeadCfId", "required": false, "type": "OBJECT", "width": 120}], "labelField": "groupName", "mainField": "groupName", "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "events": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-twJJ0vZl2rHgj7-R4D-5y", "name": "onChange", "source": "console.log(\"onChange fire\")"}], "hidden": false, "initialValue": null, "label": "abc分组方案行", "name": "strategyGroupItemId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-YRQAEnHxDa713RtrvFibq", "name": "Field", "props": {"componentProps": {"columns": ["matCode", "<PERSON><PERSON><PERSON>", "matOldId", "genMatTypeCfId", "baseUomId", "weightUomId", "grossWeight", "netWeight", "volumUomId", "matVolum", "cateId", "effectiveDate", "brandId", "attriId", "statusId", "batchRelv", "isBatchDetermination", "genCharaClassId", "shelfLife", "charas", "isAutoCountPlan"], "fieldAlias": "matId", "label": "选择物料", "labelField": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "parentModelAlias": "ERP_SCM$inv_count_strategy_must_mat_link_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$gen_mat_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$gen_mat_md_PAGING_DATA_SERVICE"}}, "hidden": false, "initialValue": null, "label": "物料", "name": "matId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-RyxdkdbS4PZ-wvO--9YhE", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "isToleranceControl", "modelAlias": "ERP_SCM$inv_count_strategy_must_mat_link_tr", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "是否控制容差", "name": "isToleranceControl", "required": false, "type": "BOOL", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-bkFzLml2npUHopo3g7t2z", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "varianceTolerance", "modelAlias": "ERP_SCM$inv_count_strategy_must_mat_link_tr", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "盘点容差", "name": "varianceTolerance", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}], "key": "ERP_SCM$INV_INV_PLAN_VIEW-KRdVi86fUmNIDjY4E0yj0", "name": "Fields", "props": {}, "type": "Meta"}], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta-table", "name": "TableForm", "props": {"creatorPosition": "bottom", "fieldName": "mustMatLink", "hideCreator": false, "hideDefaultDelete": true, "label": "表格表单", "modelAlias": "ERP_SCM$inv_count_strategy_must_mat_link_tr", "subTableEnabled": false}}], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "mustMatLink"}, "type": "Meta"}], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-mustMatLink-for-3-layout", "name": "FormGroupItem", "props": {"showSplit": true, "title": "必盘物料"}, "type": "Layout"}], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "name": "FormGroup", "props": {"colon": false, "flow": {"containerKey": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "modelAlias": "ERP_SCM$inv_count_strategy_md", "params$": "{ id: route.query?.copyId || route.recordId }", "serviceKey$": "route.query?.copyId ? \"ERP_SCM$SYS_CopyDataConverterService\" : \"ERP_SCM$SYS_FindDataByIdService\"", "test$": "route.action === \"edit\" || !!route.query?.copyId", "type": "InvokeSystemService"}, "modelAlias": "ERP_SCM$inv_count_strategy_md", "serviceKey": "ERP_SCM$SYS_FindDataByIdService"}, "type": "Container"}, {"children": [{"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView-footer-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "previous"}]}, "label": "取消"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView-footer-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindFlowConfig": {"params": [{"action": {"target": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md"}, "name": "request", "type": "action"}], "service": "ERP_SCM$INV_COUNT_STRATEGY_SAVE_EVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Message", "message": "保存成功"}, {"action": "OpenView", "openViewConfig": {"page": {"key": "ERP_SCM$INV_INV_PLAN_VIEW-list", "name": "list", "type": "View"}, "type": "NewPage"}}], "executeLogic": "BindFlow"}, "label": "保存", "type": "primary"}, "type": "Widget"}], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "name": "Page", "props": {"showBack": true, "showFooter": true, "showHeader": true}, "type": "Container"}, "frontendConfig": {"modules": ["base"]}, "key": "ERP_SCM$INV_INV_PLAN_VIEW-edit", "resources": [{"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView-footer-cancel", "label": "取消", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView-footer-save", "label": "保存", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}], "relations": [{"key": "ERP_SCM$INV_COUNT_STRATEGY_SAVE_EVENT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-ID", "label": "ID", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-创建人", "label": "创建人", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "ERP_SCM$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "Service"}, {"key": "ERP_SCM$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-更新人", "label": "更新人", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "ERP_SCM$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "Service"}, {"key": "ERP_SCM$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-创建时间", "label": "创建时间", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-更新时间", "label": "更新时间", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-版本号", "label": "版本号", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-逻辑删除标识", "label": "逻辑删除标识", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-计划编码", "label": "盘点计划编码", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "ERP_SCM$INV_COUNT_STRATEGY_MD_INVOKE_CODE_RULE_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$inv_count_strategy_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-计划名称", "label": "盘点计划名称", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-分配规则", "label": "分配规则", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-公司", "label": "公司", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}, {"key": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-生效日期", "label": "生效日期", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-失效日期", "label": "失效日期", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-周期单位", "label": "周期单位", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-间隔周期", "label": "间隔周期", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-状态", "label": "状态", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-序号", "label": "序号", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-最近盘点时间", "label": "最近盘点时间", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-上次计划日期", "label": "上次计划日期", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-下次计划日期", "label": "下次计划日期", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-备注", "label": "备注", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-是否允许增加盘点行", "label": "是否允许增加盘点行", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-Y8YSruI1X81lpkGvhJp15", "label": "条件设置", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-零库存参与盘点", "label": "零库存参与盘点", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-Y8YSruI1X81lpkGvhJp15", "label": "条件设置", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-冻结出入库", "label": "冻结出入库", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-Y8YSruI1X81lpkGvhJp15", "label": "条件设置", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-在途物料不参与盘点", "label": "在途物料不参与盘点", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-Y8YSruI1X81lpkGvhJp15", "label": "条件设置", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-widget-是否自动审核并生成物料", "label": "生成盘点方案时自动审核", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-Y8YSruI1X81lpkGvhJp15", "label": "条件设置", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-invOrgLink-3-layout-meta-table", "label": "表格表单", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-invOrgLink-for-3-layout", "label": "盘点范围-仓库范围", "type": "FormGroupItem"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-invOrgLink-3-layout-meta", "label": "自定义表单字段", "type": "CustomFormField"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta-table", "label": "表格表单", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-abcGroupItemLink-for-3-layout", "label": "ABC分组", "type": "FormGroupItem"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta", "label": "自定义表单字段", "type": "CustomFormField"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta-table", "label": "表格表单", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-mustMatLink-for-3-layout", "label": "必盘物料", "type": "FormGroupItem"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta", "label": "自定义表单字段", "type": "CustomFormField"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-LdX4GnsyrF14yikbgU9pG", "label": "删除", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-invOrgLink-for-3-layout", "label": "盘点范围-仓库范围", "type": "FormGroupItem"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-invOrgLink-3-layout-meta", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-invOrgLink-3-layout-meta-table", "label": "表格表单", "type": "TableForm"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-invOrgLink-3-layout-meta-table-layout-BatchActions-2", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-byVa3r2cJz8GFciavEaK5", "label": "ID", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-invOrgLink-for-3-layout", "label": "盘点范围-仓库范围", "type": "FormGroupItem"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-invOrgLink-3-layout-meta", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-invOrgLink-3-layout-meta-table", "label": "表格表单", "type": "TableForm"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-0Vfpg0VLlvKRYxNUnCz_F", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-lkrABWnZDLb2H6v8xr1L6", "label": "库存组织", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-invOrgLink-for-3-layout", "label": "盘点范围-仓库范围", "type": "FormGroupItem"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-invOrgLink-3-layout-meta", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-invOrgLink-3-layout-meta-table", "label": "表格表单", "type": "TableForm"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-0Vfpg0VLlvKRYxNUnCz_F", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}, {"key": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}, {"key": "sys_common$org_dimension_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_dimension_cf"}, "type": "Service"}, {"key": "sys_common$org_dimension_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_dimension_cf"}, "type": "Service"}, {"key": "ERP_GEN$gen_attr_group_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_attr_group_cf"}, "type": "Service"}, {"key": "ERP_GEN$gen_attr_group_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_attr_group_cf"}, "type": "Service"}, {"key": "ERP_SCM$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "Service"}, {"key": "ERP_SCM$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "Service"}, {"key": "sys_common$ORG_STRUCT_MD_REVERSE_CONSTRUCT_TREE_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-_SrzmLgy4HIm1pZnDJT85", "label": "库存地点", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-invOrgLink-for-3-layout", "label": "盘点范围-仓库范围", "type": "FormGroupItem"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-invOrgLink-3-layout-meta", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-invOrgLink-3-layout-meta-table", "label": "表格表单", "type": "TableForm"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-0Vfpg0VLlvKRYxNUnCz_F", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}, {"key": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}, {"key": "sys_common$org_dimension_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_dimension_cf"}, "type": "Service"}, {"key": "sys_common$org_dimension_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_dimension_cf"}, "type": "Service"}, {"key": "ERP_GEN$gen_attr_group_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_attr_group_cf"}, "type": "Service"}, {"key": "ERP_GEN$gen_attr_group_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_attr_group_cf"}, "type": "Service"}, {"key": "ERP_SCM$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "Service"}, {"key": "ERP_SCM$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-8grWXg6zOvDUEvk4No43l", "label": "删除", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-abcGroupItemLink-for-3-layout", "label": "ABC分组", "type": "FormGroupItem"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta-table", "label": "表格表单", "type": "TableForm"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta-table-layout-BatchActions-2", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-L4Q0_xIK6vbsIGuCFwwNK", "label": "ID", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-abcGroupItemLink-for-3-layout", "label": "ABC分组", "type": "FormGroupItem"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta-table", "label": "表格表单", "type": "TableForm"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-3WUWe4h5KDckrll_r6voS", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-fD2NPY9dpphrMqe5PvkFv", "label": "方案分组行", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-abcGroupItemLink-for-3-layout", "label": "ABC分组", "type": "FormGroupItem"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta-table", "label": "表格表单", "type": "TableForm"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-3WUWe4h5KDckrll_r6voS", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$inv_abc_group_strategy_item_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf"}, "type": "Service"}, {"key": "ERP_SCM$inv_abc_group_strategy_item_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf"}, "type": "Service"}, {"key": "ERP_SCM$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "Service"}, {"key": "ERP_SCM$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "Service"}, {"key": "ERP_SCM$inv_abc_group_strategy_head_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf"}, "type": "Service"}, {"key": "ERP_SCM$inv_abc_group_strategy_head_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf"}, "type": "Service"}, {"key": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}, {"key": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}, {"key": "ERP_GEN$gen_mat_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "Service"}, {"key": "ERP_GEN$gen_mat_type_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-j<PERSON>han_89RQEvB5P7PiD4", "label": "盘点次数（次/年）", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-abcGroupItemLink-for-3-layout", "label": "ABC分组", "type": "FormGroupItem"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta-table", "label": "表格表单", "type": "TableForm"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-3WUWe4h5KDckrll_r6voS", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-ylWuHhk1GQONGusocRAdS", "label": "抽盘比例（%）", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-abcGroupItemLink-for-3-layout", "label": "ABC分组", "type": "FormGroupItem"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta-table", "label": "表格表单", "type": "TableForm"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-3WUWe4h5KDckrll_r6voS", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-CR4CD3Fq3L8zTIet7pKhR", "label": "是否控制容差", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-abcGroupItemLink-for-3-layout", "label": "ABC分组", "type": "FormGroupItem"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta-table", "label": "表格表单", "type": "TableForm"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-3WUWe4h5KDckrll_r6voS", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-h8ttuhUzko3YTIbkcu_NY", "label": "盘点容差", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-abcGroupItemLink-for-3-layout", "label": "ABC分组", "type": "FormGroupItem"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-abcGroupItemLink-3-layout-meta-table", "label": "表格表单", "type": "TableForm"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-3WUWe4h5KDckrll_r6voS", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-CxVt-MPPnG1NdbQGKY1Bx", "label": "删除", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-mustMatLink-for-3-layout", "label": "必盘物料", "type": "FormGroupItem"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta-table", "label": "表格表单", "type": "TableForm"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta-table-layout-BatchActions-2", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-gaK0NHmqZSaoF_pHkUVao", "label": "ID", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-mustMatLink-for-3-layout", "label": "必盘物料", "type": "FormGroupItem"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta-table", "label": "表格表单", "type": "TableForm"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-KRdVi86fUmNIDjY4E0yj0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-3xonPOSIrxOvN200L0UCI", "label": "abc分组方案行", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-mustMatLink-for-3-layout", "label": "必盘物料", "type": "FormGroupItem"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta-table", "label": "表格表单", "type": "TableForm"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-KRdVi86fUmNIDjY4E0yj0", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$inv_abc_group_strategy_item_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf"}, "type": "Service"}, {"key": "ERP_SCM$inv_abc_group_strategy_item_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf"}, "type": "Service"}, {"key": "ERP_SCM$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "Service"}, {"key": "ERP_SCM$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "Service"}, {"key": "ERP_SCM$inv_abc_group_strategy_head_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf"}, "type": "Service"}, {"key": "ERP_SCM$inv_abc_group_strategy_head_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-YRQAEnHxDa713RtrvFibq", "label": "物料", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-mustMatLink-for-3-layout", "label": "必盘物料", "type": "FormGroupItem"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta-table", "label": "表格表单", "type": "TableForm"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-KRdVi86fUmNIDjY4E0yj0", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$gen_mat_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "Service"}, {"key": "ERP_GEN$gen_mat_md_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-RyxdkdbS4PZ-wvO--9YhE", "label": "是否控制容差", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-mustMatLink-for-3-layout", "label": "必盘物料", "type": "FormGroupItem"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta-table", "label": "表格表单", "type": "TableForm"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-KRdVi86fUmNIDjY4E0yj0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$INV_INV_PLAN_VIEW-bkFzLml2npUHopo3g7t2z", "label": "盘点容差", "path": [{"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-TERP_MIGRATE$inv_count_strategy_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-mustMatLink-for-3-layout", "label": "必盘物料", "type": "FormGroupItem"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-TERP_MIGRATE$count_plan2-total-config-container-TERP_MIGRATE$inv_count_strategy_md-for-mustMatLink-3-layout-meta-table", "label": "表格表单", "type": "TableForm"}, {"key": "ERP_SCM$INV_INV_PLAN_VIEW-KRdVi86fUmNIDjY4E0yj0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}], "title": "edit", "type": "FORM"}}