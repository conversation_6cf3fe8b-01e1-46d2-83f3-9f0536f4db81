{"parentKey": "CON", "name": "价格协议-根据协议行ids批量查询价格协议行", "type": "Action", "key": "CON$TSRM_PRICE_AGTITEM_FIND_BYIDS_ACTION", "props": {"responseType": "io.terminus.common.api.model.Paging<io.terminus.tsrm.price.spi.model.agreement.dto.PriceAgreementItemDTO>", "languageType": "Java", "method": "findAgtItemByIds", "requestType": "java.util.List<io.terminus.tsrm.price.spi.model.agreement.dto.PriceAgreementItemDTO>", "bean": "PriceAgreementAction", "order": 10, "status": "enabled"}}