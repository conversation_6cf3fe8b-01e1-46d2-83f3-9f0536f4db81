{"access": "Private", "parentKey": "CON", "name": "更新阶梯价事件", "type": "Event", "key": "CON$PRICE_TIERED_UPDATE_ONE_EVENT", "props": {"returnModelArrayWhether": false, "model": {"key": "CON$con_pri_agreement_item_tr"}, "enabledStatusVerify": false, "modelArrayWhether": false, "enabledTransaction": true, "relations": [{"actionType": "Action", "code": "CON$TSRM_PRICE_TIERED_UPDATE_ACTION", "enabledParamCheck": false}], "states": []}}