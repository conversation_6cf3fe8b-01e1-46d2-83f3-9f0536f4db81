{"access": "Private", "children": [], "key": "ERP_SCM$pur_pr_auto_match_agreement_rule_head_md", "name": "自动匹配协议规则抬头", "parentKey": "ERP_SCM", "props": {"alias": "ERP_SCM$pur_pr_auto_match_agreement_rule_head_md", "children": [{"alias": "ruleCode", "appId": 47014, "ext": false, "key": "rule_code", "name": "规则编码", "props": {"autoGenerated": false, "columnName": "rule_code", "comment": "规则编码", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 256, "required": false, "unique": false}, "teamId": 22, "type": "DataStructField"}, {"alias": "priority", "appId": 47014, "ext": false, "key": "priority", "name": "优先级", "props": {"autoGenerated": false, "columnName": "priority", "comment": "优先级", "compositeKey": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": false, "numberDisplayType": "digit", "required": false, "unique": false}, "teamId": 22, "type": "DataStructField"}, {"alias": "fromDate", "appId": 47014, "ext": false, "key": "from_date", "name": "有效期从", "props": {"autoGenerated": false, "columnName": "from_date", "comment": "有效期从", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "isSystemField": false, "required": false, "unique": false}, "teamId": 22, "type": "DataStructField"}, {"alias": "toDate", "appId": 47014, "ext": false, "key": "to_date", "name": "有效期至", "props": {"autoGenerated": false, "columnName": "to_date", "comment": "有效期至", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "isSystemField": false, "required": false, "unique": false}, "teamId": 22, "type": "DataStructField"}, {"alias": "status", "appId": 47014, "ext": false, "key": "status", "name": "状态", "props": {"autoGenerated": false, "columnName": "status", "comment": "状态", "compositeKey": false, "dictPros": {"dictValues": [{"label": "已启用", "value": "ENABLED"}, {"label": "已停用", "value": "DISENABLED"}, {"label": "已删除", "value": "DELETED"}, {"label": "未启用", "value": "UNENABLED"}, {"label": "草稿态", "value": "DRAFT"}], "multiSelect": false, "properties": null}, "encrypted": false, "fieldType": "ENUM", "isSystemField": false, "length": 256, "required": false, "unique": false}, "teamId": 22, "type": "DataStructField"}, {"alias": "ruleItem", "appId": 47014, "ext": false, "key": "rule_item", "name": "匹配协议规则行", "props": {"autoGenerated": false, "columnName": "rule_item", "comment": "匹配协议规则行", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_pr_auto_match_agreement_rule_head_md", "currentModelFieldAlias": "ruleItem", "linkModelAlias": "ERP_SCM$pur_pr_auto_match_agreement_rule_item_md", "linkModelFieldAlias": "purPrAutoMatchAgreementRuleHeadMdId", "relationKey": null, "relationModelAlias": "ERP_SCM$pur_pr_auto_match_agreement_rule_item_md", "relationModelKey": "ERP_SCM$pur_pr_auto_match_agreement_rule_item_md", "relationType": "PARENT_CHILD", "sync": true}, "required": false, "unique": false}, "teamId": 22, "type": "DataStructField"}, {"alias": "conditionType", "appId": 47014, "ext": false, "key": "condition_type", "name": "条件", "props": {"autoGenerated": false, "columnName": "condition_type", "comment": "条件", "compositeKey": false, "dictPros": {"dictValues": [{"label": "物料", "value": "MAT"}, {"label": "类目", "value": "MAT_CATE"}, {"label": "库存组织", "value": "INV_ORG"}, {"label": "需求组织", "value": "REQ_ORG"}, {"label": "采购组织", "value": "PUR_ORG"}], "multiSelect": true, "properties": null}, "encrypted": false, "fieldType": "ENUM", "isSystemField": false, "length": 256, "required": false, "unique": false}, "teamId": 22, "type": "DataStructField"}, {"alias": "id", "appId": 47014, "ext": false, "key": "id", "name": "ID", "props": {"autoGenerated": false, "columnName": "id", "comment": "ID", "compositeKey": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": true, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "created<PERSON>y", "appId": 47014, "ext": false, "key": "created_by", "name": "创建人", "props": {"autoGenerated": false, "columnName": "created_by", "comment": "创建人", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_pr_auto_match_agreement_rule_head_md", "currentModelFieldAlias": "created<PERSON>y", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "ERP_SCM$user", "relationModelKey": "ERP_SCM$user", "relationType": "LINK", "sync": false}, "required": false, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "updatedBy", "appId": 47014, "ext": false, "key": "updated_by", "name": "更新人", "props": {"autoGenerated": false, "columnName": "updated_by", "comment": "更新人", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"currentModelAlias": "ERP_SCM$pur_pr_auto_match_agreement_rule_head_md", "currentModelFieldAlias": "updatedBy", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "ERP_SCM$user", "relationModelKey": "ERP_SCM$user", "relationType": "LINK", "sync": false}, "required": false, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "createdAt", "appId": 47014, "ext": false, "key": "created_at", "name": "创建时间", "props": {"autoGenerated": false, "columnName": "created_at", "comment": "创建时间", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "isSystemField": true, "required": true, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "updatedAt", "appId": 47014, "ext": false, "key": "updated_at", "name": "更新时间", "props": {"autoGenerated": false, "columnName": "updated_at", "comment": "更新时间", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "isSystemField": true, "required": true, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "version", "appId": 47014, "ext": false, "key": "version", "name": "版本号", "props": {"autoGenerated": false, "columnName": "version", "comment": "版本号", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": true, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "deleted", "appId": 47014, "ext": false, "key": "deleted", "name": "逻辑删除标识", "props": {"autoGenerated": false, "columnName": "deleted", "comment": "逻辑删除标识", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": true, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "originOrgId", "appId": 47014, "ext": false, "key": "origin_org_id", "name": "所属组织", "props": {"autoGenerated": false, "columnName": "origin_org_id", "comment": "所属组织", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": false, "unique": true}, "teamId": 22, "type": "DataStructField"}], "desc": null, "props": {"config": {"persist": false, "self": false, "selfRelationFieldAlias": null, "system": false}, "mainField": "rule_code", "mainFieldAlias": "ruleCode", "originOrgIdEnabled": true, "physicalDelete": false, "searchModel": false, "shardingConfig": {"enabled": false, "shardingFields": null, "shardingSuffixLength": 3}, "tableName": "pur_pr_auto_match_agreement_rule_head_md", "type": "PERSIST"}}, "type": "Model"}