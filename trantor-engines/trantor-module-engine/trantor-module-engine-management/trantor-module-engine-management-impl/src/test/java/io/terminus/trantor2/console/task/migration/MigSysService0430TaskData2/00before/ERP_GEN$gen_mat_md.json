{"type": "Model", "name": "物料主数据定义表", "parentKey": "ERP_GEN", "children": [], "props": {"desc": null, "alias": "ERP_GEN$gen_mat_md", "props": {"type": "PERSIST", "config": {"self": false, "system": false, "persist": false, "selfRelationFieldAlias": null}, "mainField": "mat_name", "tableName": "gen_mat_md", "searchModel": false, "mainFieldAlias": "<PERSON><PERSON><PERSON>", "physicalDelete": false, "shardingConfig": {"enabled": false, "shardingFields": null, "shardingSuffixLength": 3}, "originOrgIdEnabled": true}, "children": [{"ext": false, "key": "mat_code", "name": "物料编码", "type": "DataStructField", "alias": "matCode", "props": {"length": 32, "unique": false, "comment": "物料编码", "required": true, "encrypted": false, "fieldType": "TEXT", "columnName": "mat_code", "compositeKey": true, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "barcode", "name": "物料条码", "type": "DataStructField", "alias": "barcode", "props": {"length": 32, "unique": false, "comment": "物料条码", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "barcode", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "mat_name", "name": "物料名称", "type": "DataStructField", "alias": "<PERSON><PERSON><PERSON>", "props": {"length": 128, "unique": false, "comment": "物料名称", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "mat_name", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "gen_mat_type_cf_id", "name": "物料类型", "type": "DataStructField", "alias": "genMatTypeCfId", "props": {"unique": false, "comment": "物料类型", "required": true, "encrypted": false, "fieldType": "OBJECT", "columnName": "gen_mat_type_cf_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_mat_type_cf", "currentModelAlias": "ERP_GEN$gen_mat_md", "relationModelAlias": "ERP_GEN$gen_mat_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "genMatTypeCfId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "base_uom_id", "name": "基本计量单位", "type": "DataStructField", "alias": "baseUomId", "props": {"unique": false, "comment": "基本计量单位", "required": true, "encrypted": false, "fieldType": "OBJECT", "columnName": "base_uom_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_uom_type_cf", "currentModelAlias": "ERP_GEN$gen_mat_md", "relationModelAlias": "ERP_GEN$gen_uom_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "baseUomId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "weight_uom_id", "name": "重量单位", "type": "DataStructField", "alias": "weightUomId", "props": {"unique": false, "comment": "重量单位", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "weight_uom_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_uom_type_cf", "currentModelAlias": "ERP_GEN$gen_mat_md", "relationModelAlias": "ERP_GEN$gen_uom_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "weightUomId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "gross_weight", "name": "毛重", "type": "DataStructField", "alias": "grossWeight", "props": {"unique": false, "comment": "毛重", "required": false, "encrypted": false, "fieldType": "NUMBER", "columnName": "gross_weight", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "net_weight", "name": "净重", "type": "DataStructField", "alias": "netWeight", "props": {"unique": false, "comment": "净重", "required": false, "encrypted": false, "fieldType": "NUMBER", "columnName": "net_weight", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "volum_uom_id", "name": "体积单位", "type": "DataStructField", "alias": "volumUomId", "props": {"unique": false, "comment": "体积单位", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "volum_uom_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_uom_type_cf", "currentModelAlias": "ERP_GEN$gen_mat_md", "relationModelAlias": "ERP_GEN$gen_uom_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "volumUomId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "mat_volum", "name": "体积", "type": "DataStructField", "alias": "matVolum", "props": {"unique": false, "comment": "体积", "required": false, "encrypted": false, "fieldType": "NUMBER", "columnName": "mat_volum", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "cate_id", "name": "类目", "type": "DataStructField", "alias": "cateId", "props": {"unique": false, "comment": "类目", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "cate_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_mat_cate_md", "currentModelAlias": "ERP_GEN$gen_mat_md", "relationModelAlias": "ERP_GEN$gen_mat_cate_md", "linkModelFieldAlias": null, "currentModelFieldAlias": "cateId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "brand_id", "name": "品牌", "type": "DataStructField", "alias": "brandId", "props": {"unique": false, "comment": "品牌", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "brand_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_brand_md", "currentModelAlias": "ERP_GEN$gen_mat_md", "relationModelAlias": "ERP_GEN$gen_brand_md", "linkModelFieldAlias": null, "currentModelFieldAlias": "brandId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "status_id", "name": "跨工厂物料状态", "type": "DataStructField", "alias": "statusId", "props": {"unique": false, "comment": "跨工厂物料状态", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "status_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_mat_status_type_cf", "currentModelAlias": "ERP_GEN$gen_mat_md", "relationModelAlias": "ERP_GEN$gen_mat_status_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "statusId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "mat_sls_id", "name": "销售物料视图", "type": "DataStructField", "alias": "matSlsId", "props": {"unique": false, "comment": "销售物料视图", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "mat_sls_id", "compositeKey": false, "relationMeta": {"sync": true, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "ERP_GEN$gen_mat_sls_md", "relationModelKey": "ERP_GEN$gen_mat_sls_md", "currentModelAlias": "ERP_GEN$gen_mat_md", "relationModelAlias": "ERP_GEN$gen_mat_sls_md", "linkModelFieldAlias": "genMatMdId", "currentModelFieldAlias": "matSlsId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "mat_pur_id", "name": "采购物料视图", "type": "DataStructField", "alias": "mat<PERSON>ur<PERSON>d", "props": {"unique": false, "comment": "采购物料视图", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "mat_pur_id", "compositeKey": false, "relationMeta": {"sync": true, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "ERP_GEN$gen_mat_pur_md", "relationModelKey": "ERP_GEN$gen_mat_pur_md", "currentModelAlias": "ERP_GEN$gen_mat_md", "relationModelAlias": "ERP_GEN$gen_mat_pur_md", "linkModelFieldAlias": "genMatMdId", "currentModelFieldAlias": "mat<PERSON>ur<PERSON>d"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "mat_inv_id", "name": "存储物料视图", "type": "DataStructField", "alias": "matInvId", "props": {"unique": false, "comment": "存储物料视图", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "mat_inv_id", "compositeKey": false, "relationMeta": {"sync": true, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "ERP_GEN$gen_mat_inv_md", "relationModelKey": "ERP_GEN$gen_mat_inv_md", "currentModelAlias": "ERP_GEN$gen_mat_md", "relationModelAlias": "ERP_GEN$gen_mat_inv_md", "linkModelFieldAlias": "genMatMdId", "currentModelFieldAlias": "matInvId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "mat_mrp_id", "name": "MRP物料视图", "type": "DataStructField", "alias": "matMrpId", "props": {"unique": false, "comment": "MRP物料视图", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "mat_mrp_id", "compositeKey": false, "relationMeta": {"sync": true, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "ERP_GEN$gen_mat_mrp_md", "relationModelKey": "ERP_GEN$gen_mat_mrp_md", "currentModelAlias": "ERP_GEN$gen_mat_md", "relationModelAlias": "ERP_GEN$gen_mat_mrp_md", "linkModelFieldAlias": "genMatMdId", "currentModelFieldAlias": "matMrpId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "uom_formula_type_id", "name": "物料单位转化", "type": "DataStructField", "alias": "uomFormulaTypeId", "props": {"unique": false, "comment": "物料单位转化", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "uom_formula_type_id", "compositeKey": false, "relationMeta": {"sync": true, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "ERP_GEN$gen_uom_formula_type_cf", "relationModelKey": "ERP_GEN$gen_uom_formula_type_cf", "currentModelAlias": "ERP_GEN$gen_mat_md", "relationModelAlias": "ERP_GEN$gen_uom_formula_type_cf", "linkModelFieldAlias": "genMatMdId", "currentModelFieldAlias": "uomFormulaTypeId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "batch_relv", "name": "批次管理", "type": "DataStructField", "alias": "batchRelv", "props": {"length": 1, "unique": false, "comment": "批次管理", "required": false, "encrypted": false, "fieldType": "BOOL", "columnName": "batch_relv", "compositeKey": false, "defaultValue": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "is_batch_determination", "name": "是否必须匹配批次确定", "type": "DataStructField", "alias": "isBatchDetermination", "props": {"length": 1, "unique": false, "comment": "是否必须匹配批次确定", "required": false, "encrypted": false, "fieldType": "BOOL", "columnName": "is_batch_determination", "compositeKey": false, "defaultValue": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "gen_chara_class_id", "name": "物料分类类别", "type": "DataStructField", "alias": "genCharaClassId", "props": {"unique": false, "comment": "物料分类类别", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "gen_chara_class_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": "ERP_GEN$gen_mat_batch_class_cf", "relationModelKey": "ERP_GEN$gen_chara_class_md", "currentModelAlias": "ERP_GEN$gen_mat_md", "relationModelAlias": "ERP_GEN$gen_chara_class_md", "linkModelFieldAlias": "matId", "currentModelFieldAlias": "genCharaClassId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "shelf_life", "name": "物料保质期", "type": "DataStructField", "alias": "shelfLife", "props": {"scale": 0, "unique": false, "comment": "物料保质期", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 8, "columnName": "shelf_life", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "charas", "name": "特征数据", "type": "DataStructField", "alias": "charas", "props": {"length": 5120, "unique": false, "comment": "特征数据", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "charas", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "is_auto_count_plan", "name": "是否支持自动生成盘点方案", "type": "DataStructField", "alias": "isAutoCountPlan", "props": {"length": 1, "unique": false, "comment": "是否支持自动生成盘点方案", "required": false, "encrypted": false, "fieldType": "BOOL", "columnName": "is_auto_count_plan", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "cost", "name": "成本价", "type": "DataStructField", "alias": "cost", "props": {"scale": 6, "unique": false, "comment": "成本价", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "cost", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "mat_code_ext", "name": "外部物料编码", "type": "DataStructField", "alias": "matCodeExt", "props": {"length": 32, "unique": false, "comment": "外部物料编码", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "mat_code_ext", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "status", "name": "状态", "type": "DataStructField", "alias": "status", "props": {"length": 256, "unique": false, "comment": "状态", "dictPros": {"dictValues": [{"label": "未启用", "value": "INACTIVE"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已停用", "value": "DISABLED"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "status", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "mat_prd", "name": " 生产视图", "type": "DataStructField", "alias": "mat<PERSON>rd", "props": {"unique": false, "comment": " 生产视图", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "mat_prd", "compositeKey": false, "relationMeta": {"sync": true, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "ERP_GEN$gen_mat_prd_md", "relationModelKey": "ERP_GEN$gen_mat_prd_md", "currentModelAlias": "ERP_GEN$gen_mat_md", "relationModelAlias": "ERP_GEN$gen_mat_prd_md", "linkModelFieldAlias": "genMatMdId", "currentModelFieldAlias": "mat<PERSON>rd"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "atp_group_id", "name": "ATP检查组", "type": "DataStructField", "alias": "atpGroupId", "props": {"unique": false, "comment": "ATP检查组", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "atp_group_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_atp_group_md", "currentModelAlias": "ERP_GEN$gen_mat_md", "relationModelAlias": "ERP_GEN$gen_atp_group_md", "linkModelFieldAlias": null, "currentModelFieldAlias": "atpGroupId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "image_url", "name": "物料图片", "type": "DataStructField", "alias": "imageUrl", "props": {"length": 4000, "unique": false, "comment": "物料图片", "required": false, "encrypted": false, "fieldType": "ATTACHMENT", "columnName": "image_url", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "attachmentProps": {"multi": false}}}, {"ext": false, "key": "mat_fin_id", "name": "物料财务视图", "type": "DataStructField", "alias": "matFinId", "props": {"unique": false, "comment": "物料财务视图", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "mat_fin_id", "compositeKey": false, "relationMeta": {"sync": true, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "ERP_GEN$gen_mat_fin_md", "relationModelKey": "ERP_GEN$gen_mat_fin_md", "currentModelAlias": "ERP_GEN$gen_mat_md", "relationModelAlias": "ERP_GEN$gen_mat_fin_md", "linkModelFieldAlias": "genMatMdId", "currentModelFieldAlias": "matFinId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "remark", "name": "备注", "type": "DataStructField", "alias": "remark", "props": {"length": 8192, "unique": false, "comment": "备注", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "remark", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "mat_wm_ids", "name": "物料仓库视图", "type": "DataStructField", "alias": "matWmIds", "props": {"unique": false, "comment": "物料仓库视图", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "mat_wm_ids", "compositeKey": false, "relationMeta": {"sync": true, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "ERP_GEN$gen_mat_wm_md", "relationModelKey": "ERP_GEN$gen_mat_wm_md", "currentModelAlias": "ERP_GEN$gen_mat_md", "relationModelAlias": "ERP_GEN$gen_mat_wm_md", "linkModelFieldAlias": "genMatMdId", "currentModelFieldAlias": "matWmIds"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "id", "name": "ID", "type": "DataStructField", "alias": "id", "props": {"length": 20, "unique": true, "comment": "ID", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "id", "compositeKey": false, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "created_by", "name": "创建人", "type": "DataStructField", "alias": "created<PERSON>y", "props": {"length": 20, "unique": true, "comment": "创建人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "created_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_mat_md", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "created<PERSON>y"}, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "updated_by", "name": "更新人", "type": "DataStructField", "alias": "updatedBy", "props": {"length": 20, "unique": true, "comment": "更新人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "updated_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_mat_md", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "updatedBy"}, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "created_at", "name": "创建时间", "type": "DataStructField", "alias": "createdAt", "props": {"unique": true, "comment": "创建时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "created_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "updated_at", "name": "更新时间", "type": "DataStructField", "alias": "updatedAt", "props": {"unique": true, "comment": "更新时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "updated_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "version", "name": "版本号", "type": "DataStructField", "alias": "version", "props": {"length": 20, "unique": true, "comment": "版本号", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "version", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "deleted", "name": "逻辑删除标识", "type": "DataStructField", "alias": "deleted", "props": {"length": 20, "unique": true, "comment": "逻辑删除标识", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "deleted", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "origin_org_id", "name": "所属组织", "type": "DataStructField", "alias": "originOrgId", "props": {"length": 20, "unique": true, "comment": "所属组织", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "origin_org_id", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}]}}