{"type": "Model", "name": "组织关联规则设置", "parentKey": "ERP_GEN", "children": [], "props": {"desc": null, "alias": "ERP_GEN$org_relation_rule_cf", "props": {"type": "PERSIST", "config": {"self": false, "system": false, "persist": false, "selfRelationFieldAlias": null}, "mainField": "org_head_dimension_id", "tableName": "org_relation_rule_cf", "searchModel": false, "mainFieldAlias": "orgHeadDimensionId", "physicalDelete": false, "shardingConfig": {"enabled": false, "shardingFields": null, "shardingSuffixLength": 3}, "originOrgIdEnabled": true}, "children": [{"ext": false, "key": "org_head_dimension_id", "name": "源组织维度", "type": "DataStructField", "alias": "orgHeadDimensionId", "appId": 46980, "props": {"unique": false, "comment": "源组织维度", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "org_head_dimension_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "sys_common$org_dimension_cf", "currentModelAlias": "ERP_GEN$org_relation_rule_cf", "relationModelAlias": "sys_common$org_dimension_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "orgHeadDimensionId"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "org_relation_dimension_id", "name": "关联组织维度", "type": "DataStructField", "alias": "orgRelationDimensionId", "appId": 46980, "props": {"unique": false, "comment": "关联组织维度", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "org_relation_dimension_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "sys_common$org_dimension_cf", "currentModelAlias": "ERP_GEN$org_relation_rule_cf", "relationModelAlias": "sys_common$org_dimension_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "orgRelationDimensionId"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "org_relation_rule_type", "name": "关联类型", "type": "DataStructField", "alias": "orgRelationRuleType", "appId": 46980, "props": {"length": 256, "unique": false, "comment": "关联类型", "dictPros": {"dictValues": [{"label": "一对一", "value": "ONE"}, {"label": "一对多", "value": "MANY"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "org_relation_rule_type", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "org_head_unit_type_id", "name": "源组织类型", "type": "DataStructField", "alias": "orgHeadUnitTypeId", "appId": 46980, "props": {"unique": false, "comment": "源组织类型", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "org_head_unit_type_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "sys_common$org_business_type_cf", "currentModelAlias": "ERP_GEN$org_relation_rule_cf", "relationModelAlias": "sys_common$org_business_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "orgHeadUnitTypeId"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "org_relation_unit_type_id", "name": "关联组织类型", "type": "DataStructField", "alias": "orgRelationUnitTypeId", "appId": 46980, "props": {"unique": false, "comment": "关联组织类型", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "org_relation_unit_type_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "sys_common$org_business_type_cf", "currentModelAlias": "ERP_GEN$org_relation_rule_cf", "relationModelAlias": "sys_common$org_business_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "orgRelationUnitTypeId"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "id", "name": "ID", "type": "DataStructField", "alias": "id", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "ID", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "id", "compositeKey": false, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}, {"ext": false, "key": "created_by", "name": "创建人", "type": "DataStructField", "alias": "created<PERSON>y", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "创建人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "created_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$org_relation_rule_cf", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "created<PERSON>y"}, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "updated_by", "name": "更新人", "type": "DataStructField", "alias": "updatedBy", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "更新人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "updated_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$org_relation_rule_cf", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "updatedBy"}, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "created_at", "name": "创建时间", "type": "DataStructField", "alias": "createdAt", "appId": 46980, "props": {"unique": true, "comment": "创建时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "created_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "updated_at", "name": "更新时间", "type": "DataStructField", "alias": "updatedAt", "appId": 46980, "props": {"unique": true, "comment": "更新时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "updated_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "version", "name": "版本号", "type": "DataStructField", "alias": "version", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "版本号", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "version", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}, {"ext": false, "key": "deleted", "name": "逻辑删除标识", "type": "DataStructField", "alias": "deleted", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "逻辑删除标识", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "deleted", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}, {"ext": false, "key": "origin_org_id", "name": "所属组织", "type": "DataStructField", "alias": "originOrgId", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "所属组织", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "origin_org_id", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}]}}