{"access": "Private", "parentKey": "CON", "name": "根据价格协议行id查询价格协议行服务", "type": "ServiceDefinition", "key": "CON$PRICE_AGTITEM_FINDONE_EVENT_SERVICE", "props": {"serviceDslJson": {"output": [{"fieldAlias": "info", "fieldName": "info", "fieldKey": "info", "fieldType": "Object"}, {"fieldAlias": "err", "fieldName": "err", "fieldKey": "err", "fieldType": "Object"}, {"fieldAlias": "success", "fieldName": "success", "fieldKey": "success", "fieldType": "Boolean"}, {"fieldAlias": "data", "fieldName": "data", "fieldKey": "data", "elements": [{"fieldAlias": "data", "fieldName": "data", "fieldKey": "data", "fieldType": "Model", "relatedModel": {"modelName": "TSRM$srm_pri_agreement_i_md", "modelAlias": "CON$con_pri_agreement_item_tr", "modelKey": "CON$con_pri_agreement_item_tr"}}], "fieldType": "Object"}], "input": [{"fieldAlias": "request", "fieldName": "request", "fieldKey": "request", "fieldType": "Model", "relatedModel": {"modelName": "TSRM$srm_pri_agreement_i_md", "modelAlias": "CON$con_pri_agreement_item_tr", "modelKey": "CON$con_pri_agreement_item_tr"}, "required": true}], "children": [{"nextNodeKey": "AssignNode0", "name": "开始节点", "type": "StartNode", "key": "start0", "props": {"output": [{"fieldAlias": "info", "fieldName": "info", "fieldKey": "info", "fieldType": "Object"}, {"fieldAlias": "err", "fieldName": "err", "fieldKey": "err", "fieldType": "Object"}, {"fieldAlias": "success", "fieldName": "success", "fieldKey": "success", "fieldType": "Boolean"}, {"fieldAlias": "data", "fieldName": "data", "fieldKey": "data", "elements": [{"fieldAlias": "data", "fieldName": "data", "fieldKey": "data", "fieldType": "Model", "relatedModel": {"modelName": "TSRM$srm_pri_agreement_i_md", "modelAlias": "CON$con_pri_agreement_item_tr", "modelKey": "CON$con_pri_agreement_item_tr"}}], "fieldType": "Object"}], "input": [{"fieldAlias": "request", "fieldName": "request", "fieldKey": "request", "fieldType": "Model", "relatedModel": {"modelName": "TSRM$srm_pri_agreement_i_md", "modelAlias": "CON$con_pri_agreement_item_tr", "modelKey": "CON$con_pri_agreement_item_tr"}, "required": true}], "globalVariable": [{"fieldAlias": "event", "fieldName": "event", "fieldKey": "event", "elements": [{"fieldAlias": "eventCode", "fieldName": "eventCode", "fieldKey": "eventCode", "fieldType": "Text"}, {"fieldAlias": "param", "fieldName": "param", "fieldKey": "param", "fieldType": "Model", "relatedModel": {"modelName": "TSRM$srm_pri_agreement_i_md", "modelAlias": "CON$con_pri_agreement_item_tr", "modelKey": "CON$con_pri_agreement_item_tr"}}], "fieldType": "Object"}], "name": "开始节点", "type": "StartProperties", "desc": "开始节点"}}, {"nextNodeKey": "SPINodeKey", "name": "赋值", "type": "AssignNode", "key": "AssignNode0", "props": {"assignments": [{"field": {"varValue": [{"valueName": "GLOBAL", "valueKey": "GLOBAL"}, {"valueName": "event", "valueKey": "event"}, {"valueName": "eventCode", "valueKey": "eventCode"}], "valueType": "VAR", "type": "VarValue", "fieldType": "Text"}, "value": {"constValue": "CON$PRICE_AGTITEM_FINDONE_EVENT", "valueType": "CONST", "type": "VarValue"}, "operator": "EQ"}, {"field": {"varValue": [{"valueName": "GLOBAL", "valueKey": "GLOBAL"}, {"valueName": "event", "valueKey": "event"}, {"valueName": "param", "valueKey": "param"}], "valueType": "VAR", "type": "VarValue", "fieldType": "Object"}, "value": {"varValue": [{"valueName": "REQUEST", "valueKey": "REQUEST"}, {"valueName": "request", "valueKey": "request"}], "valueType": "VAR", "type": "VarValue"}, "operator": "EQ"}], "name": "赋值", "type": "AssignProperties"}}, {"nextNodeKey": "end0", "name": "调用线下代码", "type": "SPINode", "key": "SPINodeKey", "props": {"output": [{"fieldAlias": "data", "fieldName": "ActionResponse", "fieldKey": "data", "fieldType": "Object"}], "inputMapping": [{"field": {"fieldAlias": "event", "fieldName": "event", "fieldKey": "event", "fieldType": "Object"}, "value": {"varValue": [{"valueName": "全局变量", "valueKey": "GLOBAL"}, {"valueName": "event", "valueKey": "event"}], "valueType": "VAR", "type": "VarValue"}}], "outputAssign": {"outputAssignType": "CUSTOM", "customAssignments": [{"field": {"varValue": [{"valueName": "服务出参", "valueKey": "OUTPUT"}], "valueType": "VAR", "type": "VarValue", "fieldType": "Object"}, "value": {"varValue": [{"valueName": "出参结构体", "valueKey": "NODE_OUTPUT_SPINodeKey"}, {"valueName": "ActionResponse", "valueKey": "data"}], "valueType": "MODEL", "type": "VarValue", "fieldType": "Object"}, "operator": "EQ"}]}, "implementation": "EventServiceExecute", "name": "调用线下代码", "convertRelatedModelData": false, "type": "SPIProperties"}}, {"name": "结束节点", "type": "EndNode", "key": "end0", "props": {"type": "EndProperties"}}], "name": "根据价格协议行id查询价格协议行服务", "type": "ServiceDefinition", "headNodeKeys": ["start0"], "key": "TSRM$PRICE_AGTITEM_FINDONE_EVENT_SERVICE", "props": {"transactionPropagation": "REQUIRED", "teamId": 35, "type": "ServiceProperties"}}, "serviceType": "EVENT", "eventProps": {"returnModel": {"key": "CON$con_pri_agreement_item_tr"}, "returnModelArrayWhether": false, "model": {"key": "CON$con_pri_agreement_item_tr"}, "enabledStatusVerify": false, "modelArrayWhether": false, "enabledTransaction": true, "relations": [{"actionType": "Action", "code": "CON$TSRM_PRICE_AGTITEM_FIND_BYID_ACTION", "enabledParamCheck": false}], "states": []}, "isEnabled": true, "modelKey": "CON$con_pri_agreement_item_tr"}}