{"type": "Model", "name": "币种配置表", "parentKey": "ERP_GEN", "children": [], "props": {"desc": null, "alias": "ERP_GEN$gen_curr_type_cf", "props": {"type": "PERSIST", "config": {"self": false, "system": false, "persist": false, "selfRelationFieldAlias": null}, "mainField": "curr_name", "tableName": "gen_curr_type_cf", "searchModel": false, "mainFieldAlias": "currName", "physicalDelete": false, "shardingConfig": {"enabled": false, "shardingFields": null, "shardingSuffixLength": 3}, "originOrgIdEnabled": true}, "children": [{"ext": false, "key": "curr_code", "name": "币别编码", "type": "DataStructField", "alias": "currCode", "appId": 46980, "props": {"length": 32, "unique": false, "comment": "币别编码", "required": true, "encrypted": false, "fieldType": "TEXT", "columnName": "curr_code", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "curr_name", "name": "名称", "type": "DataStructField", "alias": "currName", "appId": 46980, "props": {"length": 32, "unique": false, "comment": "名称", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "curr_name", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "curr_iso_name", "name": "ISO名称", "type": "DataStructField", "alias": "currIsoName", "appId": 46980, "props": {"length": 32, "unique": false, "comment": "ISO名称", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "curr_iso_name", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "decimal_place", "name": "小数位", "type": "DataStructField", "alias": "decimalPlace", "appId": 46980, "props": {"unique": false, "comment": "小数位", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 8, "columnName": "decimal_place", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}, "teamId": 22}, {"ext": false, "key": "symbol", "name": "币种符号", "type": "DataStructField", "alias": "symbol", "appId": 46980, "props": {"length": 16, "unique": false, "comment": "币种符号", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "symbol", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "remark", "name": "备注", "type": "DataStructField", "alias": "remark", "appId": 46980, "props": {"length": 256, "unique": false, "comment": "备注", "required": true, "encrypted": false, "fieldType": "TEXT", "columnName": "remark", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "id", "name": "ID", "type": "DataStructField", "alias": "id", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "ID", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "id", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "created_by", "name": "创建人", "type": "DataStructField", "alias": "created<PERSON>y", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "创建人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "created_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_curr_type_cf", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "created<PERSON>y"}, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "updated_by", "name": "更新人", "type": "DataStructField", "alias": "updatedBy", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "更新人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "updated_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_curr_type_cf", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "updatedBy"}, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "created_at", "name": "创建时间", "type": "DataStructField", "alias": "createdAt", "appId": 46980, "props": {"unique": true, "comment": "创建时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "created_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "updated_at", "name": "更新时间", "type": "DataStructField", "alias": "updatedAt", "appId": 46980, "props": {"unique": true, "comment": "更新时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "updated_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "version", "name": "版本号", "type": "DataStructField", "alias": "version", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "版本号", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "version", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "deleted", "name": "逻辑删除标识", "type": "DataStructField", "alias": "deleted", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "逻辑删除标识", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "deleted", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "origin_org_id", "name": "所属组织", "type": "DataStructField", "alias": "originOrgId", "appId": 46980, "props": {"unique": false, "comment": "所属组织", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "origin_org_id", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}]}}