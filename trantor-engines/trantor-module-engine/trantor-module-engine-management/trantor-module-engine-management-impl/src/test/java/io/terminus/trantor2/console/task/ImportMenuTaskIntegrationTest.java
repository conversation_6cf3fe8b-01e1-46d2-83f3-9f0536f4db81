package io.terminus.trantor2.console.task;

import io.terminus.trantor2.console.meta.MetaBaseIntegrationTests;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.meta.MenuTreeMeta;
import io.terminus.trantor2.module.service.MenuConsoleQueryService;
import io.terminus.trantor2.module.service.OSSService;
import io.terminus.trantor2.task.TaskOutput;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
class ImportMenuTaskIntegrationTest extends MetaBaseIntegrationTests {

    @Autowired
    private ImportMenuTask importMenuTask;

    @Autowired
    private MenuConsoleQueryService menuQueryService;

    @Autowired
    private OSSService ossService;

    @Test
    void testImportMenuIntegration() throws Exception {
        // 加载测试行为
        ideHelperBean.loadBehaviors();

        // 初始化测试环境
        MetaEditAndQueryContext ctx = ideHelperBean.initRepoV2(this.getClass(), "00base", 0L);
        
        // 确保数据集项目状态正确
        ideHelperBean.assertDatasetProject(this.getClass(), "00base", ctx);

        // 准备CSV测试数据
        String csvContent = "菜单名称,类型,页面场景/链接,路径,页面功能,描述\n" +
                "系统管理,文件夹,,系统管理,,系统管理模块\n" +
                "用户管理,文件夹,,系统管理/用户管理,,用户管理子模块\n" +
                "用户列表,关联页面场景,user_scene|user_list,系统管理/用户管理/用户列表,查看用户,用户列表页面\n" +
                "角色管理,文件夹,,系统管理/角色管理,,角色管理子模块\n" +
                "角色列表,关联页面场景,role_scene|role_list,系统管理/角色管理/角色列表,查看角色,角色列表页面";

        InputStream inputStream = new ByteArrayInputStream(csvContent.getBytes(StandardCharsets.UTF_8));

        // 准备任务参数
        ImportMenuTask.Options options = new ImportMenuTask.Options();
        options.setCsvObjectName("test_menu_import.csv");
        options.setPortalCode("test_portal");

        TaskContext taskContext = new TaskContext(teamId, teamCode, userId, false);
        TaskOutput taskOutput = mock(TaskOutput.class);

        // Mock OSS服务
        when(ossService.migrateFileOut("test_menu_import.csv", null)).thenReturn(inputStream);
        when(ossService.deleteFile("test_menu_import.csv", null)).thenReturn(true);

        // 执行预检查
        importMenuTask.preCheck(options, taskContext);

        // 执行导入任务
        importMenuTask.exec(options, taskOutput, taskContext);

        // 验证结果
        MenuTreeMeta menuTree = menuQueryService.getMenuTreeMeta("test_portal");
        assertNotNull(menuTree);
        assertNotNull(menuTree.getMenus());
        assertEquals(1, menuTree.getMenus().size());

        MenuMeta rootMenu = menuTree.getMenus().get(0);
        assertEquals("系统管理", rootMenu.getLabel());
        assertEquals(MenuMeta.RouteType.None, rootMenu.getRouteType());
        assertEquals(2, rootMenu.getChildren().size());

        // 验证用户管理子菜单
        MenuMeta userManagement = findMenuByLabel(rootMenu.getChildren(), "用户管理");
        assertNotNull(userManagement);
        assertEquals(MenuMeta.RouteType.None, userManagement.getRouteType());
        assertEquals(1, userManagement.getChildren().size());

        MenuMeta userList = userManagement.getChildren().get(0);
        assertEquals("用户列表", userList.getLabel());
        assertEquals(MenuMeta.RouteType.Scene, userList.getRouteType());

        // 验证角色管理子菜单
        MenuMeta roleManagement = findMenuByLabel(rootMenu.getChildren(), "角色管理");
        assertNotNull(roleManagement);
        assertEquals(MenuMeta.RouteType.None, roleManagement.getRouteType());
        assertEquals(1, roleManagement.getChildren().size());

        MenuMeta roleList = roleManagement.getChildren().get(0);
        assertEquals("角色列表", roleList.getLabel());
        assertEquals(MenuMeta.RouteType.Scene, roleList.getRouteType());

        // 验证OSS服务调用
        verify(ossService).migrateFileOut("test_menu_import.csv", null);
        verify(ossService).deleteFile("test_menu_import.csv", null);
    }

    @Test
    void testImportMenuWithExistingMenus() throws Exception {
        // 加载测试行为
        ideHelperBean.loadBehaviors();

        // 初始化测试环境
        MetaEditAndQueryContext ctx = ideHelperBean.initRepoV2(this.getClass(), "00base", 0L);
        
        // 确保数据集项目状态正确
        ideHelperBean.assertDatasetProject(this.getClass(), "00base", ctx);

        // 先创建一个现有的菜单树
        MenuTreeMeta existingMenuTree = new MenuTreeMeta();
        existingMenuTree.setParentKey("test_portal");
        existingMenuTree.setKey("test_portal$menu_tree");
        existingMenuTree.setName("菜单树");
        existingMenuTree.setTeamCode(teamCode);
        existingMenuTree.setTeamId(teamId);

        MenuMeta existingMenu = new MenuMeta();
        existingMenu.setLabel("现有菜单");
        existingMenu.setKey("test_portal$existing_menu");
        existingMenu.setRouteType(MenuMeta.RouteType.None);
        existingMenu.setDescription("现有的菜单项");

        existingMenuTree.setMenus(List.of(existingMenu));

        // 保存现有菜单树
        // 这里需要根据实际的存储方式来实现

        // 准备CSV测试数据 - 包含新的菜单项
        String csvContent = "菜单名称,类型,页面场景/链接,路径,页面功能,描述\n" +
                "新菜单,文件夹,,新菜单,,新菜单模块\n" +
                "新子菜单,关联页面场景,new_scene|new_list,新菜单/新子菜单,查看新菜单,新子菜单页面";

        InputStream inputStream = new ByteArrayInputStream(csvContent.getBytes(StandardCharsets.UTF_8));

        // 准备任务参数
        ImportMenuTask.Options options = new ImportMenuTask.Options();
        options.setCsvObjectName("test_menu_import_existing.csv");
        options.setPortalCode("test_portal");

        TaskContext taskContext = new TaskContext(teamId, teamCode, userId, false);
        TaskOutput taskOutput = mock(TaskOutput.class);

        // Mock OSS服务
        when(ossService.migrateFileOut("test_menu_import_existing.csv", null)).thenReturn(inputStream);
        when(ossService.deleteFile("test_menu_import_existing.csv", null)).thenReturn(true);

        // 执行预检查
        importMenuTask.preCheck(options, taskContext);

        // 执行导入任务
        importMenuTask.exec(options, taskOutput, taskContext);

        // 验证结果 - 应该包含新的菜单项
        MenuTreeMeta menuTree = menuQueryService.getMenuTreeMeta("test_portal");
        assertNotNull(menuTree);
        assertNotNull(menuTree.getMenus());

        // 验证OSS服务调用
        verify(ossService).migrateFileOut("test_menu_import_existing.csv", null);
        verify(ossService).deleteFile("test_menu_import_existing.csv", null);
    }

    @Test
    void testImportMenuWithInvalidScene() throws Exception {
        // 加载测试行为
        ideHelperBean.loadBehaviors();

        // 初始化测试环境
        MetaEditAndQueryContext ctx = ideHelperBean.initRepoV2(this.getClass(), "00base", 0L);
        
        // 确保数据集项目状态正确
        ideHelperBean.assertDatasetProject(this.getClass(), "00base", ctx);

        // 准备CSV测试数据 - 包含无效的场景
        String csvContent = "菜单名称,类型,页面场景/链接,路径,页面功能,描述\n" +
                "无效场景菜单,关联页面场景,invalid_scene|invalid_view,无效场景菜单,查看无效场景,无效场景菜单";

        InputStream inputStream = new ByteArrayInputStream(csvContent.getBytes(StandardCharsets.UTF_8));

        // 准备任务参数
        ImportMenuTask.Options options = new ImportMenuTask.Options();
        options.setCsvObjectName("test_menu_import_invalid.csv");
        options.setPortalCode("test_portal");

        TaskContext taskContext = new TaskContext(teamId, teamCode, userId, false);
        TaskOutput taskOutput = mock(TaskOutput.class);

        // Mock OSS服务
        when(ossService.migrateFileOut("test_menu_import_invalid.csv", null)).thenReturn(inputStream);
        when(ossService.deleteFile("test_menu_import_invalid.csv", null)).thenReturn(true);

        // 执行预检查
        importMenuTask.preCheck(options, taskContext);

        // 执行导入任务
        importMenuTask.exec(options, taskOutput, taskContext);

        // 验证结果 - 无效场景应该被转换为文件夹类型
        MenuTreeMeta menuTree = menuQueryService.getMenuTreeMeta("test_portal");
        assertNotNull(menuTree);
        assertNotNull(menuTree.getMenus());
        assertEquals(1, menuTree.getMenus().size());

        MenuMeta invalidMenu = menuTree.getMenus().get(0);
        assertEquals("无效场景菜单", invalidMenu.getLabel());
        assertEquals(MenuMeta.RouteType.None, invalidMenu.getRouteType()); // 应该被转换为文件夹类型

        // 验证OSS服务调用
        verify(ossService).migrateFileOut("test_menu_import_invalid.csv", null);
        verify(ossService).deleteFile("test_menu_import_invalid.csv", null);
    }

    private MenuMeta findMenuByLabel(List<MenuMeta> menus, String label) {
        return menus.stream()
                .filter(menu -> label.equals(menu.getLabel()))
                .findFirst()
                .orElse(null);
    }
} 