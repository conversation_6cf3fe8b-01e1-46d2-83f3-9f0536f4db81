{"access": "Public", "children": [], "key": "ERP_SCM$inv_mvm_type_cf", "name": "移动类型定义表", "parentKey": "ERP_SCM", "props": {"alias": "ERP_SCM$inv_mvm_type_cf", "children": [{"alias": "id", "appId": 85, "ext": false, "key": "id", "name": "ID", "props": {"autoGenerated": false, "columnName": "id", "comment": "ID", "compositeKey": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "required": true, "unique": true}, "teamId": 54, "type": "DataStructField"}, {"alias": "created<PERSON>y", "appId": 85, "ext": false, "key": "created_by", "name": "创建人", "props": {"autoGenerated": false, "columnName": "created_by", "comment": "创建人", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"currentModelAlias": "ERP_SCM$inv_mvm_type_cf", "currentModelFieldAlias": "created<PERSON>y", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "ERP_SCM$user", "relationModelKey": "ERP_SCM$user", "relationType": "LINK", "sync": false}, "required": false, "unique": true}, "teamId": 54, "type": "DataStructField"}, {"alias": "updatedBy", "appId": 85, "ext": false, "key": "updated_by", "name": "更新人", "props": {"autoGenerated": false, "columnName": "updated_by", "comment": "更新人", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"currentModelAlias": "ERP_SCM$inv_mvm_type_cf", "currentModelFieldAlias": "updatedBy", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "ERP_SCM$user", "relationModelKey": "ERP_SCM$user", "relationType": "LINK", "sync": false}, "required": false, "unique": true}, "teamId": 54, "type": "DataStructField"}, {"alias": "createdAt", "appId": 85, "ext": false, "key": "created_at", "name": "创建时间", "props": {"autoGenerated": false, "columnName": "created_at", "comment": "创建时间", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "isSystemField": true, "required": true, "unique": true}, "teamId": 54, "type": "DataStructField"}, {"alias": "updatedAt", "appId": 85, "ext": false, "key": "updated_at", "name": "更新时间", "props": {"autoGenerated": false, "columnName": "updated_at", "comment": "更新时间", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "isSystemField": true, "required": true, "unique": true}, "teamId": 54, "type": "DataStructField"}, {"alias": "version", "appId": 85, "ext": false, "key": "version", "name": "版本号", "props": {"autoGenerated": false, "columnName": "version", "comment": "版本号", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "required": true, "unique": true}, "teamId": 54, "type": "DataStructField"}, {"alias": "deleted", "appId": 85, "ext": false, "key": "deleted", "name": "逻辑删除标识", "props": {"autoGenerated": false, "columnName": "deleted", "comment": "逻辑删除标识", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "required": true, "unique": true}, "teamId": 54, "type": "DataStructField"}, {"alias": "code", "appId": 85, "ext": false, "key": "code", "name": "编码", "props": {"autoGenerated": false, "columnName": "code", "comment": "编码", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 256, "required": true, "unique": false}, "teamId": 54, "type": "DataStructField"}, {"alias": "bsTypeId", "appId": 85, "ext": false, "key": "bs_type_id", "name": "业务类型", "props": {"autoGenerated": false, "columnName": "bs_type_id", "comment": "业务类型", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$inv_mvm_type_cf", "currentModelFieldAlias": "bsTypeId", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "ERP_SCM$inv_bs_type_cf", "relationModelKey": "ERP_SCM$inv_bs_type_cf", "relationType": "LINK", "sync": false}, "required": true, "unique": false}, "teamId": 54, "type": "DataStructField"}, {"alias": "fbTypeId", "appId": 85, "ext": false, "key": "fb_type_id", "name": "作业正逆向标识", "props": {"autoGenerated": false, "columnName": "fb_type_id", "comment": "作业正逆向标识", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$inv_mvm_type_cf", "currentModelFieldAlias": "fbTypeId", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "ERP_SCM$inv_fb_type_cf", "relationModelKey": "ERP_SCM$inv_fb_type_cf", "relationType": "LINK", "sync": false}, "required": true, "unique": false}, "teamId": 54, "type": "DataStructField"}, {"alias": "invTypeTransId", "appId": 85, "ext": false, "key": "inv_type_trans_id", "name": "库存类型转移标识", "props": {"autoGenerated": false, "columnName": "inv_type_trans_id", "comment": "库存类型转移标识", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$inv_mvm_type_cf", "currentModelFieldAlias": "invTypeTransId", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "ERP_SCM$inv_inv_type_trans_cf", "relationModelKey": "ERP_SCM$inv_inv_type_trans_cf", "relationType": "LINK", "sync": false}, "required": true, "unique": false}, "teamId": 54, "type": "DataStructField"}, {"alias": "name", "appId": 85, "ext": false, "key": "name", "name": "名称", "props": {"autoGenerated": false, "columnName": "name", "comment": "名称", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 256, "required": false, "unique": false}, "teamId": 54, "type": "DataStructField"}, {"alias": "status", "appId": 85, "ext": false, "key": "status", "name": "状态", "props": {"autoGenerated": false, "columnName": "status", "comment": "状态", "compositeKey": false, "dictPros": {"dictValues": [{"label": "未启用", "value": "UNENABLED"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已停用", "value": "DISENABLED"}], "multiSelect": false, "properties": null}, "encrypted": false, "fieldType": "ENUM", "isSystemField": false, "length": 256, "required": false, "unique": false}, "teamId": 54, "type": "DataStructField"}, {"alias": "remark", "appId": 85, "ext": false, "key": "remark", "name": "备注", "props": {"autoGenerated": false, "columnName": "remark", "comment": "备注", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 256, "required": false, "unique": false}, "teamId": 54, "type": "DataStructField"}, {"alias": "mvmRule", "appId": 85, "ext": false, "key": "mvm_rule", "name": "规则明细", "props": {"autoGenerated": false, "columnName": "mvm_rule", "comment": "规则明细", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$inv_mvm_type_cf", "currentModelFieldAlias": "mvmRule", "linkModelAlias": "ERP_SCM$inv_mvm_rule_link_cf", "linkModelFieldAlias": "mvmTypeId", "relationKey": null, "relationModelAlias": "ERP_SCM$inv_mvm_rule_link_cf", "relationModelKey": "ERP_SCM$inv_mvm_rule_link_cf", "relationType": "PARENT_CHILD", "sync": true}, "required": false, "unique": false}, "teamId": 54, "type": "DataStructField"}, {"alias": "mvmExtTypeId", "appId": 85, "ext": false, "key": "mvm_ext_type_id", "name": "业务类型扩展", "props": {"autoGenerated": false, "columnName": "mvm_ext_type_id", "comment": "业务类型扩展", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "ERP_SCM$inv_mvm_type_cf", "currentModelFieldAlias": "mvmExtTypeId", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "ERP_SCM$inv_mvm_ext_type_cf", "relationModelKey": "ERP_SCM$inv_mvm_ext_type_cf", "relationType": "LINK", "sync": false}, "required": true, "unique": false}, "teamId": 54, "type": "DataStructField"}, {"alias": "batchCodeRuleKey", "appId": 85, "ext": false, "key": "batch_code_rule_key", "name": "批次编码规则", "props": {"autoGenerated": false, "columnName": "batch_code_rule_key", "comment": "批次编码规则", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 128, "required": false, "unique": false}, "teamId": 54, "type": "DataStructField"}, {"alias": "originOrgId", "appId": 47014, "ext": false, "key": "origin_org_id", "name": "所属组织", "props": {"autoGenerated": false, "columnName": "origin_org_id", "comment": "所属组织", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "numberDisplayType": "digit", "required": true, "unique": false}, "teamId": 22, "type": "DataStructField"}], "desc": "移动类型定义表", "props": {"config": {"persist": false, "self": false, "selfRelationFieldAlias": null, "system": false}, "mainField": "code", "mainFieldAlias": "code", "originOrgIdEnabled": true, "physicalDelete": false, "searchModel": false, "tableName": "inv_mvm_type_cf", "type": "PERSIST"}}, "type": "Model"}