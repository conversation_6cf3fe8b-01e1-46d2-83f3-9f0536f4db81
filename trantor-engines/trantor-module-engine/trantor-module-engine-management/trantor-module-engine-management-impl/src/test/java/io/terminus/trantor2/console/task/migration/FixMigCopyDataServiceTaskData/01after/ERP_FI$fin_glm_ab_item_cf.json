{"type": "Model", "name": "现金流量项目", "parentKey": "ERP_FI", "children": [], "props": {"desc": null, "alias": "ERP_FI$fin_glm_ab_item_cf", "props": {"type": "PERSIST", "config": {"self": true, "system": false, "persist": false, "selfRelationFieldAlias": "parentId"}, "mainField": "ab_item_name", "tableName": "fin_glm_ab_item_cf", "searchModel": false, "mainFieldAlias": "abItemName", "physicalDelete": false, "shardingConfig": {"enabled": false, "shardingFields": null, "shardingSuffixLength": 3}, "originOrgIdEnabled": true}, "children": [{"ext": false, "key": "ab_item_code", "name": "现金流量项目编码", "type": "DataStructField", "alias": "abItemCode", "appId": 43132, "props": {"length": 256, "unique": false, "comment": "现金流量项目编码", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "ab_item_code", "compositeKey": true, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "ab_item_name", "name": "现金流量项目名称", "type": "DataStructField", "alias": "abItemName", "appId": 43132, "props": {"length": 256, "unique": false, "comment": "现金流量项目名称", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "ab_item_name", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "ab_item_type", "name": "项目类别", "type": "DataStructField", "alias": "abItemType", "appId": 43132, "props": {"length": 256, "unique": false, "comment": "项目类别", "dictPros": {"dictValues": [{"label": "主表项目", "value": "MAIN"}, {"label": "附表项目", "value": "SECONDARY"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "ab_item_type", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "cash_flow_direc", "name": "现金流向", "type": "DataStructField", "alias": "cashFlowDirec", "appId": 43132, "props": {"length": 256, "unique": false, "comment": "现金流向", "dictPros": {"dictValues": [{"label": "流出", "value": "OUT_FLOW"}, {"label": "流入", "value": "IN_FLOW"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "cash_flow_direc", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "oper_activ", "name": "经营活动", "type": "DataStructField", "alias": "operActiv", "appId": 43132, "props": {"length": 1, "unique": false, "comment": "经营活动", "required": false, "encrypted": false, "fieldType": "BOOL", "columnName": "oper_activ", "compositeKey": false, "defaultValue": true, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "net_prof", "name": "净利润", "type": "DataStructField", "alias": "netProf", "appId": 43132, "props": {"length": 1, "unique": false, "comment": "净利润", "required": false, "encrypted": false, "fieldType": "BOOL", "columnName": "net_prof", "compositeKey": false, "defaultValue": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "exc_rate_flu", "name": "汇率变动", "type": "DataStructField", "alias": "excRateFlu", "appId": 43132, "props": {"length": 1, "unique": false, "comment": "汇率变动", "required": false, "encrypted": false, "fieldType": "BOOL", "columnName": "exc_rate_flu", "compositeKey": false, "defaultValue": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "est_gen_com_id", "name": "创建组织", "type": "DataStructField", "alias": "estGenComId", "appId": 43132, "props": {"unique": false, "comment": "创建组织", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "est_gen_com_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "sys_common$org_struct_md", "currentModelAlias": "ERP_FI$fin_glm_ab_item_cf", "relationModelAlias": "sys_common$org_struct_md", "linkModelFieldAlias": null, "currentModelFieldAlias": "estGenComId"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "fin_glm_ab_cft_org_ass_cf", "name": "使用组织", "type": "DataStructField", "alias": "finGlmAbCftOrgAssCf", "appId": 43132, "props": {"unique": false, "comment": "使用组织", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "fin_glm_ab_cft_org_ass_cf", "compositeKey": false, "relationMeta": {"sync": true, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "relationModelKey": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "currentModelAlias": "ERP_FI$fin_glm_ab_item_cf", "relationModelAlias": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "linkModelFieldAlias": "finGlmAbItemCfId", "currentModelFieldAlias": "finGlmAbCftOrgAssCf"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "fin_glm_cft_type_cf", "name": "现金流量项目类别", "type": "DataStructField", "alias": "finGlmCftTypeCf", "appId": 43132, "props": {"unique": false, "comment": "现金流量项目类别", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "fin_glm_cft_type_cf", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_FI$fin_glm_cft_type_cf", "currentModelAlias": "ERP_FI$fin_glm_ab_item_cf", "relationModelAlias": "ERP_FI$fin_glm_cft_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "finGlmCftTypeCf"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "fin_glm_cash_sub_ass_cf", "name": "组织项目科目关联表", "type": "DataStructField", "alias": "finGlmCashSubAssCf", "appId": 43132, "props": {"unique": false, "comment": "组织项目科目关联表", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "fin_glm_cash_sub_ass_cf", "compositeKey": false, "relationMeta": {"sync": true, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "ERP_FI$fin_glm_cash_sub_ass_cf", "relationModelKey": "ERP_FI$fin_glm_cash_sub_ass_cf", "currentModelAlias": "ERP_FI$fin_glm_ab_item_cf", "relationModelAlias": "ERP_FI$fin_glm_cash_sub_ass_cf", "linkModelFieldAlias": "abItemCfId", "currentModelFieldAlias": "finGlmCashSubAssCf"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "fin_glm_cfs_type_cf", "name": "现金流量项目表", "type": "DataStructField", "alias": "finGlmCfsTypeCf", "appId": 43132, "props": {"unique": false, "comment": "现金流量项目表", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "fin_glm_cfs_type_cf", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_FI$fin_glm_cfs_type_cf", "currentModelAlias": "ERP_FI$fin_glm_ab_item_cf", "relationModelAlias": "ERP_FI$fin_glm_cfs_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "finGlmCfsTypeCf"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "parent_id", "name": "父节点", "type": "DataStructField", "alias": "parentId", "appId": 43132, "props": {"unique": false, "comment": "父节点", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "parent_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_FI$fin_glm_ab_item_cf", "currentModelAlias": "ERP_FI$fin_glm_ab_item_cf", "relationModelAlias": "ERP_FI$fin_glm_ab_item_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "parentId"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "is_leaf", "name": "是否叶子节点", "type": "DataStructField", "alias": "<PERSON><PERSON><PERSON><PERSON>", "appId": 43132, "props": {"length": 1, "unique": false, "comment": "是否叶子节点", "required": false, "encrypted": false, "fieldType": "BOOL", "columnName": "is_leaf", "compositeKey": false, "defaultValue": true, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "id", "name": "ID", "type": "DataStructField", "alias": "id", "appId": 43132, "props": {"length": 20, "unique": true, "comment": "ID", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "id", "compositeKey": false, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}, {"ext": false, "key": "created_by", "name": "创建人", "type": "DataStructField", "alias": "created<PERSON>y", "appId": 43132, "props": {"length": 20, "unique": true, "comment": "创建人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "created_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_FI$user", "currentModelAlias": "ERP_FI$fin_glm_ab_item_cf", "relationModelAlias": "ERP_FI$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "created<PERSON>y"}, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "updated_by", "name": "更新人", "type": "DataStructField", "alias": "updatedBy", "appId": 43132, "props": {"length": 20, "unique": true, "comment": "更新人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "updated_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_FI$user", "currentModelAlias": "ERP_FI$fin_glm_ab_item_cf", "relationModelAlias": "ERP_FI$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "updatedBy"}, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "created_at", "name": "创建时间", "type": "DataStructField", "alias": "createdAt", "appId": 43132, "props": {"unique": true, "comment": "创建时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "created_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "updated_at", "name": "更新时间", "type": "DataStructField", "alias": "updatedAt", "appId": 43132, "props": {"unique": true, "comment": "更新时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "updated_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "version", "name": "版本号", "type": "DataStructField", "alias": "version", "appId": 43132, "props": {"length": 20, "unique": true, "comment": "版本号", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "version", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}, {"ext": false, "key": "deleted", "name": "逻辑删除标识", "type": "DataStructField", "alias": "deleted", "appId": 43132, "props": {"length": 20, "unique": true, "comment": "逻辑删除标识", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "deleted", "compositeKey": true, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}, {"ext": false, "key": "origin_org_id", "name": "所属组织", "type": "DataStructField", "alias": "originOrgId", "appId": 43132, "props": {"unique": false, "comment": "所属组织", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "origin_org_id", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}]}}