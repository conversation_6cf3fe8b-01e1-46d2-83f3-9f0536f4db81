{"type": "ServiceDefinition", "name": "信用等级-批量启用主数据服务", "access": "Private", "parentKey": "TB2B", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "serviceDslJson": {"children": [{"id": null, "key": "node_1hotaneev1", "name": "开始", "nextNodeKey": "node_1hotf8ol212", "props": {"globalVariable": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Model", "id": null, "modelKey": "TB2B$gen_cm_cg_md", "relatedModel": {"modelAlias": "TB2B$gen_cm_cg_md", "modelKey": "TB2B$gen_cm_cg_md", "modelName": "TB2B$gen_cm_cg_md"}, "relation": null, "required": null}, "fieldAlias": "list", "fieldKey": "list", "fieldName": "list", "fieldType": "Array", "id": null, "required": null}], "input": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Number", "id": null, "required": null}, "fieldAlias": "ids", "fieldKey": "ids", "fieldName": "ids", "fieldType": "Array", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}], "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "type": "StartProperties"}, "type": "StartNode"}, {"children": [{"children": null, "id": null, "key": "node_1hotf8ol213", "name": "条件", "nextNodeKey": "node_1hotfa9n417", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "NRfQu4UWLchaje2RMQ7hl", "key": null, "leftValue": {"fieldType": "Array", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "ids", "valueName": "ids"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "_w08tbzAoHolTjYvn-3fY", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "Uy83M6whbXIpt_YNR1Wp6", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "ConditionProperties"}, "type": "ConditionNode"}, {"id": null, "key": "node_1hotfa9n417", "name": "HTTP服务", "nextNodeKey": "node_1hotfa17d16", "props": {"body": null, "bodyType": "VALUE", "headers": [], "inputMapping": null, "jsonBody": null, "method": "GET", "output": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "fieldType", "fieldKey": "fieldType", "fieldName": "fieldType", "fieldType": "Text", "id": null, "required": null}, {"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "relationType", "fieldKey": "relationType", "fieldName": "relationType", "fieldType": "Text", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "sync", "fieldKey": "sync", "fieldName": "sync", "fieldType": "Boolean", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "relationModelAlias", "fieldKey": "relationModelAlias", "fieldName": "relationModelAlias", "fieldType": "Text", "id": null, "required": null}], "fieldAlias": "relation_meta", "fieldKey": "relation_meta", "fieldName": "relationMeta", "fieldType": "Object", "id": null, "required": null}], "fieldAlias": "props", "fieldKey": "props", "fieldName": "props", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "alias", "fieldKey": "alias", "fieldName": "alias", "fieldType": "Text", "id": null, "required": null}], "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Object", "id": null, "required": null}, "fieldAlias": "children", "fieldKey": "children", "fieldName": "children", "fieldType": "Array", "id": null, "required": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pathVariables": [], "serviceName": "查询模型元数据", "stream": false, "type": "HttpServiceProperties", "url": "http://localhost:8080/api/trantor/struct-node/find-by-key/TB2B$gen_cm_cg_md"}, "type": "HttpServiceNode"}, {"id": null, "key": "node_1hotfa17d16", "name": "在线JS脚本", "nextNodeKey": "node_1hotfaqgo18", "props": {"inputMapping": [{"field": {"defaultValue": null, "description": null, "element": null, "fieldAlias": "fields", "fieldKey": "fields", "fieldName": "fields", "fieldType": "Array", "id": null, "required": null}, "id": null, "value": {"fieldType": "Array", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "NODE_OUTPUT_node_1hotfa9n417", "valueName": "[HTTP服务]节点出参"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "data", "valueName": "data"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "data", "valueName": "data"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "children", "valueName": "children"}]}}], "language": "JS", "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "field", "fieldKey": "field", "fieldName": "field", "fieldType": "Object", "id": null, "required": null}], "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "script": "for each (var field in fields) {    if (field.alias.toUpperCase() === 'STATUS') {      return field;    }  }  return null;", "scriptEngine": null, "type": "ScriptProperties"}, "type": "ScriptNode"}, {"children": [{"children": null, "id": null, "key": "node_1hotfaqgo19", "name": "条件", "nextNodeKey": "node_1hotfcuod21", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "QS0XxRLl9z1A2XCaO-md2", "key": null, "leftValue": {"fieldType": "Object", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "NODE_OUTPUT_node_1hotfa17d16", "valueName": "[在线JS脚本]节点出参"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "field", "valueName": "field"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "8KwvDkH_zNcC_WtVR8zG9", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "Se3BRtPQswJws99cxXli2", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "ConditionProperties"}, "type": "ConditionNode"}, {"id": null, "key": "node_1hotfcuod21", "name": "在线JS脚本", "nextNodeKey": "node_1hotg1e9j24", "props": {"inputMapping": [{"field": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "field", "fieldKey": "field", "fieldName": "field", "fieldType": "Object", "id": null, "required": null}, "id": null, "value": {"fieldType": "Object", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "NODE_OUTPUT_node_1hotfa17d16", "valueName": "[在线JS脚本]节点出参"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "field", "valueName": "field"}]}}, {"field": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}, "id": null, "value": {"fieldType": "Object", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "request", "valueName": "request"}]}}], "language": "JS", "output": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Text", "id": null, "required": null}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null, "required": null}], "outputAssign": {"customAssignments": [{"field": {"fieldType": "Array", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "modelAlias": null, "relatedModel": {"modelAlias": "TB2B$gen_cm_cg_md", "modelKey": "TB2B$gen_cm_cg_md", "modelName": "TB2B$gen_cm_cg_md"}, "valueKey": "list", "valueName": "list"}]}, "id": "1hotfvh1622", "operator": "EQ", "value": {"fieldType": "Array", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "NODE_OUTPUT_node_1hotfcuod21", "valueName": "出参结构体"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "data", "valueName": "data"}]}}], "outputAssignType": "CUSTOM"}, "script": "var status = null;  var data = [];  if (field.props.fieldType === 'ENUM') {    if (field.props.dictPros !== null && field.props.dictPros.multiSelect === true) {      status = ['ENABLED'];    }else {      status = 'ENABLED';    }    request.ids.forEach(function(id) {     data.push({id:id+'',status:status})   });  }  return data;", "scriptEngine": null, "type": "ScriptProperties"}, "type": "ScriptNode"}, {"children": [{"id": null, "key": "node_1hotg28qk25", "name": "更新数据", "props": {"modelValue": {"fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hotg1e9j24", "valueName": "[循环]循环变量"}, {"fieldType": null, "modelAlias": null, "relatedModel": {"modelAlias": "TB2B$gen_cm_cg_md", "modelKey": "TB2B$gen_cm_cg_md", "modelName": "TB2B$gen_cm_cg_md"}, "valueKey": "_item", "valueName": "循环元素"}]}, "outputAssign": null, "relatedModel": {"modelAlias": "TB2B$gen_cm_cg_md", "modelKey": "TB2B$gen_cm_cg_md", "modelName": "信用等级"}, "type": "CascadeUpdateDataProperties"}, "type": "CascadeUpdateDataNode"}], "headNodeKeys": ["node_1hotg28qk25"], "id": null, "key": "node_1hotg1e9j24", "name": "循环", "props": {"loopData": {"fieldType": "Array", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "modelAlias": null, "relatedModel": {"modelAlias": "TB2B$gen_cm_cg_md", "modelKey": "TB2B$gen_cm_cg_md", "modelName": "TB2B$gen_cm_cg_md"}, "valueKey": "list", "valueName": "list"}]}, "loopElement": {"defaultValue": null, "description": null, "fieldAlias": "list", "fieldKey": "list", "fieldName": "list", "fieldType": "Model", "id": null, "modelKey": "TB2B$gen_cm_cg_md", "relatedModel": {"modelAlias": "TB2B$gen_cm_cg_md", "modelKey": "TB2B$gen_cm_cg_md", "modelName": "TB2B$gen_cm_cg_md"}, "relation": null, "required": null}, "loopType": "DATASET_LOOP", "stopWhenDataEmpty": false, "type": "LoopProperties"}, "type": "LoopNode"}, {"children": null, "id": null, "key": "node_1hotfaqgo20", "name": "else", "props": {"type": "ConditionElseProperties"}, "type": "ConditionElseNode"}], "headNodeKeys": ["node_1hotfaqgo19", "node_1hotfaqgo20"], "id": null, "key": "node_1hotfaqgo18", "name": "排他分支", "props": {"type": "ExclusiveBranchProperties"}, "type": "ExclusiveBranchNode"}, {"children": null, "id": null, "key": "node_1hotf8ol214", "name": "else", "props": {"type": "ConditionElseProperties"}, "type": "ConditionElseNode"}], "headNodeKeys": ["node_1hotf8ol213", "node_1hotf8ol214"], "id": null, "key": "node_1hotf8ol212", "name": "排他分支", "nextNodeKey": "node_1hotaneev2", "props": {"type": "ExclusiveBranchProperties"}, "type": "ExclusiveBranchNode"}, {"id": null, "key": "node_1hotaneev2", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "headNodeKeys": ["node_1hotaneev1"], "id": null, "input": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Number", "id": null, "required": null}, "fieldAlias": "ids", "fieldKey": "ids", "fieldName": "ids", "fieldType": "Array", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}], "key": "TB2B$GEN_CM_CG_MD_MASTER_DATA_MULTI_ENABLE_DATA_SERVICE", "name": "信用等级-批量启用主数据服务", "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}}