package io.terminus.trantor2.console.meta;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.ViewPermissionDTO;
import org.junit.jupiter.api.Test;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ViewPermissionQueryTests extends MetaBaseIntegrationTests {

    @Test
    void testViewPermission() {
        // load behaviors from disk (for test)
        ideHelperBean.loadBehaviors();

        MetaEditAndQueryContext ctx = ideHelperBean.initRepoV2(this.getClass(), "00base", 0L);
        // make sure LinkUtil not change props
        ideHelperBean.assertDatasetProject(this.getClass(), "00base", ctx);

        List<ViewPermissionDTO> perms = metaQueryService.findViewPermissions(ctx, "test_portal", Lists.newArrayList("test$view1", "test$view2", "test$view3", "test$view4", "test$view5", "test$view6", "test$view7"));

        ideHelperBean.assertDatasetSimpleJson(this.getClass(), "01perms",
                ImmutableMap.of(
                        "expect", perms
                )
        );
    }
}
