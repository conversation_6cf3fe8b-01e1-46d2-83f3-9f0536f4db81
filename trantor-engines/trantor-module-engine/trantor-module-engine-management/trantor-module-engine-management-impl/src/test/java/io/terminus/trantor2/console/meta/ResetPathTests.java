package io.terminus.trantor2.console.meta;

import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.PathFilter;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
public class ResetPathTests extends MetaBaseIntegrationTests {

    @Test
    void testResetPath() {
        // load behaviors from disk (for test)
        ideHelperBean.loadBehaviors();

        try (MockedStatic<KeyUtil> newKeyMock = Mockito.mockStatic(KeyUtil.class, Mockito.CALLS_REAL_METHODS)) {
            AtomicInteger counter = new AtomicInteger(1);
            newKeyMock.when(KeyUtil::newKey).then(invocation -> "random-key-" + counter.getAndIncrement());

            MetaEditAndQueryContext ctx = ideHelperBean.initRepoV2(this.getClass(), "00base", 0L);

            // 0: init base
            {
                // make sure LinkUtil not change props
                ideHelperBean.assertDatasetProject(this.getClass(), "00base", ctx);
            }
            String baseSnapshotOid = ideHelperBean.doSnapshot(ctx);

            // 01-a: add ext module
            {
                // add sys_common module (and it's ext)
                ideHelperBean.deltaInsert(this.getClass(), "01add_ext_module_a", ctx);

                // assert dataset after add ext module
                ideHelperBean.assertDatasetProject(this.getClass(), "02after_add_ext_module_a", ctx);
            }
            String afterAddExtModuleSnapshotOid = ideHelperBean.doSnapshot(ctx);

            // 01-b: (re) add ext module without m11
            {
                metaEditService.submitOp(ctx, EditUtil.resetFull(baseSnapshotOid));

                // add sys_common module (and it's ext)
                ideHelperBean.deltaInsert(this.getClass(), "01add_ext_module_b_no_m11", ctx);

                // assert dataset after add ext module
                ideHelperBean.assertDatasetProject(this.getClass(), "02after_add_ext_module_b_no_m11", ctx);
            }
            String afterAddExtModuleNoM11SnapshotOid = ideHelperBean.doSnapshot(ctx);

            // 03: change meta
            //   - clear view 1 (in dir1)
            //   - clear service1 (in dir2)
            //   - clear m11 (in sys_common/未分组 and extended)
            {
                metaQueryService.findByKey(ctx, "test$view1").ifPresent(view -> {
                    metaEditService.submitOp(ctx, EditUtil.deleteNodeOp(view.getKey(), false));
                });
                metaQueryService.findByKey(ctx, "test$service1_copy").ifPresent(service -> {
                    metaEditService.submitOp(ctx, EditUtil.deleteNodeOp(service.getKey(), false));
                });

                ideHelperBean.assertDatasetProject(this.getClass(), "03change_dir1_dir2_ungroup", ctx);
            }
            String changeDir1Dir2UngroupSnapshotOid = ideHelperBean.doSnapshot(ctx);

            // 04: only reset back dir1
            {
                metaEditService.submitOp(ctx, EditUtil.resetPath(afterAddExtModuleSnapshotOid, new PathFilter("test", "/dir1/a/b/c")));
                ideHelperBean.assertDatasetProject(this.getClass(), "04only_reset_back_dir1", ctx);
            }

            // 05: only reset back dir2
            {
                metaEditService.submitOp(ctx, EditUtil.resetFull(changeDir1Dir2UngroupSnapshotOid));
                ideHelperBean.assertDatasetProject(this.getClass(), "03change_dir1_dir2_ungroup", ctx);

                metaEditService.submitOp(ctx, EditUtil.resetPath(afterAddExtModuleSnapshotOid, new PathFilter("test", "/dir2")));
                ideHelperBean.assertDatasetProject(this.getClass(), "05only_reset_back_dir2", ctx);
            }

            // 05: only reset back sys_common 未分组
            {
                metaEditService.submitOp(ctx, EditUtil.resetFull(changeDir1Dir2UngroupSnapshotOid));
                ideHelperBean.assertDatasetProject(this.getClass(), "03change_dir1_dir2_ungroup", ctx);

                metaEditService.submitOp(ctx, EditUtil.resetPath(afterAddExtModuleSnapshotOid, new PathFilter("sys_common", "/未分组")));
                ideHelperBean.assertDatasetProject(this.getClass(), "06only_reset_back_sys_common_ungroup", ctx);
            }
        }
    }
}
