{"type": "ServiceDefinition", "name": "物料采购视图-分页数据服务", "access": "Private", "parentKey": "ERP_GEN", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "serviceDslJson": {"children": [{"id": null, "key": "node_1h9a9hm2n23", "name": "开始", "nextNodeKey": "node_1h9aa6nhd27", "props": {"globalVariable": null, "input": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "conditionGroup", "fieldKey": "conditionGroup", "fieldName": "条件组", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "conditionItems", "fieldKey": "conditionItems", "fieldName": "简化版条件组", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "element": null, "fieldAlias": "sortOrders", "fieldKey": "sortOrders", "fieldName": "字段排序", "fieldType": "Array", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "pageNo", "fieldKey": "pageNo", "fieldName": "页码", "fieldType": "Number", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "pageSize", "fieldKey": "pageSize", "fieldName": "每页数量", "fieldType": "Number", "id": null, "required": null}], "fieldAlias": "pageable", "fieldKey": "pageable", "fieldName": "pageable", "fieldType": "Pageable", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}], "output": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "total", "fieldKey": "total", "fieldName": "total", "fieldType": "Number", "id": null, "required": null}, {"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_pur_md", "modelKey": "ERP_GEN$gen_mat_pur_md", "modelName": "物料采购视图"}, "relation": null, "required": null}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null, "required": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Paging", "id": null, "required": null}], "type": "StartProperties"}, "type": "StartNode"}, {"id": null, "key": "node_1h9aa6nhd27", "name": "查询数据", "nextNodeKey": "node_1h9a9hm2n24", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "NFDhqM0Qa7M866ubjb5D6", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "genMatMdId", "fieldName": "genMatMdId", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "id", "fieldName": "id", "fieldType": null, "modelKey": null, "relatedModel": null}], "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "genMatMdId", "valueName": "genMatMdId"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"constValue": null, "fieldPaths": [{"fieldKey": "REQUEST", "fieldName": "REQUEST", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "request", "fieldName": "request", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "genMatMdId_id", "fieldName": "genMatMdId_id", "fieldType": null, "modelKey": null, "relatedModel": null}], "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "REQUEST"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "genMatMdId_id", "valueName": "genMatMdId_id"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "-hzn38n9KnI7CqkK4XRg3", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "Ggg_vUixft2kPxNMqjt_O", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "PAGING", "desensitized": true, "maximum": null, "outputAssign": {"customAssignments": [{"field": {"constValue": null, "fieldPaths": [{"fieldKey": "OUTPUT", "fieldName": "服务出参", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "data", "fieldName": "data", "fieldType": null, "modelKey": null, "relatedModel": null}], "fieldType": "Paging", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "data", "valueName": "data"}]}, "id": "1h9pl7l121", "operator": "EQ", "value": {"constValue": null, "fieldPaths": [{"fieldKey": "NODE_OUTPUT_node_1h9aa6nhd27", "fieldName": "出参结构体", "fieldType": null, "modelKey": null, "relatedModel": null}], "fieldType": "Paging", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "NODE_OUTPUT_node_1h9aa6nhd27", "valueName": "出参结构体"}]}}], "outputAssignType": "CUSTOM"}, "pageable": {"constValue": null, "fieldPaths": [{"fieldKey": "REQUEST", "fieldName": "服务入参", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "request", "fieldName": "request", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "pageable", "fieldName": "pageable", "fieldType": null, "modelKey": null, "relatedModel": null}], "fieldType": "Pageable", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "pageable", "valueName": "pageable"}]}, "queryFields": [{"conditionGroup": null, "fieldKey": "purOrg", "subModelFields": [{"conditionGroup": null, "fieldKey": "id", "subModelFields": null}, {"conditionGroup": null, "fieldKey": "orgName", "subModelFields": null}]}, {"conditionGroup": null, "fieldKey": "purUomId", "subModelFields": [{"conditionGroup": null, "fieldKey": "id", "subModelFields": null}, {"conditionGroup": null, "fieldKey": "uomDesc", "subModelFields": null}]}, {"conditionGroup": null, "fieldKey": "purPrice", "subModelFields": null}, {"conditionGroup": null, "fieldKey": "currId", "subModelFields": [{"conditionGroup": null, "fieldKey": "id", "subModelFields": null}, {"conditionGroup": null, "fieldKey": "currName", "subModelFields": null}]}, {"conditionGroup": null, "fieldKey": "isPurSrcList", "subModelFields": null}, {"conditionGroup": null, "fieldKey": "taxId", "subModelFields": [{"conditionGroup": null, "fieldKey": "id", "subModelFields": null}, {"conditionGroup": null, "fieldKey": "taxCode", "subModelFields": null}, {"conditionGroup": null, "fieldKey": "tax", "subModelFields": null}]}], "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_pur_md", "modelKey": "ERP_GEN$gen_mat_pur_md", "modelName": "物料采购视图"}, "sortOrder": null, "sortOrders": [{"field": null, "fieldAlias": "id", "sortType": "DESC"}], "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "type": "RetrieveDataNode"}, {"id": null, "key": "node_1h9a9hm2n24", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "headNodeKeys": ["node_1h9a9hm2n23"], "id": null, "input": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "conditionGroup", "fieldKey": "conditionGroup", "fieldName": "条件组", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "conditionItems", "fieldKey": "conditionItems", "fieldName": "简化版条件组", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "element": null, "fieldAlias": "sortOrders", "fieldKey": "sortOrders", "fieldName": "字段排序", "fieldType": "Array", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "pageNo", "fieldKey": "pageNo", "fieldName": "页码", "fieldType": "Number", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "pageSize", "fieldKey": "pageSize", "fieldName": "每页数量", "fieldType": "Number", "id": null, "required": null}], "fieldAlias": "pageable", "fieldKey": "pageable", "fieldName": "pageable", "fieldType": "Pageable", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}], "key": "ERP_GEN$gen_mat_pur_md_PAGING_DATA_SERVICE_unique2", "name": "物料采购视图-分页数据服务", "output": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "total", "fieldKey": "total", "fieldName": "total", "fieldType": "Number", "id": null, "required": null}, {"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_pur_md", "modelKey": "ERP_GEN$gen_mat_pur_md", "modelName": "物料采购视图"}, "relation": null, "required": null}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null, "required": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Paging", "id": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}}