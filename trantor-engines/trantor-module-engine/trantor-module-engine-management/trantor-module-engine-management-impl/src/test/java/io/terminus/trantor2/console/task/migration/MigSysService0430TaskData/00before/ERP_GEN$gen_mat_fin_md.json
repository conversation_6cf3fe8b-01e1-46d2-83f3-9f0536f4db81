{"type": "Model", "name": "物料财务视图", "parentKey": "ERP_GEN", "children": [], "props": {"desc": null, "alias": "ERP_GEN$gen_mat_fin_md", "props": {"type": "PERSIST", "config": {"self": false, "system": false, "persist": false, "selfRelationFieldAlias": null}, "mainField": "inv_org_id", "tableName": "gen_mat_fin_md", "searchModel": false, "mainFieldAlias": "invOrgId", "physicalDelete": false, "shardingConfig": {"enabled": false, "shardingFields": null, "shardingSuffixLength": 3}, "originOrgIdEnabled": true}, "children": [{"ext": false, "key": "inv_org_id", "name": "库存组织", "type": "DataStructField", "alias": "invOrgId", "props": {"unique": false, "comment": "库存组织", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "inv_org_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "sys_common$org_struct_md", "currentModelAlias": "ERP_GEN$gen_mat_fin_md", "relationModelAlias": "sys_common$org_struct_md", "linkModelFieldAlias": null, "currentModelFieldAlias": "invOrgId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "mvm_avg_prc", "name": "移动平均成本价", "type": "DataStructField", "alias": "mvmAvgPrc", "props": {"unique": false, "comment": "移动平均成本价", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "mvm_avg_prc", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "currency"}}, {"ext": false, "key": "std_avg_prc", "name": "标准成本价", "type": "DataStructField", "alias": "stdAvgPrc", "props": {"unique": false, "comment": "标准成本价", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "std_avg_prc", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "currency"}}, {"ext": false, "key": "prc_method", "name": "计价方式", "type": "DataStructField", "alias": "prcMethod", "props": {"length": 256, "unique": false, "comment": "计价方式", "dictPros": {"dictValues": [{"label": "先进先出法", "value": "FIFO"}, {"label": "后进先出发", "value": "LIFO"}, {"label": "加权平均法", "value": "WA"}, {"label": "移动加权法", "value": "MWA"}, {"label": "计划成本法", "value": "PLAN"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "prc_method", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "id", "name": "ID", "type": "DataStructField", "alias": "id", "props": {"length": 20, "unique": true, "comment": "ID", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "id", "compositeKey": false, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "created_by", "name": "创建人", "type": "DataStructField", "alias": "created<PERSON>y", "props": {"length": 20, "unique": true, "comment": "创建人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "created_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_mat_fin_md", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "created<PERSON>y"}, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "updated_by", "name": "更新人", "type": "DataStructField", "alias": "updatedBy", "props": {"length": 20, "unique": true, "comment": "更新人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "updated_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_mat_fin_md", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "updatedBy"}, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "created_at", "name": "创建时间", "type": "DataStructField", "alias": "createdAt", "props": {"unique": true, "comment": "创建时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "created_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "updated_at", "name": "更新时间", "type": "DataStructField", "alias": "updatedAt", "props": {"unique": true, "comment": "更新时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "updated_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "version", "name": "版本号", "type": "DataStructField", "alias": "version", "props": {"length": 20, "unique": true, "comment": "版本号", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "version", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "deleted", "name": "逻辑删除标识", "type": "DataStructField", "alias": "deleted", "props": {"length": 20, "unique": true, "comment": "逻辑删除标识", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "deleted", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "origin_org_id", "name": "所属组织", "type": "DataStructField", "alias": "originOrgId", "props": {"length": 20, "unique": true, "comment": "所属组织", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "origin_org_id", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "gen_mat_md_id", "name": "genMatMdId", "type": "DataStructField", "alias": "genMatMdId", "props": {"unique": false, "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "gen_mat_md_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_mat_md", "currentModelAlias": "ERP_GEN$gen_mat_fin_md", "relationModelAlias": "ERP_GEN$gen_mat_md", "linkModelFieldAlias": null, "currentModelFieldAlias": "genMatMdId"}, "autoGenerated": true, "isSystemField": false}}]}}