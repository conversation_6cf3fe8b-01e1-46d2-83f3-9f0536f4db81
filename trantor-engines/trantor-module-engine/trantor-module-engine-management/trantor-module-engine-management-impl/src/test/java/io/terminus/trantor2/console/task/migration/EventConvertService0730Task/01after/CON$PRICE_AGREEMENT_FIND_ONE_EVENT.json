{"access": "Private", "parentKey": "CON", "name": "根据协议id查询价格协议事件", "type": "Event", "key": "CON$PRICE_AGREEMENT_FIND_ONE_EVENT", "props": {"returnModel": {"key": "CON$con_pri_aggrement_head_tr"}, "returnModelArrayWhether": false, "model": {"key": "CON$con_pri_aggrement_head_tr"}, "enabledStatusVerify": false, "modelArrayWhether": false, "enabledTransaction": true, "relations": [{"actionType": "Action", "code": "CON$TSRM_PRICE_AGT_FIND_ONE_ACTION", "enabledParamCheck": false}], "states": []}}