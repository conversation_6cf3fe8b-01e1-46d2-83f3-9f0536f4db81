{"parentKey": "CON", "name": "价格协议-批量启用价格协议", "type": "Action", "key": "CON$TSRM_PRICE_AGT_BATCH_ENABLE_ACTION", "props": {"responseType": "io.terminus.common.api.model.Paging<io.terminus.tsrm.price.spi.model.agreement.dto.PriceAgreementDTO>", "languageType": "Java", "method": "batchEnable", "requestType": "java.util.List<io.terminus.tsrm.price.spi.model.agreement.dto.PriceAgreementDTO>", "bean": "PriceAgreementAction", "order": 10, "status": "enabled"}}