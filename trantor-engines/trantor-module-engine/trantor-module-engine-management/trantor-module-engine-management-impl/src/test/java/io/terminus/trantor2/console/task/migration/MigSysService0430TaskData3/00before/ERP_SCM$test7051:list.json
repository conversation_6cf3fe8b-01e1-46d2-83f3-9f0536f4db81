{"type": "View", "name": "list", "parentKey": "ERP_SCM", "children": [], "props": {"key": "ERP_SCM$test7051:list", "type": "LIST", "title": "list", "content": {"key": "ERP_SCM$test7051-page", "name": "View", "type": "Container", "props": {}, "children": [{"key": "ERP_SCM$test7051-column-page", "name": "ColumnPage", "type": "Layout", "props": {}, "children": [{"key": "ERP_SCM$test7051-left-container", "name": "Box", "type": "Layout", "props": {"style": {"height": "100%", "display": "flex", "flexDirection": "column"}}, "children": [{"key": "ERP_SCM$test7051-left-button-wrapper", "name": "Space", "type": "Layout", "props": {"style": {"marginBottom": 12}}, "children": [{"key": "ERP_SCM$test7051-create-btn", "name": "<PERSON><PERSON>", "type": "Widget", "props": {"type": "primary", "label": "新建 ", "confirmOn": "off", "buttonType": "default", "actionConfig": {"endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "type": "NewPage", "refresh": true}}]}}, "children": []}, {"key": "ERP_SCM$test7051-import-btn", "name": "ImportButton", "type": "Widget", "props": {"serviceProps": {"saveFlow": {"url": "/api/gei/task/import-sub-model", "type": "InvokeApi", "method": "GET"}, "predictFlow": {"type": "InvokeService", "serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST"}, "saveSubFlow": {"url": "/api/gei/task/import-direct-by-oss", "type": "InvokeApi", "method": "GET"}, "downloadFlow": {"url": "/api/gei/template/download", "type": "InvokeApi", "method": "GET"}, "isCustomFlow": {"type": "InvokeService", "serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST"}, "saveServiceFlow": {"type": "InvokeService", "serviceKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST"}, "saveSubServiceFlow": {"type": "InvokeService", "serviceKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST"}}}, "children": []}]}, {"key": "ERP_SCM$test7051-tree", "name": "Tree", "type": "Container", "props": {"flow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindTreeChildrenDataService"}, "onSelect$": "(key, info) => $context.mode !== 'design' && navigate({ action: 'show', recordId: info.node.id })", "treeField": "mat<PERSON>ate<PERSON><PERSON>nt", "isLazyLoad": true, "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "leafFieldName": "<PERSON><PERSON><PERSON><PERSON>", "leafFieldValue": true}, "children": [{"key": "ERP_SCM$test7051-tree-recordActions", "name": "RecordActions", "type": "Layout", "props": {}, "children": [{"key": "ERP_SCM$test7051-tree-addChildBtn", "name": "<PERSON><PERSON>", "type": "Widget", "props": {"type": "default", "label": "新建子类目", "confirmOn": "off", "buttonType": "default", "actionConfig": {"endLogic": "Other", "executeLogic": "ExecuteScript", "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form"}], "executeScriptConfig": "navigate({ action: 'new', query: { parentId: record.id } })"}}, "children": []}, {"key": "ERP_SCM$test7051-tree-editBtn", "name": "<PERSON><PERSON>", "type": "Widget", "props": {"type": "default", "label": "编辑", "confirmOn": "off", "buttonType": "default", "actionConfig": {"endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "type": "NewPage", "params": [{"name": "recordId", "type": "expression", "expression": "record?.id"}], "refresh": true}}]}, "showCondition": {"type": "ConditionGroup", "conditions": [{"type": "ConditionGroup", "conditions": [{"type": "ConditionLeaf", "operator": "NEQ", "leftValue": {"val": "status", "type": "VarValue", "scope": "row", "title": "status", "value": "status", "target": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}], "fieldType": "Enum", "valueType": "VAR"}, "rightValue": {"type": "VarValue", "valueType": "CONST", "constValue": "ENABLED"}}], "logicOperator": "AND"}], "logicOperator": "OR"}}, "children": []}, {"key": "ERP_SCM$test7051-tree-enableBtn", "name": "<PERSON><PERSON>", "type": "Widget", "props": {"type": "default", "label": "启用", "confirmOn": "off", "buttonType": "default", "actionConfig": {"endLogic": "Other", "executeLogic": "BindService", "beforeLogicConfig": [{"text": "确认启用吗？", "type": "Modal", "action": "Confirm"}], "bindServiceConfig": {"params": [{"fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "fieldAlias": "<PERSON><PERSON><PERSON>", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}, {"elements": [{"fieldName": "id", "fieldType": "Text", "fieldAlias": "id", "valueConfig": {"name": "id", "type": "expression", "expression": "record.id"}}], "fieldName": "request", "fieldType": "Object", "fieldAlias": "request"}], "service": "ERP_GEN$gen_mat_cate_md_MASTER_DATA_ENABLE_DATA_SERVICE"}, "endLogicOtherConfig": [{"action": "Message", "message": "启用成功"}, {"action": "Refresh", "target": "ERP_SCM$test7051-tree"}, {"action": "Refresh", "target": "ERP_SCM$test7051-detailView-container"}]}, "showCondition": {"type": "ConditionGroup", "conditions": [{"type": "ConditionGroup", "conditions": [{"type": "ConditionLeaf", "operator": "NEQ", "leftValue": {"val": "status", "type": "VarValue", "scope": "row", "title": "status", "value": "status", "target": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}], "fieldType": "Enum", "valueType": "VAR"}, "rightValue": {"type": "VarValue", "valueType": "CONST", "constValue": "ENABLED"}}], "logicOperator": "AND"}], "logicOperator": "OR"}}, "children": []}, {"key": "ERP_SCM$test7051-tree-disableBtn", "name": "<PERSON><PERSON>", "type": "Widget", "props": {"type": "default", "label": "停用", "confirmOn": "off", "buttonType": "default", "actionConfig": {"endLogic": "Other", "executeLogic": "BindService", "beforeLogicConfig": [{"text": "确认停用吗？", "type": "Modal", "action": "Confirm"}], "bindServiceConfig": {"params": [{"fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "fieldAlias": "<PERSON><PERSON><PERSON>", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}, {"elements": [{"fieldName": "id", "fieldType": "Text", "fieldAlias": "id", "valueConfig": {"name": "id", "type": "expression", "expression": "record.id"}}], "fieldName": "request", "fieldType": "Object", "fieldAlias": "request"}], "service": "ERP_GEN$gen_mat_cate_md_MASTER_DATA_DISABLE_DATA_SERVICE"}, "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_SCM$test7051-detailView-container"}, {"action": "Refresh", "target": "ERP_SCM$test7051-tree"}, {"action": "Message", "message": "停用成功"}]}, "showCondition": {"type": "ConditionGroup", "conditions": [{"type": "ConditionGroup", "conditions": [{"type": "ConditionLeaf", "operator": "EQ", "leftValue": {"val": "status", "type": "VarValue", "scope": "row", "title": "status", "value": "status", "target": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}], "fieldType": "Enum", "valueType": "VAR"}, "rightValue": {"type": "VarValue", "valueType": "CONST", "constValue": "ENABLED"}}], "logicOperator": "AND"}], "logicOperator": "OR"}}, "children": []}, {"key": "ERP_SCM$test7051-tree-deleteBtn", "name": "<PERSON><PERSON>", "type": "Widget", "props": {"type": "default", "label": "删除", "confirmOn": "off", "buttonType": "default", "actionConfig": {"endLogic": "Other", "executeLogic": "BindService", "beforeLogicConfig": [{"text": "确认删除吗？", "type": "Modal", "action": "Confirm"}], "bindServiceConfig": {"params": [{"fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "fieldAlias": "<PERSON><PERSON><PERSON>", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}, {"elements": [{"fieldName": "id", "fieldType": "Text", "fieldAlias": "id", "valueConfig": {"name": "id", "type": "expression", "expression": "record.id"}}], "fieldName": "request", "fieldType": "Object", "fieldAlias": "request"}], "service": "ERP_GEN$gen_mat_cate_md_DELETE_DATA_BY_ID_SERVICE"}, "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_SCM$test7051-tree"}, {"action": "Message", "message": "删除成功"}]}, "showCondition": {"type": "ConditionGroup", "conditions": [{"type": "ConditionGroup", "conditions": [{"type": "ConditionLeaf", "operator": "NEQ", "leftValue": {"val": "status", "type": "VarValue", "scope": "row", "title": "status", "value": "status", "target": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}], "fieldType": "Enum", "valueType": "VAR"}, "rightValue": {"type": "VarValue", "valueType": "CONST", "constValue": "ENABLED"}}], "logicOperator": "AND"}], "logicOperator": "OR"}}, "children": []}]}]}]}, {"key": "ERP_SCM$test7051-column-page-stackView", "name": "StackView", "type": "Container", "props": {"key": "list", "items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}]}, "children": [{"key": "ERP_SCM$test7051-list-ERP_GEN$gen_mat_cate_md-empty-box", "name": "Box", "type": "Layout", "props": {"style": {"height": "100%", "display": "flex", "align-items": "center", "justify-content": "center"}}, "children": [{"key": "ERP_SCM$test7051-list-ERP_GEN$gen_mat_cate_md-empty", "name": "Empty", "props": {"imageStyle": {"width": 200, "height": 200}, "description": "从左侧选中数据后查看详情~"}, "children": []}]}, {"key": "ERP_SCM$test7051-detailView-container", "name": "View", "type": "Container", "props": {}, "children": [{"key": "ERP_SCM$test7051-detailView-page", "name": "Page", "type": "Container", "props": {"params": [], "modelAlias": "ERP_GEN$gen_mat_cate_md", "showFooter": false, "showHeader": true}, "children": [{"key": "ERP_SCM$test7051-detailView-page-title", "name": "Page<PERSON><PERSON>le", "type": "Meta", "props": {"title": "{{((data?.code || \"\") + (data?.name || \"\")) || \"类目配置表详情\"}}", "useExpression": true}, "children": []}, {"key": "ERP_SCM$test7051-detailView-page-title-extra", "name": "PageTitleExtra", "type": "Layout", "props": {"children": [{"name": "If", "props": {"test": true}, "children": [{"name": "Status", "props": {"text$": "(n=>({DRAFT:{type:\"default\",text:\"草稿\"},UNENABLED:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISENABLED:{type:\"failure\",text:\"已停用\"},DELETED:{type:\"failure\",text:\"已删除\"},SUBMITTED:{type:\"default\",text:\"已提交\"}})[n])(data?.status)?.text", "type$": "(n=>({DRAFT:{type:\"default\",text:\"草稿\"},UNENABLED:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISENABLED:{type:\"failure\",text:\"已停用\"},DELETED:{type:\"failure\",text:\"已删除\"},SUBMITTED:{type:\"default\",text:\"已提交\"}})[n])(data?.status)?.type"}}]}]}, "children": []}, {"key": "ERP_SCM$test7051-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "Layout", "props": {}, "children": [{"key": "ERP_SCM$test7051-detailView-actions", "name": "Space", "type": "Layout", "props": {}, "children": [{"key": "ERP_SCM$test7051-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"type": "default", "label": "删除", "confirmOn": "off", "buttonType": "default", "actionConfig": {"endLogic": "Other", "executeLogic": "BindService", "beforeLogicConfig": [{"text": "确认删除吗？", "type": "Modal", "action": "Confirm"}], "bindServiceConfig": {"params": [{"fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "fieldAlias": "<PERSON><PERSON><PERSON>", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}, {"elements": [{"fieldName": "id", "fieldType": "Text", "fieldAlias": "id", "valueConfig": {"name": "id", "type": "expression", "expression": "route.recordId"}}], "fieldName": "request", "fieldType": "Object", "fieldAlias": "request"}], "service": "ERP_GEN$gen_mat_cate_md_DELETE_DATA_BY_ID_SERVICE"}, "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_SCM$test7051-tree"}, {"action": "Message", "message": "删除成功"}]}, "showCondition": {"type": "ConditionGroup", "conditions": [{"type": "ConditionGroup", "conditions": [{"type": "ConditionLeaf", "operator": "NEQ", "leftValue": {"val": "status", "type": "VarValue", "scope": "form", "title": "status", "value": "ERP_GEN$gen_mat_cate_md.status", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}], "fieldType": "Enum", "valueType": "VAR"}, "rightValue": {"type": "VarValue", "fieldType": "Enum", "valueType": "CONST", "constValue": "ENABLED"}}], "logicOperator": "AND"}], "logicOperator": "OR"}}, "children": []}, {"key": "ERP_SCM$test7051-detailView-actions-disable", "name": "<PERSON><PERSON>", "type": "Widget", "props": {"type": "default", "label": "停用", "confirmOn": "off", "buttonType": "default", "actionConfig": {"endLogic": "Other", "executeLogic": "BindService", "beforeLogicConfig": [{"text": "确认停用吗？", "type": "Popconfirm", "action": "Confirm"}], "bindServiceConfig": {"params": [{"fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "fieldAlias": "<PERSON><PERSON><PERSON>", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}, {"elements": [{"fieldName": "id", "fieldType": "Text", "fieldAlias": "id", "valueConfig": {"name": "id", "type": "expression", "expression": "route.recordId"}}], "fieldName": "request", "fieldType": "Object", "fieldAlias": "request"}], "service": "ERP_GEN$gen_mat_cate_md_MASTER_DATA_DISABLE_DATA_SERVICE"}, "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_SCM$test7051-detailView-container"}, {"action": "Refresh", "target": "ERP_SCM$test7051-tree"}, {"action": "Message", "message": "停用成功"}]}, "showCondition": {"type": "ConditionGroup", "conditions": [{"type": "ConditionGroup", "conditions": [{"type": "ConditionLeaf", "operator": "EQ", "leftValue": {"val": "status", "type": "VarValue", "scope": "form", "title": "status", "value": "ERP_GEN$gen_mat_cate_md.status", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}], "fieldType": "Enum", "valueType": "VAR"}, "rightValue": {"type": "VarValue", "fieldType": "Enum", "valueType": "CONST", "constValue": "ENABLED"}}], "logicOperator": "AND"}], "logicOperator": "OR"}}, "children": []}, {"key": "ERP_SCM$test7051-detailView-actions-enable", "name": "<PERSON><PERSON>", "type": "Widget", "props": {"type": "default", "label": "启用", "confirmOn": "off", "buttonType": "default", "actionConfig": {"endLogic": "Other", "executeLogic": "BindService", "beforeLogicConfig": [{"text": "确认启用吗？", "type": "Popconfirm", "action": "Confirm"}], "bindServiceConfig": {"params": [{"fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "fieldAlias": "<PERSON><PERSON><PERSON>", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}, {"elements": [{"fieldName": "id", "fieldType": "Text", "fieldAlias": "id", "valueConfig": {"name": "id", "type": "expression", "expression": "route.recordId"}}], "fieldName": "request", "fieldType": "Object", "fieldAlias": "request"}], "service": "ERP_GEN$gen_mat_cate_md_MASTER_DATA_ENABLE_DATA_SERVICE"}, "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_SCM$test7051-detailView-container"}, {"action": "Refresh", "target": "ERP_SCM$test7051-tree"}, {"action": "Message", "message": "启用成功"}]}, "showCondition": {"type": "ConditionGroup", "conditions": [{"type": "ConditionGroup", "conditions": [{"type": "ConditionLeaf", "operator": "NEQ", "leftValue": {"val": "status", "type": "VarValue", "scope": "form", "title": "status", "value": "ERP_GEN$gen_mat_cate_md.status", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}], "fieldType": "Enum", "valueType": "VAR"}, "rightValue": {"type": "VarValue", "fieldType": "Enum", "valueType": "CONST", "constValue": "ENABLED"}}], "logicOperator": "AND"}], "logicOperator": "OR"}}, "children": []}, {"key": "ERP_SCM$test7051-detailView-actions-edit", "name": "<PERSON><PERSON>", "type": "Widget", "props": {"type": "primary", "label": "编辑", "confirmOn": "off", "buttonType": "default", "actionConfig": {"endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "type": "NewPage", "params": [{"name": "recordId", "type": "expression", "expression": "route.recordId"}], "refresh": true}}]}, "showCondition": {"type": "ConditionGroup", "conditions": [{"type": "ConditionGroup", "conditions": [{"type": "ConditionLeaf", "operator": "NEQ", "leftValue": {"val": "status", "type": "VarValue", "scope": "form", "title": "status", "value": "ERP_GEN$gen_mat_cate_md.status", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}], "fieldType": "Enum", "valueType": "VAR"}, "rightValue": {"type": "VarValue", "fieldType": "Enum", "valueType": "CONST", "constValue": "ENABLED"}}], "logicOperator": "AND"}], "logicOperator": "OR"}}, "children": []}, {"key": "ERP_SCM$test7051-detail-ERP_GEN$gen_mat_cate_md-logs", "name": "Logs", "type": "Widget", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"type": "InvokeService", "serviceKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST"}}}, "children": []}]}]}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "name": "Detail", "type": "Container", "props": {"flow": {"type": "InvokeService", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$gen_mat_cate_md_FIND_DATA_BY_ID_SERVICE_ykcOjc1", "containerKey": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail"}, "onFinish$": "(values) => invokeSystemService(\"ERP_GEN$SYS_MasterData_SaveDataService\", \"ERP_GEN$gen_mat_cate_md\", {...data, ...values}).then(() => $(\"ERP_SCM$test7051-detailView-page\").action(\"reload\"))", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "children": [{"key": "ERP_SCM$test7051-detailView-defaultTabs", "name": "Tabs", "type": "Layout", "props": {"items": [{"label": "主体信息"}, {"label": "系统信息"}], "underline": true}, "children": [{"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "name": "TabItem", "type": "Layout", "props": {}, "children": [{"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "name": "DetailGroupItem", "type": "Layout", "props": {"title": false}, "children": [{"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-matCateCode", "name": "DetailField", "props": {"name": "matCateCode", "type": "TEXT", "label": "类目编码", "hidden": false, "editable": false, "componentProps": {"fieldAlias": "matCateCode", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}}, "children": []}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-matCateName", "name": "DetailField", "props": {"name": "matCateName", "type": "TEXT", "label": "类目名称", "hidden": false, "editable": false, "componentProps": {"fieldAlias": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}}, "children": []}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-isLeaf", "name": "DetailField", "props": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "BOOL", "label": "是否叶子节点", "hidden": true, "editable": false, "componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}}, "children": []}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-status", "name": "DetailField", "props": {"name": "status", "type": "SELECT", "label": "状态", "hidden": false, "editable": false, "componentProps": {"fieldAlias": "status", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}}, "children": []}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-genCounClassMatTaxItemCfId", "name": "DetailField", "props": {"name": "genCounClassMatTaxItemCfId", "type": "OBJECT", "label": "物料税", "hidden": false, "editable": false, "componentProps": {"label": "选择物料税", "columns": ["counId", "taxType", "taxClassId", "taxClassDesc", "genVendFinMdId", "genCustFinMdId", "matSlsId"], "fieldAlias": "genCounClassMatTaxItemCfId", "labelField": "taxType", "modelAlias": "ERP_GEN$gen_coun_class_mat_tax_item_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$gen_coun_class_mat_tax_item_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$gen_coun_class_mat_tax_item_cf_PAGING_DATA_SERVICE"}, "parentModelAlias": "ERP_GEN$gen_mat_cate_md"}}, "children": []}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-qualificationsGroupId", "name": "DetailField", "props": {"name": "qualificationsGroupId", "type": "OBJECT", "label": "关联资质组", "hidden": false, "editable": false, "componentProps": {"label": "选择关联资质组", "columns": ["qualificationsHeadCode", "qualificationsHeadName"], "fieldAlias": "qualificationsGroupId", "labelField": "qualificationsHeadCode", "modelAlias": "ERP_SCM$gen_qualifications_head_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$gen_qualifications_head_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$gen_qualifications_head_cf_PAGING_DATA_SERVICE"}, "parentModelAlias": "ERP_GEN$gen_mat_cate_md"}}, "children": []}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-isLimitPoQualifications", "name": "DetailField", "props": {"name": "isLimitPoQualifications", "type": "BOOL", "label": "采购订单是否限制资质", "hidden": false, "editable": false, "componentProps": {"fieldAlias": "isLimitPoQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}}, "children": []}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-isLimitSoQualifications", "name": "DetailField", "props": {"name": "isLimitSoQualifications", "type": "BOOL", "label": "销售订单是否限制资质", "hidden": false, "editable": false, "componentProps": {"fieldAlias": "isLimitSoQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}}, "children": []}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-isLimitCtQualifications", "name": "DetailField", "props": {"name": "isLimitCtQualifications", "type": "BOOL", "label": "合同是否限制资质", "hidden": false, "editable": false, "componentProps": {"fieldAlias": "isLimitCtQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}}, "children": []}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-path", "name": "DetailField", "props": {"name": "path", "type": "TEXT", "label": "类目路径", "hidden": false, "editable": false, "componentProps": {"fieldAlias": "path", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}}, "children": []}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-wqMatType", "name": "DetailField", "props": {"name": "wqMatType", "type": "SELECT", "label": "物料类型", "hidden": false, "editable": false, "componentProps": {"fieldAlias": "wqMatType", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}}, "children": []}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-matCateParent", "name": "DetailField", "props": {"name": "mat<PERSON>ate<PERSON><PERSON>nt", "type": "SELFRELATION", "label": "父类目", "hidden": false, "editable": false, "componentProps": {"label": "选择父类目", "columns": ["matCateCode", "matCateName", "<PERSON><PERSON><PERSON><PERSON>", "status", "genCounClassMatTaxItemCfId", "qualificationsGroupId", "isLimitPoQualifications", "isLimitSoQualifications", "isLimitCtQualifications", "path", "wqMatType", "mat<PERSON>ate<PERSON><PERSON>nt"], "fieldAlias": "mat<PERSON>ate<PERSON><PERSON>nt", "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$gen_mat_cate_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$gen_mat_cate_md_PAGING_DATA_SERVICE"}, "parentModelAlias": "ERP_GEN$gen_mat_cate_md"}}, "children": []}]}]}, {"key": "ERP_SCM$test7051-detailView-system-detail-TabItem", "name": "TabItem", "type": "Layout", "props": {}, "children": [{"key": "ERP_SCM$test7051-detailView-system-detail-group", "name": "DetailGroupItem", "type": "Layout", "props": {"title": false}, "children": [{"key": "ERP_SCM$test7051-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"name": "created<PERSON>y", "type": "OBJECT", "label": "创建人", "editable": false, "componentProps": {"label": "选择创建人", "columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "labelField": "username", "modelAlias": "ERP_GEN$user", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$user_PAGING_DATA_SERVICE"}, "parentModelAlias": "ERP_GEN$gen_mat_cate_md"}}, "children": []}, {"key": "ERP_SCM$test7051-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"name": "updatedBy", "type": "OBJECT", "label": "更新人", "editable": false, "componentProps": {"label": "选择更新人", "columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "labelField": "username", "modelAlias": "ERP_GEN$user", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$user_PAGING_DATA_SERVICE"}, "parentModelAlias": "ERP_GEN$gen_mat_cate_md"}}, "children": []}, {"key": "ERP_SCM$test7051-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"name": "createdAt", "type": "DATE", "label": "创建时间", "editable": false, "componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}}, "children": []}, {"key": "ERP_SCM$test7051-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"name": "updatedAt", "type": "DATE", "label": "更新时间", "editable": false, "componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}}, "children": []}]}]}]}]}]}]}, {"key": "ERP_SCM$test7051-editView", "name": "Page", "type": "Container", "props": {"params": [], "showFooter": true, "showHeader": true}, "children": [{"key": "ERP_SCM$test7051-editView-page-title", "name": "Page<PERSON><PERSON>le", "type": "Meta", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" + ((data?.code || \"\") + (data?.name || \"类目配置表\")) : \"新建类目配置表\"}}", "useExpression": true}, "children": []}, {"key": "ERP_SCM$test7051-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "Layout", "props": {}, "children": []}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "name": "FormGroup", "type": "Container", "props": {"flow": {"type": "InvokeService", "test$": "route.action === 'edit'", "params$": "{ id: route.recordId }", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$gen_mat_cate_md_FIND_DATA_BY_ID_SERVICE_ykcOjc2", "containerKey": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form"}, "colon": false, "layout": "horizontal", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "children": [{"key": "ERP_SCM$test7051-editView-defaultTabs", "name": "Tabs", "type": "Layout", "props": {"items": [{"label": "主体信息"}], "underline": true}, "children": [{"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "name": "TabItem", "type": "Layout", "props": {"title": false}, "children": [{"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "name": "FormGroupItem", "type": "Layout", "props": {"title": false, "showSplit": true}, "children": [{"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-matCateCode", "name": "FormField", "type": "Widget", "props": {"name": "matCateCode", "type": "TEXT", "label": "类目编码", "rules": [], "hidden": false, "componentProps": {"fieldAlias": "matCateCode", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}}, "children": []}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-matCateName", "name": "FormField", "type": "Widget", "props": {"name": "matCateName", "type": "TEXT", "label": "类目名称", "rules": [], "hidden": false, "componentProps": {"fieldAlias": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}}, "children": []}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-isLeaf", "name": "FormField", "type": "Widget", "props": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "BOOL", "label": "是否叶子节点", "rules": [], "hidden": true, "componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}}, "children": []}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-genCounClassMatTaxItemCfId", "name": "FormField", "type": "Widget", "props": {"name": "genCounClassMatTaxItemCfId", "type": "OBJECT", "label": "物料税", "rules": [], "hidden": false, "componentProps": {"label": "选择物料税", "columns": ["counId", "taxType", "taxClassId", "taxClassDesc", "genVendFinMdId", "genCustFinMdId", "matSlsId"], "fieldAlias": "genCounClassMatTaxItemCfId", "labelField": "taxType", "modelAlias": "ERP_GEN$gen_coun_class_mat_tax_item_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$gen_coun_class_mat_tax_item_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$gen_coun_class_mat_tax_item_cf_PAGING_DATA_SERVICE"}, "parentModelAlias": "ERP_GEN$gen_mat_cate_md"}}, "children": []}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-qualificationsGroupId", "name": "FormField", "type": "Widget", "props": {"name": "qualificationsGroupId", "type": "OBJECT", "label": "关联资质组", "rules": [], "hidden": false, "componentProps": {"label": "选择关联资质组", "columns": ["qualificationsHeadCode", "qualificationsHeadName"], "fieldAlias": "qualificationsGroupId", "labelField": "qualificationsHeadCode", "modelAlias": "ERP_SCM$gen_qualifications_head_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$gen_qualifications_head_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$gen_qualifications_head_cf_PAGING_DATA_SERVICE"}, "parentModelAlias": "ERP_GEN$gen_mat_cate_md"}}, "children": []}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-isLimitPoQualifications", "name": "FormField", "type": "Widget", "props": {"name": "isLimitPoQualifications", "type": "BOOL", "label": "采购订单是否限制资质", "rules": [], "hidden": false, "componentProps": {"fieldAlias": "isLimitPoQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}}, "children": []}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-isLimitSoQualifications", "name": "FormField", "type": "Widget", "props": {"name": "isLimitSoQualifications", "type": "BOOL", "label": "销售订单是否限制资质", "rules": [], "hidden": false, "componentProps": {"fieldAlias": "isLimitSoQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}}, "children": []}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-isLimitCtQualifications", "name": "FormField", "type": "Widget", "props": {"name": "isLimitCtQualifications", "type": "BOOL", "label": "合同是否限制资质", "rules": [], "hidden": false, "componentProps": {"fieldAlias": "isLimitCtQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}}, "children": []}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-path", "name": "FormField", "type": "Widget", "props": {"name": "path", "type": "TEXT", "label": "类目路径", "rules": [], "hidden": false, "componentProps": {"fieldAlias": "path", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}}, "children": []}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-wqMatType", "name": "FormField", "type": "Widget", "props": {"name": "wqMatType", "type": "SELECT", "label": "物料类型", "rules": [], "hidden": false, "componentProps": {"fieldAlias": "wqMatType", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}}, "children": []}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-matCateParent", "name": "FormField", "type": "Widget", "props": {"name": "mat<PERSON>ate<PERSON><PERSON>nt", "type": "SELFRELATION", "label": "父类目", "rules": [], "hidden": false, "initialValue$": "route.query?.parentId ? { id: route.query?.parentId } : undefined", "componentProps": {"label": "选择父类目", "columns": ["matCateCode", "matCateName", "<PERSON><PERSON><PERSON><PERSON>", "status", "genCounClassMatTaxItemCfId", "qualificationsGroupId", "isLimitPoQualifications", "isLimitSoQualifications", "isLimitCtQualifications", "path", "wqMatType", "mat<PERSON>ate<PERSON><PERSON>nt"], "fieldAlias": "mat<PERSON>ate<PERSON><PERSON>nt", "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$gen_mat_cate_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$gen_mat_cate_md_PAGING_DATA_SERVICE"}, "parentModelAlias": "ERP_GEN$gen_mat_cate_md"}, "editComponentType": "SelfRelation", "editComponentProps": {"flow": {"type": "InvokeSystemService", "params": [{"fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "fieldAlias": "<PERSON><PERSON><PERSON>", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "user"}}, {"elements": [{"fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "fieldAlias": "<PERSON><PERSON><PERSON>", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}], "fieldName": "request", "fieldType": "Pageable", "fieldAlias": "request"}], "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindTreeChildrenDataService", "containerKey": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-matCateParent"}, "fields": [], "leafOnly": false, "labelField": ["matCateName"], "modelAlias": "ERP_GEN$gen_mat_cate_md", "cascaderField": "mat<PERSON>ate<PERSON><PERSON>nt", "tableCondition": null, "reverseConstructFlow": {"type": "InvokeService", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$gen_mat_cate_md_REVERSE_CONSTRUCT_TREE_SERVICE"}}}, "children": []}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-id", "name": "FormField", "type": "Widget", "props": {"name": "id", "type": "NUMBER", "label": "ID", "rules": [{"message": "请输入ID", "required": true}], "hidden": true, "componentProps": {"fieldAlias": "id", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}}, "children": []}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-createdBy", "name": "FormField", "type": "Widget", "props": {"name": "created<PERSON>y", "type": "OBJECT", "label": "创建人", "rules": [], "hidden": true, "componentProps": {"label": "选择创建人", "columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "labelField": "username", "modelAlias": "ERP_GEN$user", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$user_PAGING_DATA_SERVICE"}, "parentModelAlias": "ERP_GEN$gen_mat_cate_md"}}, "children": []}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-updatedBy", "name": "FormField", "type": "Widget", "props": {"name": "updatedBy", "type": "OBJECT", "label": "更新人", "rules": [], "hidden": true, "componentProps": {"label": "选择更新人", "columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "labelField": "username", "modelAlias": "ERP_GEN$user", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$user_PAGING_DATA_SERVICE"}, "parentModelAlias": "ERP_GEN$gen_mat_cate_md"}}, "children": []}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-createdAt", "name": "FormField", "type": "Widget", "props": {"name": "createdAt", "type": "DATE", "label": "创建时间", "rules": [{"message": "请输入创建时间", "required": true}], "hidden": true, "componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}}, "children": []}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-updatedAt", "name": "FormField", "type": "Widget", "props": {"name": "updatedAt", "type": "DATE", "label": "更新时间", "rules": [{"message": "请输入更新时间", "required": true}], "hidden": true, "componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}}, "children": []}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-version", "name": "FormField", "type": "Widget", "props": {"name": "version", "type": "NUMBER", "label": "版本号", "rules": [{"message": "请输入版本号", "required": true}], "hidden": true, "initialValue": 0, "componentProps": {"fieldAlias": "version", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入", "defaultValue": 0}}, "children": []}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-deleted", "name": "FormField", "type": "Widget", "props": {"name": "deleted", "type": "NUMBER", "label": "逻辑删除标识", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "hidden": true, "initialValue": 0, "componentProps": {"fieldAlias": "deleted", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入", "defaultValue": 0}}, "children": []}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-originOrgId", "name": "FormField", "type": "Widget", "props": {"name": "originOrgId", "type": "NUMBER", "label": "所属组织", "rules": [{"message": "请输入所属组织", "required": true}], "hidden": true, "initialValue": 0, "componentProps": {"fieldAlias": "originOrgId", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入", "defaultValue": 0}}, "children": []}]}]}]}]}, {"key": "ERP_SCM$test7051-editView-footer", "name": "<PERSON>Footer", "type": "Layout", "props": {}, "children": [{"key": "ERP_SCM$test7051-editView-actions", "name": "Space", "type": "Layout", "props": {}, "children": [{"key": "ERP_SCM$test7051-editView-action-cancel", "name": "<PERSON><PERSON>", "type": "Widget", "props": {"type": "default", "label": "取消", "confirmOn": "off", "buttonType": "default", "actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "ConditionFlowAction", "children": [{"action": "RefreshTab", "target": ["current"]}], "condition": {"id": "ERP_SCM$test7051-cancel-condition-flow-1", "type": "ConditionGroup", "conditions": [{"id": "ERP_SCM$test7051-cancel-condition-flow-1-1", "type": "ConditionGroup", "conditions": [{"id": "ERP_SCM$test7051-cancel-condition-flow-1-2", "type": "ConditionLeaf", "operator": "IS_NULL", "leftValue": {"val": "recordId", "scope": "route", "fieldType": "Text"}}], "logicOperator": "AND"}], "logicOperator": "OR"}}, {"action": "ConditionFlowAction", "children": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "type": "NewPage", "params": [{"name": "recordId", "type": "expression", "expression": "route.recordId"}], "refresh": true}}], "condition": {"id": "ERP_SCM$test7051-cancel-condition-flow-2", "type": "ConditionGroup", "conditions": [{"id": "ERP_SCM$test7051-cancel-condition-flow-2-1", "type": "ConditionGroup", "conditions": [{"id": "ERP_SCM$test7051-cancel-condition-flow-2-2", "type": "ConditionLeaf", "operator": "IS_NOT_NULL", "leftValue": {"val": "recordId", "scope": "route", "fieldType": "Text"}}], "logicOperator": "AND"}], "logicOperator": "OR"}}]}}, "children": []}, {"key": "ERP_SCM$test7051-editView-action-save", "name": "<PERSON><PERSON>", "type": "Widget", "props": {"type": "primary", "label": "保存", "confirmOn": "off", "buttonType": "default", "actionConfig": {"endLogic": "Other", "executeLogic": "BindService", "beforeLogicConfig": [{"action": "Validate", "validate": true}], "bindServiceConfig": {"params": [{"fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "fieldAlias": "<PERSON><PERSON><PERSON>", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}, {"fieldName": "request", "fieldType": "Object", "fieldAlias": "request", "valueConfig": {"type": "action", "action": {"target": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form"}}}], "service": "ERP_GEN$gen_mat_cate_md_MASTER_DATA_SAVE_DATA_SERVICE"}, "endLogicOtherConfig": [{"action": "ExecuteScript", "script": " getAction('ERP_SCM$test7051-tree', 'reload')(getAction('ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form', 'getData')()?.matCateParent?.id, false) "}, {"level": "success", "action": "Message", "message": "保存成功!"}]}}, "children": []}]}]}]}]}]}]}, "resources": [{"key": "ERP_SCM$test7051-tree", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-left-container", "type": "Box", "label": "区块"}], "type": "Container", "label": "树", "relations": [{"key": "ERP_GEN$SYS_FindTreeChildrenDataService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}}], "description": null}, {"key": "ERP_SCM$test7051-create-btn", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-left-container", "type": "Box", "label": "区块"}, {"key": "ERP_SCM$test7051-left-button-wrapper", "type": "Space", "label": "间距"}], "type": "<PERSON><PERSON>", "label": "新建 ", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-import-btn", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-left-container", "type": "Box", "label": "区块"}, {"key": "ERP_SCM$test7051-left-button-wrapper", "type": "Space", "label": "间距"}], "type": "Container", "label": "导入", "relations": [{"key": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": null}}, {"key": "/api/gei/task/import-sub-model", "name": null, "type": "Api", "props": {"httpMethod": "GET", "modelAlias": null}}, {"key": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": null}}, {"key": "/api/gei/task/import-direct-by-oss", "name": null, "type": "Api", "props": {"httpMethod": "GET", "modelAlias": null}}, {"key": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": null}}, {"key": "/api/gei/template/download", "name": null, "type": "Api", "props": {"httpMethod": "GET", "modelAlias": null}}, {"key": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": null}}], "description": null}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}], "type": "Container", "label": "表单组", "relations": [{"key": "ERP_GEN$gen_mat_cate_md_FIND_DATA_BY_ID_SERVICE_ykcOjc1", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}}], "description": null}, {"key": "ERP_SCM$test7051-tree-addChildBtn", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-left-container", "type": "Box", "label": "区块"}, {"key": "ERP_SCM$test7051-tree", "type": "Tree", "label": "树"}, {"key": "ERP_SCM$test7051-tree-recordActions", "type": "RecordActions", "label": "按钮组"}], "type": "<PERSON><PERSON>", "label": "新建子类目", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-tree-editBtn", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-left-container", "type": "Box", "label": "区块"}, {"key": "ERP_SCM$test7051-tree", "type": "Tree", "label": "树"}, {"key": "ERP_SCM$test7051-tree-recordActions", "type": "RecordActions", "label": "按钮组"}], "type": "<PERSON><PERSON>", "label": "编辑", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-tree-enableBtn", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-left-container", "type": "Box", "label": "区块"}, {"key": "ERP_SCM$test7051-tree", "type": "Tree", "label": "树"}, {"key": "ERP_SCM$test7051-tree-recordActions", "type": "RecordActions", "label": "按钮组"}], "type": "<PERSON><PERSON>", "label": "启用", "relations": [{"key": "ERP_GEN$gen_mat_cate_md_MASTER_DATA_ENABLE_DATA_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}}], "description": null}, {"key": "ERP_SCM$test7051-tree-disableBtn", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-left-container", "type": "Box", "label": "区块"}, {"key": "ERP_SCM$test7051-tree", "type": "Tree", "label": "树"}, {"key": "ERP_SCM$test7051-tree-recordActions", "type": "RecordActions", "label": "按钮组"}], "type": "<PERSON><PERSON>", "label": "停用", "relations": [{"key": "ERP_GEN$gen_mat_cate_md_MASTER_DATA_DISABLE_DATA_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}}], "description": null}, {"key": "ERP_SCM$test7051-tree-deleteBtn", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-left-container", "type": "Box", "label": "区块"}, {"key": "ERP_SCM$test7051-tree", "type": "Tree", "label": "树"}, {"key": "ERP_SCM$test7051-tree-recordActions", "type": "RecordActions", "label": "按钮组"}], "type": "<PERSON><PERSON>", "label": "删除", "relations": [{"key": "ERP_GEN$gen_mat_cate_md_DELETE_DATA_BY_ID_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}}], "description": null}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}], "type": "Container", "label": "详情", "relations": [{"key": "ERP_GEN$gen_mat_cate_md_FIND_DATA_BY_ID_SERVICE_ykcOjc1", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}}], "description": null}, {"key": "ERP_SCM$test7051-editView-action-cancel", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-footer", "type": "<PERSON>Footer", "label": "页面底部"}, {"key": "ERP_SCM$test7051-editView-actions", "type": "Space", "label": "间距"}], "type": "<PERSON><PERSON>", "label": "取消", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-editView-action-save", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-footer", "type": "<PERSON>Footer", "label": "页面底部"}, {"key": "ERP_SCM$test7051-editView-actions", "type": "Space", "label": "间距"}], "type": "<PERSON><PERSON>", "label": "保存", "relations": [{"key": "ERP_GEN$gen_mat_cate_md_MASTER_DATA_SAVE_DATA_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}}], "description": null}, {"key": "ERP_SCM$test7051-detailView-actions-delete", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-page-header", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "页头操作栏"}, {"key": "ERP_SCM$test7051-detailView-actions", "type": "Space", "label": "间距"}], "type": "<PERSON><PERSON>", "label": "删除", "relations": [{"key": "ERP_GEN$gen_mat_cate_md_DELETE_DATA_BY_ID_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}}], "description": null}, {"key": "ERP_SCM$test7051-detailView-actions-disable", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-page-header", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "页头操作栏"}, {"key": "ERP_SCM$test7051-detailView-actions", "type": "Space", "label": "间距"}], "type": "<PERSON><PERSON>", "label": "停用", "relations": [{"key": "ERP_GEN$gen_mat_cate_md_MASTER_DATA_DISABLE_DATA_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}}], "description": null}, {"key": "ERP_SCM$test7051-detailView-actions-enable", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-page-header", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "页头操作栏"}, {"key": "ERP_SCM$test7051-detailView-actions", "type": "Space", "label": "间距"}], "type": "<PERSON><PERSON>", "label": "启用", "relations": [{"key": "ERP_GEN$gen_mat_cate_md_MASTER_DATA_ENABLE_DATA_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}}], "description": null}, {"key": "ERP_SCM$test7051-detailView-actions-edit", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-page-header", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "页头操作栏"}, {"key": "ERP_SCM$test7051-detailView-actions", "type": "Space", "label": "间距"}], "type": "<PERSON><PERSON>", "label": "编辑", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-detail-ERP_GEN$gen_mat_cate_md-logs", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-page-header", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "页头操作栏"}, {"key": "ERP_SCM$test7051-detailView-actions", "type": "Space", "label": "间距"}], "type": "Container", "label": "日志", "relations": [{"key": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": null}}], "description": null}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-matCateCode", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "type": "FormGroup", "label": "表单组"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "类目编码", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-matCateName", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "type": "FormGroup", "label": "表单组"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "类目名称", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-isLeaf", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "type": "FormGroup", "label": "表单组"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "是否叶子节点", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-genCounClassMatTaxItemCfId", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "type": "FormGroup", "label": "表单组"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "物料税", "relations": [{"key": "ERP_GEN$gen_coun_class_mat_tax_item_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_coun_class_mat_tax_item_cf"}}, {"key": "ERP_GEN$gen_coun_class_mat_tax_item_cf_PAGING_DATA_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_coun_class_mat_tax_item_cf"}}], "description": null}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-qualificationsGroupId", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "type": "FormGroup", "label": "表单组"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "关联资质组", "relations": [{"key": "ERP_SCM$gen_qualifications_head_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_SCM$gen_qualifications_head_cf"}}, {"key": "ERP_SCM$gen_qualifications_head_cf_PAGING_DATA_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_SCM$gen_qualifications_head_cf"}}], "description": null}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-isLimitPoQualifications", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "type": "FormGroup", "label": "表单组"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "采购订单是否限制资质", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-isLimitSoQualifications", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "type": "FormGroup", "label": "表单组"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "销售订单是否限制资质", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-isLimitCtQualifications", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "type": "FormGroup", "label": "表单组"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "合同是否限制资质", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-path", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "type": "FormGroup", "label": "表单组"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "类目路径", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-wqMatType", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "type": "FormGroup", "label": "表单组"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "物料类型", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-matCateParent", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "type": "FormGroup", "label": "表单组"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "父类目", "relations": [{"key": "ERP_GEN$gen_mat_cate_md_FIND_DATA_BY_ID_SERVICE_ykcOjc1", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}}, {"key": "ERP_GEN$gen_mat_cate_md_PAGING_DATA_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}}, {"key": "ERP_GEN$SYS_FindTreeChildrenDataService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}}, {"key": "ERP_GEN$gen_mat_cate_md_REVERSE_CONSTRUCT_TREE_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}}], "description": null}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-id", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "type": "FormGroup", "label": "表单组"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "ID", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-createdBy", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "type": "FormGroup", "label": "表单组"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "创建人", "relations": [{"key": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}}, {"key": "ERP_GEN$user_PAGING_DATA_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}}], "description": null}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-updatedBy", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "type": "FormGroup", "label": "表单组"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "更新人", "relations": [{"key": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}}, {"key": "ERP_GEN$user_PAGING_DATA_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}}], "description": null}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-createdAt", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "type": "FormGroup", "label": "表单组"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "创建时间", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-updatedAt", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "type": "FormGroup", "label": "表单组"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "更新时间", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-version", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "type": "FormGroup", "label": "表单组"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "版本号", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-deleted", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "type": "FormGroup", "label": "表单组"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "逻辑删除标识", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-originOrgId", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-editView", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "type": "FormGroup", "label": "表单组"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "所属组织", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-matCateCode", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "type": "Detail", "label": "详情"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "type": "DetailGroupItem", "label": false}], "type": "Container", "label": "类目编码", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-matCateName", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "type": "Detail", "label": "详情"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "type": "DetailGroupItem", "label": false}], "type": "Container", "label": "类目名称", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-isLeaf", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "type": "Detail", "label": "详情"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "type": "DetailGroupItem", "label": false}], "type": "Container", "label": "是否叶子节点", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-status", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "type": "Detail", "label": "详情"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "type": "DetailGroupItem", "label": false}], "type": "Container", "label": "状态", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-genCounClassMatTaxItemCfId", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "type": "Detail", "label": "详情"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "type": "DetailGroupItem", "label": false}], "type": "Container", "label": "物料税", "relations": [{"key": "ERP_GEN$gen_coun_class_mat_tax_item_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_coun_class_mat_tax_item_cf"}}, {"key": "ERP_GEN$gen_coun_class_mat_tax_item_cf_PAGING_DATA_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_coun_class_mat_tax_item_cf"}}], "description": null}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-qualificationsGroupId", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "type": "Detail", "label": "详情"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "type": "DetailGroupItem", "label": false}], "type": "Container", "label": "关联资质组", "relations": [{"key": "ERP_SCM$gen_qualifications_head_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_SCM$gen_qualifications_head_cf"}}, {"key": "ERP_SCM$gen_qualifications_head_cf_PAGING_DATA_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_SCM$gen_qualifications_head_cf"}}], "description": null}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-isLimitPoQualifications", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "type": "Detail", "label": "详情"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "type": "DetailGroupItem", "label": false}], "type": "Container", "label": "采购订单是否限制资质", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-isLimitSoQualifications", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "type": "Detail", "label": "详情"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "type": "DetailGroupItem", "label": false}], "type": "Container", "label": "销售订单是否限制资质", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-isLimitCtQualifications", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "type": "Detail", "label": "详情"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "type": "DetailGroupItem", "label": false}], "type": "Container", "label": "合同是否限制资质", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-path", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "type": "Detail", "label": "详情"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "type": "DetailGroupItem", "label": false}], "type": "Container", "label": "类目路径", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-wqMatType", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "type": "Detail", "label": "详情"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "type": "DetailGroupItem", "label": false}], "type": "Container", "label": "物料类型", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-matCateParent", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "type": "Detail", "label": "详情"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "type": "DetailGroupItem", "label": false}], "type": "Container", "label": "父类目", "relations": [{"key": "ERP_GEN$gen_mat_cate_md_FIND_DATA_BY_ID_SERVICE_ykcOjc1", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}}, {"key": "ERP_GEN$gen_mat_cate_md_PAGING_DATA_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}}], "description": null}, {"key": "ERP_SCM$test7051-detailView-system-detail-field-createdBy", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "type": "Detail", "label": "详情"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-detailView-system-detail-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-detailView-system-detail-group", "type": "DetailGroupItem", "label": false}], "type": "Container", "label": "创建人", "relations": [{"key": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}}, {"key": "ERP_GEN$user_PAGING_DATA_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}}], "description": null}, {"key": "ERP_SCM$test7051-detailView-system-detail-field-updatedBy", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "type": "Detail", "label": "详情"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-detailView-system-detail-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-detailView-system-detail-group", "type": "DetailGroupItem", "label": false}], "type": "Container", "label": "更新人", "relations": [{"key": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}}, {"key": "ERP_GEN$user_PAGING_DATA_SERVICE", "name": null, "type": "Service", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}}], "description": null}, {"key": "ERP_SCM$test7051-detailView-system-detail-field-createdAt", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "type": "Detail", "label": "详情"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-detailView-system-detail-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-detailView-system-detail-group", "type": "DetailGroupItem", "label": false}], "type": "Container", "label": "创建时间", "relations": [], "description": null}, {"key": "ERP_SCM$test7051-detailView-system-detail-field-updatedAt", "path": [{"key": "ERP_SCM$test7051-page", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "ERP_SCM$test7051-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "ERP_SCM$test7051-detailView-container", "type": "View", "label": "视图"}, {"key": "ERP_SCM$test7051-detailView-page", "type": "Page", "label": "页面"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "type": "Detail", "label": "详情"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "ERP_SCM$test7051-detailView-system-detail-TabItem", "type": "TabItem", "label": "页签项"}, {"key": "ERP_SCM$test7051-detailView-system-detail-group", "type": "DetailGroupItem", "label": false}], "type": "Container", "label": "更新时间", "relations": [], "description": null}], "frontendConfig": {"modules": ["base", "service"]}, "containerSelect": {"ERP_SCM$test7051-tree": [], "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form": [{"field": "matCateCode", "selectFields": null}, {"field": "matCateName", "selectFields": null}, {"field": "genCounClassMatTaxItemCfId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "taxType", "selectFields": null}]}, {"field": "qualificationsGroupId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "qualificationsHeadCode", "selectFields": null}]}, {"field": "isLimitPoQualifications", "selectFields": null}, {"field": "isLimitSoQualifications", "selectFields": null}, {"field": "isLimitCtQualifications", "selectFields": null}, {"field": "path", "selectFields": null}, {"field": "wqMatType", "selectFields": null}, {"field": "mat<PERSON>ate<PERSON><PERSON>nt", "selectFields": [{"field": "id", "selectFields": null}, {"field": "matCateName", "selectFields": null}]}, {"field": "version", "selectFields": null}], "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail": [{"field": "matCateCode", "selectFields": null}, {"field": "matCateName", "selectFields": null}, {"field": "status", "selectFields": null}, {"field": "genCounClassMatTaxItemCfId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "taxType", "selectFields": null}]}, {"field": "qualificationsGroupId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "qualificationsHeadCode", "selectFields": null}]}, {"field": "isLimitPoQualifications", "selectFields": null}, {"field": "isLimitSoQualifications", "selectFields": null}, {"field": "isLimitCtQualifications", "selectFields": null}, {"field": "path", "selectFields": null}, {"field": "wqMatType", "selectFields": null}, {"field": "mat<PERSON>ate<PERSON><PERSON>nt", "selectFields": [{"field": "id", "selectFields": null}, {"field": "matCateName", "selectFields": null}]}, {"field": "created<PERSON>y", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "updatedBy", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "createdAt", "selectFields": null}, {"field": "updatedAt", "selectFields": null}]}}}