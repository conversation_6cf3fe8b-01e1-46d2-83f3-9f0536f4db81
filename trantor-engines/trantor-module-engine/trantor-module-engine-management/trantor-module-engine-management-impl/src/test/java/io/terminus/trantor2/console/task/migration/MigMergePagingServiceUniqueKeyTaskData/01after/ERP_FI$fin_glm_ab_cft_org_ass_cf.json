{"type": "Model", "name": "现金流使用组织关联表", "parentKey": "ERP_FI", "children": [], "props": {"desc": null, "alias": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "props": {"type": "PERSIST", "config": {"self": false, "system": false, "persist": false, "selfRelationFieldAlias": null}, "mainField": "est_gen_com_id", "tableName": "fin_glm_ab_cft_org_ass_cf", "searchModel": false, "mainFieldAlias": "estGenComId", "physicalDelete": false, "originOrgIdEnabled": true}, "children": [{"ext": false, "key": "est_gen_com_id", "name": "使用组织", "type": "DataStructField", "alias": "estGenComId", "appId": 43132, "props": {"unique": false, "comment": "使用组织", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "est_gen_com_id", "compositeKey": true, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "sys_common$org_struct_md", "currentModelAlias": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "relationModelAlias": "sys_common$org_struct_md", "linkModelFieldAlias": null, "currentModelFieldAlias": "estGenComId"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "fin_glm_ab_item_cf_id", "name": "现金流量项目", "type": "DataStructField", "alias": "finGlmAbItemCfId", "appId": 43132, "props": {"unique": false, "comment": "现金流量项目", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "fin_glm_ab_item_cf_id", "compositeKey": true, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_FI$fin_glm_ab_item_cf", "currentModelAlias": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "relationModelAlias": "ERP_FI$fin_glm_ab_item_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "finGlmAbItemCfId"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "id", "name": "ID", "type": "DataStructField", "alias": "id", "appId": 43132, "props": {"length": 20, "unique": true, "comment": "ID", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "id", "compositeKey": false, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}, {"ext": false, "key": "created_by", "name": "创建人", "type": "DataStructField", "alias": "created<PERSON>y", "appId": 43132, "props": {"length": 20, "unique": true, "comment": "创建人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "created_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_FI$user", "currentModelAlias": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "relationModelAlias": "ERP_FI$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "created<PERSON>y"}, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "updated_by", "name": "更新人", "type": "DataStructField", "alias": "updatedBy", "appId": 43132, "props": {"length": 20, "unique": true, "comment": "更新人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "updated_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_FI$user", "currentModelAlias": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "relationModelAlias": "ERP_FI$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "updatedBy"}, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "created_at", "name": "创建时间", "type": "DataStructField", "alias": "createdAt", "appId": 43132, "props": {"unique": true, "comment": "创建时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "created_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "updated_at", "name": "更新时间", "type": "DataStructField", "alias": "updatedAt", "appId": 43132, "props": {"unique": true, "comment": "更新时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "updated_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "version", "name": "版本号", "type": "DataStructField", "alias": "version", "appId": 43132, "props": {"length": 20, "unique": true, "comment": "版本号", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "version", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}, {"ext": false, "key": "deleted", "name": "逻辑删除标识", "type": "DataStructField", "alias": "deleted", "appId": 43132, "props": {"length": 20, "unique": true, "comment": "逻辑删除标识", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "deleted", "compositeKey": true, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}, {"ext": false, "key": "origin_org_id", "name": "所属组织", "type": "DataStructField", "alias": "originOrgId", "appId": 43132, "props": {"unique": false, "comment": "所属组织", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "origin_org_id", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}]}}