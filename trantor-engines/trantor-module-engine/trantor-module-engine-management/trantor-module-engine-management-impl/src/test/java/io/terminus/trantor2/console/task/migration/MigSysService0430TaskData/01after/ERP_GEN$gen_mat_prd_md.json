{"type": "Model", "name": "物料生产视图表", "parentKey": "ERP_GEN", "children": [], "props": {"desc": null, "alias": "ERP_GEN$gen_mat_prd_md", "props": {"type": "PERSIST", "config": {"self": false, "system": false, "persist": false, "selfRelationFieldAlias": null}, "mainField": "gen_mat_md_id", "tableName": "gen_mat_prd_md", "searchModel": false, "mainFieldAlias": "genMatMdId", "physicalDelete": false, "shardingConfig": {"enabled": false, "shardingFields": null, "shardingSuffixLength": 3}, "originOrgIdEnabled": true}, "children": [{"ext": false, "key": "inv_org_id", "name": "工厂（库存组织）", "type": "DataStructField", "alias": "invOrgId", "props": {"unique": false, "comment": "工厂（库存组织）", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "inv_org_id", "compositeKey": true, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "sys_common$org_struct_md", "currentModelAlias": "ERP_GEN$gen_mat_prd_md", "relationModelAlias": "sys_common$org_struct_md", "linkModelFieldAlias": null, "currentModelFieldAlias": "invOrgId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "factory_status_id", "name": "工厂状态", "type": "DataStructField", "alias": "factoryStatusId", "props": {"unique": false, "comment": "工厂状态", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "factory_status_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_mat_status_type_cf", "currentModelAlias": "ERP_GEN$gen_mat_prd_md", "relationModelAlias": "ERP_GEN$gen_mat_status_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "factoryStatusId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "inv_loc_id", "name": "生产存储库存地点", "type": "DataStructField", "alias": "invLocId", "props": {"unique": false, "comment": "生产存储库存地点", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "inv_loc_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "sys_common$org_struct_md", "currentModelAlias": "ERP_GEN$gen_mat_prd_md", "relationModelAlias": "sys_common$org_struct_md", "linkModelFieldAlias": null, "currentModelFieldAlias": "invLocId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "production_cycle", "name": "生产周期（天）", "type": "DataStructField", "alias": "productionCycle", "props": {"unique": false, "comment": "生产周期（天）", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 10, "columnName": "production_cycle", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "inspection_cycle", "name": "质检周期（天）", "type": "DataStructField", "alias": "inspectionCycle", "props": {"unique": false, "comment": "质检周期（天）", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 10, "columnName": "inspection_cycle", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "planned_calendar_id", "name": "计划日历", "type": "DataStructField", "alias": "plannedCalendarId", "props": {"unique": false, "comment": "计划日历", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "planned_calendar_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_wc_head_cf", "currentModelAlias": "ERP_GEN$gen_mat_prd_md", "relationModelAlias": "ERP_GEN$gen_wc_head_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "plannedCalendarId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "component_scrap", "name": "变动损耗率", "type": "DataStructField", "alias": "componentScrap", "props": {"scale": 3, "unique": false, "comment": "变动损耗率", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 5, "columnName": "component_scrap", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "gen_mat_md_id", "name": "genMatMdId", "type": "DataStructField", "alias": "genMatMdId", "props": {"unique": false, "comment": "genMatMdId", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "gen_mat_md_id", "compositeKey": true, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_mat_md", "currentModelAlias": "ERP_GEN$gen_mat_prd_md", "relationModelAlias": "ERP_GEN$gen_mat_md", "linkModelFieldAlias": null, "currentModelFieldAlias": "genMatMdId"}, "autoGenerated": true, "isSystemField": false}}, {"ext": false, "key": "prd_uom_id", "name": "生产计量单位", "type": "DataStructField", "alias": "prdUomId", "props": {"unique": false, "comment": "生产计量单位", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "prd_uom_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_uom_type_cf", "currentModelAlias": "ERP_GEN$gen_mat_prd_md", "relationModelAlias": "ERP_GEN$gen_uom_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "prdUomId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "post_inspection", "name": "过账到检验库存", "type": "DataStructField", "alias": "postInspection", "props": {"length": 1, "unique": false, "comment": "过账到检验库存", "required": false, "encrypted": false, "fieldType": "BOOL", "columnName": "post_inspection", "compositeKey": false, "defaultValue": true, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "insufficient_delivery_tolerance", "name": "不足交货容差", "type": "DataStructField", "alias": "insufficientDeliveryTolerance", "props": {"scale": 4, "unique": false, "comment": "不足交货容差", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "insufficient_delivery_tolerance", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "excessive_delivery_tolerance", "name": "过度交货容差", "type": "DataStructField", "alias": "excessiveDeliveryTolerance", "props": {"scale": 4, "unique": false, "comment": "过度交货容差", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "excessive_delivery_tolerance", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "unlimited_over_delivery", "name": "未限制的过度交货", "type": "DataStructField", "alias": "unlimitedOverDelivery", "props": {"length": 1, "unique": false, "comment": "未限制的过度交货", "required": false, "encrypted": false, "fieldType": "BOOL", "columnName": "unlimited_over_delivery", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "procurement_type", "name": "采购类型", "type": "DataStructField", "alias": "procurementType", "props": {"length": 256, "unique": false, "comment": "采购类型", "dictPros": {"dictValues": [{"label": "外购", "value": "F"}, {"label": "自制", "value": "E"}, {"label": "自制/外购", "value": "X"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "procurement_type", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "back_flush", "name": "反冲标识", "type": "DataStructField", "alias": "backFlush", "props": {"length": 256, "unique": false, "comment": "反冲标识", "dictPros": {"dictValues": [{"label": "始终反冲", "value": "ALWAYS"}, {"label": "工作中心决定是否反冲", "value": "WORKCENTER"}, {"label": "不反冲", "value": "NEVER"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "back_flush", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "individual_coll", "name": "独立/集中", "type": "DataStructField", "alias": "individualColl", "props": {"length": 256, "unique": false, "comment": "独立/集中", "dictPros": {"dictValues": [{"label": "独立", "value": "IND"}, {"label": "集中", "value": "COLL"}, {"label": "需求类型确定", "value": "ALL"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "individual_coll", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "prd_manger_id", "name": "prd_manger_id", "type": "DataStructField", "alias": "prdMangerId", "props": {"unique": false, "comment": "prd_manger_id", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "prd_manger_id", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "batch_para_id", "name": "batch_para_id", "type": "DataStructField", "alias": "batchParaId", "props": {"unique": false, "comment": "batch_para_id", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "batch_para_id", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "serial_para_id", "name": "serial_para_id", "type": "DataStructField", "alias": "serialParaId", "props": {"unique": false, "comment": "serial_para_id", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "serial_para_id", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "scrap_type", "name": "损耗类型", "type": "DataStructField", "alias": "scrapType", "props": {"length": 256, "unique": false, "comment": "损耗类型", "dictPros": {"dictValues": [{"label": "单一损耗", "value": "SINGLE"}, {"label": "阶梯损耗", "value": "STEP"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "scrap_type", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "fixed_scrap", "name": "固定损耗", "type": "DataStructField", "alias": "fixedScrap", "props": {"unique": false, "comment": "固定损耗", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "fixed_scrap", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "gen_mat_prd_step_scrap_md", "name": "关联阶梯损耗", "type": "DataStructField", "alias": "genMatPrdStepScrapMd", "props": {"unique": false, "comment": "关联阶梯损耗", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "gen_mat_prd_step_scrap_md", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "ERP_GEN$gen_mat_prd_step_scrap_md", "relationModelKey": "ERP_GEN$gen_mat_prd_step_scrap_md", "currentModelAlias": "ERP_GEN$gen_mat_prd_md", "relationModelAlias": "ERP_GEN$gen_mat_prd_step_scrap_md", "linkModelFieldAlias": "genMatPrdMdId", "currentModelFieldAlias": "genMatPrdStepScrapMd"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "prdvrs_list", "name": "生产版本", "type": "DataStructField", "alias": "prdvrsList", "props": {"unique": false, "comment": "生产版本", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "prdvrs_list", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "ERP_GEN$gen_prdvrs_md", "relationModelKey": "ERP_GEN$gen_prdvrs_md", "currentModelAlias": "ERP_GEN$gen_mat_prd_md", "relationModelAlias": "ERP_GEN$gen_prdvrs_md", "linkModelFieldAlias": "genMatPrdMdId", "currentModelFieldAlias": "prdvrsList"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "id", "name": "ID", "type": "DataStructField", "alias": "id", "props": {"length": 20, "unique": true, "comment": "ID", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "id", "compositeKey": false, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "created_by", "name": "创建人", "type": "DataStructField", "alias": "created<PERSON>y", "props": {"length": 20, "unique": true, "comment": "创建人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "created_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_mat_prd_md", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "created<PERSON>y"}, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "updated_by", "name": "更新人", "type": "DataStructField", "alias": "updatedBy", "props": {"length": 20, "unique": true, "comment": "更新人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "updated_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_mat_prd_md", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "updatedBy"}, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "created_at", "name": "创建时间", "type": "DataStructField", "alias": "createdAt", "props": {"unique": true, "comment": "创建时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "created_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "updated_at", "name": "更新时间", "type": "DataStructField", "alias": "updatedAt", "props": {"unique": true, "comment": "更新时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "updated_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "version", "name": "版本号", "type": "DataStructField", "alias": "version", "props": {"length": 20, "unique": true, "comment": "版本号", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "version", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "deleted", "name": "逻辑删除标识", "type": "DataStructField", "alias": "deleted", "props": {"length": 20, "unique": true, "comment": "逻辑删除标识", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "deleted", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "origin_org_id", "name": "所属组织", "type": "DataStructField", "alias": "originOrgId", "props": {"unique": false, "comment": "所属组织", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "origin_org_id", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}]}}