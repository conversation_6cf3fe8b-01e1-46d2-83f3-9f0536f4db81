{"parentKey": "CON", "name": "阶梯价数量计算", "type": "ServiceDefinition", "key": "CON$price_tiered_quantity_calculation", "props": {"serviceDslJson": {"output": [{"fieldAlias": "params", "fieldName": "params", "fieldKey": "params", "fieldType": "Text"}], "input": [{"fieldAlias": "params", "fieldName": "params", "fieldKey": "params", "fieldType": "Text"}], "children": [{"nextNodeKey": "node_1hk85piur119", "name": "开始", "type": "StartNode", "key": "node_1hk85pavn117", "props": {"output": [{"fieldAlias": "params", "fieldName": "params", "fieldKey": "params", "fieldType": "Text"}], "input": [{"fieldAlias": "params", "fieldName": "params", "fieldKey": "params", "fieldType": "Text"}], "type": "StartProperties"}}, {"nextNodeKey": "node_1hk85pavn118", "name": "数据转化", "type": "ConvertDataNode", "key": "node_1hk85piur119", "props": {"targetVariable": {"varValue": [{"valueName": "服务出参", "valueKey": "OUTPUT"}], "valueType": "VAR", "type": "VarValue", "fieldType": "Object"}, "sourceConditionGroup": {"id": "E3-CwhyqXt_D97mXZ9Vft", "conditions": [], "type": "ConditionGroup", "logicOperator": "OR"}, "sourceValue": {"varValue": [{"valueName": "服务出参", "valueKey": "OUTPUT"}], "valueType": "VAR", "type": "VarValue", "fieldType": "Object"}, "type": "ConvertDataProperties", "existsConfig": {"otherFieldConfig": {"fieldMapping": [{"value": {"type": "FuncValue", "funcExpression": "NOW()"}, "key": "params"}]}, "arrayFieldConfigs": []}}}, {"name": "结束", "type": "EndNode", "key": "node_1hk85pavn118", "props": {"type": "EndProperties"}}], "aiService": false, "name": "阶梯价数量计算", "aiChatMode": false, "type": "ServiceDefinition", "headNodeKeys": ["node_1hk85pavn117"], "key": "TSRM$price_tiered_quantity_calculation", "props": {"transactionPropagation": "REQUIRED", "teamId": 35, "type": "ServiceProperties"}}, "serviceType": "PROGRAMMABLE", "isEnabled": true, "serviceDslMd5": "null", "modelKey": "null"}}