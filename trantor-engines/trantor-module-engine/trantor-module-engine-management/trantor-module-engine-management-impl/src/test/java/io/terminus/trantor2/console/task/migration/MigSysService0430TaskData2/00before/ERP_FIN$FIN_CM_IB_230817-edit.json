{"type": "View", "name": "ERP_FIN$FIN_CM_IB_230817-edit", "parentKey": "ERP_FIN", "children": [], "props": {"key": "ERP_FIN$FIN_CM_IB_230817-edit", "type": "FORM", "title": "edit", "content": {"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "name": "Page", "type": "Container", "props": {"showFooter": true, "showHeader": true}, "children": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView-title", "name": "Page<PERSON><PERSON>le", "type": "Meta", "props": {"title": "{{route?.action === \"edit\" ? \"编辑账户期初余额确认单头表\" : \"创建账户期初余额确认单头表\"}}", "useExpression": true}, "children": []}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "name": "FormGroup", "type": "Container", "props": {"flow": {"type": "InvokeSystemService", "test$": "route.action === \"edit\" || !!route.query?.copyId", "params$": "{ id: route.query?.copyId || route.recordId }", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "serviceKey$": "route.query?.copyId ? \"ERP_FIN$SYS_CopyDataConverterService\" : \"ERP_FIN$SYS_FindDataByIdService\"", "containerKey": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr"}, "colon": false, "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "serviceKey": "ERP_FIN$SYS_FindDataByIdService"}, "children": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "name": "FormGroupItem", "type": "Layout", "props": {"title": false, "defaultCollapsed": false}, "children": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-id", "name": "FormField", "type": "Widget", "props": {"name": "id", "type": "NUMBER", "label": "ID", "rules": [{"message": "请输入ID", "required": true}], "hidden": true, "initialValue": null, "componentProps": {"precision": null, "fieldAlias": "id", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请输入", "defaultValue": null}}, "children": []}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-createdBy", "name": "FormField", "type": "Widget", "props": {"name": "created<PERSON>y", "type": "OBJECT", "label": "创建人", "rules": [], "hidden": true, "initialValue": null, "componentProps": {"label": "选择创建人", "columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "labelField": "username", "modelAlias": "ERP_FIN$user", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}, "parentModelAlias": "ERP_FIN$fin_cm_ib_head_tr"}}, "children": []}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-updatedBy", "name": "FormField", "type": "Widget", "props": {"name": "updatedBy", "type": "OBJECT", "label": "更新人", "rules": [], "hidden": true, "initialValue": null, "componentProps": {"label": "选择更新人", "columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "labelField": "username", "modelAlias": "ERP_FIN$user", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}, "parentModelAlias": "ERP_FIN$fin_cm_ib_head_tr"}}, "children": []}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-createdAt", "name": "FormField", "type": "Widget", "props": {"name": "createdAt", "type": "DATE", "label": "创建时间", "rules": [{"message": "请输入创建时间", "required": true}], "hidden": true, "initialValue": null, "componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请选择", "defaultValue": null}}, "children": []}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-updatedAt", "name": "FormField", "type": "Widget", "props": {"name": "updatedAt", "type": "DATE", "label": "更新时间", "rules": [{"message": "请输入更新时间", "required": true}], "hidden": true, "initialValue": null, "componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请选择", "defaultValue": null}}, "children": []}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-version", "name": "FormField", "type": "Widget", "props": {"name": "version", "type": "NUMBER", "label": "版本号", "rules": [{"message": "请输入版本号", "required": true}], "hidden": true, "initialValue": 0, "componentProps": {"precision": null, "fieldAlias": "version", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请输入", "defaultValue": 0}}, "children": []}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-deleted", "name": "FormField", "type": "Widget", "props": {"name": "deleted", "type": "NUMBER", "label": "逻辑删除标识", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "hidden": true, "initialValue": 0, "componentProps": {"precision": null, "fieldAlias": "deleted", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请输入", "defaultValue": 0}}, "children": []}, {"key": "IDLKA9RnNKM9ZoMsk3cvK", "name": "FormGroupItem", "type": "Layout", "props": {"title": "基本信息", "defaultCollapsed": false}, "children": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-ibCode", "name": "FormField", "type": "Widget", "props": {"name": "ibCode", "type": "TEXT", "label": "账户期初单单号", "rules": [{"message": "请输入账户期初单单号", "required": true}], "hidden": false, "lookup": [{"key": "49AGNvjpAu7cxTR1ted6M", "action": "get", "operator": "SERVICE", "fieldRules": {"hidden": false, "disabled": false, "readOnly": true, "required": false}, "valueRules": {"val": "ERP_FIN$SYS_InvokeCodeRuleService", "type": "SERVICE", "scope": null, "title": "(系统)调用取号规则服务", "value": "ERP_FIN$SYS_InvokeCodeRuleService", "serviceParams": {"outParams": "data", "entryNewParams": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_FIN$fin_cm_ib_head_tr"}, {"name": "request.rule<PERSON>ey", "type": "const", "value": "ERP_FIN$fin_cm_ib_code"}]}}, "conditionGroup": null}], "initialValue": null, "componentProps": {"fieldAlias": "ibCode", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请输入", "defaultValue": null}, "editComponentType": "InputText", "displayComponentType": "Text"}, "children": []}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-ibDate", "name": "FormField", "type": "Widget", "props": {"name": "ibDate", "type": "DATE", "label": "业务日期", "rules": [{"message": "请输入业务日期", "required": true}], "hidden": false, "initialValue": null, "componentProps": {"fieldAlias": "ibDate", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请选择", "defaultValue": null}, "editComponentType": "DatePicker", "displayComponentType": "Date"}, "children": []}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-comOrg", "name": "FormField", "type": "Widget", "props": {"name": "comOrg", "type": "OBJECT", "label": "公司组织", "rules": [{"message": "请输入公司组织", "required": true}], "hidden": false, "componentProps": {"label": "选择公司组织", "columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status", "counId"], "fieldAlias": "comOrg", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}, "parentModelAlias": "ERP_FIN$fin_cm_ib_head_tr"}, "editComponentType": "RelationSelect", "editComponentProps": {"flow": {"type": "InvokeSystemService", "params": [{"fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "fieldAlias": "<PERSON><PERSON><PERSON>", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_struct_md"}}], "context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "ERP_FIN$SYS_PagingDataService", "containerKey": ""}, "fields": [{"name": "orgCode", "type": "TEXT", "label": "组织编码", "width": 120, "hidden": false, "required": false, "componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "isRelationColumn": true}, {"name": "orgName", "type": "TEXT", "label": "组织名称", "width": 120, "hidden": false, "required": false, "componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "isRelationColumn": true}, {"name": "orgStatus", "type": "SELECT", "label": "状态", "width": 120, "hidden": false, "required": false, "componentProps": {"fieldAlias": "orgStatus", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "isRelationColumn": true}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "BOOL", "label": "是否叶子节点", "width": 120, "hidden": false, "required": false, "initialValue": true, "componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "defaultValue": true}, "isRelationColumn": true}], "showScope": "filter", "labelField": ["orgName"], "modelAlias": "sys_common$org_struct_md", "filterFields": [{"name": "orgCode", "type": "TEXT", "label": "组织编码", "width": 120, "hidden": false, "required": false, "componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}}, {"name": "orgName", "type": "TEXT", "label": "组织名称", "width": 120, "hidden": false, "required": false, "componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}}, {"name": "orgStatus", "type": "SELECT", "label": "状态", "width": 120, "hidden": false, "required": false, "componentProps": {"fieldAlias": "orgStatus", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "BOOL", "label": "是否叶子节点", "width": 120, "hidden": false, "required": false, "initialValue": true, "componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "defaultValue": true}}], "tableCondition": {"id": "5IBkj8zpP3fI8Ap0o5MF-", "type": "ConditionGroup", "conditions": [{"id": "fHMnMDRwTNxRwBetk7Em3", "type": "ConditionGroup", "conditions": [{"id": "vVVKD9feLcLI0gy5j-zWP", "type": "ConditionLeaf", "operator": "EQ", "leftValue": {"val": "orgBusinessTypeCode", "type": "VarValue", "title": "业务类型编码", "value": "sys_common$org_struct_md.orgBusinessTypeCode", "varVal": "orgBusinessTypeCode", "varValue": [{"valueKey": "orgBusinessTypeCode", "valueName": "orgBusinessTypeCode"}], "fieldType": "Text", "valueType": "VAR"}, "rightValue": {"type": "ConstValue", "fieldType": "Text", "valueType": "CONST", "constValue": "COM_ORG"}}, {"id": "-tTzwQCnIU8sqYrC8z97C", "type": "ConditionLeaf", "operator": "EQ", "leftValue": {"val": "orgStatus", "type": "VarValue", "title": "状态", "value": "ERP_GEN$org_struct_md.orgStatus", "varVal": "orgStatus", "varValue": [{"valueKey": "orgStatus", "valueName": "orgStatus"}], "fieldType": "Enum", "valueType": "VAR"}, "rightValue": {"type": "ConstValue", "fieldType": "Enum", "valueType": "CONST", "constValue": "ENABLED"}}], "logicOperator": "AND"}], "logicOperator": "OR"}, "showFilterFields": true, "reverseConstructFlow": {"type": "InvokeSystemService", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "ERP_FIN$SYS_ReverseConstructTreeService"}, "tableConditionContext$": "$context"}, "displayComponentType": "RelationShow", "displayComponentProps": {"labelField": ["orgName"], "modelAlias": "sys_common$org_struct_md"}}, "children": []}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-baseCurr", "name": "FormField", "type": "Widget", "props": {"name": "baseCurr", "type": "OBJECT", "label": "本位币别", "rules": [], "hidden": false, "lookup": [{"key": "PIQsYs6hoBj_0Gd-_wcUI", "action": "get", "operator": "SERVICE", "fieldRules": {"hidden": false, "disabled": false, "readOnly": true, "required": false}, "valueRules": {"val": "ERP_GEN$GEN_CURR_FROM_COM_ORG_FIELD_SERVICE", "type": "SERVICE", "scope": null, "value": "ERP_GEN$GEN_CURR_FROM_COM_ORG_FIELD_SERVICE", "serviceParams": {"outputParams": {"type": "expression", "expression": "", "serviceKey": "ERP_GEN$GEN_CURR_FROM_COM_ORG_FIELD_SERVICE"}, "entryNewParams": [{"required": null, "fieldName": "公司组织", "fieldType": "Number", "fieldAlias": "id", "valueConfig": {"type": "action", "action": {"target": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "selector": "comOrg.id"}}}]}}, "conditionGroup": null}], "initialValue": null, "componentProps": {"label": "选择本位币别", "columns": ["currCode", "currName", "currIsoName", "decimalPlace", "symbol", "remark"], "fieldAlias": "baseCurr", "labelField": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}, "parentModelAlias": "ERP_FIN$fin_cm_ib_head_tr"}, "editComponentType": "RelationSelect", "editComponentProps": {"flow": {"type": "InvokeSystemService", "params": [{"fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "fieldAlias": "<PERSON><PERSON><PERSON>", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_curr_type_cf"}}], "context$": "$context", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_FIN$SYS_PagingDataService", "containerKey": ""}, "fields": [{"name": "currCode", "type": "TEXT", "label": "币别编码", "width": 120, "hidden": false, "required": true, "initialValue": null, "componentProps": {"fieldAlias": "currCode", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入", "defaultValue": null}, "isRelationColumn": true}, {"name": "currName", "type": "TEXT", "label": "名称", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"fieldAlias": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入", "defaultValue": null}, "isRelationColumn": true}, {"name": "currIsoName", "type": "TEXT", "label": "ISO名称", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"fieldAlias": "currIsoName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入", "defaultValue": null}, "isRelationColumn": true}, {"name": "decimalPlace", "type": "NUMBER", "label": "小数位", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"precision": null, "fieldAlias": "decimalPlace", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入", "defaultValue": null}, "isRelationColumn": true}, {"name": "symbol", "type": "TEXT", "label": "币种符号", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"fieldAlias": "symbol", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入", "defaultValue": null}, "isRelationColumn": true}, {"name": "remark", "type": "TEXT", "label": "备注", "width": 120, "hidden": false, "required": true, "initialValue": null, "componentProps": {"fieldAlias": "remark", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入", "defaultValue": null}, "isRelationColumn": true}], "mainField": "currName", "showScope": "all", "labelField": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "tableCondition": null, "tableConditionContext$": null}, "displayComponentType": "RelationShow", "displayComponentProps": {"labelField": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf"}}, "children": []}]}, {"key": "fVS_LtydQYhEwDL8KDKsx", "name": "FormGroupItem", "type": "Layout", "props": {"title": "账户信息", "defaultCollapsed": false}, "children": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-alCode", "name": "FormField", "type": "Widget", "props": {"name": "alCode", "type": "OBJECT", "label": "余额账户", "rules": [{"message": "请输入余额账户", "required": true}], "hidden": false, "initialValue": null, "componentProps": {"label": "选择余额账户", "columns": ["alCode", "alName", "accountCode", "accountType", "accountAttribute", "comOrgId", "currId", "ccsHeadCode"], "fieldAlias": "alCode", "labelField": "alCode", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}, "parentModelAlias": "ERP_FIN$fin_cm_ib_head_tr"}, "editComponentType": "RelationSelect", "editComponentProps": {"flow": {"type": "InvokeSystemService", "context$": "$context", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "serviceKey": "ERP_FIN$SYS_PagingDataService"}, "fields": [{"name": "alCode", "type": "TEXT", "label": "账户编码", "width": 120, "hidden": false, "required": true, "initialValue": null, "componentProps": {"fieldAlias": "alCode", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请输入", "defaultValue": null}}, {"name": "alName", "type": "TEXT", "label": "账户名称", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"fieldAlias": "alName", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请输入", "defaultValue": null}}, {"name": "accountCode", "type": "TEXT", "label": "账户号", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"fieldAlias": "accountCode", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请输入", "defaultValue": null}}, {"name": "accountType", "type": "SELECT", "label": "账户类型", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"fieldAlias": "accountType", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请选择", "defaultValue": null}}, {"name": "accountAttribute", "type": "SELECT", "label": "账户属性", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"fieldAlias": "accountAttribute", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请选择", "defaultValue": null}}, {"name": "comOrgId", "type": "OBJECT", "label": "公司组织", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"label": "选择公司组织", "columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status", "counId"], "fieldAlias": "comOrgId", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}, "parentModelAlias": "ERP_FIN$fin_cm_al_type_md"}}, {"name": "currId", "type": "OBJECT", "label": "币别", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"label": "选择币别", "columns": ["currCode", "currName", "currIsoName", "decimalPlace", "symbol", "remark"], "fieldAlias": "currId", "labelField": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}, "parentModelAlias": "ERP_FIN$fin_cm_al_type_md"}}, {"name": "ccsHeadCode", "type": "OBJECT", "label": "现金盘点策略编码", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"label": "选择现金盘点策略编码", "columns": ["ccsHeadCode", "ccsName", "currId"], "fieldAlias": "ccsHeadCode", "labelField": "ccsHeadCode", "modelAlias": "ERP_FIN$fin_cm_ccs_head_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}, "parentModelAlias": "ERP_FIN$fin_cm_al_type_md"}}], "mainField": "alCode", "showScope": "all", "labelField": "alName", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "filterFields": [{"name": "alCode", "type": "TEXT", "label": "账户编码", "width": 120, "hidden": false, "required": true, "initialValue": null, "componentProps": {"fieldAlias": "alCode", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请输入", "defaultValue": null}}, {"name": "alName", "type": "TEXT", "label": "账户名称", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"fieldAlias": "alName", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请输入", "defaultValue": null}}, {"name": "accountCode", "type": "TEXT", "label": "账户号", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"fieldAlias": "accountCode", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请输入", "defaultValue": null}}, {"name": "accountType", "type": "SELECT", "label": "账户类型", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"fieldAlias": "accountType", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请选择", "defaultValue": null}}, {"name": "accountAttribute", "type": "SELECT", "label": "账户属性", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"fieldAlias": "accountAttribute", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请选择", "defaultValue": null}}, {"name": "comOrgId", "type": "OBJECT", "label": "公司组织", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"label": "选择公司组织", "columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status", "counId"], "fieldAlias": "comOrgId", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}, "parentModelAlias": "ERP_FIN$fin_cm_al_type_md"}}, {"name": "currId", "type": "OBJECT", "label": "币别", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"label": "选择币别", "columns": ["currCode", "currName", "currIsoName", "decimalPlace", "symbol", "remark"], "fieldAlias": "currId", "labelField": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}, "parentModelAlias": "ERP_FIN$fin_cm_al_type_md"}}, {"name": "ccsHeadCode", "type": "OBJECT", "label": "现金盘点策略编码", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"label": "选择现金盘点策略编码", "columns": ["ccsHeadCode", "ccsName", "currId"], "fieldAlias": "ccsHeadCode", "labelField": "ccsHeadCode", "modelAlias": "ERP_FIN$fin_cm_ccs_head_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}, "parentModelAlias": "ERP_FIN$fin_cm_al_type_md"}}], "tableCondition": null}, "displayComponentType": "RelationShow", "displayComponentProps": {"labelField": "alName"}}, "children": []}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-accountType", "name": "FormField", "type": "Widget", "props": {"name": "accountType", "type": "SELECT", "label": "账户类型", "rules": [{"message": "请输入账户类型", "required": true}], "hidden": false, "lookup": [{"key": "7b7sCrzca3-n3BRto8iRx", "action": "get", "operator": "FIELD", "fieldRules": {"hidden": false, "disabled": false, "readOnly": true, "required": false}, "valueRules": {"val": "alCode.accountType", "type": "FIELD", "scope": "form", "value": "ERP_FIN$fin_cm_ib_head_tr.alCode.accountType", "valueType": "model"}, "conditionGroup": null}], "initialValue": null, "componentProps": {"fieldAlias": "accountType", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请选择", "defaultValue": null}, "editComponentType": "Select", "displayComponentType": "Enum"}, "children": []}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-docCurr", "name": "FormField", "type": "Widget", "props": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "OBJECT", "label": "单据币别", "rules": [], "hidden": false, "lookup": [{"key": "fpWhB1JOKklLqOuWYlJT3", "action": "get", "operator": "FIELD", "fieldRules": {"hidden": false, "disabled": false, "readOnly": true, "required": false}, "valueRules": {"val": "alCode.currId", "type": "FIELD", "scope": "form", "value": "ERP_FIN$fin_cm_ib_head_tr.alCode.currId", "valueType": "model"}, "conditionGroup": null}], "initialValue": null, "componentProps": {"label": "选择单据币别", "columns": ["currCode", "currName", "currIsoName", "decimalPlace", "symbol", "remark"], "fieldAlias": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "labelField": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}, "parentModelAlias": "ERP_FIN$fin_cm_ib_head_tr"}, "editComponentType": "RelationSelect", "editComponentProps": {"flow": {"type": "InvokeSystemService", "context$": "$context", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_FIN$SYS_PagingDataService"}, "fields": [{"name": "currCode", "type": "TEXT", "label": "币别编码", "width": 120, "hidden": false, "required": true, "initialValue": null, "componentProps": {"fieldAlias": "currCode", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入", "defaultValue": null}}, {"name": "currName", "type": "TEXT", "label": "名称", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"fieldAlias": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入", "defaultValue": null}}, {"name": "currIsoName", "type": "TEXT", "label": "ISO名称", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"fieldAlias": "currIsoName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入", "defaultValue": null}}, {"name": "decimalPlace", "type": "NUMBER", "label": "小数位", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"precision": null, "fieldAlias": "decimalPlace", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入", "defaultValue": null}}, {"name": "symbol", "type": "TEXT", "label": "币种符号", "width": 120, "hidden": false, "required": false, "initialValue": null, "componentProps": {"fieldAlias": "symbol", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入", "defaultValue": null}}, {"name": "remark", "type": "TEXT", "label": "备注", "width": 120, "hidden": false, "required": true, "initialValue": null, "componentProps": {"fieldAlias": "remark", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入", "defaultValue": null}}], "mainField": "currName", "showScope": "all", "labelField": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "tableCondition": null}, "displayComponentType": "RelationShow", "displayComponentProps": {"labelField": "currName"}}, "children": []}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-exchRate", "name": "FormField", "type": "Widget", "props": {"name": "exchRate", "type": "NUMBER", "label": "汇率", "rules": [], "hidden": false, "lookup": [{"key": "eTRBIHRpwMxgyBCqkzd7F", "action": "get", "operator": "SERVICE", "fieldRules": {"hidden": false, "disabled": false, "readOnly": true, "required": false}, "valueRules": {"val": "ERP_GEN$GET_RATE_FROM_CURR_SERVICE", "type": "SERVICE", "scope": null, "title": "根据基本币种与目标币种获取汇率", "value": "ERP_GEN$GET_RATE_FROM_CURR_SERVICE", "serviceParams": {"outType": "Number", "outParams": "data", "entryNewParams": [{"name": "base_curr", "type": "action", "action": {"target": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "selector": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fieldType": "Number"}, {"name": "target_curr", "type": "action", "action": {"target": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "selector": "baseCurr"}, "fieldType": "Number"}]}}, "conditionGroup": null}], "initialValue": null, "componentProps": {"precision": 6, "fieldAlias": "exchRate", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请输入", "defaultValue": null}, "editComponentType": "InputNumber", "editComponentProps": {"precision": 6}, "displayComponentType": "Number", "displayComponentProps": {"precision": 6}}, "children": []}]}, {"key": "NA0CTISOB5L_Z-iFikiof", "name": "FormGroupItem", "type": "Layout", "props": {"title": "期初余额", "defaultCollapsed": false}, "children": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-initialBalanceDocAmt", "name": "FormField", "type": "Widget", "props": {"name": "initialBalanceDocAmt", "type": "NUMBER", "label": "账户期初余额", "rules": [], "hidden": false, "initialValue": null, "componentProps": {"precision": 6, "fieldAlias": "initialBalanceDocAmt", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请输入", "defaultValue": null}, "editComponentType": "InputNumber", "editComponentProps": {"precision": 2}, "displayComponentType": "Number", "displayComponentProps": {"precision": 2}}, "children": []}, {"key": "4_lqLaijs1rqQAcy5bFc8", "name": "FormGroupItem", "type": "Layout", "props": {"title": "期初余额本位币", "defaultCollapsed": false}, "children": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-initialBalanceBaseAmt", "name": "FormField", "type": "Widget", "props": {"name": "initialBalanceBaseAmt", "type": "NUMBER", "label": "账户期初余额", "rules": [], "hidden": false, "lookup": [{"key": "EN4j8p1K7GAL-VtUfqkKX", "action": "get", "operator": "FIELD", "fieldRules": {"hidden": false, "disabled": false, "readOnly": true, "required": false}, "valueRules": {"val": "initialBalanceDocAmt", "type": "FIELD", "scope": "form", "value": "ERP_FIN$fin_cm_ib_head_tr.initialBalanceDocAmt", "valueType": "model"}, "conditionGroup": null}], "initialValue": null, "componentProps": {"precision": 6, "fieldAlias": "initialBalanceBaseAmt", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请输入", "defaultValue": null}, "editComponentType": "InputNumber", "editComponentProps": {"precision": 2}, "displayComponentType": "Number", "displayComponentProps": {"precision": 2}}, "children": []}]}]}]}]}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView-footer", "name": "<PERSON>Footer", "type": "Layout", "props": {}, "children": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView-footer-cancel", "name": "<PERSON><PERSON>", "type": "Widget", "props": {"label": "取消", "actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "previous"}]}}, "children": []}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView-footer-save", "name": "<PERSON><PERSON>", "type": "Widget", "props": {"type": "primary", "label": "保存", "actionConfig": {"endLogic": "Other", "executeLogic": "BindFlow", "bindFlowConfig": {"params": [{"name": "request", "type": "action", "action": {"target": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr"}}], "service": "ERP_FIN$IB_SUBMIT_EVENT_SERVICE"}, "endLogicOtherConfig": [{"action": "PageJump", "target": "list"}, {"action": "Refresh", "target": "TERP_MIGRATE$FIN_CM_IB_230817-editView"}]}}, "children": []}]}]}, "resources": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "type": "Page", "label": "页面"}], "type": "Container", "label": "表单组", "relations": [], "description": null}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView-footer-cancel", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "type": "Page", "label": "页面"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView-footer", "type": "<PERSON>Footer", "label": "页面底部"}], "type": "<PERSON><PERSON>", "label": "取消", "relations": [], "description": null}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView-footer-save", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "type": "Page", "label": "页面"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView-footer", "type": "<PERSON>Footer", "label": "页面底部"}], "type": "<PERSON><PERSON>", "label": "保存", "relations": [{"key": "ERP_FIN$IB_SUBMIT_EVENT_SERVICE", "name": null, "type": "Service", "props": null}], "description": null}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-id", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "type": "Page", "label": "页面"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroup", "label": "表单组"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "ID", "relations": [], "description": null}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-createdBy", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "type": "Page", "label": "页面"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroup", "label": "表单组"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "创建人", "relations": [{"key": "ERP_FIN$SYS_FindDataByIdService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "ERP_FIN$user"}}, {"key": "ERP_FIN$SYS_PagingDataService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "ERP_FIN$user"}}], "description": null}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-updatedBy", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "type": "Page", "label": "页面"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroup", "label": "表单组"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "更新人", "relations": [{"key": "ERP_FIN$SYS_FindDataByIdService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "ERP_FIN$user"}}, {"key": "ERP_FIN$SYS_PagingDataService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "ERP_FIN$user"}}], "description": null}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-createdAt", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "type": "Page", "label": "页面"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroup", "label": "表单组"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "创建时间", "relations": [], "description": null}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-updatedAt", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "type": "Page", "label": "页面"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroup", "label": "表单组"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "更新时间", "relations": [], "description": null}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-version", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "type": "Page", "label": "页面"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroup", "label": "表单组"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "版本号", "relations": [], "description": null}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-deleted", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "type": "Page", "label": "页面"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroup", "label": "表单组"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "逻辑删除标识", "relations": [], "description": null}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-ibCode", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "type": "Page", "label": "页面"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroup", "label": "表单组"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroupItem", "label": false}, {"key": "IDLKA9RnNKM9ZoMsk3cvK", "type": "FormGroupItem", "label": "基本信息"}], "type": "Container", "label": "账户期初单单号", "relations": [{"key": "ERP_FIN$SYS_InvokeCodeRuleService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "ERP_FIN$fin_cm_ib_head_tr"}}], "description": null}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-ibDate", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "type": "Page", "label": "页面"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroup", "label": "表单组"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroupItem", "label": false}, {"key": "IDLKA9RnNKM9ZoMsk3cvK", "type": "FormGroupItem", "label": "基本信息"}], "type": "Container", "label": "业务日期", "relations": [], "description": null}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-comOrg", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "type": "Page", "label": "页面"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroup", "label": "表单组"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroupItem", "label": false}, {"key": "IDLKA9RnNKM9ZoMsk3cvK", "type": "FormGroupItem", "label": "基本信息"}], "type": "Container", "label": "公司组织", "relations": [{"key": "ERP_FIN$SYS_FindDataByIdService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}}, {"key": "ERP_FIN$SYS_PagingDataService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}}, {"key": "ERP_FIN$SYS_ReverseConstructTreeService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}}], "description": null}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-baseCurr", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "type": "Page", "label": "页面"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroup", "label": "表单组"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroupItem", "label": false}, {"key": "IDLKA9RnNKM9ZoMsk3cvK", "type": "FormGroupItem", "label": "基本信息"}], "type": "Container", "label": "本位币别", "relations": [{"key": "ERP_FIN$SYS_FindDataByIdService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}}, {"key": "ERP_FIN$SYS_PagingDataService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}}, {"key": "ERP_GEN$GEN_CURR_FROM_COM_ORG_FIELD_SERVICE", "name": null, "type": "Service", "props": null}], "description": null}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-alCode", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "type": "Page", "label": "页面"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroup", "label": "表单组"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroupItem", "label": false}, {"key": "fVS_LtydQYhEwDL8KDKsx", "type": "FormGroupItem", "label": "账户信息"}], "type": "Container", "label": "余额账户", "relations": [{"key": "ERP_FIN$SYS_FindDataByIdService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "ERP_FIN$fin_cm_al_type_md"}}, {"key": "ERP_FIN$SYS_PagingDataService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "ERP_FIN$fin_cm_al_type_md"}}, {"key": "ERP_FIN$SYS_FindDataByIdService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}}, {"key": "ERP_FIN$SYS_PagingDataService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}}, {"key": "ERP_FIN$SYS_FindDataByIdService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}}, {"key": "ERP_FIN$SYS_PagingDataService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}}, {"key": "ERP_FIN$SYS_FindDataByIdService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "ERP_FIN$fin_cm_ccs_head_cf"}}, {"key": "ERP_FIN$SYS_PagingDataService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "ERP_FIN$fin_cm_ccs_head_cf"}}], "description": null}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-accountType", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "type": "Page", "label": "页面"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroup", "label": "表单组"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroupItem", "label": false}, {"key": "fVS_LtydQYhEwDL8KDKsx", "type": "FormGroupItem", "label": "账户信息"}], "type": "Container", "label": "账户类型", "relations": [], "description": null}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-docCurr", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "type": "Page", "label": "页面"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroup", "label": "表单组"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroupItem", "label": false}, {"key": "fVS_LtydQYhEwDL8KDKsx", "type": "FormGroupItem", "label": "账户信息"}], "type": "Container", "label": "单据币别", "relations": [{"key": "ERP_FIN$SYS_FindDataByIdService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}}, {"key": "ERP_FIN$SYS_PagingDataService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}}], "description": null}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-exchRate", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "type": "Page", "label": "页面"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroup", "label": "表单组"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroupItem", "label": false}, {"key": "fVS_LtydQYhEwDL8KDKsx", "type": "FormGroupItem", "label": "账户信息"}], "type": "Container", "label": "汇率", "relations": [{"key": "ERP_GEN$GET_RATE_FROM_CURR_SERVICE", "name": null, "type": "Service", "props": null}], "description": null}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-initialBalanceDocAmt", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "type": "Page", "label": "页面"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroup", "label": "表单组"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroupItem", "label": false}, {"key": "NA0CTISOB5L_Z-iFikiof", "type": "FormGroupItem", "label": "期初余额"}], "type": "Container", "label": "账户期初余额", "relations": [], "description": null}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-initialBalanceBaseAmt", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "type": "Page", "label": "页面"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroup", "label": "表单组"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "type": "FormGroupItem", "label": false}, {"key": "NA0CTISOB5L_Z-iFikiof", "type": "FormGroupItem", "label": "期初余额"}, {"key": "4_lqLaijs1rqQAcy5bFc8", "type": "FormGroupItem", "label": "期初余额本位币"}], "type": "Container", "label": "账户期初余额", "relations": [], "description": null}], "frontendConfig": {"modules": ["base"]}, "conditionGroups": {"5IBkj8zpP3fI8Ap0o5MF-": {"id": "5IBkj8zpP3fI8Ap0o5MF-", "type": "ConditionGroup", "conditions": [{"id": "fHMnMDRwTNxRwBetk7Em3", "type": "ConditionGroup", "conditions": [{"id": "vVVKD9feLcLI0gy5j-zWP", "key": null, "type": "ConditionLeaf", "operator": "EQ", "leftValue": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "orgBusinessTypeCode", "fieldType": null, "valueName": "orgBusinessTypeCode", "modelAlias": null, "relatedModel": null}], "fieldType": "Text", "valueType": "VAR", "constValue": null, "fieldPaths": [{"fieldKey": "orgBusinessTypeCode", "fieldName": "orgBusinessTypeCode", "fieldType": null, "modelAlias": null, "relatedModel": null}]}, "rightValue": {"id": null, "type": "ConstValue", "fieldType": "Text", "constValue": "COM_ORG"}, "rightValues": null}, {"id": "-tTzwQCnIU8sqYrC8z97C", "key": null, "type": "ConditionLeaf", "operator": "EQ", "leftValue": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "orgStatus", "fieldType": null, "valueName": "orgStatus", "modelAlias": null, "relatedModel": null}], "fieldType": "Enum", "valueType": "VAR", "constValue": null, "fieldPaths": [{"fieldKey": "orgStatus", "fieldName": "orgStatus", "fieldType": null, "modelAlias": null, "relatedModel": null}]}, "rightValue": {"id": null, "type": "ConstValue", "fieldType": "Enum", "constValue": "ENABLED"}, "rightValues": null}], "logicOperator": "AND"}], "logicOperator": "OR"}}, "containerSelect": {"TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr": [{"field": "version", "selectFields": null}, {"field": "ibCode", "selectFields": null}, {"field": "ibDate", "selectFields": null}, {"field": "comOrg", "selectFields": [{"field": "id", "selectFields": null}, {"field": "orgName", "selectFields": null}]}, {"field": "baseCurr", "selectFields": [{"field": "id", "selectFields": null}, {"field": "currName", "selectFields": null}]}, {"field": "alCode", "selectFields": [{"field": "id", "selectFields": null}, {"field": "alCode", "selectFields": null}]}, {"field": "accountType", "selectFields": null}, {"field": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectFields": [{"field": "id", "selectFields": null}, {"field": "currName", "selectFields": null}]}, {"field": "exchRate", "selectFields": null}, {"field": "initialBalanceDocAmt", "selectFields": null}, {"field": "initialBalanceBaseAmt", "selectFields": null}]}}}