{"access": "Private", "parentKey": "CON", "name": "批量停用价格协议行", "type": "Event", "key": "CON$TSRM_PRICE_AGT_BATCH_DISABLE_EVENT", "props": {"returnModel": {"key": "CON$con_pri_agreement_item_tr"}, "returnModelArrayWhether": true, "model": {"key": "CON$con_pri_agreement_item_tr"}, "enabledStatusVerify": false, "modelArrayWhether": true, "enabledTransaction": true, "relations": [{"actionType": "Action", "code": "CON$TSRM_PRICE_AGTITEM_BATCH_DISABLE_ACTION", "enabledParamCheck": false}], "states": []}}