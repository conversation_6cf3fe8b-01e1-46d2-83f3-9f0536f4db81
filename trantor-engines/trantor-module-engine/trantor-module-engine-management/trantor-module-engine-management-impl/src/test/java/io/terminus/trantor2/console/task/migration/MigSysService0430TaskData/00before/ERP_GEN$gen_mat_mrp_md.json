{"type": "Model", "name": "物料MRP视图", "parentKey": "ERP_GEN", "children": [], "props": {"desc": null, "alias": "ERP_GEN$gen_mat_mrp_md", "props": {"type": "PERSIST", "config": {"self": false, "system": false, "persist": false, "selfRelationFieldAlias": null}, "mainField": "mrp_area_id", "tableName": "gen_mat_mrp_md", "searchModel": false, "mainFieldAlias": "mrpAreaId", "physicalDelete": false, "shardingConfig": {"enabled": false, "shardingFields": null, "shardingSuffixLength": 3}, "originOrgIdEnabled": true}, "children": [{"ext": false, "key": "reorder_qty", "name": "再订货点", "type": "DataStructField", "alias": "reorderQty", "props": {"unique": false, "comment": "再订货点", "required": false, "encrypted": false, "fieldType": "NUMBER", "columnName": "reorder_qty", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "planning_cycle", "name": "计划周期", "type": "DataStructField", "alias": "planningCycle", "props": {"unique": false, "comment": "计划周期", "required": false, "encrypted": false, "fieldType": "NUMBER", "columnName": "planning_cycle", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "planned_deliv_time", "name": "计划交货天数", "type": "DataStructField", "alias": "plannedDelivTime", "props": {"unique": false, "comment": "计划交货天数", "required": false, "encrypted": false, "fieldType": "NUMBER", "columnName": "planned_deliv_time", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "safety_stk_qty", "name": "安全库存", "type": "DataStructField", "alias": "safetyStkQty", "props": {"scale": 6, "unique": false, "comment": "安全库存", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "safety_stk_qty", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "min_safety_stk_qty", "name": "最小安全库存", "type": "DataStructField", "alias": "minSafetyStkQty", "props": {"scale": 6, "unique": false, "comment": "最小安全库存", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "min_safety_stk_qty", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "serv_level_percent", "name": "服务水平", "type": "DataStructField", "alias": "servLevelPercent", "props": {"unique": false, "comment": "服务水平", "required": false, "encrypted": false, "fieldType": "NUMBER", "columnName": "serv_level_percent", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "gen_mat_md_id", "name": "gen_mat_md_id", "type": "DataStructField", "alias": "genMatMdId", "props": {"unique": false, "comment": "gen_mat_md_id", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "gen_mat_md_id", "compositeKey": true, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_mat_md", "currentModelAlias": "ERP_GEN$gen_mat_mrp_md", "relationModelAlias": "ERP_GEN$gen_mat_md", "linkModelFieldAlias": null, "currentModelFieldAlias": "genMatMdId"}, "autoGenerated": true, "isSystemField": false}}, {"ext": false, "key": "prediction_formula", "name": "预测公式", "type": "DataStructField", "alias": "predictionFormula", "props": {"length": 256, "unique": false, "comment": "预测公式", "dictPros": {"dictValues": [{"label": "简单平均", "value": "SA"}, {"label": "加权平均", "value": "WA"}, {"label": "一阶指数平滑", "value": "SES"}, {"label": "二阶指数平滑", "value": "DES"}, {"label": "线性回归", "value": "LR"}, {"label": "智能算法", "value": "AI"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "prediction_formula", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "prediction_interval_start_date", "name": "预测区间开始日期", "type": "DataStructField", "alias": "predictionIntervalStartDate", "props": {"unique": false, "comment": "预测区间开始日期", "required": false, "encrypted": false, "fieldType": "DATE", "columnName": "prediction_interval_start_date", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "prediction_interval_end_date", "name": "预测区间结束日期", "type": "DataStructField", "alias": "predictionIntervalEndDate", "props": {"unique": false, "comment": "预测区间结束日期", "required": false, "encrypted": false, "fieldType": "DATE", "columnName": "prediction_interval_end_date", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "inv_mvm_type_name", "name": "参与预测移动凭证类型", "type": "DataStructField", "alias": "invMvmTypeName", "props": {"unique": false, "comment": "参与预测移动凭证类型", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "inv_mvm_type_name", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_SCM$inv_mvm_type_cf", "currentModelAlias": "ERP_GEN$gen_mat_mrp_md", "relationModelAlias": "ERP_SCM$inv_mvm_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "invMvmTypeName"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "smoothing_index", "name": "平滑系数", "type": "DataStructField", "alias": "smoothingIndex", "props": {"unique": false, "comment": "平滑系数", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "smoothing_index", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "mrp_area_id", "name": "MRP区域", "type": "DataStructField", "alias": "mrpAreaId", "props": {"unique": false, "comment": "MRP区域", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "mrp_area_id", "compositeKey": true, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "planning_strategy_id", "name": "计划策略", "type": "DataStructField", "alias": "planningStrategyId", "props": {"unique": false, "comment": "计划策略", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "planning_strategy_id", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "supply_type", "name": "供应类型", "type": "DataStructField", "alias": "supplyType", "props": {"length": 256, "unique": false, "comment": "供应类型", "dictPros": {"dictValues": [{"label": "自制", "value": "E"}, {"label": "外购", "value": "F"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "supply_type", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "plan_wc_cf_id", "name": "计划日历", "type": "DataStructField", "alias": "planWcCfId", "props": {"unique": false, "comment": "计划日历", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "plan_wc_cf_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_wc_head_cf", "currentModelAlias": "ERP_GEN$gen_mat_mrp_md", "relationModelAlias": "ERP_GEN$gen_wc_head_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "planWcCfId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "lot_size_type_cf_id", "name": "批量类型", "type": "DataStructField", "alias": "lotSizeTypeCfId", "props": {"unique": false, "comment": "批量类型", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "lot_size_type_cf_id", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "planning_group_cf_id", "name": "MRP计划组", "type": "DataStructField", "alias": "planningGroupCfId", "props": {"unique": false, "comment": "MRP计划组", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "planning_group_cf_id", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "default_inv_org_id", "name": "默认库存组织", "type": "DataStructField", "alias": "defaultInvOrgId", "props": {"unique": false, "comment": "默认库存组织", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "default_inv_org_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "sys_common$org_struct_md", "currentModelAlias": "ERP_GEN$gen_mat_mrp_md", "relationModelAlias": "sys_common$org_struct_md", "linkModelFieldAlias": null, "currentModelFieldAlias": "defaultInvOrgId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "default_inv_loc_id", "name": "默认库存地点", "type": "DataStructField", "alias": "defaultInvLocId", "props": {"unique": false, "comment": "默认库存地点", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "default_inv_loc_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "sys_common$org_struct_md", "currentModelAlias": "ERP_GEN$gen_mat_mrp_md", "relationModelAlias": "sys_common$org_struct_md", "linkModelFieldAlias": null, "currentModelFieldAlias": "defaultInvLocId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "pur_process_days", "name": "采购处理天数", "type": "DataStructField", "alias": "purProcessDays", "props": {"unique": false, "comment": "采购处理天数", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 10, "columnName": "pur_process_days", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}}, {"ext": false, "key": "id", "name": "ID", "type": "DataStructField", "alias": "id", "props": {"length": 20, "unique": true, "comment": "ID", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "id", "compositeKey": false, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "created_by", "name": "创建人", "type": "DataStructField", "alias": "created<PERSON>y", "props": {"length": 20, "unique": true, "comment": "创建人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "created_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_mat_mrp_md", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "created<PERSON>y"}, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "updated_by", "name": "更新人", "type": "DataStructField", "alias": "updatedBy", "props": {"length": 20, "unique": true, "comment": "更新人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "updated_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_mat_mrp_md", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "updatedBy"}, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "created_at", "name": "创建时间", "type": "DataStructField", "alias": "createdAt", "props": {"unique": true, "comment": "创建时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "created_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "updated_at", "name": "更新时间", "type": "DataStructField", "alias": "updatedAt", "props": {"unique": true, "comment": "更新时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "updated_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "version", "name": "版本号", "type": "DataStructField", "alias": "version", "props": {"length": 20, "unique": true, "comment": "版本号", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "version", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "deleted", "name": "逻辑删除标识", "type": "DataStructField", "alias": "deleted", "props": {"length": 20, "unique": true, "comment": "逻辑删除标识", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "deleted", "compositeKey": true, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "origin_org_id", "name": "所属组织", "type": "DataStructField", "alias": "originOrgId", "props": {"unique": false, "comment": "所属组织", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "origin_org_id", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}]}}