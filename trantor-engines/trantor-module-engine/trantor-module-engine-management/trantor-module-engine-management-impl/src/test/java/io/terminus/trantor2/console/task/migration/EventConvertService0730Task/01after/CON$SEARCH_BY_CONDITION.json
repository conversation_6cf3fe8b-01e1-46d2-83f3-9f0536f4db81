{"parentKey": "CON", "name": "搜索价格协议", "type": "ServiceDefinition", "key": "CON$SEARCH_BY_CONDITION", "props": {"serviceDslJson": {"output": [{"fieldAlias": "data", "fieldName": "data", "fieldKey": "data", "elements": [{"fieldAlias": "total", "fieldName": "total", "fieldKey": "total", "fieldType": "Number"}, {"fieldAlias": "data", "fieldName": "data", "fieldKey": "data", "fieldType": "Array", "element": {"fieldAlias": "element", "fieldName": "element", "fieldKey": "element", "fieldType": "Model", "relatedModel": {"modelAlias": "CON$con_pri_agreement_item_tr", "modelKey": "CON$con_pri_agreement_item_tr"}}}], "fieldType": "Paging"}], "input": [{"fieldAlias": "purchaser", "fieldName": "采购应公司", "fieldKey": "purchaser", "fieldType": "Number"}, {"fieldAlias": "pagingAble", "fieldName": "分页条件", "fieldKey": "pagingAble", "elements": [{"fieldAlias": "conditionGroup", "fieldName": "条件组", "fieldKey": "conditionGroup", "fieldType": "Object"}, {"fieldAlias": "conditionItems", "fieldName": "简化版条件组", "fieldKey": "conditionItems", "elements": [{"fieldAlias": "conditions", "fieldName": "条件对象", "fieldKey": "conditions", "fieldType": "Model", "relatedModel": {"modelAlias": "CON$con_pri_agreement_item_tr", "modelKey": "CON$con_pri_agreement_item_tr"}}, {"fieldAlias": "logicOperator", "fieldName": "逻辑运算符", "fieldKey": "logicOperator", "fieldType": "Text"}], "fieldType": "Object"}, {"fieldAlias": "sortOrders", "fieldName": "字段排序", "fieldKey": "sortOrders", "fieldType": "Array"}, {"fieldAlias": "pageNo", "fieldName": "页码", "fieldKey": "pageNo", "fieldType": "Number"}, {"fieldAlias": "pageSize", "fieldName": "每页数量", "fieldKey": "pageSize", "fieldType": "Number"}], "fieldType": "Pageable"}], "children": [{"nextNodeKey": "node_1hl2502uq2", "name": "开始", "type": "StartNode", "key": "node_1hl2502uq1", "props": {"output": [{"fieldAlias": "data", "fieldName": "data", "fieldKey": "data", "elements": [{"fieldAlias": "total", "fieldName": "total", "fieldKey": "total", "fieldType": "Number"}, {"fieldAlias": "data", "fieldName": "data", "fieldKey": "data", "fieldType": "Array", "element": {"fieldAlias": "element", "fieldName": "element", "fieldKey": "element", "fieldType": "Model", "relatedModel": {"modelAlias": "CON$con_pri_agreement_item_tr", "modelKey": "CON$con_pri_agreement_item_tr"}}}], "fieldType": "Paging"}], "input": [{"fieldAlias": "purchaser", "fieldName": "采购应公司", "fieldKey": "purchaser", "fieldType": "Number"}, {"fieldAlias": "pagingAble", "fieldName": "分页条件", "fieldKey": "pagingAble", "elements": [{"fieldAlias": "conditionGroup", "fieldName": "条件组", "fieldKey": "conditionGroup", "fieldType": "Object"}, {"fieldAlias": "conditionItems", "fieldName": "简化版条件组", "fieldKey": "conditionItems", "elements": [{"fieldAlias": "conditions", "fieldName": "条件对象", "fieldKey": "conditions", "fieldType": "Model", "relatedModel": {"modelAlias": "CON$con_pri_agreement_item_tr", "modelKey": "CON$con_pri_agreement_item_tr"}}, {"fieldAlias": "logicOperator", "fieldName": "逻辑运算符", "fieldKey": "logicOperator", "fieldType": "Text"}], "fieldType": "Object"}, {"fieldAlias": "sortOrders", "fieldName": "字段排序", "fieldKey": "sortOrders", "fieldType": "Array"}, {"fieldAlias": "pageNo", "fieldName": "页码", "fieldKey": "pageNo", "fieldType": "Number"}, {"fieldAlias": "pageSize", "fieldName": "每页数量", "fieldKey": "pageSize", "fieldType": "Number"}], "fieldType": "Pageable"}], "type": "StartProperties"}}, {"name": "结束", "type": "EndNode", "key": "node_1hl2502uq2", "props": {"type": "EndProperties"}}], "name": "搜索价格协议", "type": "ServiceDefinition", "headNodeKeys": ["node_1hl2502uq1"], "key": "TSRM$SEARCH_BY_CONDITION", "props": {"transactionPropagation": "REQUIRED", "teamId": 35, "type": "ServiceProperties"}}, "serviceType": "PROGRAMMABLE", "isEnabled": false, "serviceDslMd5": "null", "modelKey": "null"}}