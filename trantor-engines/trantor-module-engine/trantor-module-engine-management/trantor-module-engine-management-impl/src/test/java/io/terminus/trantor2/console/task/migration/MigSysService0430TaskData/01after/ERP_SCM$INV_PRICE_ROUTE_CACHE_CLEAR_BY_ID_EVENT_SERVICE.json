{"access": "Private", "children": [], "key": "ERP_SCM$INV_PRICE_ROUTE_CACHE_CLEAR_BY_ID_EVENT_SERVICE", "name": "INV-库存计价路由-删除缓存服务服务", "parentKey": "ERP_SCM", "props": {"eventProps": {"desc": null, "enabledStatusVerify": false, "enabledTransaction": true, "model": {"children": null, "desc": null, "key": "ERP_SCM$inv_price_route_cf", "name": null}, "modelArrayWhether": false, "notice": null, "notices": null, "relations": [{"actionType": "Action", "code": "ERP_SCM$INV_PRICE_ROUTE_CACHE_CLEAR_ACTION", "conditionId": null, "conditions": null, "convert": null, "desc": null, "enabledParamCheck": false, "enabledTransaction": false, "express": null, "nextCode": "ERP_SCM$SYS_DeleteDataByIdService", "sourceCode": null}, {"actionType": "ServiceDefinition", "code": "ERP_SCM$SYS_DeleteDataByIdService", "conditionId": null, "conditions": null, "convert": null, "desc": null, "enabledParamCheck": false, "enabledTransaction": false, "express": null, "nextCode": null, "sourceCode": null}], "returnModel": null, "returnModelArrayWhether": null, "states": []}, "isDeleted": null, "isEnabled": true, "modelKey": "ERP_SCM$inv_price_route_cf", "serviceDslJson": {"children": [{"children": null, "headNodeKeys": null, "id": null, "key": "start0", "name": "开始节点", "nextNodeKey": "AssignNode0", "preNodeKey": null, "props": {"desc": "开始节点", "globalVariable": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "eventCode", "fieldKey": "eventCode", "fieldName": "eventCode", "fieldType": "Text", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "param", "fieldKey": "param", "fieldName": "param", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_price_route_cf", "modelKey": "ERP_SCM$inv_price_route_cf", "modelName": "ERP_SCM$inv_price_route_cf"}, "relation": null, "required": null}], "fieldAlias": "event", "fieldKey": "event", "fieldName": "event", "fieldType": "Object", "id": null, "required": null}], "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_price_route_cf", "modelKey": "ERP_SCM$inv_price_route_cf", "modelName": "ERP_SCM$inv_price_route_cf"}, "relation": null, "required": true}], "name": "开始节点", "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "type": "StartProperties"}, "renderType": null, "type": "StartNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "AssignNode0", "name": "赋值", "nextNodeKey": "SPINodeKey", "preNodeKey": null, "props": {"assignments": [{"field": {"constValue": null, "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "GLOBAL"}, {"modelAlias": null, "relatedModel": null, "valueKey": "event", "valueName": "event"}, {"modelAlias": null, "relatedModel": null, "valueKey": "eventCode", "valueName": "eventCode"}]}, "id": null, "operator": "EQ", "value": {"constValue": "ERP_SCM$INV_PRICE_ROUTE_CACHE_CLEAR_BY_ID_EVENT", "fieldType": null, "id": null, "type": "VarValue", "valueType": "CONST", "varValue": null}}, {"field": {"constValue": null, "fieldType": "Object", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "GLOBAL"}, {"modelAlias": null, "relatedModel": null, "valueKey": "event", "valueName": "event"}, {"modelAlias": null, "relatedModel": null, "valueKey": "param", "valueName": "param"}]}, "id": null, "operator": "EQ", "value": {"constValue": null, "fieldType": null, "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "REQUEST"}, {"modelAlias": null, "relatedModel": null, "valueKey": "request", "valueName": "request"}]}}], "desc": null, "name": "赋值", "type": "AssignProperties"}, "renderType": null, "type": "AssignNode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "SPINodeKey", "name": "调用线下代码", "nextNodeKey": "end0", "preNodeKey": null, "props": {"convertRelatedModelData": false, "desc": null, "implementation": "EventServiceExecute", "implementationName": null, "inputMapping": [{"field": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "event", "fieldKey": "event", "fieldName": "event", "fieldType": "Object", "id": null, "required": null}, "id": null, "value": {"constValue": null, "fieldType": null, "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"modelAlias": null, "relatedModel": null, "valueKey": "event", "valueName": "event"}]}}], "name": "调用线下代码", "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "ActionResponse", "fieldType": "Object", "id": null, "required": null}], "outputAssign": {"customAssignments": [{"field": {"constValue": null, "fieldType": "Object", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}]}, "id": null, "operator": "EQ", "value": {"constValue": null, "fieldType": "Object", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"modelAlias": null, "relatedModel": null, "valueKey": "NODE_OUTPUT_SPINodeKey", "valueName": "出参结构体"}, {"modelAlias": null, "relatedModel": null, "valueKey": "data", "valueName": "ActionResponse"}]}}], "outputAssignType": "CUSTOM"}, "type": "SPIProperties"}, "renderType": null, "type": "SPINode"}, {"children": null, "headNodeKeys": null, "id": null, "key": "end0", "name": "结束节点", "nextNodeKey": null, "preNodeKey": null, "props": {"desc": null, "name": null, "outputMapping": null, "type": "EndProperties"}, "renderType": null, "type": "EndNode"}], "headNodeKeys": ["start0"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_price_route_cf", "modelKey": "ERP_SCM$inv_price_route_cf", "modelName": "ERP_SCM$inv_price_route_cf"}, "relation": null, "required": true}], "key": "ERP_SCM$INV_PRICE_ROUTE_CACHE_CLEAR_BY_ID_EVENT_SERVICE", "name": "INV-库存计价路由-删除缓存服务服务", "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "desc": null, "name": null, "transactionPropagation": "REQUIRED", "type": "ServiceProperties"}, "type": "ServiceDefinition"}, "serviceDslMd5": null, "serviceName": null, "serviceType": "EVENT"}, "type": "ServiceDefinition"}