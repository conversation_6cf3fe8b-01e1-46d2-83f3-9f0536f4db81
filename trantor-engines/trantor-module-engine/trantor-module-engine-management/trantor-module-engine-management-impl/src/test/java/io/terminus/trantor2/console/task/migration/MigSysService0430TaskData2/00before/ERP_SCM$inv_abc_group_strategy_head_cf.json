{"type": "Model", "name": "ABC分组方案", "parentKey": "ERP_SCM", "children": [], "props": {"desc": "ABC分组方案", "alias": "ERP_SCM$inv_abc_group_strategy_head_cf", "props": {"type": "PERSIST", "config": {"self": false, "system": false, "persist": false, "selfRelationFieldAlias": null}, "mainField": "name", "tableName": "inv_abc_group_strategy_head_cf", "searchModel": false, "mainFieldAlias": "name", "physicalDelete": false, "originOrgIdEnabled": true}, "children": [{"ext": false, "key": "code", "name": "分组策略编码", "type": "DataStructField", "alias": "code", "appId": 47014, "props": {"length": 32, "unique": false, "comment": "分组策略编码", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "code", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "name", "name": "分组策略名称", "type": "DataStructField", "alias": "name", "appId": 47014, "props": {"length": 32, "unique": false, "comment": "分组策略名称", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "name", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "note", "name": "备注", "type": "DataStructField", "alias": "note", "appId": 47014, "props": {"length": 256, "unique": false, "comment": "备注", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "note", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "status", "name": "状态", "type": "DataStructField", "alias": "status", "appId": 47014, "props": {"length": 256, "unique": false, "comment": "状态", "dictPros": {"dictValues": [{"label": "未启用", "value": "INACTIVE"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已停用", "value": "DISABLED"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "status", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "com_org_id", "name": "公司", "type": "DataStructField", "alias": "comOrgId", "appId": 47014, "props": {"unique": false, "comment": "公司", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "com_org_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "sys_common$org_struct_md", "currentModelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "relationModelAlias": "sys_common$org_struct_md", "linkModelFieldAlias": null, "currentModelFieldAlias": "comOrgId"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "date_from", "name": "开始日期", "type": "DataStructField", "alias": "dateFrom", "appId": 47014, "props": {"unique": false, "comment": "开始日期", "required": false, "encrypted": false, "fieldType": "DATE", "columnName": "date_from", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "date_to", "name": "结束日期", "type": "DataStructField", "alias": "dateTo", "appId": 47014, "props": {"unique": false, "comment": "结束日期", "required": false, "encrypted": false, "fieldType": "DATE", "columnName": "date_to", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "mat_type_id", "name": "物料类型", "type": "DataStructField", "alias": "matTypeId", "appId": 47014, "props": {"unique": false, "comment": "物料类型", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "mat_type_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_mat_type_cf", "currentModelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "relationModelAlias": "ERP_GEN$gen_mat_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "matTypeId"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "group_index", "name": "分组指标", "type": "DataStructField", "alias": "groupIndex", "appId": 47014, "props": {"length": 256, "unique": false, "comment": "分组指标", "dictPros": {"dictValues": [{"label": "消耗", "value": "CONSUME"}, {"label": "需求", "value": "REQUIREMENT"}, {"label": "价格", "value": "PRICE"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "group_index", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "index_set_mode", "name": "指标设置方式", "type": "DataStructField", "alias": "indexSetMode", "appId": 47014, "props": {"length": 256, "unique": false, "comment": "指标设置方式", "dictPros": {"dictValues": [{"label": "比例", "value": "RATIO"}, {"label": "数值", "value": "NUMERIC"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "index_set_mode", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "group_strategy_item_id", "name": "ABC分组方案行项目", "type": "DataStructField", "alias": "groupStrategyItemId", "appId": 47014, "props": {"unique": false, "comment": "ABC分组方案行项目", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "group_strategy_item_id", "compositeKey": false, "relationMeta": {"sync": true, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "relationModelKey": "ERP_SCM$inv_abc_group_strategy_item_cf", "currentModelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "relationModelAlias": "ERP_SCM$inv_abc_group_strategy_item_cf", "linkModelFieldAlias": "invAbcGroupStrategyHeadCfId", "currentModelFieldAlias": "groupStrategyItemId"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "id", "name": "ID", "type": "DataStructField", "alias": "id", "appId": 47014, "props": {"length": 20, "unique": true, "comment": "ID", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "id", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "created_by", "name": "创建人", "type": "DataStructField", "alias": "created<PERSON>y", "appId": 47014, "props": {"length": 20, "unique": true, "comment": "创建人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "created_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_SCM$user", "currentModelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "relationModelAlias": "ERP_SCM$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "created<PERSON>y"}, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "updated_by", "name": "更新人", "type": "DataStructField", "alias": "updatedBy", "appId": 47014, "props": {"length": 20, "unique": true, "comment": "更新人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "updated_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_SCM$user", "currentModelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "relationModelAlias": "ERP_SCM$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "updatedBy"}, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "created_at", "name": "创建时间", "type": "DataStructField", "alias": "createdAt", "appId": 47014, "props": {"unique": true, "comment": "创建时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "created_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "updated_at", "name": "更新时间", "type": "DataStructField", "alias": "updatedAt", "appId": 47014, "props": {"unique": true, "comment": "更新时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "updated_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "version", "name": "版本号", "type": "DataStructField", "alias": "version", "appId": 47014, "props": {"length": 20, "unique": true, "comment": "版本号", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "version", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "deleted", "name": "逻辑删除标识", "type": "DataStructField", "alias": "deleted", "appId": 47014, "props": {"length": 20, "unique": true, "comment": "逻辑删除标识", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "deleted", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "origin_org_id", "name": "所属组织", "type": "DataStructField", "alias": "originOrgId", "appId": 47014, "props": {"unique": false, "comment": "所属组织", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "origin_org_id", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}]}}