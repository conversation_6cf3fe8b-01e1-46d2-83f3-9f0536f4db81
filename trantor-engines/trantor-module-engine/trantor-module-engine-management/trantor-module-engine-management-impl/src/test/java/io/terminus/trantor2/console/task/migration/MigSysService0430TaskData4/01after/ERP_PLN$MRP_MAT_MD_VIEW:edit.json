{"type": "View", "name": "ERP_PLN$MRP_MAT_MD_VIEW:edit", "parentKey": "ERP_PLN", "children": [], "props": {"conditionGroups": {"T0eI7Jwz6W3dMZniR8tRt": {"conditions": [{"conditions": [{"id": "0T4BDa5iBZ0_yxNZ_Gr5X", "key": null, "leftValue": {"fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgDimensionCode", "valueName": "orgDimensionCode"}]}, "operator": "EQ", "rightValue": {"constValue": "SCM_ORG_GRP", "fieldType": "Text", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "N5HvXdM8aiyF6llr8VeIU", "key": null, "leftValue": {"fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgBusinessTypeCodes", "valueName": "orgBusinessTypeCodes"}]}, "operator": "CONTAINS", "rightValue": {"constValue": "INV_ORG", "fieldType": "Text", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "sVhfSLBPDTEV82vH0lTSj", "key": null, "leftValue": {"fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "X_LMeAPmInMeTjkNSrwkP", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "T0eI7Jwz6W3dMZniR8tRt", "logicOperator": "OR", "type": "ConditionGroup"}, "qHubmxihHIFCnnNzmWkQU": {"conditions": [{"conditions": [{"id": "l2cMYX77sH71lO4HMJoDc", "key": null, "leftValue": {"fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgDimensionCode", "valueName": "orgDimensionCode"}]}, "operator": "EQ", "rightValue": {"constValue": "SCM_ORG_GRP", "fieldType": "Text", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "38V_PCbK6-kk-12YTvix7", "key": null, "leftValue": {"fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgBusinessTypeCodes", "valueName": "orgBusinessTypeCodes"}]}, "operator": "CONTAINS", "rightValue": {"constValue": "INV_LOC", "fieldType": "Text", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "OHm0ZirlW_IgAbU-sKfeI", "key": null, "leftValue": {"fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgParentId", "valueName": "orgParentId"}]}, "operator": "EQ", "rightValue": {"fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "defaultInvOrgId", "valueName": "defaultInvOrgId"}]}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "MA1UuINyDWx1DddGoR5Lv", "key": null, "leftValue": {"fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "nJdvRn9jbxQ33vq54MphA", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "qHubmxihHIFCnnNzmWkQU", "logicOperator": "OR", "type": "ConditionGroup"}}, "content": {"children": [{"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-editView-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" : \"创建\"}}物料MRP视图", "useExpression": "true"}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-qCK-ssGr0gv9abWd2v5rf", "name": "FormField", "props": {"componentProps": {"fieldAlias": "genMatMdId", "label": "选择gen_mat_md_id", "labelField": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "parentModelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$gen_mat_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_mat_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "matCode", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "isRelationColumn": true, "label": "物料编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "5uDih8FuTtrVWLSdjxQRt", "valueRules": null}], "name": "matCode", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "isRelationColumn": true, "label": "物料名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "6KAZAzDNmIRV4iDkLLf2N", "valueRules": null}], "name": "<PERSON><PERSON><PERSON>", "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["matTypeCode", "matTypeName"], "fieldAlias": "genMatTypeCfId", "label": "选择物料类型", "labelField": "matTypeName", "modelAlias": "ERP_GEN$gen_mat_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$gen_mat_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$gen_mat_type_cf_PAGING_DATA_SERVICE"}}, "isRelationColumn": true, "label": "物料类型", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "m1qlhpwZhepZ8376KxK0w", "valueRules": null}], "name": "genMatTypeCfId", "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "baseUomId", "label": "选择基本计量单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$gen_uom_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$gen_uom_type_cf_PAGING_DATA_SERVICE"}}, "isRelationColumn": true, "label": "基本计量单位", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "NJsWbjGjSqi-802Y-Oe5b", "valueRules": null}], "name": "baseUomId", "type": "OBJECT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "matCode", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "label": "物料编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "FGBJkTzyadNm6Df9JvKwl", "valueRules": null}], "name": "matCode", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "label": "物料名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "JgRU1WDEqLKRTnDj-uX0k", "valueRules": null}], "name": "<PERSON><PERSON><PERSON>", "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$gen_mat_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_md", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_md"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_GEN$gen_mat_md_PAGING_DATA_SERVICE", "type": "InvokeService"}, "labelField": "<PERSON><PERSON><PERSON>", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_GEN$gen_mat_md", "showFilterFields": true, "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "label": "物料编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "ATBF22OEVAbDGYT1Drf3w", "valueRules": null}], "name": "genMatMdId", "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-mrpAreaId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "mrpAreaId", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "displayComponentProps": {"findFlow": {"containerKey": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-mrpAreaId", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_mrp_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_GEN$gen_mat_mrp_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "labelField": ["areaName"], "modelAlias": "ERP_PLN$pln_mrp_area_info_md", "valueField": "id"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "areaCode", "modelAlias": "ERP_PLN$pln_mrp_area_info_md", "placeholder": "请输入"}, "isRelationColumn": true, "label": "区域编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "q9g_QCnzgB9fU-2DH2zZ_", "valueRules": null}], "name": "areaCode", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "areaName", "modelAlias": "ERP_PLN$pln_mrp_area_info_md", "placeholder": "请输入"}, "isRelationColumn": true, "label": "区域名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "qGZHNFWhZ7xGYdboi5iUv", "valueRules": null}], "name": "areaName", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_PLN$pln_mrp_area_info_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "isRelationColumn": true, "label": "ID", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "0NAnjdz6Oj4HmgCwemC9d", "valueRules": null}], "name": "id", "type": "NUMBER", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "areaCode", "modelAlias": "ERP_PLN$pln_mrp_area_info_md", "placeholder": "请输入"}, "label": "区域编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "1siy0JWXSxIKkHie4x-RG", "valueRules": null}], "name": "areaCode", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "areaName", "modelAlias": "ERP_PLN$pln_mrp_area_info_md", "placeholder": "请输入"}, "label": "区域名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "969jF3Xp_3_5XGj6jMUNb", "valueRules": null}], "name": "areaName", "type": "TEXT", "width": 120}], "findFlow": {"containerKey": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-mrpAreaId", "context$": "$context", "modelAlias": "ERP_PLN$pln_mrp_area_info_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_PLN$pln_mrp_area_info_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_PLN$pln_mrp_area_info_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_PLN$pln_mrp_area_info_md", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_PLN$pln_mrp_area_info_md"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_PLN$pln_mrp_area_info_md_PAGING_DATA_SERVICE", "type": "InvokeService"}, "labelField": ["areaName"], "modalProps": {"size": "middle", "width": 720}, "mode": "single", "modelAlias": "ERP_PLN$pln_mrp_area_info_md", "shape": "line", "showFilterFields": true, "showScope": "all", "tableCondition": null, "tableConditionContext$": null, "valueField": "id"}, "editComponentType": "RelationSelect", "hidden": false, "label": "MRP区域", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "jqvN0zmgfaMwpAlwPqhyc", "valueRules": null}], "name": "mrpAreaId", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-planningStrategyId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "planningStrategyId", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "strategyCode", "modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "placeholder": "请输入"}, "isRelationColumn": true, "label": "策略编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "X1vyCTdf9TMHety7GZKHL", "valueRules": null}], "name": "strategyCode", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "strategyName", "modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "placeholder": "请输入"}, "isRelationColumn": true, "label": "策略名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "QA-Aq-1Ci43EXjvUu3w3D", "valueRules": null}], "name": "strategyName", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "isRelationColumn": true, "label": "ID", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "06c1IqCFLjIv_hx_eFHrj", "valueRules": null}], "name": "id", "type": "NUMBER", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "strategyCode", "modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "placeholder": "请输入"}, "label": "策略编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "4_-DMeSKW1DuwtIYK-1uX", "valueRules": null}], "name": "strategyCode", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "strategyName", "modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "placeholder": "请输入"}, "label": "策略名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "EeWz66VyfDqH77L5MWOGf", "valueRules": null}], "name": "strategyName", "type": "TEXT", "width": 120}], "findFlow": {"containerKey": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-planningStrategyId", "context$": "$context", "modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_PLN$pln_mrp_planning_strategy_cf"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_PLN$pln_mrp_planning_strategy_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_PLN$pln_mrp_planning_strategy_cf"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_PLN$pln_mrp_planning_strategy_cf_PAGING_DATA_SERVICE", "type": "InvokeService"}, "label": "选择计划策略", "labelField": ["strategyName"], "modalProps": {"size": "middle", "width": 720}, "mode": "single", "modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "shape": "line", "showFilterFields": true, "showScope": "all", "tableCondition": null, "tableConditionContext$": null, "valueField": "id"}, "editComponentType": "RelationSelect", "hidden": false, "label": "计划策略", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "rr6ZjokZhM5c19UOHxYkS", "valueRules": null}], "name": "planningStrategyId", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-planningGroupCfId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "planningGroupCfId", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "planGroupCode", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请输入"}, "isRelationColumn": true, "label": "计划组编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "lYCqekh_XUS9-EYbCmpsJ", "valueRules": null}], "name": "planGroupCode", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "planGroupName", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请输入"}, "isRelationColumn": true, "label": "计划组名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "LkjaOPi_d1hAVRwjd5nWu", "valueRules": null}], "name": "planGroupName", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"fields": [], "filterFields": [], "labelField": [], "shape": "line"}, "editComponentType": "InputNumber", "isRelationColumn": true, "label": "ID", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "61gCvfoh3mHYkmwafZ9G1", "valueRules": null}], "name": "id", "type": "NUMBER", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "planGroupCode", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请输入"}, "label": "计划组编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "RsajWICJlY22KaDWU-Aop", "valueRules": null}], "name": "planGroupCode", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "planGroupName", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请输入"}, "label": "计划组名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "maNmoQUx7fV9f3fHYUjVA", "valueRules": null}], "name": "planGroupName", "type": "TEXT", "width": 120}], "findFlow": {"containerKey": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-planningGroupCfId", "context$": "$context", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_PLN$pln_mrp_planning_group_cf"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_PLN$pln_mrp_planning_group_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_PLN$pln_mrp_planning_group_cf"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_PLN$pln_mrp_planning_group_cf_PAGING_DATA_SERVICE", "type": "InvokeService"}, "label": "选择MRP计划组", "labelField": ["planGroupName"], "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "shape": "line", "showFilterFields": true, "showScope": "all", "tableCondition": null, "tableConditionContext$": null, "valueField": "id"}, "editComponentType": "RelationSelect", "hidden": false, "label": "MRP计划组", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "_1iOpDgkiqfIRSjkASr0c", "valueRules": null}], "name": "planningGroupCfId", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-planningCycle", "name": "FormField", "props": {"componentProps": {"fieldAlias": "planningCycle", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "hidden": false, "label": "计划周期", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "CslCo_FsAYNiRQTdCMf6S", "valueRules": null}], "name": "planningCycle", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-minSafetyStkQty", "name": "FormField", "props": {"componentProps": {"fieldAlias": "minSafetyStkQty", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入", "precision": 6}, "displayComponentProps": {"precision": 6}, "displayComponentType": "Number", "editComponentProps": {"precision": 6, "shape": "line"}, "editComponentType": "InputNumber", "hidden": false, "label": "最小安全库存", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "Y2rvWEoXQ438pRM7iKZfi", "valueRules": null}], "name": "minSafetyStkQty", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-servLevelPercent", "name": "FormField", "props": {"componentProps": {"fieldAlias": "servLevelPercent", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "hidden": false, "label": "服务水平", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "5YcXQH910iNJfanplCbbj", "valueRules": null}], "name": "servLevelPercent", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-predictionFormula", "name": "FormField", "props": {"componentProps": {"fieldAlias": "predictionFormula", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "hidden": false, "label": "预测公式", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "cpfNG2UPr92nFs2TXsIqR", "valueRules": null}], "name": "predictionFormula", "rules": [], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-predictionIntervalStartDate", "name": "FormField", "props": {"componentProps": {"fieldAlias": "predictionIntervalStartDate", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请选择"}, "displayComponentType": "Date", "editComponentType": "DatePicker", "hidden": false, "label": "预测区间开始日期", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "pEnchs-02LYDvbd5pJXZI", "valueRules": null}], "name": "predictionIntervalStartDate", "rules": [], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-predictionIntervalEndDate", "name": "FormField", "props": {"componentProps": {"fieldAlias": "predictionIntervalEndDate", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请选择"}, "displayComponentType": "Date", "editComponentType": "DatePicker", "hidden": false, "label": "预测区间结束日期", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "LbFeiXQMEZKHwP2RFrcV1", "valueRules": null}], "name": "predictionIntervalEndDate", "rules": [], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-invMvmTypeName", "name": "FormField", "props": {"componentProps": {"fieldAlias": "invMvmTypeName", "label": "选择参与预测移动凭证类型", "labelField": "code", "modelAlias": "ERP_SCM$inv_mvm_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_SCM$inv_mvm_type_cf", "serviceKey": "ERP_SCM$inv_mvm_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}, "labelField": ["code"], "modelAlias": "ERP_SCM$inv_mvm_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "code", "modelAlias": "ERP_SCM$inv_mvm_type_cf", "placeholder": "请输入"}, "isRelationColumn": true, "label": "编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "CSwSlQnsSLRP_z8UGY2FF", "valueRules": null}], "name": "code", "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["code", "uniqueCode", "name", "isCreateNewBatch", "isSupportWriteOff"], "fieldAlias": "bsTypeId", "label": "选择业务类型", "labelField": "name", "modelAlias": "ERP_SCM$inv_bs_type_cf", "parentModelAlias": "ERP_SCM$inv_mvm_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$inv_bs_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$inv_bs_type_cf_PAGING_DATA_SERVICE"}}, "isRelationColumn": true, "label": "业务类型", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "5yT2RvtxEgEsjNSU3Ec68", "valueRules": null}], "name": "bsTypeId", "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["code", "name", "isRevType", "revFbTypeId", "isDocPre", "uniqueCode"], "fieldAlias": "fbTypeId", "label": "选择作业正逆向标识", "labelField": "name", "modelAlias": "ERP_SCM$inv_fb_type_cf", "parentModelAlias": "ERP_SCM$inv_mvm_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$inv_fb_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$inv_fb_type_cf_PAGING_DATA_SERVICE"}}, "isRelationColumn": true, "label": "作业正逆向标识", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "rIwUr1H0zqltDlwSULE3l", "valueRules": null}], "name": "fbTypeId", "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["code", "name", "from", "to"], "fieldAlias": "invTypeTransId", "label": "选择库存类型转移标识", "labelField": "code", "modelAlias": "ERP_SCM$inv_inv_type_trans_cf", "parentModelAlias": "ERP_SCM$inv_mvm_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$inv_inv_type_trans_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$inv_inv_type_trans_cf_PAGING_DATA_SERVICE"}}, "isRelationColumn": true, "label": "库存类型转移标识", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "PdZG4yAIwS1OE7GjIpSqK", "valueRules": null}], "name": "invTypeTransId", "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["code", "uniqueCode", "name"], "fieldAlias": "mvmExtTypeId", "label": "选择业务类型扩展", "labelField": "name", "modelAlias": "ERP_SCM$inv_mvm_ext_type_cf", "parentModelAlias": "ERP_SCM$inv_mvm_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$inv_mvm_ext_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$inv_mvm_ext_type_cf_PAGING_DATA_SERVICE"}}, "isRelationColumn": true, "label": "业务类型扩展", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "hW3RgY0IDzJwtdnIYnPzA", "valueRules": null}], "name": "mvmExtTypeId", "type": "OBJECT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_SCM$inv_mvm_type_cf", "serviceKey": "ERP_SCM$inv_mvm_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_SCM$inv_mvm_type_cf", "serviceKey": "ERP_SCM$inv_mvm_type_cf_PAGING_DATA_SERVICE", "type": "InvokeSystemService"}, "label": "选择参与预测移动凭证类型", "labelField": "code", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_SCM$inv_mvm_type_cf", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "参与预测移动凭证类型", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "dr1lx7ZwjOUlZbjh8jE_X", "valueRules": null}], "name": "invMvmTypeName", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-smoothingIndex", "name": "FormField", "props": {"componentProps": {"fieldAlias": "smoothingIndex", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "hidden": false, "label": "平滑系数", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "u786p1CpsdV098sTwEPBQ", "valueRules": null}], "name": "smoothingIndex", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-id", "name": "FormField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-createdBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "ERP_GEN$user", "parentModelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$user", "serviceKey": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}, "labelField": ["username"], "modelAlias": "ERP_GEN$user"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "modelAlias": "ERP_GEN$user", "placeholder": "请输入"}, "isRelationColumn": true, "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "bzRlAziAHzJ-3-qizaBUF", "valueRules": null}], "name": "username", "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$user", "serviceKey": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$user", "serviceKey": "ERP_GEN$user_PAGING_DATA_SERVICE", "type": "InvokeSystemService"}, "label": "选择创建人", "labelField": "username", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_GEN$user", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": true, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-updatedBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "ERP_GEN$user", "parentModelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$user", "serviceKey": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}, "labelField": ["username"], "modelAlias": "ERP_GEN$user"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "modelAlias": "ERP_GEN$user", "placeholder": "请输入"}, "isRelationColumn": true, "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "RoVo3ngyCGdM1aoaCsMhN", "valueRules": null}], "name": "username", "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$user", "serviceKey": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$user", "serviceKey": "ERP_GEN$user_PAGING_DATA_SERVICE", "type": "InvokeSystemService"}, "label": "选择更新人", "labelField": "username", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_GEN$user", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": true, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-createdAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请选择"}, "displayComponentType": "Date", "editComponentType": "DatePicker", "hidden": true, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-updatedAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请选择"}, "displayComponentType": "Date", "editComponentType": "DatePicker", "hidden": true, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-originOrgId", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "originOrgId", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "hidden": true, "initialValue": 0, "label": "所属组织", "name": "originOrgId", "rules": [{"message": "请输入所属组织", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-m5ttBBUm_9Yl-yNzhMxZU", "name": "FormField", "props": {"componentProps": {"fieldAlias": "nickname", "modelAlias": "ERP_GEN$user", "placeholder": "请输入"}, "displayComponentProps": {"labelField": ["username"], "modelAlias": "ERP_GEN$user"}, "displayComponentType": "Text", "editComponentProps": {"fields": [], "filterFields": [], "findFlow": {"containerKey": "ERP_PLN$MRP_MAT_MD_VIEW-m5ttBBUm_9Yl-yNzhMxZU", "context$": "$context", "modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "params": [{"elements": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_PLN$pln_mrp_planning_strategy_cf"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Pageable"}], "serviceKey": "ERP_PLN$pln_mrp_planning_strategy_cf_FIND_ONE_DATA_SERVICE", "type": "InvokeService"}, "labelField": [], "modalProps": {"size": "middle", "width": 720}, "tableConditionContext$": null}, "editComponentType": "InputText", "label": "关键字段1，请勿删除", "lookup": [{"action": "get", "conditionGroup": {"conditions": [{"conditions": [{"id": "A1xkyo2jLwU7T9TXiTQdf", "leftValue": {"fieldType": "Number", "scope": "form", "title": "计划策略", "type": "VarValue", "val": "planningStrategyId", "value": "ERP_GEN$gen_mat_mrp_md.planningStrategyId", "valueType": "VAR", "varVal": "planningStrategyId", "varValue": [{"valueKey": "planningStrategyId", "valueName": "planningStrategyId"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "_SeJe_u7JDCPjcmtTHrJp", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "w4uC7gk044e0bXc5dwBaw", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "iuF3hZytHmubfJGBQwJRg", "operator": "SERVICE", "trigger": "all", "valueRules": {"scope": null, "serviceParams": {"entryNewParams": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"type": "const", "value": "ERP_PLN$pln_mrp_planning_strategy_cf"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"action": {"selector": "planningStrategyId", "target": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md"}, "type": "action"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "outputParams": {"expression": "data.strategyCode", "serviceKey": "ERP_PLN$SYS_FindDataByIdService", "type": "expression"}}, "type": "SERVICE", "val": "ERP_PLN$PLN_MRP_PLANNING_STRATEGY_CF_FIND_SINGLE_DATA_BY_ID_SERVICE", "value": "ERP_PLN$PLN_MRP_PLANNING_STRATEGY_CF_FIND_SINGLE_DATA_BY_ID_SERVICE"}}, {"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "tJWcZQ_B7b64v1OgP22LT", "valueRules": null}], "name": "createdBy.nickname", "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-RYE8noxsl8wmoQC210-Cr", "name": "FormField", "props": {"componentProps": {"fieldAlias": "deleted", "modelAlias": "ERP_GEN$user", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "label": "关键字段2，请勿删除", "lookup": [{"action": "get", "conditionGroup": {"conditions": [{"conditions": [{"id": "ZTAKXgQl89KdfY54D8nac", "leftValue": {"fieldType": "Number", "scope": "form", "title": "计划策略", "type": "VarValue", "val": "planningStrategyId", "value": "ERP_GEN$gen_mat_mrp_md.planningStrategyId", "valueType": "VAR", "varVal": "planningStrategyId", "varValue": [{"valueKey": "planningStrategyId", "valueName": "planningStrategyId"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "6yPLS599c3t9-kOk5qQdp", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "0ZKAiNtOFA97xMfGOLXxh", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "e71X27HtM118SrJrHOMoZ", "operator": "SERVICE", "trigger": "all", "valueRules": {"scope": null, "serviceParams": {"entryNewParams": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "required": null, "valueConfig": {"action": {"selector": "planningStrategyId", "target": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md"}, "type": "action"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "required": null}], "outputParams": {"expression": "count", "serviceKey": "ERP_PLN$strategy_consume_line_query", "type": "expression"}}, "type": "SERVICE", "val": "ERP_PLN$strategy_consume_line_query", "value": "ERP_PLN$strategy_consume_line_query"}}, {"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "MHXvlDTjAK9jcXWNkDxdk", "valueRules": null}], "name": "createdBy.deleted", "type": "NUMBER", "width": 120}, "type": "Widget"}], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "MRP信息"}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-supplyType", "name": "FormField", "props": {"componentProps": {"fieldAlias": "supplyType", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "hidden": false, "label": "供应方式", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "bNnyjVS2pnK3xipukSOzz", "valueRules": null}], "name": "supplyType", "rules": [], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-defaultInvOrgId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "defaultInvOrgId", "label": "选择默认库存组织", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}, "labelField": ["orgName"], "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "isRelationColumn": true, "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "TGYYPIh-oWSm0sG_hrxb1", "valueRules": null}], "name": "orgName", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "isRelationColumn": true, "label": "组织编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "pIoa7OvjdHoCTb0Of4IBj", "valueRules": null}], "name": "orgCode", "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentType": "InputText", "filterType": "fuzzy", "label": "组织编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "oRFkyJD6ibnzvp2TacW1F", "valueRules": null}], "name": "orgCode", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentType": "InputText", "filterType": "fuzzy", "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "AJtqhaOlHuBbUltAiH4A7", "valueRules": null}], "name": "orgName", "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "orgDimensionCode", "fieldName": "orgDimensionCode", "fieldType": "Text", "valueConfig": {"type": "const", "value": "SCM_ORG_GRP"}}, {"fieldAlias": "orgStatus", "fieldName": "orgStatus", "fieldType": "Text", "valueConfig": {"type": "const", "value": "ENABLED"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "type": "InvokeService"}, "label": "选择默认库存组织", "labelField": "orgName", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "sys_common$org_struct_md", "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "0T4BDa5iBZ0_yxNZ_Gr5X", "leftValue": {"fieldType": "Text", "title": "组织维度编码", "type": "VarValue", "val": "orgDimensionCode", "value": "sys_common$org_struct_md.orgDimensionCode", "valueType": "VAR", "varVal": "orgDimensionCode", "varValue": [{"valueKey": "orgDimensionCode", "valueName": "orgDimensionCode"}]}, "operator": "EQ", "rightValue": {"constValue": "SCM_ORG_GRP", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "N5HvXdM8aiyF6llr8VeIU", "leftValue": {"fieldType": "Text", "title": "业务类型编码集合", "type": "VarValue", "val": "orgBusinessTypeCodes", "value": "sys_common$org_struct_md.orgBusinessTypeCodes", "valueType": "VAR", "varVal": "orgBusinessTypeCodes", "varValue": [{"valueKey": "orgBusinessTypeCodes", "valueName": "orgBusinessTypeCodes"}]}, "operator": "CONTAINS", "rightValue": {"constValue": "INV_ORG", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "sVhfSLBPDTEV82vH0lTSj", "leftValue": {"fieldType": "Enum", "title": "状态", "type": "VarValue", "val": "orgStatus", "value": "sys_common$org_struct_md.orgStatus", "valueType": "VAR", "varVal": "orgStatus", "varValue": [{"valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "X_LMeAPmInMeTjkNSrwkP", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "T0eI7Jwz6W3dMZniR8tRt", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": "$context"}, "editComponentType": "RelationSelect", "hidden": false, "label": "默认库存组织", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "uVPTXW0oBH13iC3wKzKif", "valueRules": null}], "name": "defaultInvOrgId", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-defaultInvLocId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "defaultInvLocId", "label": "选择默认库存地点", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}, "labelField": ["orgName"], "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "isRelationColumn": true, "label": "组织编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "d-6bl9BuWXMAFhWYsoyXk", "valueRules": null}], "name": "orgCode", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "isRelationColumn": true, "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "Zh60kfrdjR57DJAfJ1Whw", "valueRules": null}], "name": "orgName", "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentType": "InputText", "filterType": "fuzzy", "label": "组织编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "NqmMHbgTmGzjjNkh2-eXZ", "valueRules": null}], "name": "orgCode", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "editComponentType": "InputText", "filterType": "fuzzy", "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "Vrz1wxYEVTnaxdCXqgk8j", "valueRules": null}], "name": "orgName", "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "orgDimensionCode", "fieldName": "orgDimensionCode", "fieldType": "Text", "valueConfig": {"type": "const", "value": "SCM_ORG_GRP"}}, {"fieldAlias": "parentId", "fieldName": "parentId", "fieldType": "Text", "valueConfig": {"action": {"name": "getData", "selector": "defaultInvOrgId", "target": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md"}, "type": "action"}}, {"fieldAlias": "orgStatus", "fieldName": "orgStatus", "fieldType": "Text", "valueConfig": {"type": "const", "value": "ENABLED"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "type": "InvokeService"}, "label": "选择默认库存地点", "labelField": "orgName", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "sys_common$org_struct_md", "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "l2cMYX77sH71lO4HMJoDc", "leftValue": {"fieldType": "Text", "title": "组织维度编码", "type": "VarValue", "val": "orgDimensionCode", "value": "sys_common$org_struct_md.orgDimensionCode", "valueType": "VAR", "varVal": "orgDimensionCode", "varValue": [{"valueKey": "orgDimensionCode", "valueName": "orgDimensionCode"}]}, "operator": "EQ", "rightValue": {"constValue": "SCM_ORG_GRP", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "38V_PCbK6-kk-12YTvix7", "leftValue": {"fieldType": "Text", "title": "业务类型编码集合", "type": "VarValue", "val": "orgBusinessTypeCodes", "value": "sys_common$org_struct_md.orgBusinessTypeCodes", "valueType": "VAR", "varVal": "orgBusinessTypeCodes", "varValue": [{"valueKey": "orgBusinessTypeCodes", "valueName": "orgBusinessTypeCodes"}]}, "operator": "CONTAINS", "rightValue": {"constValue": "INV_LOC", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "OHm0ZirlW_IgAbU-sKfeI", "leftValue": {"fieldType": "Model", "title": "父组织", "type": "VarValue", "val": "orgParentId", "value": "sys_common$org_struct_md.orgParentId", "valueType": "VAR", "varVal": "orgParentId", "varValue": [{"valueKey": "orgParentId", "valueName": "orgParentId"}]}, "operator": "EQ", "rightValue": {"fieldType": "Model", "scope": "form", "target": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "title": "defaultInvOrgId", "type": "VarValue", "val": "defaultInvOrgId", "value": "defaultInvOrgId", "valueType": "VAR", "varVal": "defaultInvOrgId", "varValue": [{"valueKey": "defaultInvOrgId", "valueName": "defaultInvOrgId"}]}, "type": "ConditionLeaf"}, {"id": "MA1UuINyDWx1DddGoR5Lv", "leftValue": {"fieldType": "Enum", "title": "状态", "type": "VarValue", "val": "orgStatus", "value": "sys_common$org_struct_md.orgStatus", "valueType": "VAR", "varVal": "orgStatus", "varValue": [{"valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "nJdvRn9jbxQ33vq54MphA", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "qHubmxihHIFCnnNzmWkQU", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": "$context"}, "editComponentType": "RelationSelect", "hidden": false, "label": "默认库存地点", "lookup": [{"action": "clear", "conditionGroup": {"conditions": [{"conditions": [{"id": "q1_c8YNsF3uiq6-9kfjxf", "leftValue": {"fieldType": "Model", "scope": "form", "title": "默认库存组织", "type": "VarValue", "val": "defaultInvOrgId", "value": "ERP_GEN$gen_mat_mrp_md.defaultInvOrgId", "valueType": "VAR", "varVal": "defaultInvOrgId", "varValue": [{"valueKey": "defaultInvOrgId", "valueName": "defaultInvOrgId"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "J_UUyqojfeyCh14sv8HIm", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "sweXlRS-c6Epm-CjF5_yZ", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "aK6R7qwsrMk6Jkei7pLSU", "operator": "clear", "trigger": "onchange", "valueRules": null}, {"action": "clear", "conditionGroup": {"conditions": [{"conditions": [{"id": "6fXDJtklWRAsfvZyMH9pi", "leftValue": {"fieldType": "Model", "scope": "form", "title": "默认库存组织", "type": "VarValue", "val": "defaultInvOrgId", "value": "ERP_GEN$gen_mat_mrp_md.defaultInvOrgId", "valueType": "VAR", "varVal": "defaultInvOrgId", "varValue": [{"valueKey": "defaultInvOrgId", "valueName": "defaultInvOrgId"}]}, "operator": "IS_NULL", "type": "ConditionLeaf"}], "id": "XFtvn1W1v3nCw0p6fFCqS", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "tFhShoUPNvPjGnwSlb9rG", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "Wcgb8Wt6TuN1ghp8MMhWL", "operator": "clear", "trigger": "all", "valueRules": null}], "name": "defaultInvLocId", "rules": [], "type": "OBJECT"}, "type": "Widget"}], "key": "ERP_PLN$MRP_MAT_MD_VIEW-jKbwJfw9tTq4RiYwyYocQ", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "供应信息"}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-plannedDelivTime", "name": "FormField", "props": {"componentProps": {"fieldAlias": "plannedDelivTime", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "hidden": false, "label": "计划交货天数", "lookup": [{"conditionGroup": {"conditions": [{"conditions": [{"id": "xR46CxPzGVeJtbAq_Mb6n", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "供应类型", "type": "VarValue", "val": "supplyType", "value": "ERP_GEN$gen_mat_mrp_md.supplyType", "valueType": "VAR", "varVal": "supplyType", "varValue": [{"valueKey": "supplyType", "valueName": "supplyType"}]}, "operator": "EQ", "rightValue": {"constValue": "F", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "wSMnUjj2z95r6Y4v0ryjp", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "8pZ0ogxP9COETi_C6V6i-", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "wATmAF9ZtX2-GC-HpbgBy", "trigger": "all", "valueRules": null}, {"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "uN6A-bzXOHrya3DOt7mCn", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "供应类型", "type": "VarValue", "val": "supplyType", "value": "ERP_GEN$gen_mat_mrp_md.supplyType", "valueType": "VAR", "varVal": "supplyType", "varValue": [{"valueKey": "supplyType", "valueName": "supplyType"}]}, "operator": "NEQ", "rightValue": {"constValue": "F", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "zdtH9OwyFp8KcVEo8nGbl", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "nlokMWBT7C1jx6La9hUzN", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "required": false}, "key": "J_JcghgKit35-m6J7TikW", "operator": null, "trigger": "all", "valueRules": null}], "name": "plannedDelivTime", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-purProcessDays", "name": "FormField", "props": {"componentProps": {"fieldAlias": "purProcessDays", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "hidden": false, "label": "采购处理天数", "lookup": [{"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "LPbM3uq_kEtFotQW0a1vM", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "供应类型", "type": "VarValue", "val": "supplyType", "value": "ERP_GEN$gen_mat_mrp_md.supplyType", "valueType": "VAR", "varVal": "supplyType", "varValue": [{"valueKey": "supplyType", "valueName": "supplyType"}]}, "operator": "EQ", "rightValue": {"constValue": "F", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "bkuqQTg5nlIHrHvVSSpdz", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "2jjsGvGTb2lKLD6NJT20z", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "TxM8KMDmFkSzALC-L_rO_", "operator": null, "trigger": "all", "valueRules": null}, {"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "y9E6ot9Hy7ASvj5iuYjX0", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "供应类型", "type": "VarValue", "val": "supplyType", "value": "ERP_GEN$gen_mat_mrp_md.supplyType", "valueType": "VAR", "varVal": "supplyType", "varValue": [{"valueKey": "supplyType", "valueName": "supplyType"}]}, "operator": "NEQ", "rightValue": {"constValue": "F", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "zCfn7Z-At5ylWI44QAV8Q", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "9Sl1zzLk-xFxK3dGn-SEB", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "required": false}, "key": "DgPRfTphx0LSpBm2bshVR", "operator": null, "trigger": "all", "valueRules": null}], "name": "purProcessDays", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-planWcCfId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "planWcCfId", "label": "选择计划日历", "labelField": "wcHeadCode", "modelAlias": "ERP_GEN$gen_wc_head_cf", "parentModelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_wc_head_cf", "serviceKey": "ERP_GEN$gen_wc_head_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}, "labelField": ["wcHeadCode"], "modelAlias": "ERP_GEN$gen_wc_head_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "wcHeadCode", "modelAlias": "ERP_GEN$gen_wc_head_cf", "placeholder": "请输入"}, "isRelationColumn": true, "label": "工作日日历编号", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "dDOTR3QG7aumJ3bvYmIv2", "valueRules": null}], "name": "wcHeadCode", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "wcHeadName", "modelAlias": "ERP_GEN$gen_wc_head_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "isRelationColumn": true, "label": "工作日日历名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "yEL-CSgddnoAjj3__oIcl", "valueRules": null}], "name": "wcHeadName", "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "wcHeadCode", "modelAlias": "ERP_GEN$gen_wc_head_cf", "placeholder": "请输入"}, "label": "工作日日历编号", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "cN3FekvTUdMM7pTxHh_K0", "valueRules": null}], "name": "wcHeadCode", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "wcHeadName", "modelAlias": "ERP_GEN$gen_wc_head_cf", "placeholder": "请输入"}, "editComponentType": "InputText", "filterType": "fuzzy", "label": "工作日日历名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "aonja39-CnuFBTBLFJpR7", "valueRules": null}], "name": "wcHeadName", "type": "TEXT", "width": 120}], "findFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_wc_head_cf", "serviceKey": "ERP_GEN$gen_wc_head_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_wc_head_cf", "serviceKey": "ERP_GEN$gen_wc_head_cf_PAGING_DATA_SERVICE", "type": "InvokeSystemService"}, "label": "选择计划日历", "labelField": ["wcHeadName"], "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_GEN$gen_wc_head_cf", "showFilterFields": true, "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "计划日历", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "Uk48CrbXt6r-oKcOwKDcm", "valueRules": null}], "name": "planWcCfId", "rules": [], "type": "OBJECT"}, "type": "Widget"}], "key": "ERP_PLN$MRP_MAT_MD_VIEW-JcwQe3PDLxFd6S3re0kDN", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "计划时间"}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-Ss5zbpQLroWG_SGX4unnJ", "name": "FormField", "props": {"componentProps": {"fieldAlias": "lotSizeIntervalDays", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "label": "期间批量天数", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "cV1pQedMYcFm6kvxmzuDF", "valueRules": null}], "name": "lotSizeIntervalDays", "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-1SpTcBBvND1eTj7p-3L6R", "name": "FormField", "props": {"componentProps": {"fieldAlias": "lotSizeAvailableDateType", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "label": "期间日期类型", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "YFZofsG486xxvkSlUYYjQ", "valueRules": null}], "name": "lotSizeAvailableDateType", "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-_LWHK58kcLnOUc30dtdEq", "name": "FormField", "props": {"componentProps": {"fieldAlias": "fixedLotSizeQty", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入", "precision": 6}, "displayComponentProps": {"precision": 6}, "displayComponentType": "Number", "editComponentProps": {"precision": 6, "shape": "line"}, "editComponentType": "InputNumber", "label": "固定批量", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "pL-0zXZgrHzsnctSFC9h5", "valueRules": null}], "name": "fixedLotSizeQty", "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-JKvI_dv2PXWTOQDqhuDvf", "name": "FormField", "props": {"componentProps": {"fieldAlias": "maxLotSizeQty", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入", "precision": 6}, "displayComponentProps": {"precision": 6}, "displayComponentType": "Number", "editComponentProps": {"precision": 6, "shape": "line"}, "editComponentType": "InputNumber", "label": "最大批量", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "hCyqhQav_5blcPr2mNLSQ", "valueRules": null}], "name": "maxLotSizeQty", "type": "NUMBER", "validateRules": [{"condition": {"conditions": {"conditions": [{"conditions": [{"id": "qvit7W44QCPWEBOMO2pGh", "leftValue": {"fieldType": "Number", "scope": "form", "title": "最大批量", "type": "VarValue", "val": "maxLotSizeQty", "value": "ERP_GEN$gen_mat_mrp_md.maxLotSizeQty", "valueType": "VAR", "varVal": "maxLotSizeQty", "varValue": [{"valueKey": "maxLotSizeQty", "valueName": "maxLotSizeQty"}]}, "operator": "GTE", "rightValue": {"fieldType": "Number", "scope": "form", "target": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "title": "minLotSizeQty", "type": "VarValue", "val": "minLotSizeQty", "value": "minLotSizeQty", "valueType": "VAR", "varVal": "minLotSizeQty", "varValue": [{"valueKey": "minLotSizeQty", "valueName": "minLotSizeQty"}]}, "type": "ConditionLeaf"}], "id": "clxi6e34iFQmZltGQwhTn", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "TuZHOEaQCV-xWd0gU0Q71", "logicOperator": "OR", "type": "ConditionGroup"}, "message": "最大批量不能小于最小批量"}, "conditionGroup": {"conditions": [{"conditions": [{"id": "zYO3JXmA3uAfl5uh5ZaUk", "leftValue": {"fieldType": "Number", "scope": "form", "title": "最小批量", "type": "VarValue", "val": "minLotSizeQty", "value": "ERP_GEN$gen_mat_mrp_md.minLotSizeQty", "valueType": "VAR", "varVal": "minLotSizeQty", "varValue": [{"valueKey": "minLotSizeQty", "valueName": "minLotSizeQty"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}, {"id": "2JTHBVGMLObCL39tPfwgr", "leftValue": {"fieldType": "Number", "scope": "form", "title": "最大批量", "type": "VarValue", "val": "maxLotSizeQty", "value": "ERP_GEN$gen_mat_mrp_md.maxLotSizeQty", "valueType": "VAR", "varVal": "maxLotSizeQty", "varValue": [{"valueKey": "maxLotSizeQty", "valueName": "maxLotSizeQty"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "0LubJ2pa9F1IvPRJHyROt", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "mEwYAc5i7RLa09OmOhhwu", "logicOperator": "OR", "type": "ConditionGroup"}, "key": "0tJ0YfHPFpHw33utOyEbS", "trigger": "onchange"}], "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-_VmGRwDkquU0g4Xc1m_0O", "name": "FormField", "props": {"componentProps": {"fieldAlias": "minLotSizeQty", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入", "precision": 6}, "displayComponentProps": {"precision": 6}, "displayComponentType": "Number", "editComponentProps": {"precision": 6, "shape": "line"}, "editComponentType": "InputNumber", "label": "最小批量", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "lH06PDZ2hIOhGdmrOI5t_", "valueRules": null}], "name": "minLotSizeQty", "type": "NUMBER", "validateRules": [{"condition": {"conditions": {"conditions": [{"conditions": [{"id": "JwTZGa7bc6TdLNUCewMiO", "leftValue": {"fieldType": "Number", "scope": "form", "title": "最小批量", "type": "VarValue", "val": "minLotSizeQty", "value": "ERP_GEN$gen_mat_mrp_md.minLotSizeQty", "valueType": "VAR", "varVal": "minLotSizeQty", "varValue": [{"valueKey": "minLotSizeQty", "valueName": "minLotSizeQty"}]}, "operator": "LTE", "rightValue": {"fieldType": "Number", "scope": "form", "target": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "title": "maxLotSizeQty", "type": "VarValue", "val": "maxLotSizeQty", "value": "maxLotSizeQty", "valueType": "VAR", "varVal": "maxLotSizeQty", "varValue": [{"valueKey": "maxLotSizeQty", "valueName": "maxLotSizeQty"}]}, "type": "ConditionLeaf"}], "id": "hvw5pTaelDaXHuD2y_KbV", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "9GYQQoCANhHRr7UAL3qSQ", "logicOperator": "OR", "type": "ConditionGroup"}, "message": "最小批量不能大于最大批量"}, "conditionGroup": {"conditions": [{"conditions": [{"id": "Nk-K2BmHgJXI_oPeUrC4N", "leftValue": {"fieldType": "Number", "scope": "form", "title": "最大批量", "type": "VarValue", "val": "maxLotSizeQty", "value": "ERP_GEN$gen_mat_mrp_md.maxLotSizeQty", "valueType": "VAR", "varVal": "maxLotSizeQty", "varValue": [{"valueKey": "maxLotSizeQty", "valueName": "maxLotSizeQty"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}, {"id": "QDEzI2VWtZfFXLH7B_xJE", "leftValue": {"fieldType": "Number", "scope": "form", "title": "最小批量", "type": "VarValue", "val": "minLotSizeQty", "value": "ERP_GEN$gen_mat_mrp_md.minLotSizeQty", "valueType": "VAR", "varVal": "minLotSizeQty", "varValue": [{"valueKey": "minLotSizeQty", "valueName": "minLotSizeQty"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "1cIgK01BF1oHl90ks6dPB", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "CvdFgNLmMuaQwLuU738sh", "logicOperator": "OR", "type": "ConditionGroup"}, "key": "CH0IC9zcuVrvja53xK_8n", "trigger": "onchange"}], "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-4PhgkWyB1lS15TY_KnBxJ", "name": "FormField", "props": {"componentProps": {"fieldAlias": "minPackagingQty", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入", "precision": 6}, "displayComponentProps": {"precision": 6}, "displayComponentType": "Number", "editComponentProps": {"precision": 6, "shape": "line"}, "editComponentType": "InputNumber", "label": "最小包装量", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "66PYehxqc0uj7-D0zgvt5", "valueRules": null}], "name": "minPackagingQty", "type": "NUMBER", "validateRules": [{"condition": {"conditions": {"conditions": [{"conditions": [{"id": "aaAt20jhsIZ9Pd-uJoRAO", "leftValue": {"fieldType": "Number", "scope": "form", "title": "最小包装量", "type": "VarValue", "val": "minPackagingQty", "value": "ERP_GEN$gen_mat_mrp_md.minPackagingQty", "valueType": "VAR", "varVal": "minPackagingQty", "varValue": [{"valueKey": "minPackagingQty", "valueName": "minPackagingQty"}]}, "operator": "LTE", "rightValue": {"fieldType": "Number", "scope": "form", "target": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "title": "maxLotSizeQty", "type": "VarValue", "val": "maxLotSizeQty", "value": "maxLotSizeQty", "valueType": "VAR", "varVal": "maxLotSizeQty", "varValue": [{"valueKey": "maxLotSizeQty", "valueName": "maxLotSizeQty"}]}, "type": "ConditionLeaf"}], "id": "SK8FrkW1pAYgQ6PDTr9nv", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "3lLPkDtj1kgPqzB6zxtcU", "logicOperator": "OR", "type": "ConditionGroup"}, "message": "最小包装量不能大于最大批量"}, "conditionGroup": {"conditions": [{"conditions": [{"id": "g6NdySZwjgcGJSW_LfS1j", "leftValue": {"fieldType": "Number", "scope": "form", "title": "最小包装量", "type": "VarValue", "val": "minPackagingQty", "value": "ERP_GEN$gen_mat_mrp_md.minPackagingQty", "valueType": "VAR", "varVal": "minPackagingQty", "varValue": [{"valueKey": "minPackagingQty", "valueName": "minPackagingQty"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}, {"id": "Kf6UP_W2gGsiaa0_SAnr4", "leftValue": {"fieldType": "Number", "scope": "form", "title": "最大批量", "type": "VarValue", "val": "maxLotSizeQty", "value": "ERP_GEN$gen_mat_mrp_md.maxLotSizeQty", "valueType": "VAR", "varVal": "maxLotSizeQty", "varValue": [{"valueKey": "maxLotSizeQty", "valueName": "maxLotSizeQty"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "i_UGh6lN4J271kMAUd8hR", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "d9KnwatKVL2KvwScK3fIX", "logicOperator": "OR", "type": "ConditionGroup"}, "key": "iyT6WCu1OJZnkNLCrFCDS", "trigger": "all"}], "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-_ebnEIbfDSg3tQNjQql1j", "name": "FormField", "props": {"componentProps": {"fieldAlias": "lotSizeSplitIntervalDays", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "label": "拆分间隔天数", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "wzTXcA86lOLS6Ur3ee_C9", "valueRules": null}], "name": "lotSizeSplitIntervalDays", "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-lotSizeTypeCfId", "name": "FormField", "props": {"componentProps": {"fieldAlias": "lotSizeSplitIntervalType", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "hidden": false, "label": "拆分间隔类型", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "Dyjt8vnOl5TxL2tShbLMe", "valueRules": null}], "name": "lotSizeSplitIntervalType", "rules": [], "type": "SELECT", "width": 120}, "type": "Widget"}], "key": "ERP_PLN$MRP_MAT_MD_VIEW-siINAs5sg1mG4h6ITE1B6", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "批量控制"}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-safetyStkQty", "name": "FormField", "props": {"componentProps": {"fieldAlias": "safetyStkQty", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入", "precision": 6}, "displayComponentProps": {"precision": 6}, "displayComponentType": "Number", "editComponentProps": {"precision": 6, "shape": "line"}, "editComponentType": "InputNumber", "hidden": false, "label": "安全库存", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "3GoMh7A6rb_ezdx2SQtxG", "valueRules": null}], "name": "safetyStkQty", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-reorderQty", "name": "FormField", "props": {"componentProps": {"fieldAlias": "reorderQty", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "hidden": false, "label": "再订货点", "lookup": [{"action": "clear", "conditionGroup": {"conditions": [{"conditions": [{"id": "hnFo8VFgeR7DqdQpZvjT6", "leftValue": {"fieldType": "Text", "scope": "form", "title": "创建人.昵称", "type": "VarValue", "val": "createdBy.nickname", "value": "ERP_GEN$gen_mat_mrp_md.createdBy.nickname", "valueType": "VAR", "varVal": "createdBy.nickname", "varValue": [{"valueKey": "created<PERSON>y", "valueName": "created<PERSON>y"}, {"valueKey": "nickname", "valueName": "nickname"}]}, "operator": "EQ", "rightValue": {"constValue": "MTS", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "wkLAhR69CowqYqVr4xCOe", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "Q3bkpBCH9CCSFERyfzQHA", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "DXnwmdhZmgvk9NFQEAk_P", "operator": "clear", "trigger": "all", "valueRules": null}], "name": "reorderQty", "rules": [], "type": "NUMBER"}, "type": "Widget"}], "key": "ERP_PLN$MRP_MAT_MD_VIEW-vkhGhYJidRtyXN_n5LRlk", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "库存计划"}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-GJQlKpikk2SkMtRh0bngU", "name": "FormField", "props": {"componentProps": {"fieldAlias": "consumptionMode", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "label": "消耗模式", "lookup": [{"action": "clear", "conditionGroup": {"conditions": [{"conditions": [{"id": "G6WI3g0mkt2BpzkngKWZE", "leftValue": {"fieldType": "Number", "scope": "form", "title": "创建人.逻辑删除标识", "type": "VarValue", "val": "createdBy.deleted", "value": "ERP_GEN$gen_mat_mrp_md.createdBy.deleted", "valueType": "VAR", "varVal": "createdBy.deleted", "varValue": [{"valueKey": "created<PERSON>y", "valueName": "created<PERSON>y"}, {"valueKey": "deleted", "valueName": "deleted"}]}, "operator": "EQ", "rightValue": {"constValue": "-1", "fieldType": "Number", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "Hs4inCOFXxv12t2M36tOb", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "6QapzOKmlcPnvrIhrVG64", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"hidden": false, "readOnly": true, "required": false}, "key": "cgRghpBmgq7OKSpkyJKoF", "operator": "clear", "trigger": "all", "valueRules": null}, {"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "SvyXu2xkHazOgJeh2BC_L", "leftValue": {"fieldType": "Number", "scope": "form", "title": "创建人.逻辑删除标识", "type": "VarValue", "val": "createdBy.deleted", "value": "ERP_GEN$gen_mat_mrp_md.createdBy.deleted", "valueType": "VAR", "varVal": "createdBy.deleted", "varValue": [{"valueKey": "created<PERSON>y", "valueName": "created<PERSON>y"}, {"valueKey": "deleted", "valueName": "deleted"}]}, "operator": "EQ", "rightValue": {"constValue": "-1", "fieldType": "Number", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "eZUNgQGb4SWeOlE3mitid", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "pw4o_hITWznrwu8Keb68m", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "required": false}, "key": "u_HWdzq5pM3aHokCUs6xA", "operator": null, "trigger": "all", "valueRules": null}], "name": "consumptionMode", "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-PJ_anRlTqcdrvkDxlcIr3", "name": "FormField", "props": {"componentProps": {"fieldAlias": "forwardConsumptionDays", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "label": "向前消耗天数", "lookup": [{"action": "clear", "conditionGroup": {"conditions": [{"conditions": [{"id": "TH0e9OviWVDehsozPVxC4", "leftValue": {"fieldType": "Number", "scope": "form", "title": "创建人.逻辑删除标识", "type": "VarValue", "val": "createdBy.deleted", "value": "ERP_GEN$gen_mat_mrp_md.createdBy.deleted", "valueType": "VAR", "varVal": "createdBy.deleted", "varValue": [{"valueKey": "created<PERSON>y", "valueName": "created<PERSON>y"}, {"valueKey": "deleted", "valueName": "deleted"}]}, "operator": "EQ", "rightValue": {"constValue": "-1", "fieldType": "Number", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "uh5gaxqB2Er6xAnSrdZx8", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "Yi9OmJ3vsgmNI4L_jWBaA", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"hidden": false, "readOnly": true, "required": false}, "key": "gEyHh1_TpaL806c3ulJLY", "operator": "clear", "trigger": "all", "valueRules": null}, {"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "SrwjeSiKLc3y1Meo2Xiwj", "leftValue": {"fieldType": "Number", "scope": "form", "title": "创建人.逻辑删除标识", "type": "VarValue", "val": "createdBy.deleted", "value": "ERP_GEN$gen_mat_mrp_md.createdBy.deleted", "valueType": "VAR", "varVal": "createdBy.deleted", "varValue": [{"valueKey": "created<PERSON>y", "valueName": "created<PERSON>y"}, {"valueKey": "deleted", "valueName": "deleted"}]}, "operator": "EQ", "rightValue": {"constValue": "1", "fieldType": "Number", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "OeW21la-m6gdz0ZcsVjyC", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "CgMueoZzKVyLgGWBuw4CC", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "required": false}, "key": "JC0wQu8Jh_eSTUxW9x1nJ", "operator": null, "trigger": "all", "valueRules": null}], "name": "forwardConsumptionDays", "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-RAoyVtgUQJv8yV0ZTdE3m", "name": "FormField", "props": {"componentProps": {"fieldAlias": "backwardConsumptionDays", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "label": "向后消耗天数", "lookup": [{"action": "clear", "conditionGroup": {"conditions": [{"conditions": [{"id": "c90ynn_zSIhqfegHIP_fG", "leftValue": {"fieldType": "Number", "scope": "form", "title": "创建人.逻辑删除标识", "type": "VarValue", "val": "createdBy.deleted", "value": "ERP_GEN$gen_mat_mrp_md.createdBy.deleted", "valueType": "VAR", "varVal": "createdBy.deleted", "varValue": [{"valueKey": "created<PERSON>y", "valueName": "created<PERSON>y"}, {"valueKey": "deleted", "valueName": "deleted"}]}, "operator": "EQ", "rightValue": {"constValue": "-1", "fieldType": "Number", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "iXHNPszS7pkVT2pNSaR6R", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "s9qlkA5fIRAfmHXsxLDZ9", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"hidden": false, "readOnly": true, "required": false}, "key": "ShBeioVe0OcW_SxrNvb7c", "operator": "clear", "trigger": "all", "valueRules": null}, {"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "xzvrBgBUe-DTBclXcHatd", "leftValue": {"fieldType": "Number", "scope": "form", "title": "创建人.逻辑删除标识", "type": "VarValue", "val": "createdBy.deleted", "value": "ERP_GEN$gen_mat_mrp_md.createdBy.deleted", "valueType": "VAR", "varVal": "createdBy.deleted", "varValue": [{"valueKey": "created<PERSON>y", "valueName": "created<PERSON>y"}, {"valueKey": "deleted", "valueName": "deleted"}]}, "operator": "EQ", "rightValue": {"constValue": "1", "fieldType": "Number", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "XdVDUPh3KR_jcW1iFx3yh", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "42ixlWbMP1ENWWoU1APhm", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "required": false}, "key": "rEWe6IenzQ9SnTrpfxfFd", "operator": null, "trigger": "all", "valueRules": null}], "name": "backwardConsumptionDays", "type": "NUMBER", "width": 120}, "type": "Widget"}], "key": "ERP_PLN$MRP_MAT_MD_VIEW-wqDJNw1zzuAD-bXnaEPlE", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "消耗控制"}, "type": "Layout"}], "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "params$": "{ id: route.recordId }", "serviceKey": "ERP_GEN$gen_mat_mrp_md_FIND_DATA_BY_ID_SERVICE_BzVyMx1", "test$": "!!route.recordId", "type": "InvokeService"}, {"containerKey": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "route?.query?.copyId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "params$": "{ id: route?.query?.copyId }", "serviceKey": "ERP_GEN$gen_mat_mrp_md_COPY_DATA_CONVERTER_SERVICE_BzVyMx2", "test$": "!!route.query?.copyId", "type": "InvokeService"}], "type": "Condition"}, "modelAlias": "ERP_GEN$gen_mat_mrp_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "type": "Container"}, {"children": [{"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-editView-footer-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "previous"}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$MRP_MAT_MD_VIEW-editView-footer-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_mrp_md"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "valueConfig": {"action": {"target": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md"}, "type": "action"}}], "service": "ERP_GEN$gen_mat_mrp_md_SAVE_DATA_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Message", "message": "保存成功！"}, {"action": "OpenView", "openViewConfig": {"page": {"key": "ERP_PLN$MRP_MAT_MD_VIEW:list", "name": "list", "type": "View"}, "refresh": true, "type": "NewPage"}}, {"action": "Close", "target": ["ROOT"]}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "showCondition": {}, "type": "primary"}, "type": "Widget"}], "key": "ERP_PLN$MRP_MAT_MD_VIEW-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showBack": true, "showFooter": true, "showHeader": true}, "type": "Container"}, "frontendConfig": {"modules": ["base"]}, "key": "ERP_PLN$MRP_MAT_MD_VIEW:edit", "resources": [{"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$gen_mat_mrp_md_FIND_DATA_BY_ID_SERVICE_BzVyMx1", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_mrp_md"}, "type": "Service"}, {"key": "ERP_GEN$gen_mat_mrp_md_COPY_DATA_CONVERTER_SERVICE_BzVyMx2", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_mrp_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-editView-footer-cancel", "label": "取消", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-editView-footer-save", "label": "保存", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}], "relations": [{"key": "ERP_GEN$gen_mat_mrp_md_SAVE_DATA_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-qCK-ssGr0gv9abWd2v5rf", "label": "物料编码", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$gen_mat_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "Service"}, {"key": "ERP_GEN$gen_mat_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "Service"}, {"key": "ERP_GEN$gen_mat_type_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "Service"}, {"key": "ERP_GEN$gen_uom_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "Service"}, {"key": "ERP_GEN$gen_uom_type_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "Service"}, {"key": "ERP_GEN$gen_mat_md_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-mrpAreaId", "label": "MRP区域", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$gen_mat_mrp_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_mrp_md"}, "type": "Service"}, {"key": "ERP_PLN$pln_mrp_area_info_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_mrp_area_info_md"}, "type": "Service"}, {"key": "ERP_PLN$pln_mrp_area_info_md_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_mrp_area_info_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-planningStrategyId", "label": "计划策略", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_PLN$pln_mrp_planning_strategy_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf"}, "type": "Service"}, {"key": "ERP_PLN$pln_mrp_planning_strategy_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-planningGroupCfId", "label": "MRP计划组", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_PLN$pln_mrp_planning_group_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf"}, "type": "Service"}, {"key": "ERP_PLN$pln_mrp_planning_group_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-planningCycle", "label": "计划周期", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-minSafetyStkQty", "label": "最小安全库存", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-servLevelPercent", "label": "服务水平", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-predictionFormula", "label": "预测公式", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-predictionIntervalStartDate", "label": "预测区间开始日期", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-predictionIntervalEndDate", "label": "预测区间结束日期", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-invMvmTypeName", "label": "参与预测移动凭证类型", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_SCM$inv_mvm_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$inv_mvm_type_cf"}, "type": "Service"}, {"key": "ERP_SCM$inv_bs_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$inv_bs_type_cf"}, "type": "Service"}, {"key": "ERP_SCM$inv_bs_type_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$inv_bs_type_cf"}, "type": "Service"}, {"key": "ERP_SCM$inv_fb_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$inv_fb_type_cf"}, "type": "Service"}, {"key": "ERP_SCM$inv_fb_type_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$inv_fb_type_cf"}, "type": "Service"}, {"key": "ERP_SCM$inv_inv_type_trans_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$inv_inv_type_trans_cf"}, "type": "Service"}, {"key": "ERP_SCM$inv_inv_type_trans_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$inv_inv_type_trans_cf"}, "type": "Service"}, {"key": "ERP_SCM$inv_mvm_ext_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$inv_mvm_ext_type_cf"}, "type": "Service"}, {"key": "ERP_SCM$inv_mvm_ext_type_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$inv_mvm_ext_type_cf"}, "type": "Service"}, {"key": "ERP_SCM$inv_mvm_type_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$inv_mvm_type_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-smoothingIndex", "label": "平滑系数", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-id", "label": "ID", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-createdBy", "label": "创建人", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}, "type": "Service"}, {"key": "ERP_GEN$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-updatedBy", "label": "更新人", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}, "type": "Service"}, {"key": "ERP_GEN$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-createdAt", "label": "创建时间", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-updatedAt", "label": "更新时间", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-version", "label": "版本号", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-deleted", "label": "逻辑删除标识", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-originOrgId", "label": "所属组织", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-m5ttBBUm_9Yl-yNzhMxZU", "label": "关键字段1，请勿删除", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_PLN$pln_mrp_planning_strategy_cf_FIND_ONE_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf"}, "type": "Service"}, {"key": "ERP_PLN$PLN_MRP_PLANNING_STRATEGY_CF_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-RYE8noxsl8wmoQC210-Cr", "label": "关键字段2，请勿删除", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md", "label": "MRP信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_PLN$strategy_consume_line_query", "name": null, "props": null, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-supplyType", "label": "供应方式", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-jKbwJfw9tTq4RiYwyYocQ", "label": "供应信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-defaultInvOrgId", "label": "默认库存组织", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-jKbwJfw9tTq4RiYwyYocQ", "label": "供应信息", "type": "FormGroupItem"}], "relations": [{"key": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}, {"key": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-defaultInvLocId", "label": "默认库存地点", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-jKbwJfw9tTq4RiYwyYocQ", "label": "供应信息", "type": "FormGroupItem"}], "relations": [{"key": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}, {"key": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-plannedDelivTime", "label": "计划交货天数", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-JcwQe3PDLxFd6S3re0kDN", "label": "计划时间", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-purProcessDays", "label": "采购处理天数", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-JcwQe3PDLxFd6S3re0kDN", "label": "计划时间", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-planWcCfId", "label": "计划日历", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-JcwQe3PDLxFd6S3re0kDN", "label": "计划时间", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$gen_wc_head_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_wc_head_cf"}, "type": "Service"}, {"key": "ERP_GEN$gen_wc_head_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_wc_head_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-Ss5zbpQLroWG_SGX4unnJ", "label": "期间批量天数", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-siINAs5sg1mG4h6ITE1B6", "label": "批量控制", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-1SpTcBBvND1eTj7p-3L6R", "label": "期间日期类型", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-siINAs5sg1mG4h6ITE1B6", "label": "批量控制", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-_LWHK58kcLnOUc30dtdEq", "label": "固定批量", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-siINAs5sg1mG4h6ITE1B6", "label": "批量控制", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-JKvI_dv2PXWTOQDqhuDvf", "label": "最大批量", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-siINAs5sg1mG4h6ITE1B6", "label": "批量控制", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-_VmGRwDkquU0g4Xc1m_0O", "label": "最小批量", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-siINAs5sg1mG4h6ITE1B6", "label": "批量控制", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-4PhgkWyB1lS15TY_KnBxJ", "label": "最小包装量", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-siINAs5sg1mG4h6ITE1B6", "label": "批量控制", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-_ebnEIbfDSg3tQNjQql1j", "label": "拆分间隔天数", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-siINAs5sg1mG4h6ITE1B6", "label": "批量控制", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-lotSizeTypeCfId", "label": "拆分间隔类型", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-siINAs5sg1mG4h6ITE1B6", "label": "批量控制", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-safetyStkQty", "label": "安全库存", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-vkhGhYJidRtyXN_n5LRlk", "label": "库存计划", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-container-ERP_GEN$gen_mat_mrp_md-for-widget-reorderQty", "label": "再订货点", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-vkhGhYJidRtyXN_n5LRlk", "label": "库存计划", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-GJQlKpikk2SkMtRh0bngU", "label": "消耗模式", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-wqDJNw1zzuAD-bXnaEPlE", "label": "消耗控制", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-PJ_anRlTqcdrvkDxlcIr3", "label": "向前消耗天数", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-wqDJNw1zzuAD-bXnaEPlE", "label": "消耗控制", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$MRP_MAT_MD_VIEW-RAoyVtgUQJv8yV0ZTdE3m", "label": "向后消耗天数", "path": [{"key": "ERP_PLN$MRP_MAT_MD_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-total-config-ERP_GEN$gen_mat_mrp_md", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$MRP_MAT_MD_VIEW-wqDJNw1zzuAD-bXnaEPlE", "label": "消耗控制", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "title": "edit", "type": "FORM"}}