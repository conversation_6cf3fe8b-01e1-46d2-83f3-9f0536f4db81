{"type": "ServiceDefinition", "name": "发送通知", "access": "Private", "parentKey": "TSRM", "props": {"modelKey": "null", "isDeleted": null, "isEnabled": true, "eventProps": null, "permissions": null, "serviceName": null, "serviceType": "PROGRAMMABLE", "serviceDslMd5": "null", "serviceDslJson": {"id": null, "key": "TSRM$ORG_EMPLOYEE_MD_ENABLE", "desc": null, "name": "ORG_员工管理_启用", "input": [{"id": null, "fieldKey": "id", "required": null, "fieldName": "id", "fieldType": "Number", "fieldAlias": "id", "description": null, "defaultValue": null}], "props": {"type": "ServiceProperties", "aiService": null, "aiChatMode": null, "fieldRules": null, "schedulerJob": null, "stateMachine": null, "permissionKey": null, "aiRoundsStrategy": null, "transactionPropagation": "REQUIRED"}, "output": [{"id": null, "fieldKey": "response", "modelKey": "TSRM$user", "relation": null, "required": null, "fieldName": "response", "fieldType": "Model", "fieldAlias": "response", "description": null, "defaultValue": null, "relatedModel": {"modelKey": "TSRM$user", "modelName": "TSRM$user", "modelAlias": "TSRM$user"}}], "children": [{"id": null, "key": "node_1hb5l7tjm74", "desc": null, "name": "开始", "type": "StartNode", "props": {"type": "StartProperties", "input": [{"id": null, "fieldKey": "id", "required": null, "fieldName": "id", "fieldType": "Number", "fieldAlias": "id", "description": null, "defaultValue": null}], "output": [], "globalVariable": [{"id": null, "fieldKey": "orgEmployee", "modelKey": "sys_common$org_employee_md", "relation": null, "required": null, "fieldName": "orgEmployee", "fieldType": "Model", "fieldAlias": "orgEmployee", "description": null, "defaultValue": null, "relatedModel": {"modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md", "modelAlias": "sys_common$org_employee_md"}}, {"id": null, "fieldKey": "user", "modelKey": "TSRM$user", "relation": null, "required": null, "fieldName": "user", "fieldType": "Model", "fieldAlias": "user", "description": null, "defaultValue": null, "relatedModel": {"modelKey": "TSRM$user", "modelName": "TSRM$user", "modelAlias": "TSRM$user"}}]}, "nextNodeKey": "node_1hb5lq36n83"}, {"id": null, "key": "node_1hb5lq36n83", "desc": null, "name": "排他分支", "type": "ExclusiveBranchNode", "props": {"type": "ExclusiveBranchProperties"}, "children": [{"id": null, "key": "node_1hb5lq36n84", "desc": null, "name": "条件", "type": "ConditionNode", "props": {"type": "ConditionProperties", "conditionGroup": {"id": null, "type": "ConditionGroup", "conditions": [{"id": null, "type": "ConditionGroup", "conditions": [{"id": "wuYu2VGKkgLi04whz1Wsl", "key": "wuYu2VGKkgLi04whz1Wsl", "type": "ConditionLeaf", "operator": "IS_NOT_NULL", "leftValue": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "REQUEST", "fieldType": null, "valueName": "服务入参"}, {"valueKey": "id", "fieldType": null, "valueName": "id"}], "fieldType": "Number", "valueType": "VAR"}, "rightValue": null, "rightValues": null}], "logicOperator": "AND"}], "logicOperator": "OR"}}, "children": null, "nextNodeKey": "node_1hb5ljggm76"}, {"id": null, "key": "node_1hb5ljggm76", "desc": null, "name": "查询数据", "type": "RetrieveDataNode", "props": {"type": "RetrieveDataProperties", "maximum": null, "dataType": "MODEL", "pageable": null, "sortOrders": null, "desensitized": true, "outputAssign": {"outputAssignType": "CUSTOM", "customAssignments": [{"id": "1hb832leq8", "field": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "GLOBAL", "fieldType": null, "valueName": "全局变量"}, {"valueKey": "orgEmployee", "fieldType": null, "valueName": "orgEmployee", "relatedModel": {"modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md", "modelAlias": "sys_common$org_employee_md"}}], "fieldType": "Model", "valueType": "VAR"}, "value": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "NODE_OUTPUT_node_1hb5ljggm76", "fieldType": null, "valueName": "出参结构体", "relatedModel": {"modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t", "modelAlias": "sys_common$org_employee_md"}}], "fieldType": "Model", "valueType": "MODEL"}, "operator": "EQ"}, {"id": "1hbct2nbp15", "field": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "GLOBAL", "fieldType": null, "valueName": "全局变量"}, {"valueKey": "TSRM$user", "fieldType": null, "valueName": "user", "relatedModel": {"modelKey": "TSRM$user", "modelName": "TSRM$user", "modelAlias": "TSRM$user"}}, {"valueKey": "nickname", "fieldType": null, "valueName": "昵称", "modelAlias": "TSRM$user", "relatedModel": {"modelKey": null, "modelName": null, "modelAlias": null}}], "fieldType": "Text", "valueType": "VAR"}, "value": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "NODE_OUTPUT_node_1hb5ljggm76", "fieldType": null, "valueName": "出参结构体", "relatedModel": {"modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t", "modelAlias": "sys_common$org_employee_md"}}, {"valueKey": "name", "fieldType": null, "valueName": "姓名", "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelKey": null, "modelName": null, "modelAlias": null}}], "fieldType": "Text", "valueType": "MODEL"}, "operator": "EQ"}, {"id": "1hbct2nv516", "field": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "GLOBAL", "fieldType": null, "valueName": "全局变量"}, {"valueKey": "TSRM$user", "fieldType": null, "valueName": "user", "relatedModel": {"modelKey": "TSRM$user", "modelName": "TSRM$user", "modelAlias": "TSRM$user"}}, {"valueKey": "username", "fieldType": null, "valueName": "用户名", "modelAlias": "TSRM$user", "relatedModel": {"modelKey": null, "modelName": null, "modelAlias": null}}], "fieldType": "Text", "valueType": "VAR"}, "value": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "NODE_OUTPUT_node_1hb5ljggm76", "fieldType": null, "valueName": "出参结构体", "relatedModel": {"modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t", "modelAlias": "sys_common$org_employee_md"}}, {"valueKey": "code", "fieldType": null, "valueName": "员工编码", "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelKey": null, "modelName": null, "modelAlias": null}}], "fieldType": "Text", "valueType": "MODEL"}, "operator": "EQ"}, {"id": "1hbct2ogn17", "field": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "GLOBAL", "fieldType": null, "valueName": "全局变量"}, {"valueKey": "TSRM$user", "fieldType": null, "valueName": "user", "relatedModel": {"modelKey": "TSRM$user", "modelName": "TSRM$user", "modelAlias": "TSRM$user"}}, {"valueKey": "email", "fieldType": null, "valueName": "用户邮箱", "modelAlias": "TSRM$user", "relatedModel": {"modelKey": null, "modelName": null, "modelAlias": null}}], "fieldType": "Text", "valueType": "VAR"}, "value": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "NODE_OUTPUT_node_1hb5ljggm76", "fieldType": null, "valueName": "出参结构体", "relatedModel": {"modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t", "modelAlias": "sys_common$org_employee_md"}}, {"valueKey": "email", "fieldType": null, "valueName": "邮箱", "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelKey": null, "modelName": null, "modelAlias": null}}], "fieldType": "Text", "valueType": "MODEL"}, "operator": "EQ"}, {"id": "1hbct2p0518", "field": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "GLOBAL", "fieldType": null, "valueName": "全局变量"}, {"valueKey": "TSRM$user", "fieldType": null, "valueName": "user", "relatedModel": {"modelKey": "TSRM$user", "modelName": "TSRM$user", "modelAlias": "TSRM$user"}}, {"valueKey": "mobile", "fieldType": null, "valueName": "用户手机", "modelAlias": "TSRM$user", "relatedModel": {"modelKey": null, "modelName": null, "modelAlias": null}}], "fieldType": "Text", "valueType": "VAR"}, "value": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "NODE_OUTPUT_node_1hb5ljggm76", "fieldType": null, "valueName": "出参结构体", "relatedModel": {"modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t", "modelAlias": "sys_common$org_employee_md"}}, {"valueKey": "mobile", "fieldType": null, "valueName": "手机", "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelKey": null, "modelName": null, "modelAlias": null}}], "fieldType": "Text", "valueType": "MODEL"}, "operator": "EQ"}, {"id": "1hbcu3c0627", "field": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "GLOBAL", "fieldType": null, "valueName": "全局变量"}, {"valueKey": "TSRM$user", "fieldType": null, "valueName": "user", "relatedModel": {"modelKey": "TSRM$user", "modelName": "TSRM$user", "modelAlias": "TSRM$user"}}, {"valueKey": "id", "fieldType": null, "valueName": "ID", "modelAlias": "TSRM$user", "relatedModel": {"modelKey": null, "modelName": null, "modelAlias": null}}], "fieldType": "Number", "valueType": "VAR"}, "value": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "NODE_OUTPUT_node_1hb5ljggm76", "fieldType": null, "valueName": "出参结构体", "relatedModel": {"modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t", "modelAlias": "sys_common$org_employee_md"}}, {"valueKey": "userId", "fieldType": null, "valueName": "用户", "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelKey": "TSRM$user", "modelName": "TERP_MIGRATE$user", "modelAlias": "TSRM$user"}}, {"valueKey": "id", "fieldType": null, "valueName": "ID", "modelAlias": "TSRM$user", "relatedModel": {"modelKey": null, "modelName": null, "modelAlias": null}}], "fieldType": "Number", "valueType": "MODEL"}, "operator": "EQ"}]}, "relatedModel": {"modelKey": "sys_common$org_employee_md", "modelName": "员工信息表\t", "modelAlias": "sys_common$org_employee_md"}, "conditionGroup": {"id": null, "type": "ConditionGroup", "conditions": [{"id": null, "type": "ConditionGroup", "conditions": [{"id": "eOjyIHnshWPMzb7KCNad7", "key": "eOjyIHnshWPMzb7KCNad7", "type": "ConditionLeaf", "operator": "EQ", "leftValue": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "id", "fieldType": null, "valueName": "ID", "modelAlias": "sys_common$org_employee_md"}], "fieldType": "Number", "valueType": "MODEL"}, "rightValue": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "REQUEST", "fieldType": null, "valueName": "服务入参"}, {"valueKey": "id", "fieldType": null, "valueName": "id"}], "fieldType": "Number", "valueType": "VAR"}, "rightValues": null}], "logicOperator": "AND"}], "logicOperator": "OR"}, "queryModelFields": {"modelKey": "sys_common$org_employee_md", "allFields": true, "queryFields": null}, "stopWhenDataEmpty": true, "subQueryRelatedModels": null, "dataConditionPermissionKey": null}, "nextNodeKey": "node_1hbctpb9821"}, {"id": null, "key": "node_1hbctpb9821", "desc": null, "name": "排他分支", "type": "ExclusiveBranchNode", "props": {"type": "ExclusiveBranchProperties"}, "children": [{"id": null, "key": "node_1hbctpb9822", "desc": null, "name": "新建用户", "type": "ConditionNode", "props": {"type": "ConditionProperties", "conditionGroup": {"id": null, "type": "ConditionGroup", "conditions": [{"id": null, "type": "ConditionGroup", "conditions": [{"id": "GeCCMp1lGPhE8bR735_hj", "key": "GeCCMp1lGPhE8bR735_hj", "type": "ConditionLeaf", "operator": "IS_NULL", "leftValue": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "GLOBAL", "fieldType": null, "valueName": "全局变量"}, {"valueKey": "TSRM$user", "fieldType": null, "valueName": "user", "relatedModel": {"modelKey": "TSRM$user", "modelName": "TSRM$user", "modelAlias": "TSRM$user"}}, {"valueKey": "id", "fieldType": null, "valueName": "ID", "modelAlias": "TSRM$user", "relatedModel": {"modelKey": null, "modelName": null, "modelAlias": null}}], "fieldType": "Number", "valueType": "VAR"}, "rightValue": null, "rightValues": null}], "logicOperator": "AND"}], "logicOperator": "OR"}}, "children": null, "nextNodeKey": "node_1hbctotll20"}, {"id": null, "key": "node_1hbctotll20", "desc": null, "name": "调用编排服务", "type": "CallServiceNode", "props": {"type": "CallServiceProperties", "output": [{"id": null, "fieldKey": "data", "modelKey": "TSRM$user", "relation": null, "required": null, "fieldName": "data", "fieldType": "Model", "fieldAlias": "data", "description": null, "defaultValue": null, "relatedModel": {"modelKey": "TSRM$user", "modelName": "COMMON_2B$user", "modelAlias": "TSRM$user"}}], "serviceKey": "TSRM$ORG_USER_ADD_SERVICE", "serviceName": "ORG_添加用户服务", "inputMapping": [{"id": "1hbctrnpa24", "field": {"id": null, "fieldKey": "request", "modelKey": "TSRM$user", "relation": null, "required": true, "fieldName": "request", "fieldType": "Model", "fieldAlias": "request", "description": null, "defaultValue": null, "relatedModel": {"modelKey": "TSRM$user", "modelName": "COMMON_2B$user", "modelAlias": "TSRM$user"}}, "value": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "GLOBAL", "fieldType": null, "valueName": "全局变量"}, {"valueKey": "TSRM$user", "fieldType": null, "valueName": "user", "relatedModel": {"modelKey": "TSRM$user", "modelName": "TSRM$user", "modelAlias": "TSRM$user"}}], "fieldType": "Model", "valueType": "VAR"}}], "outputAssign": {"outputAssignType": "CUSTOM", "customAssignments": [{"id": "1hbctv81325", "field": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "GLOBAL", "fieldType": null, "valueName": "全局变量"}, {"valueKey": "TSRM$user", "fieldType": null, "valueName": "user", "relatedModel": {"modelKey": "TSRM$user", "modelName": "TSRM$user", "modelAlias": "TSRM$user"}}], "fieldType": "Model", "valueType": "VAR"}, "value": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "NODE_OUTPUT_node_1hbctotll20", "fieldType": null, "valueName": "出参结构体"}, {"valueKey": "data", "fieldType": null, "valueName": "data", "relatedModel": {"modelKey": "TSRM$user", "modelName": "COMMON_2B$user", "modelAlias": "TSRM$user"}}], "fieldType": "Model", "valueType": "MODEL"}, "operator": "EQ"}, {"id": "1hbcu044d26", "field": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "GLOBAL", "fieldType": null, "valueName": "全局变量"}, {"valueKey": "orgEmployee", "fieldType": null, "valueName": "orgEmployee", "relatedModel": {"modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md", "modelAlias": "sys_common$org_employee_md"}}, {"valueKey": "userId", "fieldType": null, "valueName": "用户", "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelKey": "TSRM$user", "modelName": "TERP_MIGRATE$user", "modelAlias": "TSRM$user"}}], "fieldType": "Model", "valueType": "VAR"}, "value": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "NODE_OUTPUT_node_1hbctotll20", "fieldType": null, "valueName": "出参结构体"}, {"valueKey": "data", "fieldType": null, "valueName": "data", "relatedModel": {"modelKey": "TSRM$user", "modelName": "COMMON_2B$user", "modelAlias": "TSRM$user"}}], "fieldType": "Model", "valueType": "MODEL"}, "operator": "EQ"}]}, "transactionPropagation": "NOT_SUPPORTED"}, "nextNodeKey": "node_1hctl5s171"}, {"id": null, "key": "node_1hctl5s171", "desc": null, "name": "通知", "type": "NoticeNode", "props": {"type": "NoticeProperties", "inputMapping": [{"id": null, "key": "password", "value": {"id": null, "type": "VarValue", "varValue": null, "fieldType": "Text", "valueType": "CONST", "constValue": "TERPsrm@2023"}, "required": null}, {"id": null, "key": "password", "value": {"id": null, "type": "VarValue", "varValue": null, "fieldType": "Text", "valueType": "CONST", "constValue": "TERPsrm@2023"}, "required": null}], "receiverUsers": [], "receiverEmails": [{"id": null, "type": "VarValue", "varValue": [{"valueKey": "GLOBAL", "fieldType": null, "valueName": "全局变量"}, {"valueKey": "orgEmployee", "fieldType": null, "valueName": "orgEmployee", "relatedModel": {"modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md", "modelAlias": "sys_common$org_employee_md"}}, {"valueKey": "email", "fieldType": null, "valueName": "邮箱", "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelKey": null, "modelName": null, "modelAlias": null}}], "fieldType": "Text", "valueType": "VAR"}], "noticeSceneCode": "COMM_2B_EMPLOYEE_SENDPASSWORD", "noticeSceneName": "员工管理-启用-默认密码发送", "receiverMobiles": [{"id": null, "type": "VarValue", "varValue": [{"valueKey": "GLOBAL", "fieldType": null, "valueName": "全局变量"}, {"valueKey": "orgEmployee", "fieldType": null, "valueName": "orgEmployee", "relatedModel": {"modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md", "modelAlias": "sys_common$org_employee_md"}}, {"valueKey": "mobile", "fieldType": null, "valueName": "手机", "modelAlias": "sys_common$org_employee_md", "relatedModel": {"modelKey": null, "modelName": null, "modelAlias": null}}], "fieldType": "Text", "valueType": "VAR"}], "receiverUserIds": [], "placeholderMapping": {"NOTICE_TEMPLATE16554810604965888": [{"id": null, "key": "password", "value": {"id": null, "type": "VarValue", "varValue": null, "fieldType": "Text", "valueType": "CONST", "constValue": "TERPsrm@2023"}, "required": null}], "NOTICE_TEMPLATE16555095968632832": [{"id": null, "key": "password", "value": {"id": null, "type": "VarValue", "varValue": null, "fieldType": "Text", "valueType": "CONST", "constValue": "TERPsrm@2023"}, "required": null}]}, "receiverStationLetterIds": null, "sendAfterTransactionCommitted": false}}, {"id": null, "key": "node_1hbctpb9823", "desc": null, "name": "更新用户", "type": "ConditionNode", "props": {"type": "ConditionProperties", "conditionGroup": {"id": null, "type": "ConditionGroup", "conditions": [], "logicOperator": "OR"}}, "children": null, "nextNodeKey": "node_1hbd85jkd28"}, {"id": null, "key": "node_1hbd85jkd28", "desc": null, "name": "调用编排服务", "type": "CallServiceNode", "props": {"type": "CallServiceProperties", "output": [{"id": null, "elements": null, "fieldKey": "data", "required": null, "fieldName": "data", "fieldType": "Object", "fieldAlias": "data", "description": null, "defaultValue": null}], "serviceKey": "TSRM$ORG_USER_UPDATE_SERVICE", "serviceName": "ORG_更新用户服务", "inputMapping": [{"id": "1hbd862og29", "field": {"id": null, "fieldKey": "request", "modelKey": "TSRM$user", "relation": null, "required": true, "fieldName": "request", "fieldType": "Model", "fieldAlias": "request", "description": null, "defaultValue": null, "relatedModel": {"modelKey": "TSRM$user", "modelName": "COMMON_2B$user", "modelAlias": "TSRM$user"}}, "value": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "GLOBAL", "fieldType": null, "valueName": "全局变量"}, {"valueKey": "TSRM$user", "fieldType": null, "valueName": "user", "relatedModel": {"modelKey": "TSRM$user", "modelName": "TSRM$user", "modelAlias": "TSRM$user"}}], "fieldType": "Model", "valueType": "VAR"}}], "outputAssign": {"outputAssignType": "SYSTEM", "customAssignments": null}, "transactionPropagation": null}}], "nextNodeKey": "node_1hb87t8dm13", "headNodeKeys": ["node_1hbctpb9822", "node_1hbctpb9823"]}, {"id": null, "key": "node_1hb87t8dm13", "desc": null, "name": "调用编排服务", "type": "CallServiceNode", "props": {"type": "CallServiceProperties", "output": [{"id": null, "fieldKey": "data", "modelKey": "sys_common$org_employee_md", "relation": null, "required": null, "fieldName": "data", "fieldType": "Model", "fieldAlias": "data", "description": null, "defaultValue": null, "relatedModel": {"modelKey": "sys_common$org_employee_md", "modelName": "TERP_MIGRATE$org_employee_md", "modelAlias": "sys_common$org_employee_md"}}], "serviceKey": "TSRM$ORG_EMPLOYEE_ENABLE_SERVICE", "serviceName": "ORG_员工管理_启用服务", "inputMapping": [{"id": "1hb87tut614", "field": {"id": null, "fieldKey": "request", "modelKey": "sys_common$org_employee_md", "relation": null, "required": true, "fieldName": "request", "fieldType": "Model", "fieldAlias": "request", "description": null, "defaultValue": null, "relatedModel": {"modelKey": "sys_common$org_employee_md", "modelName": "TERP_MIGRATE$org_employee_md", "modelAlias": "sys_common$org_employee_md"}}, "value": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "GLOBAL", "fieldType": null, "valueName": "全局变量"}, {"valueKey": "orgEmployee", "fieldType": null, "valueName": "orgEmployee", "relatedModel": {"modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md", "modelAlias": "sys_common$org_employee_md"}}], "fieldType": "Model", "valueType": "VAR"}}], "outputAssign": {"outputAssignType": "CUSTOM", "customAssignments": [{"id": "1hb87uk2415", "field": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "GLOBAL", "fieldType": null, "valueName": "全局变量"}, {"valueKey": "orgEmployee", "fieldType": null, "valueName": "orgEmployee", "relatedModel": {"modelKey": "sys_common$org_employee_md", "modelName": "sys_common$org_employee_md", "modelAlias": "sys_common$org_employee_md"}}], "fieldType": "Model", "valueType": "VAR"}, "value": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "NODE_OUTPUT_node_1hb87t8dm13", "fieldType": null, "valueName": "出参结构体"}, {"valueKey": "data", "fieldType": null, "valueName": "data", "relatedModel": {"modelKey": "sys_common$org_employee_md", "modelName": "TERP_MIGRATE$org_employee_md", "modelAlias": "sys_common$org_employee_md"}}], "fieldType": "Model", "valueType": "MODEL"}, "operator": "EQ"}]}, "transactionPropagation": null}}, {"id": null, "key": "node_1hb5lq36n85", "desc": null, "name": "条件", "type": "ConditionNode", "props": {"type": "ConditionProperties", "conditionGroup": {"id": null, "type": "ConditionGroup", "conditions": [{"id": null, "type": "ConditionGroup", "conditions": [{"id": "op_q5oG8B8IY3MzqLy605", "key": "op_q5oG8B8IY3MzqLy605", "type": "ConditionLeaf", "operator": "IS_NULL", "leftValue": {"id": null, "type": "VarValue", "varValue": [{"valueKey": "REQUEST", "fieldType": null, "valueName": "服务入参"}, {"valueKey": "id", "fieldType": null, "valueName": "id"}], "fieldType": "Number", "valueType": "VAR"}, "rightValue": null, "rightValues": null}], "logicOperator": "AND"}], "logicOperator": "OR"}}, "children": null}], "nextNodeKey": "node_1hb5l7tjm75", "headNodeKeys": ["node_1hb5lq36n84", "node_1hb5lq36n85"]}, {"id": null, "key": "node_1hb5l7tjm75", "desc": null, "name": "结束", "type": "EndNode", "props": {"type": "EndProperties"}}], "headNodeKeys": ["node_1hb5l7tjm74"]}}}