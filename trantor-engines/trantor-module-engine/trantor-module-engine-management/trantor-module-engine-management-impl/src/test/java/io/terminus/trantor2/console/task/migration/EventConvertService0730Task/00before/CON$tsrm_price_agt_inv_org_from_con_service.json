{"parentKey": "CON", "name": "合同适用采购组织转价格协议适用采购组织", "description": "合同适用采购组织转价格协议适用采购组织", "type": "ServiceDefinition", "key": "CON$tsrm_price_agt_inv_org_from_con_service", "props": {"serviceDslJson": {"output": [{"fieldAlias": "purOrg", "fieldName": "purOrg", "fieldKey": "purOrg", "fieldType": "Array", "element": {"fieldAlias": "element", "fieldName": "element", "fieldKey": "element", "fieldType": "Model", "relatedModel": {"modelAlias": "CON$tsrm_price_pur_org_dto", "modelKey": "CON$tsrm_price_pur_org_dto"}}}], "input": [{"fieldAlias": "request", "fieldName": "request", "fieldKey": "request", "fieldType": "Number", "required": false}], "children": [{"nextNodeKey": "node_1hkgfrer175", "name": "开始", "type": "StartNode", "key": "node_1hkgfko7p73", "props": {"output": [{"fieldAlias": "purOrg", "fieldName": "purOrg", "fieldKey": "purOrg", "fieldType": "Array", "element": {"fieldAlias": "element", "fieldName": "element", "fieldKey": "element", "fieldType": "Model", "relatedModel": {"modelAlias": "CON$tsrm_price_pur_org_dto", "modelKey": "CON$tsrm_price_pur_org_dto"}}}], "input": [{"fieldAlias": "request", "fieldName": "request", "fieldKey": "request", "fieldType": "Number", "required": false}], "globalVariable": [{"fieldAlias": "purOrg<PERSON><PERSON>p", "fieldName": "purOrg<PERSON><PERSON>p", "fieldKey": "purOrg<PERSON><PERSON>p", "fieldType": "Model", "relatedModel": {"modelAlias": "CON$tsrm_price_pur_org_dto", "modelKey": "CON$tsrm_price_pur_org_dto"}}], "type": "StartProperties"}}, {"nextNodeKey": "node_1hkghquqh81", "name": "查询数据", "type": "RetrieveDataNode", "key": "node_1hkgfrer175", "props": {"stopWhenDataEmpty": false, "outputAssign": {"outputAssignType": "SYSTEM"}, "conditionGroup": {"id": "VnDoxTpYoLRNPFwlKMh8-", "conditions": [{"id": "faVrZDHpJvob9dvLouqia", "conditions": [{"leftValue": {"varValue": [{"valueName": "conHeadTrId", "modelAlias": "CON$con_pur_org_link_cf", "valueKey": "conHeadTrId"}, {"valueName": "ID", "modelAlias": "CON$con_head_tr", "valueKey": "id"}], "valueType": "MODEL", "type": "VarValue", "fieldType": "Number"}, "rightValue": {"varValue": [{"valueName": "服务入参", "valueKey": "REQUEST"}, {"valueName": "request", "valueKey": "request"}], "valueType": "VAR", "type": "VarValue", "fieldType": "Number"}, "id": "nhjOavRh2adg8tcSicvY-", "type": "ConditionLeaf", "operator": "EQ"}], "type": "ConditionGroup", "logicOperator": "AND"}], "type": "ConditionGroup", "logicOperator": "OR"}, "dataType": "ARRAY", "type": "RetrieveDataProperties", "relatedModel": {"modelName": "合同适用的采购组织", "modelAlias": "CON$con_pur_org_link_cf", "modelKey": "CON$con_pur_org_link_cf"}, "subQueryRelatedModels": [{"subQueryRelatedModels": [], "fieldAlias": "purcharseOrg", "modelAlias": "sys_common$org_struct_md"}], "maximum": 1000}}, {"nextNodeKey": "node_1hkgfko7p74", "children": [{"name": "赋值", "type": "AssignNode", "key": "node_1hkghuipn82", "props": {"assignments": [{"field": {"varValue": [{"valueName": "全局变量", "valueKey": "GLOBAL"}, {"valueName": "purOrg<PERSON><PERSON>p", "valueKey": "purOrg<PERSON><PERSON>p", "relatedModel": {"modelAlias": "CON$tsrm_price_pur_org_dto", "modelKey": "CON$tsrm_price_pur_org_dto"}}, {"valueName": "采购组织编码", "modelAlias": "CON$tsrm_price_pur_org_dto", "valueKey": "code"}], "valueType": "VAR", "type": "VarValue", "fieldType": "Text"}, "id": "1hkghul9l83", "value": {"varValue": [{"valueName": "[循环]循环变量", "valueKey": "LOOP_node_1hkghquqh81"}, {"valueName": "循环元素", "valueKey": "_item", "relatedModel": {"modelName": "合同适用的采购组织", "modelAlias": "CON$con_pur_org_link_cf", "modelKey": "CON$con_pur_org_link_cf"}}, {"valueName": "采购组织", "modelAlias": "CON$con_pur_org_link_cf", "valueKey": "purcharseOrg", "relatedModel": {"modelName": "sys_common$org_struct_md", "modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md"}}, {"valueName": "组织编码", "modelAlias": "sys_common$org_struct_md", "valueKey": "orgCode", "relatedModel": {}}], "valueType": "VAR", "type": "VarValue", "fieldType": "Text"}, "operator": "EQ"}, {"field": {"varValue": [{"valueName": "全局变量", "valueKey": "GLOBAL"}, {"valueName": "purOrg<PERSON><PERSON>p", "valueKey": "purOrg<PERSON><PERSON>p", "relatedModel": {"modelAlias": "CON$tsrm_price_pur_org_dto", "modelKey": "CON$tsrm_price_pur_org_dto"}}, {"valueName": "采购组织名称", "modelAlias": "CON$tsrm_price_pur_org_dto", "valueKey": "name"}], "valueType": "VAR", "type": "VarValue", "fieldType": "Text"}, "id": "1hkgi00as84", "value": {"varValue": [{"valueName": "[循环]循环变量", "valueKey": "LOOP_node_1hkghquqh81"}, {"valueName": "循环元素", "valueKey": "_item", "relatedModel": {"modelName": "合同适用的采购组织", "modelAlias": "CON$con_pur_org_link_cf", "modelKey": "CON$con_pur_org_link_cf"}}, {"valueName": "采购组织", "modelAlias": "CON$con_pur_org_link_cf", "valueKey": "purcharseOrg", "relatedModel": {"modelName": "sys_common$org_struct_md", "modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md"}}, {"valueName": "组织名称", "modelAlias": "sys_common$org_struct_md", "valueKey": "orgName", "relatedModel": {}}], "valueType": "VAR", "type": "VarValue", "fieldType": "Text"}, "operator": "EQ"}, {"field": {"varValue": [{"valueName": "全局变量", "valueKey": "GLOBAL"}, {"valueName": "purOrg<PERSON><PERSON>p", "valueKey": "purOrg<PERSON><PERSON>p", "relatedModel": {"modelAlias": "CON$tsrm_price_pur_org_dto", "modelKey": "CON$tsrm_price_pur_org_dto"}}, {"valueName": "id", "modelAlias": "CON$tsrm_price_pur_org_dto", "valueKey": "id"}], "valueType": "VAR", "type": "VarValue", "fieldType": "Number"}, "id": "1hkgi0hs985", "value": {"varValue": [{"valueName": "循环变量[循环]", "valueKey": "LOOP_node_1hkghquqh81"}, {"valueName": "循环元素", "valueKey": "_item", "relatedModel": {"modelName": "合同适用的采购组织", "modelAlias": "CON$con_pur_org_link_cf", "modelKey": "CON$con_pur_org_link_cf"}}, {"valueName": "采购组织", "modelAlias": "CON$con_pur_org_link_cf", "valueKey": "purcharseOrg", "relatedModel": {"modelName": "TERP_MIGRATE$org_pur_org_cf", "modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md"}}, {"valueName": "ID", "modelAlias": "sys_common$org_struct_md", "valueKey": "id", "relatedModel": {}}], "valueType": "VAR", "type": "VarValue", "fieldType": "Number"}, "operator": "EQ"}, {"field": {"varValue": [{"valueName": "服务出参", "valueKey": "OUTPUT"}, {"valueName": "purOrg", "valueKey": "purOrg"}], "valueType": "VAR", "type": "VarValue", "fieldType": "Array"}, "id": "1hkgi17i386", "value": {"varValue": [{"valueName": "全局变量", "valueKey": "GLOBAL"}, {"valueName": "purOrg<PERSON><PERSON>p", "valueKey": "purOrg<PERSON><PERSON>p", "relatedModel": {"modelAlias": "CON$tsrm_price_pur_org_dto", "modelKey": "CON$tsrm_price_pur_org_dto"}}], "valueType": "VAR", "type": "VarValue", "fieldType": "Model"}, "operator": "ADD"}, {"field": {"varValue": [{"valueName": "全局变量", "valueKey": "GLOBAL"}, {"valueName": "purOrg<PERSON><PERSON>p", "valueKey": "purOrg<PERSON><PERSON>p", "relatedModel": {"modelAlias": "CON$tsrm_price_pur_org_dto", "modelKey": "CON$tsrm_price_pur_org_dto"}}], "valueType": "VAR", "type": "VarValue", "fieldType": "Model"}, "id": "1hkgi1pmo87", "value": {"type": "FuncValue", "funcExpression": "NULL()"}, "operator": "EQ"}], "type": "AssignProperties"}}], "name": "循环", "type": "LoopNode", "headNodeKeys": ["node_1hkghuipn82"], "key": "node_1hkghquqh81", "props": {"loopType": "DATASET_LOOP", "stopWhenDataEmpty": false, "loopElement": {"fieldAlias": "NODE_OUTPUT_node_1hkgfrer175", "fieldName": "[查询数据]节点.output", "fieldKey": "NODE_OUTPUT_node_1hkgfrer175", "fieldType": "Model", "relatedModel": {"modelName": "合同适用的采购组织", "modelAlias": "CON$con_pur_org_link_cf", "modelKey": "CON$con_pur_org_link_cf"}}, "loopData": {"varValue": [{"valueName": "全局变量", "valueKey": "GLOBAL"}, {"valueName": "[查询数据]节点.output", "valueKey": "NODE_OUTPUT_node_1hkgfrer175", "relatedModel": {"modelName": "合同适用的采购组织", "modelAlias": "CON$con_pur_org_link_cf", "modelKey": "CON$con_pur_org_link_cf"}}], "valueType": "VAR", "type": "VarValue", "fieldType": "Array"}, "type": "LoopProperties"}}, {"name": "结束", "type": "EndNode", "key": "node_1hkgfko7p74", "props": {"type": "EndProperties"}}], "name": "合同适用采购组织转价格协议适用采购组织", "type": "ServiceDefinition", "headNodeKeys": ["node_1hkgfko7p73"], "key": "CON$tsrm_price_agt_inv_org_from_con_service", "props": {"transactionPropagation": "REQUIRED", "type": "ServiceProperties", "desc": "合同适用采购组织转价格协议适用采购组织"}}, "serviceType": "PROGRAMMABLE", "isEnabled": true, "serviceDslMd5": "null", "modelKey": "null"}}