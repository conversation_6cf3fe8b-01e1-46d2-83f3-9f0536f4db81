{"type": "ServiceDefinition", "name": "ABC分组方案-保存主数据服务", "access": "Private", "parentKey": "ERP_SCM", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1hotq7efc80", "name": "开始", "nextNodeKey": "node_1hotqchbp82", "props": {"globalVariable": [{"defaultValue": null, "description": null, "fieldAlias": "toCreate", "fieldKey": "toCreate", "fieldName": "toCreate", "fieldType": "Model", "id": null, "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "relatedModel": {"modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelName": "ABC分组方案"}, "relation": null, "required": null}], "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "relatedModel": {"modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelName": "ABC分组方案"}, "relation": null, "required": null}], "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "type": "StartProperties"}, "type": "StartNode"}, {"children": [{"children": [{"desc": null, "id": null, "key": "node_1hr5vpn3c1", "name": "更新数据", "nextNodeKey": "node_1i582apub1", "props": {"conditionGroup": null, "dataConditionPermissionKey": "null", "modelValue": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelName": "ABC分组方案"}, "valueKey": "request", "valueName": "request"}]}, "outputAssign": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelName": "ABC分组方案"}, "type": "CascadeUpdateDataProperties"}, "type": "CascadeUpdateDataNode"}, {"desc": null, "id": null, "key": "node_1i582apub1", "name": "赋值", "props": {"assignments": [{"field": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}, "id": "1i582at312", "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelName": "ABC分组方案"}, "valueKey": "request", "valueName": "request"}]}}], "type": "AssignProperties"}, "type": "AssignNode"}], "desc": null, "id": null, "key": "node_1hotqchbp83", "name": "查询条件中的ID字段不为空", "nextNodeKey": "node_1hr5vpn3c1", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "3XdRJlNTKUiK_DeCFjffd", "key": null, "leftValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelName": "ABC分组方案"}, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "SB6akPQ782PZ4Q-Uvu7Gh", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "GEkR4vmC00KpQdcgU07kl", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "ConditionProperties"}, "type": "ConditionNode"}, {"children": [{"desc": null, "id": null, "key": "node_1hr5vqjmc2", "name": "查询模型元数据的结构节点字段类型和关联模型信息", "nextNodeKey": "node_1hr5vsa0c7", "props": {"body": null, "bodyType": "VALUE", "headers": [], "inputMapping": null, "jsonBody": null, "method": "GET", "output": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Object", "id": null, "required": null}, "fieldAlias": "children", "fieldKey": "children", "fieldName": "children", "fieldType": "Array", "id": null, "required": null}, {"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Object", "id": null, "required": null}, "fieldAlias": "children", "fieldKey": "children", "fieldName": "children", "fieldType": "Array", "id": null, "required": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pathVariables": [], "serviceName": "查询模型元数据", "stream": false, "type": "HttpServiceProperties", "url": "http://localhost:8080/api/trantor/struct-node/find-by-key/ERP_SCM$inv_abc_group_strategy_head_cf"}, "type": "HttpServiceNode"}, {"desc": null, "id": null, "key": "node_1hr5vsa0c7", "name": "脚本节点中根据字段别名查询状态字段信息", "nextNodeKey": "node_1hr5vrgn14", "props": {"inputMapping": [{"field": {"defaultValue": null, "description": null, "element": null, "fieldAlias": "fields", "fieldKey": "fields", "fieldName": "fields", "fieldType": "Array", "id": null, "required": null}, "id": null, "value": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "NODE_OUTPUT_node_1hr5vqjmc2", "valueName": "[查询模型元数据的结构节点字段类型和关联模型信息]节点出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}, {"fieldType": null, "valueKey": "children", "valueName": "children"}]}}], "language": "JS", "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "field", "fieldKey": "field", "fieldName": "field", "fieldType": "Object", "id": null, "required": null}], "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "script": "for each (var field in fields) {    if (field.alias.toUpperCase() === 'STATUS') {      return field;    }  }  return null;", "scriptEngine": null, "type": "ScriptProperties"}, "type": "ScriptNode"}, {"children": [{"children": [{"desc": null, "id": null, "key": "node_1hr5vu8ic8", "name": "根据输入的编排服务节点配置信息分析得出的标题为：根据字段类型判断并修改request状态", "props": {"inputMapping": [{"field": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "field", "fieldKey": "field", "fieldName": "field", "fieldType": "Object", "id": null, "required": null}, "id": null, "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "NODE_OUTPUT_node_1hr5vsa0c7", "valueName": "[脚本节点中根据字段别名查询状态字段信息]节点出参"}, {"fieldType": null, "valueKey": "field", "valueName": "field"}]}}, {"field": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}, "id": null, "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelName": "ABC分组方案"}, "valueKey": "request", "valueName": "request"}]}}], "language": "JS", "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}], "outputAssign": {"customAssignments": [{"field": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelName": "ABC分组方案"}, "valueKey": "toCreate", "valueName": "toCreate"}]}, "id": "1hr83j8961", "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_1hr5vu8ic8", "valueName": "出参结构体"}, {"fieldType": null, "valueKey": "request", "valueName": "request"}]}}], "outputAssignType": "CUSTOM"}, "script": "if (field.props.fieldType === 'ENUM') {    if (field.props.dictPros !== null && field.props.dictPros.multiSelect === true) {      request.status = ['INACTIVE'];    }else {      request.status = 'INACTIVE';    }    }  return request;", "scriptEngine": null, "type": "ScriptProperties"}, "type": "ScriptNode"}], "desc": null, "id": null, "key": "node_1hr5vrgn15", "name": "条件", "nextNodeKey": "node_1hr5vu8ic8", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "xeWPjCdL3isbD3BnesUce", "key": null, "leftValue": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "NODE_OUTPUT_node_1hr5vsa0c7", "valueName": "[脚本节点中根据字段别名查询状态字段信息]节点出参"}, {"fieldType": null, "valueKey": "field", "valueName": "field"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "f7XFSYesptbxshO2As3I4", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "d7tAxHavvN7-1xm3RKPW4", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "ConditionProperties"}, "type": "ConditionNode"}, {"children": [{"desc": null, "id": null, "key": "node_1hr5vug909", "name": "赋值", "props": {"assignments": [{"field": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelName": "ABC分组方案"}, "valueKey": "toCreate", "valueName": "toCreate"}]}, "id": "1hr3l1lua3", "operator": "EQ", "value": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelName": "ABC分组方案"}, "valueKey": "request", "valueName": "request"}]}}], "type": "AssignProperties"}, "type": "AssignNode"}], "desc": null, "id": null, "key": "node_1hr5vrgn16", "name": "else", "nextNodeKey": "node_1hr5vug909", "props": {"type": "ConditionElseProperties"}, "type": "ConditionElseNode"}], "desc": null, "id": null, "key": "node_1hr5vrgn14", "name": "排他分支", "nextNodeKey": "node_1hr5vunf410", "props": {"type": "ExclusiveBranchProperties"}, "type": "ExclusiveBranchNode"}, {"desc": null, "id": null, "key": "node_1hr5vunf410", "name": "新增数据", "props": {"dataConditionPermissionKey": null, "modelValue": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelName": "ABC分组方案"}, "valueKey": "toCreate", "valueName": "toCreate"}]}, "outputAssign": {"customAssignments": [{"field": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}, "id": "1hr60lai311", "operator": "EQ", "value": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "NODE_OUTPUT_node_1hr5vunf410", "valueName": "出参结构体"}]}}], "outputAssignType": "CUSTOM"}, "relatedModel": {"modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelName": "ABC分组方案"}, "type": "CascadeCreateDataProperties"}, "type": "CascadeCreateDataNode"}], "desc": null, "id": null, "key": "node_1hotqchbp84", "name": "else", "nextNodeKey": "node_1hr5vqjmc2", "props": {"type": "ConditionElseProperties"}, "type": "ConditionElseNode"}], "desc": null, "id": null, "key": "node_1hotqchbp82", "name": "排他分支", "nextNodeKey": "node_1hotq7efc81", "props": {"type": "ExclusiveBranchProperties"}, "type": "ExclusiveBranchNode"}, {"desc": null, "id": null, "key": "node_1hotq7efc81", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "headNodeKeys": ["node_1hotq7efc80"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "relatedModel": {"modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelName": "ABC分组方案"}, "relation": null, "required": null}], "key": "ERP_SCM$INV_ABC_GROUP_STRATEGY_HEAD_CF_MASTER_DATA_SAVE_DATA_SERVICE", "name": "ABC分组方案-保存主数据服务", "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "fieldRules": null, "permissionKey": "ERP_SCM$INV_ABC_GROUP_STRATEGY_HEAD_CF_UPDATE_PERMISSION", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}}