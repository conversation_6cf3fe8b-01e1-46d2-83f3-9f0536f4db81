{"type": "Model", "key": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "name": "现金流使用组织关联表", "props": {"alias": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "children": [{"alias": "estGenComId", "appId": 43132, "ext": false, "key": "est_gen_com_id", "name": "使用组织", "props": {"autoGenerated": false, "columnName": "est_gen_com_id", "comment": "使用组织", "compositeKey": true, "encrypted": false, "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "currentModelFieldAlias": "estGenComId", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "sys_common$org_struct_md", "relationModelKey": "sys_common$org_struct_md", "relationType": "LINK", "sync": false}, "required": false, "unique": false}, "teamId": 22, "type": "DataStructField"}, {"alias": "finGlmAbItemCfId", "appId": 43132, "ext": false, "key": "fin_glm_ab_item_cf_id", "name": "现金流量项目", "props": {"autoGenerated": false, "columnName": "fin_glm_ab_item_cf_id", "comment": "现金流量项目", "compositeKey": true, "encrypted": false, "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "currentModelFieldAlias": "finGlmAbItemCfId", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "ERP_FI$fin_glm_ab_item_cf", "relationModelKey": "ERP_FI$fin_glm_ab_item_cf", "relationType": "LINK", "sync": false}, "required": false, "unique": false}, "teamId": 22, "type": "DataStructField"}, {"alias": "id", "appId": 43132, "ext": false, "key": "id", "name": "ID", "props": {"autoGenerated": false, "columnName": "id", "comment": "ID", "compositeKey": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": true, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "created<PERSON>y", "appId": 43132, "ext": false, "key": "created_by", "name": "创建人", "props": {"autoGenerated": false, "columnName": "created_by", "comment": "创建人", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"currentModelAlias": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "currentModelFieldAlias": "created<PERSON>y", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "ERP_FI$user", "relationModelKey": "ERP_FI$user", "relationType": "LINK", "sync": false}, "required": false, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "updatedBy", "appId": 43132, "ext": false, "key": "updated_by", "name": "更新人", "props": {"autoGenerated": false, "columnName": "updated_by", "comment": "更新人", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"currentModelAlias": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "currentModelFieldAlias": "updatedBy", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "ERP_FI$user", "relationModelKey": "ERP_FI$user", "relationType": "LINK", "sync": false}, "required": false, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "createdAt", "appId": 43132, "ext": false, "key": "created_at", "name": "创建时间", "props": {"autoGenerated": false, "columnName": "created_at", "comment": "创建时间", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "isSystemField": true, "required": true, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "updatedAt", "appId": 43132, "ext": false, "key": "updated_at", "name": "更新时间", "props": {"autoGenerated": false, "columnName": "updated_at", "comment": "更新时间", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "isSystemField": true, "required": true, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "version", "appId": 43132, "ext": false, "key": "version", "name": "版本号", "props": {"autoGenerated": false, "columnName": "version", "comment": "版本号", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": true, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "deleted", "appId": 43132, "ext": false, "key": "deleted", "name": "逻辑删除标识", "props": {"autoGenerated": false, "columnName": "deleted", "comment": "逻辑删除标识", "compositeKey": true, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": true, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "originOrgId", "appId": 43132, "ext": false, "key": "origin_org_id", "name": "所属组织", "props": {"autoGenerated": false, "columnName": "origin_org_id", "comment": "所属组织", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "numberDisplayType": "digit", "required": true, "unique": false}, "teamId": 22, "type": "DataStructField"}], "desc": null, "props": {"config": {"persist": false, "self": false, "selfRelationFieldAlias": null, "system": false}, "mainField": "est_gen_com_id", "mainFieldAlias": "estGenComId", "originOrgIdEnabled": true, "physicalDelete": false, "searchModel": false, "tableName": "fin_glm_ab_cft_org_ass_cf", "type": "PERSIST"}}, "parentKey": "ERP_FI"}