{"type": "Model", "name": "计量单位配置表", "parentKey": "ERP_GEN", "children": [], "props": {"desc": null, "alias": "ERP_GEN$gen_uom_type_cf", "props": {"type": "PERSIST", "config": {"self": false, "system": false, "persist": false, "selfRelationFieldAlias": null}, "mainField": "uom_desc", "tableName": "gen_uom_type_cf", "searchModel": false, "mainFieldAlias": "uomDesc", "physicalDelete": false, "originOrgIdEnabled": true}, "children": [{"ext": false, "key": "uom_type", "name": "计量单位类型编码", "type": "DataStructField", "alias": "uomType", "appId": 46980, "props": {"length": 256, "unique": false, "comment": "计量单位类型编码", "dictPros": {"dictValues": [{"label": "长度", "value": "L"}, {"label": "数量", "value": "QTY"}, {"label": "面积", "value": "AREA"}, {"label": "体积", "value": "VOL"}, {"label": "时间", "value": "TIME"}, {"label": "质量", "value": "MASS"}, {"label": "其他", "value": "OTHER"}], "properties": null, "multiSelect": false}, "required": true, "encrypted": false, "fieldType": "ENUM", "columnName": "uom_type", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "uom_code", "name": "计量单位编码", "type": "DataStructField", "alias": "uomCode", "appId": 46980, "props": {"length": 32, "unique": false, "comment": "计量单位编码", "required": true, "encrypted": false, "fieldType": "TEXT", "columnName": "uom_code", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "uom_desc", "name": "计量单位名称", "type": "DataStructField", "alias": "uomDesc", "appId": 46980, "props": {"length": 32, "unique": false, "comment": "计量单位名称", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "uom_desc", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "id", "name": "ID", "type": "DataStructField", "alias": "id", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "ID", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "id", "compositeKey": false, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}, {"ext": false, "key": "created_by", "name": "创建人", "type": "DataStructField", "alias": "created<PERSON>y", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "创建人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "created_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_uom_type_cf", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "created<PERSON>y"}, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "updated_by", "name": "更新人", "type": "DataStructField", "alias": "updatedBy", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "更新人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "updated_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_uom_type_cf", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "updatedBy"}, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "created_at", "name": "创建时间", "type": "DataStructField", "alias": "createdAt", "appId": 46980, "props": {"unique": true, "comment": "创建时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "created_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "updated_at", "name": "更新时间", "type": "DataStructField", "alias": "updatedAt", "appId": 46980, "props": {"unique": true, "comment": "更新时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "updated_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "version", "name": "版本号", "type": "DataStructField", "alias": "version", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "版本号", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "version", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}, {"ext": false, "key": "deleted", "name": "逻辑删除标识", "type": "DataStructField", "alias": "deleted", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "逻辑删除标识", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "deleted", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}, {"ext": false, "key": "origin_org_id", "name": "所属组织", "type": "DataStructField", "alias": "originOrgId", "appId": 46980, "props": {"unique": false, "comment": "所属组织", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "origin_org_id", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}]}}