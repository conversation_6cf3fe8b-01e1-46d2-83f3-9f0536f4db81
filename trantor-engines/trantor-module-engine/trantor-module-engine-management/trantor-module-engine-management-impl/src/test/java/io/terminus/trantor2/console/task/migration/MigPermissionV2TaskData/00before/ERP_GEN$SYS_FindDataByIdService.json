{"type": "ServiceDefinition", "name": "ERP_GEN$SYS_FindDataByIdService", "parentKey": "test", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": null, "desc": null, "id": null, "input": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "id": null, "required": null}, {"elements": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "id": null, "required": null}, {"elements": null, "fieldAlias": "formParams", "fieldKey": "formParams", "fieldName": "formParams", "fieldType": "Object", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}], "key": "ERP_SCM$SYS_InvokeCodeRuleService", "name": "(系统)调用取号规则服务", "output": [{"fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Text", "id": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "fieldRules": null, "permissionKey": null, "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "SYSTEM"}}