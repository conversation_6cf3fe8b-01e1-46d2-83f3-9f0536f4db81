{"type": "Model", "name": "银行支行表", "parentKey": "ERP_GEN", "children": [], "props": {"desc": null, "alias": "ERP_GEN$gen_sub_bank_cf", "props": {"type": "PERSIST", "config": {"self": false, "system": false, "persist": false, "selfRelationFieldAlias": null}, "mainField": "sub_bank_name", "tableName": "gen_sub_bank_cf", "searchModel": false, "mainFieldAlias": "subBankName", "physicalDelete": false, "originOrgIdEnabled": true}, "children": [{"ext": false, "key": "sub_bank_code", "name": "银行支行编码", "type": "DataStructField", "alias": "subBankCode", "appId": 46980, "props": {"length": 256, "unique": false, "comment": "银行支行编码", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "sub_bank_code", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "sub_bank_name", "name": "银行支行名称", "type": "DataStructField", "alias": "subBankName", "appId": 46980, "props": {"length": 50, "unique": false, "comment": "银行支行名称", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "sub_bank_name", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "gen_bank_cf_id", "name": "所属银行", "type": "DataStructField", "alias": "genBankCfId", "appId": 46980, "props": {"unique": false, "comment": "所属银行", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "gen_bank_cf_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_bank_cf", "currentModelAlias": "ERP_GEN$gen_sub_bank_cf", "relationModelAlias": "ERP_GEN$gen_bank_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "genBankCfId"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "province", "name": "省", "type": "DataStructField", "alias": "province", "appId": 46980, "props": {"unique": false, "comment": "省", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "province", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_addr_type_cf", "currentModelAlias": "ERP_GEN$gen_sub_bank_cf", "relationModelAlias": "ERP_GEN$gen_addr_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "province"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "city", "name": "市", "type": "DataStructField", "alias": "city", "appId": 46980, "props": {"unique": false, "comment": "市", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "city", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_addr_type_cf", "currentModelAlias": "ERP_GEN$gen_sub_bank_cf", "relationModelAlias": "ERP_GEN$gen_addr_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "city"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "district", "name": "区", "type": "DataStructField", "alias": "district", "appId": 46980, "props": {"unique": false, "comment": "区", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "district", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_addr_type_cf", "currentModelAlias": "ERP_GEN$gen_sub_bank_cf", "relationModelAlias": "ERP_GEN$gen_addr_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "district"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "id", "name": "ID", "type": "DataStructField", "alias": "id", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "ID", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "id", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "created_by", "name": "创建人", "type": "DataStructField", "alias": "created<PERSON>y", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "创建人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "created_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_sub_bank_cf", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "created<PERSON>y"}, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "updated_by", "name": "更新人", "type": "DataStructField", "alias": "updatedBy", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "更新人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "updated_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_sub_bank_cf", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "updatedBy"}, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "created_at", "name": "创建时间", "type": "DataStructField", "alias": "createdAt", "appId": 46980, "props": {"unique": true, "comment": "创建时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "created_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "updated_at", "name": "更新时间", "type": "DataStructField", "alias": "updatedAt", "appId": 46980, "props": {"unique": true, "comment": "更新时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "updated_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "version", "name": "版本号", "type": "DataStructField", "alias": "version", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "版本号", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "version", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "deleted", "name": "逻辑删除标识", "type": "DataStructField", "alias": "deleted", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "逻辑删除标识", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "deleted", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "origin_org_id", "name": "所属组织", "type": "DataStructField", "alias": "originOrgId", "appId": 46980, "props": {"unique": false, "comment": "所属组织", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "origin_org_id", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}]}}