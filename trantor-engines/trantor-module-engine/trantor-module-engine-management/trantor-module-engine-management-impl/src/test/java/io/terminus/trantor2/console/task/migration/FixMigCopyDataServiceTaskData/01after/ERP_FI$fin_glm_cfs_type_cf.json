{"type": "Model", "key": "ERP_FI$fin_glm_cfs_type_cf", "name": "现金流量项目表", "props": {"alias": "ERP_FI$fin_glm_cfs_type_cf", "children": [{"alias": "cfsTypeCode", "appId": 43132, "ext": false, "key": "cfs_type_code", "name": "现金流项目表编码", "props": {"autoGenerated": false, "columnName": "cfs_type_code", "comment": "现金流项目表编码", "compositeKey": true, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 56, "required": false, "unique": false}, "teamId": 22, "type": "DataStructField"}, {"alias": "cfsTypeName", "appId": 43132, "ext": false, "key": "cfs_type_name", "name": "现金流项目表名称", "props": {"autoGenerated": false, "columnName": "cfs_type_name", "comment": "现金流项目表名称", "compositeKey": false, "encrypted": false, "fieldType": "TEXT", "isSystemField": false, "length": 56, "required": false, "unique": false}, "teamId": 22, "type": "DataStructField"}, {"alias": "finGlmCftTypeCf", "appId": 43132, "ext": false, "key": "fin_glm_cft_type_cf", "name": "现金流量项目类别", "props": {"autoGenerated": false, "columnName": "fin_glm_cft_type_cf", "comment": "现金流量项目类别", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "ERP_FI$fin_glm_cfs_type_cf", "currentModelFieldAlias": "finGlmCftTypeCf", "linkModelAlias": "ERP_FI$fin_glm_cft_type_cf", "linkModelFieldAlias": "finGlmCfsTypeCfId", "relationKey": null, "relationModelAlias": "ERP_FI$fin_glm_cft_type_cf", "relationModelKey": "ERP_FI$fin_glm_cft_type_cf", "relationType": "PARENT_CHILD", "sync": true}, "required": false, "unique": false}, "teamId": 22, "type": "DataStructField"}, {"alias": "cfsOrg", "appId": 43132, "ext": false, "key": "cfs_org", "name": "创建组织", "props": {"autoGenerated": false, "columnName": "cfs_org", "comment": "创建组织", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "ERP_FI$fin_glm_cfs_type_cf", "currentModelFieldAlias": "cfsOrg", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "sys_common$org_struct_md", "relationModelKey": "sys_common$org_struct_md", "relationType": "LINK", "sync": false}, "required": false, "unique": false}, "teamId": 22, "type": "DataStructField"}, {"alias": "id", "appId": 43132, "ext": false, "key": "id", "name": "ID", "props": {"autoGenerated": false, "columnName": "id", "comment": "ID", "compositeKey": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": true, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "created<PERSON>y", "appId": 43132, "ext": false, "key": "created_by", "name": "创建人", "props": {"autoGenerated": false, "columnName": "created_by", "comment": "创建人", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"currentModelAlias": "ERP_FI$fin_glm_cfs_type_cf", "currentModelFieldAlias": "created<PERSON>y", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "ERP_FI$user", "relationModelKey": "ERP_FI$user", "relationType": "LINK", "sync": false}, "required": false, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "updatedBy", "appId": 43132, "ext": false, "key": "updated_by", "name": "更新人", "props": {"autoGenerated": false, "columnName": "updated_by", "comment": "更新人", "compositeKey": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"currentModelAlias": "ERP_FI$fin_glm_cfs_type_cf", "currentModelFieldAlias": "updatedBy", "linkModelAlias": null, "linkModelFieldAlias": null, "relationKey": null, "relationModelAlias": "ERP_FI$user", "relationModelKey": "ERP_FI$user", "relationType": "LINK", "sync": false}, "required": false, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "createdAt", "appId": 43132, "ext": false, "key": "created_at", "name": "创建时间", "props": {"autoGenerated": false, "columnName": "created_at", "comment": "创建时间", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "isSystemField": true, "required": true, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "updatedAt", "appId": 43132, "ext": false, "key": "updated_at", "name": "更新时间", "props": {"autoGenerated": false, "columnName": "updated_at", "comment": "更新时间", "compositeKey": false, "encrypted": false, "fieldType": "DATE", "isSystemField": true, "required": true, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "version", "appId": 43132, "ext": false, "key": "version", "name": "版本号", "props": {"autoGenerated": false, "columnName": "version", "comment": "版本号", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": true, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "deleted", "appId": 43132, "ext": false, "key": "deleted", "name": "逻辑删除标识", "props": {"autoGenerated": false, "columnName": "deleted", "comment": "逻辑删除标识", "compositeKey": true, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "numberDisplayType": "digit", "required": true, "unique": true}, "teamId": 22, "type": "DataStructField"}, {"alias": "originOrgId", "appId": 43132, "ext": false, "key": "origin_org_id", "name": "所属组织", "props": {"autoGenerated": false, "columnName": "origin_org_id", "comment": "所属组织", "compositeKey": false, "defaultValue": 0, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "numberDisplayType": "digit", "required": true, "unique": false}, "teamId": 22, "type": "DataStructField"}], "desc": null, "props": {"config": {"persist": false, "self": false, "selfRelationFieldAlias": null, "system": false}, "mainField": "cfs_type_name", "mainFieldAlias": "cfsTypeName", "originOrgIdEnabled": true, "physicalDelete": false, "searchModel": false, "shardingConfig": {"enabled": false, "shardingFields": null, "shardingSuffixLength": 3}, "tableName": "fin_glm_cfs_type_cf", "type": "PERSIST"}}, "parentKey": "ERP_FI", "access": "Private"}