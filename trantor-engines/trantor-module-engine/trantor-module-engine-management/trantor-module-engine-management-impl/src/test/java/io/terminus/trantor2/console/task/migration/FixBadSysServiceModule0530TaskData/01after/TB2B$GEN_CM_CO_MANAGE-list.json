{"type": "View", "name": "TB2B$GEN_CM_CO_MANAGE-list", "parentKey": "TB2B", "children": [], "props": {"containerSelect": {"TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-detail": [{"field": "cmCgTypeCode", "selectFields": null}, {"field": "cmCgTypeName", "selectFields": null}, {"field": "cmCgTypeRemark", "selectFields": null}, {"field": "cmCgOrg", "selectFields": [{"field": "id", "selectFields": null}, {"field": "cmCoTypeCode", "selectFields": null}]}, {"field": "created<PERSON>y", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "updatedBy", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "createdAt", "selectFields": null}, {"field": "updatedAt", "selectFields": null}], "TB2B$GEN_CM_CO_MANAGE-detailView-useQuery": [], "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form": [{"field": "version", "selectFields": null}, {"field": "cmCgTypeCode", "selectFields": null}, {"field": "cmCgTypeName", "selectFields": null}, {"field": "cmCgTypeRemark", "selectFields": null}, {"field": "cmCgOrg", "selectFields": [{"field": "id", "selectFields": null}, {"field": "cmCoTypeCode", "selectFields": null}]}], "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md": [{"field": "version", "selectFields": null}, {"field": "cmCgTypeCode", "selectFields": null}, {"field": "cmCgTypeName", "selectFields": null}]}, "content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-new", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "新建", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch", "name": "DropdownButton", "props": {"combinationMode": true, "disabled$": "$context.selectedKeys?.length === 0 || $context.route?.action === \"new\" || $context.route?.action === \"edit\"", "items": [{"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"elements": [{"fieldAlias": "ids", "fieldName": "ids", "fieldType": "Number", "valueConfig": {"expression": "<PERSON><PERSON><PERSON><PERSON>", "name": "ids", "type": "expression"}}, {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$gen_cm_cg_md"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "TB2B$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ""}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "TB2B$GEN_CM_CO_MANAGE-TB2B$gen_cm_cg_md-multi-delete", "label": "批量删除"}, {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认统一启用吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"elements": [{"fieldAlias": "ids", "fieldName": "ids", "fieldType": "Number", "valueConfig": {"expression": "<PERSON><PERSON><PERSON><PERSON>", "name": "ids", "type": "expression"}}, {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$gen_cm_cg_md"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "TB2B$SYS_MasterData_MultiEnableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md"}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "TB2B$GEN_CM_CO_MANAGE-TB2B$gen_cm_cg_md-multi-start", "label": "批量启用"}, {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认统一停用吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"elements": [{"fieldAlias": "ids", "fieldName": "ids", "fieldType": "Number", "valueConfig": {"expression": "<PERSON><PERSON><PERSON><PERSON>", "name": "ids", "type": "expression"}}, {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$gen_cm_cg_md"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "TB2B$SYS_MasterData_MultiDisableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md"}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "TB2B$GEN_CM_CO_MANAGE-TB2B$gen_cm_cg_md-multi-stop", "label": "批量停用"}], "label": "批量操作"}, "type": "Widget"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-import", "name": "ImportButton", "props": {"addApprovalManageServiceProps": {"predictFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/gei/task/config/predict"}}, "deleteApprovalManageServiceProps": {"saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/workflow/v2/{param0}"}}, "disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "editApprovalManageServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/workflow/v2/{param0}"}}, "label": "导入"}, "type": "Widget"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-export", "name": "ExportButton", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "exportButtonServiceProps": {"getUserInfoFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/portal/user/current"}, "saveFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/gei/task/export-direct"}}, "label": "导出"}, "type": "Widget"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-logs", "name": "Logs", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch-actions", "name": "BatchActions", "props": {}}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "fN4HeGU8JmZWPiu2HNkYi", "name": "Field", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请输入", "precision": null}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "fkBem2IN_nHYgpRC3TRag", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "cmCgTypeCode", "modelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请输入"}, "displayComponentType": "Text", "label": "等级编码", "name": "cmCgTypeCode", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "zWCZjwFkQTzie3_o1tZ18", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "cmCgTypeName", "modelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请输入"}, "displayComponentType": "Text", "label": "等级名称", "name": "cmCgTypeName", "required": true, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "5GVsJkxRDjiNU9Ls-m51b", "name": "Fields", "props": {}, "type": "Meta"}], "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "name": "Table", "props": {"allowRowSelect": true, "filterFields": [{"componentProps": {"defaultValue": null, "fieldAlias": "cmCgTypeCode", "modelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请输入"}, "hidden": false, "label": "等级编码", "name": "cmCgTypeCode", "type": "TEXT", "width": 146}, {"componentProps": {"defaultValue": null, "fieldAlias": "cmCgTypeName", "modelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请输入"}, "hidden": false, "label": "等级名称", "name": "cmCgTypeName", "type": "TEXT", "width": 146}, {"componentProps": {"defaultValue": null, "fieldAlias": "cmCgTypeRemark", "modelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请输入"}, "hidden": false, "label": "备注", "name": "cmCgTypeRemark", "type": "TEXT", "width": 146}, {"componentProps": {"columns": ["cmCoTypeCode", "cmCoTypeName", "cmCoTypeRemark"], "fieldAlias": "cmCgOrg", "label": "选择信用组织", "labelField": "cmCoTypeCode", "modelAlias": "TB2B$gen_cm_co_md", "parentModelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TB2B$GEN_CM_CO_MD_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "TB2B$GEN_CM_CO_MD_PAGING_DATA_SERVICE"}}, "hidden": false, "label": "信用组织", "name": "cmCgOrg", "type": "OBJECT", "width": 168}], "flow": {"containerKey": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "context$": "$context", "modelAlias": "TB2B$gen_cm_cg_md", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$gen_cm_cg_md"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "TB2B$GEN_CM_CG_MD_PAGING_DATA_SERVICE_chgCnV1", "type": "InvokeService"}, "isProFilter": false, "label": "表格", "mode": "simple", "modelAlias": "TB2B$gen_cm_cg_md", "onRowActionConfig": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "enable": true}, "pagination": {"size": "small"}, "serviceKey": "BAD_TB2B_BAD$SYS_PagingDataService", "showConfigure": false, "showScope": "all", "tableCondition": null}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{((data?.code || \"\") + (data?.name || \"\")) || \"信用等级详情\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [{"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}, {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$gen_cm_cg_md"}, {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "TB2B$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "TB2B$GEN_CM_CO_MANAGE-page"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "label": "删除", "tooltip": "确定要删除信用等级吗", "type": "default"}}], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions-enable-delete-show", "name": "Show", "props": {"value$": "data?.status !== \"ENABLED\""}, "type": "Meta"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "route.recordId", "name": "copyId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "label": "复制"}, "type": "Widget"}, {"children": [{"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions-disable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认停用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}, {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$gen_cm_cg_md"}, {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "TB2B$SYS_MasterData_DisableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "TB2B$GEN_CM_CO_MANAGE-detailView-page"}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "label": "停用"}, "type": "Widget"}], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions-disable-show", "name": "Show", "props": {"value$": "data?.status === \"ENABLED\""}, "type": "Meta"}, {"children": [{"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions-enable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认启用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}, {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$gen_cm_cg_md"}, {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "TB2B$SYS_MasterData_EnableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "TB2B$GEN_CM_CO_MANAGE-detailView-page"}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "label": "启用"}, "type": "Widget"}], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions-enable-show", "name": "Show", "props": {"value$": "data?.status === \"INACTIVE\" || data?.status === \"DISABLED\""}, "type": "Meta"}, {"children": [{"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "label": "编辑", "type": "primary"}, "type": "Widget"}], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions-enable-edit-show", "name": "Show", "props": {"value$": "data?.status !== \"ENABLED\""}, "type": "Meta"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-detail-TB2B$gen_cm_cg_md-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-field-cmCgTypeCode", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "cmCgTypeCode", "modelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请输入"}, "editable": false, "label": "等级编码", "name": "cmCgTypeCode", "type": "TEXT"}}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-field-cmCgTypeName", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "cmCgTypeName", "modelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请输入"}, "editable": false, "label": "等级名称", "name": "cmCgTypeName", "type": "TEXT"}}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-field-cmCgTypeRemark", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "cmCgTypeRemark", "modelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请输入"}, "editable": false, "label": "备注", "name": "cmCgTypeRemark", "type": "TEXT"}}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-field-cmCgOrg", "name": "DetailField", "props": {"componentProps": {"columns": ["cmCoTypeCode", "cmCoTypeName", "cmCoTypeRemark"], "fieldAlias": "cmCgOrg", "label": "选择信用组织", "labelField": "cmCoTypeCode", "modelAlias": "TB2B$gen_cm_co_md", "parentModelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TB2B$GEN_CM_CO_MD_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "TB2B$GEN_CM_CO_MD_PAGING_DATA_SERVICE"}}, "editable": false, "label": "信用组织", "name": "cmCgOrg", "type": "OBJECT"}}], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-detail-defaultGroup", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}, {"children": [{"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TB2B$user", "parentModelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TB2B$USER_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "TB2B$USER_PAGING_DATA_SERVICE"}}, "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TB2B$user", "parentModelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TB2B$USER_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "TB2B$USER_PAGING_DATA_SERVICE"}}, "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请选择"}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请选择"}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-system-detail-group", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "系统信息"}], "underline": true}, "type": "Layout"}], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-detail", "name": "Detail", "props": {"flow": {"containerKey": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-detail", "modelAlias": "TB2B$gen_cm_cg_md", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "TB2B$GEN_CM_CG_MD_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "modelAlias": "TB2B$gen_cm_cg_md", "onFinish$": "(values) => invokeSystemService(\"BAD_TB2B_BAD$SYS_MasterData_SaveDataService\", \"TB2B$gen_cm_cg_md\", {...data, ...values}).then(() => $(\"TB2B$GEN_CM_CO_MANAGE-detailView-page\").action(\"reload\"))", "serviceKey": "BAD_TB2B_BAD$SYS_FindDataByIdService"}, "type": "Container"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "TB2B$GEN_CM_CO_MANAGE-detailView-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showFooter": false, "showHeader": true}, "type": "Container"}, {"children": [{"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" + ((data?.code || \"\") + (data?.name || \"信用等级\")) : \"新建信用等级\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-id", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-createdBy", "name": "FormField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "TB2B$user", "parentModelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TB2B$USER_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "TB2B$USER_PAGING_DATA_SERVICE"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-updatedBy", "name": "FormField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "TB2B$user", "parentModelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TB2B$USER_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "TB2B$USER_PAGING_DATA_SERVICE"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-createdAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-updatedAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-cmCgTypeCode", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "cmCgTypeCode", "modelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "等级编码", "name": "cmCgTypeCode", "rules": [{"message": "请输入等级编码", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-cmCgTypeName", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "cmCgTypeName", "modelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "等级名称", "name": "cmCgTypeName", "rules": [{"message": "请输入等级名称", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-cmCgTypeRemark", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "cmCgTypeRemark", "modelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "备注", "name": "cmCgTypeRemark", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-cmCgOrg", "name": "FormField", "props": {"componentProps": {"columns": ["cmCoTypeCode", "cmCoTypeName", "cmCoTypeRemark"], "fieldAlias": "cmCgOrg", "label": "选择信用组织", "labelField": "cmCoTypeCode", "modelAlias": "TB2B$gen_cm_co_md", "parentModelAlias": "TB2B$gen_cm_cg_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TB2B$GEN_CM_CO_MD_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "TB2B$GEN_CM_CO_MD_PAGING_DATA_SERVICE"}}, "hidden": false, "initialValue": null, "label": "信用组织", "name": "cmCgOrg", "rules": [{"message": "请输入信用组织", "required": true}], "type": "OBJECT"}, "type": "Widget"}], "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form-defaultGroup", "name": "FormGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "TB2B$GEN_CM_CO_MANAGE-editView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}], "underline": true}, "type": "Layout"}], "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form", "name": "FormGroup", "props": {"colon": false, "flow": {"containerKey": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form", "modelAlias": "TB2B$gen_cm_cg_md", "params$": "{ id: route.query?.copyId || route.recordId }", "serviceKey$": "route.query?.copyId ? \"BAD_TB2B_BAD$SYS_CopyDataConverterService\" : \"BAD_TB2B_BAD$SYS_FindDataByIdService\"", "test$": "route.action === \"edit\" || !!route.query?.copyId", "type": "InvokeSystemService"}, "layout": "horizontal", "modelAlias": "TB2B$gen_cm_cg_md", "serviceKey": "BAD_TB2B_BAD$SYS_FindDataByIdService"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target$": "route?.action === \"edit\" ? \"show\" : \"list\""}]}, "label": "取消"}, "type": "Widget"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Validate", "validate": true}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "TB2B$gen_cm_cg_md"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "valueConfig": {"action": {"target": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form"}, "type": "action"}}], "service": "TB2B$SYS_MasterData_SaveDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "data.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}, {"action": "Refresh", "target": ["TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md"]}, {"action": "Message", "level": "success", "message": "保存成功!"}], "executeLogic": "BindService"}, "label": "保存", "type": "primary"}, "type": "Widget"}], "key": "TB2B$GEN_CM_CO_MANAGE-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "TB2B$GEN_CM_CO_MANAGE-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "TB2B$GEN_CM_CO_MANAGE-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": true, "showHeader": true}, "type": "Container"}], "key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "TB2B$GEN_CM_CO_MANAGE-column-page", "name": "ColumnPage", "props": {}, "type": "Layout"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [], "key": "TB2B$GEN_CM_CO_MANAGE-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "TB2B$GEN_CM_CO_MANAGE-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": false, "showHeader": false, "title": "信用等级管理"}, "type": "Container"}, "extended": false, "frontendConfig": {"modules": ["base", "terp", "service"]}, "key": "TB2B$GEN_CM_CO_MANAGE-list", "resources": [{"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}], "relations": [{"key": "TB2B$USER_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$user"}, "type": "Service"}, {"key": "TB2B$USER_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$user"}, "type": "Service"}, {"key": "TB2B$GEN_CM_CO_MD_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$gen_cm_co_md"}, "type": "Service"}, {"key": "TB2B$GEN_CM_CO_MD_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$gen_cm_co_md"}, "type": "Service"}, {"key": "TB2B$GEN_CM_CG_MD_PAGING_DATA_SERVICE_chgCnV1", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$gen_cm_cg_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-new", "label": "新建", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch/items/TB2B$GEN_CM_CO_MANAGE-TB2B$gen_cm_cg_md-multi-delete", "label": "批量删除", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "TB2B$SYS_BatchDeleteDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$gen_cm_cg_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch/items/TB2B$GEN_CM_CO_MANAGE-TB2B$gen_cm_cg_md-multi-start", "label": "批量启用", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "TB2B$SYS_MasterData_MultiEnableDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$gen_cm_cg_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch/items/TB2B$GEN_CM_CO_MANAGE-TB2B$gen_cm_cg_md-multi-stop", "label": "批量停用", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "TB2B$SYS_MasterData_MultiDisableDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$gen_cm_cg_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-import", "label": "导入", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-import/importPredict", "label": "预测", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-import", "label": "导入", "type": "ImportButton"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-import/importSaving", "label": "保存", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-import", "label": "导入", "type": "ImportButton"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-import/importSub", "label": "保存（子模型）", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-import", "label": "导入", "type": "ImportButton"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-import/download", "label": "下载默认模板", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-import", "label": "导入", "type": "ImportButton"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-import/isCustom", "label": "判断是否是自定义服务", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-import", "label": "导入", "type": "ImportButton"}], "relations": [], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-export", "label": "导出", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-export/exportButtonService", "label": "组件调用服务", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-export", "label": "导出", "type": "ExportButton"}], "relations": [{"key": "/api/trantor/portal/user/current", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "/api/gei/task/export-direct", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-logs", "label": "日志", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-logs/logsService", "label": "组件调用服务", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md-logs", "label": "日志", "type": "Logs"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "fN4HeGU8JmZWPiu2HNkYi", "label": "ID", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "5GVsJkxRDjiNU9Ls-m51b", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "G3xJ__C_MPjVQoCOJRqzc", "label": "创建人", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "5GVsJkxRDjiNU9Ls-m51b", "label": "字段组", "type": "Fields"}], "relations": [{"key": "TB2B$USER_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$user"}, "type": "Service"}, {"key": "TB2B$USER_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "-KT4B_VYoQy-SG2svN1rz", "label": "更新人", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "5GVsJkxRDjiNU9Ls-m51b", "label": "字段组", "type": "Fields"}], "relations": [{"key": "TB2B$USER_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$user"}, "type": "Service"}, {"key": "TB2B$USER_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "-bqYG8rFBFAklNvkIr1uq", "label": "创建时间", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "5GVsJkxRDjiNU9Ls-m51b", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "9qTQbBrXVt-LGYoOOSQQj", "label": "更新时间", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "5GVsJkxRDjiNU9Ls-m51b", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "2k4TB6S6D4UzYaUjdweVI", "label": "版本号", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "5GVsJkxRDjiNU9Ls-m51b", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "P3moX0_oUC10wAMBLeWiS", "label": "逻辑删除标识", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "5GVsJkxRDjiNU9Ls-m51b", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "NNRE7SlSkLglljBcWcvhp", "label": "备注", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "5GVsJkxRDjiNU9Ls-m51b", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "daxXofT_7Bmf8cQ1TmGD0", "label": "信用组织", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "5GVsJkxRDjiNU9Ls-m51b", "label": "字段组", "type": "Fields"}], "relations": [{"key": "TB2B$GEN_CM_CO_MD_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$gen_cm_co_md"}, "type": "Service"}, {"key": "TB2B$GEN_CM_CO_MD_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$gen_cm_co_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "fkBem2IN_nHYgpRC3TRag", "label": "等级编码", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "5GVsJkxRDjiNU9Ls-m51b", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "zWCZjwFkQTzie3_o1tZ18", "label": "等级名称", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-list-TB2B$gen_cm_cg_md", "label": "表格", "type": "Table"}, {"key": "5GVsJkxRDjiNU9Ls-m51b", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form", "label": "表单组", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView", "label": "页面", "type": "Page"}], "relations": [], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-detail", "label": "详情", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-useQuery", "label": "请求容器", "type": "UseQuery"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page", "label": "页面", "type": "Page"}], "relations": [{"key": "TB2B$GEN_CM_CG_MD_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$gen_cm_cg_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-editView-action-cancel", "label": "取消", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-editView-action-save", "label": "保存", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "TB2B$SYS_MasterData_SaveDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$gen_cm_cg_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions-copy", "label": "复制", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-useQuery", "label": "请求容器", "type": "UseQuery"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-detail-TB2B$gen_cm_cg_md-logs", "label": "日志", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-useQuery", "label": "请求容器", "type": "UseQuery"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-detail-TB2B$gen_cm_cg_md-logs/logsService", "label": "组件调用服务", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-useQuery", "label": "请求容器", "type": "UseQuery"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions", "label": "间距", "type": "Space"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detail-TB2B$gen_cm_cg_md-logs", "label": "日志", "type": "Logs"}], "relations": [{"key": "/api/oplog/admin/paging/log", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-id", "label": "ID", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-createdBy", "label": "创建人", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "TB2B$USER_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$user"}, "type": "Service"}, {"key": "TB2B$USER_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-updatedBy", "label": "更新人", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "TB2B$USER_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$user"}, "type": "Service"}, {"key": "TB2B$USER_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-createdAt", "label": "创建时间", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-updatedAt", "label": "更新时间", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-version", "label": "版本号", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-deleted", "label": "逻辑删除标识", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-cmCgTypeCode", "label": "等级编码", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-cmCgTypeName", "label": "等级名称", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-cmCgTypeRemark", "label": "备注", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-field-cmCgOrg", "label": "信用组织", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TB2B$GEN_CM_CO_MANAGE-editView-TB2B$gen_cm_cg_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "TB2B$GEN_CM_CO_MD_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$gen_cm_co_md"}, "type": "Service"}, {"key": "TB2B$GEN_CM_CO_MD_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$gen_cm_co_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions-delete", "label": "删除", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-useQuery", "label": "请求容器", "type": "UseQuery"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions", "label": "间距", "type": "Space"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions-enable-delete-show", "label": "展示逻辑", "type": "Show"}], "relations": [{"key": "TB2B$SYS_DeleteDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$gen_cm_cg_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions-disable", "label": "停用", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-useQuery", "label": "请求容器", "type": "UseQuery"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions", "label": "间距", "type": "Space"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions-disable-show", "label": "展示逻辑", "type": "Show"}], "relations": [{"key": "TB2B$SYS_MasterData_DisableDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$gen_cm_cg_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions-enable", "label": "启用", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-useQuery", "label": "请求容器", "type": "UseQuery"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions", "label": "间距", "type": "Space"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions-enable-show", "label": "展示逻辑", "type": "Show"}], "relations": [{"key": "TB2B$SYS_MasterData_EnableDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$gen_cm_cg_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions-edit", "label": "编辑", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-useQuery", "label": "请求容器", "type": "UseQuery"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions", "label": "间距", "type": "Space"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-actions-enable-edit-show", "label": "展示逻辑", "type": "Show"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-field-cmCgTypeCode", "label": "等级编码", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-useQuery", "label": "请求容器", "type": "UseQuery"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-detail", "label": "详情", "type": "Detail"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-field-cmCgTypeName", "label": "等级名称", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-useQuery", "label": "请求容器", "type": "UseQuery"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-detail", "label": "详情", "type": "Detail"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-field-cmCgTypeRemark", "label": "备注", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-useQuery", "label": "请求容器", "type": "UseQuery"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-detail", "label": "详情", "type": "Detail"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-field-cmCgOrg", "label": "信用组织", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-useQuery", "label": "请求容器", "type": "UseQuery"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-detail", "label": "详情", "type": "Detail"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [{"key": "TB2B$GEN_CM_CO_MD_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$gen_cm_co_md"}, "type": "Service"}, {"key": "TB2B$GEN_CM_CO_MD_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$gen_cm_co_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-detailView-system-detail-field-createdBy", "label": "创建人", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-useQuery", "label": "请求容器", "type": "UseQuery"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-detail", "label": "详情", "type": "Detail"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [{"key": "TB2B$USER_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$user"}, "type": "Service"}, {"key": "TB2B$USER_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-detailView-system-detail-field-updatedBy", "label": "更新人", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-useQuery", "label": "请求容器", "type": "UseQuery"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-detail", "label": "详情", "type": "Detail"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [{"key": "TB2B$USER_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$user"}, "type": "Service"}, {"key": "TB2B$USER_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "TB2B$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-detailView-system-detail-field-createdAt", "label": "创建时间", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-useQuery", "label": "请求容器", "type": "UseQuery"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-detail", "label": "详情", "type": "Detail"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TB2B$GEN_CM_CO_MANAGE-detailView-system-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "TB2B$GEN_CM_CO_MANAGE-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "TB2B$GEN_CM_CO_MANAGE-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-useQuery", "label": "请求容器", "type": "UseQuery"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-page", "label": "页面", "type": "Page"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-TB2B$gen_cm_cg_md-detail", "label": "详情", "type": "Detail"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "TB2B$GEN_CM_CO_MANAGE-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}}