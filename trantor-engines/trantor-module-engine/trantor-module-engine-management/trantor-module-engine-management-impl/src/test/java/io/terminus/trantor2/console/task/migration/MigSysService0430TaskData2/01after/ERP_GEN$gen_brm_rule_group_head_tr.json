{"type": "Model", "name": "组织架构表", "parentKey": "ERP_GEN", "children": [], "props": {"desc": null, "alias": "ERP_GEN$gen_brm_rule_group_head_tr", "props": {"type": "PERSIST", "config": {"self": false, "system": false, "persist": false, "selfRelationFieldAlias": null}, "mainField": "name", "tableName": "gen_brm_rule_group_head_tr", "searchModel": false, "mainFieldAlias": "name", "physicalDelete": false, "shardingConfig": {"enabled": false, "shardingFields": null, "shardingSuffixLength": 3}, "originOrgIdEnabled": true}, "children": [{"ext": false, "key": "code", "name": "业务规则组编号", "type": "DataStructField", "alias": "code", "appId": 46980, "props": {"length": 256, "unique": false, "comment": "业务规则组编号", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "code", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "name", "name": "业务规则组名称", "type": "DataStructField", "alias": "name", "appId": 46980, "props": {"length": 256, "unique": false, "comment": "业务规则组名称", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "name", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "biz_category", "name": "业务类别", "type": "DataStructField", "alias": "bizCategory", "appId": 46980, "props": {"length": 256, "unique": false, "comment": "业务类别", "dictPros": {"dictValues": [{"label": "采购", "value": "PURCHASE"}, {"label": "销售", "value": "SALE"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "biz_category", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "status", "name": "启用状态", "type": "DataStructField", "alias": "status", "appId": 46980, "props": {"length": 256, "unique": false, "comment": "启用状态", "dictPros": {"dictValues": [{"label": "未启用", "value": "INACTIVE"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已停用", "value": "DISABLED"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "status", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "remark", "name": "描述", "type": "DataStructField", "alias": "remark", "appId": 46980, "props": {"length": 256, "unique": false, "comment": "描述", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "remark", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "fin_brm_group_item_list", "name": "业务规则组行表集合", "type": "DataStructField", "alias": "finBrmGroupItemList", "appId": 46980, "props": {"unique": false, "comment": "业务规则组行表集合", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "fin_brm_group_item_list", "compositeKey": false, "relationMeta": {"sync": true, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "ERP_GEN$gen_brm_rule_group_item_tr", "relationModelKey": "ERP_GEN$gen_brm_rule_group_item_tr", "currentModelAlias": "ERP_GEN$gen_brm_rule_group_head_tr", "relationModelAlias": "ERP_GEN$gen_brm_rule_group_item_tr", "linkModelFieldAlias": "genBrmRuleGroupHeadId", "currentModelFieldAlias": "finBrmGroupItemList"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "id", "name": "ID", "type": "DataStructField", "alias": "id", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "ID", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "id", "compositeKey": false, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}, {"ext": false, "key": "created_by", "name": "创建人", "type": "DataStructField", "alias": "created<PERSON>y", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "创建人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "created_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_brm_rule_group_head_tr", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "created<PERSON>y"}, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "updated_by", "name": "更新人", "type": "DataStructField", "alias": "updatedBy", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "更新人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "updated_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_brm_rule_group_head_tr", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "updatedBy"}, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "created_at", "name": "创建时间", "type": "DataStructField", "alias": "createdAt", "appId": 46980, "props": {"unique": true, "comment": "创建时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "created_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "updated_at", "name": "更新时间", "type": "DataStructField", "alias": "updatedAt", "appId": 46980, "props": {"unique": true, "comment": "更新时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "updated_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "version", "name": "版本号", "type": "DataStructField", "alias": "version", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "版本号", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "version", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}, {"ext": false, "key": "deleted", "name": "逻辑删除标识", "type": "DataStructField", "alias": "deleted", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "逻辑删除标识", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "deleted", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}, {"ext": false, "key": "origin_org_id", "name": "所属组织", "type": "DataStructField", "alias": "originOrgId", "appId": 46980, "props": {"length": 20, "unique": true, "comment": "所属组织", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "origin_org_id", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}]}}