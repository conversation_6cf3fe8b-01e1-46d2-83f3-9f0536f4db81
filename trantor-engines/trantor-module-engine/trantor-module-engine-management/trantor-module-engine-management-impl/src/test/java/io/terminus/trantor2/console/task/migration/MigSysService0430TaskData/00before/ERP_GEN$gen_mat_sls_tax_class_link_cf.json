{"type": "Model", "name": "物料销售视图税分类关联表", "parentKey": "ERP_GEN", "children": [], "props": {"desc": null, "alias": "ERP_GEN$gen_mat_sls_tax_class_link_cf", "props": {"type": "PERSIST", "config": {"self": false, "system": false, "persist": false, "selfRelationFieldAlias": null}, "mainField": "mat_tax_type_id", "tableName": "gen_mat_sls_tax_class_link_cf", "searchModel": false, "mainFieldAlias": "matTaxTypeId", "physicalDelete": false, "shardingConfig": {"enabled": false, "shardingFields": null, "shardingSuffixLength": 3}, "originOrgIdEnabled": true}, "children": [{"ext": false, "key": "mat_tax_type_id", "name": "物料税分类", "type": "DataStructField", "alias": "matTaxTypeId", "props": {"unique": false, "comment": "物料税分类", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "mat_tax_type_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_mat_tax_type_cf", "currentModelAlias": "ERP_GEN$gen_mat_sls_tax_class_link_cf", "relationModelAlias": "ERP_GEN$gen_mat_tax_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "matTaxTypeId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "tax_type_desc", "name": "分类描述", "type": "DataStructField", "alias": "taxTypeDesc", "props": {"length": 256, "unique": false, "comment": "分类描述", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "tax_type_desc", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "coun_id", "name": "国家", "type": "DataStructField", "alias": "counId", "props": {"unique": false, "comment": "国家", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "coun_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_coun_type_cf", "currentModelAlias": "ERP_GEN$gen_mat_sls_tax_class_link_cf", "relationModelAlias": "ERP_GEN$gen_coun_type_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "counId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "id", "name": "ID", "type": "DataStructField", "alias": "id", "props": {"length": 20, "unique": true, "comment": "ID", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "id", "compositeKey": false, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "created_by", "name": "创建人", "type": "DataStructField", "alias": "created<PERSON>y", "props": {"length": 20, "unique": true, "comment": "创建人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "created_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_mat_sls_tax_class_link_cf", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "created<PERSON>y"}, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "updated_by", "name": "更新人", "type": "DataStructField", "alias": "updatedBy", "props": {"length": 20, "unique": true, "comment": "更新人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "updated_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_mat_sls_tax_class_link_cf", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "updatedBy"}, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "created_at", "name": "创建时间", "type": "DataStructField", "alias": "createdAt", "props": {"unique": true, "comment": "创建时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "created_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "updated_at", "name": "更新时间", "type": "DataStructField", "alias": "updatedAt", "props": {"unique": true, "comment": "更新时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "updated_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "version", "name": "版本号", "type": "DataStructField", "alias": "version", "props": {"length": 20, "unique": true, "comment": "版本号", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "version", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "deleted", "name": "逻辑删除标识", "type": "DataStructField", "alias": "deleted", "props": {"length": 20, "unique": true, "comment": "逻辑删除标识", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "deleted", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "origin_org_id", "name": "所属组织", "type": "DataStructField", "alias": "originOrgId", "props": {"length": 20, "unique": true, "comment": "所属组织", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "origin_org_id", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}, {"ext": false, "key": "gen_mat_sls_md_id", "name": "genMatSlsMdId", "type": "DataStructField", "alias": "genMatSlsMdId", "props": {"unique": false, "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "gen_mat_sls_md_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_mat_sls_md", "currentModelAlias": "ERP_GEN$gen_mat_sls_tax_class_link_cf", "relationModelAlias": "ERP_GEN$gen_mat_sls_md", "linkModelFieldAlias": null, "currentModelFieldAlias": "genMatSlsMdId"}, "autoGenerated": true, "isSystemField": false}}]}}