{"type": "ServiceDefinition", "name": "计划策略配置-根据ID查找单表数据服务", "access": "Private", "parentKey": "ERP_PLN", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "serviceDslJson": {"children": [{"id": null, "key": "node_1horqbhng35", "name": "开始", "nextNodeKey": "node_1horqcdfb37", "props": {"globalVariable": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "modelKey": "ERP_PLN$pln_mrp_planning_strategy_cf", "relatedModel": {"modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "modelKey": "ERP_PLN$pln_mrp_planning_strategy_cf", "modelName": "ERP_PLN$pln_mrp_planning_strategy_cf"}, "relation": null, "required": null}], "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "modelKey": "ERP_PLN$pln_mrp_planning_strategy_cf", "relatedModel": {"modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "modelKey": "ERP_PLN$pln_mrp_planning_strategy_cf", "modelName": "ERP_PLN$pln_mrp_planning_strategy_cf"}, "relation": null, "required": null}], "type": "StartProperties"}, "type": "StartNode"}, {"id": null, "key": "node_1horqcdfb37", "name": "查询数据", "nextNodeKey": "node_1horqbhng36", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "OfibYE84yNDLN_Sx8_jN5", "key": null, "leftValue": {"fieldType": "Number", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "modelAlias": null, "relatedModel": {"modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "modelKey": "ERP_PLN$pln_mrp_planning_strategy_cf", "modelName": "ERP_PLN$pln_mrp_planning_strategy_cf"}, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "-c_2AErlwlSmklLooq0C8", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "sej2nLHSeRiC3xMr97xmH", "logicOperator": "OR", "type": "ConditionGroup"}, "dataType": "MODEL", "desensitized": true, "maximum": null, "outputAssign": {"customAssignments": [{"field": {"fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "modelAlias": null, "relatedModel": {"modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "modelKey": "ERP_PLN$pln_mrp_planning_strategy_cf", "modelName": "ERP_PLN$pln_mrp_planning_strategy_cf"}, "valueKey": "data", "valueName": "data"}]}, "id": "1horqdk7n38", "operator": "EQ", "value": {"fieldType": "Model", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": {"modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "modelKey": "ERP_PLN$pln_mrp_planning_strategy_cf", "modelName": "计划策略配置"}, "valueKey": "NODE_OUTPUT_node_1horqcdfb37", "valueName": "出参结构体"}]}}], "outputAssignType": "CUSTOM"}, "pageable": null, "queryModelFields": {"allFields": true, "modelKey": "ERP_PLN$pln_mrp_planning_strategy_cf", "queryFields": null}, "relatedModel": {"modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "modelKey": "ERP_PLN$pln_mrp_planning_strategy_cf", "modelName": "计划策略配置"}, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "type": "RetrieveDataNode"}, {"id": null, "key": "node_1horqbhng36", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "headNodeKeys": ["node_1horqbhng35"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "modelKey": "ERP_PLN$pln_mrp_planning_strategy_cf", "relatedModel": {"modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "modelKey": "ERP_PLN$pln_mrp_planning_strategy_cf", "modelName": "ERP_PLN$pln_mrp_planning_strategy_cf"}, "relation": null, "required": null}], "key": "ERP_PLN$PLN_MRP_PLANNING_STRATEGY_CF_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": "计划策略配置-根据ID查找单表数据服务", "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "modelKey": "ERP_PLN$pln_mrp_planning_strategy_cf", "relatedModel": {"modelAlias": "ERP_PLN$pln_mrp_planning_strategy_cf", "modelKey": "ERP_PLN$pln_mrp_planning_strategy_cf", "modelName": "ERP_PLN$pln_mrp_planning_strategy_cf"}, "relation": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}}