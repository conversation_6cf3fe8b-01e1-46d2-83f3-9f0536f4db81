{"type": "ServiceDefinition", "name": "现金流量项目表-分页数据服务", "access": "Private", "parentKey": "ERP_FI", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1h9a9hm2n23", "name": "开始", "nextNodeKey": "node_1h9aa6nhd27", "props": {"globalVariable": null, "input": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "conditionGroup", "fieldKey": "conditionGroup", "fieldName": "条件组", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "conditionItems", "fieldKey": "conditionItems", "fieldName": "简化版条件组", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "element": null, "fieldAlias": "sortOrders", "fieldKey": "sortOrders", "fieldName": "字段排序", "fieldType": "Array", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "pageNo", "fieldKey": "pageNo", "fieldName": "页码", "fieldType": "Number", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "pageSize", "fieldKey": "pageSize", "fieldName": "每页数量", "fieldType": "Number", "id": null, "required": null}], "fieldAlias": "pageable", "fieldKey": "pageable", "fieldName": "pageable", "fieldType": "Pageable", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}], "output": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "total", "fieldKey": "total", "fieldName": "total", "fieldType": "Number", "id": null, "required": null}, {"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Model", "id": null, "modelKey": "ERP_FI$fin_glm_cfs_type_cf", "relatedModel": {"modelAlias": "ERP_FI$fin_glm_cfs_type_cf", "modelKey": "ERP_FI$fin_glm_cfs_type_cf", "modelName": "现金流量项目表"}, "relation": null, "required": null}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null, "required": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Paging", "id": null, "required": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1h9aa6nhd27", "name": "查询数据", "nextNodeKey": "node_1h9a9hm2n24", "props": {"conditionGroup": null, "dataConditionPermissionKey": null, "dataType": "PAGING", "desensitized": true, "maximum": null, "outputAssign": {"customAssignments": [{"field": {"fieldType": "Paging", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}, "id": "1h9pl7l121", "operator": "EQ", "value": {"fieldType": "Paging", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "valueKey": "NODE_OUTPUT_node_1h9aa6nhd27", "valueName": "出参结构体"}]}}], "outputAssignType": "CUSTOM"}, "pageable": {"fieldType": "Pageable", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "valueKey": "pageable", "valueName": "pageable"}]}, "queryModelFields": {"allFields": false, "modelKey": "ERP_FI$fin_glm_cfs_type_cf", "queryFields": [{"fieldKey": "test"}, {"fieldKey": "test1"}, {"fieldKey": "abItemCode"}, {"fieldKey": "abItemName"}, {"fieldKey": "abItemType"}, {"fieldKey": "cashFlowDirec"}, {"fieldKey": "operActiv"}, {"fieldKey": "netProf"}, {"fieldKey": "excRateFlu"}, {"fieldKey": "estGenComId", "queryModelFields": {"allFields": false, "modelKey": null, "queryFields": [{"fieldKey": "id"}, {"fieldKey": "orgName"}]}}, {"fieldKey": "finGlmCfsTypeCf", "queryModelFields": {"allFields": false, "modelKey": null, "queryFields": [{"fieldKey": "id"}, {"fieldKey": "cfsTypeName"}]}}, {"fieldKey": "parentId", "queryModelFields": {"allFields": false, "modelKey": null, "queryFields": [{"fieldKey": "id"}, {"fieldKey": "abItemName"}]}}, {"fieldKey": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldKey": "finGlmAbCftOrgAssCf", "queryModelFields": {"allFields": false, "modelKey": null, "queryFields": [{"fieldKey": "estGenComId", "queryModelFields": {"allFields": false, "modelKey": null, "queryFields": [{"fieldKey": "id"}, {"fieldKey": "orgName"}]}}]}}]}, "relatedModel": {"modelAlias": "ERP_FI$fin_glm_cfs_type_cf", "modelKey": "ERP_FI$fin_glm_cfs_type_cf", "modelName": "现金流量项目表"}, "sortOrders": [{"fieldAlias": "id", "sortType": "DESC"}], "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "type": "RetrieveDataNode"}, {"desc": null, "id": null, "key": "node_1h9a9hm2n24", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "headNodeKeys": ["node_1h9a9hm2n23"], "id": null, "input": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "conditionGroup", "fieldKey": "conditionGroup", "fieldName": "条件组", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "conditionItems", "fieldKey": "conditionItems", "fieldName": "简化版条件组", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "element": null, "fieldAlias": "sortOrders", "fieldKey": "sortOrders", "fieldName": "字段排序", "fieldType": "Array", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "pageNo", "fieldKey": "pageNo", "fieldName": "页码", "fieldType": "Number", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "pageSize", "fieldKey": "pageSize", "fieldName": "每页数量", "fieldType": "Number", "id": null, "required": null}], "fieldAlias": "pageable", "fieldKey": "pageable", "fieldName": "pageable", "fieldType": "Pageable", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}], "key": "ERP_FI$FIN_GLM_CFS_TYPE_CF_PAGING_DATA_SERVICE", "name": "现金流量项目表-分页数据服务", "output": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "total", "fieldKey": "total", "fieldName": "total", "fieldType": "Number", "id": null, "required": null}, {"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Model", "id": null, "modelKey": "ERP_FI$fin_glm_cfs_type_cf", "relatedModel": {"modelAlias": "ERP_FI$fin_glm_cfs_type_cf", "modelKey": "ERP_FI$fin_glm_cfs_type_cf", "modelName": "现金流量项目表"}, "relation": null, "required": null}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null, "required": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Paging", "id": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "fieldRules": null, "permissionKey": "ERP_FI$FIN_GLM_CFS_TYPE_CF_RETRIEVE_PERMISSION", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}}