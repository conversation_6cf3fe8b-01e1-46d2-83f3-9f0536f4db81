{"type": "ServiceDefinition", "name": "组织架构表-根据ID查找数据服务", "access": "Public", "parentKey": "sys_common", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1horqbhng35", "name": "开始", "nextNodeKey": "node_1horqcdfb37", "props": {"globalVariable": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "modelKey": "sys_common$org_struct_md", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "sys_common$org_struct_md"}, "relation": null, "required": null}], "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "modelKey": "sys_common$org_struct_md", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "sys_common$org_struct_md"}, "relation": null, "required": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1horqcdfb37", "name": "查询数据", "nextNodeKey": "node_1horqbhng36", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "OfibYE84yNDLN_Sx8_jN5", "key": null, "leftValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "modelAlias": "sys_common$org_struct_md", "valueKey": "id", "valueName": "ID"}]}, "operator": "EQ", "rightValue": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "sys_common$org_struct_md"}, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "modelAlias": "sys_common$org_struct_md", "relatedModel": {"modelAlias": null, "modelKey": null, "modelName": null}, "valueKey": "id", "valueName": "ID"}]}, "rightValues": null, "type": "ConditionLeaf"}], "id": "-c_2AErlwlSmklLooq0C8", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "sej2nLHSeRiC3xMr97xmH", "logicOperator": "OR", "type": "ConditionGroup"}, "dataConditionPermissionKey": "null", "dataType": "MODEL", "desensitized": true, "maximum": null, "outputAssign": {"customAssignments": [{"field": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "sys_common$org_struct_md"}, "valueKey": "data", "valueName": "data"}]}, "id": "1horqdk7n38", "operator": "EQ", "value": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "组织架构表"}, "valueKey": "NODE_OUTPUT_node_1horqcdfb37", "valueName": "出参结构体"}]}}], "outputAssignType": "CUSTOM"}, "pageable": null, "queryModelFields": {"allFields": true, "modelKey": "sys_common$org_struct_md", "queryFields": null}, "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "组织架构表"}, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": null, "type": "RetrieveDataProperties"}, "type": "RetrieveDataNode"}, {"desc": null, "id": null, "key": "node_1horqbhng36", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "headNodeKeys": ["node_1horqbhng35"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "modelKey": "sys_common$org_struct_md", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "sys_common$org_struct_md"}, "relation": null, "required": null}], "key": "sys_common$ORG_STRUCT_MD_FIND_DATA_BY_ID_SERVICE", "name": "组织架构表-根据ID查找数据服务", "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "modelKey": "sys_common$org_struct_md", "relatedModel": {"modelAlias": "sys_common$org_struct_md", "modelKey": "sys_common$org_struct_md", "modelName": "sys_common$org_struct_md"}, "relation": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "fieldRules": null, "permissionKey": "sys_common$ORG_STRUCT_MD_RETRIEVE_PERMISSION", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}}