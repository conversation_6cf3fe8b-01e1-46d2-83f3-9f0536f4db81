{"parentKey": "CON", "name": "回写已生成的商品id", "type": "ServiceDefinition", "key": "CON$srm_generated_product_id", "props": {"serviceDslJson": {"input": [{"fieldAlias": "request", "fieldName": "request", "fieldKey": "request", "fieldType": "Model", "relatedModel": {"modelAlias": "CON$con_pri_agreement_item_tr", "modelKey": "CON$con_pri_agreement_item_tr"}}], "children": [{"nextNodeKey": "node_1hjrk7b7423", "name": "开始", "type": "StartNode", "key": "node_1hjrk5ogq21", "props": {"input": [{"fieldAlias": "request", "fieldName": "request", "fieldKey": "request", "fieldType": "Model", "relatedModel": {"modelAlias": "CON$con_pri_agreement_item_tr", "modelKey": "CON$con_pri_agreement_item_tr"}}], "type": "StartProperties"}}, {"nextNodeKey": "node_1hjrk5ogq22", "name": "更新数据", "type": "CascadeUpdateDataNode", "key": "node_1hjrk7b7423", "props": {"modelValue": {"varValue": [{"valueName": "服务入参", "valueKey": "REQUEST"}, {"valueName": "request", "valueKey": "request", "relatedModel": {"modelAlias": "CON$con_pri_agreement_item_tr", "modelKey": "CON$con_pri_agreement_item_tr"}}], "valueType": "VAR", "type": "VarValue", "fieldType": "Model"}, "type": "CascadeUpdateDataProperties", "relatedModel": {"modelName": "采购价格协议物料行项目", "modelAlias": "CON$con_pri_agreement_item_tr", "modelKey": "CON$con_pri_agreement_item_tr"}}}, {"name": "结束", "type": "EndNode", "key": "node_1hjrk5ogq22", "props": {"type": "EndProperties"}}], "name": "回写已生成的商品id", "type": "ServiceDefinition", "headNodeKeys": ["node_1hjrk5ogq21"], "key": "TSRM$srm_generated_product_id", "props": {"transactionPropagation": "REQUIRED", "teamId": 35, "type": "ServiceProperties"}}, "serviceType": "PROGRAMMABLE", "isEnabled": true, "serviceDslMd5": "null", "modelKey": "null"}}