{"type": "ServiceDefinition", "name": "物料销售视图-根据ID删除数据服务", "access": "Private", "parentKey": "ERP_SCM", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1horpproo21", "name": "开始", "nextNodeKey": "node_1horprssq23", "props": {"globalVariable": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "modelKey": "ERP_SCM$sls_so_head_tr", "relatedModel": {"modelAlias": "ERP_SCM$sls_so_head_tr", "modelKey": "ERP_SCM$sls_so_head_tr", "modelName": "ERP_SCM$sls_so_head_tr"}, "relation": null, "required": null}], "output": [], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1horprssq23", "name": "删除数据", "nextNodeKey": "node_1horpproo22", "props": {"conditionGroup": null, "dataConditionPermissionKey": "null", "modelValue": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "relatedModel": {"modelAlias": "ERP_SCM$sls_so_head_tr", "modelKey": "ERP_SCM$sls_so_head_tr", "modelName": "ERP_SCM$sls_so_head_tr"}, "valueKey": "request", "valueName": "request"}]}, "outputAssign": null, "relatedModel": {"modelAlias": "ERP_SCM$sls_so_head_tr", "modelKey": "ERP_SCM$sls_so_head_tr", "modelName": "物料销售视图"}, "type": "CascadeDeleteDataProperties"}, "type": "CascadeDeleteDataNode"}, {"desc": null, "id": null, "key": "node_1horpproo22", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "headNodeKeys": ["node_1horpproo21"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "modelKey": "ERP_SCM$sls_so_head_tr", "relatedModel": {"modelAlias": "ERP_SCM$sls_so_head_tr", "modelKey": "ERP_SCM$sls_so_head_tr", "modelName": "ERP_SCM$sls_so_head_tr"}, "relation": null, "required": null}], "key": "ERP_SCM$SLS_SO_HEAD_TR_DELETE_DATA_BY_ID_SERVICE", "name": "物料销售视图-根据ID删除数据服务", "output": null, "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "fieldRules": null, "permissionKey": "ERP_SCM$SLS_SO_HEAD_TR_DELETE_PERMISSION", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}}