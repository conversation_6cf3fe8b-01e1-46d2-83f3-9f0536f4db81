{"access": "Private", "parentKey": "CON", "name": "保存和更新价格协议行事件", "type": "Event", "key": "CON$PRICE_AGTITEM_CRTUPD_ONE_EVENT", "props": {"returnModel": {"key": "CON$con_pri_agreement_item_tr"}, "returnModelArrayWhether": false, "model": {"key": "CON$con_pri_agreement_item_tr"}, "enabledStatusVerify": false, "modelArrayWhether": false, "enabledTransaction": true, "relations": [{"actionType": "Action", "code": "CON$TSRM_PRICE_AGTITEM_UPDATE_ACTION", "enabledParamCheck": false}], "states": []}}