{"type": "ServiceDefinition", "name": "ABC分组方案-批量删除数据服务", "access": "Private", "parentKey": "ERP_SCM", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1hothhu5c13", "name": "开始", "nextNodeKey": "node_1hothk43k15", "props": {"globalVariable": [{"defaultValue": null, "description": null, "fieldAlias": "mat", "fieldKey": "mat", "fieldName": "mat", "fieldType": "Model", "id": null, "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "relatedModel": {"modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelName": "ERP_SCM$inv_abc_group_strategy_head_cf"}, "relation": null, "required": null}], "input": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Number", "id": null, "required": null}, "fieldAlias": "ids", "fieldKey": "ids", "fieldName": "ids", "fieldType": "Array", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}], "output": [], "type": "StartProperties"}, "type": "StartNode"}, {"children": [{"desc": null, "id": null, "key": "node_1hothlf1u18", "name": "赋值", "nextNodeKey": "node_1hothl3hr16", "props": {"assignments": [{"field": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelName": "ERP_SCM$inv_abc_group_strategy_head_cf"}, "valueKey": "mat", "valueName": "mat"}, {"fieldType": null, "modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "valueKey": "id", "valueName": "ID"}]}, "id": "1hothlg6g19", "operator": "EQ", "value": {"fieldType": "Number", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "LOOP_node_1hothk43k15", "valueName": "[循环]循环变量"}, {"fieldType": null, "valueKey": "_item", "valueName": "循环元素"}]}}], "type": "AssignProperties"}, "type": "AssignNode"}, {"desc": null, "id": null, "key": "node_1hothl3hr16", "name": "删除数据", "props": {"conditionGroup": null, "dataConditionPermissionKey": "null", "modelValue": {"fieldType": "Model", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelName": "ERP_SCM$inv_abc_group_strategy_head_cf"}, "valueKey": "mat", "valueName": "mat"}]}, "outputAssign": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelKey": "ERP_SCM$inv_abc_group_strategy_head_cf", "modelName": "ABC分组方案"}, "type": "CascadeDeleteDataProperties"}, "type": "CascadeDeleteDataNode"}], "desc": null, "headNodeKeys": ["node_1hothlf1u18"], "id": null, "key": "node_1hothk43k15", "name": "循环", "nextNodeKey": "node_1hothhu5c14", "props": {"loopData": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "valueKey": "ids", "valueName": "ids"}]}, "loopElement": {"defaultValue": null, "description": null, "fieldAlias": "ids", "fieldKey": "ids", "fieldName": "ids", "fieldType": "Number", "id": null, "required": null}, "loopType": "DATASET_LOOP", "stopWhenDataEmpty": false, "type": "LoopProperties"}, "type": "LoopNode"}, {"desc": null, "id": null, "key": "node_1hothhu5c14", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "headNodeKeys": ["node_1hothhu5c13"], "id": null, "input": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Number", "id": null, "required": null}, "fieldAlias": "ids", "fieldKey": "ids", "fieldName": "ids", "fieldType": "Array", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}], "key": "ERP_SCM$INV_ABC_GROUP_STRATEGY_HEAD_CF_BATCH_DELETE_DATA_SERVICE", "name": "ABC分组方案-批量删除数据服务", "output": null, "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "fieldRules": null, "permissionKey": "ERP_SCM$INV_ABC_GROUP_STRATEGY_HEAD_CF_DELETE_PERMISSION", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}}