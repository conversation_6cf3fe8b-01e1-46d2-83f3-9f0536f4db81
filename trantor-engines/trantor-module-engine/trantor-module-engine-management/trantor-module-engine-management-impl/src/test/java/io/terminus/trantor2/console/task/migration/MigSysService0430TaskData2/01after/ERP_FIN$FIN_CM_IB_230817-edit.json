{"type": "View", "name": "ERP_FIN$FIN_CM_IB_230817-edit", "parentKey": "ERP_FIN", "children": [], "props": {"conditionGroups": {"5IBkj8zpP3fI8Ap0o5MF-": {"conditions": [{"conditions": [{"id": "vVVKD9feLcLI0gy5j-zWP", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgBusinessTypeCode", "fieldName": "orgBusinessTypeCode", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgBusinessTypeCode", "valueName": "orgBusinessTypeCode"}]}, "operator": "EQ", "rightValue": {"constValue": "COM_ORG", "fieldType": "Text", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "-tTzwQCnIU8sqYrC8z97C", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgStatus", "fieldName": "orgStatus", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "fHMnMDRwTNxRwBetk7Em3", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "5IBkj8zpP3fI8Ap0o5MF-", "logicOperator": "OR", "type": "ConditionGroup"}}, "containerSelect": {"TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr": [{"field": "version", "selectFields": null}, {"field": "ibCode", "selectFields": null}, {"field": "ibDate", "selectFields": null}, {"field": "comOrg", "selectFields": [{"field": "id", "selectFields": null}, {"field": "orgName", "selectFields": null}]}, {"field": "baseCurr", "selectFields": [{"field": "id", "selectFields": null}, {"field": "currName", "selectFields": null}]}, {"field": "alCode", "selectFields": [{"field": "id", "selectFields": null}, {"field": "alCode", "selectFields": null}]}, {"field": "accountType", "selectFields": null}, {"field": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectFields": [{"field": "id", "selectFields": null}, {"field": "currName", "selectFields": null}]}, {"field": "exchRate", "selectFields": null}, {"field": "initialBalanceDocAmt", "selectFields": null}, {"field": "initialBalanceBaseAmt", "selectFields": null}]}, "content": {"children": [{"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-editView-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑账户期初余额确认单头表\" : \"创建账户期初余额确认单头表\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-id", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "id", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-createdBy", "name": "FormField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "ERP_FIN$user", "parentModelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-updatedBy", "name": "FormField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "ERP_FIN$user", "parentModelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-createdAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "createdAt", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-updatedAt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "updatedAt", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [{"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-ibCode", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "ibCode", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "initialValue": null, "label": "账户期初单单号", "lookup": [{"action": "get", "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "49AGNvjpAu7cxTR1ted6M", "operator": "SERVICE", "valueRules": {"scope": null, "serviceParams": {"entryNewParams": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_FIN$fin_cm_ib_head_tr"}, {"name": "request.rule<PERSON>ey", "type": "const", "value": "ERP_FIN$fin_cm_ib_code"}], "outParams": "data"}, "title": "(系统)调用取号规则服务", "type": "SERVICE", "val": "ERP_FIN$FIN_CM_IB_HEAD_TR_INVOKE_CODE_RULE_SERVICE", "value": "ERP_FIN$FIN_CM_IB_HEAD_TR_INVOKE_CODE_RULE_SERVICE"}}], "name": "ibCode", "rules": [{"message": "请输入账户期初单单号", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-ibDate", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "ibDate", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请选择"}, "displayComponentType": "Date", "editComponentType": "DatePicker", "hidden": false, "initialValue": null, "label": "业务日期", "name": "ibDate", "rules": [{"message": "请输入业务日期", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-comOrg", "name": "FormField", "props": {"componentProps": {"columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status", "counId"], "fieldAlias": "comOrg", "label": "选择公司组织", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$ORG_STRUCT_MD_FIND_DATA_BY_ID_SERVICE", "searchServiceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE"}}, "displayComponentProps": {"labelField": ["orgName"], "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "组织编码", "name": "orgCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "组织名称", "name": "orgName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgStatus", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": false, "isRelationColumn": true, "label": "状态", "name": "orgStatus", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"defaultValue": true, "fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": false, "initialValue": true, "isRelationColumn": true, "label": "是否叶子节点", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "type": "BOOL", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织编码", "name": "orgCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织名称", "name": "orgName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgStatus", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": false, "label": "状态", "name": "orgStatus", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"defaultValue": true, "fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": false, "initialValue": true, "label": "是否叶子节点", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "type": "BOOL", "width": 120}], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "orgBusinessTypeCodes", "fieldName": "orgBusinessTypeCodes", "fieldType": "Text", "valueConfig": {"type": "const", "value": "COM_ORG"}}, {"fieldAlias": "orgStatus", "fieldName": "orgStatus", "fieldType": "Text", "valueConfig": {"type": "const", "value": "ENABLED"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "type": "InvokeService"}, "labelField": ["orgName"], "modelAlias": "sys_common$org_struct_md", "reverseConstructFlow": {"context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [], "fieldAlias": "request", "fieldName": "request", "fieldType": "Pageable"}], "serviceKey": "sys_common$ORG_STRUCT_MD_REVERSE_CONSTRUCT_TREE_SERVICE", "type": "InvokeService"}, "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "vVVKD9feLcLI0gy5j-zWP", "leftValue": {"fieldType": "Text", "title": "业务类型编码", "type": "VarValue", "val": "orgBusinessTypeCode", "value": "sys_common$org_struct_md.orgBusinessTypeCode", "valueType": "VAR", "varVal": "orgBusinessTypeCode", "varValue": [{"valueKey": "orgBusinessTypeCode", "valueName": "orgBusinessTypeCode"}]}, "operator": "EQ", "rightValue": {"constValue": "COM_ORG", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "-tTzwQCnIU8sqYrC8z97C", "leftValue": {"fieldType": "Enum", "title": "状态", "type": "VarValue", "val": "orgStatus", "value": "ERP_GEN$org_struct_md.orgStatus", "valueType": "VAR", "varVal": "orgStatus", "varValue": [{"valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "fHMnMDRwTNxRwBetk7Em3", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "5IBkj8zpP3fI8Ap0o5MF-", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": "$context"}, "editComponentType": "RelationSelect", "hidden": false, "label": "公司组织", "name": "comOrg", "rules": [{"message": "请输入公司组织", "required": true}], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-baseCurr", "name": "FormField", "props": {"componentProps": {"columns": ["currCode", "currName", "currIsoName", "decimalPlace", "symbol", "remark"], "fieldAlias": "baseCurr", "label": "选择本位币别", "labelField": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "parentModelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}}, "displayComponentProps": {"labelField": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "currCode", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "币别编码", "name": "currCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "名称", "name": "currName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "currIsoName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "ISO名称", "name": "currIsoName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "decimalPlace", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "小数位", "name": "decimalPlace", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "symbol", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "币种符号", "name": "symbol", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "remark", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "isRelationColumn": true, "label": "备注", "name": "remark", "required": true, "type": "TEXT", "width": 120}], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_GEN$gen_curr_type_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_curr_type_cf"}}], "serviceKey": "ERP_FIN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "currName", "mainField": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "本位币别", "lookup": [{"action": "get", "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "PIQsYs6hoBj_0Gd-_wcUI", "operator": "SERVICE", "valueRules": {"scope": null, "serviceParams": {"entryNewParams": [{"fieldAlias": "id", "fieldName": "公司组织", "fieldType": "Number", "required": null, "valueConfig": {"action": {"selector": "comOrg.id", "target": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr"}, "type": "action"}}], "outputParams": {"expression": "", "serviceKey": "ERP_GEN$GEN_CURR_FROM_COM_ORG_FIELD_SERVICE", "type": "expression"}}, "type": "SERVICE", "val": "ERP_GEN$GEN_CURR_FROM_COM_ORG_FIELD_SERVICE", "value": "ERP_GEN$GEN_CURR_FROM_COM_ORG_FIELD_SERVICE"}}], "name": "baseCurr", "rules": [], "type": "OBJECT"}, "type": "Widget"}], "key": "IDLKA9RnNKM9ZoMsk3cvK", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "title": "基本信息"}, "type": "Layout"}, {"children": [{"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-alCode", "name": "FormField", "props": {"componentProps": {"columns": ["alCode", "alName", "accountCode", "accountType", "accountAttribute", "comOrgId", "currId", "ccsHeadCode"], "fieldAlias": "alCode", "label": "选择余额账户", "labelField": "alCode", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "parentModelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}}, "displayComponentProps": {"labelField": "alName"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "alCode", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "账户编码", "name": "alCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "alName", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "账户名称", "name": "alName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "accountCode", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "账户号", "name": "accountCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "accountType", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "账户类型", "name": "accountType", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "accountAttribute", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "账户属性", "name": "accountAttribute", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status", "counId"], "fieldAlias": "comOrgId", "label": "选择公司组织", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$ORG_STRUCT_MD_FIND_DATA_BY_ID_SERVICE", "searchServiceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE"}}, "hidden": false, "initialValue": null, "label": "公司组织", "name": "comOrgId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["currCode", "currName", "currIsoName", "decimalPlace", "symbol", "remark"], "fieldAlias": "currId", "label": "选择币别", "labelField": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "parentModelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "币别", "name": "currId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["ccsHeadCode", "ccsName", "currId"], "fieldAlias": "ccsHeadCode", "label": "选择现金盘点策略编码", "labelField": "ccsHeadCode", "modelAlias": "ERP_FIN$fin_cm_ccs_head_cf", "parentModelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "现金盘点策略编码", "name": "ccsHeadCode", "required": false, "type": "OBJECT", "width": 120}], "filterFields": [{"componentProps": {"defaultValue": null, "fieldAlias": "alCode", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "账户编码", "name": "alCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "alName", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "账户名称", "name": "alName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "accountCode", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "账户号", "name": "accountCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "accountType", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "账户类型", "name": "accountType", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "accountAttribute", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "账户属性", "name": "accountAttribute", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"columns": ["code", "name", "currId", "comId", "chaccId", "comPeriodId", "status", "counId"], "fieldAlias": "comOrgId", "label": "选择公司组织", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$ORG_STRUCT_MD_FIND_DATA_BY_ID_SERVICE", "searchServiceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE"}}, "hidden": false, "initialValue": null, "label": "公司组织", "name": "comOrgId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["currCode", "currName", "currIsoName", "decimalPlace", "symbol", "remark"], "fieldAlias": "currId", "label": "选择币别", "labelField": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "parentModelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "币别", "name": "currId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["ccsHeadCode", "ccsName", "currId"], "fieldAlias": "ccsHeadCode", "label": "选择现金盘点策略编码", "labelField": "ccsHeadCode", "modelAlias": "ERP_FIN$fin_cm_ccs_head_cf", "parentModelAlias": "ERP_FIN$fin_cm_al_type_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "现金盘点策略编码", "name": "ccsHeadCode", "required": false, "type": "OBJECT", "width": 120}], "flow": {"context$": "$context", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "serviceKey": "ERP_FIN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "alName", "mainField": "alCode", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "余额账户", "name": "alCode", "rules": [{"message": "请输入余额账户", "required": true}], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-accountType", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "accountType", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请选择"}, "displayComponentType": "Enum", "editComponentType": "Select", "hidden": false, "initialValue": null, "label": "账户类型", "lookup": [{"action": "get", "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "7b7sCrzca3-n3BRto8iRx", "operator": "FIELD", "valueRules": {"scope": "form", "type": "FIELD", "val": "alCode.accountType", "value": "ERP_FIN$fin_cm_ib_head_tr.alCode.accountType", "valueType": "model"}}], "name": "accountType", "rules": [{"message": "请输入账户类型", "required": true}], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-docCurr", "name": "FormField", "props": {"componentProps": {"columns": ["currCode", "currName", "currIsoName", "decimalPlace", "symbol", "remark"], "fieldAlias": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "选择单据币别", "labelField": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "parentModelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FIN$SYS_FindDataByIdService", "searchServiceKey": "ERP_FIN$SYS_PagingDataService"}}, "displayComponentProps": {"labelField": "currName"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"defaultValue": null, "fieldAlias": "currCode", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "币别编码", "name": "currCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "名称", "name": "currName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "currIsoName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "ISO名称", "name": "currIsoName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "decimalPlace", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "小数位", "name": "decimalPlace", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "symbol", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "币种符号", "name": "symbol", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"defaultValue": null, "fieldAlias": "remark", "modelAlias": "ERP_GEN$gen_curr_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "备注", "name": "remark", "required": true, "type": "TEXT", "width": 120}], "flow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_FIN$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "currName", "mainField": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "showScope": "all", "tableCondition": null}, "editComponentType": "RelationSelect", "hidden": false, "initialValue": null, "label": "单据币别", "lookup": [{"action": "get", "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "fpWhB1JOKklLqOuWYlJT3", "operator": "FIELD", "valueRules": {"scope": "form", "type": "FIELD", "val": "alCode.currId", "value": "ERP_FIN$fin_cm_ib_head_tr.alCode.currId", "valueType": "model"}}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-exchRate", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "exchRate", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请输入", "precision": 6}, "displayComponentProps": {"precision": 6}, "displayComponentType": "Number", "editComponentProps": {"precision": 6}, "editComponentType": "InputNumber", "hidden": false, "initialValue": null, "label": "汇率", "lookup": [{"action": "get", "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "eTRBIHRpwMxgyBCqkzd7F", "operator": "SERVICE", "valueRules": {"scope": null, "serviceParams": {"entryNewParams": [{"action": {"selector": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr"}, "fieldType": "Number", "name": "base_curr", "type": "action"}, {"action": {"selector": "baseCurr", "target": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr"}, "fieldType": "Number", "name": "target_curr", "type": "action"}], "outParams": "data", "outType": "Number"}, "title": "根据基本币种与目标币种获取汇率", "type": "SERVICE", "val": "ERP_GEN$GET_RATE_FROM_CURR_SERVICE", "value": "ERP_GEN$GET_RATE_FROM_CURR_SERVICE"}}], "name": "exchRate", "rules": [], "type": "NUMBER"}, "type": "Widget"}], "key": "fVS_LtydQYhEwDL8KDKsx", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "title": "账户信息"}, "type": "Layout"}, {"children": [{"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-initialBalanceDocAmt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "initialBalanceDocAmt", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请输入", "precision": 6}, "displayComponentProps": {"precision": 2}, "displayComponentType": "Number", "editComponentProps": {"precision": 2}, "editComponentType": "InputNumber", "hidden": false, "initialValue": null, "label": "账户期初余额", "name": "initialBalanceDocAmt", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [{"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-initialBalanceBaseAmt", "name": "FormField", "props": {"componentProps": {"defaultValue": null, "fieldAlias": "initialBalanceBaseAmt", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "placeholder": "请输入", "precision": 6}, "displayComponentProps": {"precision": 2}, "displayComponentType": "Number", "editComponentProps": {"precision": 2}, "editComponentType": "InputNumber", "hidden": false, "initialValue": null, "label": "账户期初余额", "lookup": [{"action": "get", "conditionGroup": null, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "EN4j8p1K7GAL-VtUfqkKX", "operator": "FIELD", "valueRules": {"scope": "form", "type": "FIELD", "val": "initialBalanceDocAmt", "value": "ERP_FIN$fin_cm_ib_head_tr.initialBalanceDocAmt", "valueType": "model"}}], "name": "initialBalanceBaseAmt", "rules": [], "type": "NUMBER"}, "type": "Widget"}], "key": "4_lqLaijs1rqQAcy5bFc8", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "title": "期初余额本位币"}, "type": "Layout"}], "key": "NA0CTISOB5L_Z-iFikiof", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "title": "期初余额"}, "type": "Layout"}], "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "title": false}, "type": "Layout"}], "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "name": "FormGroup", "props": {"colon": false, "flow": {"containerKey": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "params$": "{ id: route.query?.copyId || route.recordId }", "serviceKey$": "route.query?.copyId ? \"ERP_FIN$SYS_CopyDataConverterService\" : \"ERP_FIN$SYS_FindDataByIdService\"", "test$": "route.action === \"edit\" || !!route.query?.copyId", "type": "InvokeSystemService"}, "modelAlias": "ERP_FIN$fin_cm_ib_head_tr", "serviceKey": "ERP_FIN$SYS_FindDataByIdService"}, "type": "Container"}, {"children": [{"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-editView-footer-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "previous"}]}, "label": "取消"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-editView-footer-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindFlowConfig": {"params": [{"action": {"target": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr"}, "name": "request", "type": "action"}], "service": "ERP_FIN$IB_SUBMIT_EVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "list"}, {"action": "Refresh", "target": "TERP_MIGRATE$FIN_CM_IB_230817-editView"}], "executeLogic": "BindFlow"}, "label": "保存", "type": "primary"}, "type": "Widget"}], "key": "TERP_MIGRATE$FIN_CM_IB_230817-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "name": "Page", "props": {"showFooter": true, "showHeader": true}, "type": "Container"}, "frontendConfig": {"modules": ["base"]}, "key": "ERP_FIN$FIN_CM_IB_230817-edit", "resources": [{"description": null, "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "label": "表单组", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "label": "页面", "type": "Page"}], "relations": [], "type": "Container"}, {"description": null, "key": "TERP_MIGRATE$FIN_CM_IB_230817-editView-footer-cancel", "label": "取消", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "label": "页面", "type": "Page"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TERP_MIGRATE$FIN_CM_IB_230817-editView-footer-save", "label": "保存", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "label": "页面", "type": "Page"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}], "relations": [{"key": "ERP_FIN$IB_SUBMIT_EVENT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-id", "label": "ID", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "label": "页面", "type": "Page"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "label": "表单组", "type": "FormGroup"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-createdBy", "label": "创建人", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "label": "页面", "type": "Page"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "label": "表单组", "type": "FormGroup"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "ERP_FIN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FIN$user"}, "type": "SystemService"}, {"key": "ERP_FIN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FIN$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-updatedBy", "label": "更新人", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "label": "页面", "type": "Page"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "label": "表单组", "type": "FormGroup"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "ERP_FIN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FIN$user"}, "type": "SystemService"}, {"key": "ERP_FIN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FIN$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-createdAt", "label": "创建时间", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "label": "页面", "type": "Page"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "label": "表单组", "type": "FormGroup"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-updatedAt", "label": "更新时间", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "label": "页面", "type": "Page"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "label": "表单组", "type": "FormGroup"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-version", "label": "版本号", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "label": "页面", "type": "Page"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "label": "表单组", "type": "FormGroup"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-deleted", "label": "逻辑删除标识", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "label": "页面", "type": "Page"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "label": "表单组", "type": "FormGroup"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-ibCode", "label": "账户期初单单号", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "label": "页面", "type": "Page"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "label": "表单组", "type": "FormGroup"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "label": false, "type": "FormGroupItem"}, {"key": "IDLKA9RnNKM9ZoMsk3cvK", "label": "基本信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_FIN$FIN_CM_IB_HEAD_TR_INVOKE_CODE_RULE_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FIN$fin_cm_ib_head_tr"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-ibDate", "label": "业务日期", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "label": "页面", "type": "Page"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "label": "表单组", "type": "FormGroup"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "label": false, "type": "FormGroupItem"}, {"key": "IDLKA9RnNKM9ZoMsk3cvK", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-comOrg", "label": "公司组织", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "label": "页面", "type": "Page"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "label": "表单组", "type": "FormGroup"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "label": false, "type": "FormGroupItem"}, {"key": "IDLKA9RnNKM9ZoMsk3cvK", "label": "基本信息", "type": "FormGroupItem"}], "relations": [{"key": "sys_common$ORG_STRUCT_MD_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}, {"key": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}, {"key": "sys_common$ORG_STRUCT_MD_REVERSE_CONSTRUCT_TREE_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-baseCurr", "label": "本位币别", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "label": "页面", "type": "Page"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "label": "表单组", "type": "FormGroup"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "label": false, "type": "FormGroupItem"}, {"key": "IDLKA9RnNKM9ZoMsk3cvK", "label": "基本信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_FIN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "SystemService"}, {"key": "ERP_FIN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$GEN_CURR_FROM_COM_ORG_FIELD_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-alCode", "label": "余额账户", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "label": "页面", "type": "Page"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "label": "表单组", "type": "FormGroup"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "label": false, "type": "FormGroupItem"}, {"key": "fVS_LtydQYhEwDL8KDKsx", "label": "账户信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_FIN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FIN$fin_cm_al_type_md"}, "type": "SystemService"}, {"key": "ERP_FIN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FIN$fin_cm_al_type_md"}, "type": "SystemService"}, {"key": "sys_common$ORG_STRUCT_MD_FIND_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}, {"key": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}, {"key": "ERP_FIN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "SystemService"}, {"key": "ERP_FIN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "SystemService"}, {"key": "ERP_FIN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FIN$fin_cm_ccs_head_cf"}, "type": "SystemService"}, {"key": "ERP_FIN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FIN$fin_cm_ccs_head_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-accountType", "label": "账户类型", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "label": "页面", "type": "Page"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "label": "表单组", "type": "FormGroup"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "label": false, "type": "FormGroupItem"}, {"key": "fVS_LtydQYhEwDL8KDKsx", "label": "账户信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-docCurr", "label": "单据币别", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "label": "页面", "type": "Page"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "label": "表单组", "type": "FormGroup"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "label": false, "type": "FormGroupItem"}, {"key": "fVS_LtydQYhEwDL8KDKsx", "label": "账户信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_FIN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "SystemService"}, {"key": "ERP_FIN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-exchRate", "label": "汇率", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "label": "页面", "type": "Page"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "label": "表单组", "type": "FormGroup"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "label": false, "type": "FormGroupItem"}, {"key": "fVS_LtydQYhEwDL8KDKsx", "label": "账户信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$GET_RATE_FROM_CURR_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "Container"}, {"description": null, "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-initialBalanceDocAmt", "label": "账户期初余额", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "label": "页面", "type": "Page"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "label": "表单组", "type": "FormGroup"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "label": false, "type": "FormGroupItem"}, {"key": "NA0CTISOB5L_Z-iFikiof", "label": "期初余额", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr-for-widget-initialBalanceBaseAmt", "label": "账户期初余额", "path": [{"key": "TERP_MIGRATE$FIN_CM_IB_230817-editView", "label": "页面", "type": "Page"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr", "label": "表单组", "type": "FormGroup"}, {"key": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-container-ERP_FIN$fin_cm_ib_head_tr", "label": false, "type": "FormGroupItem"}, {"key": "NA0CTISOB5L_Z-iFikiof", "label": "期初余额", "type": "FormGroupItem"}, {"key": "4_lqLaijs1rqQAcy5bFc8", "label": "期初余额本位币", "type": "FormGroupItem"}], "relations": [], "type": "Container"}], "title": "edit", "type": "FORM"}}