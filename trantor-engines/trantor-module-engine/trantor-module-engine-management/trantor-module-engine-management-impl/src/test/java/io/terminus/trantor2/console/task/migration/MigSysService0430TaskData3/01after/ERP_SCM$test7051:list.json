{"type": "View", "name": "list", "parentKey": "ERP_SCM", "children": [], "props": {"containerSelect": {"ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail": [{"field": "matCateCode", "selectFields": null}, {"field": "matCateName", "selectFields": null}, {"field": "status", "selectFields": null}, {"field": "genCounClassMatTaxItemCfId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "taxType", "selectFields": null}]}, {"field": "qualificationsGroupId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "qualificationsHeadCode", "selectFields": null}]}, {"field": "isLimitPoQualifications", "selectFields": null}, {"field": "isLimitSoQualifications", "selectFields": null}, {"field": "isLimitCtQualifications", "selectFields": null}, {"field": "path", "selectFields": null}, {"field": "wqMatType", "selectFields": null}, {"field": "mat<PERSON>ate<PERSON><PERSON>nt", "selectFields": [{"field": "id", "selectFields": null}, {"field": "matCateName", "selectFields": null}]}, {"field": "created<PERSON>y", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "updatedBy", "selectFields": [{"field": "id", "selectFields": null}, {"field": "username", "selectFields": null}]}, {"field": "createdAt", "selectFields": null}, {"field": "updatedAt", "selectFields": null}], "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form": [{"field": "matCateCode", "selectFields": null}, {"field": "matCateName", "selectFields": null}, {"field": "genCounClassMatTaxItemCfId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "taxType", "selectFields": null}]}, {"field": "qualificationsGroupId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "qualificationsHeadCode", "selectFields": null}]}, {"field": "isLimitPoQualifications", "selectFields": null}, {"field": "isLimitSoQualifications", "selectFields": null}, {"field": "isLimitCtQualifications", "selectFields": null}, {"field": "path", "selectFields": null}, {"field": "wqMatType", "selectFields": null}, {"field": "mat<PERSON>ate<PERSON><PERSON>nt", "selectFields": [{"field": "id", "selectFields": null}, {"field": "matCateName", "selectFields": null}]}, {"field": "version", "selectFields": null}], "ERP_SCM$test7051-tree": []}, "content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "ERP_SCM$test7051-create-btn", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "label": "新建 ", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-import-btn", "name": "ImportButton", "props": {"serviceProps": {"downloadFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download"}, "isCustomFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "type": "InvokeService"}, "predictFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "type": "InvokeService"}, "saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-sub-model"}, "saveServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "type": "InvokeService"}, "saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-direct-by-oss"}, "saveSubServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "ERP_SCM$test7051-left-button-wrapper", "name": "Space", "props": {"style": {"marginBottom": 12}}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_SCM$test7051-tree-addChildBtn", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form"}], "executeLogic": "ExecuteScript", "executeScriptConfig": "navigate({ action: 'new', query: { parentId: record.id } })"}, "buttonType": "default", "confirmOn": "off", "label": "新建子类目", "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-tree-editBtn", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-tree-enableBtn", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认启用吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Text", "valueConfig": {"expression": "record.id", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "ERP_GEN$gen_mat_cate_md_MASTER_DATA_ENABLE_DATA_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Message", "message": "启用成功"}, {"action": "Refresh", "target": "ERP_SCM$test7051-tree"}, {"action": "Refresh", "target": "ERP_SCM$test7051-detailView-container"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "启用", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-tree-disableBtn", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认停用吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Text", "valueConfig": {"expression": "record.id", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "ERP_GEN$gen_mat_cate_md_MASTER_DATA_DISABLE_DATA_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_SCM$test7051-detailView-container"}, {"action": "Refresh", "target": "ERP_SCM$test7051-tree"}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "停用", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-tree-deleteBtn", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Text", "valueConfig": {"expression": "record.id", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "ERP_GEN$gen_mat_cate_md_DELETE_DATA_BY_ID_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_SCM$test7051-tree"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}], "key": "ERP_SCM$test7051-tree-recordActions", "name": "RecordActions", "props": {}, "type": "Layout"}], "key": "ERP_SCM$test7051-tree", "name": "Tree", "props": {"flow": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "params": [{"elements": [], "fieldAlias": "request", "fieldName": "request", "fieldType": "Pageable"}], "serviceKey": "ERP_GEN$GEN_MAT_CATE_MD_FIND_TREE_CHILDREN_DATA_SERVICE", "type": "InvokeService"}, "isLazyLoad": true, "labelField": "matCateName", "leafFieldName": "<PERSON><PERSON><PERSON><PERSON>", "leafFieldValue": true, "modelAlias": "ERP_GEN$gen_mat_cate_md", "onSelect$": "(key, info) => $context.mode !== 'design' && navigate({ action: 'show', recordId: info.node.id })", "treeField": "mat<PERSON>ate<PERSON><PERSON>nt"}, "type": "Container"}], "key": "ERP_SCM$test7051-left-container", "name": "Box", "props": {"style": {"display": "flex", "flexDirection": "column", "height": "100%"}}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_SCM$test7051-list-ERP_GEN$gen_mat_cate_md-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 200, "width": 200}}}], "key": "ERP_SCM$test7051-list-ERP_GEN$gen_mat_cate_md-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_SCM$test7051-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{((data?.code || \"\") + (data?.name || \"\")) || \"类目配置表详情\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "ERP_SCM$test7051-detailView-page-title-extra", "name": "PageTitleExtra", "props": {"children": [{"children": [{"name": "Status", "props": {"text$": "(n=>({DRAFT:{type:\"default\",text:\"草稿\"},UNENABLED:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISENABLED:{type:\"failure\",text:\"已停用\"},DELETED:{type:\"failure\",text:\"已删除\"},SUBMITTED:{type:\"default\",text:\"已提交\"}})[n])(data?.status)?.text", "type$": "(n=>({DRAFT:{type:\"default\",text:\"草稿\"},UNENABLED:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISENABLED:{type:\"failure\",text:\"已停用\"},DELETED:{type:\"failure\",text:\"已删除\"},SUBMITTED:{type:\"default\",text:\"已提交\"}})[n])(data?.status)?.type"}}], "name": "If", "props": {"test": true}}]}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_SCM$test7051-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Text", "valueConfig": {"expression": "route.recordId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "ERP_GEN$gen_mat_cate_md_DELETE_DATA_BY_ID_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_SCM$test7051-tree"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "form", "title": "status", "type": "VarValue", "val": "status", "value": "ERP_GEN$gen_mat_cate_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}}, {"children": [], "key": "ERP_SCM$test7051-detailView-actions-disable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认停用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Text", "valueConfig": {"expression": "route.recordId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "ERP_GEN$gen_mat_cate_md_MASTER_DATA_DISABLE_DATA_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_SCM$test7051-detailView-container"}, {"action": "Refresh", "target": "ERP_SCM$test7051-tree"}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "停用", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "form", "title": "status", "type": "VarValue", "val": "status", "value": "ERP_GEN$gen_mat_cate_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-detailView-actions-enable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认启用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Text", "valueConfig": {"expression": "route.recordId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "ERP_GEN$gen_mat_cate_md_MASTER_DATA_ENABLE_DATA_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_SCM$test7051-detailView-container"}, {"action": "Refresh", "target": "ERP_SCM$test7051-tree"}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "启用", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "form", "title": "status", "type": "VarValue", "val": "status", "value": "ERP_GEN$gen_mat_cate_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "route.recordId", "name": "recordId", "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "form", "title": "status", "type": "VarValue", "val": "status", "value": "ERP_GEN$gen_mat_cate_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-detail-ERP_GEN$gen_mat_cate_md-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"serviceKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "ERP_SCM$test7051-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "ERP_SCM$test7051-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-matCateCode", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "matCateCode", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "editable": false, "hidden": false, "label": "类目编码", "name": "matCateCode", "type": "TEXT"}}, {"children": [], "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-matCateName", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "editable": false, "hidden": false, "label": "类目名称", "name": "matCateName", "type": "TEXT"}}, {"children": [], "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-isLeaf", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "editable": false, "hidden": true, "label": "是否叶子节点", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "BOOL"}}, {"children": [], "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-status", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "status", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "editable": false, "hidden": false, "label": "状态", "name": "status", "type": "SELECT"}}, {"children": [], "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-genCounClassMatTaxItemCfId", "name": "DetailField", "props": {"componentProps": {"columns": ["counId", "taxType", "taxClassId", "taxClassDesc", "genVendFinMdId", "genCustFinMdId", "matSlsId"], "fieldAlias": "genCounClassMatTaxItemCfId", "label": "选择物料税", "labelField": "taxType", "modelAlias": "ERP_GEN$gen_coun_class_mat_tax_item_cf", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$gen_coun_class_mat_tax_item_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$gen_coun_class_mat_tax_item_cf_PAGING_DATA_SERVICE"}}, "editable": false, "hidden": false, "label": "物料税", "name": "genCounClassMatTaxItemCfId", "type": "OBJECT"}}, {"children": [], "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-qualificationsGroupId", "name": "DetailField", "props": {"componentProps": {"columns": ["qualificationsHeadCode", "qualificationsHeadName"], "fieldAlias": "qualificationsGroupId", "label": "选择关联资质组", "labelField": "qualificationsHeadCode", "modelAlias": "ERP_SCM$gen_qualifications_head_cf", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$gen_qualifications_head_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$gen_qualifications_head_cf_PAGING_DATA_SERVICE"}}, "editable": false, "hidden": false, "label": "关联资质组", "name": "qualificationsGroupId", "type": "OBJECT"}}, {"children": [], "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-isLimitPoQualifications", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "isLimitPoQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "editable": false, "hidden": false, "label": "采购订单是否限制资质", "name": "isLimitPoQualifications", "type": "BOOL"}}, {"children": [], "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-isLimitSoQualifications", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "isLimitSoQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "editable": false, "hidden": false, "label": "销售订单是否限制资质", "name": "isLimitSoQualifications", "type": "BOOL"}}, {"children": [], "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-isLimitCtQualifications", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "isLimitCtQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "editable": false, "hidden": false, "label": "合同是否限制资质", "name": "isLimitCtQualifications", "type": "BOOL"}}, {"children": [], "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-path", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "path", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "editable": false, "hidden": false, "label": "类目路径", "name": "path", "type": "TEXT"}}, {"children": [], "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-wqMatType", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "wqMatType", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "editable": false, "hidden": false, "label": "物料类型", "name": "wqMatType", "type": "SELECT"}}, {"children": [], "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-matCateParent", "name": "DetailField", "props": {"componentProps": {"columns": ["matCateCode", "matCateName", "<PERSON><PERSON><PERSON><PERSON>", "status", "genCounClassMatTaxItemCfId", "qualificationsGroupId", "isLimitPoQualifications", "isLimitSoQualifications", "isLimitCtQualifications", "path", "wqMatType", "mat<PERSON>ate<PERSON><PERSON>nt"], "fieldAlias": "mat<PERSON>ate<PERSON><PERSON>nt", "label": "选择父类目", "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$gen_mat_cate_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$gen_mat_cate_md_PAGING_DATA_SERVICE"}}, "editable": false, "hidden": false, "label": "父类目", "name": "mat<PERSON>ate<PERSON><PERSON>nt", "type": "SELFRELATION"}}], "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_SCM$test7051-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "ERP_GEN$user", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$user_PAGING_DATA_SERVICE"}}, "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "ERP_SCM$test7051-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "ERP_GEN$user", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$user_PAGING_DATA_SERVICE"}}, "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "ERP_SCM$test7051-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "ERP_SCM$test7051-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "ERP_SCM$test7051-detailView-system-detail-group", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "ERP_SCM$test7051-detailView-system-detail-TabItem", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "ERP_SCM$test7051-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "系统信息"}], "underline": true}, "type": "Layout"}], "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "name": "Detail", "props": {"flow": {"containerKey": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$gen_mat_cate_md_FIND_DATA_BY_ID_SERVICE_ykcOjc1", "type": "InvokeService"}, "modelAlias": "ERP_GEN$gen_mat_cate_md", "onFinish$": "(values) => invokeSystemService(\"ERP_GEN$SYS_MasterData_SaveDataService\", \"ERP_GEN$gen_mat_cate_md\", {...data, ...values}).then(() => $(\"ERP_SCM$test7051-detailView-page\").action(\"reload\"))", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "type": "Container"}], "key": "ERP_SCM$test7051-detailView-page", "name": "Page", "props": {"modelAlias": "ERP_GEN$gen_mat_cate_md", "params": [], "showFooter": false, "showHeader": true}, "type": "Container"}], "key": "ERP_SCM$test7051-detailView-container", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [], "key": "ERP_SCM$test7051-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" + ((data?.code || \"\") + (data?.name || \"类目配置表\")) : \"新建类目配置表\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "ERP_SCM$test7051-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-matCateCode", "name": "FormField", "props": {"componentProps": {"fieldAlias": "matCateCode", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": false, "label": "类目编码", "name": "matCateCode", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-matCateName", "name": "FormField", "props": {"componentProps": {"fieldAlias": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": false, "label": "类目名称", "name": "matCateName", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-isLeaf", "name": "FormField", "props": {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "label": "是否叶子节点", "name": "<PERSON><PERSON><PERSON><PERSON>", "rules": [], "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-genCounClassMatTaxItemCfId", "name": "FormField", "props": {"componentProps": {"columns": ["counId", "taxType", "taxClassId", "taxClassDesc", "genVendFinMdId", "genCustFinMdId", "matSlsId"], "fieldAlias": "genCounClassMatTaxItemCfId", "label": "选择物料税", "labelField": "taxType", "modelAlias": "ERP_GEN$gen_coun_class_mat_tax_item_cf", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$gen_coun_class_mat_tax_item_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$gen_coun_class_mat_tax_item_cf_PAGING_DATA_SERVICE"}}, "hidden": false, "label": "物料税", "name": "genCounClassMatTaxItemCfId", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-qualificationsGroupId", "name": "FormField", "props": {"componentProps": {"columns": ["qualificationsHeadCode", "qualificationsHeadName"], "fieldAlias": "qualificationsGroupId", "label": "选择关联资质组", "labelField": "qualificationsHeadCode", "modelAlias": "ERP_SCM$gen_qualifications_head_cf", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$gen_qualifications_head_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_SCM$gen_qualifications_head_cf_PAGING_DATA_SERVICE"}}, "hidden": false, "label": "关联资质组", "name": "qualificationsGroupId", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-isLimitPoQualifications", "name": "FormField", "props": {"componentProps": {"fieldAlias": "isLimitPoQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": false, "label": "采购订单是否限制资质", "name": "isLimitPoQualifications", "rules": [], "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-isLimitSoQualifications", "name": "FormField", "props": {"componentProps": {"fieldAlias": "isLimitSoQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": false, "label": "销售订单是否限制资质", "name": "isLimitSoQualifications", "rules": [], "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-isLimitCtQualifications", "name": "FormField", "props": {"componentProps": {"fieldAlias": "isLimitCtQualifications", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": false, "label": "合同是否限制资质", "name": "isLimitCtQualifications", "rules": [], "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-path", "name": "FormField", "props": {"componentProps": {"fieldAlias": "path", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": false, "label": "类目路径", "name": "path", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-wqMatType", "name": "FormField", "props": {"componentProps": {"fieldAlias": "wqMatType", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": false, "label": "物料类型", "name": "wqMatType", "rules": [], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-matCateParent", "name": "FormField", "props": {"componentProps": {"columns": ["matCateCode", "matCateName", "<PERSON><PERSON><PERSON><PERSON>", "status", "genCounClassMatTaxItemCfId", "qualificationsGroupId", "isLimitPoQualifications", "isLimitSoQualifications", "isLimitCtQualifications", "path", "wqMatType", "mat<PERSON>ate<PERSON><PERSON>nt"], "fieldAlias": "mat<PERSON>ate<PERSON><PERSON>nt", "label": "选择父类目", "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$gen_mat_cate_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$gen_mat_cate_md_PAGING_DATA_SERVICE"}}, "editComponentProps": {"cascaderField": "mat<PERSON>ate<PERSON><PERSON>nt", "fields": [], "flow": {"containerKey": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-matCateParent", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "user"}}, {"elements": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Pageable"}], "serviceKey": "ERP_GEN$SYS_FindTreeChildrenDataService", "type": "InvokeSystemService"}, "labelField": ["matCateName"], "leafOnly": false, "modelAlias": "ERP_GEN$gen_mat_cate_md", "reverseConstructFlow": {"context$": "$context", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$gen_mat_cate_md_REVERSE_CONSTRUCT_TREE_SERVICE", "type": "InvokeService"}, "tableCondition": null}, "editComponentType": "SelfRelation", "hidden": false, "initialValue$": "route.query?.parentId ? { id: route.query?.parentId } : undefined", "label": "父类目", "name": "mat<PERSON>ate<PERSON><PERSON>nt", "rules": [], "type": "SELFRELATION"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-id", "name": "FormField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-createdBy", "name": "FormField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "ERP_GEN$user", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$user_PAGING_DATA_SERVICE"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-updatedBy", "name": "FormField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "ERP_GEN$user", "parentModelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_GEN$user_PAGING_DATA_SERVICE"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-createdAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-updatedAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-originOrgId", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "originOrgId", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "所属组织", "name": "originOrgId", "rules": [{"message": "请输入所属组织", "required": true}], "type": "NUMBER"}, "type": "Widget"}], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "name": "FormGroupItem", "props": {"showSplit": true, "title": false}, "type": "Layout"}], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "name": "TabItem", "props": {"title": false}, "type": "Layout"}], "key": "ERP_SCM$test7051-editView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}], "underline": true}, "type": "Layout"}], "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "name": "FormGroup", "props": {"colon": false, "flow": {"containerKey": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "modelAlias": "ERP_GEN$gen_mat_cate_md", "params$": "{ id: route.recordId }", "serviceKey": "ERP_GEN$gen_mat_cate_md_FIND_DATA_BY_ID_SERVICE_ykcOjc2", "test$": "route.action === 'edit'", "type": "InvokeService"}, "layout": "horizontal", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "ERP_SCM$test7051-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "ConditionFlowAction", "children": [{"action": "RefreshTab", "target": ["current"]}], "condition": {"conditions": [{"conditions": [{"id": "ERP_SCM$test7051-cancel-condition-flow-1-2", "leftValue": {"fieldType": "Text", "scope": "route", "val": "recordId"}, "operator": "IS_NULL", "type": "ConditionLeaf"}], "id": "ERP_SCM$test7051-cancel-condition-flow-1-1", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "ERP_SCM$test7051-cancel-condition-flow-1", "logicOperator": "OR", "type": "ConditionGroup"}}, {"action": "ConditionFlowAction", "children": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "route.recordId", "name": "recordId", "type": "expression"}], "refresh": true, "type": "NewPage"}}], "condition": {"conditions": [{"conditions": [{"id": "ERP_SCM$test7051-cancel-condition-flow-2-2", "leftValue": {"fieldType": "Text", "scope": "route", "val": "recordId"}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "ERP_SCM$test7051-cancel-condition-flow-2-1", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "ERP_SCM$test7051-cancel-condition-flow-2", "logicOperator": "OR", "type": "ConditionGroup"}}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$test7051-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Validate", "validate": true}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "valueConfig": {"action": {"target": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form"}, "type": "action"}}], "service": "ERP_GEN$gen_mat_cate_md_MASTER_DATA_SAVE_DATA_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "ExecuteScript", "script": " getAction('ERP_SCM$test7051-tree', 'reload')(getAction('ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form', 'getData')()?.matCateParent?.id, false) "}, {"action": "Message", "level": "success", "message": "保存成功!"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "type": "primary"}, "type": "Widget"}], "key": "ERP_SCM$test7051-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "ERP_SCM$test7051-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "ERP_SCM$test7051-editView", "name": "Page", "props": {"params": [], "showFooter": true, "showHeader": true}, "type": "Container"}], "key": "ERP_SCM$test7051-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "ERP_SCM$test7051-column-page", "name": "ColumnPage", "props": {}, "type": "Layout"}], "key": "ERP_SCM$test7051-page", "name": "View", "props": {}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "key": "ERP_SCM$test7051:list", "resources": [{"description": null, "key": "ERP_SCM$test7051-tree", "label": "树", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-left-container", "label": "区块", "type": "Box"}], "relations": [{"key": "ERP_GEN$GEN_MAT_CATE_MD_FIND_TREE_CHILDREN_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-create-btn", "label": "新建 ", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-left-container", "label": "区块", "type": "Box"}, {"key": "ERP_SCM$test7051-left-button-wrapper", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_SCM$test7051-import-btn", "label": "导入", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-left-container", "label": "区块", "type": "Box"}, {"key": "ERP_SCM$test7051-left-button-wrapper", "label": "间距", "type": "Space"}], "relations": [{"key": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/import-sub-model", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/import-direct-by-oss", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/template/download", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "label": "表单组", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$gen_mat_cate_md_FIND_DATA_BY_ID_SERVICE_ykcOjc1", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-tree-addChildBtn", "label": "新建子类目", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-left-container", "label": "区块", "type": "Box"}, {"key": "ERP_SCM$test7051-tree", "label": "树", "type": "Tree"}, {"key": "ERP_SCM$test7051-tree-recordActions", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_SCM$test7051-tree-editBtn", "label": "编辑", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-left-container", "label": "区块", "type": "Box"}, {"key": "ERP_SCM$test7051-tree", "label": "树", "type": "Tree"}, {"key": "ERP_SCM$test7051-tree-recordActions", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_SCM$test7051-tree-enableBtn", "label": "启用", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-left-container", "label": "区块", "type": "Box"}, {"key": "ERP_SCM$test7051-tree", "label": "树", "type": "Tree"}, {"key": "ERP_SCM$test7051-tree-recordActions", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "ERP_GEN$gen_mat_cate_md_MASTER_DATA_ENABLE_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_SCM$test7051-tree-disableBtn", "label": "停用", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-left-container", "label": "区块", "type": "Box"}, {"key": "ERP_SCM$test7051-tree", "label": "树", "type": "Tree"}, {"key": "ERP_SCM$test7051-tree-recordActions", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "ERP_GEN$gen_mat_cate_md_MASTER_DATA_DISABLE_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_SCM$test7051-tree-deleteBtn", "label": "删除", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-left-container", "label": "区块", "type": "Box"}, {"key": "ERP_SCM$test7051-tree", "label": "树", "type": "Tree"}, {"key": "ERP_SCM$test7051-tree-recordActions", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "ERP_GEN$gen_mat_cate_md_DELETE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "label": "详情", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$gen_mat_cate_md_FIND_DATA_BY_ID_SERVICE_ykcOjc1", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-action-cancel", "label": "取消", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "ERP_SCM$test7051-editView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_SCM$test7051-editView-action-save", "label": "保存", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "ERP_SCM$test7051-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "ERP_GEN$gen_mat_cate_md_MASTER_DATA_SAVE_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_SCM$test7051-detailView-actions-delete", "label": "删除", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_SCM$test7051-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "ERP_GEN$gen_mat_cate_md_DELETE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_SCM$test7051-detailView-actions-disable", "label": "停用", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_SCM$test7051-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "ERP_GEN$gen_mat_cate_md_MASTER_DATA_DISABLE_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_SCM$test7051-detailView-actions-enable", "label": "启用", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_SCM$test7051-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "ERP_GEN$gen_mat_cate_md_MASTER_DATA_ENABLE_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_SCM$test7051-detailView-actions-edit", "label": "编辑", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_SCM$test7051-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_SCM$test7051-detail-ERP_GEN$gen_mat_cate_md-logs", "label": "日志", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_SCM$test7051-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-matCateCode", "label": "类目编码", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-matCateName", "label": "类目名称", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-isLeaf", "label": "是否叶子节点", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-genCounClassMatTaxItemCfId", "label": "物料税", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$gen_coun_class_mat_tax_item_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_coun_class_mat_tax_item_cf"}, "type": "Service"}, {"key": "ERP_GEN$gen_coun_class_mat_tax_item_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_coun_class_mat_tax_item_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-qualificationsGroupId", "label": "关联资质组", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "ERP_SCM$gen_qualifications_head_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$gen_qualifications_head_cf"}, "type": "Service"}, {"key": "ERP_SCM$gen_qualifications_head_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$gen_qualifications_head_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-isLimitPoQualifications", "label": "采购订单是否限制资质", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-isLimitSoQualifications", "label": "销售订单是否限制资质", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-isLimitCtQualifications", "label": "合同是否限制资质", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-path", "label": "类目路径", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-wqMatType", "label": "物料类型", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-matCateParent", "label": "父类目", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$gen_mat_cate_md_FIND_DATA_BY_ID_SERVICE_ykcOjc1", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "Service"}, {"key": "ERP_GEN$gen_mat_cate_md_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "Service"}, {"key": "ERP_GEN$GEN_MAT_CATE_MD_FIND_TREE_CHILDREN_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "Service"}, {"key": "ERP_GEN$gen_mat_cate_md_REVERSE_CONSTRUCT_TREE_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-id", "label": "ID", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-createdBy", "label": "创建人", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}, "type": "Service"}, {"key": "ERP_GEN$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-updatedBy", "label": "更新人", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}, "type": "Service"}, {"key": "ERP_GEN$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-createdAt", "label": "创建时间", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-updatedAt", "label": "更新时间", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-version", "label": "版本号", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-deleted", "label": "逻辑删除标识", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-field-originOrgId", "label": "所属组织", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-editView", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_SCM$test7051-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-editView-ERP_GEN$gen_mat_cate_md-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-matCateCode", "label": "类目编码", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-matCateName", "label": "类目名称", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-isLeaf", "label": "是否叶子节点", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-status", "label": "状态", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-genCounClassMatTaxItemCfId", "label": "物料税", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$gen_coun_class_mat_tax_item_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_coun_class_mat_tax_item_cf"}, "type": "Service"}, {"key": "ERP_GEN$gen_coun_class_mat_tax_item_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_coun_class_mat_tax_item_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-qualificationsGroupId", "label": "关联资质组", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [{"key": "ERP_SCM$gen_qualifications_head_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$gen_qualifications_head_cf"}, "type": "Service"}, {"key": "ERP_SCM$gen_qualifications_head_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$gen_qualifications_head_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-isLimitPoQualifications", "label": "采购订单是否限制资质", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-isLimitSoQualifications", "label": "销售订单是否限制资质", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-isLimitCtQualifications", "label": "合同是否限制资质", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-path", "label": "类目路径", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-wqMatType", "label": "物料类型", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-field-matCateParent", "label": "父类目", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-tabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$gen_mat_cate_md_FIND_DATA_BY_ID_SERVICE_ykcOjc1", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "Service"}, {"key": "ERP_GEN$gen_mat_cate_md_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-detailView-system-detail-field-createdBy", "label": "创建人", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-detailView-system-detail-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}, "type": "Service"}, {"key": "ERP_GEN$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-detailView-system-detail-field-updatedBy", "label": "更新人", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-detailView-system-detail-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}, "type": "Service"}, {"key": "ERP_GEN$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-detailView-system-detail-field-createdAt", "label": "创建时间", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-detailView-system-detail-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$test7051-detailView-system-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "ERP_SCM$test7051-page", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_SCM$test7051-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_SCM$test7051-detailView-container", "label": "视图", "type": "View"}, {"key": "ERP_SCM$test7051-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$test7051-detailView-ERP_GEN$gen_mat_cate_md-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$test7051-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$test7051-detailView-system-detail-TabItem", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$test7051-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}}