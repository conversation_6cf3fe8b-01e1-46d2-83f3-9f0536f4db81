{"type": "ServiceDefinition", "name": "物料主数据定义表-启用主数据服务", "access": "Private", "parentKey": "ERP_GEN", "children": [], "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "serviceDslJson": {"children": [{"id": null, "key": "node_1hork4mvf54", "name": "开始", "nextNodeKey": "node_1hork6bkm62", "props": {"globalVariable": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": "ERP_GEN$gen_mat_md"}, "relation": null, "required": null}], "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": "ERP_GEN$gen_mat_md"}, "relation": null, "required": null}], "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "type": "StartProperties"}, "type": "StartNode"}, {"children": [{"children": null, "id": null, "key": "node_1hork6bkn63", "name": "条件", "nextNodeKey": "node_1hork4rev56", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "4muua7A_UG4F9ywn3N8hY", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "REQUEST", "fieldName": "服务入参", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "request", "fieldName": "request", "fieldType": null, "modelKey": null, "relatedModel": null}], "fieldType": "Object", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "request", "valueName": "request"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "FvfTvltOzWrh7SN9Voulo", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "n3e-csYbbgFARQlo1fTQc", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "ConditionProperties"}, "type": "ConditionNode"}, {"id": null, "key": "node_1hork4rev56", "name": "HTTP服务", "nextNodeKey": "node_1hork59d057", "props": {"body": null, "bodyType": "VALUE", "headers": [], "inputMapping": null, "jsonBody": null, "method": "GET", "output": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "fieldType", "fieldKey": "fieldType", "fieldName": "fieldType", "fieldType": "Text", "id": null, "required": null}, {"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "relationType", "fieldKey": "relationType", "fieldName": "relationType", "fieldType": "Text", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "sync", "fieldKey": "sync", "fieldName": "sync", "fieldType": "Boolean", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "relationModelAlias", "fieldKey": "relationModelAlias", "fieldName": "relationModelAlias", "fieldType": "Text", "id": null, "required": null}], "fieldAlias": "relation_meta", "fieldKey": "relation_meta", "fieldName": "relationMeta", "fieldType": "Object", "id": null, "required": null}], "fieldAlias": "props", "fieldKey": "props", "fieldName": "props", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "alias", "fieldKey": "alias", "fieldName": "alias", "fieldType": "Text", "id": null, "required": null}], "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Object", "id": null, "required": null}, "fieldAlias": "children", "fieldKey": "children", "fieldName": "children", "fieldType": "Array", "id": null, "required": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pathVariables": [], "serviceName": "查询模型元数据", "stream": false, "type": "HttpServiceProperties", "url": "http://localhost:8080/api/trantor/struct-node/find-by-key/ERP_GEN$gen_mat_md"}, "type": "HttpServiceNode"}, {"id": null, "key": "node_1hork59d057", "name": "在线JS脚本", "nextNodeKey": "node_1hot9rcua65", "props": {"inputMapping": [{"field": {"defaultValue": null, "description": null, "element": null, "fieldAlias": "fields", "fieldKey": "fields", "fieldName": "fields", "fieldType": "Array", "id": null, "required": null}, "id": "1horf8iiq10", "value": {"constValue": null, "fieldPaths": [{"fieldKey": "GLOBAL", "fieldName": "全局变量", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "NODE_OUTPUT_node_1hork4rev56", "fieldName": "[HTTP服务]节点出参", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "data", "fieldName": "data", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "data", "fieldName": "data", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "children", "fieldName": "children", "fieldType": null, "modelKey": null, "relatedModel": null}], "fieldType": "Array", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "NODE_OUTPUT_node_1hork4rev56", "valueName": "[HTTP服务]节点出参"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "data", "valueName": "data"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "data", "valueName": "data"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "children", "valueName": "children"}]}}], "language": "JS", "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "field", "fieldKey": "field", "fieldName": "field", "fieldType": "Object", "id": null, "required": null}], "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "script": "for each (var field in fields) {    if (field.alias.toUpperCase() === 'STATUS') {      return field;    }  }  return null;", "scriptEngine": null, "type": "ScriptProperties"}, "type": "ScriptNode"}, {"children": [{"children": null, "id": null, "key": "node_1hot9rcua66", "name": "条件", "nextNodeKey": "node_1hork64sf61", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "Cr2rGPdzhJknNzkyR7nXq", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "GLOBAL", "fieldName": "全局变量", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "NODE_OUTPUT_node_1hork59d057", "fieldName": "[在线JS脚本]节点出参", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "field", "fieldName": "field", "fieldType": null, "modelKey": null, "relatedModel": null}], "fieldType": "Object", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "NODE_OUTPUT_node_1hork59d057", "valueName": "[在线JS脚本]节点出参"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "field", "valueName": "field"}]}, "operator": "IS_NOT_NULL", "rightValue": null, "rightValues": null, "type": "ConditionLeaf"}], "id": "XC1_2OW2zilX8hine9hFO", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "YatdAY1QyQYNkNpjl1DTD", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "ConditionProperties"}, "type": "ConditionNode"}, {"id": null, "key": "node_1hork64sf61", "name": "在线JS脚本", "nextNodeKey": "node_1hotaatan71", "props": {"inputMapping": [{"field": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "field", "fieldKey": "field", "fieldName": "field", "fieldType": "Object", "id": null, "required": null}, "id": null, "value": {"constValue": null, "fieldPaths": [{"fieldKey": "GLOBAL", "fieldName": "全局变量", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "NODE_OUTPUT_node_1hork59d057", "fieldName": "[在线JS脚本]节点出参", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "field", "fieldName": "field", "fieldType": null, "modelKey": null, "relatedModel": null}], "fieldType": "Object", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "NODE_OUTPUT_node_1hork59d057", "valueName": "[在线JS脚本]节点出参"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "field", "valueName": "field"}]}}, {"field": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}, "id": null, "value": {"constValue": null, "fieldPaths": [{"fieldKey": "REQUEST", "fieldName": "服务入参", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "request", "fieldName": "request", "fieldType": null, "modelKey": null, "relatedModel": null}], "fieldType": "Object", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "request", "valueName": "request"}]}}], "language": "JS", "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "outputAssign": {"customAssignments": [{"field": {"constValue": null, "fieldPaths": [{"fieldKey": "GLOBAL", "fieldName": "全局变量", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "data", "fieldName": "data", "fieldType": null, "modelKey": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": "ERP_GEN$gen_mat_md"}}], "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": "ERP_GEN$gen_mat_md"}, "valueKey": "data", "valueName": "data"}]}, "id": "1hotabg4m72", "operator": "EQ", "value": {"constValue": null, "fieldPaths": [{"fieldKey": "NODE_OUTPUT_node_1hork64sf61", "fieldName": "出参结构体", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "data", "fieldName": "data", "fieldType": null, "modelKey": null, "relatedModel": null}], "fieldType": "Object", "id": null, "type": "VarValue", "valueType": "MODEL", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "NODE_OUTPUT_node_1hork64sf61", "valueName": "出参结构体"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "data", "valueName": "data"}]}}], "outputAssignType": "CUSTOM"}, "script": "if (field.props.fieldType === 'ENUM') {    if (field.props.dictPros !== null && field.props.dictPros.multiSelect === true) {      request.status = ['ENABLED'];    }else {      request.status = 'ENABLED';    }    }  return request;", "scriptEngine": null, "type": "ScriptProperties"}, "type": "ScriptNode"}, {"id": null, "key": "node_1hotaatan71", "name": "更新数据", "props": {"modelValue": {"constValue": null, "fieldPaths": [{"fieldKey": "GLOBAL", "fieldName": "全局变量", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "data", "fieldName": "data", "fieldType": null, "modelKey": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": "ERP_GEN$gen_mat_md"}}], "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "modelAlias": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": "ERP_GEN$gen_mat_md"}, "valueKey": "data", "valueName": "data"}]}, "outputAssign": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": "物料主数据定义表"}, "type": "CascadeUpdateDataProperties"}, "type": "CascadeUpdateDataNode"}, {"children": null, "id": null, "key": "node_1hot9rcua67", "name": "else", "props": {"type": "ConditionElseProperties"}, "type": "ConditionElseNode"}], "headNodeKeys": ["node_1hot9rcua66", "node_1hot9rcua67"], "id": null, "key": "node_1hot9rcua65", "name": "排他分支", "props": {"type": "ExclusiveBranchProperties"}, "type": "ExclusiveBranchNode"}, {"children": null, "id": null, "key": "node_1hork6bkn64", "name": "else", "props": {"type": "ConditionElseProperties"}, "type": "ConditionElseNode"}], "headNodeKeys": ["node_1hork6bkn63", "node_1hork6bkn64"], "id": null, "key": "node_1hork6bkm62", "name": "排他分支", "nextNodeKey": "node_1hork4mvf55", "props": {"type": "ExclusiveBranchProperties"}, "type": "ExclusiveBranchNode"}, {"id": null, "key": "node_1hork4mvf55", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "headNodeKeys": ["node_1hork4mvf54"], "id": null, "input": [{"defaultValue": null, "description": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_GEN$gen_mat_md", "modelKey": "ERP_GEN$gen_mat_md", "modelName": "ERP_GEN$gen_mat_md"}, "relation": null, "required": null}], "key": "ERP_GEN$gen_mat_md_MASTER_DATA_ENABLE_DATA_SERVICE", "name": "物料主数据定义表-启用主数据服务", "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}}