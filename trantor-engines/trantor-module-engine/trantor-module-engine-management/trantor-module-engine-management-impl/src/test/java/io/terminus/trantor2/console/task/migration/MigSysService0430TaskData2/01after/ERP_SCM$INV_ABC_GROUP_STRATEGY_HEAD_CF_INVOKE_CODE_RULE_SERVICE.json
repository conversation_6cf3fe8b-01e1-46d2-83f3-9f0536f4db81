{"type": "ServiceDefinition", "name": "ABC分组方案-调用取号规则服务", "access": "Private", "parentKey": "ERP_SCM", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1hotflvm59", "name": "开始", "nextNodeKey": "node_1hothdbi211", "props": {"globalVariable": null, "input": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "formParams", "fieldKey": "formParams", "fieldName": "formParams", "fieldType": "Object", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}], "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Text", "id": null, "required": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1hothdbi211", "name": "赋值", "nextNodeKey": "node_1hotflvm510", "props": {"assignments": [{"field": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}, "id": "1hothdck712", "operator": "EQ", "value": {"fieldType": null, "funcExpression": "AUTO_GENERATE_CODE('ERP_SCM$inv_abc_group_strategy_head_cf','ERP_SCM$inv_abc_group_scheme_cf',${REQUEST.request.formParams})", "id": null, "type": "FuncValue"}}], "type": "AssignProperties"}, "type": "AssignNode"}, {"desc": null, "id": null, "key": "node_1hotflvm510", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "headNodeKeys": ["node_1hotflvm59"], "id": null, "input": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "formParams", "fieldKey": "formParams", "fieldName": "formParams", "fieldType": "Object", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}], "key": "ERP_SCM$INV_ABC_GROUP_STRATEGY_HEAD_CF_INVOKE_CODE_RULE_SERVICE", "name": "ABC分组方案-调用取号规则服务", "output": [{"defaultValue": null, "description": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Text", "id": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "fieldRules": null, "permissionKey": "ERP_SCM$INV_ABC_GROUP_STRATEGY_HEAD_CF_CREATE_PERMISSION", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}}