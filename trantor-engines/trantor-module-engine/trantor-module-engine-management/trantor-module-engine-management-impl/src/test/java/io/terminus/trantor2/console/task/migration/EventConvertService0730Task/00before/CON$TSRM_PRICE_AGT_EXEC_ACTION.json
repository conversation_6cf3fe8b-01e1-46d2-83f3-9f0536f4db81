{"parentKey": "CON", "name": "价格协议-执行价格协议", "type": "Action", "key": "CON$TSRM_PRICE_AGT_EXEC_ACTION", "props": {"responseType": "io.terminus.common.api.model.Paging<io.terminus.tsrm.price.spi.model.agreement.dto.PriceAgreementItemDTO>", "languageType": "Java", "method": "execute", "requestType": "java.util.List<io.terminus.tsrm.price.spi.dict.agreement.PriceAgreementExecDTO>", "bean": "PriceAgreementAction", "order": 10, "status": "enabled"}}