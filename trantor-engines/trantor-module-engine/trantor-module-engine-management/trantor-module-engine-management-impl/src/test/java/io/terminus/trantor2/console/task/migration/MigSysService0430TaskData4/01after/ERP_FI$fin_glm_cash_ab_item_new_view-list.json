{"type": "View", "name": "ERP_FI$fin_glm_cash_ab_item_new_view-list", "parentKey": "ERP_PLN", "children": [], "props": {"conditionGroups": {"-bfcYmbo9WXU8q50dcLiL": {"conditions": [{"conditions": [{"id": "Z833Eeo-DLqw2fzUakMFT", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgStatus", "fieldName": "orgStatus", "fieldType": null, "modelKey": null, "relatedModel": null}], "fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "wOqHUxfdgH1XVc1QaSWNw", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgDimensionId", "fieldName": "orgDimensionId", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "orgDimensionCode", "fieldName": "orgDimensionCode", "fieldType": null, "modelKey": null, "relatedModel": null}], "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgDimensionId", "valueName": "orgDimensionId"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgDimensionCode", "valueName": "orgDimensionCode"}]}, "operator": "EQ", "rightValue": {"constValue": "FIN_ORG_GRP", "fieldType": "Text", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "R3Uu4oPeLpUnYUa4UlKW4", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "-bfcYmbo9WXU8q50dcLiL", "logicOperator": "OR", "type": "ConditionGroup"}, "WRjteypPS9rZxQY4q4Qho": {"conditions": [{"conditions": [{"id": "dhNMj2HKrRdm7QUk8XOKO", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgDimensionId", "fieldName": "orgDimensionId", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "orgDimensionCode", "fieldName": "orgDimensionCode", "fieldType": null, "modelKey": null, "relatedModel": null}], "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgDimensionId", "valueName": "orgDimensionId"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgDimensionCode", "valueName": "orgDimensionCode"}]}, "operator": "EQ", "rightValue": {"constValue": "FIN_ORG_GRP", "fieldType": "Text", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "xlCZmoCScgvsX6JUPL_3V", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgStatus", "fieldName": "orgStatus", "fieldType": null, "modelKey": null, "relatedModel": null}], "fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "ldLLctcZtagTgLRFLtLgM", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "WRjteypPS9rZxQY4q4Qho", "logicOperator": "OR", "type": "ConditionGroup"}, "nvk0wAruAIsUN1cpp73KF": {"conditions": [{"conditions": [{"id": "Uc1veFegx9UAgt3q_5e7x", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgStatus", "fieldName": "orgStatus", "fieldType": null, "modelKey": null, "relatedModel": null}], "fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "DoWa4QROJDD694NXonFU9", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgDimensionId", "fieldName": "orgDimensionId", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "orgDimensionCode", "fieldName": "orgDimensionCode", "fieldType": null, "modelKey": null, "relatedModel": null}], "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgDimensionId", "valueName": "orgDimensionId"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgDimensionCode", "valueName": "orgDimensionCode"}]}, "operator": "EQ", "rightValue": {"constValue": "FIN_ORG_GRP", "fieldType": "Text", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "fyUf8gd1pSGPRij7ioDJI", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "nvk0wAruAIsUN1cpp73KF", "logicOperator": "OR", "type": "ConditionGroup"}}, "containerSelect": {"ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail": [{"field": "abItemCode", "selectFields": null}, {"field": "abItemName", "selectFields": null}, {"field": "abItemType", "selectFields": null}, {"field": "cashFlowDirec", "selectFields": null}, {"field": "operActiv", "selectFields": null}, {"field": "netProf", "selectFields": null}, {"field": "excRateFlu", "selectFields": null}, {"field": "estGenComId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "orgName", "selectFields": null}]}, {"field": "finGlmCfsTypeCf", "selectFields": [{"field": "id", "selectFields": null}, {"field": "cfsTypeName", "selectFields": null}]}, {"field": "parentId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "abItemName", "selectFields": null}]}, {"field": "<PERSON><PERSON><PERSON><PERSON>", "selectFields": null}, {"field": "finGlmAbCftOrgAssCf", "selectFields": [{"field": "estGenComId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "orgName", "selectFields": null}]}]}], "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form": [{"field": "abItemCode", "selectFields": null}, {"field": "abItemName", "selectFields": null}, {"field": "finGlmCfsTypeCf", "selectFields": [{"field": "id", "selectFields": null}, {"field": "cfsTypeName", "selectFields": null}]}, {"field": "estGenComId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "orgName", "selectFields": null}]}, {"field": "abItemType", "selectFields": null}, {"field": "cashFlowDirec", "selectFields": null}, {"field": "operActiv", "selectFields": null}, {"field": "netProf", "selectFields": null}, {"field": "excRateFlu", "selectFields": null}, {"field": "parentId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "abItemName", "selectFields": null}]}, {"field": "<PERSON><PERSON><PERSON><PERSON>", "selectFields": null}, {"field": "version", "selectFields": null}, {"field": "finGlmAbCftOrgAssCf", "selectFields": [{"field": "estGenComId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "orgName", "selectFields": null}]}]}], "ERP_FI$fin_glm_cash_ab_item_new_view-mvpa4DSrK9YoWw7RysOXN": [], "ERP_FI$fin_glm_cash_ab_item_new_view-tmAkNzQCtICQYnJeoWPA4": [{"field": "orgCode", "selectFields": null}, {"field": "orgName", "selectFields": null}]}, "content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-xwk_FkiBmZADGiT9uq0U1", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-8Z-XsHSqEufwXj1AiDw4r", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-ytaagnyOJ-ZoPvhjDEKFe", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-IaF4MFhKebeB6XL-qV1hI", "name": "Field", "props": {"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织编码", "name": "orgCode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-u5fsAdNHZc1qxqBCwUiC5", "name": "Field", "props": {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织名称", "name": "orgName", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-LUUtuie-MUPvTauriZxUr", "name": "Fields", "props": {}, "type": "Meta"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-tmAkNzQCtICQYnJeoWPA4", "name": "Table", "props": {"allowClickRowSelect": true, "allowRowSelect": true, "filterFields": [{"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织编码", "name": "orgCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织名称", "name": "orgName", "required": false, "type": "TEXT", "width": 120}], "flow": {"containerKey": "ERP_FI$fin_glm_cash_ab_item_new_view-tmAkNzQCtICQYnJeoWPA4", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "orgStatus", "fieldName": "orgStatus", "fieldType": "Text", "valueConfig": {"type": "const", "value": "ENABLED"}}, {"fieldAlias": "orgDimensionCode", "fieldName": "orgDimensionCode", "fieldType": "Text", "valueConfig": {"type": "const", "value": "FIN_ORG_GRP"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "type": "InvokeService"}, "isProFilter": false, "label": "表格", "mode": "normal", "modelAlias": "sys_common$org_struct_md", "showConfigure": false, "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "Uc1veFegx9UAgt3q_5e7x", "leftValue": {"fieldType": "Enum", "title": "状态", "type": "VarValue", "val": "orgStatus", "value": "sys_common$org_struct_md.orgStatus", "valueType": "VAR", "varVal": "orgStatus", "varValue": [{"valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "DoWa4QROJDD694NXonFU9", "leftValue": {"fieldType": "Text", "title": "组织维度.维度编码", "type": "VarValue", "val": "orgDimensionId.orgDimensionCode", "value": "sys_common$org_struct_md.orgDimensionId.orgDimensionCode", "valueType": "VAR", "varVal": "orgDimensionId.orgDimensionCode", "varValue": [{"valueKey": "orgDimensionId", "valueName": "orgDimensionId"}, {"valueKey": "orgDimensionCode", "valueName": "orgDimensionCode"}]}, "operator": "EQ", "rightValue": {"constValue": "FIN_ORG_GRP", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "fyUf8gd1pSGPRij7ioDJI", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "nvk0wAruAIsUN1cpp73KF", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": null}, "type": "Container"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-Z5Ick4vfZ_Q_4niSm5XVE", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-wnFOwFsS4HE1eBUr5-R3x", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": ["ERP_FI$fin_glm_cash_ab_item_new_view-E-v1269bf7PlqySbY9BdI"]}]}, "confirmOn": "off", "label": "取消", "showCondition": {}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-vQVS9SUP1kFL9Fg2TTuDK", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "FillData", "scope": "row", "selector": "estGenComId", "target": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_cft_org_ass_cf-tableform"}, {"action": "Close", "target": ["ERP_FI$fin_glm_cash_ab_item_new_view-E-v1269bf7PlqySbY9BdI"]}], "executeLogic": "ExecuteScript", "executeScriptConfig": "(function($) {\n  const key = 'ERP_FI$fin_glm_cash_ab_item_new_view-tmAkNzQCtICQYnJeoWPA4'\n  const ids = $(key).action('getSelectedKeys')\n  const data = ids.map(id => ({ id }))\n  return { success: true, data }\n})($)"}, "confirmOn": "off", "label": "保存", "showCondition": {}, "type": "default"}, "type": "Widget"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-3fbAjrobL3PDYT35vYuld", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-E-v1269bf7PlqySbY9BdI", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": false, "layout": "modal", "layoutProps": {"bodyStyle": {}, "width": 560}, "title": "引用组织"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-s7juUbqQ9jGdrb-LFNX2-", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ["ERP_FI$fin_glm_cash_ab_item_new_view-page"]}, {"action": "OpenView", "openViewConfig": {"page": {"key": "new", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "route.recordId || record.id", "name": "recordId", "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "confirmOn": "off", "label": "新建", "showCondition": {}, "type": "primary"}, "type": "Widget"}, {"children": [{"children": [{"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-bNCnhiWH5Z0oaGCQROMz3", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "new", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "record.id", "name": "parentId", "type": "expression"}, {"expression": "record?.id", "name": "recordId", "type": "expression"}, {"expression": "record?.finGlmCfsTypeCf", "name": "finGlmCfsTypeCf", "type": "expression"}, {"expression": "record?.abItemType", "name": "abItemType", "type": "expression"}, {"expression": "record?.cashFlowDirec", "name": "cashFlowDirec", "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "confirmOn": "off", "label": "新建子级", "showCondition": {"conditions": [{"conditions": [{"id": "iLGiWAuv01ggs9SPktVK7", "leftValue": {"fieldType": "Number", "scope": "row", "title": "现金流量项目.ID", "type": "VarValue", "val": "finGlmAbItemCfId.id", "value": "ERP_FI$fin_glm_ab_cft_org_ass_cf.finGlmAbItemCfId.id", "valueType": "VAR", "varVal": "finGlmAbItemCfId.id", "varValue": [{"valueKey": "finGlmAbItemCfId", "valueName": "finGlmAbItemCfId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"exprValue": "route.recordId", "fieldType": "Number", "type": "ExprValue", "value": "route.recordId", "valueType": "ExprValue", "varVal": "route.recordId"}, "type": "ConditionLeaf"}, {"id": "5lOxUHOOOkY1N1_H9yGrq", "leftValue": {"fieldType": "Model", "scope": "form", "title": "使用组织", "type": "VarValue", "val": "finGlmAbCftOrgAssCf", "value": "ERP_FI$fin_glm_ab_item_cf.finGlmAbCftOrgAssCf", "valueType": "VAR", "varVal": "finGlmAbCftOrgAssCf", "varValue": [{"valueKey": "finGlmAbCftOrgAssCf", "valueName": "finGlmAbCftOrgAssCf"}]}, "operator": "IS_NULL", "type": "ConditionLeaf"}], "id": "QCTuqDGWkiM1sxOX3PGI5", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "zKN_YzxK26dZCGvPKzxPj", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-WHjq8LCswPE_J15QItTkf", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "是否确认删除？", "type": "Popconfirm"}], "bindFlowConfig": {"params": [{"elements": [{"fieldAlias": "abItemCode", "fieldName": "现金流量项目编码", "fieldType": "TEXT"}, {"fieldAlias": "abItemName", "fieldName": "现金流量项目名称", "fieldType": "TEXT"}, {"fieldAlias": "abItemType", "fieldName": "项目类别", "fieldType": "ENUM"}, {"fieldAlias": "cashFlowDirec", "fieldName": "现金流向", "fieldType": "ENUM"}, {"fieldAlias": "operActiv", "fieldName": "经营活动", "fieldType": "BOOL"}, {"fieldAlias": "netProf", "fieldName": "净利润", "fieldType": "BOOL"}, {"fieldAlias": "excRateFlu", "fieldName": "汇率变动", "fieldType": "BOOL"}, {"fieldAlias": "estGenComId", "fieldName": "创建组织", "fieldType": "OBJECT", "valueConfig": {"expression": "record?.estGenComId", "type": "expression"}}, {"fieldAlias": "finGlmAbCftOrgAssCf", "fieldName": "使用组织", "fieldType": "Array"}, {"fieldAlias": "finGlmCftTypeCf", "fieldName": "现金流量项目类别", "fieldType": "OBJECT"}, {"fieldAlias": "finGlmCashSubAssCf", "fieldName": "组织项目科目关联表", "fieldType": "Array"}, {"fieldAlias": "finGlmCfsTypeCf", "fieldName": "现金流量项目表", "fieldType": "OBJECT"}, {"fieldAlias": "parentId", "fieldName": "父节点", "fieldType": "OBJECT", "valueConfig": {"expression": "record?.parentId", "type": "expression"}}, {"fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "fieldName": "是否叶子节点", "fieldType": "BOOL", "valueConfig": {"expression": "record?.is<PERSON><PERSON><PERSON>", "type": "expression"}}, {"fieldAlias": "id", "fieldName": "ID", "fieldType": "NUMBER", "valueConfig": {"expression": "record?.id", "type": "expression"}}, {"fieldAlias": "created<PERSON>y", "fieldName": "创建人", "fieldType": "OBJECT"}, {"fieldAlias": "updatedBy", "fieldName": "更新人", "fieldType": "OBJECT"}, {"fieldAlias": "createdAt", "fieldName": "创建时间", "fieldType": "DATE"}, {"fieldAlias": "updatedAt", "fieldName": "更新时间", "fieldType": "DATE"}, {"fieldAlias": "version", "fieldName": "版本号", "fieldType": "NUMBER"}, {"fieldAlias": "deleted", "fieldName": "逻辑删除标识", "fieldType": "NUMBER"}, {"fieldAlias": "originOrgId", "fieldName": "所属组织", "fieldType": "NUMBER"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "required": true}], "service": "ERP_FI$FIN_GLM_AB_ITEM_CF_DELETE_EVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ["ERP_FI$fin_glm_cash_ab_item_new_view-mvpa4DSrK9YoWw7RysOXN"]}, {"action": "Message", "level": "success", "message": "删除成功"}], "executeLogic": "BindFlow"}, "confirmOn": "off", "label": "删除", "showCondition": {}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-bmTgplxuMjCagdtm6aUPr", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "record.id || route.recordId", "name": "recordId", "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "confirmOn": "off", "label": "编辑", "showCondition": {}, "type": "default"}, "type": "Widget"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-lxuUQ5DOW5hxAL7ibp-YO", "name": "RecordActions", "props": {}, "type": "Layout"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-mvpa4DSrK9YoWw7RysOXN", "name": "Tree", "props": {"draggable": false, "flow": {"context$": "$context", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_FI$fin_glm_ab_item_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_FI$fin_glm_ab_item_cf_FIND_TREE_DATA_SERVICE", "type": "InvokeService"}, "isLazyLoad": false, "labelField": "abItemName", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "onSelect$": "(key, info) => $context.mode !== 'design' && navigate({ action: 'show', recordId: info.node.id })", "tableConditionContext$": null, "treeField": "parentId"}, "type": "Container"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-b5DqOBAY4xMck3lTBzvGt", "name": "Box", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-list-ERP_FI$fin_glm_ab_item_cf-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-list-ERP_FI$fin_glm_ab_item_cf-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{((data?.code || \"\") + (data?.name || \"\")) || \"现金流量项目详情\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindFlowConfig": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "required": true, "valueConfig": {"action": {"selector": "", "target": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail"}, "type": "action"}}], "service": "ERP_FI$FIN_GLM_AB_ITEM_CF_DELETE_EVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_FI$fin_glm_cash_ab_item_new_view-list-ERP_FI$fin_glm_ab_item_cf"}, {"action": "Message", "message": "删除成功"}, {"action": "PageJump", "target": "list"}], "executeLogic": "BindFlow"}, "confirmOn": "off", "label": "删除", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "ERP_FI$FIN_GLM_AB_ITEM_VIEW-editView-ERP_FI$fin_glm_ab_item_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "route.recordId", "name": "recordId", "type": "expression"}], "type": "NewPage"}}]}, "confirmOn": "off", "label": "编辑", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "row", "target": "ERP_FI$FIN_GLM_AB_ITEM_VIEW-editView-ERP_FI$fin_glm_ab_item_cf", "title": "status", "type": "VarValue", "val": "status", "value": "status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-field-abItemCode", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "abItemCode", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "editable": false, "label": "现金流量项目编码", "name": "abItemCode", "type": "TEXT"}}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-field-abItemName", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "abItemName", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请输入"}, "editable": false, "label": "现金流量项目名称", "name": "abItemName", "type": "TEXT"}}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-field-abItemType", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "abItemType", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "editable": false, "label": "项目类型", "name": "abItemType", "type": "SELECT"}}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-field-cashFlowDirec", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "cashFlowDirec", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择"}, "editable": false, "label": "现金流向", "name": "cashFlowDirec", "type": "SELECT"}}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-field-operActiv", "name": "DetailField", "props": {"componentProps": {"defaultValue": true, "fieldAlias": "operActiv", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "editable": false, "label": "经营活动", "name": "operActiv", "type": "BOOL"}}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-field-netProf", "name": "DetailField", "props": {"componentProps": {"defaultValue": false, "fieldAlias": "netProf", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择"}, "editable": false, "label": "净利润", "name": "netProf", "type": "BOOL"}}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-field-excRateFlu", "name": "DetailField", "props": {"componentProps": {"defaultValue": false, "fieldAlias": "excRateFlu", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择"}, "editable": false, "label": "汇率变动", "name": "excRateFlu", "type": "BOOL"}}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-field-estGenComId", "name": "DetailField", "props": {"componentProps": {"columns": ["orgDimensionId", "attrGroupId", "orgCode", "orgName", "orgParentId", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode"], "fieldAlias": "estGenComId", "label": "选择创建组织", "labelField": "orgCode", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "sys_common$org_struct_md_PAGING_DATA_SERVICE"}}, "displayComponentProps": {"labelField": ["orgName"], "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$org_struct_md_PAGING_DATA_SERVICE", "type": "InvokeService"}, "labelField": [], "modalProps": {"size": "middle", "width": 720}, "modelAlias": "sys_common$org_struct_md", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "editable": false, "label": "创建组织", "name": "estGenComId", "type": "OBJECT"}}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-field-finGlmCftTypeCf", "name": "DetailField", "props": {"componentProps": {"columns": ["cfsTypeCode", "cfsTypeName", "cfsOrg"], "fieldAlias": "finGlmCfsTypeCf", "label": "选择现金流量项目表", "labelField": "cfsTypeName", "modelAlias": "ERP_FI$fin_glm_cfs_type_cf", "parentModelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FI$fin_glm_cfs_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_FI$fin_glm_cfs_type_cf_PAGING_DATA_SERVICE"}}, "displayComponentProps": {"modelAlias": "ERP_FI$fin_glm_cfs_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "cfsTypeName", "modelAlias": "ERP_FI$fin_glm_cfs_type_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "现金流项目表名称", "name": "cfsTypeName", "required": false, "type": "TEXT", "width": 120}], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_FI$fin_glm_cfs_type_cf", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_FI$fin_glm_cfs_type_cf"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_FI$fin_glm_cfs_type_cf_PAGING_DATA_SERVICE", "type": "InvokeService"}, "labelField": ["cfsTypeName"], "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_FI$fin_glm_cfs_type_cf", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "editable": false, "label": "现金流量项目表", "name": "finGlmCfsTypeCf", "required": false, "type": "OBJECT", "width": 120}}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-HzUoDiXYQz-RECKuhoIma", "name": "DetailField", "props": {"componentProps": {"columns": ["abItemCode", "abItemName", "abItemType", "cashFlowDirec", "operActiv", "netProf", "excRateFlu", "estGenComId", "finGlmAbCftOrgAssCf", "finGlmCftTypeCf", "finGlmCashSubAssCf", "finGlmCfsTypeCf", "parentId", "<PERSON><PERSON><PERSON><PERSON>"], "fieldAlias": "parentId", "label": "选择父节点", "labelField": "abItemName", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "parentModelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FI$fin_glm_ab_item_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_FI$fin_glm_ab_item_cf_PAGING_DATA_SERVICE"}}, "displayComponentProps": {"modelAlias": "ERP_FI$fin_glm_ab_item_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "abItemName", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "现金流量项目名称", "name": "abItemName", "required": false, "type": "TEXT", "width": 120}], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_FI$fin_glm_ab_item_cf"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_FI$fin_glm_ab_item_cf_PAGING_DATA_SERVICE", "type": "InvokeService"}, "labelField": "abItemName", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "editable": false, "label": "父节点", "name": "parentId", "required": false, "type": "SELFRELATION", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-5MikenoqSbi9laFRglb0w", "name": "DetailField", "props": {"componentProps": {"defaultValue": true, "fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentProps": {"fields": [], "filterFields": [], "labelField": []}, "editComponentType": "Switch", "editable": false, "label": "是否叶子节点", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "type": "BOOL", "width": 120}, "type": "Widget"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail-defaultGroup", "name": "DetailGroupItem", "props": {"defaultCollapsed": false, "title": false}, "type": "Layout"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-4eYhqwih_3RW2iaxrcLn2", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_cft_org_ass_cf-list-batch-actions", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_cft_org_ass_cf-list-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-gRfDqj3L032JNuudYBl2-", "name": "Field", "props": {"align": "left", "componentProps": {"columns": ["orgDimensionId", "attrGroupId", "orgCode", "orgName", "orgParentId", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode"], "fieldAlias": "estGenComId", "label": "选择使用组织", "labelField": "orgCode", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "sys_common$org_struct_md_PAGING_DATA_SERVICE"}}, "displayComponentProps": {"labelField": ["orgName", "orgCode"], "modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "hidden": false, "label": "使用组织", "modelAlias": "sys_common$org_struct_md", "name": "estGenComId", "type": "OBJECT"}, "type": "Widget"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-HxVAa_AMIkrXckf917Fls", "name": "Fields", "props": {}, "type": "Meta"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_cft_org_ass_cf-list", "name": "Table", "props": {"fieldName": "finGlmAbCftOrgAssCf", "flow": {"context$": "$context", "name": "finGlmAbCftOrgAssCf", "type": "RelationData"}, "label": "表格", "mode": "simple", "modelAlias": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "serviceKey": "$context", "subTableEnabled": false}, "type": "Container"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_cft_org_ass_cf-customDetailField", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "finGlmAbCftOrgAssCf"}, "type": "Meta"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-3RJam2KB4s8bC9TkTawJn", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"key": "npT0EbOZvwOjeDnhOhAfM", "label": "主体信息"}, {"key": "sR7ze61-9yxKHPZabZrFa", "label": "使用组织", "lookup": [{"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "r8S6yv9WF23EAaOHgVxA_", "leftValue": {"fieldType": "Number", "scope": "form", "title": "父节点.ID", "type": "VarValue", "val": "parentId.id", "value": "ERP_FI$fin_glm_ab_item_cf.parentId.id", "valueType": "VAR", "varVal": "parentId.id", "varValue": [{"valueKey": "parentId", "valueName": "parentId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NULL", "type": "ConditionLeaf"}], "id": "AF7OhoL6dSxKOu_ae3DVO", "logicOperator": "OR", "type": "ConditionGroup"}, {"conditions": [{"id": "EVS6akre4HeEkBjilghE7", "leftValue": {"fieldType": "Number", "scope": "form", "title": "父节点.ID", "type": "VarValue", "val": "parentId.id", "value": "ERP_FI$fin_glm_ab_item_cf.parentId.id", "valueType": "VAR", "varVal": "parentId.id", "varValue": [{"valueKey": "parentId", "valueName": "parentId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}, {"id": "mWTrov3MbdZJWehEe24dI", "leftValue": {"fieldType": "Boolean", "scope": "form", "title": "是否叶子节点", "type": "VarValue", "val": "<PERSON><PERSON><PERSON><PERSON>", "value": "ERP_FI$fin_glm_ab_item_cf.isLeaf", "valueType": "VAR", "varVal": "<PERSON><PERSON><PERSON><PERSON>", "varValue": [{"valueKey": "<PERSON><PERSON><PERSON><PERSON>", "valueName": "<PERSON><PERSON><PERSON><PERSON>"}]}, "operator": "EQ", "rightValue": {"constValue": "false", "fieldType": "Boolean", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "TBoRBw9tTYwtx7Et2El8g", "leftValue": {"fieldType": "Number", "scope": "form", "title": "ID", "type": "VarValue", "val": "id", "value": "ERP_FI$fin_glm_ab_item_cf.id", "valueType": "VAR", "varVal": "id", "varValue": [{"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "HG17hdssvy0T-cfzIfAFi", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "h7k6I2nLp7TvLRGzPIrVH", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": true, "required": false}, "key": "RwwyV9KHyomX52plCEBd8", "operator": null, "trigger": "all", "valueRules": null}]}], "lookup": [], "underline": true}, "type": "Layout"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail", "name": "Detail", "props": {"flow": {"containerKey": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail", "context$": "$context", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_FI$fin_glm_ab_item_cf"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_FI$fin_glm_ab_item_cf_FIND_DATA_BY_ID_SERVICE_TIUqpW2", "type": "InvokeService"}, "layout": "horizontal", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "onFinish$": "(values) => invokeSystemService(\"ERP_FI$SYS_MasterData_SaveDataService\", \"ERP_FI$fin_glm_ab_item_cf\", {...data, ...values}).then(() => $(\"ERP_FI$FIN_GLM_AB_ITEM_VIEW-detailView-page\").action(\"reload\"))", "serviceKey": "ERP_FI$SYS_FindDataByIdService"}, "type": "Container"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showBack": true, "showFooter": false, "showHeader": true}, "type": "Container"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-view", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" + ((data?.code || \"\") + (data?.name || \"现金流量项目\")) : \"新建现金流量项目\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-abItemCode", "name": "FormField", "props": {"componentProps": {"fieldAlias": "abItemCode", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "现金流量项目编码", "lookup": [{"conditionGroup": {"conditions": [], "id": "_kkn1c2xI7Zlxk2fQTPDl", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "ERP_FI$fin_glm_cash_ab_item_view-xfQ5TRePVuAlHJS2IbSqm", "trigger": "all", "valueRules": null}], "name": "abItemCode", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-abItemName", "name": "FormField", "props": {"componentProps": {"fieldAlias": "abItemName", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "现金流量项目名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "ERP_FI$fin_glm_cash_ab_item_view-QdMQ_CAEo6XOMxCLT_PK7", "valueRules": null}], "name": "abItemName", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-finGlmCftTypeCf", "name": "FormField", "props": {"componentProps": {"columns": ["cfsTypeCode", "cfsTypeName", "cfsOrg"], "fieldAlias": "finGlmCfsTypeCf", "label": "选择现金流量项目表", "labelField": "cfsTypeName", "modelAlias": "ERP_FI$fin_glm_cfs_type_cf", "parentModelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FI$fin_glm_cfs_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_FI$fin_glm_cfs_type_cf_PAGING_DATA_SERVICE"}}, "displayComponentProps": {"modelAlias": "ERP_FI$fin_glm_cfs_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "cfsTypeName", "modelAlias": "ERP_FI$fin_glm_cfs_type_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "现金流项目表名称", "name": "cfsTypeName", "required": false, "type": "TEXT", "width": 120}], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_FI$fin_glm_cfs_type_cf", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_FI$fin_glm_cfs_type_cf"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_FI$fin_glm_cfs_type_cf_PAGING_DATA_SERVICE", "type": "InvokeService"}, "labelField": "cfsTypeName", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_FI$fin_glm_cfs_type_cf", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "现金流量项目表", "lookup": [{"conditionGroup": {"conditions": [{"conditions": [{"id": "rZ9RfVIjp6gtxFf9eaWpU", "leftValue": {"fieldType": "Model", "scope": "form", "title": "父节点", "type": "VarValue", "val": "parentId", "value": "ERP_FI$fin_glm_ab_item_cf.parentId", "valueType": "VAR", "varVal": "parentId", "varValue": [{"valueKey": "parentId", "valueName": "parentId"}]}, "operator": "IS_NULL", "type": "ConditionLeaf"}], "id": "uOJtD8irn_CR4cmtGuiO6", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "YJDPAyEwiUrU9g3dOatrD", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "UreGvErA_Qk4Khvh5XqyZ", "trigger": "all", "valueRules": null}, {"action": "get", "conditionGroup": {"conditions": [{"conditions": [{"id": "NTIVqpWXpjQPh11aUXeNX", "leftValue": {"fieldType": "Model", "scope": "form", "title": "父节点", "type": "VarValue", "val": "parentId", "value": "ERP_FI$fin_glm_ab_item_cf.parentId", "valueType": "VAR", "varVal": "parentId", "varValue": [{"valueKey": "parentId", "valueName": "parentId"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}, {"id": "24LEWZw_ZeiocDBpD5VWZ", "leftValue": {"fieldType": "Number", "scope": "form", "title": "ID", "type": "VarValue", "val": "id", "value": "ERP_FI$fin_glm_ab_item_cf.id", "valueType": "VAR", "varVal": "id", "varValue": [{"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NULL", "type": "ConditionLeaf"}], "id": "tPPLoi0d8U_rsDDojPc__", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "7uICfvvC_kjWVcNUxZu1m", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "RkSWmNVhjB3gC0cwnWnHh", "operator": "FIELD", "trigger": "all", "valueRules": {"scope": "form", "type": "FIELD", "val": "parentId.finGlmCfsTypeCf", "value": "ERP_FI$fin_glm_ab_item_cf.parentId.finGlmCfsTypeCf", "valueType": "model"}}], "name": "finGlmCfsTypeCf", "required": false, "rules": [], "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-iGgR4VBEjqFevpIusbntQ", "name": "FormField", "props": {"componentProps": {"fieldAlias": "estGenComId", "placeholder": "请输入", "serviceInfo": {"findServiceKey": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "sys_common$org_struct_md_PAGING_DATA_SERVICE"}}, "displayComponentProps": {"modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "组织编码", "name": "orgCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "组织名称", "name": "orgName", "required": false, "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织编码", "name": "orgCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织名称", "name": "orgName", "required": false, "type": "TEXT", "width": 120}], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "orgStatus", "fieldName": "orgStatus", "fieldType": "Text", "valueConfig": {"type": "const", "value": "ENABLED"}}, {"fieldAlias": "orgDimensionCode", "fieldName": "orgDimensionCode", "fieldType": "Text", "valueConfig": {"type": "const", "value": "FIN_ORG_GRP"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "type": "InvokeService"}, "labelField": ["orgCode"], "modalProps": {"size": "middle", "width": 720}, "modelAlias": "sys_common$org_struct_md", "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "Z833Eeo-DLqw2fzUakMFT", "leftValue": {"fieldType": "Enum", "title": "状态", "type": "VarValue", "val": "orgStatus", "value": "sys_common$org_struct_md.orgStatus", "valueType": "VAR", "varVal": "orgStatus", "varValue": [{"valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "wOqHUxfdgH1XVc1QaSWNw", "leftValue": {"fieldType": "Text", "title": "[object Object].维度编码", "type": "VarValue", "val": "orgDimensionId.orgDimensionCode", "value": "sys_common$org_struct_md.orgDimensionId.orgDimensionCode", "valueType": "VAR", "varVal": "orgDimensionId.orgDimensionCode", "varValue": [{"valueKey": "orgDimensionId", "valueName": "orgDimensionId"}, {"valueKey": "orgDimensionCode", "valueName": "orgDimensionCode"}]}, "operator": "EQ", "rightValue": {"constValue": "FIN_ORG_GRP", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "R3Uu4oPeLpUnYUa4UlKW4", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "-bfcYmbo9WXU8q50dcLiL", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": "$context"}, "editComponentType": "RelationSelect", "label": "创建组织", "lookup": [{"action": "get", "conditionGroup": {"conditions": [{"conditions": [{"id": "qMroHVo38-2D0s9nVa_tK", "leftValue": {"fieldType": "Model", "scope": "form", "title": "现金流量项目表", "type": "VarValue", "val": "finGlmCfsTypeCf", "value": "ERP_FI$fin_glm_ab_item_cf.finGlmCfsTypeCf", "valueType": "VAR", "varVal": "finGlmCfsTypeCf", "varValue": [{"valueKey": "finGlmCfsTypeCf", "valueName": "finGlmCfsTypeCf"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "ojAiGALfPjRlKMMd1CdeF", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "YFRH2723pfEX5oSi4IFHs", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "Sewc8y0A8davAmn-IYCTM", "operator": "FIELD", "trigger": "all", "valueRules": {"scope": "form", "type": "FIELD", "val": "finGlmCfsTypeCf.cfsOrg", "value": "ERP_FI$fin_glm_ab_item_cf.finGlmCfsTypeCf.cfsOrg", "valueType": "model"}}, {"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "clZxu3TPRK4Firf_E-k0K", "leftValue": {"fieldType": "Model", "scope": "form", "title": "现金流量项目表", "type": "VarValue", "val": "finGlmCfsTypeCf", "value": "ERP_FI$fin_glm_ab_item_cf.finGlmCfsTypeCf", "valueType": "VAR", "varVal": "finGlmCfsTypeCf", "varValue": [{"valueKey": "finGlmCfsTypeCf", "valueName": "finGlmCfsTypeCf"}]}, "operator": "IS_NULL", "type": "ConditionLeaf"}], "id": "dNDc2PC1gWbq_Flm2Rs0F", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "GR2XUZLgL3fM_XyAkYc89", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "FcgQKTbshsM8GSPjgaOdw", "operator": null, "trigger": "all", "valueRules": null}], "name": "estGenComId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-abItemType", "name": "FormField", "props": {"componentProps": {"fieldAlias": "abItemType", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "hidden": false, "label": "项目类型", "lookup": [{"action": "get", "componentProps": [], "conditionGroup": {"conditions": [{"conditions": [{"id": "SA9UiT1UVSP7xRL0o5E9m", "leftValue": {"fieldType": "Number", "scope": "form", "title": "父节点.ID", "type": "VarValue", "val": "parentId.id", "value": "ERP_FI$fin_glm_ab_item_cf.parentId.id", "valueType": "VAR", "varVal": "parentId.id", "varValue": [{"valueKey": "parentId", "valueName": "parentId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}, {"id": "L1oyLh6dr8vmvRGx80MZs", "leftValue": {"fieldType": "Number", "scope": "form", "title": "ID", "type": "VarValue", "val": "id", "value": "ERP_FI$fin_glm_ab_item_cf.id", "valueType": "VAR", "varVal": "id", "varValue": [{"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NULL", "type": "ConditionLeaf"}], "id": "obO8Mb5u3TQHIsh5v6jTA", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "aX3dXXsM9iIfD0YMSq2N9", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "Z1WHpIYtV5sNM_RKWSXoS", "operator": "FIELD", "trigger": "all", "valueRules": {"scope": "form", "type": "FIELD", "val": "parentId.abItemType", "value": "ERP_FI$fin_glm_ab_item_cf.parentId.abItemType", "valueType": "model"}}, {"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "2rYdE4lBXvUUU4dVAuZEE", "leftValue": {"fieldType": "Number", "scope": "form", "title": "父节点.ID", "type": "VarValue", "val": "parentId.id", "value": "ERP_FI$fin_glm_ab_item_cf.parentId.id", "valueType": "VAR", "varVal": "parentId.id", "varValue": [{"valueKey": "parentId", "valueName": "parentId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NULL", "type": "ConditionLeaf"}], "id": "t2FBSPKskGvuG-my7dYMs", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "lqsUobBcMzMiWsigJBOvy", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "9CzGds5HE2IJcJA9UjSrj", "operator": null, "trigger": "all", "valueRules": null}], "name": "abItemType", "rules": [], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-cashFlowDirec", "name": "FormField", "props": {"componentProps": {"fieldAlias": "cashFlowDirec", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "hidden": false, "label": "现金流向", "lookup": [{"action": "get", "conditionGroup": {"conditions": [{"conditions": [{"id": "t43hVrqUEV87aY28V3jEi", "leftValue": {"fieldType": "Model", "scope": "form", "title": "父节点", "type": "VarValue", "val": "parentId", "value": "ERP_FI$fin_glm_ab_item_cf.parentId", "valueType": "VAR", "varVal": "parentId", "varValue": [{"valueKey": "parentId", "valueName": "parentId"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "AYzfSerH8eza-_IsZ7Xya", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "HVSganS6Q8OxhIcOdZT12", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "9S4rsSaXUQh9LiDUmBbYU", "operator": "FIELD", "trigger": "all", "valueRules": {"scope": "form", "type": "FIELD", "val": "parentId.cashFlowDirec", "value": "ERP_FI$fin_glm_ab_item_cf.parentId.cashFlowDirec", "valueType": "model"}}, {"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "Io2EsarFreOpNYn_o9Ugt", "leftValue": {"fieldType": "Model", "scope": "form", "title": "父节点", "type": "VarValue", "val": "parentId", "value": "ERP_FI$fin_glm_ab_item_cf.parentId", "valueType": "VAR", "varVal": "parentId", "varValue": [{"valueKey": "parentId", "valueName": "parentId"}]}, "operator": "IS_NULL", "type": "ConditionLeaf"}], "id": "yDexVCMB-4WtDtTUBrOLS", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "iaEtrpFxEw2RZxfLqJKaa", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "required": true}, "key": "EUsoRLiYpfQZOxHk-PcP6", "operator": null, "trigger": "all", "valueRules": null}], "name": "cashFlowDirec", "rules": [], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-operActiv", "name": "FormField", "props": {"componentProps": {"defaultValue": true, "fieldAlias": "operActiv", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择"}, "hidden": false, "initialValue": true, "label": "经营活动", "name": "operActiv", "rules": [], "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-netProf", "name": "FormField", "props": {"componentProps": {"defaultValue": false, "fieldAlias": "netProf", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择"}, "hidden": false, "initialValue": false, "label": "净利润", "name": "netProf", "rules": [], "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-excRateFlu", "name": "FormField", "props": {"componentProps": {"defaultValue": false, "fieldAlias": "excRateFlu", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "hidden": false, "initialValue": false, "label": "汇率变动", "name": "excRateFlu", "rules": [], "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-7R84l_Aao254zmY4VdMcN", "name": "FormField", "props": {"componentProps": {"columns": ["abItemCode", "abItemName", "abItemType", "cashFlowDirec", "operActiv", "netProf", "excRateFlu", "estGenComId", "finGlmAbCftOrgAssCf", "finGlmCftTypeCf", "finGlmCashSubAssCf", "finGlmCfsTypeCf", "parentId", "<PERSON><PERSON><PERSON><PERSON>"], "fieldAlias": "parentId", "label": "选择父节点", "labelField": "abItemName", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "parentModelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FI$fin_glm_ab_item_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_FI$fin_glm_ab_item_cf_PAGING_DATA_SERVICE"}}, "displayComponentProps": {"flow": {"context$": "$context", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_FI$fin_glm_ab_item_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeService"}, "labelField": ["abItemName"], "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "reverseConstructFlow": {"context$": "$context", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "params": [{"elements": [], "fieldAlias": "request", "fieldName": "request", "fieldType": "Pageable"}], "serviceKey": "ERP_FI$fin_glm_ab_item_cf_REVERSE_CONSTRUCT_TREE_SERVICE", "type": "InvokeService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "params": [{"elements": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_FI$fin_glm_ab_item_cf"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Pageable"}], "serviceKey": "ERP_FI$fin_glm_ab_item_cf_FIND_TREE_CHILDREN_DATA_SERVICE", "type": "InvokeService"}, "leafOnly": false, "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "reverseConstructFlow": {"context$": "$context", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "params": [{"elements": [], "fieldAlias": "request", "fieldName": "request", "fieldType": "Pageable"}], "serviceKey": "ERP_FI$fin_glm_ab_item_cf_REVERSE_CONSTRUCT_TREE_SERVICE", "type": "InvokeService"}, "showScope": "all", "showSearch": false, "tableCondition": null, "tableConditionContext$": null, "treeSelectField": "parentId"}, "editComponentType": "TreeSelect", "label": "父节点", "lookup": [{"action": "get", "conditionGroup": {"conditions": [{"conditions": [{"id": "YQY1oEtnV874By6QJL2j5", "leftValue": {"fieldType": "Number", "scope": "form", "title": "ID", "type": "VarValue", "val": "id", "value": "ERP_FI$fin_glm_ab_item_cf.id", "valueType": "VAR", "varVal": "id", "varValue": [{"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NULL", "type": "ConditionLeaf"}], "id": "dTPluaTpUAIdDZ6eYJfSM", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "QeOqg5ZBU4NhvJe-pqSD4", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "required": false}, "key": "uDyLdXoPNdk8jbTgO5_Lt", "operator": "FIELD", "trigger": "onload", "valueRules": {"scope": "", "type": "FIELD", "val": "{ id: route.query.parentId, \nfinGlmCfsTypeCf: route.query.finGlmCfsTypeCf,\nabItemType: route.query.abItemType, \ncashFlowDirec: route.query.cashFlowDirec}", "value": "{ id: route.query.parentId, \nfinGlmCfsTypeCf: route.query.finGlmCfsTypeCf,\nabItemType: route.query.abItemType, \ncashFlowDirec: route.query.cashFlowDirec}", "valueType": "expression"}}], "name": "parentId", "required": false, "type": "SELFRELATION", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-_FGaOP9Yp2Tyjkx6Fxrji", "name": "FormField", "props": {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "initialValue": true, "label": "是否叶子节点", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": false}, "key": "-eedhjqccg8z-iondjCFK", "valueRules": null}], "name": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "type": "BOOL", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-id", "name": "FormField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "hidden": true, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-createdBy", "name": "FormField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "ERP_FI$user", "parentModelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FI$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_FI$user_PAGING_DATA_SERVICE"}}, "displayComponentProps": {"modelAlias": "ERP_FI$user"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "modelAlias": "ERP_FI$user", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "用户名", "name": "username", "required": false, "type": "TEXT", "width": 120}], "labelField": "username", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_FI$user", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": true, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-updatedBy", "name": "FormField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "ERP_FI$user", "parentModelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_FI$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_FI$user_PAGING_DATA_SERVICE"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-createdAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-updatedAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form-defaultGroup", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": false}, "type": "Layout"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-3lW4PwWmG0YshQcWb5inb", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-T0l9SnkhK0dY3OiKJnncT", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-E-v1269bf7PlqySbY9BdI", "name": "引用组织 (窗口)", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "refresh": true, "type": "Modal"}}]}, "confirmOn": "off", "label": "引用组织", "showCondition": {}, "type": "default"}, "type": "Widget"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_cft_org_ass_cf-tableform-batchActions", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_cft_org_ass_cf-tableform-action-deleteLine", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "SystemAction", "name": "remove"}]}, "confirmOn": "off", "label": "删除", "showCondition": {}, "type": "text"}, "type": "Widget"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_cft_org_ass_cf-tableform-reordActions", "name": "RecordActions", "props": {"width": 120}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-qGl565gRTUAxRa6o94UnE", "name": "Field", "props": {"componentProps": {"fieldAlias": "estGenComId", "placeholder": "请输入", "serviceInfo": {"findServiceKey": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "sys_common$org_struct_md_PAGING_DATA_SERVICE"}}, "displayComponentProps": {"modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "orgDimensionCode", "fieldName": "orgDimensionCode", "fieldType": "Text", "valueConfig": {"type": "const", "value": "FIN_ORG_GRP"}}, {"fieldAlias": "orgStatus", "fieldName": "orgStatus", "fieldType": "Text", "valueConfig": {"type": "const", "value": "ENABLED"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "sys_common$ORG_STRUCT_MD_PAGING_DATA_SERVICE", "type": "InvokeService"}, "labelField": ["orgCode"], "modalProps": {"size": "middle", "width": 720}, "modelAlias": "sys_common$org_struct_md", "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "dhNMj2HKrRdm7QUk8XOKO", "leftValue": {"fieldType": "Text", "title": "组织维度.维度编码", "type": "VarValue", "val": "orgDimensionId.orgDimensionCode", "value": "sys_common$org_struct_md.orgDimensionId.orgDimensionCode", "valueType": "VAR", "varVal": "orgDimensionId.orgDimensionCode", "varValue": [{"valueKey": "orgDimensionId", "valueName": "orgDimensionId"}, {"valueKey": "orgDimensionCode", "valueName": "orgDimensionCode"}]}, "operator": "EQ", "rightValue": {"constValue": "FIN_ORG_GRP", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "xlCZmoCScgvsX6JUPL_3V", "leftValue": {"fieldType": "Enum", "title": "状态", "type": "VarValue", "val": "orgStatus", "value": "sys_common$org_struct_md.orgStatus", "valueType": "VAR", "varVal": "orgStatus", "varValue": [{"valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "ldLLctcZtagTgLRFLtLgM", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "WRjteypPS9rZxQY4q4Qho", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": "$context"}, "editComponentType": "RelationSelect", "hidden": false, "label": "使用组织", "name": "estGenComId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-tfyhzHEZACgxT-asdn6G3", "name": "Fields", "props": {}, "type": "Meta"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_cft_org_ass_cf-tableform", "name": "TableForm", "props": {"creatorPosition": "bottom", "enablePagination": false, "fieldName": "finGlmAbCftOrgAssCf", "fields": [], "hideCreator": false, "hideDefaultDelete": true, "label": "表格表单", "modelAlias": "ERP_FI$fin_glm_ab_cft_org_ass_cf", "serviceInfo": {"findServiceKey": "ERP_FI$FIN_GLM_AB_CFT_ORG_ASS_CF_FIND_SINGLE_DATA_BY_ID_SERVICE", "searchServiceKey": "ERP_FI$FIN_GLM_AB_CFT_ORG_ASS_CF_PAGING_DATA_SERVICE"}, "subTableEnabled": false}}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_cft_org_ass_cf-form-field-finGlmAbCftOrgAssCf", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "finGlmAbCftOrgAssCf"}, "type": "Meta"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-kMa83SeWRDNnTQP0iQxcT", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "name": "Tabs", "props": {"items": [{"key": "phWfx2zE8mxdcZMadXg3v", "label": "主体信息"}, {"key": "NucyICNzCK2vEfvLV8300", "label": "使用组织", "lookup": [{"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "Adh_9sxR41HBoObXMTED2", "leftValue": {"fieldType": "Number", "scope": "form", "title": "父节点.ID", "type": "VarValue", "val": "parentId.id", "value": "ERP_FI$fin_glm_ab_item_cf.parentId.id", "valueType": "VAR", "varVal": "parentId.id", "varValue": [{"valueKey": "parentId", "valueName": "parentId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NULL", "type": "ConditionLeaf"}], "id": "kh99PbjEzLtZVX6iB0YzQ", "logicOperator": "OR", "type": "ConditionGroup"}, {"conditions": [{"id": "6p5UkUVsWsSY7X-uaQzOK", "leftValue": {"fieldType": "Number", "scope": "form", "title": "父节点.ID", "type": "VarValue", "val": "parentId.id", "value": "ERP_FI$fin_glm_ab_item_cf.parentId.id", "valueType": "VAR", "varVal": "parentId.id", "varValue": [{"valueKey": "parentId", "valueName": "parentId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}, {"id": "5pdWYjWxT5ZvbfkU0UyEk", "leftValue": {"fieldType": "Boolean", "scope": "form", "title": "是否叶子节点", "type": "VarValue", "val": "<PERSON><PERSON><PERSON><PERSON>", "value": "ERP_FI$fin_glm_ab_item_cf.isLeaf", "valueType": "VAR", "varVal": "<PERSON><PERSON><PERSON><PERSON>", "varValue": [{"valueKey": "<PERSON><PERSON><PERSON><PERSON>", "valueName": "<PERSON><PERSON><PERSON><PERSON>"}]}, "operator": "EQ", "rightValue": {"constValue": "false", "fieldType": "Boolean", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "oOsQc_waneeR7z3hmNp31", "leftValue": {"fieldType": "Number", "scope": "form", "title": "ID", "type": "VarValue", "val": "id", "value": "ERP_FI$fin_glm_ab_item_cf.id", "valueType": "VAR", "varVal": "id", "varValue": [{"valueKey": "id", "valueName": "id"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "dxnjJ-utcfsRRxbmSFqY2", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "9tOubO1q7k4hyiR8udfZe", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": true, "required": false}, "key": "Ryk4QTixPGzHGr34PpLJg", "operator": null, "trigger": "all", "valueRules": null}]}], "lookup": [{"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "a55Ki4-PdDHEheCBsoXRJ", "leftValue": {"fieldType": "Model", "scope": "form", "title": "父节点", "type": "VarValue", "val": "parentId", "value": "ERP_FI$fin_glm_ab_item_cf.parentId", "valueType": "VAR", "varVal": "parentId", "varValue": [{"valueKey": "parentId", "valueName": "parentId"}]}, "operator": "IS_NULL", "type": "ConditionLeaf"}], "id": "TMcIYr5TIZVCtewSGBDwc", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "sfnLGm00N1GDyfwZnM_jn", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "required": false}, "key": "de_rt-El70qXIfL7Ts8Qw", "operator": null, "trigger": "all", "valueRules": null}], "underline": true}, "type": "Layout"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "context$": "$context", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_FI$fin_glm_ab_item_cf"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_FI$fin_glm_ab_item_cf_FIND_DATA_BY_ID_SERVICE_peKVjk1", "test$": "!!route.recordId", "type": "InvokeService"}, {"containerKey": "", "context$": "$context", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_FI$fin_glm_ab_item_cf"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "route?.query?.copyId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_FI$fin_glm_ab_item_cf_COPY_DATA_CONVERTER_SERVICE", "test$": "!!route.query?.copyId", "type": "InvokeService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "serviceKey": "ERP_FI$SYS_FindDataByIdService"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target$": "route?.action === \"edit\" ? \"show\" : \"list\""}]}, "confirmOn": "off", "label": "取消", "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindFlowConfig": {"params": [{"elements": [{"fieldAlias": "abItemCode", "fieldName": "现金流量项目编码", "fieldType": "TEXT"}, {"fieldAlias": "abItemName", "fieldName": "现金流量项目名称", "fieldType": "TEXT"}, {"fieldAlias": "abItemType", "fieldName": "项目类别", "fieldType": "ENUM"}, {"fieldAlias": "cashFlowDirec", "fieldName": "现金流向", "fieldType": "ENUM"}, {"fieldAlias": "operActiv", "fieldName": "经营活动", "fieldType": "BOOL"}, {"fieldAlias": "netProf", "fieldName": "净利润", "fieldType": "BOOL"}, {"fieldAlias": "excRateFlu", "fieldName": "汇率变动", "fieldType": "BOOL"}, {"fieldAlias": "estGenComId", "fieldName": "创建组织", "fieldType": "OBJECT"}, {"fieldAlias": "finGlmAbCftOrgAssCf", "fieldName": "使用组织", "fieldType": "Array"}, {"fieldAlias": "finGlmCftTypeCf", "fieldName": "现金流量项目类别", "fieldType": "OBJECT"}, {"fieldAlias": "finGlmCashSubAssCf", "fieldName": "组织项目科目关联表", "fieldType": "Array"}, {"fieldAlias": "finGlmCfsTypeCf", "fieldName": "现金流量项目表", "fieldType": "OBJECT"}, {"fieldAlias": "parentId", "fieldName": "父节点", "fieldType": "OBJECT"}, {"fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "fieldName": "是否叶子节点", "fieldType": "BOOL"}, {"fieldAlias": "id", "fieldName": "ID", "fieldType": "NUMBER"}, {"fieldAlias": "created<PERSON>y", "fieldName": "创建人", "fieldType": "OBJECT"}, {"fieldAlias": "updatedBy", "fieldName": "更新人", "fieldType": "OBJECT"}, {"fieldAlias": "createdAt", "fieldName": "创建时间", "fieldType": "DATE"}, {"fieldAlias": "updatedAt", "fieldName": "更新时间", "fieldType": "DATE"}, {"fieldAlias": "version", "fieldName": "版本号", "fieldType": "NUMBER"}, {"fieldAlias": "deleted", "fieldName": "逻辑删除标识", "fieldType": "NUMBER"}, {"fieldAlias": "originOrgId", "fieldName": "所属组织", "fieldType": "NUMBER"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "required": true, "valueConfig": {"action": {"selector": "", "target": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form"}, "type": "action"}}], "service": "ERP_FI$FIN_GLM_AB_ITEM_CF_SAVE_EVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "RefreshTab", "target": ["current", "previous"]}, {"action": "Message", "level": "success", "message": "创建成功"}], "executeLogic": "BindFlow"}, "confirmOn": "off", "label": "保存", "showCondition": {}, "type": "primary"}, "type": "Widget"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showBack": true, "showFooter": true, "showHeader": true}, "type": "Container"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "name": "ColumnPage", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": false, "showHeader": false, "title": "PC端现金流量项目"}, "type": "Container"}, "frontendConfig": {"modules": ["base"]}, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-list", "resources": [{"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-tmAkNzQCtICQYnJeoWPA4", "label": "表格", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-E-v1269bf7PlqySbY9BdI", "label": "引用组织", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-Z5Ick4vfZ_Q_4niSm5XVE", "label": "弹窗内容", "type": "ChildViewBody"}], "relations": [{"key": "sys_common$org_struct_md_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-wnFOwFsS4HE1eBUr5-R3x", "label": "取消", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-E-v1269bf7PlqySbY9BdI", "label": "引用组织", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3fbAjrobL3PDYT35vYuld", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-vQVS9SUP1kFL9Fg2TTuDK", "label": "保存", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-E-v1269bf7PlqySbY9BdI", "label": "引用组织", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3fbAjrobL3PDYT35vYuld", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-s7juUbqQ9jGdrb-LFNX2-", "label": "新建", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-b5DqOBAY4xMck3lTBzvGt", "label": "区块", "type": "Box"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-mvpa4DSrK9YoWw7RysOXN", "label": "树", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-b5DqOBAY4xMck3lTBzvGt", "label": "区块", "type": "Box"}], "relations": [{"key": "ERP_FI$fin_glm_ab_item_cf_FIND_TREE_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FI$fin_glm_ab_item_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_FI$fin_glm_ab_item_cf_FIND_DATA_BY_ID_SERVICE_TIUqpW2", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FI$fin_glm_ab_item_cf"}, "type": "Service"}, {"key": "ERP_FI$fin_glm_ab_item_cf_COPY_DATA_CONVERTER_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FI$fin_glm_ab_item_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-IaF4MFhKebeB6XL-qV1hI", "label": "组织编码", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-E-v1269bf7PlqySbY9BdI", "label": "引用组织", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-Z5Ick4vfZ_Q_4niSm5XVE", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-tmAkNzQCtICQYnJeoWPA4", "label": "表格", "type": "Table"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-LUUtuie-MUPvTauriZxUr", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-u5fsAdNHZc1qxqBCwUiC5", "label": "组织名称", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-E-v1269bf7PlqySbY9BdI", "label": "引用组织", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-Z5Ick4vfZ_Q_4niSm5XVE", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-tmAkNzQCtICQYnJeoWPA4", "label": "表格", "type": "Table"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-LUUtuie-MUPvTauriZxUr", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-bNCnhiWH5Z0oaGCQROMz3", "label": "新建子级", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-b5DqOBAY4xMck3lTBzvGt", "label": "区块", "type": "Box"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-mvpa4DSrK9YoWw7RysOXN", "label": "树", "type": "Tree"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-lxuUQ5DOW5hxAL7ibp-YO", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-WHjq8LCswPE_J15QItTkf", "label": "删除", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-b5DqOBAY4xMck3lTBzvGt", "label": "区块", "type": "Box"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-mvpa4DSrK9YoWw7RysOXN", "label": "树", "type": "Tree"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-lxuUQ5DOW5hxAL7ibp-YO", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "ERP_FI$FIN_GLM_AB_ITEM_CF_DELETE_EVENT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-bmTgplxuMjCagdtm6aUPr", "label": "编辑", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-b5DqOBAY4xMck3lTBzvGt", "label": "区块", "type": "Box"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-mvpa4DSrK9YoWw7RysOXN", "label": "树", "type": "Tree"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-lxuUQ5DOW5hxAL7ibp-YO", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail", "label": "详情", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_FI$fin_glm_ab_item_cf_FIND_DATA_BY_ID_SERVICE_TIUqpW2", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FI$fin_glm_ab_item_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-action-cancel", "label": "取消", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-action-save", "label": "保存", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "ERP_FI$FIN_GLM_AB_ITEM_CF_SAVE_EVENT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-actions-delete", "label": "删除", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "ERP_FI$FIN_GLM_AB_ITEM_CF_DELETE_EVENT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-actions-edit", "label": "编辑", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-abItemCode", "label": "现金流量项目编码", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3lW4PwWmG0YshQcWb5inb", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-abItemName", "label": "现金流量项目名称", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3lW4PwWmG0YshQcWb5inb", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-finGlmCftTypeCf", "label": "现金流量项目表", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3lW4PwWmG0YshQcWb5inb", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "ERP_FI$fin_glm_cfs_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FI$fin_glm_cfs_type_cf"}, "type": "Service"}, {"key": "ERP_FI$fin_glm_cfs_type_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FI$fin_glm_cfs_type_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-iGgR4VBEjqFevpIusbntQ", "label": "创建组织", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3lW4PwWmG0YshQcWb5inb", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "sys_common$org_struct_md_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-abItemType", "label": "项目类型", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3lW4PwWmG0YshQcWb5inb", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-cashFlowDirec", "label": "现金流向", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3lW4PwWmG0YshQcWb5inb", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-operActiv", "label": "经营活动", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3lW4PwWmG0YshQcWb5inb", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-netProf", "label": "净利润", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3lW4PwWmG0YshQcWb5inb", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-excRateFlu", "label": "汇率变动", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3lW4PwWmG0YshQcWb5inb", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-7R84l_Aao254zmY4VdMcN", "label": "父节点", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3lW4PwWmG0YshQcWb5inb", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "ERP_FI$fin_glm_ab_item_cf_FIND_DATA_BY_ID_SERVICE_TIUqpW2", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FI$fin_glm_ab_item_cf"}, "type": "Service"}, {"key": "ERP_FI$fin_glm_ab_item_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FI$fin_glm_ab_item_cf"}, "type": "Service"}, {"key": "ERP_FI$fin_glm_ab_item_cf_REVERSE_CONSTRUCT_TREE_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FI$fin_glm_ab_item_cf"}, "type": "Service"}, {"key": "ERP_FI$fin_glm_ab_item_cf_FIND_TREE_CHILDREN_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FI$fin_glm_ab_item_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-_FGaOP9Yp2Tyjkx6Fxrji", "label": "是否叶子节点", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3lW4PwWmG0YshQcWb5inb", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-id", "label": "ID", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3lW4PwWmG0YshQcWb5inb", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-createdBy", "label": "创建人", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3lW4PwWmG0YshQcWb5inb", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "ERP_FI$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FI$user"}, "type": "Service"}, {"key": "ERP_FI$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FI$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-updatedBy", "label": "更新人", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3lW4PwWmG0YshQcWb5inb", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "ERP_FI$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FI$user"}, "type": "Service"}, {"key": "ERP_FI$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FI$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-createdAt", "label": "创建时间", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3lW4PwWmG0YshQcWb5inb", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-updatedAt", "label": "更新时间", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3lW4PwWmG0YshQcWb5inb", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-version", "label": "版本号", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3lW4PwWmG0YshQcWb5inb", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-field-deleted", "label": "逻辑删除标识", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3lW4PwWmG0YshQcWb5inb", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_cft_org_ass_cf-tableform", "label": "表格表单", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-kMa83SeWRDNnTQP0iQxcT", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_cft_org_ass_cf-form-field-finGlmAbCftOrgAssCf", "label": "自定义表单字段", "type": "CustomFormField"}], "relations": [{"key": "ERP_FI$FIN_GLM_AB_CFT_ORG_ASS_CF_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FI$fin_glm_ab_cft_org_ass_cf"}, "type": "Service"}, {"key": "ERP_FI$FIN_GLM_AB_CFT_ORG_ASS_CF_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FI$fin_glm_ab_cft_org_ass_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-field-abItemCode", "label": "现金流量项目编码", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-4eYhqwih_3RW2iaxrcLn2", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-field-abItemName", "label": "现金流量项目名称", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-4eYhqwih_3RW2iaxrcLn2", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-field-abItemType", "label": "项目类型", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-4eYhqwih_3RW2iaxrcLn2", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-field-cashFlowDirec", "label": "现金流向", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-4eYhqwih_3RW2iaxrcLn2", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-field-operActiv", "label": "经营活动", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-4eYhqwih_3RW2iaxrcLn2", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-field-netProf", "label": "净利润", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-4eYhqwih_3RW2iaxrcLn2", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-field-excRateFlu", "label": "汇率变动", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-4eYhqwih_3RW2iaxrcLn2", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-field-estGenComId", "label": "创建组织", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-4eYhqwih_3RW2iaxrcLn2", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [{"key": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}, {"key": "sys_common$org_struct_md_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-field-finGlmCftTypeCf", "label": "现金流量项目表", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-4eYhqwih_3RW2iaxrcLn2", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [{"key": "ERP_FI$fin_glm_cfs_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FI$fin_glm_cfs_type_cf"}, "type": "Service"}, {"key": "ERP_FI$fin_glm_cfs_type_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FI$fin_glm_cfs_type_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-HzUoDiXYQz-RECKuhoIma", "label": "父节点", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-4eYhqwih_3RW2iaxrcLn2", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [{"key": "ERP_FI$fin_glm_ab_item_cf_FIND_DATA_BY_ID_SERVICE_TIUqpW2", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FI$fin_glm_ab_item_cf"}, "type": "Service"}, {"key": "ERP_FI$fin_glm_ab_item_cf_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_FI$fin_glm_ab_item_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-5MikenoqSbi9laFRglb0w", "label": "是否叶子节点", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-4eYhqwih_3RW2iaxrcLn2", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_cft_org_ass_cf-list", "label": "表格", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3RJam2KB4s8bC9TkTawJn", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_cft_org_ass_cf-customDetailField", "label": "自定义详情字段", "type": "CustomDetailField"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-T0l9SnkhK0dY3OiKJnncT", "label": "引用组织", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-kMa83SeWRDNnTQP0iQxcT", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_cft_org_ass_cf-form-field-finGlmAbCftOrgAssCf", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_cft_org_ass_cf-tableform", "label": "表格表单", "type": "TableForm"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_cft_org_ass_cf-tableform-batchActions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_cft_org_ass_cf-tableform-action-deleteLine", "label": "删除", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-kMa83SeWRDNnTQP0iQxcT", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_cft_org_ass_cf-form-field-finGlmAbCftOrgAssCf", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_cft_org_ass_cf-tableform", "label": "表格表单", "type": "TableForm"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_cft_org_ass_cf-tableform-reordActions", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-qGl565gRTUAxRa6o94UnE", "label": "使用组织", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_item_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-kMa83SeWRDNnTQP0iQxcT", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_cft_org_ass_cf-form-field-finGlmAbCftOrgAssCf", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-editView-ERP_FI$fin_glm_ab_cft_org_ass_cf-tableform", "label": "表格表单", "type": "TableForm"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-tfyhzHEZACgxT-asdn6G3", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$org_struct_md_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_FI$fin_glm_cash_ab_item_new_view-gRfDqj3L032JNuudYBl2-", "label": "使用组织", "path": [{"key": "ERP_FI$fin_glm_cash_ab_item_new_view-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_item_cf-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-3RJam2KB4s8bC9TkTawJn", "label": "页签项", "type": "TabItem"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_cft_org_ass_cf-customDetailField", "label": "自定义详情字段", "type": "CustomDetailField"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-detailView-ERP_FI$fin_glm_ab_cft_org_ass_cf-list", "label": "表格", "type": "Table"}, {"key": "ERP_FI$fin_glm_cash_ab_item_new_view-HxVAa_AMIkrXckf917Fls", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$org_struct_md_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}, {"key": "sys_common$org_struct_md_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "Service"}], "type": "Container"}], "title": "list", "type": "LIST"}}