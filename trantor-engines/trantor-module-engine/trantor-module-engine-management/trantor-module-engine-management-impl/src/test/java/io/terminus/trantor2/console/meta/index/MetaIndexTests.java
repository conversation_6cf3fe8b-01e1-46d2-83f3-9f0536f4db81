package io.terminus.trantor2.console.meta.index;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import io.terminus.trantor2.console.meta.MetaBaseIntegrationTests;
import io.terminus.trantor2.ide.import_export.MetaReadWriteCenter;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.ModuleInfo;
import io.terminus.trantor2.meta.blob.JdbcMetaBlobRepo;
import io.terminus.trantor2.meta.blob.MetaBlob;
import io.terminus.trantor2.meta.blob.MetaBlobImpl;
import io.terminus.trantor2.meta.context.MetaContext;
import io.terminus.trantor2.meta.editor.model.MetaObject;
import io.terminus.trantor2.meta.editor.util.ObjectUtil;
import io.terminus.trantor2.meta.index.MetaIndexAssetRepo;
import io.terminus.trantor2.meta.index.MetaIndexer;
import io.terminus.trantor2.meta.index.TempTree;
import io.terminus.trantor2.meta.management.service.MetaUsageDirection;
import io.terminus.trantor2.meta.management.service.MetaUsageResolver;
import io.terminus.trantor2.meta.object.MetaObjectV2;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.meta.util.ZipUtil;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
public class MetaIndexTests extends MetaBaseIntegrationTests {

    @Autowired
    protected MetaIndexer metaIndexer;

    @Autowired
    protected MetaIndexAssetRepo metaIndexAssetRepo;

    @Autowired
    private MetaReadWriteCenter metaReadWriteCenter;

    @Autowired
    private MetaUsageResolver metaUsageResolver;

    @Test
    void testIndex() {
        // load behaviors from disk (for test)
        ideHelperBean.loadBehaviors();

        try (MockedStatic<KeyUtil> newKeyMock = Mockito.mockStatic(KeyUtil.class, Mockito.CALLS_REAL_METHODS)) {
            AtomicInteger counter = new AtomicInteger(1);
            newKeyMock.when(KeyUtil::newKey).then(invocation -> "random-key-" + counter.getAndIncrement());

            MetaEditAndQueryContext ctx = ideHelperBean.initRepoV2(this.getClass(), "00base", 0L);

            // 0: init base
            {
                // make sure LinkUtil not change props
                ideHelperBean.assertDatasetProject(this.getClass(), "00base", ctx);

                // test base raw
                ideHelperBean.assertDatasetMetaRaw(this.getClass(), "00base_raw", ctx);

                // test base index
                ideHelperBean.assertDatasetIndexAsset(this.getClass(), "00base_index", ctx);
            }
            String baseSnapshotOid = ideHelperBean.doSnapshot(ctx);

            // 01: add ext module
            {
                // add sys_common module (and it's ext)
                ideHelperBean.deltaInsert(this.getClass(), "01add_ext_module", ctx);

                // assert dataset after add ext module
                {
                    ideHelperBean.assertDatasetProject(this.getClass(), "02after_add_ext_module", ctx);
                    ideHelperBean.assertDatasetMetaRaw(this.getClass(), "02after_add_ext_module_raw", ctx);
                    ideHelperBean.assertDatasetIndexAsset(this.getClass(), "02after_add_ext_module_index", ctx);
                }
            }
            String afterAddExtModuleSnapshotOid = ideHelperBean.doSnapshot(ctx);

            // 02: base to base, and re-back to after add ext module
            {
                // reset back to base, and assert
                metaEditService.submitOp(ctx, EditUtil.resetFull(baseSnapshotOid));

                {
                    ideHelperBean.assertDatasetProject(this.getClass(), "00base", ctx);
                    ideHelperBean.assertDatasetMetaRaw(this.getClass(), "00base_raw", ctx);
                    ideHelperBean.assertDatasetIndexAsset(this.getClass(), "00base_index", ctx);
                }

                // reset goto after add ext module, and assert
                metaEditService.submitOp(ctx, EditUtil.resetFull(afterAddExtModuleSnapshotOid));

                {
                    ideHelperBean.assertDatasetProject(this.getClass(), "02after_add_ext_module", ctx);
                    ideHelperBean.assertDatasetMetaRaw(this.getClass(), "02after_add_ext_module_raw", ctx);
                    ideHelperBean.assertDatasetIndexAsset(this.getClass(), "02after_add_ext_module_index", ctx);
                }
            }

            // 02.b1: test RebuildIndexOp
            {
                metaEditService.submitOp(ctx, EditUtil.rebuildIndexOp());
                // assert index not changed
                {
                    ideHelperBean.assertDatasetProject(this.getClass(), "02after_add_ext_module", ctx);
                    ideHelperBean.assertDatasetMetaRaw(this.getClass(), "02after_add_ext_module_raw", ctx);
                    ideHelperBean.assertDatasetIndexAsset(this.getClass(), "02after_add_ext_module_index", ctx);
                }
            }

            // 02.b2: test tree structure
            {
                TempTree tree = metaUsageResolver.findTree(ctx.getTeamId(), Lists.newArrayList("sys_common", "test"), false);
                TempTree view1Forward = metaUsageResolver.findUsageTree(ctx.getTeamId(), "test$view1", null, MetaUsageDirection.Forward, true);
                // assert tree
                ideHelperBean.assertDatasetSimpleJson(this.getClass(), "02after_add_ext_module_tree",
                        ImmutableMap.of(
                                "tree", tree,
                                "view1_forward", view1Forward
                        )
                );
            }

            // 03: test export and import not changes
            {
//                JdbcMetaBlobRepo mock = Mockito.mock(JdbcMetaBlobRepo.class);
//                Mockito.when(mock.findOne(ctx.getTeamId(), MetaBlob.Full.class, "sys_common"))
//                                .thenAnswer(invocation -> {
//                                    MetaBlobImpl b = (MetaBlobImpl)invocation.callRealMethod();
//                                    MetaObject node = ZipUtil.unzip(b.getObject(), MetaObject.class);
//                                    node.getProps().put("nativeModule", true);
//                                    MetaObjectV2 obj = ObjectUtil.hash(node);
//                                    b.setOid(obj.getOid());
//                                    b.setObject(obj.getContent());
//                                    return b;
//                                });
                MetaContext.addModules(Lists.newArrayList(ModuleInfo.of(ctx.getTeamCode(), "sys_common", false, true, false)));
                String url = metaReadWriteCenter.write(ctx.getTeamId(), "sys_common");
                metaReadWriteCenter.read(ctx.getTeamId(), "sys_common", url);

                // re-assert dataset after export and import
                {
                    ideHelperBean.assertDatasetProject(this.getClass(), "03after_reimport_ext_module", ctx);
                    ideHelperBean.assertDatasetMetaRaw(this.getClass(), "03after_reimport_ext_module_raw", ctx);
                    ideHelperBean.assertDatasetIndexAsset(this.getClass(), "03after_reimport_ext_module_index", ctx);
                }
            }
        }
    }
}
