{"type": "Model", "name": "盘点计划", "parentKey": "ERP_SCM", "children": [], "props": {"desc": null, "alias": "ERP_SCM$inv_count_strategy_md", "props": {"type": "PERSIST", "config": {"self": false, "system": false, "persist": false, "selfRelationFieldAlias": null}, "mainField": "plan_code", "tableName": "inv_count_strategy_md", "searchModel": false, "mainFieldAlias": "planCode", "physicalDelete": false, "originOrgIdEnabled": true}, "children": [{"ext": false, "key": "plan_code", "name": "计划编码", "type": "DataStructField", "alias": "planCode", "appId": 47014, "props": {"length": 32, "unique": false, "comment": "计划编码", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "plan_code", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "plan_name", "name": "计划名称", "type": "DataStructField", "alias": "planName", "appId": 47014, "props": {"length": 32, "unique": false, "comment": "计划名称", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "plan_name", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "com_org_id", "name": "公司", "type": "DataStructField", "alias": "comOrgId", "appId": 47014, "props": {"unique": false, "comment": "公司", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "com_org_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "sys_common$org_struct_md", "currentModelAlias": "ERP_SCM$inv_count_strategy_md", "relationModelAlias": "sys_common$org_struct_md", "linkModelFieldAlias": null, "currentModelFieldAlias": "comOrgId"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "effect_date", "name": "生效日期", "type": "DataStructField", "alias": "effectDate", "appId": 47014, "props": {"unique": false, "comment": "生效日期", "required": false, "encrypted": false, "fieldType": "DATE", "columnName": "effect_date", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "expire_date", "name": "失效日期", "type": "DataStructField", "alias": "expireDate", "appId": 47014, "props": {"unique": false, "comment": "失效日期", "required": false, "encrypted": false, "fieldType": "DATE", "columnName": "expire_date", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "cycle_num", "name": "间隔周期", "type": "DataStructField", "alias": "cycleNum", "appId": 47014, "props": {"unique": false, "comment": "间隔周期", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 8, "columnName": "cycle_num", "compositeKey": false, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}, "teamId": 22}, {"ext": false, "key": "cycle_unit", "name": "周期单位", "type": "DataStructField", "alias": "cycleUnit", "appId": 47014, "props": {"length": 256, "unique": false, "comment": "周期单位", "dictPros": {"dictValues": [{"label": "天", "value": "DAY"}, {"label": "周", "value": "WEEK"}, {"label": "月", "value": "MONTH"}, {"label": "季", "value": "SEASON"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "cycle_unit", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "status", "name": "状态", "type": "DataStructField", "alias": "status", "appId": 47014, "props": {"length": 256, "unique": false, "comment": "状态", "dictPros": {"dictValues": [{"label": "未启用", "value": "INACTIVE"}, {"label": "已启用", "value": "ENABLED"}, {"label": "已停用", "value": "DISABLED"}, {"label": "已失效", "value": "DISCARDED"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "status", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "seq", "name": "序号", "type": "DataStructField", "alias": "seq", "appId": 47014, "props": {"unique": false, "comment": "序号", "required": false, "encrypted": false, "fieldType": "NUMBER", "intLength": 8, "columnName": "seq", "compositeKey": false, "defaultValue": 1, "autoGenerated": false, "isSystemField": false, "numberDisplayType": "digit"}, "teamId": 22}, {"ext": false, "key": "last_count_date", "name": "最近盘点时间", "type": "DataStructField", "alias": "lastCountDate", "appId": 47014, "props": {"unique": false, "comment": "最近盘点时间", "required": false, "encrypted": false, "fieldType": "DATE", "columnName": "last_count_date", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "last_schedule_date", "name": "上次计划日期", "type": "DataStructField", "alias": "lastScheduleDate", "appId": 47014, "props": {"unique": false, "comment": "上次计划日期", "required": false, "encrypted": false, "fieldType": "DATE", "columnName": "last_schedule_date", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "next_schedule_date", "name": "下次计划日期", "type": "DataStructField", "alias": "nextScheduleDate", "appId": 47014, "props": {"unique": false, "comment": "下次计划日期", "required": false, "encrypted": false, "fieldType": "DATE", "columnName": "next_schedule_date", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "note", "name": "备注", "type": "DataStructField", "alias": "note", "appId": 47014, "props": {"length": 256, "unique": false, "comment": "备注", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "note", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "is_add_row_allowed", "name": "是否允许增加盘点行", "type": "DataStructField", "alias": "isAddRowAllowed", "appId": 47014, "props": {"length": 1, "unique": false, "comment": "是否允许增加盘点行", "required": false, "encrypted": false, "fieldType": "BOOL", "columnName": "is_add_row_allowed", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "is_zero_inv_count", "name": "零库存参与盘点", "type": "DataStructField", "alias": "isZeroInvCount", "appId": 47014, "props": {"length": 1, "unique": false, "comment": "零库存参与盘点", "required": false, "encrypted": false, "fieldType": "BOOL", "columnName": "is_zero_inv_count", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "is_post_block", "name": "冻结出入库", "type": "DataStructField", "alias": "isPostBlock", "appId": 47014, "props": {"length": 1, "unique": false, "comment": "冻结出入库", "required": false, "encrypted": false, "fieldType": "BOOL", "columnName": "is_post_block", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "is_transit_inv_count", "name": "在途物料不参与盘点", "type": "DataStructField", "alias": "isTransitInvCount", "appId": 47014, "props": {"length": 1, "unique": false, "comment": "在途物料不参与盘点", "required": false, "encrypted": false, "fieldType": "BOOL", "columnName": "is_transit_inv_count", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "is_auto_approve_count_plan", "name": "生成盘点方案时自动审核", "type": "DataStructField", "alias": "isAutoApproveCountPlan", "appId": 47014, "props": {"length": 1, "unique": false, "comment": "生成盘点方案时自动审核", "required": false, "encrypted": false, "fieldType": "BOOL", "columnName": "is_auto_approve_count_plan", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "inv_org_link", "name": "库存组合关联", "type": "DataStructField", "alias": "invOrgLink", "appId": 47014, "props": {"unique": false, "comment": "库存组合关联", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "inv_org_link", "compositeKey": false, "relationMeta": {"sync": true, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "ERP_SCM$inv_count_strategy_inv_org_link_tr", "relationModelKey": "ERP_SCM$inv_count_strategy_inv_org_link_tr", "currentModelAlias": "ERP_SCM$inv_count_strategy_md", "relationModelAlias": "ERP_SCM$inv_count_strategy_inv_org_link_tr", "linkModelFieldAlias": "invCountStrategyMdId", "currentModelFieldAlias": "invOrgLink"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "abc_group_item_link", "name": "abc分组项目关联", "type": "DataStructField", "alias": "abcGroupItemLink", "appId": 47014, "props": {"unique": false, "comment": "abc分组项目关联", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "abc_group_item_link", "compositeKey": false, "relationMeta": {"sync": true, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "ERP_SCM$inv_count_strategy_group_item_link_tr", "relationModelKey": "ERP_SCM$inv_count_strategy_group_item_link_tr", "currentModelAlias": "ERP_SCM$inv_count_strategy_md", "relationModelAlias": "ERP_SCM$inv_count_strategy_group_item_link_tr", "linkModelFieldAlias": "invCountStrategyMdId", "currentModelFieldAlias": "abcGroupItemLink"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "must_mat_link", "name": "必盘物料关联", "type": "DataStructField", "alias": "mustMatLink", "appId": 47014, "props": {"unique": false, "comment": "必盘物料关联", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "must_mat_link", "compositeKey": false, "relationMeta": {"sync": true, "relationKey": null, "relationType": "PARENT_CHILD", "linkModelAlias": "ERP_SCM$inv_count_strategy_must_mat_link_tr", "relationModelKey": "ERP_SCM$inv_count_strategy_must_mat_link_tr", "currentModelAlias": "ERP_SCM$inv_count_strategy_md", "relationModelAlias": "ERP_SCM$inv_count_strategy_must_mat_link_tr", "linkModelFieldAlias": "invCountStrategyMdId", "currentModelFieldAlias": "mustMatLink"}, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "distribute_rule", "name": "分配规则", "type": "DataStructField", "alias": "distributeRule", "appId": 47014, "props": {"length": 256, "unique": false, "comment": "分配规则", "dictPros": {"dictValues": [{"label": "库存组织", "value": "INV_ORG_ID"}, {"label": "库存地点", "value": "INV_LOC_ID"}, {"label": "批次", "value": "BATCH_ID"}, {"label": "库存类型", "value": "INV_TYPE_ID"}, {"label": "特殊库存标识", "value": "SPC_STK_TYPE_ID"}, {"label": "特殊库存分类", "value": "SPC_STK_TYPE_CLASS"}], "properties": null, "multiSelect": true}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "distribute_rule", "compositeKey": false, "autoGenerated": false, "isSystemField": false}, "teamId": 22}, {"ext": false, "key": "id", "name": "ID", "type": "DataStructField", "alias": "id", "appId": 47014, "props": {"length": 20, "unique": true, "comment": "ID", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "id", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "created_by", "name": "创建人", "type": "DataStructField", "alias": "created<PERSON>y", "appId": 47014, "props": {"length": 20, "unique": true, "comment": "创建人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "created_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_SCM$user", "currentModelAlias": "ERP_SCM$inv_count_strategy_md", "relationModelAlias": "ERP_SCM$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "created<PERSON>y"}, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "updated_by", "name": "更新人", "type": "DataStructField", "alias": "updatedBy", "appId": 47014, "props": {"length": 20, "unique": true, "comment": "更新人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "updated_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_SCM$user", "currentModelAlias": "ERP_SCM$inv_count_strategy_md", "relationModelAlias": "ERP_SCM$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "updatedBy"}, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "created_at", "name": "创建时间", "type": "DataStructField", "alias": "createdAt", "appId": 47014, "props": {"unique": true, "comment": "创建时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "created_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "updated_at", "name": "更新时间", "type": "DataStructField", "alias": "updatedAt", "appId": 47014, "props": {"unique": true, "comment": "更新时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "updated_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "version", "name": "版本号", "type": "DataStructField", "alias": "version", "appId": 47014, "props": {"length": 20, "unique": true, "comment": "版本号", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "version", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "deleted", "name": "逻辑删除标识", "type": "DataStructField", "alias": "deleted", "appId": 47014, "props": {"length": 20, "unique": true, "comment": "逻辑删除标识", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "deleted", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true}, "teamId": 22}, {"ext": false, "key": "origin_org_id", "name": "所属组织", "type": "DataStructField", "alias": "originOrgId", "appId": 47014, "props": {"unique": false, "comment": "所属组织", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "origin_org_id", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}, "teamId": 22}]}}