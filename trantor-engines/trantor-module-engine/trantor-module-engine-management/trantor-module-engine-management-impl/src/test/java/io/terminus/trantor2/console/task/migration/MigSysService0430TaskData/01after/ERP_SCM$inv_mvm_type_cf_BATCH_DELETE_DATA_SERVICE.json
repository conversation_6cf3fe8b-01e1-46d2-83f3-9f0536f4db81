{"name": "移动类型定义表-批量删除数据服务", "access": "Private", "parentKey": "ERP_SCM", "type": "ServiceDefinition", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "serviceDslJson": {"children": [{"id": null, "key": "node_1hothhu5c13", "name": "开始", "nextNodeKey": "node_1hothk43k15", "props": {"globalVariable": [{"defaultValue": null, "description": null, "fieldAlias": "mat", "fieldKey": "mat", "fieldName": "mat", "fieldType": "Model", "id": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_mvm_type_cf", "modelKey": "ERP_SCM$inv_mvm_type_cf", "modelName": "ERP_SCM$inv_mvm_type_cf"}, "relation": null, "required": null}], "input": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Number", "id": null, "required": null}, "fieldAlias": "ids", "fieldKey": "ids", "fieldName": "ids", "fieldType": "Array", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}], "output": [], "type": "StartProperties"}, "type": "StartNode"}, {"children": [{"id": null, "key": "node_1hothlf1u18", "name": "赋值", "nextNodeKey": "node_1hothl3hr16", "props": {"assignments": [{"field": {"constValue": null, "fieldPaths": [{"fieldKey": "GLOBAL", "fieldName": "全局变量", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "mat", "fieldName": "mat", "fieldType": null, "modelKey": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_mvm_type_cf", "modelKey": "ERP_SCM$inv_mvm_type_cf", "modelName": "ERP_SCM$inv_mvm_type_cf"}}, {"fieldKey": "id", "fieldName": "ID", "fieldType": null, "modelKey": "ERP_SCM$inv_mvm_type_cf", "relatedModel": null}], "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "modelAlias": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_mvm_type_cf", "modelKey": "ERP_SCM$inv_mvm_type_cf", "modelName": "ERP_SCM$inv_mvm_type_cf"}, "valueKey": "mat", "valueName": "mat"}, {"fieldType": null, "modelAlias": "ERP_SCM$inv_mvm_type_cf", "relatedModel": null, "valueKey": "id", "valueName": "ID"}]}, "id": "1hothlg6g19", "operator": "EQ", "value": {"constValue": null, "fieldPaths": [{"fieldKey": "LOOP_node_1hothk43k15", "fieldName": "[循环]循环变量", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "_item", "fieldName": "循环元素", "fieldType": null, "modelKey": null, "relatedModel": null}], "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "LOOP_node_1hothk43k15", "valueName": "[循环]循环变量"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "_item", "valueName": "循环元素"}]}}], "type": "AssignProperties"}, "type": "AssignNode"}, {"id": null, "key": "node_1hothl3hr16", "name": "删除数据", "props": {"modelValue": {"constValue": null, "fieldPaths": [{"fieldKey": "GLOBAL", "fieldName": "全局变量", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "mat", "fieldName": "mat", "fieldType": null, "modelKey": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_mvm_type_cf", "modelKey": "ERP_SCM$inv_mvm_type_cf", "modelName": "ERP_SCM$inv_mvm_type_cf"}}], "fieldType": "Model", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "modelAlias": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_mvm_type_cf", "modelKey": "ERP_SCM$inv_mvm_type_cf", "modelName": "ERP_SCM$inv_mvm_type_cf"}, "valueKey": "mat", "valueName": "mat"}]}, "outputAssign": null, "relatedModel": {"modelAlias": "ERP_SCM$inv_mvm_type_cf", "modelKey": "ERP_SCM$inv_mvm_type_cf", "modelName": "移动类型定义表"}, "type": "CascadeDeleteDataProperties"}, "type": "CascadeDeleteDataNode"}], "headNodeKeys": ["node_1hothlf1u18"], "id": null, "key": "node_1hothk43k15", "name": "循环", "nextNodeKey": "node_1hothhu5c14", "props": {"loopData": {"constValue": null, "fieldPaths": [{"fieldKey": "REQUEST", "fieldName": "服务入参", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "request", "fieldName": "request", "fieldType": null, "modelKey": null, "relatedModel": null}, {"fieldKey": "ids", "fieldName": "ids", "fieldType": null, "modelKey": null, "relatedModel": null}], "fieldType": "Array", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "request", "valueName": "request"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "ids", "valueName": "ids"}]}, "loopElement": {"defaultValue": null, "description": null, "fieldAlias": "ids", "fieldKey": "ids", "fieldName": "ids", "fieldType": "Number", "id": null, "required": null}, "loopType": "DATASET_LOOP", "stopWhenDataEmpty": false, "type": "LoopProperties"}, "type": "LoopNode"}, {"id": null, "key": "node_1hothhu5c14", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "headNodeKeys": ["node_1hothhu5c13"], "id": null, "input": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Number", "id": null, "required": null}, "fieldAlias": "ids", "fieldKey": "ids", "fieldName": "ids", "fieldType": "Array", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}], "key": "ERP_SCM$inv_mvm_type_cf_BATCH_DELETE_DATA_SERVICE", "name": "移动类型定义表-批量删除数据服务", "output": null, "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}}