{"type": "Model", "name": "类目配置表", "parentKey": "ERP_GEN", "children": [], "props": {"desc": null, "alias": "ERP_GEN$gen_mat_cate_md", "props": {"type": "PERSIST", "config": {"self": true, "system": false, "persist": false, "selfRelationFieldAlias": "mat<PERSON>ate<PERSON><PERSON>nt"}, "mainField": "mat_cate_name", "tableName": "gen_mat_cate_md", "searchModel": false, "mainFieldAlias": "matCateName", "physicalDelete": false, "shardingConfig": {"enabled": false, "shardingFields": null, "shardingSuffixLength": 3}, "originOrgIdEnabled": true}, "children": [{"ext": false, "key": "mat_cate_code", "name": "类目编码", "type": "DataStructField", "alias": "matCateCode", "props": {"length": 32, "unique": false, "comment": "类目编码", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "mat_cate_code", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "mat_cate_name", "name": "类目名称", "type": "DataStructField", "alias": "matCateName", "props": {"length": 128, "unique": false, "comment": "类目名称", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "mat_cate_name", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "is_leaf", "name": "是否叶子节点", "type": "DataStructField", "alias": "<PERSON><PERSON><PERSON><PERSON>", "props": {"length": 1, "unique": false, "comment": "是否叶子节点", "required": false, "encrypted": false, "fieldType": "BOOL", "columnName": "is_leaf", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "status", "name": "状态", "type": "DataStructField", "alias": "status", "props": {"length": 256, "unique": false, "comment": "状态", "dictPros": {"dictValues": [{"label": "启用", "value": "ENABLED"}, {"label": "停用", "value": "INACTIVE"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "status", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "gen_coun_class_mat_tax_item_cf_id", "name": "物料税", "type": "DataStructField", "alias": "genCounClassMatTaxItemCfId", "props": {"unique": false, "comment": "物料税", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "gen_coun_class_mat_tax_item_cf_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_coun_class_mat_tax_item_cf", "currentModelAlias": "ERP_GEN$gen_mat_cate_md", "relationModelAlias": "ERP_GEN$gen_coun_class_mat_tax_item_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "genCounClassMatTaxItemCfId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "qualifications_group_id", "name": "关联资质组", "type": "DataStructField", "alias": "qualificationsGroupId", "props": {"length": 256, "unique": false, "comment": "关联资质组", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "qualifications_group_id", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_SCM$gen_qualifications_head_cf", "currentModelAlias": "ERP_GEN$gen_mat_cate_md", "relationModelAlias": "ERP_SCM$gen_qualifications_head_cf", "linkModelFieldAlias": null, "currentModelFieldAlias": "qualificationsGroupId"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "is_limit_po_qualifications", "name": "采购订单是否限制资质", "type": "DataStructField", "alias": "isLimitPoQualifications", "props": {"length": 1, "unique": false, "comment": "采购订单是否限制资质", "required": false, "encrypted": false, "fieldType": "BOOL", "columnName": "is_limit_po_qualifications", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "is_limit_so_qualifications", "name": "销售订单是否限制资质", "type": "DataStructField", "alias": "isLimitSoQualifications", "props": {"length": 1, "unique": false, "comment": "销售订单是否限制资质", "required": false, "encrypted": false, "fieldType": "BOOL", "columnName": "is_limit_so_qualifications", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "is_limit_ct_qualifications", "name": "合同是否限制资质", "type": "DataStructField", "alias": "isLimitCtQualifications", "props": {"length": 1, "unique": false, "comment": "合同是否限制资质", "required": false, "encrypted": false, "fieldType": "BOOL", "columnName": "is_limit_ct_qualifications", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "path", "name": "类目路径", "type": "DataStructField", "alias": "path", "props": {"length": 512, "unique": false, "comment": "类目路径", "required": false, "encrypted": false, "fieldType": "TEXT", "columnName": "path", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "wq_mat_type", "name": "物料类型", "type": "DataStructField", "alias": "wqMatType", "props": {"length": 256, "unique": false, "comment": "物料类型", "dictPros": {"dictValues": [{"label": "经营物料", "value": "JYWL"}, {"label": "配件", "value": "PJ"}, {"label": "基建物资", "value": "JJWZ"}, {"label": "非生产物料", "value": "FSCWL"}, {"label": "非经营性废料", "value": "FJYXFL"}, {"label": "受委托加工料", "value": "SWTJGL"}], "properties": null, "multiSelect": false}, "required": false, "encrypted": false, "fieldType": "ENUM", "columnName": "wq_mat_type", "compositeKey": false, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "mat_cate_parent", "name": "父类目", "type": "DataStructField", "alias": "mat<PERSON>ate<PERSON><PERSON>nt", "props": {"unique": false, "comment": "父类目", "required": false, "encrypted": false, "fieldType": "OBJECT", "columnName": "mat_cate_parent", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$gen_mat_cate_md", "currentModelAlias": "ERP_GEN$gen_mat_cate_md", "relationModelAlias": "ERP_GEN$gen_mat_cate_md", "linkModelFieldAlias": null, "currentModelFieldAlias": "mat<PERSON>ate<PERSON><PERSON>nt"}, "autoGenerated": false, "isSystemField": false}}, {"ext": false, "key": "id", "name": "ID", "type": "DataStructField", "alias": "id", "props": {"length": 20, "unique": true, "comment": "ID", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "id", "compositeKey": false, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "created_by", "name": "创建人", "type": "DataStructField", "alias": "created<PERSON>y", "props": {"length": 20, "unique": true, "comment": "创建人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "created_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_mat_cate_md", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "created<PERSON>y"}, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "updated_by", "name": "更新人", "type": "DataStructField", "alias": "updatedBy", "props": {"length": 20, "unique": true, "comment": "更新人", "required": false, "encrypted": false, "fieldType": "OBJECT", "intLength": 20, "columnName": "updated_by", "compositeKey": false, "relationMeta": {"sync": false, "relationKey": null, "relationType": "LINK", "linkModelAlias": null, "relationModelKey": "ERP_GEN$user", "currentModelAlias": "ERP_GEN$gen_mat_cate_md", "relationModelAlias": "ERP_GEN$user", "linkModelFieldAlias": null, "currentModelFieldAlias": "updatedBy"}, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "created_at", "name": "创建时间", "type": "DataStructField", "alias": "createdAt", "props": {"unique": true, "comment": "创建时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "created_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "updated_at", "name": "更新时间", "type": "DataStructField", "alias": "updatedAt", "props": {"unique": true, "comment": "更新时间", "required": true, "encrypted": false, "fieldType": "DATE", "columnName": "updated_at", "compositeKey": false, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "version", "name": "版本号", "type": "DataStructField", "alias": "version", "props": {"length": 20, "unique": true, "comment": "版本号", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "version", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "deleted", "name": "逻辑删除标识", "type": "DataStructField", "alias": "deleted", "props": {"length": 20, "unique": true, "comment": "逻辑删除标识", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "deleted", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true}}, {"ext": false, "key": "origin_org_id", "name": "所属组织", "type": "DataStructField", "alias": "originOrgId", "props": {"unique": false, "comment": "所属组织", "required": true, "encrypted": false, "fieldType": "NUMBER", "intLength": 20, "columnName": "origin_org_id", "compositeKey": false, "defaultValue": 0, "autoGenerated": false, "isSystemField": true, "numberDisplayType": "digit"}}]}}