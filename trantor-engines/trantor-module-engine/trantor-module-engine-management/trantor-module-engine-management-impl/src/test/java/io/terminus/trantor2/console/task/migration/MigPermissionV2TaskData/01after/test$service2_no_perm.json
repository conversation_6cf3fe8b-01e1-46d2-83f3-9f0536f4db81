{"type": "ServiceDefinition", "name": "Service 2 - No Perm", "access": "Private", "parentKey": "test", "children": [], "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "serviceDslJson": {"children": [], "headNodeKeys": [], "id": null, "input": [], "output": [], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "permissionKey": "test$service2_no_perm_perm_ac", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}}