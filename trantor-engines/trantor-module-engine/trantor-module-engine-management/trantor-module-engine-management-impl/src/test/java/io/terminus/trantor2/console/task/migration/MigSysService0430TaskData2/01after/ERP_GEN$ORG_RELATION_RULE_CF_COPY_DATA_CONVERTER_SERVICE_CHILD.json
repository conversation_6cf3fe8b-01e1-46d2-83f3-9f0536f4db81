{"type": "ServiceDefinition", "name": "组织关联规则设置-复制数据转换子服务", "access": "Private", "parentKey": "ERP_GEN", "props": {"eventProps": null, "isDeleted": null, "isEnabled": true, "modelKey": null, "permissions": null, "serviceDslJson": {"children": [{"desc": null, "id": null, "key": "node_1holp7m6n36", "name": "开始", "nextNodeKey": "node_1holp88un38", "props": {"globalVariable": null, "input": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "id", "fieldKey": "id", "fieldName": "id", "fieldType": "Text", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "<PERSON><PERSON><PERSON>", "fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "id": null, "required": null}], "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "type": "StartProperties"}, "type": "StartNode"}, {"desc": null, "id": null, "key": "node_1holp88un38", "name": "在线JS脚本", "nextNodeKey": "node_1holp9l3r40", "props": {"inputMapping": [{"field": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}, "id": "1holp8pmd39", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "request", "valueName": "request"}]}}], "language": "JS", "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "script": "  request.id = null;  return request;", "scriptEngine": null, "type": "ScriptProperties"}, "type": "ScriptNode"}, {"desc": null, "id": null, "key": "node_1holp9l3r40", "name": "HTTP服务", "nextNodeKey": "node_1hom0f3lq47", "props": {"body": null, "bodyType": "VALUE", "headers": [], "inputMapping": null, "jsonBody": null, "method": "GET", "output": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "fieldType", "fieldKey": "fieldType", "fieldName": "fieldType", "fieldType": "Text", "id": null, "required": null}, {"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "relationType", "fieldKey": "relationType", "fieldName": "relationType", "fieldType": "Text", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "sync", "fieldKey": "sync", "fieldName": "sync", "fieldType": "Boolean", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "relationModelAlias", "fieldKey": "relationModelAlias", "fieldName": "relationModelAlias", "fieldType": "Text", "id": null, "required": null}], "fieldAlias": "relation_meta", "fieldKey": "relation_meta", "fieldName": "relationMeta", "fieldType": "Object", "id": null, "required": null}], "fieldAlias": "props", "fieldKey": "props", "fieldName": "props", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "alias", "fieldKey": "alias", "fieldName": "alias", "fieldType": "Text", "id": null, "required": null}], "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Object", "id": null, "required": null}, "fieldAlias": "children", "fieldKey": "children", "fieldName": "children", "fieldType": "Array", "id": null, "required": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "pathVariables": [{"id": null, "key": "<PERSON><PERSON><PERSON>", "required": null, "value": {"fieldType": null, "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "<PERSON><PERSON><PERSON>", "valueName": "<PERSON><PERSON><PERSON>"}]}}], "serviceName": "查询模型元数据", "stream": false, "type": "HttpServiceProperties", "url": "http://localhost:8080/api/trantor/struct-node/find-by-key/{modelKey}"}, "type": "HttpServiceNode"}, {"children": [{"children": [{"children": null, "desc": null, "id": null, "key": "node_1hom1prki54", "name": "条件", "nextNodeKey": "node_1hom2jh9156", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "IeHeOnBWWGE0qvCMd50yB", "key": null, "leftValue": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "LOOP_node_1hom0f3lq47", "valueName": "[循环]循环变量"}, {"fieldType": null, "valueKey": "_item", "valueName": "循环元素"}, {"fieldType": null, "valueKey": "props", "valueName": "props"}, {"fieldType": null, "valueKey": "fieldType", "valueName": "fieldType"}]}, "operator": "EQ", "rightValue": {"constValue": "OBJECT", "fieldType": "Text", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "sL3BVieB9dXKdM8Tzh3ba", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "Wg-ME5P2qwlns1cANsYhC", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "ConditionProperties"}, "type": "ConditionNode"}, {"children": [{"children": null, "desc": null, "id": null, "key": "node_1hom2jh9157", "name": "同步模型", "nextNodeKey": "node_1hom42ak060", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "VcDHFU_CD7AKmMLMWYyNq", "key": null, "leftValue": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "LOOP_node_1hom0f3lq47", "valueName": "[循环]循环变量"}, {"fieldType": null, "valueKey": "_item", "valueName": "循环元素"}, {"fieldType": null, "valueKey": "props", "valueName": "props"}, {"fieldType": null, "valueKey": "relation_meta", "valueName": "relationMeta"}, {"fieldType": null, "valueKey": "relationType", "valueName": "relationType"}]}, "operator": "EQ", "rightValue": {"constValue": "LINK", "fieldType": "Text", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "xVkLsv30JN8fubtmWeF_b", "key": null, "leftValue": {"fieldType": "Boolean", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "LOOP_node_1hom0f3lq47", "valueName": "[循环]循环变量"}, {"fieldType": null, "valueKey": "_item", "valueName": "循环元素"}, {"fieldType": null, "valueKey": "props", "valueName": "props"}, {"fieldType": null, "valueKey": "relation_meta", "valueName": "relationMeta"}, {"fieldType": null, "valueKey": "sync", "valueName": "sync"}]}, "operator": "EQ", "rightValue": {"constValue": "true", "fieldType": "Boolean", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "QYZSVnuSRp6AwYBBT5LcT", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "fS-QSkMu6SA01IuknSJa1", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "ConditionProperties"}, "type": "ConditionNode"}, {"desc": null, "id": null, "key": "node_1hom42ak060", "name": "获取子模型数据", "nextNodeKey": "node_1hom449i763", "props": {"inputMapping": [{"field": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}, "id": "1hom42kmp61", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "NODE_OUTPUT_node_1holp88un38", "valueName": "[在线JS脚本]节点出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}, {"field": {"defaultValue": null, "description": null, "fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "fieldKey": "<PERSON><PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON><PERSON>", "fieldType": "Text", "id": null, "required": null}, "id": "1hom43fm562", "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "LOOP_node_1hom0f3lq47", "valueName": "[循环]循环变量"}, {"fieldType": null, "valueKey": "_item", "valueName": "循环元素"}, {"fieldType": null, "valueKey": "alias", "valueName": "alias"}]}}], "language": "JS", "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "script": "  return request.get(fieldAlias);", "scriptEngine": null, "type": "ScriptProperties"}, "type": "ScriptNode"}, {"desc": null, "id": null, "key": "node_1hom449i763", "name": "调用编排服务", "props": {"inputMapping": [{"field": {"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "id", "fieldKey": "id", "fieldName": "id", "fieldType": "Text", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}, "id": "1hom455sj67", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "NODE_OUTPUT_node_1hom42ak060", "valueName": "[在线JS脚本]节点出参"}]}}, {"field": {"defaultValue": null, "description": null, "fieldAlias": "<PERSON><PERSON><PERSON>", "fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "id": null, "required": null}, "id": "1hom455sj68", "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "LOOP_node_1hom0f3lq47", "valueName": "[循环]循环变量"}, {"fieldType": null, "valueKey": "_item", "valueName": "循环元素"}, {"fieldType": null, "valueKey": "props", "valueName": "props"}, {"fieldType": null, "valueKey": "relation_meta", "valueName": "relationMeta"}, {"fieldType": null, "valueKey": "relationModelAlias", "valueName": "relationModelAlias"}]}}], "output": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "serviceKey": "ERP_GEN$ORG_RELATION_RULE_CF_COPY_DATA_CONVERTER_SERVICE_CHILD", "serviceName": "组织关联规则设置-复制数据转换子服务", "transactionPropagation": "NOT_SUPPORTED", "type": "CallServiceProperties"}, "type": "CallServiceNode"}, {"children": null, "desc": null, "id": null, "key": "node_1hom2k4ml59", "name": "同步模型数组", "nextNodeKey": "node_1hom4c6ik70", "props": {"conditionGroup": {"conditions": [{"conditions": [{"id": "xdTq3g8f2Zu8ETYXAofCC", "key": null, "leftValue": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "LOOP_node_1hom0f3lq47", "valueName": "[循环]循环变量"}, {"fieldType": null, "valueKey": "_item", "valueName": "循环元素"}, {"fieldType": null, "valueKey": "props", "valueName": "props"}, {"fieldType": null, "valueKey": "relation_meta", "valueName": "relationMeta"}, {"fieldType": null, "valueKey": "relationType", "valueName": "relationType"}]}, "operator": "EQ", "rightValue": {"constValue": "PARENT_CHILD", "fieldType": "Text", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "ec0xs7ty072jV-YSYuj_K", "key": null, "leftValue": {"fieldType": "Boolean", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "LOOP_node_1hom0f3lq47", "valueName": "[循环]循环变量"}, {"fieldType": null, "valueKey": "_item", "valueName": "循环元素"}, {"fieldType": null, "valueKey": "props", "valueName": "props"}, {"fieldType": null, "valueKey": "relation_meta", "valueName": "relationMeta"}, {"fieldType": null, "valueKey": "sync", "valueName": "sync"}]}, "operator": "EQ", "rightValue": {"constValue": "true", "fieldType": "Boolean", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "_vr4r8egVL5do8vWqDnQp", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "y_l6RlZRhiVDm_z6yVILS", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "ConditionProperties"}, "type": "ConditionNode"}, {"desc": null, "id": null, "key": "node_1hom4c6ik70", "name": "获取数组子模型数据", "nextNodeKey": "node_1hom4f2ed73", "props": {"inputMapping": [{"field": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}, "id": "1hom4clgm71", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "NODE_OUTPUT_node_1holp88un38", "valueName": "[在线JS脚本]节点出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}}, {"field": {"defaultValue": null, "description": null, "fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "fieldKey": "<PERSON><PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON><PERSON>", "fieldType": "Text", "id": null, "required": null}, "id": "1hom4csvg72", "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "LOOP_node_1hom0f3lq47", "valueName": "[循环]循环变量"}, {"fieldType": null, "valueKey": "_item", "valueName": "循环元素"}, {"fieldType": null, "valueKey": "alias", "valueName": "alias"}]}}], "language": "JS", "output": [{"defaultValue": null, "description": null, "element": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "element", "fieldKey": "element", "fieldName": "element", "fieldType": "Object", "id": null, "required": null}, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Array", "id": null, "required": null}], "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "script": "  return request.get(fieldAlias);", "scriptEngine": null, "type": "ScriptProperties"}, "type": "ScriptNode"}, {"children": [{"desc": null, "id": null, "key": "node_1hom4guql74", "name": "调用编排服务", "props": {"inputMapping": [{"field": {"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "id", "fieldKey": "id", "fieldName": "id", "fieldType": "Text", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}, "id": "1hom4iung81", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "LOOP_node_1hom4f2ed73", "valueName": "[循环]循环变量"}, {"fieldType": null, "valueKey": "_item", "valueName": "循环元素"}]}}, {"field": {"defaultValue": null, "description": null, "fieldAlias": "<PERSON><PERSON><PERSON>", "fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "id": null, "required": null}, "id": "1hom4iung82", "value": {"fieldType": "Text", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "LOOP_node_1hom0f3lq47", "valueName": "[循环]循环变量"}, {"fieldType": null, "valueKey": "_item", "valueName": "循环元素"}, {"fieldType": null, "valueKey": "props", "valueName": "props"}, {"fieldType": null, "valueKey": "relation_meta", "valueName": "relationMeta"}, {"fieldType": null, "valueKey": "relationModelAlias", "valueName": "relationModelAlias"}]}}], "output": null, "outputAssign": {"customAssignments": null, "outputAssignType": "SYSTEM"}, "serviceKey": "ERP_GEN$ORG_RELATION_RULE_CF_COPY_DATA_CONVERTER_SERVICE_CHILD", "serviceName": "组织关联规则设置-复制数据转换子服务", "transactionPropagation": "NOT_SUPPORTED", "type": "CallServiceProperties"}, "type": "CallServiceNode"}], "desc": null, "headNodeKeys": ["node_1hom4guql74"], "id": null, "key": "node_1hom4f2ed73", "name": "循环", "props": {"loopData": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "NODE_OUTPUT_node_1hom4c6ik70", "valueName": "[获取数组子模型数据]节点出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}, "loopElement": {"defaultValue": null, "description": null, "elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}, "loopType": "DATASET_LOOP", "stopWhenDataEmpty": false, "type": "LoopProperties"}, "type": "LoopNode"}, {"children": null, "desc": null, "id": null, "key": "node_1hom2jh9158", "name": "else", "props": {"type": "ConditionElseProperties"}, "type": "ConditionElseNode"}], "desc": null, "headNodeKeys": ["node_1hom2jh9157", "node_1hom2k4ml59", "node_1hom2jh9158"], "id": null, "key": "node_1hom2jh9156", "name": "排他分支", "props": {"type": "ExclusiveBranchProperties"}, "type": "ExclusiveBranchNode"}, {"children": null, "desc": null, "id": null, "key": "node_1hom1prki55", "name": "else", "props": {"type": "ConditionElseProperties"}, "type": "ConditionElseNode"}], "desc": null, "headNodeKeys": ["node_1hom1prki54", "node_1hom1prki55"], "id": null, "key": "node_1hom1prki53", "name": "排他分支", "props": {"type": "ExclusiveBranchProperties"}, "type": "ExclusiveBranchNode"}], "desc": null, "headNodeKeys": ["node_1hom1prki53"], "id": null, "key": "node_1hom0f3lq47", "name": "循环", "nextNodeKey": "node_1hom5100n84", "props": {"loopData": {"fieldType": "Array", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "GLOBAL", "valueName": "全局变量"}, {"fieldType": null, "valueKey": "NODE_OUTPUT_node_1holp9l3r40", "valueName": "[HTTP服务]节点出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}, {"fieldType": null, "valueKey": "children", "valueName": "children"}]}, "loopElement": {"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "fieldType", "fieldKey": "fieldType", "fieldName": "fieldType", "fieldType": "Text", "id": null, "required": null}, {"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "relationType", "fieldKey": "relationType", "fieldName": "relationType", "fieldType": "Text", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "sync", "fieldKey": "sync", "fieldName": "sync", "fieldType": "Boolean", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "relationModelAlias", "fieldKey": "relationModelAlias", "fieldName": "relationModelAlias", "fieldType": "Text", "id": null, "required": null}], "fieldAlias": "relation_meta", "fieldKey": "relation_meta", "fieldName": "relationMeta", "fieldType": "Object", "id": null, "required": null}], "fieldAlias": "props", "fieldKey": "props", "fieldName": "props", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "alias", "fieldKey": "alias", "fieldName": "alias", "fieldType": "Text", "id": null, "required": null}], "fieldAlias": "children", "fieldKey": "children", "fieldName": "children", "fieldType": "Object", "id": null, "required": null}, "loopType": "DATASET_LOOP", "stopWhenDataEmpty": false, "type": "LoopProperties"}, "type": "LoopNode"}, {"desc": null, "id": null, "key": "node_1hom5100n84", "name": "赋值", "nextNodeKey": "node_1holp7m6n37", "props": {"assignments": [{"field": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "OUTPUT", "valueName": "服务出参"}, {"fieldType": null, "valueKey": "data", "valueName": "data"}]}, "id": "1hom511ht85", "operator": "EQ", "value": {"fieldType": "Object", "id": null, "scope": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "valueKey": "REQUEST", "valueName": "服务入参"}, {"fieldType": null, "valueKey": "request", "valueName": "request"}]}}], "type": "AssignProperties"}, "type": "AssignNode"}, {"desc": null, "id": null, "key": "node_1holp7m6n37", "name": "结束", "props": {"type": "EndProperties"}, "type": "EndNode"}], "desc": null, "headNodeKeys": ["node_1holp7m6n36"], "id": null, "input": [{"defaultValue": null, "description": null, "elements": [{"defaultValue": null, "description": null, "fieldAlias": "id", "fieldKey": "id", "fieldName": "id", "fieldType": "Text", "id": null, "required": null}], "fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "id": null, "required": null}, {"defaultValue": null, "description": null, "fieldAlias": "<PERSON><PERSON><PERSON>", "fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "id": null, "required": null}], "key": "ERP_GEN$ORG_RELATION_RULE_CF_COPY_DATA_CONVERTER_SERVICE_CHILD", "name": "组织关联规则设置-复制数据转换子服务", "output": [{"defaultValue": null, "description": null, "elements": null, "fieldAlias": "data", "fieldKey": "data", "fieldName": "data", "fieldType": "Object", "id": null, "required": null}], "props": {"aiChatMode": null, "aiRoundsStrategy": null, "aiService": null, "fieldRules": null, "permissionKey": "ERP_GEN$ORG_RELATION_RULE_CF_CREATE_PERMISSION", "schedulerJob": null, "stateMachine": null, "transactionPropagation": "NOT_SUPPORTED", "type": "ServiceProperties"}}, "serviceDslMd5": null, "serviceName": null, "serviceType": "PROGRAMMABLE"}}