{"type": "View", "name": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new:list", "parentKey": "ERP_PLN", "children": [], "props": {"content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config-new", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "buttonType": "default", "confirmOn": "off", "disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "新建", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config-batch", "name": "DropdownButton", "props": {"combinationMode": true, "disabled$": "$context.selectedKeys?.length === 0 || $context.route?.action === \"new\" || $context.route?.action === \"edit\"", "items": [{"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"elements": [{"fieldAlias": "ids", "fieldName": "ids", "fieldType": "Number", "valueConfig": {"expression": "<PERSON><PERSON><PERSON><PERSON>", "name": "ids", "type": "expression"}}, {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_PLN$pln_dp_demand_forecast_type_cf"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "ERP_PLN$pln_dp_demand_forecast_type_cf_BATCH_DELETE_DATA_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config"}, {"action": "PageJump", "target": "list"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-ERP_PLN$pln_mrp_demand_forecast_type_config-multi-delete", "label": "批量删除"}], "label": "批量操作", "type": "default", "variant": "primary"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config-import", "name": "ImportButton", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "downloadServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download"}}, "isCustomServiceProps": {"isCustomFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "type": "InvokeService"}}, "label": "导入", "predictServiceProps": {"predictFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "type": "InvokeService"}}, "saveServiceProps": {"saveServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "type": "InvokeService"}}, "saveSubServiceProps": {"saveSubServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "type": "InvokeService"}}, "serviceProps": {"downloadFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download"}, "isCustomFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "type": "InvokeService"}, "predictFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "type": "InvokeService"}, "saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-sub-model"}, "saveServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "type": "InvokeService"}, "saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-direct-by-oss"}, "saveSubServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "type": "InvokeService"}}}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config-export", "name": "ExportButton", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "exportButtonServiceProps": {"getUserInfoFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/portal/user/current"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "label": "导出", "queryFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFieldsFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "type": "InvokeService"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}, "serviceProps": {"saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config-batch-actions", "name": "BatchActions", "props": {}}, {"children": [{"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config-logs", "name": "Logs", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "日志", "logsServiceProps": {"getLogsFlow": {"serviceKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-B5McuquLj_muzeR5qxCCA", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "name", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "hidden": false, "label": "名称", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "name": "name", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-EjykxL062ILJzd6SRnOQL", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "code", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "hidden": false, "label": "编码", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "name": "code", "type": "TEXT", "width": 146}, "type": "Widget"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-eR4Gc-WK7QcT1_Dlv_h9X", "name": "Fields", "props": {}, "type": "Meta"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config", "name": "Table", "props": {"allowClickRowSelect": true, "allowRowSelect": true, "filterFields": [{"componentProps": {"fieldAlias": "name", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请输入"}, "editComponentType": "InputText", "filterType": "fuzzy", "label": "名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "8DRr1ujkDOJCGKNia6F_b", "valueRules": null}], "name": "name", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "code", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请输入"}, "editComponentType": "InputText", "filterType": "fuzzy", "label": "编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "CmKyF5HCsfEcawOsCgf1R", "valueRules": null}], "name": "code", "type": "TEXT", "width": 120}], "flow": {"containerKey": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config", "context$": "$context", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_PLN$pln_dp_demand_forecast_type_cf"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_PLN$pln_dp_demand_forecast_type_cf_PAGING_DATA_SERVICE_BzVyMx1", "type": "InvokeService"}, "isProFilter": false, "label": "表格", "mode": "simple", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "onRowActionConfig": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "enable": true}, "pagination": {"size": "small"}, "showConfigure": false, "showFilterFields": true, "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "预测需求类型配置表详情"}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}, {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_PLN$pln_dp_demand_forecast_type_cf"}, {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "ERP_PLN$pln_dp_demand_forecast_type_cf_DELETE_DATA_BY_ID_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config"}, {"action": "Message", "message": "删除成功"}, {"action": "RefreshTab", "target": ["current"]}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "showCondition": {"conditions": [{"conditions": [{"id": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detail-shanchu-condition-id1", "leftValue": {"fieldType": "Enum", "scope": "form", "target": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config", "title": "status", "type": "VarValue", "val": "status", "value": "ERP_PLN$pln_dp_demand_forecast_type_cf.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detail-shanchu-condition-id2", "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-actions-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "edit", "name": "创建/编辑页", "type": "View"}, "params": [{"expression": "route.recordId", "name": "copyId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "buttonType": "default", "confirmOn": "off", "label": "复制", "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "showCondition": {"conditions": [{"conditions": [{"id": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detail-bianji-condition-id1", "leftValue": {"fieldType": "Enum", "scope": "form", "target": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config", "title": "status", "type": "VarValue", "val": "status", "value": "ERP_PLN$pln_dp_demand_forecast_type_cf.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detail-bianji-condition-id2", "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detail-ERP_PLN$pln_mrp_demand_forecast_type_config-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"serviceKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-name", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "name", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "名称", "name": "name", "type": "TEXT"}}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-code", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "code", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editable": false, "label": "编码", "name": "code", "type": "TEXT"}}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-effectiveDays", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "effectiveDays", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请输入", "precision": 0}, "displayComponentProps": {"precision": 0}, "displayComponentType": "Number", "editable": false, "label": "有效天数", "name": "effectiveDays", "type": "NUMBER"}}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-coverHistory", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "coverHistory", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editable": false, "label": "是否覆盖历史需求", "name": "coverHistory", "type": "BOOL"}}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-hDrJtf80iXsslVt4ili2d", "name": "DetailGroupItem", "props": {"defaultCollapsed": false, "title": "基本信息"}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-itemCode", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "itemCode", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editable": false, "label": "物料编码", "name": "itemCode", "type": "BOOL"}}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-invOrg", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "invOrg", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editable": false, "label": "库存组织", "name": "invOrg", "type": "BOOL"}}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-invLoc", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "invLoc", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editable": false, "label": "库存地点", "name": "invLoc", "type": "BOOL"}}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-AaZEKTiFyBkUJSxXILHuw", "name": "DetailGroupItem", "props": {"defaultCollapsed": false, "title": "必填项配置"}, "type": "Layout"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail-defaultGroup", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-N2ZOE2yRp9UHZEX3Q2Kj3", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "ERP_PLN$user", "parentModelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_PLN$user", "serviceKey": "ERP_PLN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "ERP_PLN$user", "parentModelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_PLN$user", "serviceKey": "ERP_PLN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请选择"}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请选择"}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-system-detail-group", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-Bf_B5lBQqHL9T0gVMvigj", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "系统信息"}], "underline": true}, "type": "Layout"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail", "name": "Detail", "props": {"flow": {"containerKey": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail", "context$": "$context", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_PLN$pln_dp_demand_forecast_type_cf_FIND_DATA_BY_ID_SERVICE_BzVyMx2", "type": "InvokeService"}, "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf"}, "type": "Container"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showFooter": false, "showHeader": true}, "type": "Container"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-view", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? 编辑预测需求类型配置表 : \"新建预测需求类型配置表\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-name", "name": "FormField", "props": {"componentProps": {"fieldAlias": "name", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "t9YHvglW7jLFfY1OJmYCD", "valueRules": null}], "name": "name", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-code", "name": "FormField", "props": {"componentProps": {"fieldAlias": "code", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "ZPICgEQPBacC30y-SzNGe", "valueRules": null}], "name": "code", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-effectiveDays", "name": "FormField", "props": {"componentProps": {"fieldAlias": "effectiveDays", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请输入", "precision": 0}, "displayComponentProps": {"precision": 0}, "displayComponentType": "Number", "editComponentProps": {"precision": 0, "shape": "line"}, "editComponentType": "InputNumber", "hidden": false, "label": "有效天数", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "_jH_OKW8w5HmoyeVQrNKY", "valueRules": null}], "name": "effectiveDays", "rules": [], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-coverHistory", "name": "FormField", "props": {"componentProps": {"fieldAlias": "coverHistory", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "hidden": false, "initialValue": true, "label": "是否覆盖历史需求", "lookup": [{"fieldRules": {"hidden": false, "readOnly": true, "required": false}, "key": "TO0eQEiQmrHNJd363ekfo", "valueRules": null}], "name": "coverHistory", "rules": [], "type": "BOOL"}, "type": "Widget"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-gxYVJC-o6aX-vml_zTErw", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "基本信息"}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-itemCode", "name": "FormField", "props": {"componentProps": {"fieldAlias": "itemCode", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "hidden": false, "label": "物料编码", "name": "itemCode", "rules": [], "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-invOrg", "name": "FormField", "props": {"componentProps": {"fieldAlias": "invOrg", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "hidden": false, "label": "库存组织", "name": "invOrg", "rules": [], "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-invLoc", "name": "FormField", "props": {"componentProps": {"fieldAlias": "invLoc", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "hidden": false, "label": "库存地点", "name": "invLoc", "rules": [], "type": "BOOL"}, "type": "Widget"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-qWOBxJF1p27hPaCN7x3Vj", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "必填项配置"}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-id", "name": "FormField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-createdBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "ERP_PLN$user", "parentModelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_PLN$user", "serviceKey": "ERP_PLN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "ERP_PLN$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "mA3Cs3uVuJLfYKIsYiztg", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "ERP_PLN$user", "serviceKey": "ERP_PLN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_PLN$user", "serviceKey": "ERP_PLN$user_PAGING_DATA_SERVICE", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-updatedBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "ERP_PLN$user", "parentModelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_PLN$user", "serviceKey": "ERP_PLN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "ERP_PLN$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "c1CSotzJNXLkUazCTm42f", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "ERP_PLN$user", "serviceKey": "ERP_PLN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_PLN$user", "serviceKey": "ERP_PLN$user_PAGING_DATA_SERVICE", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-createdAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-updatedAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-originOrgId", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "originOrgId", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "所属组织", "name": "originOrgId", "rules": [], "type": "NUMBER"}, "type": "Widget"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form-defaultGroup", "name": "FormGroupItem", "props": {"showSplit": true, "title": false}, "type": "Layout"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-z9nzRHKa1LDkvR2lXgBl5", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}], "underline": true}, "type": "Layout"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form", "context$": "$context", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "params$": "{ id: route.recordId }", "serviceKey": "ERP_PLN$pln_dp_demand_forecast_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE_BzVyMx3", "test$": "!!route.recordId", "type": "InvokeService"}, {"containerKey": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form", "context$": "$context", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "route?.query?.copyId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "params$": "{ id: route?.query?.copyId }", "serviceKey": "ERP_PLN$pln_dp_demand_forecast_type_cf_COPY_DATA_CONVERTER_SERVICE_BzVyMx4", "test$": "!!route.query?.copyId", "type": "InvokeService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target$": "route?.action === \"edit\" ? \"show\" : \"list\""}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Validate", "validate": true}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_PLN$pln_dp_demand_forecast_type_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "valueConfig": {"action": {"target": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form"}, "type": "action"}}], "service": "ERP_PLN$pln_dp_demand_forecast_type_cf_MASTER_DATA_SAVE_DATA_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "data.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}, {"action": "Refresh", "target": ["ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config"]}, {"action": "Message", "level": "success", "message": "保存成功!"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "type": "primary"}, "type": "Widget"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showBack": true, "showFooter": true, "showHeader": true}, "type": "Container"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "name": "ColumnPage", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": false, "showHeader": false, "title": "预测需求类型配置"}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new:list", "resources": [{"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config", "label": "表格", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}], "relations": [{"key": "ERP_PLN$pln_dp_demand_forecast_type_cf_PAGING_DATA_SERVICE_BzVyMx1", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config-new", "label": "新建", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config", "label": "表格", "type": "Table"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config-batch/items/ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-ERP_PLN$pln_mrp_demand_forecast_type_config-multi-delete", "label": "批量删除", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config", "label": "表格", "type": "Table"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "ERP_PLN$pln_dp_demand_forecast_type_cf_BATCH_DELETE_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf"}, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config-import", "label": "导入", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config", "label": "表格", "type": "Table"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "/api/gei/template/download", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/import-sub-model", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/import-direct-by-oss", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config-export", "label": "导出", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config", "label": "表格", "type": "Table"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_QUERY_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_SAVE_HEADER_SELECTIVE_RECORD_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/trantor/portal/user/current", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config-logs", "label": "日志", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config", "label": "表格", "type": "Table"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config-toolbar-actions", "label": "按钮组", "type": "ToolbarActions"}], "relations": [{"key": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-B5McuquLj_muzeR5qxCCA", "label": "名称", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config", "label": "表格", "type": "Table"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-eR4Gc-WK7QcT1_Dlv_h9X", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-EjykxL062ILJzd6SRnOQL", "label": "编码", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-list-ERP_PLN$pln_mrp_demand_forecast_type_config", "label": "表格", "type": "Table"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-eR4Gc-WK7QcT1_Dlv_h9X", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form", "label": "表单组", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_PLN$pln_dp_demand_forecast_type_cf_FIND_SINGLE_DATA_BY_ID_SERVICE_BzVyMx3", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf"}, "type": "Service"}, {"key": "ERP_PLN$pln_dp_demand_forecast_type_cf_COPY_DATA_CONVERTER_SERVICE_BzVyMx4", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail", "label": "详情", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_PLN$pln_dp_demand_forecast_type_cf_FIND_DATA_BY_ID_SERVICE_BzVyMx2", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-action-cancel", "label": "取消", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-action-save", "label": "保存", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "ERP_PLN$pln_dp_demand_forecast_type_cf_MASTER_DATA_SAVE_DATA_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-actions-delete", "label": "删除", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "ERP_PLN$pln_dp_demand_forecast_type_cf_DELETE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_dp_demand_forecast_type_cf"}, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-actions-copy", "label": "复制", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-actions-edit", "label": "编辑", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detail-ERP_PLN$pln_mrp_demand_forecast_type_config-logs", "label": "日志", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-name", "label": "名称", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-z9nzRHKa1LDkvR2lXgBl5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-gxYVJC-o6aX-vml_zTErw", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-code", "label": "编码", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-z9nzRHKa1LDkvR2lXgBl5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-gxYVJC-o6aX-vml_zTErw", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-effectiveDays", "label": "有效天数", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-z9nzRHKa1LDkvR2lXgBl5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-gxYVJC-o6aX-vml_zTErw", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-coverHistory", "label": "是否覆盖历史需求", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-z9nzRHKa1LDkvR2lXgBl5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-gxYVJC-o6aX-vml_zTErw", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-itemCode", "label": "物料编码", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-z9nzRHKa1LDkvR2lXgBl5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-qWOBxJF1p27hPaCN7x3Vj", "label": "必填项配置", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-invOrg", "label": "库存组织", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-z9nzRHKa1LDkvR2lXgBl5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-qWOBxJF1p27hPaCN7x3Vj", "label": "必填项配置", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-invLoc", "label": "库存地点", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-z9nzRHKa1LDkvR2lXgBl5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-qWOBxJF1p27hPaCN7x3Vj", "label": "必填项配置", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-id", "label": "ID", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-z9nzRHKa1LDkvR2lXgBl5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-createdBy", "label": "创建人", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-z9nzRHKa1LDkvR2lXgBl5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "ERP_PLN$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$user"}, "type": "Service"}, {"key": "ERP_PLN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-updatedBy", "label": "更新人", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-z9nzRHKa1LDkvR2lXgBl5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "ERP_PLN$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$user"}, "type": "Service"}, {"key": "ERP_PLN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-createdAt", "label": "创建时间", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-z9nzRHKa1LDkvR2lXgBl5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-updatedAt", "label": "更新时间", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-z9nzRHKa1LDkvR2lXgBl5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-version", "label": "版本号", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-z9nzRHKa1LDkvR2lXgBl5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-deleted", "label": "逻辑删除标识", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-z9nzRHKa1LDkvR2lXgBl5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-originOrgId", "label": "所属组织", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-z9nzRHKa1LDkvR2lXgBl5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-editView-ERP_PLN$pln_mrp_demand_forecast_type_config-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-system-detail-field-createdBy", "label": "创建人", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-Bf_B5lBQqHL9T0gVMvigj", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [{"key": "ERP_PLN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-system-detail-field-updatedBy", "label": "更新人", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-Bf_B5lBQqHL9T0gVMvigj", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [{"key": "ERP_PLN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-system-detail-field-createdAt", "label": "创建时间", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-Bf_B5lBQqHL9T0gVMvigj", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-system-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-Bf_B5lBQqHL9T0gVMvigj", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-name", "label": "名称", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-N2ZOE2yRp9UHZEX3Q2Kj3", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-hDrJtf80iXsslVt4ili2d", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-code", "label": "编码", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-N2ZOE2yRp9UHZEX3Q2Kj3", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-hDrJtf80iXsslVt4ili2d", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-effectiveDays", "label": "有效天数", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-N2ZOE2yRp9UHZEX3Q2Kj3", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-hDrJtf80iXsslVt4ili2d", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-coverHistory", "label": "是否覆盖历史需求", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-N2ZOE2yRp9UHZEX3Q2Kj3", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-hDrJtf80iXsslVt4ili2d", "label": "基本信息", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-itemCode", "label": "物料编码", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-N2ZOE2yRp9UHZEX3Q2Kj3", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-AaZEKTiFyBkUJSxXILHuw", "label": "必填项配置", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-invOrg", "label": "库存组织", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-N2ZOE2yRp9UHZEX3Q2Kj3", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-AaZEKTiFyBkUJSxXILHuw", "label": "必填项配置", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-field-invLoc", "label": "库存地点", "path": [{"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-N2ZOE2yRp9UHZEX3Q2Kj3", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-detailView-ERP_PLN$pln_mrp_demand_forecast_type_config-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}, {"key": "ERP_PLN$pln_mrp_forecast_demand_type_config_view_new-AaZEKTiFyBkUJSxXILHuw", "label": "必填项配置", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}}