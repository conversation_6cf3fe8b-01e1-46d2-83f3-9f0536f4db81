{"type": "View", "name": "OnClick Disable And Enable", "children": [], "props": {"content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "rn7Veb4dqSxz9VZ0SZtYe", "name": "<PERSON><PERSON>", "props": {"label": "新建", "onClick$": "systemActions.create($context)", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "TkyCMkSfFRr0ZzUjk4nSt", "name": "<PERSON><PERSON>", "props": {"label": "导入"}, "type": "Widget"}, {"children": [], "key": "wbBX8SAxH5utL9wb5DCX3", "name": "<PERSON><PERSON>", "props": {"label": "导出"}}], "key": "xGReYPAAuO4cva2cxXAWo", "name": "BatchActions", "props": {}}, {"children": [{"children": [], "key": "4noVlrQpjGufPHXNmQsBF", "name": "Field", "props": {"componentProps": {"options": [{"label": "销售", "value": "SLS"}, {"label": "采购", "value": "PUR"}], "placeholder": "请选择业务类别"}, "label": "业务类别", "name": "btClass", "type": "MULTISELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "QpcxncGVP-mV_a6UptF5G", "name": "Field", "props": {"componentProps": {"placeholder": "请输入类型"}, "label": "类型", "name": "reasonTypeCode", "type": "TEXT", "width": 120}, "type": "Widget"}], "key": "KHlZ5Av5JXJCKECDpfQlt", "name": "Fields", "props": {}, "type": "Meta"}], "key": "BhdjHon98DLs88AAINBFq", "name": "Table", "props": {"filterFields": [{"label": "业务类别", "name": "btClass"}, {"label": "类型", "name": "reasonTypeCode"}, {"label": "名称", "name": "reasonName"}, {"label": "类别", "name": "reasonCate"}, {"label": "备注", "name": "remark"}, {"label": "是否开票冻结", "name": "isBilBlock"}, {"label": "是否交货冻结", "name": "isDelBlock"}], "flow$": "params => invokeSystemServiceQuery('ERP_SCM$SYS_PagingDataService', 'cf_reason_type', params)", "mode": "simple", "onRow$": "record => ({ onClick: systemActions.show({ record }, {openType: \"slot\"}) })", "pagination": {"size": "small"}}, "type": "Container"}, {"children": [{"children": [], "key": "g5swIepCmNO6KD3cZwnt4", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~"}}, {"children": [{"children": [{"children": [{"children": [], "key": "zuY-v2_y7716bXnzCZKS6", "name": "Text", "props": {"value$": "data?.name"}}], "key": "stolhzmL0sGUWCWMdq-Sw", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "q-tCE6G7-IkSVHWcZNd1T", "name": "<PERSON><PERSON>", "props": {"label": "删除", "onClick$": "systemActions.delete($context, \"cf_reason_type\")"}}, {"children": [], "key": "6SuSLph3aINr8RlnntjFe", "name": "<PERSON><PERSON>", "props": {"label": "复制"}, "type": "Widget"}, {"children": [{"children": [], "key": "Ako-Fqp_-voHhVFFxI1Cf", "name": "<PERSON><PERSON>", "props": {"label": "停用", "onClick$": "() => invokeSystemServiceQuery(\"ERP_SCM$SYS_MasterData_DisableDataService\", \"cf_reason_type\", {id: data?.id})"}, "type": "Widget"}], "key": "M3G0CTMfCxs5aYYIqLaPp", "name": "Show", "props": {"value$": "data?.status?.includes(\"ENABLED\")"}, "type": "Meta"}, {"children": [{"children": [], "key": "cow5zHcaIaMTbn5DNolX0", "name": "<PERSON><PERSON>", "props": {"label": "启用", "onClick$": "() => invokeSystemServiceQuery(\"ERP_SCM$SYS_MasterData_EnableDataService\", \"cf_reason_type\", {id: data?.id})"}, "type": "Widget"}], "key": "LWFR-ThpjYk-3H5lt_skf", "name": "Show", "props": {"value$": "data?.status?.includes(\"INACTIVE\") || data?.status?.includes(\"DISABLED\")"}, "type": "Meta"}, {"children": [], "key": "b6ALQV4z9FlAfOWj9mX6G", "name": "<PERSON><PERSON>", "props": {"actionConfig": {}, "label": "编辑", "onClick$": "systemActions.edit($context)", "type": "primary"}, "type": "Widget"}], "key": "Fb5jK9WduqNN6zVqMIZ6N", "name": "Space", "props": {}, "type": "Layout"}], "key": "vDUMaLVgsun8IiqzQQEzu", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "7W45LpC-JwrY4odYhkEEE", "name": "DetailField", "props": {"componentProps": {"options": [{"label": "销售", "value": "SLS"}, {"label": "采购", "value": "PUR"}], "placeholder": "请选择业务类别"}, "label": "业务类别", "name": "btClass", "type": "MULTISELECT"}}, {"children": [], "key": "pyNvrqdHQliBSvlc_IA6N", "name": "DetailField", "props": {"componentProps": {"placeholder": "请输入类型"}, "label": "类型", "name": "reasonTypeCode", "type": "TEXT"}}, {"children": [], "key": "zAeEuHckeMAcCTEIwdSS2", "name": "DetailField", "props": {"componentProps": {"placeholder": "请输入名称"}, "label": "名称", "name": "reasonName", "type": "TEXT"}}, {"children": [], "key": "BIVY2eWeWGunPo793-PqG", "name": "DetailField", "props": {"componentProps": {"placeholder": "请输入类别"}, "label": "类别", "name": "reasonCate", "type": "TEXT"}}, {"children": [], "key": "39Fs0CcOUExg4FomOj_bM", "name": "DetailField", "props": {"componentProps": {"placeholder": "请输入备注"}, "label": "备注", "name": "remark", "type": "TEXT"}}, {"children": [], "key": "pO8X2aWoHz8wOPJmWNP7q", "name": "DetailField", "props": {"componentProps": {"placeholder": "请选择是否开票冻结"}, "label": "是否开票冻结", "name": "isBilBlock", "type": "BOOL"}}, {"children": [], "key": "YjUsK-sJFcEGOoG8XqGiB", "name": "DetailField", "props": {"componentProps": {"placeholder": "请选择是否交货冻结"}, "label": "是否交货冻结", "name": "isDelBlock", "type": "BOOL"}}], "key": "U3dZEJKAKZ9rSpUFBInag", "name": "DetailGroupItem", "props": {}, "title": "基本信息", "type": "Layout"}], "key": "APmQvvuhcgRZJPXyM-gqh", "name": "Detail", "props": {"flow$": "() => (mode === 'design' ? {} : data)"}, "type": "Container"}], "key": "pZRnf0-sumraBF5Z4-cKL", "name": "Tabs", "props": {"items": [{"label": "主体信息"}], "underline": true}, "type": "Layout"}], "key": "yJMEHz1DNvpAMcqNnYQr2", "name": "Show", "props": {"value$": "data?.id"}, "type": "Meta"}], "key": "5TBanTBlTjQUsAKcDWNWc", "name": "Page", "props": {}, "type": "Container"}], "key": "mbKwgXrJn8NSqPEDcwfyo", "name": "UseQuery", "props": {"loader$": "() => invokeSystemServiceQuery('ERP_SCM$SYS_FindDataByIdService', 'cf_reason_type')"}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "oJjxY_74A14XrJmL4xcaX", "name": "Text", "props": {"value$": "route?.action === \"edit\" ? \"编辑原因代码表\" : \"创建原因代码表\""}}], "key": "xBGqU5IDYXfyGc2rQVh9R", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "CgjwLWmR_jnIXtRS1fu-J", "name": "FormField", "props": {"componentProps": {"options": [{"label": "销售", "value": "SLS"}, {"label": "采购", "value": "PUR"}], "placeholder": "请选择业务类别"}, "label": "业务类别", "name": "btClass", "rules": [{"message": "请输入业务类别", "required": true}], "type": "MULTISELECT"}, "type": "Widget"}, {"children": [], "key": "SblQ4OWpPpeVqLq2OPiTh", "name": "FormField", "props": {"componentProps": {"placeholder": "请输入类型"}, "label": "类型", "name": "reasonTypeCode", "rules": [{"message": "请输入类型", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "4YAsl9NL6fWvKh4brMVZ3", "name": "FormField", "props": {"componentProps": {"placeholder": "请输入名称"}, "label": "名称", "name": "reasonName", "rules": [{"message": "请输入名称", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "m5yEpRkWgfj13_9WAjVXr", "name": "FormField", "props": {"componentProps": {"placeholder": "请输入类别"}, "label": "类别", "name": "reasonCate", "rules": [{"message": "请输入类别", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "WpBG-gk8rAyBCuT1g3IJ2", "name": "FormField", "props": {"componentProps": {"placeholder": "请输入备注"}, "label": "备注", "name": "remark", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "TOu5BAbdz0GAX87vp75gC", "name": "FormField", "props": {"componentProps": {"placeholder": "请选择是否开票冻结"}, "label": "是否开票冻结", "name": "isBilBlock", "rules": [], "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "wPQUEIIuYBbsM6ob4QKy2", "name": "FormField", "props": {"componentProps": {"placeholder": "请选择是否交货冻结"}, "label": "是否交货冻结", "name": "isDelBlock", "rules": [], "type": "BOOL"}, "type": "Widget"}], "key": "RF7WasDczT8lIPy06TMR7", "name": "FormGroupItem", "props": {"title": "基本信息"}, "type": "Layout"}], "key": "o0FDtRqvThZBuhFElmA36", "name": "Tabs", "props": {"items": [{"label": "主体信息"}], "underline": true}, "type": "Layout"}], "key": "juozY04j0nkfYUY-5dOn5", "name": "FormGroup", "props": {"layout": "horizontal", "loadFlow$": "() => route.action === 'edit' ? invokeSystemServiceQuery('ERP_SCM$SYS_FindDataByIdService', 'cf_reason_type') : null"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "Nq90mBUolfF028a92U4JQ", "name": "<PERSON><PERSON>", "props": {"label": "取消", "onClick$": "systemActions.show($context, {openType: \"slot\"})"}, "type": "Widget"}, {"children": [], "key": "r0vnGmu2l5r5hs-e-avMS", "name": "<PERSON><PERSON>", "props": {"label": "保存", "onClick$": "systemActions.save($context, \"ERP_SCM$SYS_MasterData_SaveDataService\", \"cf_reason_type\")", "type": "primary"}, "type": "Widget"}], "key": "CwJJsWMXGZtPs49sXmrRE", "name": "Space", "props": {}, "type": "Layout"}], "key": "15KVDiKD3o4be79rrRpxx", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "sh1iuNvjurr_I0Kwwrovb", "name": "Page", "props": {}, "type": "Container"}], "key": "VbOQIkubK6MuqkpdyR3hz", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "lEjClq2ozBvcO2hO9do4g", "name": "ColumnPage", "props": {}, "type": "Layout"}], "key": "PytcRy-7ZuSdxVxnbvOTV", "name": "Page", "props": {"showHeader": false, "title": "原因配置"}, "type": "Container"}}}