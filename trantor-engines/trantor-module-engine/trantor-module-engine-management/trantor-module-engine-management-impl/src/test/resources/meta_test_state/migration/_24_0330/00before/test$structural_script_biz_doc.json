{"type": "View", "name": "Structural script for bizDoc", "children": [], "props": {"content": {"children": [{"key": "fg0", "name": "FormGroup", "props": {"modelAlias": "not related"}, "type": "Widget"}, {"key": "fg1", "name": "FormGroup", "props": {"modelAlias": "TERP_MIGRATE$gen_mat_cate_md"}, "type": "Widget"}, {"key": "b2", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "systemActions.save($context, \"TERP_MIGRATE$SYS_SaveDataService\", \"TERP_MIGRATE$gen_mat_cate_md\")()", "endLogic": "Other", "endLogicOtherConfig": []}}, "type": "Widget"}, {"key": "b3", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "navigate({ action: 'new', query: { copyId: route.recordId } })"}}, "type": "Widget"}], "key": "111", "name": "Page", "props": {"showBack": true, "showFooter": true, "showHeader": true}, "type": "Container"}, "key": "test$structural_script_biz_doc", "type": "DETAIL"}}