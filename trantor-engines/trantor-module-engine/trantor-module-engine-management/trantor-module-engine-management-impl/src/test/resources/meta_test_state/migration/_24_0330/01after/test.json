{"type": "<PERSON><PERSON><PERSON>", "name": "Test Module", "children": [{"type": "View", "key": "test$frontend_config_module_all_1", "name": "Frontend Config Module All 1"}, {"type": "View", "key": "test$frontend_config_module_only_material", "name": "Frontend Config Module Only Material"}, {"type": "View", "key": "test$frontend_config_module_only_terp", "name": "Frontend Config Module Only TERP"}, {"type": "View", "key": "test$frontend_config_module_useless_service", "name": "Frontend Config Module Useless Service"}, {"type": "View", "key": "test$remove_hidden_fields_1", "name": "Remove Hidden Fields 1"}, {"type": "View", "key": "test$remove_hidden_fields_2", "name": "Remove Hidden Fields 2"}, {"type": "View", "key": "test$remove_hidden_fields_3", "name": "Remove Hidden Fields 3"}, {"type": "View", "key": "test$structural_script_master_data", "name": "Structural script for masterData"}, {"type": "View", "key": "test$structural_script_biz_doc", "name": "Structural script for bizDoc"}, {"type": "View", "key": "test$structural_script_all", "name": "Structural script for all"}, {"type": "View", "key": "test$append_custom_detail_for_table", "name": "append custom detail for table"}]}