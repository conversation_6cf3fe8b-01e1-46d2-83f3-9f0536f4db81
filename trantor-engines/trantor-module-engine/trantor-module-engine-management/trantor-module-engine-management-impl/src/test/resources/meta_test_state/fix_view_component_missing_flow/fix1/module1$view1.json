{"name": "view1", "type": "View", "props": {"content": {"key": "TERP_MIGRATE$GEN_CATE-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent", "name": "FormField", "type": "Widget", "props": {"name": "mat<PERSON>ate<PERSON><PERSON>nt", "type": "SELFRELATION", "label": "父类目", "rules": [], "hidden": false, "initialValue": null, "componentProps": {"label": "选择父类目", "columns": ["matCateCode", "matCateName", "<PERSON><PERSON><PERSON><PERSON>", "status", "mat<PERSON>ate<PERSON><PERSON>nt", "genCounClassMatTaxItemCfId", "qualificationsGroupId", "isLimitPoQualifications", "isLimitSoQualifications", "isLimitCtQualifications"], "fieldAlias": "mat<PERSON>ate<PERSON><PERSON>nt", "labelField": "matCateName", "modelAlias": "TERP_MIGRATE$gen_mat_cate_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "searchServiceKey": "TERP_MIGRATE$SYS_PagingDataService"}, "parentModelAlias": "TERP_MIGRATE$gen_mat_cate_md"}, "editComponentType": "TreeSelect", "editComponentProps": {"leafOnly": false, "showScope": "all", "labelField": "matCateName", "tableCondition": null, "treeSelectField": "mat<PERSON>ate<PERSON><PERSON>nt", "flow": {"containerKey": "TERP_MIGRATE$GEN_CATE-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent", "context$": "$context", "modelAlias": "TERP_MIGRATE$gen_mat_cate_md", "serviceKey": "TERP_MIGRATE$SYS_FindTreeChildrenDataService", "type": "InvokeSystemService"}, "reverseConstructFlow": {"containerKey": "TERP_MIGRATE$GEN_CATE-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent", "context$": "$context", "modelAlias": "TERP_MIGRATE$gen_mat_cate_md", "serviceKey": "TERP_MIGRATE$SYS_ReverseConstructTreeService", "type": "InvokeSystemService"}}, "displayComponentType": "SelfRelationShow", "displayComponentProps": {"flow": {"containerKey": "TERP_MIGRATE$GEN_CATE-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent", "context$": "$context", "modelAlias": "TERP_MIGRATE$gen_mat_cate_md", "serviceKey": "TERP_MIGRATE$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "reverseConstructFlow": {"containerKey": "TERP_MIGRATE$GEN_CATE-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent", "context$": "$context", "modelAlias": "TERP_MIGRATE$gen_mat_cate_md", "serviceKey": "TERP_MIGRATE$SYS_ReverseConstructTreeService", "type": "InvokeSystemService"}}}, "children": []}, "resources": [{"key": "TERP_MIGRATE$GEN_CATE-editView-TERP_MIGRATE$gen_mat_cate_md-field-matCateParent", "path": [{"key": "TERP_MIGRATE$GEN_CATE-page", "type": "View", "label": "视图"}, {"key": "TERP_MIGRATE$GEN_CATE-column-page", "type": "ColumnPage", "label": "双栏页面"}, {"key": "TERP_MIGRATE$GEN_CATE-column-page-stackView", "type": "StackView", "label": "栈视图"}, {"key": "TERP_MIGRATE$GEN_CATE-editView", "type": "Page", "label": "页面"}, {"key": "TERP_MIGRATE$GEN_CATE-editView-TERP_MIGRATE$gen_mat_cate_md-form", "type": "FormGroup", "label": "表单组"}, {"key": "TERP_MIGRATE$GEN_CATE-editView-defaultTabs", "type": "Tabs", "label": "页签"}, {"key": "TERP_MIGRATE$GEN_CATE-editView-TERP_MIGRATE$gen_mat_cate_md-form-defaultGroup", "type": "FormGroupItem", "label": false}], "type": "Container", "label": "父类目", "relations": [{"key": "TERP_MIGRATE$SYS_FindDataByIdService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$gen_mat_cate_md"}}, {"key": "TERP_MIGRATE$SYS_PagingDataService", "name": null, "type": "SystemService", "props": {"httpMethod": null, "modelAlias": "TERP_MIGRATE$gen_mat_cate_md"}}, {"key": "TERP_MIGRATE$SYS_FindTreeChildrenDataService", "type": "SystemService", "props": {"modelAlias": "TERP_MIGRATE$gen_mat_cate_md"}}, {"key": "TERP_MIGRATE$SYS_ReverseConstructTreeService", "type": "SystemService", "props": {"modelAlias": "TERP_MIGRATE$gen_mat_cate_md"}}], "description": null}]}}