{"type": "View", "name": "物料详情", "parentKey": "ERP_SCM", "children": [], "props": {"conditionGroups": {"0JrE-8bIOtTjq6xv8kFe9": {"conditions": [{"conditions": [{"id": "U9nU0qhPL3tOyweW1RPeS", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "genMatMdId", "fieldName": "genMatMdId", "fieldType": null, "modelAlias": null, "relatedModel": null}, {"fieldKey": "id", "fieldName": "id", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "genMatMdId", "valueName": "genMatMdId"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"exprValue": "route.recordId", "fieldType": "Number", "id": null, "type": "ExprValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "Uj6LfJ1sKOnyEvwYp8VUC", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "0JrE-8bIOtTjq6xv8kFe9", "logicOperator": "OR", "type": "ConditionGroup"}, "B6BoQ0QNv3CkHxO84I3eY": {"conditions": [{"conditions": [{"id": "RTsPhwOZ4PeoD_JAC2fOy", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "genMatMdId", "fieldName": "genMatMdId", "fieldType": null, "modelAlias": null, "relatedModel": null}, {"fieldKey": "id", "fieldName": "id", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "genMatMdId", "valueName": "genMatMdId"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"exprValue": "route.recordId", "fieldType": "Number", "id": null, "type": "ExprValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "Ue_pdeS13G3uNkMPSlunx", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "B6BoQ0QNv3CkHxO84I3eY", "logicOperator": "OR", "type": "ConditionGroup"}, "EEw0sFqcO-v3qd_-LTUBp": {"conditions": [{"conditions": [{"id": "hBTOuv0ffc_lpMhUXyWWG", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "genMatMdId", "fieldName": "genMatMdId", "fieldType": null, "modelAlias": null, "relatedModel": null}, {"fieldKey": "id", "fieldName": "id", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "genMatMdId", "valueName": "genMatMdId"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"exprValue": "route.recordId", "fieldType": "Number", "id": null, "type": "ExprValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "-bXpnNa-hKdiv8wHM8d_i", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "EEw0sFqcO-v3qd_-LTUBp", "logicOperator": "OR", "type": "ConditionGroup"}, "Eim6jL3qCaopYv0c7qqTH": {"conditions": [{"conditions": [{"id": "-LzXgNS7x7xXMZgBPC9zK", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "genMatMdId", "fieldName": "genMatMdId", "fieldType": null, "modelAlias": null, "relatedModel": null}, {"fieldKey": "id", "fieldName": "id", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "genMatMdId", "valueName": "genMatMdId"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"exprValue": "route.recordId", "fieldType": "Number", "id": null, "type": "ExprValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "yNVfPcADWU95x9QG6xh0I", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "Eim6jL3qCaopYv0c7qqTH", "logicOperator": "OR", "type": "ConditionGroup"}, "F0N643-GekkdIqvbG1T2b": {"conditions": [{"conditions": [{"id": "dTgGyF0vZtQ8SUowj3Nj0", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgBusinessTypeCode", "fieldName": "orgBusinessTypeCode", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgBusinessTypeCode", "valueName": "orgBusinessTypeCode"}]}, "operator": "EQ", "rightValue": {"constValue": "INV_ORG", "fieldType": "Text", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "w5uy-U_zpM9-V5K07lQuy", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgStatus", "fieldName": "orgStatus", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "AEhzHSmYkXz6K7XdqTfMt", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "F0N643-GekkdIqvbG1T2b", "logicOperator": "OR", "type": "ConditionGroup"}, "Ggg_vUixft2kPxNMqjt_O": {"conditions": [{"conditions": [{"id": "NFDhqM0Qa7M866ubjb5D6", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "genMatMdId", "fieldName": "genMatMdId", "fieldType": null, "modelAlias": null, "relatedModel": null}, {"fieldKey": "id", "fieldName": "id", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "genMatMdId", "valueName": "genMatMdId"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"exprValue": "route.recordId", "fieldType": "Number", "id": null, "type": "ExprValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "-hzn38n9KnI7CqkK4XRg3", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "Ggg_vUixft2kPxNMqjt_O", "logicOperator": "OR", "type": "ConditionGroup"}, "ipekk-lyI5-h9BUH_Y_iC": {"conditions": [{"conditions": [{"id": "TmazHuQsWg63ovCpJ4DgX", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "genMatMdId", "fieldName": "genMatMdId", "fieldType": null, "modelAlias": null, "relatedModel": null}, {"fieldKey": "id", "fieldName": "id", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "genMatMdId", "valueName": "genMatMdId"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"exprValue": "route.recordId", "fieldType": "Number", "id": null, "type": "ExprValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "-bQGuSfu6Gf3VS8tkGO81", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "ipekk-lyI5-h9BUH_Y_iC", "logicOperator": "OR", "type": "ConditionGroup"}, "jNhkuO9cpx_DUtKv5wVmZ": {"conditions": [{"conditions": [{"id": "XEpdLa56pYBXabjrfBP7M", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgStatus", "fieldName": "orgStatus", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "M-rjF7BUurxP5_bi2q7rw", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgBusinessTypeCode", "fieldName": "orgBusinessTypeCode", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgBusinessTypeCode", "valueName": "orgBusinessTypeCode"}]}, "operator": "EQ", "rightValue": {"constValue": "INV_LOC", "fieldType": "Text", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "vXwAIoBGYF6WwPualCHhP", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "jNhkuO9cpx_DUtKv5wVmZ", "logicOperator": "OR", "type": "ConditionGroup"}, "qzRLuBqIfIyicwGMmeqhA": {"conditions": [{"conditions": [{"id": "y1NdQoUeGbQi13bW9AbXT", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgStatus", "fieldName": "orgStatus", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Enum", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}, {"id": "kyj9hi0QTTm2HYvsFhCKM", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "orgBusinessTypeCode", "fieldName": "orgBusinessTypeCode", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Text", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "orgBusinessTypeCode", "valueName": "orgBusinessTypeCode"}]}, "operator": "EQ", "rightValue": {"constValue": "INV_ORG", "fieldType": "Text", "id": null, "type": "ConstValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "B2zkpBykJDSR2dGJHBJp_", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "qzRLuBqIfIyicwGMmeqhA", "logicOperator": "OR", "type": "ConditionGroup"}, "z5g6025M9qCJz4rc6wV_Z": {"conditions": [{"conditions": [{"id": "Ef0sQ7vbvgH0_V8ovUDD6", "key": null, "leftValue": {"constValue": null, "fieldPaths": [{"fieldKey": "genMatMdId", "fieldName": "genMatMdId", "fieldType": null, "modelAlias": null, "relatedModel": null}, {"fieldKey": "id", "fieldName": "id", "fieldType": null, "modelAlias": null, "relatedModel": null}], "fieldType": "Number", "id": null, "type": "VarValue", "valueType": "VAR", "varValue": [{"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "genMatMdId", "valueName": "genMatMdId"}, {"fieldType": null, "modelAlias": null, "relatedModel": null, "valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"exprValue": "route.recordId", "fieldType": "Number", "id": null, "type": "ExprValue"}, "rightValues": null, "type": "ConditionLeaf"}], "id": "qlDNge-tjz2q2uB3BoIuG", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "z5g6025M9qCJz4rc6wV_Z", "logicOperator": "OR", "type": "ConditionGroup"}}, "containerSelect": {"ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65": [{"field": "invOrgId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "orgName", "selectFields": null}]}, {"field": "factoryStatusId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "matStatusName", "selectFields": null}]}, {"field": "invLocId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "orgName", "selectFields": null}]}, {"field": "productionCycle", "selectFields": null}, {"field": "inspectionCycle", "selectFields": null}, {"field": "plannedCalendarId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "wcHeadCode", "selectFields": null}]}, {"field": "componentScrap", "selectFields": null}, {"field": "prdUomId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "uomDesc", "selectFields": null}]}, {"field": "postInspection", "selectFields": null}, {"field": "insufficientDeliveryTolerance", "selectFields": null}, {"field": "excessiveDeliveryTolerance", "selectFields": null}, {"field": "unlimitedOverDelivery", "selectFields": null}, {"field": "procurementType", "selectFields": null}, {"field": "backFlush", "selectFields": null}, {"field": "individualColl", "selectFields": null}, {"field": "prdMangerId", "selectFields": null}, {"field": "batchParaId", "selectFields": null}, {"field": "serialParaId", "selectFields": null}], "ERP_SCM$GEN_MAT_NEW_VIEW-7L2mAxHLXcSgSDM-6WkQt": [{"field": "baseUnitFactor", "selectFields": null}, {"field": "unitId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "uomDesc", "selectFields": null}]}, {"field": "symbol", "selectFields": null}, {"field": "targetUnitFactor", "selectFields": null}, {"field": "targetUnitId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "uomDesc", "selectFields": null}]}], "ERP_SCM$GEN_MAT_NEW_VIEW-8TWGUVjVdzAkatD_C9vVM": [{"field": "invOrgId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "orgName", "selectFields": null}]}, {"field": "mvmAvgPrc", "selectFields": null}, {"field": "stdAvgPrc", "selectFields": null}, {"field": "prcMethod", "selectFields": null}], "ERP_SCM$GEN_MAT_NEW_VIEW-KSYC3Wr5zoygttu713DyA": [{"field": "reorderQty", "selectFields": null}, {"field": "planningCycle", "selectFields": null}, {"field": "plannedDelivTime", "selectFields": null}, {"field": "inhouseProdutionTime", "selectFields": null}], "ERP_SCM$GEN_MAT_NEW_VIEW-YtesVUhKBEs0Bx52Ncrya": [{"field": "purOrg", "selectFields": [{"field": "id", "selectFields": null}, {"field": "orgName", "selectFields": null}]}, {"field": "purUomId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "uomDesc", "selectFields": null}]}, {"field": "purPrice", "selectFields": null}, {"field": "currId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "currName", "selectFields": null}]}, {"field": "isPurSrcList", "selectFields": null}, {"field": "taxId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "taxCode", "selectFields": null}, {"field": "tax", "selectFields": null}]}], "ERP_SCM$GEN_MAT_NEW_VIEW-lLVJtb0r_ii4i5_xUoiW2": [{"field": "slsOrgId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "orgName", "selectFields": null}]}, {"field": "slsDcId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "orgName", "selectFields": null}]}, {"field": "slsUomId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "uomDesc", "selectFields": null}]}, {"field": "slsBasePrice", "selectFields": null}, {"field": "slsCurrId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "currName", "selectFields": null}]}, {"field": "slsSoItemTypeGroupId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "soItemTypeGroupName", "selectFields": null}]}, {"field": "allowSurplusSurplusPercent", "selectFields": null}, {"field": "allowShortageShortagePercent", "selectFields": null}], "ERP_SCM$GEN_MAT_NEW_VIEW-t30HUYGRrLVdXuqwde9WP": [{"field": "invOrgId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "orgName", "selectFields": null}]}, {"field": "invLocId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "orgName", "selectFields": null}]}, {"field": "invUomId", "selectFields": [{"field": "id", "selectFields": null}, {"field": "uomDesc", "selectFields": null}]}, {"field": "delayDeliveryTime", "selectFields": null}, {"field": "receivingTime", "selectFields": null}]}, "content": {"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-_oP9B6Io8C8LvQGq7EakU", "name": "Page<PERSON><PERSON>le", "props": {"title": "物料详情", "useExpression": false}, "type": "Meta"}, {"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-tPD2g6BgnFrobKMCxqQV4", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"type": "const", "value": "ERP_GEN$gen_mat_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "route.recordId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "ERP_GEN$SYS_MasterData_EnableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ["ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO"]}], "executeLogic": "BindService"}, "confirmOn": "off", "label": "启用", "showCondition": {"conditions": [{"conditions": [{"id": "g9hYazX7srlbEAJve4R7b", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "状态", "type": "VarValue", "val": "status", "value": "ERP_GEN$gen_mat_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "itn6-Np_HiCLU1Hu557eg", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "Q0tJGateLShX6BUpZiCrS", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-yK0GVPCzhR6d1fMt0mjRN", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"type": "const", "value": "ERP_GEN$gen_mat_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "route.recordId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "ERP_GEN$SYS_MasterData_DisableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ["ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO"]}], "executeLogic": "BindService"}, "confirmOn": "off", "label": "停用", "showCondition": {"conditions": [{"conditions": [{"id": "xjW5B_EABr8OmQ1ObCsdT", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "状态", "type": "VarValue", "val": "status", "value": "ERP_GEN$gen_mat_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "HMMK-qO_8Gky79Gk2wMJH", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "ydwK9H-TnfQEqpivTP0fT", "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-38qrQym2s4fwo3G9YzMYZ", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "ERP_SCM$GEN_MAT_NEW_VIEW:TUiwgD6xaC-OxyYsf8NDL", "name": "编辑物料", "type": "View"}, "params": [{"expression": "route.recordId", "name": "recordId", "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "confirmOn": "off", "label": "编辑", "showCondition": {}, "type": "default"}, "type": "Widget"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-22OrU45hCIuICtzPHroVr", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-IyBm28Cih1CEBwrWSo5YA", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-Aje-tvubSbL2fHyltCZhZ", "name": "DetailMainInfo", "props": {"detailFields": ["matCode", "<PERSON><PERSON><PERSON>", "genMatTypeCfId", "baseUomId", "cateId", "brandId", "status"], "fields": [{"componentProps": {"fieldAlias": "matCode", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "label": "物料编码", "name": "matCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "label": "物料名称", "name": "<PERSON><PERSON><PERSON>", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["matTypeCode", "matTypeName"], "fieldAlias": "genMatTypeCfId", "label": "选择物料类型", "labelField": "matTypeName", "modelAlias": "ERP_GEN$gen_mat_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "label": "物料类型", "name": "genMatTypeCfId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "baseUomId", "label": "选择基本计量单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "label": "基本计量单位", "name": "baseUomId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["matCateCode", "matCateName", "<PERSON><PERSON><PERSON><PERSON>", "status", "genCounClassMatTaxItemCfId", "qualificationsGroupId", "isLimitPoQualifications", "isLimitSoQualifications", "isLimitCtQualifications", "path", "wqMatType", "mat<PERSON>ate<PERSON><PERSON>nt"], "fieldAlias": "cateId", "label": "选择类目", "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "label": "类目", "name": "cateId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["brandCode", "brandName", "brandPic"], "fieldAlias": "brandId", "label": "选择品牌", "labelField": "brandName", "modelAlias": "ERP_GEN$gen_brand_md", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "label": "品牌", "name": "brandId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "status", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "label": "状态", "name": "status", "required": false, "type": "SELECT", "width": 120}], "pictureField": "imageUrl", "title": "{{data.matName}}", "useExpression": true}, "type": "Widget"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-yaWyjEEfzf5RxIMuLYwL_", "name": "GridItem", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-6fzvYqQzpqpgrEW4mQnuq", "name": "MaterialIndicatorCard", "props": {"MaterialIndicatorServiceProps": {"listFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor2default/preference/INDICATOR"}, "saveFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/INDICATOR/save"}}, "indicators": [{"key": "gyj6bCfCTLvqe-OTrAot4", "label": "新的指标"}, {"key": "taAO1kHxxJv_UWqdij23V", "label": "新的指标"}, {"key": "dtoVrbJAmvONBDCa-DHem", "label": "新的指标"}]}, "type": "Widget"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-hZgUBW3hcGVTL0ZS0Evee", "name": "GridItem", "props": {}, "type": "Layout"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-GgLgT87qTxgCYwUZgeX1Z", "name": "Grid", "props": {"colNumber": 2, "colRatio": [6, 6], "customHeight": false, "gap": 0, "mode": "horizontal", "type": "2"}, "type": "Layout"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-ZSyniY6xBvdFjUdTyNEmx", "name": "DetailGroupItem", "props": {"defaultCollapsed": false, "title": ""}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-Z6FwBc2iFfyRygED_yAAO", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "imageUrl", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请上传"}, "displayComponentProps": {"displayType": "picture-card"}, "displayComponentType": "FileUploadShow", "editable": false, "label": "物料图片", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false}, "key": "SN-nIapCs901k88Jt2UKA", "valueRules": null}], "name": "imageUrl", "required": false, "type": "ATTACHMENT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-M4LUQqgJ4F6mF285xTf67", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "matCode", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "editable": false, "hidden": false, "label": "物料编码", "name": "matCode", "required": true, "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-nas76IR91qqlNQxE08cPM", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "editable": false, "hidden": false, "label": "物料名称", "name": "<PERSON><PERSON><PERSON>", "required": false, "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-EoKo-RPOUHOZJdh9W_JMz", "name": "DetailField", "props": {"componentProps": {"columns": ["matCateCode", "matCateName", "<PERSON><PERSON><PERSON><PERSON>", "status", "genCounClassMatTaxItemCfId", "qualificationsGroupId", "isLimitPoQualifications", "isLimitSoQualifications", "isLimitCtQualifications", "path", "wqMatType", "mat<PERSON>ate<PERSON><PERSON>nt"], "fieldAlias": "cateId", "label": "选择类目ID", "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "displayComponentProps": {"modelAlias": "ERP_GEN$gen_mat_cate_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "类目名称", "name": "matCateName", "required": false, "type": "TEXT", "width": 120}], "labelField": "matCateName", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_GEN$gen_mat_cate_md", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "editable": false, "hidden": false, "label": "类目", "name": "cateId", "required": false, "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-vOV0tA4T2KLNXSJ4CthxM", "name": "DetailField", "props": {"componentProps": {"columns": ["matTypeCode", "matTypeName"], "fieldAlias": "genMatTypeCfId", "label": "选择物料类型", "labelField": "matTypeCode", "modelAlias": "ERP_GEN$gen_mat_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "displayComponentProps": {"modelAlias": "ERP_GEN$gen_mat_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "matTypeCode", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "物料类型编码", "name": "matTypeCode", "required": true, "type": "TEXT", "width": 120}], "labelField": "matTypeCode", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_GEN$gen_mat_type_cf", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "editable": false, "hidden": false, "label": "物料类型", "name": "genMatTypeCfId", "required": true, "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-J5f0D-H3YlxNq-BTfb_M2", "name": "DetailField", "props": {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "baseUomId", "label": "选择基本计量单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "displayComponentProps": {"modelAlias": "ERP_GEN$gen_uom_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "计量单位名称", "name": "uomDesc", "required": false, "type": "TEXT", "width": 120}], "labelField": "uomDesc", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_GEN$gen_uom_type_cf", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "editable": false, "hidden": false, "label": "基本计量单位", "name": "baseUomId", "required": true, "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-Olc9_HswP1XywfPSaUT_s", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "matCodeExt", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "editable": false, "hidden": false, "label": "外部物料编码", "name": "matCodeExt", "required": false, "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-hpc2exYTmW-ed-yXMI3SW", "name": "DetailField", "props": {"componentProps": {"columns": ["brandCode", "brandName", "brandPic"], "fieldAlias": "brandId", "label": "选择品牌ID", "labelField": "brandCode", "modelAlias": "ERP_GEN$gen_brand_md", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "displayComponentProps": {"modelAlias": "ERP_GEN$gen_brand_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "brandCode", "modelAlias": "ERP_GEN$gen_brand_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "品牌编码", "name": "brandCode", "required": false, "type": "TEXT", "width": 120}], "labelField": "brandCode", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_GEN$gen_brand_md", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "editable": false, "hidden": false, "label": "品牌", "name": "brandId", "required": false, "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-j_LqfQdYdXI8FpRoZxqRB", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "editable": false, "hidden": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER"}, "type": "Widget"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-bV5wwTusBfIohRwQmfcbL", "name": "DetailGroupItem", "props": {"defaultCollapsed": false, "title": ""}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-0PVtJeR5LQpZDyvTY1Y2p", "name": "DetailField", "props": {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "weightUomId", "label": "选择重量单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "displayComponentProps": {"modelAlias": "ERP_GEN$gen_uom_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "计量单位名称", "name": "uomDesc", "required": false, "type": "TEXT", "width": 120}], "labelField": "uomDesc", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_GEN$gen_uom_type_cf", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "editable": false, "hidden": false, "label": "重量单位", "name": "weightUomId", "required": false, "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-awGZb0sWnBTWca68DDXND", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "grossWeight", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "editable": false, "hidden": false, "label": "毛重", "name": "grossWeight", "required": false, "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-wE9kuyjJh5jkS12mneBMV", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "netWeight", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "editable": false, "hidden": false, "label": "净重", "name": "netWeight", "required": false, "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-l3ama0exZ08rl9gj1ADGl", "name": "DetailField", "props": {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "volumUomId", "label": "选择体积单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "displayComponentProps": {"labelField": ["uomDesc"], "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "计量单位名称", "name": "uomDesc", "required": false, "type": "TEXT", "width": 120}], "labelField": "uomDesc", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_GEN$gen_uom_type_cf", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "editable": false, "hidden": false, "label": "体积单位", "name": "volumUomId", "required": false, "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-ANvTmtnC_m7iWkMWy4Dom", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "matVolum", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "editable": false, "hidden": false, "label": "体积", "name": "matVolum", "required": false, "type": "NUMBER"}, "type": "Widget"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-ocOc0Or6d93CDGwbVj0NR", "name": "DetailGroupItem", "props": {"defaultCollapsed": false, "title": "定价相关"}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-yDCCP7mpENoiBBb3TiDOk", "name": "DetailField", "props": {"componentProps": {"defaultValue": false, "fieldAlias": "batchRelv", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "editable": false, "hidden": false, "initialValue": false, "label": "是否启用批次管理", "name": "batchRelv", "required": false, "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-ExnWVNc-WYxaMNgOzILyz", "name": "DetailField", "props": {"componentProps": {"defaultValue": false, "fieldAlias": "isBatchDetermination", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "editable": false, "hidden": false, "initialValue": false, "label": "是否必须匹配批次确定", "lookup": [{"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "6m7XVHlaN-UlK9pESqury", "leftValue": {"fieldType": "Boolean", "scope": "form", "title": "批次管理", "type": "VarValue", "val": "batchRelv", "value": "ERP_GEN$gen_mat_md.batchRelv", "valueType": "VAR", "varVal": "batchRelv", "varValue": [{"valueKey": "batchRelv", "valueName": "batchRelv"}]}, "operator": "EQ", "rightValue": {"constValue": "false", "fieldType": "Boolean", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "6qIlgb5JV1_xP5d0REVxZ", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "apBrqsyeRFyyjSNUAqVHJ", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": true, "required": false}, "key": "bdhdxylcc3NMNlyxlK9hS", "operator": null, "valueRules": null}], "name": "isBatchDetermination", "required": false, "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-87DvdrDLijCrcRfG30A4S", "name": "DetailField", "props": {"componentProps": {"columns": ["code", "charaClassType", "name", "status", "remark"], "fieldAlias": "genCharaClassId", "label": "选择物料分类类别", "labelField": "name", "modelAlias": "ERP_GEN$gen_chara_class_md", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "displayComponentProps": {"modelAlias": "ERP_GEN$gen_chara_class_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "name", "modelAlias": "ERP_GEN$gen_chara_class_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "名称 ", "name": "name", "required": true, "type": "TEXT", "width": 120}], "labelField": "name", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_GEN$gen_chara_class_md", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "editable": false, "hidden": false, "label": "物料分类类别", "lookup": [{"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "jzsYrG-kFx099ZyIIFWin", "leftValue": {"fieldType": "Boolean", "scope": "form", "title": "批次管理", "type": "VarValue", "val": "batchRelv", "value": "ERP_GEN$gen_mat_md.batchRelv", "valueType": "VAR", "varVal": "batchRelv", "varValue": [{"valueKey": "batchRelv", "valueName": "batchRelv"}]}, "operator": "EQ", "rightValue": {"constValue": "false", "fieldType": "Boolean", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "Ui1PN3v_3yc6kt0wGsShS", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "75vBBgFglPDppCA6siV5l", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": true, "required": false}, "key": "EkeXkBvuU3l9bzWDllIWA", "operator": null, "valueRules": null}], "name": "genCharaClassId", "required": false, "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-R2HGVzbuf6HNTEzt6y5eW", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "charas", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "editable": false, "hidden": false, "label": "特征数据", "lookup": [{"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "nAgbxJb_mLGq9ItBog7e_", "leftValue": {"fieldType": "Boolean", "scope": "form", "title": "批次管理", "type": "VarValue", "val": "batchRelv", "value": "ERP_GEN$gen_mat_md.batchRelv", "valueType": "VAR", "varVal": "batchRelv", "varValue": [{"valueKey": "batchRelv", "valueName": "batchRelv"}]}, "operator": "EQ", "rightValue": {"constValue": "false", "fieldType": "Boolean", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "aTmnAXyki9_ngMUCARtlv", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "ajzFFiPHLZ_rAEd8H7ltX", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": true, "required": false}, "key": "FFgNUPeae8KqulWNeGRgj", "operator": null, "valueRules": null}], "name": "charas", "required": false, "type": "TEXT"}, "type": "Widget"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-Dl73seMFOiBVLFdnRMTdl", "name": "DetailGroupItem", "props": {"defaultCollapsed": false, "title": "批次相关"}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-BpFoRvLsM8MCgVnNYv1_e", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "shelfLife", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入", "precision": 0}, "displayComponentProps": {"precision": 0}, "displayComponentType": "Number", "editComponentProps": {"precision": 0, "shape": "line"}, "editComponentType": "InputNumber", "editable": false, "hidden": false, "label": "物料保质期", "name": "shelfLife", "required": false, "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-5_zMDevQIf39GSaXTaBOW", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "isAutoCountPlan", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentType": "BoolShow", "editComponentType": "Switch", "editable": false, "hidden": false, "label": "是否支持自动生成盘点方案", "lookup": [{"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "ycgDVKVGQunmb36KCIGjg", "leftValue": {"fieldType": "Boolean", "scope": "form", "title": "批次管理", "type": "VarValue", "val": "batchRelv", "value": "ERP_GEN$gen_mat_md.batchRelv", "valueType": "VAR", "varVal": "batchRelv", "varValue": [{"valueKey": "batchRelv", "valueName": "batchRelv"}]}, "operator": "EQ", "rightValue": {"constValue": "false", "fieldType": "Boolean", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "BOWWWyRSpE3bLuU8JRgCw", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "4M_ua0oZAv3MbYQGF7VHJ", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": true, "required": false}, "key": "elzBbHWvnwiH7nrOepVT9", "operator": null, "valueRules": null}], "name": "isAutoCountPlan", "required": false, "type": "BOOL"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-b3Ye9xVwp_unrRKVFfcwa", "name": "DetailField", "props": {"componentProps": {"columns": ["code", "name"], "fieldAlias": "atpGroupId", "label": "选择ATP检查组", "labelField": "name", "modelAlias": "ERP_GEN$gen_atp_group_md", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "displayComponentProps": {"labelField": ["name"], "modelAlias": "ERP_GEN$gen_atp_group_md"}, "displayComponentType": "RelationShow", "editable": false, "label": "ATP检查组", "name": "atpGroupId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-32Z55Z0CaNeO-HGa01QQ8", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "status", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "editable": false, "hidden": false, "label": "状态", "name": "status", "required": false, "type": "SELECT"}, "type": "Widget"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-SR2xCIfq3fSpjmtoMAY6I", "name": "DetailGroupItem", "props": {"defaultCollapsed": false, "title": "其他"}, "type": "Layout"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-t41GBakCOCRfCIm12li4H", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-2BGQpwR2Aa7a-ZhGafQTE", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-Ln4K_qoYronG2YUkDnzdq", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-2ImtoMf3jINDgdZ7b5xCr", "name": "Field", "props": {"componentProps": {"columns": ["orgCode", "orgName", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "orgSort", "orgBusinessTypeCode", "orgBusinessTypeId", "orgDimensionId", "orgParentId"], "fieldAlias": "slsOrgId", "label": "选择销售组织", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_GEN$gen_mat_sls_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": false, "label": "销售组织", "name": "slsOrgId", "required": true, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-sD2zspTQO4o24pPEbKyzz", "name": "Field", "props": {"componentProps": {"columns": ["orgCode", "orgName", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "orgSort", "orgBusinessTypeCode", "orgBusinessTypeId", "orgDimensionId", "orgParentId"], "fieldAlias": "slsDcId", "label": "选择销售渠道", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_GEN$gen_mat_sls_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": false, "label": "销售渠道", "name": "slsDcId", "required": true, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-muYkytSjUKoXYX05OnoTq", "name": "Field", "props": {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "slsUomId", "label": "选择销售单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_sls_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "label": "销售单位", "name": "slsUomId", "required": true, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-sFq0qlH87gB6edXZ4_A9O", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "slsBasePrice", "modelAlias": "ERP_GEN$gen_mat_sls_md", "placeholder": "请输入", "precision": 6}, "displayComponentProps": {"precision": 6}, "displayComponentType": "Number", "label": "基准价格", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "wUE1Px2OBlJNgxTOua-Bh", "valueRules": null}], "modelAlias": "ERP_GEN$gen_mat_sls_md", "name": "slsBasePrice", "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-440ENzXSPRSpnc0DFt9-_", "name": "Field", "props": {"align": "left", "componentProps": {"columns": ["currCode", "currName", "currIsoName", "decimalPlace", "symbol", "remark"], "fieldAlias": "slsCurrId", "label": "选择币种", "labelField": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_sls_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "displayComponentProps": {"modelAlias": "ERP_GEN$gen_curr_type_cf"}, "displayComponentType": "RelationShow", "label": "币种", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "CXWEMKRNS93c-02sd6tll", "valueRules": null}], "name": "slsCurrId", "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-z8fgDxXS4r_8sg6ZRnwkO", "name": "Field", "props": {"align": "left", "componentProps": {"columns": ["soItemTypeGroupCode", "soItemTypeGroupName", "desc"], "fieldAlias": "slsSoItemTypeGroupId", "label": "选择订单行项目类型组", "labelField": "soItemTypeGroupName", "modelAlias": "ERP_GEN$gen_sls_so_item_type_group_cf", "parentModelAlias": "ERP_GEN$gen_mat_sls_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "displayComponentProps": {"modelAlias": "ERP_GEN$gen_sls_so_item_type_group_cf"}, "displayComponentType": "RelationShow", "label": "订单行项目类型组", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "GIGmBNKW8UkIyBjEMrYns", "valueRules": null}], "name": "slsSoItemTypeGroupId", "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-vKOMnyrMBOm_NeBv2-3m4", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "allowSurplusSurplusPercent", "modelAlias": "ERP_GEN$gen_mat_sls_md", "placeholder": "请输入"}, "displayComponentType": "Number", "label": "允许超发比例", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "6q4NDwq3yiMQfZwThZQe_", "valueRules": null}], "name": "allowSurplusSurplusPercent", "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-EBzkGCjxHY91jQzGRiYPf", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "allowShortageShortagePercent", "modelAlias": "ERP_GEN$gen_mat_sls_md", "placeholder": "请输入"}, "displayComponentType": "Number", "label": "允许缺发比例", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "Dr9CI8k0WtD66Iv0vlUvJ", "valueRules": null}], "name": "allowShortageShortagePercent", "type": "NUMBER", "width": 120}, "type": "Widget"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-Gt9Cxs5AmfIB8qS_S_gHs", "name": "Fields", "props": {}, "type": "Meta"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-lLVJtb0r_ii4i5_xUoiW2", "name": "Table", "props": {"$subTable": {"fields": [{"align": "left", "componentProps": {"columns": ["counCode", "coun<PERSON><PERSON>", "languageId", "currId", "timezoneId", "defaultRelv"], "fieldAlias": "counId", "label": "选择国家", "labelField": "coun<PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_coun_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_sls_tax_class_link_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "displayComponentType": "RelationShow", "label": "国家", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "gBeUyhbE6UA_zNAeoI2OT", "valueRules": null}], "name": "counId", "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["taxType", "taxClassCode", "taxClassDesc", "counId"], "fieldAlias": "matTaxTypeId", "label": "选择物料税分类", "labelField": "taxClassCode", "modelAlias": "ERP_GEN$gen_mat_tax_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_sls_tax_class_link_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "displayComponentType": "RelationShow", "label": "物料税分类", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "pvOLDqknwXbTzzcarKcOk", "valueRules": null}], "name": "matTaxTypeId", "type": "OBJECT", "width": 120}, {"align": "left", "componentProps": {"fieldAlias": "taxClassDesc", "modelAlias": "ERP_GEN$gen_mat_tax_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "label": "分类描述", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "rv8K4NZwW9GT4SLdHl7mo", "valueRules": null}], "name": "matTaxTypeId.taxClassDesc", "type": "TEXT", "width": 120}], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_sls_tax_class_link_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_sls_tax_class_link_cf"}}], "serviceKey": "ERP_SCM$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "ERP_GEN$gen_mat_sls_tax_class_link_cf", "showConfigure": false, "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "enableSolution": false, "filterFields": [{"componentProps": {"columns": ["orgCode", "orgName", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "orgSort", "orgBusinessTypeCode", "orgBusinessTypeId", "orgDimensionId", "orgParentId"], "fieldAlias": "slsOrgId", "label": "选择销售组织", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_GEN$gen_mat_sls_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": false, "label": "销售组织", "name": "slsOrgId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["orgCode", "orgName", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "orgSort", "orgBusinessTypeCode", "orgBusinessTypeId", "orgDimensionId", "orgParentId"], "fieldAlias": "slsDcId", "label": "选择销售渠道", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_GEN$gen_mat_sls_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": false, "label": "销售渠道", "name": "slsDcId", "required": true, "type": "OBJECT", "width": 120}], "flow": {"containerKey": "ERP_SCM$GEN_MAT_NEW_VIEW-lLVJtb0r_ii4i5_xUoiW2", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_sls_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_sls_md"}}], "serviceKey": "ERP_SCM$SYS_PagingDataService", "type": "InvokeSystemService"}, "isProFilter": false, "label": "表格", "mode": "simple", "modelAlias": "ERP_GEN$gen_mat_sls_md", "showConfigure": false, "showFilterFields": true, "showScope": "filter", "subTableEnabled": true, "tableCondition": {"conditions": [{"conditions": [{"id": "TmazHuQsWg63ovCpJ4DgX", "leftValue": {"fieldType": "Number", "title": "gen_mat_md_id.ID", "type": "VarValue", "val": "genMatMdId.id", "value": "ERP_GEN$gen_mat_sls_md.genMatMdId.id", "valueType": "VAR", "varVal": "genMatMdId.id", "varValue": [{"valueKey": "genMatMdId", "valueName": "genMatMdId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"exprValue": "route.recordId", "fieldType": "Number", "type": "ExprValue", "value": "route.recordId", "valueType": "ExprValue", "varVal": "route.recordId"}, "type": "ConditionLeaf"}], "id": "-bQGuSfu6Gf3VS8tkGO81", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "ipekk-lyI5-h9BUH_Y_iC", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": null}, "type": "Container"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-ZmQMcH9hkiEMJd2IuWOVB", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-79aCS7rDs58Bke1YwjEFv", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-GOo1yBtRosvh3sJMMYywv", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-EOSurKIKge_4j0CJ71MAq", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-M_OJYyZtQ_cdO6rLSzzZE", "name": "Field", "props": {"componentProps": {"columns": ["orgCode", "orgName", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "orgSort", "orgBusinessTypeCode", "orgBusinessTypeId", "orgDimensionId", "orgParentId"], "fieldAlias": "purOrg", "label": "选择采购组织", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_GEN$gen_mat_pur_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": false, "label": "采购组织", "name": "purOrg", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-qerEQMNDQNrgD2tRJQnCD", "name": "Field", "props": {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "purUomId", "label": "选择采购单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_pur_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "label": "采购单位", "name": "purUomId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-TySPhgXHJB9ot4f5x0fu9", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "purPrice", "modelAlias": "ERP_GEN$gen_mat_pur_md", "placeholder": "请输入", "precision": 6}, "displayComponentProps": {"precision": 6}, "displayComponentType": "Number", "label": "采购价", "modelAlias": "ERP_GEN$gen_mat_pur_md", "name": "purPrice", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-DS--zeNjBCFV84oyaLZIm", "name": "Field", "props": {"align": "left", "componentProps": {"columns": ["currCode", "currName", "currIsoName", "decimalPlace", "symbol", "remark"], "fieldAlias": "currId", "label": "选择采购价币种", "labelField": "currName", "modelAlias": "ERP_GEN$gen_curr_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_pur_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "displayComponentProps": {"labelField": ["currName"], "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "displayComponentType": "RelationShow", "label": "采购价币种", "name": "currId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-ApZoo-OhQUT_ByBW91mvl", "name": "Field", "props": {"componentProps": {"defaultValue": false, "fieldAlias": "isPurSrcList", "modelAlias": "ERP_GEN$gen_mat_pur_md", "placeholder": "请选择"}, "hidden": false, "initialValue": false, "label": "是否开启货源清单", "name": "isPurSrcList", "required": false, "type": "BOOL", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-hGnfcd0lo3Ie1ukNINJgt", "name": "Field", "props": {"align": "left", "componentProps": {"columns": ["counId", "taxCode", "taxcate", "tax", "associatedTaxCode"], "fieldAlias": "taxId", "label": "选择税", "labelField": "taxCode", "modelAlias": "ERP_GEN$gen_tax_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_pur_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "displayComponentProps": {"labelField": ["taxCode"], "modelAlias": "ERP_GEN$gen_tax_type_cf"}, "displayComponentType": "RelationShow", "label": "默认税码", "name": "taxId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-DxiQG5wunxKpN5wjtrh0M", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "tax", "modelAlias": "ERP_GEN$gen_tax_type_cf", "placeholder": "请输入", "precision": 6}, "displayComponentProps": {"precision": 6}, "displayComponentType": "Number", "label": "默认税率", "name": "taxId.tax", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-cSJn_1Qvs2-OasGMvmh5A", "name": "Fields", "props": {}, "type": "Meta"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-YtesVUhKBEs0Bx52Ncrya", "name": "Table", "props": {"filterFields": [{"componentProps": {"columns": ["orgCode", "orgName", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "orgSort", "orgBusinessTypeCode", "orgBusinessTypeId", "orgDimensionId", "orgParentId"], "fieldAlias": "purOrg", "label": "选择采购组织", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_GEN$gen_mat_pur_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": false, "label": "采购组织", "name": "purOrg", "required": false, "type": "OBJECT", "width": 120}], "flow": {"containerKey": "ERP_SCM$GEN_MAT_NEW_VIEW-YtesVUhKBEs0Bx52Ncrya", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_pur_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_pur_md"}}], "serviceKey": "ERP_SCM$SYS_PagingDataService", "type": "InvokeSystemService"}, "isProFilter": false, "label": "表格", "mode": "simple", "modelAlias": "ERP_GEN$gen_mat_pur_md", "showConfigure": false, "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "NFDhqM0Qa7M866ubjb5D6", "leftValue": {"fieldType": "Number", "title": "物料主数据.ID", "type": "VarValue", "val": "genMatMdId.id", "value": "ERP_GEN$gen_mat_pur_md.genMatMdId.id", "valueType": "VAR", "varVal": "genMatMdId.id", "varValue": [{"valueKey": "genMatMdId", "valueName": "genMatMdId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"exprValue": "route.recordId", "fieldType": "Number", "type": "ExprValue", "value": "route.recordId", "valueType": "ExprValue", "varVal": "route.recordId"}, "type": "ConditionLeaf"}], "id": "-hzn38n9KnI7CqkK4XRg3", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "Ggg_vUixft2kPxNMqjt_O", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": null}, "type": "Container"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-mO-283pAdqHYjCmS8k5zP", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-9PfVMQh2O2o6oyrdTtmd7", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-Ft0SQOZKInAE7dda0x-1J", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-AMTCAjoY7xmwJ6VhyAjM4", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-JueUKFIqJIgCUXcyi5E4d", "name": "Field", "props": {"componentProps": {"columns": ["orgCode", "orgName", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "orgSort", "orgBusinessTypeCode", "orgBusinessTypeId", "orgDimensionId", "orgParentId"], "fieldAlias": "invOrgId", "label": "选择工厂（库存组织）", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_GEN$gen_mat_prd_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": false, "label": "工厂（库存组织）", "name": "invOrgId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-usCpR06TB602XG3mOBGms", "name": "Field", "props": {"componentProps": {"columns": ["matStatusCode", "matStatusName", "eventId", "msgType"], "fieldAlias": "factoryStatusId", "label": "选择工厂状态", "labelField": "matStatusCode", "modelAlias": "ERP_GEN$gen_mat_status_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_prd_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "label": "工厂状态", "name": "factoryStatusId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-j8hM7DgM4QHimBiJtgJsk", "name": "Field", "props": {"componentProps": {"columns": ["orgCode", "orgName", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "orgSort", "orgBusinessTypeCode", "orgBusinessTypeId", "orgDimensionId", "orgParentId"], "fieldAlias": "invLocId", "label": "选择生产存储库存地点", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_GEN$gen_mat_prd_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": false, "label": "生产存储库存地点", "name": "invLocId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-DbMsw96R7Wt3Vlo5Lo21_", "name": "Field", "props": {"componentProps": {"fieldAlias": "productionCycle", "modelAlias": "ERP_GEN$gen_mat_prd_md", "placeholder": "请输入"}, "hidden": false, "label": "生产周期（天）", "name": "productionCycle", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-1v_cxK0iS4TzXiXjJLOej", "name": "Field", "props": {"componentProps": {"fieldAlias": "inspectionCycle", "modelAlias": "ERP_GEN$gen_mat_prd_md", "placeholder": "请输入"}, "hidden": false, "label": "质检周期（天）", "name": "inspectionCycle", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-5-b2RuZJ2wviXUAkWF41c", "name": "Field", "props": {"componentProps": {"columns": ["wcHeadCode", "wcHeadName", "defaultRestDate", "startDate", "finishDate", "enableStatus"], "fieldAlias": "plannedCalendarId", "label": "选择计划日历", "labelField": "wcHeadCode", "modelAlias": "ERP_GEN$gen_wc_head_cf", "parentModelAlias": "ERP_GEN$gen_mat_prd_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "label": "计划日历", "name": "plannedCalendarId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-fE8jhA0xSyWIjgHIFgBvB", "name": "Field", "props": {"componentProps": {"fieldAlias": "componentScrap", "modelAlias": "ERP_GEN$gen_mat_prd_md", "placeholder": "请输入", "precision": 3}, "hidden": false, "label": "组件报废率%", "name": "componentScrap", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-8ranFJKcnCSfQutr9-S0N", "name": "Field", "props": {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "prdUomId", "label": "选择生产计量单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_prd_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "label": "生产计量单位", "name": "prdUomId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-fe4SUbPb2APmtqfvCQfTi", "name": "Field", "props": {"componentProps": {"defaultValue": true, "fieldAlias": "postInspection", "modelAlias": "ERP_GEN$gen_mat_prd_md", "placeholder": "请选择"}, "hidden": false, "initialValue": true, "label": "过账到检验库存", "name": "postInspection", "required": false, "type": "BOOL", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-9U58pgvM2yGWbnQI-muTj", "name": "Field", "props": {"componentProps": {"fieldAlias": "insufficientDeliveryTolerance", "modelAlias": "ERP_GEN$gen_mat_prd_md", "placeholder": "请输入", "precision": 4}, "hidden": false, "label": "不足交货容差", "name": "insufficientDeliveryTolerance", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-_q_RhkIWJOO4pQ0VJi8ZO", "name": "Field", "props": {"componentProps": {"fieldAlias": "excessiveDeliveryTolerance", "modelAlias": "ERP_GEN$gen_mat_prd_md", "placeholder": "请输入", "precision": 4}, "hidden": false, "label": "过度交货容差", "name": "excessiveDeliveryTolerance", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-zSLFX4Ab5Dn5qIBT2Xs2l", "name": "Field", "props": {"componentProps": {"fieldAlias": "unlimitedOverDelivery", "modelAlias": "ERP_GEN$gen_mat_prd_md", "placeholder": "请选择"}, "hidden": false, "label": "未限制的过度交货", "name": "unlimitedOverDelivery", "required": false, "type": "BOOL", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-O1jM_lnynwE0-wNWNLuvd", "name": "Field", "props": {"componentProps": {"fieldAlias": "procurementType", "modelAlias": "ERP_GEN$gen_mat_prd_md", "placeholder": "请选择"}, "hidden": false, "label": "采购类型", "name": "procurementType", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-U28PuU81yk2Drto0hujQq", "name": "Field", "props": {"componentProps": {"fieldAlias": "backFlush", "modelAlias": "ERP_GEN$gen_mat_prd_md", "placeholder": "请选择"}, "hidden": false, "label": "反冲标识", "name": "backFlush", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-jk9CbAjVvqF-g_ntG4oCl", "name": "Field", "props": {"componentProps": {"fieldAlias": "individualColl", "modelAlias": "ERP_GEN$gen_mat_prd_md", "placeholder": "请选择"}, "hidden": false, "label": "独立/集中", "name": "individualColl", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-bu9628FXnEy9RnWbzF-oK", "name": "Field", "props": {"componentProps": {"fieldAlias": "prdMangerId", "modelAlias": "ERP_GEN$gen_mat_prd_md", "placeholder": "请输入"}, "hidden": false, "label": "生产管理员", "name": "prdMangerId", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-rcXadzzGlt4J5OU5mloPv", "name": "Field", "props": {"componentProps": {"fieldAlias": "batchParaId", "modelAlias": "ERP_GEN$gen_mat_prd_md", "placeholder": "请输入"}, "hidden": false, "label": "批次参数文件", "name": "batchParaId", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-PbYi93oIoL6w0BATiE0JT", "name": "Field", "props": {"componentProps": {"fieldAlias": "serialParaId", "modelAlias": "ERP_GEN$gen_mat_prd_md", "placeholder": "请输入"}, "hidden": false, "label": "序列号参数文件", "name": "serialParaId", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-ldpKDIiSD5gPY2iXXX2Y1", "name": "Fields", "props": {}, "type": "Meta"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "name": "Table", "props": {"filterFields": [{"componentProps": {"columns": ["orgCode", "orgName", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "orgSort", "orgBusinessTypeCode", "orgBusinessTypeId", "orgDimensionId", "orgParentId"], "fieldAlias": "invOrgId", "label": "选择工厂（库存组织）", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_GEN$gen_mat_prd_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": false, "label": "工厂（库存组织）", "name": "invOrgId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["orgCode", "orgName", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "orgSort", "orgBusinessTypeCode", "orgBusinessTypeId", "orgDimensionId", "orgParentId"], "fieldAlias": "invLocId", "label": "选择生产存储库存地点", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_GEN$gen_mat_prd_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": false, "label": "生产存储库存地点", "name": "invLocId", "required": false, "type": "OBJECT", "width": 120}], "flow": {"containerKey": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_prd_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_prd_md"}}], "serviceKey": "ERP_SCM$SYS_PagingDataService", "type": "InvokeSystemService"}, "isProFilter": false, "label": "表格", "mode": "simple", "modelAlias": "ERP_GEN$gen_mat_prd_md", "showConfigure": false, "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "-LzXgNS7x7xXMZgBPC9zK", "leftValue": {"fieldType": "Number", "title": "genMatMdId.ID", "type": "VarValue", "val": "genMatMdId.id", "value": "ERP_GEN$gen_mat_prd_md.genMatMdId.id", "valueType": "VAR", "varVal": "genMatMdId.id", "varValue": [{"valueKey": "genMatMdId", "valueName": "genMatMdId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"exprValue": "route.recordId", "fieldType": "Number", "type": "ExprValue", "value": "route.recordId", "valueType": "ExprValue", "varVal": "route.recordId"}, "type": "ConditionLeaf"}], "id": "yNVfPcADWU95x9QG6xh0I", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "Eim6jL3qCaopYv0c7qqTH", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": null}, "type": "Container"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-G_TJbV71KzYVlc5w4d28r", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW--S1SStAdOxkS15lyMPceJ", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-0jNRsJhTw6dwPCsixITPm", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-xwLWfC2R7tmR9u1RVKt5U", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-GqoJae4H2YBSfchWAZ_jZ", "name": "Field", "props": {"componentProps": {"columns": ["orgCode", "orgName", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "orgSort", "orgBusinessTypeCode", "orgBusinessTypeId", "orgDimensionId", "orgParentId"], "fieldAlias": "invOrgId", "label": "选择库存组织", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_GEN$gen_mat_fin_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "label": "库存组织", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "ko5bpr3b2gocAdUnWsAM8", "valueRules": null}], "name": "invOrgId", "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-sQlYZ-4asxEppNMsdcOtP", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "mvmAvgPrc", "modelAlias": "ERP_GEN$gen_mat_fin_md", "placeholder": "请输入"}, "displayComponentProps": {"precision": 6}, "displayComponentType": "Number", "label": "移动平均成本价", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "ZcY6Zke-nSks1t4Xd4GQp", "valueRules": null}], "name": "mvmAvgPrc", "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-Jk9QVOczrH4pUr9zZ_oCD", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "stdAvgPrc", "modelAlias": "ERP_GEN$gen_mat_fin_md", "placeholder": "请输入"}, "displayComponentProps": {"precision": 6}, "displayComponentType": "Number", "label": "标准成本价", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "I1ywYc5x22b7Z_K5Sa7_k", "valueRules": null}], "name": "stdAvgPrc", "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-AEc27Qj3DZgKjxgP4n7_5", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "prcMethod", "modelAlias": "ERP_GEN$gen_mat_fin_md", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "label": "计价方式", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "0qdIRfgXif2a-98FROvrj", "valueRules": null}], "name": "prcMethod", "type": "SELECT", "width": 120}, "type": "Widget"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-78QTtlOQ2FAUQgEsnf6vR", "name": "Fields", "props": {}, "type": "Meta"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-8TWGUVjVdzAkatD_C9vVM", "name": "Table", "props": {"filterFields": [{"componentProps": {"columns": ["orgCode", "orgName", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "orgSort", "orgBusinessTypeCode", "orgBusinessTypeId", "orgDimensionId", "orgParentId"], "fieldAlias": "invOrgId", "label": "选择库存组织", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_GEN$gen_mat_fin_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "editComponentProps": {"fields": [{"align": "left", "componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "isRelationColumn": true, "label": "组织编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "Rvg7A6LlQGVrvu2a6JX11", "valueRules": null}], "name": "orgCode", "type": "TEXT", "width": 120}, {"align": "left", "componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "displayComponentType": "Text", "isRelationColumn": true, "label": "组织名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "zm-FAKEhkKccm4vIFn-Jy", "valueRules": null}], "name": "orgName", "type": "TEXT", "width": 120}], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_struct_md"}}], "serviceKey": "ERP_SCM$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "orgName", "modalProps": {"size": "middle", "width": 720}, "mode": "single", "modelAlias": "sys_common$org_struct_md", "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "y1NdQoUeGbQi13bW9AbXT", "leftValue": {"fieldType": "Enum", "title": "状态", "type": "VarValue", "val": "orgStatus", "value": "sys_common$org_struct_md.orgStatus", "valueType": "VAR", "varVal": "orgStatus", "varValue": [{"valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "kyj9hi0QTTm2HYvsFhCKM", "leftValue": {"fieldType": "Text", "title": "业务类型编码", "type": "VarValue", "val": "orgBusinessTypeCode", "value": "sys_common$org_struct_md.orgBusinessTypeCode", "valueType": "VAR", "varVal": "orgBusinessTypeCode", "varValue": [{"valueKey": "orgBusinessTypeCode", "valueName": "orgBusinessTypeCode"}]}, "operator": "EQ", "rightValue": {"constValue": "INV_ORG", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "B2zkpBykJDSR2dGJHBJp_", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "qzRLuBqIfIyicwGMmeqhA", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "label": "库存组织", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "1ngGJ0GgQFtyXFairFSdL", "valueRules": null}], "name": "invOrgId", "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "prcMethod", "modelAlias": "ERP_GEN$gen_mat_fin_md", "placeholder": "请选择"}, "label": "计价方式", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "JBau1krYo2x5lq7NbQOXk", "valueRules": null}], "name": "prcMethod", "type": "SELECT", "width": 120}], "flow": {"containerKey": "ERP_SCM$GEN_MAT_NEW_VIEW-8TWGUVjVdzAkatD_C9vVM", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_fin_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_fin_md"}}], "serviceKey": "ERP_SCM$SYS_PagingDataService", "type": "InvokeSystemService"}, "isProFilter": false, "label": "表格", "mode": "simple", "modelAlias": "ERP_GEN$gen_mat_fin_md", "showConfigure": false, "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "hBTOuv0ffc_lpMhUXyWWG", "leftValue": {"fieldType": "Number", "title": "genMatMdId.ID", "type": "VarValue", "val": "genMatMdId.id", "value": "ERP_GEN$gen_mat_fin_md.genMatMdId.id", "valueType": "VAR", "varVal": "genMatMdId.id", "varValue": [{"valueKey": "genMatMdId", "valueName": "genMatMdId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"exprValue": "route.recordId", "fieldType": "Number", "type": "ExprValue", "value": "route.recordId", "valueType": "ExprValue", "varVal": "route.recordId"}, "type": "ConditionLeaf"}], "id": "-bXpnNa-hKdiv8wHM8d_i", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "EEw0sFqcO-v3qd_-LTUBp", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": null}, "type": "Container"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-MO70XPCOYwWxw9aaoipqU", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-8jH4p2FIA5gEujY7lqbIx", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-rvxiFHG2yCEM8uPl6BjzC", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-pBOaOUBm58KHtCDnBT8dc", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-00SSwNh0FBuK6xKFiIigJ", "name": "Field", "props": {"align": "left", "componentProps": {"columns": ["orgCode", "orgName", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "orgSort", "orgBusinessTypeCode", "orgBusinessTypeId", "orgDimensionId", "orgParentId"], "fieldAlias": "invOrgId", "label": "选择库存组织", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_GEN$gen_mat_inv_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "displayComponentProps": {"modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "hidden": false, "label": "库存组织", "modelAlias": "sys_common$org_struct_md", "name": "invOrgId", "required": true, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-UducxHYiwf2tUStmFEmlf", "name": "Field", "props": {"componentProps": {"columns": ["orgCode", "orgName", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "orgSort", "orgBusinessTypeCode", "orgBusinessTypeId", "orgDimensionId", "orgParentId"], "fieldAlias": "invLocId", "label": "选择库存地点", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_GEN$gen_mat_inv_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": false, "label": "库存地点", "name": "invLocId", "required": true, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-Jg_0zIsyk3RcKZEdtz6WY", "name": "Field", "props": {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "invUomId", "label": "选择存储单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_inv_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "label": "存储单位", "name": "invUomId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-OK5TNawyMmbcr4pMWO578", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "delayDeliveryTime", "modelAlias": "ERP_GEN$gen_mat_inv_md", "placeholder": "请输入"}, "displayComponentType": "Number", "label": "延迟发货处理时间(天)", "name": "delayDeliveryTime", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-7krR3c5B8ygI6NzMm8A8V", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "receivingTime", "modelAlias": "ERP_GEN$gen_mat_inv_md", "placeholder": "请输入"}, "displayComponentType": "Number", "label": "收货处理时间(天)", "name": "receivingTime", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-lT1l3BmrmgTPtYmho2Qvm", "name": "Fields", "props": {}, "type": "Meta"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-t30HUYGRrLVdXuqwde9WP", "name": "Table", "props": {"filterFields": [{"componentProps": {"columns": ["orgCode", "orgName", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "orgSort", "orgBusinessTypeCode", "orgBusinessTypeId", "orgDimensionId", "orgParentId"], "fieldAlias": "invOrgId", "label": "选择库存组织", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_GEN$gen_mat_inv_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "组织编码", "name": "orgCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "组织名称", "name": "orgName", "required": false, "type": "TEXT", "width": 120}], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_struct_md"}}], "serviceKey": "ERP_SCM$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "orgName", "modalProps": {"size": "middle", "width": 720}, "mode": "single", "modelAlias": "sys_common$org_struct_md", "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "dTgGyF0vZtQ8SUowj3Nj0", "leftValue": {"fieldType": "Text", "title": "业务类型编码", "type": "VarValue", "val": "orgBusinessTypeCode", "value": "sys_common$org_struct_md.orgBusinessTypeCode", "valueType": "VAR", "varVal": "orgBusinessTypeCode", "varValue": [{"valueKey": "orgBusinessTypeCode", "valueName": "orgBusinessTypeCode"}]}, "operator": "EQ", "rightValue": {"constValue": "INV_ORG", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "w5uy-U_zpM9-V5K07lQuy", "leftValue": {"fieldType": "Enum", "title": "状态", "type": "VarValue", "val": "orgStatus", "value": "sys_common$org_struct_md.orgStatus", "valueType": "VAR", "varVal": "orgStatus", "varValue": [{"valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "AEhzHSmYkXz6K7XdqTfMt", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "F0N643-GekkdIqvbG1T2b", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "库存组织", "name": "invOrgId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["orgCode", "orgName", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "orgSort", "orgBusinessTypeCode", "orgBusinessTypeId", "orgDimensionId", "orgParentId"], "fieldAlias": "invLocId", "label": "选择库存地点", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_GEN$gen_mat_inv_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "组织编码", "name": "orgCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "组织名称", "name": "orgName", "required": false, "type": "TEXT", "width": 120}], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "sys_common$org_struct_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_struct_md"}}], "serviceKey": "ERP_SCM$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "orgName", "modalProps": {"size": "middle", "width": 720}, "mode": "single", "modelAlias": "sys_common$org_struct_md", "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "XEpdLa56pYBXabjrfBP7M", "leftValue": {"fieldType": "Enum", "title": "状态", "type": "VarValue", "val": "orgStatus", "value": "sys_common$org_struct_md.orgStatus", "valueType": "VAR", "varVal": "orgStatus", "varValue": [{"valueKey": "orgStatus", "valueName": "orgStatus"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}, {"id": "M-rjF7BUurxP5_bi2q7rw", "leftValue": {"fieldType": "Text", "title": "业务类型编码", "type": "VarValue", "val": "orgBusinessTypeCode", "value": "sys_common$org_struct_md.orgBusinessTypeCode", "valueType": "VAR", "varVal": "orgBusinessTypeCode", "varValue": [{"valueKey": "orgBusinessTypeCode", "valueName": "orgBusinessTypeCode"}]}, "operator": "EQ", "rightValue": {"constValue": "INV_LOC", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "vXwAIoBGYF6WwPualCHhP", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "jNhkuO9cpx_DUtKv5wVmZ", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "库存地点", "name": "invLocId", "required": true, "type": "OBJECT", "width": 120}], "flow": {"containerKey": "ERP_SCM$GEN_MAT_NEW_VIEW-t30HUYGRrLVdXuqwde9WP", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_inv_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_inv_md"}}], "serviceKey": "ERP_SCM$SYS_PagingDataService", "type": "InvokeSystemService"}, "isProFilter": false, "label": "表格", "mode": "simple", "modelAlias": "ERP_GEN$gen_mat_inv_md", "showConfigure": false, "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "U9nU0qhPL3tOyweW1RPeS", "leftValue": {"fieldType": "Number", "title": "物理主数据.ID", "type": "VarValue", "val": "genMatMdId.id", "value": "ERP_GEN$gen_mat_inv_md.genMatMdId.id", "valueType": "VAR", "varVal": "genMatMdId.id", "varValue": [{"valueKey": "genMatMdId", "valueName": "genMatMdId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"exprValue": "route.recordId", "fieldType": "Number", "type": "ExprValue", "value": "route.recordId", "valueType": "ExprValue", "varVal": "route.recordId"}, "type": "ConditionLeaf"}], "id": "Uj6LfJ1sKOnyEvwYp8VUC", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "0JrE-8bIOtTjq6xv8kFe9", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": null}, "type": "Container"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-AhxUCQwd3-Iz6gcvRKQoP", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-RrOonE_uSfsQC26OYt1WC", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-NyP-N50kEgP3dtCee9pgJ", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-Hoqa79zUGOLJ13UbiRc-j", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-_qeGSoC-cOvqOgZLvh4eS", "name": "Field", "props": {"componentProps": {"fieldAlias": "reorderQty", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "hidden": false, "label": "再订货点", "name": "reorderQty", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-r6AkFqcyJJEs-E1RJn0Aj", "name": "Field", "props": {"componentProps": {"fieldAlias": "planningCycle", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "hidden": false, "label": "计划周期", "name": "planningCycle", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-N5xTYwCTLjC_v1EmMZF41", "name": "Field", "props": {"componentProps": {"fieldAlias": "plannedDelivTime", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "hidden": false, "label": "计划交货天数", "name": "plannedDelivTime", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-qVCEcYohtVNaKRnpT36Lu", "name": "Field", "props": {"componentProps": {"fieldAlias": "inhouseProdutionTime", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "hidden": false, "label": "自制时间", "name": "inhouseProdutionTime", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-UEXeXMDMaWs7CsCDFfh6I", "name": "Fields", "props": {}, "type": "Meta"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-KSYC3Wr5zoygttu713DyA", "name": "Table", "props": {"filterFields": [{"componentProps": {"fieldAlias": "mrpAreaId", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "mrpAreaId", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "isRelationColumn": true, "label": "MRP区域", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "QFXmXG1cGdOycFeuoFfGW", "valueRules": null}], "name": "mrpAreaId", "type": "NUMBER", "width": 120}], "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_mrp_md"}}], "serviceKey": "ERP_SCM$SYS_PagingDataService", "type": "InvokeSystemService"}, "labelField": "mrpAreaId", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_GEN$gen_mat_mrp_md", "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "MRP区域", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "STeaCYsiNK0V4L1YvIita", "valueRules": null}], "name": "mrpAreaId", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "planningGroupCfId", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "placeholder": "请输入"}, "hidden": false, "label": "MRP计划组", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "Iw0TpxkGn2_ajSIUD7Okp", "valueRules": null}], "name": "planningGroupCfId", "required": false, "type": "NUMBER", "width": 120}], "flow": {"containerKey": "ERP_SCM$GEN_MAT_NEW_VIEW-KSYC3Wr5zoygttu713DyA", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_mrp_md"}}], "serviceKey": "ERP_SCM$SYS_PagingDataService", "type": "InvokeSystemService"}, "isProFilter": false, "label": "表格", "mode": "simple", "modelAlias": "ERP_GEN$gen_mat_mrp_md", "showConfigure": false, "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "RTsPhwOZ4PeoD_JAC2fOy", "leftValue": {"fieldType": "Number", "title": "gen_mat_md_id.ID", "type": "VarValue", "val": "genMatMdId.id", "value": "ERP_GEN$gen_mat_mrp_md.genMatMdId.id", "valueType": "VAR", "varVal": "genMatMdId.id", "varValue": [{"valueKey": "genMatMdId", "valueName": "genMatMdId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"exprValue": "route.recordId", "fieldType": "Number", "type": "ExprValue", "value": "route.recordId", "valueType": "ExprValue", "varVal": "route.recordId"}, "type": "ConditionLeaf"}], "id": "Ue_pdeS13G3uNkMPSlunx", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "B6BoQ0QNv3CkHxO84I3eY", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": null}, "type": "Container"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-KMfJWGPEsLt7EYeLB3i8r", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-Ug-CiQQgpawVOf03H0qV7", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-KEqknxRYau7aSF-QV1cjb", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-oayCSDhwYeAaJCQro3pBN", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-BfimaBtgDxQS9ZJZ2gXlS", "name": "Field", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-1Hg4oPeD37jLeJIiSjeqW", "name": "Field", "props": {"componentProps": {"fieldAlias": "baseUnitFactor", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "基本单位系数", "name": "baseUnitFactor", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-hFVn4T4tfHgJODuPHnR1g", "name": "Field", "props": {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "unitId", "label": "选择基础单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "label": "基础单位", "name": "unitId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-A7aKGEQ1BXADrFY1c76r3", "name": "Field", "props": {"componentProps": {"defaultValue": "=", "fieldAlias": "symbol", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请输入"}, "hidden": false, "initialValue": "=", "label": "运算符号", "name": "symbol", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-e25XbHu7w975G2Yme3o1z", "name": "Field", "props": {"componentProps": {"fieldAlias": "targetUnitFactor", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "目标单位系数", "name": "targetUnitFactor", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-n-eoCCEKEt7PwBLVC0FjV", "name": "Field", "props": {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "targetUnitId", "label": "选择目标单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "label": "目标单位", "name": "targetUnitId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-dj1Fky24w6yCp5i5fUSG_", "name": "Fields", "props": {}, "type": "Meta"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-7L2mAxHLXcSgSDM-6WkQt", "name": "Table", "props": {"filterFields": [{"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "unitId", "label": "选择基础单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_uom_formula_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": false, "label": "基础单位", "name": "unitId", "required": false, "type": "OBJECT", "width": 120}], "flow": {"containerKey": "ERP_SCM$GEN_MAT_NEW_VIEW-7L2mAxHLXcSgSDM-6WkQt", "context$": "$context", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_uom_formula_type_cf"}}], "serviceKey": "ERP_SCM$SYS_PagingDataService", "type": "InvokeSystemService"}, "isProFilter": false, "label": "表格", "mode": "simple", "modelAlias": "ERP_GEN$gen_uom_formula_type_cf", "showConfigure": false, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "Ef0sQ7vbvgH0_V8ovUDD6", "leftValue": {"fieldType": "Number", "title": "genMatMdId.ID", "type": "VarValue", "val": "genMatMdId.id", "value": "ERP_GEN$gen_uom_formula_type_cf.genMatMdId.id", "valueType": "VAR", "varVal": "genMatMdId.id", "varValue": [{"valueKey": "genMatMdId", "valueName": "genMatMdId"}, {"valueKey": "id", "valueName": "id"}]}, "operator": "EQ", "rightValue": {"exprValue": "route.recordId", "fieldType": "Number", "type": "ExprValue", "value": "route.recordId", "valueType": "ExprValue", "varVal": "route.recordId"}, "type": "ConditionLeaf"}], "id": "qlDNge-tjz2q2uB3BoIuG", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "z5g6025M9qCJz4rc6wV_Z", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": null}, "type": "Container"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW--5NK9W6TeWMg2pHt9VUi5", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "name": "Tabs", "props": {"items": [{"key": "ZlmGus751W6Jyx4EiFfAs", "label": "基本信息"}, {"key": "aPi9tBKBYOmBdAxwzeFj2", "label": "销售视图"}, {"key": "iXwVxjZ8Io_tprrnpoNGl", "label": "采购视图"}, {"key": "1iHXRYJ6-gw5zm0x7FLkC", "label": "生产视图"}, {"key": "uLLZoi7S0ai1f8JUDwqrS", "label": "财务视图"}, {"key": "kWUAB3NPzEKgKU2sMNkIb", "label": "存储视图"}, {"key": "Hlbw8OTxPkzE9i3P4tiDL", "label": "MRP视图"}, {"key": "txrVeS_z8GBcki2ij_QbS", "label": "单位转换"}], "lookup": []}, "type": "Layout"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "name": "DetailGroupItem", "props": {"defaultCollapsed": false, "title": ""}, "type": "Layout"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "name": "Detail", "props": {"flow": {"context$": "$context", "outputParams": {"expression": "data", "serviceKey": "ERP_GEN$GEN_QUERY_MAT_DETAIL", "type": "expression"}, "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "required": null, "valueConfig": {"expression": "route.recordId", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "required": null}], "serviceKey": "ERP_GEN$GEN_QUERY_MAT_DETAIL", "type": "InvokeService"}, "layout": "horizontal", "modelAlias": "ERP_GEN$gen_mat_md", "saveType": "service"}, "type": "Container"}], "key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "name": "Page", "props": {"showBack": true, "showFooter": false, "showHeader": true}, "type": "Container"}, "frontendConfig": {"modules": ["base", "terp"]}, "key": "ERP_SCM$GEN_MAT_NEW_VIEW:NAelHwsbofCYIspS3L-As", "resources": [{"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$GEN_QUERY_MAT_DETAIL", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-tPD2g6BgnFrobKMCxqQV4", "label": "启用", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-22OrU45hCIuICtzPHroVr", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}], "relations": [{"key": "ERP_GEN$SYS_MasterData_EnableDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-yK0GVPCzhR6d1fMt0mjRN", "label": "停用", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-22OrU45hCIuICtzPHroVr", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}], "relations": [{"key": "ERP_GEN$SYS_MasterData_DisableDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-38qrQym2s4fwo3G9YzMYZ", "label": "编辑", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-22OrU45hCIuICtzPHroVr", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-6fzvYqQzpqpgrEW4mQnuq/MaterialIndicatorService", "label": "组件调用服务", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ZSyniY6xBvdFjUdTyNEmx", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-GgLgT87qTxgCYwUZgeX1Z", "label": "分栏", "type": "Grid"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-hZgUBW3hcGVTL0ZS0Evee", "label": "分栏项", "type": "GridItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-6fzvYqQzpqpgrEW4mQnuq", "label": "物料指标卡", "type": "MaterialIndicatorCard"}], "relations": [{"key": "/api/trantor2default/preference/INDICATOR", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "/api/trantor2default/preference/INDICATOR/save", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-lLVJtb0r_ii4i5_xUoiW2", "label": "表格", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ZmQMcH9hkiEMJd2IuWOVB", "label": "页签项", "type": "TabItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_coun_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_coun_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_tax_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_tax_type_cf"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_sls_tax_class_link_cf"}, "type": "SystemService"}, {"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_sls_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-YtesVUhKBEs0Bx52Ncrya", "label": "表格", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-mO-283pAdqHYjCmS8k5zP", "label": "页签项", "type": "TabItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_pur_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "label": "表格", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-G_TJbV71KzYVlc5w4d28r", "label": "页签项", "type": "TabItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_prd_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-8TWGUVjVdzAkatD_C9vVM", "label": "表格", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-MO70XPCOYwWxw9aaoipqU", "label": "页签项", "type": "TabItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_fin_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-t30HUYGRrLVdXuqwde9WP", "label": "表格", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-AhxUCQwd3-Iz6gcvRKQoP", "label": "页签项", "type": "TabItem"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_inv_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-KSYC3Wr5zoygttu713DyA", "label": "表格", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-KMfJWGPEsLt7EYeLB3i8r", "label": "页签项", "type": "TabItem"}], "relations": [{"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_mrp_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-7L2mAxHLXcSgSDM-6WkQt", "label": "表格", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW--5NK9W6TeWMg2pHt9VUi5", "label": "页签项", "type": "TabItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_formula_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-Z6FwBc2iFfyRygED_yAAO", "label": "物料图片", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-bV5wwTusBfIohRwQmfcbL", "label": "", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-M4LUQqgJ4F6mF285xTf67", "label": "物料编码", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-bV5wwTusBfIohRwQmfcbL", "label": "", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-nas76IR91qqlNQxE08cPM", "label": "物料名称", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-bV5wwTusBfIohRwQmfcbL", "label": "", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-EoKo-RPOUHOZJdh9W_JMz", "label": "类目", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-bV5wwTusBfIohRwQmfcbL", "label": "", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_cate_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-vOV0tA4T2KLNXSJ4CthxM", "label": "物料类型", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-bV5wwTusBfIohRwQmfcbL", "label": "", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-J5f0D-H3YlxNq-BTfb_M2", "label": "基本计量单位", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-bV5wwTusBfIohRwQmfcbL", "label": "", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-Olc9_HswP1XywfPSaUT_s", "label": "外部物料编码", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-bV5wwTusBfIohRwQmfcbL", "label": "", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-hpc2exYTmW-ed-yXMI3SW", "label": "品牌", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-bV5wwTusBfIohRwQmfcbL", "label": "", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_brand_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_brand_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-j_LqfQdYdXI8FpRoZxqRB", "label": "ID", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-bV5wwTusBfIohRwQmfcbL", "label": "", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-0PVtJeR5LQpZDyvTY1Y2p", "label": "重量单位", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ocOc0Or6d93CDGwbVj0NR", "label": "定价相关", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-awGZb0sWnBTWca68DDXND", "label": "毛重", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ocOc0Or6d93CDGwbVj0NR", "label": "定价相关", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-wE9kuyjJh5jkS12mneBMV", "label": "净重", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ocOc0Or6d93CDGwbVj0NR", "label": "定价相关", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-l3ama0exZ08rl9gj1ADGl", "label": "体积单位", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ocOc0Or6d93CDGwbVj0NR", "label": "定价相关", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-ANvTmtnC_m7iWkMWy4Dom", "label": "体积", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ocOc0Or6d93CDGwbVj0NR", "label": "定价相关", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-yDCCP7mpENoiBBb3TiDOk", "label": "是否启用批次管理", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-Dl73seMFOiBVLFdnRMTdl", "label": "批次相关", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-ExnWVNc-WYxaMNgOzILyz", "label": "是否必须匹配批次确定", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-Dl73seMFOiBVLFdnRMTdl", "label": "批次相关", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-87DvdrDLijCrcRfG30A4S", "label": "物料分类类别", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-Dl73seMFOiBVLFdnRMTdl", "label": "批次相关", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_chara_class_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_chara_class_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-R2HGVzbuf6HNTEzt6y5eW", "label": "特征数据", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-Dl73seMFOiBVLFdnRMTdl", "label": "批次相关", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-BpFoRvLsM8MCgVnNYv1_e", "label": "物料保质期", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-SR2xCIfq3fSpjmtoMAY6I", "label": "其他", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-5_zMDevQIf39GSaXTaBOW", "label": "是否支持自动生成盘点方案", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-SR2xCIfq3fSpjmtoMAY6I", "label": "其他", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-b3Ye9xVwp_unrRKVFfcwa", "label": "ATP检查组", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-SR2xCIfq3fSpjmtoMAY6I", "label": "其他", "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_atp_group_md"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_atp_group_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-32Z55Z0CaNeO-HGa01QQ8", "label": "状态", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-0-RwXVvl5PRFzbUvnQk6u", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-SR2xCIfq3fSpjmtoMAY6I", "label": "其他", "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-2ImtoMf3jINDgdZ7b5xCr", "label": "销售组织", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ZmQMcH9hkiEMJd2IuWOVB", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-lLVJtb0r_ii4i5_xUoiW2", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-Gt9Cxs5AmfIB8qS_S_gHs", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-sD2zspTQO4o24pPEbKyzz", "label": "销售渠道", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ZmQMcH9hkiEMJd2IuWOVB", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-lLVJtb0r_ii4i5_xUoiW2", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-Gt9Cxs5AmfIB8qS_S_gHs", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-muYkytSjUKoXYX05OnoTq", "label": "销售单位", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ZmQMcH9hkiEMJd2IuWOVB", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-lLVJtb0r_ii4i5_xUoiW2", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-Gt9Cxs5AmfIB8qS_S_gHs", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-sFq0qlH87gB6edXZ4_A9O", "label": "基准价格", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ZmQMcH9hkiEMJd2IuWOVB", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-lLVJtb0r_ii4i5_xUoiW2", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-Gt9Cxs5AmfIB8qS_S_gHs", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-440ENzXSPRSpnc0DFt9-_", "label": "币种", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ZmQMcH9hkiEMJd2IuWOVB", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-lLVJtb0r_ii4i5_xUoiW2", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-Gt9Cxs5AmfIB8qS_S_gHs", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-z8fgDxXS4r_8sg6ZRnwkO", "label": "订单行项目类型组", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ZmQMcH9hkiEMJd2IuWOVB", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-lLVJtb0r_ii4i5_xUoiW2", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-Gt9Cxs5AmfIB8qS_S_gHs", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_sls_so_item_type_group_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_sls_so_item_type_group_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-vKOMnyrMBOm_NeBv2-3m4", "label": "允许超发比例", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ZmQMcH9hkiEMJd2IuWOVB", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-lLVJtb0r_ii4i5_xUoiW2", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-Gt9Cxs5AmfIB8qS_S_gHs", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-EBzkGCjxHY91jQzGRiYPf", "label": "允许缺发比例", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ZmQMcH9hkiEMJd2IuWOVB", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-lLVJtb0r_ii4i5_xUoiW2", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-Gt9Cxs5AmfIB8qS_S_gHs", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-M_OJYyZtQ_cdO6rLSzzZE", "label": "采购组织", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-mO-283pAdqHYjCmS8k5zP", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-YtesVUhKBEs0Bx52Ncrya", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-cSJn_1Qvs2-OasGMvmh5A", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-qerEQMNDQNrgD2tRJQnCD", "label": "采购单位", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-mO-283pAdqHYjCmS8k5zP", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-YtesVUhKBEs0Bx52Ncrya", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-cSJn_1Qvs2-OasGMvmh5A", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-TySPhgXHJB9ot4f5x0fu9", "label": "采购价", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-mO-283pAdqHYjCmS8k5zP", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-YtesVUhKBEs0Bx52Ncrya", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-cSJn_1Qvs2-OasGMvmh5A", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-DS--zeNjBCFV84oyaLZIm", "label": "采购价币种", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-mO-283pAdqHYjCmS8k5zP", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-YtesVUhKBEs0Bx52Ncrya", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-cSJn_1Qvs2-OasGMvmh5A", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_curr_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-ApZoo-OhQUT_ByBW91mvl", "label": "是否开启货源清单", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-mO-283pAdqHYjCmS8k5zP", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-YtesVUhKBEs0Bx52Ncrya", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-cSJn_1Qvs2-OasGMvmh5A", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-hGnfcd0lo3Ie1ukNINJgt", "label": "默认税码", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-mO-283pAdqHYjCmS8k5zP", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-YtesVUhKBEs0Bx52Ncrya", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-cSJn_1Qvs2-OasGMvmh5A", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_tax_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_tax_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-DxiQG5wunxKpN5wjtrh0M", "label": "默认税率", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-mO-283pAdqHYjCmS8k5zP", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-YtesVUhKBEs0Bx52Ncrya", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-cSJn_1Qvs2-OasGMvmh5A", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-JueUKFIqJIgCUXcyi5E4d", "label": "工厂（库存组织）", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-G_TJbV71KzYVlc5w4d28r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ldpKDIiSD5gPY2iXXX2Y1", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-usCpR06TB602XG3mOBGms", "label": "工厂状态", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-G_TJbV71KzYVlc5w4d28r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ldpKDIiSD5gPY2iXXX2Y1", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_status_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_status_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-j8hM7DgM4QHimBiJtgJsk", "label": "生产存储库存地点", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-G_TJbV71KzYVlc5w4d28r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ldpKDIiSD5gPY2iXXX2Y1", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-DbMsw96R7Wt3Vlo5Lo21_", "label": "生产周期（天）", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-G_TJbV71KzYVlc5w4d28r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ldpKDIiSD5gPY2iXXX2Y1", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-1v_cxK0iS4TzXiXjJLOej", "label": "质检周期（天）", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-G_TJbV71KzYVlc5w4d28r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ldpKDIiSD5gPY2iXXX2Y1", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-5-b2RuZJ2wviXUAkWF41c", "label": "计划日历", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-G_TJbV71KzYVlc5w4d28r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ldpKDIiSD5gPY2iXXX2Y1", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_wc_head_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_wc_head_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-fE8jhA0xSyWIjgHIFgBvB", "label": "组件报废率%", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-G_TJbV71KzYVlc5w4d28r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ldpKDIiSD5gPY2iXXX2Y1", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-8ranFJKcnCSfQutr9-S0N", "label": "生产计量单位", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-G_TJbV71KzYVlc5w4d28r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ldpKDIiSD5gPY2iXXX2Y1", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-fe4SUbPb2APmtqfvCQfTi", "label": "过账到检验库存", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-G_TJbV71KzYVlc5w4d28r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ldpKDIiSD5gPY2iXXX2Y1", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-9U58pgvM2yGWbnQI-muTj", "label": "不足交货容差", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-G_TJbV71KzYVlc5w4d28r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ldpKDIiSD5gPY2iXXX2Y1", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-_q_RhkIWJOO4pQ0VJi8ZO", "label": "过度交货容差", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-G_TJbV71KzYVlc5w4d28r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ldpKDIiSD5gPY2iXXX2Y1", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-zSLFX4Ab5Dn5qIBT2Xs2l", "label": "未限制的过度交货", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-G_TJbV71KzYVlc5w4d28r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ldpKDIiSD5gPY2iXXX2Y1", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-O1jM_lnynwE0-wNWNLuvd", "label": "采购类型", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-G_TJbV71KzYVlc5w4d28r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ldpKDIiSD5gPY2iXXX2Y1", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-U28PuU81yk2Drto0hujQq", "label": "反冲标识", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-G_TJbV71KzYVlc5w4d28r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ldpKDIiSD5gPY2iXXX2Y1", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-jk9CbAjVvqF-g_ntG4oCl", "label": "独立/集中", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-G_TJbV71KzYVlc5w4d28r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ldpKDIiSD5gPY2iXXX2Y1", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-bu9628FXnEy9RnWbzF-oK", "label": "生产管理员", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-G_TJbV71KzYVlc5w4d28r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ldpKDIiSD5gPY2iXXX2Y1", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-rcXadzzGlt4J5OU5mloPv", "label": "批次参数文件", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-G_TJbV71KzYVlc5w4d28r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ldpKDIiSD5gPY2iXXX2Y1", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-PbYi93oIoL6w0BATiE0JT", "label": "序列号参数文件", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-G_TJbV71KzYVlc5w4d28r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-5uu1iT8QjiW4zrS8tGM65", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-ldpKDIiSD5gPY2iXXX2Y1", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-GqoJae4H2YBSfchWAZ_jZ", "label": "库存组织", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-MO70XPCOYwWxw9aaoipqU", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-8TWGUVjVdzAkatD_C9vVM", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-78QTtlOQ2FAUQgEsnf6vR", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-sQlYZ-4asxEppNMsdcOtP", "label": "移动平均成本价", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-MO70XPCOYwWxw9aaoipqU", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-8TWGUVjVdzAkatD_C9vVM", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-78QTtlOQ2FAUQgEsnf6vR", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-Jk9QVOczrH4pUr9zZ_oCD", "label": "标准成本价", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-MO70XPCOYwWxw9aaoipqU", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-8TWGUVjVdzAkatD_C9vVM", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-78QTtlOQ2FAUQgEsnf6vR", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-AEc27Qj3DZgKjxgP4n7_5", "label": "计价方式", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-MO70XPCOYwWxw9aaoipqU", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-8TWGUVjVdzAkatD_C9vVM", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-78QTtlOQ2FAUQgEsnf6vR", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-00SSwNh0FBuK6xKFiIigJ", "label": "库存组织", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-AhxUCQwd3-Iz6gcvRKQoP", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-t30HUYGRrLVdXuqwde9WP", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-lT1l3BmrmgTPtYmho2Qvm", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-UducxHYiwf2tUStmFEmlf", "label": "库存地点", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-AhxUCQwd3-Iz6gcvRKQoP", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-t30HUYGRrLVdXuqwde9WP", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-lT1l3BmrmgTPtYmho2Qvm", "label": "字段组", "type": "Fields"}], "relations": [{"key": "sys_common$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "sys_common$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-Jg_0zIsyk3RcKZEdtz6WY", "label": "存储单位", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-AhxUCQwd3-Iz6gcvRKQoP", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-t30HUYGRrLVdXuqwde9WP", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-lT1l3BmrmgTPtYmho2Qvm", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-OK5TNawyMmbcr4pMWO578", "label": "延迟发货处理时间(天)", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-AhxUCQwd3-Iz6gcvRKQoP", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-t30HUYGRrLVdXuqwde9WP", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-lT1l3BmrmgTPtYmho2Qvm", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-7krR3c5B8ygI6NzMm8A8V", "label": "收货处理时间(天)", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-AhxUCQwd3-Iz6gcvRKQoP", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-t30HUYGRrLVdXuqwde9WP", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-lT1l3BmrmgTPtYmho2Qvm", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-_qeGSoC-cOvqOgZLvh4eS", "label": "再订货点", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-KMfJWGPEsLt7EYeLB3i8r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-KSYC3Wr5zoygttu713DyA", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-UEXeXMDMaWs7CsCDFfh6I", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-r6AkFqcyJJEs-E1RJn0Aj", "label": "计划周期", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-KMfJWGPEsLt7EYeLB3i8r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-KSYC3Wr5zoygttu713DyA", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-UEXeXMDMaWs7CsCDFfh6I", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-N5xTYwCTLjC_v1EmMZF41", "label": "计划交货天数", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-KMfJWGPEsLt7EYeLB3i8r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-KSYC3Wr5zoygttu713DyA", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-UEXeXMDMaWs7CsCDFfh6I", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-qVCEcYohtVNaKRnpT36Lu", "label": "自制时间", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-KMfJWGPEsLt7EYeLB3i8r", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-KSYC3Wr5zoygttu713DyA", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-UEXeXMDMaWs7CsCDFfh6I", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-BfimaBtgDxQS9ZJZ2gXlS", "label": "ID", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW--5NK9W6TeWMg2pHt9VUi5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-7L2mAxHLXcSgSDM-6WkQt", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-dj1Fky24w6yCp5i5fUSG_", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-1Hg4oPeD37jLeJIiSjeqW", "label": "基本单位系数", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW--5NK9W6TeWMg2pHt9VUi5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-7L2mAxHLXcSgSDM-6WkQt", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-dj1Fky24w6yCp5i5fUSG_", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-hFVn4T4tfHgJODuPHnR1g", "label": "基础单位", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW--5NK9W6TeWMg2pHt9VUi5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-7L2mAxHLXcSgSDM-6WkQt", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-dj1Fky24w6yCp5i5fUSG_", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-A7aKGEQ1BXADrFY1c76r3", "label": "运算符号", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW--5NK9W6TeWMg2pHt9VUi5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-7L2mAxHLXcSgSDM-6WkQt", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-dj1Fky24w6yCp5i5fUSG_", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-e25XbHu7w975G2Yme3o1z", "label": "目标单位系数", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW--5NK9W6TeWMg2pHt9VUi5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-7L2mAxHLXcSgSDM-6WkQt", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-dj1Fky24w6yCp5i5fUSG_", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_SCM$GEN_MAT_NEW_VIEW-n-eoCCEKEt7PwBLVC0FjV", "label": "目标单位", "path": [{"key": "ERP_SCM$GEN_MAT_NEW_VIEW-254luDa3oRkfGEYXW73nO", "label": "页面", "type": "Page"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-nTEsW_TzC79WLFTYXCjxl", "label": "详情", "type": "Detail"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-c4ktcD-FvBoeIFiDkfH9I", "label": "", "type": "DetailGroupItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-e94svyWRu0DbdNwLwbpjJ", "label": "页签", "type": "Tabs"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW--5NK9W6TeWMg2pHt9VUi5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-7L2mAxHLXcSgSDM-6WkQt", "label": "表格", "type": "Table"}, {"key": "ERP_SCM$GEN_MAT_NEW_VIEW-dj1Fky24w6yCp5i5fUSG_", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}], "type": "Container"}], "title": "物料详情", "type": "CUSTOM"}}