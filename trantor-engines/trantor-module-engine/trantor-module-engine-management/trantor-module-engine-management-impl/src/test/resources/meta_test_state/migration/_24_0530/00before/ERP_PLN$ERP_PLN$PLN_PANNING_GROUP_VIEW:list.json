{"type": "View", "name": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW:list", "parentKey": "ERP_PLN", "children": [], "props": {"content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf-new", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "buttonType": "default", "confirmOn": "off", "disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "新建", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf-batch", "name": "DropdownButton", "props": {"combinationMode": true, "disabled$": "$context.selectedKeys?.length === 0 || $context.route?.action === \"new\" || $context.route?.action === \"edit\"", "items": [{"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"elements": [{"fieldAlias": "ids", "fieldName": "ids", "fieldType": "Number", "valueConfig": {"expression": "<PERSON><PERSON><PERSON><PERSON>", "name": "ids", "type": "expression"}}, {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_PLN$pln_mrp_planning_group_cf"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "ERP_PLN$pln_mrp_planning_group_cf_BATCH_DELETE_DATA_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf"}, {"action": "PageJump", "target": "list"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-ERP_PLN$pln_mrp_planning_group_cf-multi-delete", "label": "批量删除"}], "label": "批量操作", "type": "default", "variant": "primary"}, "type": "Widget"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf-batch-actions", "name": "BatchActions", "props": {}}, {"children": [{"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf-logs", "name": "Logs", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "日志", "logsServiceProps": {"getLogsFlow": {"serviceKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-B9SBuEtZaUh3y2aA3DTz3", "name": "Field", "props": {"componentProps": {"fieldAlias": "planGroupCode", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请输入"}, "hidden": false, "label": "计划组编码", "name": "planGroupCode", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-mUS1QFt75MmjISkODtBJX", "name": "Field", "props": {"componentProps": {"fieldAlias": "planGroupName", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请输入"}, "hidden": false, "label": "计划组名称", "name": "planGroupName", "type": "TEXT", "width": 146}, "type": "Widget"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-daJxXRTJYhX1aTotkKj-v", "name": "Fields", "props": {}, "type": "Meta"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf", "name": "Table", "props": {"allowClickRowSelect": true, "allowRowSelect": true, "filterFields": [{"componentProps": {"fieldAlias": "planGroupCode", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请输入"}, "label": "计划组编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "kR30fT2JdrntLtetItcob", "valueRules": null}], "name": "planGroupCode", "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "planGroupName", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请输入"}, "label": "计划组名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "P-MPXCUQeB2V3kD6uJJNE", "valueRules": null}], "name": "planGroupName", "type": "TEXT", "width": 120}], "flow": {"containerKey": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf", "context$": "$context", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_PLN$pln_mrp_planning_group_cf"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_PLN$pln_mrp_planning_group_cf_PAGING_DATA_SERVICE_BzVyMx1", "type": "InvokeService"}, "isProFilter": false, "label": "表格", "mode": "simple", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "onRowActionConfig": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "enable": true}, "pagination": {"size": "small"}, "serviceKey": "ERP_PLN$SYS_PagingDataService", "showFilterFields": true, "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{((data?.code || \"\") + (data?.name || \"\")) || \"计划组配置详情\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-DXU_USWnFQ2BiIHubDzdT", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "buttonType": "default", "confirmOn": "off", "label": "编辑", "showCondition": {}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}, {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_PLN$pln_mrp_planning_group_cf"}, {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "ERP_PLN$pln_mrp_planning_group_cf_DELETE_DATA_BY_ID_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf"}, {"action": "Message", "message": "删除成功"}, {"action": "RefreshTab", "target": ["current"]}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "删除", "showCondition": {"conditions": [{"conditions": [{"id": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detail-shanchu-condition-id1", "leftValue": {"fieldType": "Enum", "scope": "form", "target": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf", "title": "status", "type": "VarValue", "val": "status", "value": "ERP_PLN$pln_mrp_planning_group_cf.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detail-shanchu-condition-id2", "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "default"}}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detail-ERP_PLN$pln_mrp_planning_group_cf-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"serviceKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-ERP_PLN$pln_mrp_planning_group_cf-field-planGroupCode", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "planGroupCode", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请输入"}, "editable": false, "label": "计划组编码", "name": "planGroupCode", "type": "TEXT"}}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-ERP_PLN$pln_mrp_planning_group_cf-field-planGroupName", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "planGroupName", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请输入"}, "editable": false, "label": "计划组名称", "name": "planGroupName", "type": "TEXT"}}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-ERP_PLN$pln_mrp_planning_group_cf-detail-defaultGroup", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-Q7knLMWdanvj-l102cQs6", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "ERP_PLN$user", "parentModelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_PLN$user", "serviceKey": "ERP_PLN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "ERP_PLN$user", "parentModelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_PLN$user", "serviceKey": "ERP_PLN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请选择"}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请选择"}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-system-detail-group", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-aPrAO15SRnP-wo7Muk8E_", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "系统信息"}], "underline": true}, "type": "Layout"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-ERP_PLN$pln_mrp_planning_group_cf-detail", "name": "Detail", "props": {"flow": {"containerKey": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-ERP_PLN$pln_mrp_planning_group_cf-detail", "context$": "$context", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_PLN$pln_mrp_planning_group_cf_FIND_DATA_BY_ID_SERVICE_BzVyMx2", "type": "InvokeService"}, "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "onFinish$": "(values) => invokeSystemService(\"ERP_PLN$SYS_MasterData_SaveDataService\", \"ERP_PLN$pln_mrp_planning_group_cf\", {...data, ...values}).then(() => $(\"ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-page\").action(\"reload\"))", "serviceKey": "ERP_PLN$SYS_FindDataByIdService"}, "type": "Container"}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}], "showFooter": false, "showHeader": true}, "type": "Container"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-view", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑\" + ((data?.code || \"\") + (data?.name || \"计划组配置\")) : \"新建计划组配置\"}}", "useExpression": true}, "type": "Meta"}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-field-planGroupCode", "name": "FormField", "props": {"componentProps": {"fieldAlias": "planGroupCode", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "计划组编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "uBdHkb8b8aQRDyb3njE7N", "valueRules": null}], "name": "planGroupCode", "rules": [{"message": "请输入计划组编码", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-field-planGroupName", "name": "FormField", "props": {"componentProps": {"fieldAlias": "planGroupName", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "计划组名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "W4X5TSbttiqGHP7ShZlbc", "valueRules": null}], "name": "planGroupName", "rules": [{"message": "请输入计划组名称", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-field-id", "name": "FormField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-field-createdBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "ERP_PLN$user", "parentModelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_PLN$user", "serviceKey": "ERP_PLN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "ERP_PLN$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "GVlo5zvZP_KVK5avaSKLn", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "ERP_PLN$user", "serviceKey": "ERP_PLN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_PLN$user", "serviceKey": "ERP_PLN$user_PAGING_DATA_SERVICE", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-field-updatedBy", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "ERP_PLN$user", "parentModelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"context$": "$context", "modelAlias": "ERP_PLN$user", "serviceKey": "ERP_PLN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "username", "parentModelAlias": "ERP_PLN$user", "placeholder": "请输入"}, "editComponentProps": {}, "editComponentType": "InputText", "label": "用户名", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "n9Cqgi_lom3HnbMoR4Y2r", "trigger": "auto", "valueRules": null}], "name": "username", "type": "TEXT"}], "findFlow": {"context$": "$context", "modelAlias": "ERP_PLN$user", "serviceKey": "ERP_PLN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "type": "InvokeSystemService"}, "flow": {"context$": "$context", "modelAlias": "ERP_PLN$user", "serviceKey": "ERP_PLN$user_PAGING_DATA_SERVICE", "type": "InvokeSystemService"}}, "editComponentType": "RelationSelect", "hidden": true, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-field-createdAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-field-updatedAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-field-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-field-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-field-originOrgId", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "originOrgId", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "所属组织", "name": "originOrgId", "rules": [], "type": "NUMBER"}, "type": "Widget"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form-defaultGroup", "name": "FormGroupItem", "props": {"showSplit": true, "title": false}, "type": "Layout"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-7cDfhqmkr0tHjwpqmwio9", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}], "underline": true}, "type": "Layout"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form", "context$": "$context", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "params$": "{ id: route.recordId }", "serviceKey": "ERP_PLN$pln_mrp_planning_group_cf_FIND_SINGLE_DATA_BY_ID_SERVICE_BzVyMx3", "test$": "!!route.recordId", "type": "InvokeService"}, {"containerKey": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form", "context$": "$context", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "params": [{"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "route?.query?.copyId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "params$": "{ id: route?.query?.copyId }", "serviceKey": "ERP_PLN$pln_mrp_planning_group_cf_COPY_DATA_CONVERTER_SERVICE_BzVyMx4", "test$": "!!route.query?.copyId", "type": "InvokeService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf", "serviceKey": "ERP_PLN$SYS_FindDataByIdService"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target$": "route?.action === \"edit\" ? \"show\" : \"list\""}]}, "buttonType": "default", "confirmOn": "off", "label": "取消", "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Validate", "validate": true}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_PLN$pln_mrp_planning_group_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "valueConfig": {"action": {"target": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form"}, "type": "action"}}], "service": "ERP_PLN$pln_mrp_planning_group_cf_MASTER_DATA_SAVE_DATA_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "data.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}, {"action": "Refresh", "target": ["ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf"]}, {"action": "Message", "level": "success", "message": "保存成功!"}], "executeLogic": "BindService"}, "buttonType": "default", "confirmOn": "off", "label": "保存", "type": "primary"}, "type": "Widget"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showBack": true, "showFooter": true, "showHeader": true}, "type": "Container"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "name": "ColumnPage", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}], "showFooter": false, "showHeader": false, "title": "MRP计划组配置"}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW:list", "resources": [{"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf", "label": "表格", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}], "relations": [{"key": "ERP_PLN$pln_mrp_planning_group_cf_PAGING_DATA_SERVICE_BzVyMx1", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf-new", "label": "新建", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf", "label": "表格", "type": "Table"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf-batch/items/ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-ERP_PLN$pln_mrp_planning_group_cf-multi-delete", "label": "批量删除", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf", "label": "表格", "type": "Table"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf-batch", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "ERP_PLN$pln_mrp_planning_group_cf_BATCH_DELETE_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf"}, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf-logs", "label": "日志", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf", "label": "表格", "type": "Table"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf-toolbar-actions", "label": "按钮组", "type": "ToolbarActions"}], "relations": [{"key": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-B9SBuEtZaUh3y2aA3DTz3", "label": "计划组编码", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf", "label": "表格", "type": "Table"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-daJxXRTJYhX1aTotkKj-v", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-mUS1QFt75MmjISkODtBJX", "label": "计划组名称", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-list-ERP_PLN$pln_mrp_planning_group_cf", "label": "表格", "type": "Table"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-daJxXRTJYhX1aTotkKj-v", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form", "label": "表单组", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_PLN$pln_mrp_planning_group_cf_FIND_SINGLE_DATA_BY_ID_SERVICE_BzVyMx3", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf"}, "type": "Service"}, {"key": "ERP_PLN$pln_mrp_planning_group_cf_COPY_DATA_CONVERTER_SERVICE_BzVyMx4", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-ERP_PLN$pln_mrp_planning_group_cf-detail", "label": "详情", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-page", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_PLN$pln_mrp_planning_group_cf_FIND_DATA_BY_ID_SERVICE_BzVyMx2", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-action-cancel", "label": "取消", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-action-save", "label": "保存", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "ERP_PLN$pln_mrp_planning_group_cf_MASTER_DATA_SAVE_DATA_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-DXU_USWnFQ2BiIHubDzdT", "label": "编辑", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-actions-delete", "label": "删除", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "ERP_PLN$pln_mrp_planning_group_cf_DELETE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$pln_mrp_planning_group_cf"}, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detail-ERP_PLN$pln_mrp_planning_group_cf-logs", "label": "日志", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-field-planGroupCode", "label": "计划组编码", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-7cDfhqmkr0tHjwpqmwio9", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-field-planGroupName", "label": "计划组名称", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-7cDfhqmkr0tHjwpqmwio9", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-field-id", "label": "ID", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-7cDfhqmkr0tHjwpqmwio9", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-field-createdBy", "label": "创建人", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-7cDfhqmkr0tHjwpqmwio9", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "ERP_PLN$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$user"}, "type": "Service"}, {"key": "ERP_PLN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-field-updatedBy", "label": "更新人", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-7cDfhqmkr0tHjwpqmwio9", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "ERP_PLN$user_PAGING_DATA_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$user"}, "type": "Service"}, {"key": "ERP_PLN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-field-createdAt", "label": "创建时间", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-7cDfhqmkr0tHjwpqmwio9", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-field-updatedAt", "label": "更新时间", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-7cDfhqmkr0tHjwpqmwio9", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-field-version", "label": "版本号", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-7cDfhqmkr0tHjwpqmwio9", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-field-deleted", "label": "逻辑删除标识", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-7cDfhqmkr0tHjwpqmwio9", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-field-originOrgId", "label": "所属组织", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-7cDfhqmkr0tHjwpqmwio9", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-editView-ERP_PLN$pln_mrp_planning_group_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-ERP_PLN$pln_mrp_planning_group_cf-field-planGroupCode", "label": "计划组编码", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-ERP_PLN$pln_mrp_planning_group_cf-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-Q7knLMWdanvj-l102cQs6", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-ERP_PLN$pln_mrp_planning_group_cf-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-ERP_PLN$pln_mrp_planning_group_cf-field-planGroupName", "label": "计划组名称", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-ERP_PLN$pln_mrp_planning_group_cf-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-Q7knLMWdanvj-l102cQs6", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-ERP_PLN$pln_mrp_planning_group_cf-detail-defaultGroup", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-system-detail-field-createdBy", "label": "创建人", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-ERP_PLN$pln_mrp_planning_group_cf-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-aPrAO15SRnP-wo7Muk8E_", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [{"key": "ERP_PLN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-system-detail-field-updatedBy", "label": "更新人", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-ERP_PLN$pln_mrp_planning_group_cf-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-aPrAO15SRnP-wo7Muk8E_", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [{"key": "ERP_PLN$user_FIND_SINGLE_DATA_BY_ID_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_PLN$user"}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-system-detail-field-createdAt", "label": "创建时间", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-ERP_PLN$pln_mrp_planning_group_cf-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-aPrAO15SRnP-wo7Muk8E_", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-system-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-view", "label": "视图", "type": "View"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-ERP_PLN$pln_mrp_planning_group_cf-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-aPrAO15SRnP-wo7Muk8E_", "label": "页签项", "type": "TabItem"}, {"key": "ERP_PLN$ERP_PLN$PLN_PANNING_GROUP_VIEW-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}}