{"type": "<PERSON><PERSON><PERSON>", "name": "Test Module", "children": [{"type": "View", "key": "test$frontend_config_module_empty_1", "name": "Frontend Config Module Empty 1"}, {"type": "View", "key": "test$frontend_config_module_empty_2", "name": "Frontend Config Module Empty 2"}, {"type": "View", "key": "test$frontend_config_module_only_t_material", "name": "Frontend Config Module Only T Material"}, {"type": "View", "key": "test$frontend_config_module_t_material_and_other_1", "name": "Frontend Config Module T Material And Other 1"}, {"type": "View", "key": "test$frontend_config_module_t_material_and_other_2", "name": "Frontend Config Module T Material And Other 2"}, {"type": "View", "key": "test$frontend_config_module_t_material_and_other_3", "name": "Frontend Config Module T Material And Other 3"}]}