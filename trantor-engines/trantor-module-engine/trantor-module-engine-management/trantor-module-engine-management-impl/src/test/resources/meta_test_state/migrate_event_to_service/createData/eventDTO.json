{"name": "SO-创建订单-提交", "code": "ERP_SCM$SO_CREATE_SUBMIT_EVENT", "desc": null, "model": {"key": "ERP_SCM$sls_so_head_tr", "desc": null, "name": "销售订单抬头表", "children": null}, "modelArrayWhether": false, "returnModel": null, "returnModelArrayWhether": false, "module": "ERP_SCM", "moduleName": "ERP SCM", "actions": [{"code": "ERP_SCM$SO_EDIT_CHECK", "desc": null, "convert": null, "express": null, "nextCode": "TERP_MIGRATE$SO_PARTNER_CHECK", "actionType": "Action", "conditions": null, "sourceCode": null, "conditionId": null, "enabledParamCheck": false}, {"code": "TERP_MIGRATE$SO_PARTNER_CHECK", "desc": null, "convert": null, "express": null, "nextCode": "TERP_MIGRATE$SO_QUALIFICATION_CHECK", "actionType": "Action", "conditions": null, "sourceCode": null, "conditionId": null, "enabledParamCheck": false}, {"code": "TERP_MIGRATE$SO_QUALIFICATION_CHECK", "desc": null, "convert": null, "express": null, "nextCode": "ERP_SCM$SO_LOAD_CF_EVENT", "actionType": "Action", "conditions": null, "sourceCode": null, "conditionId": null, "enabledParamCheck": false}, {"code": "ERP_SCM$SO_LOAD_CF_EVENT", "desc": null, "convert": null, "express": null, "nextCode": "ERP_SCM$SO_SLS_QTY_MIN_CHECK_ACTION", "actionType": "Event", "conditions": null, "sourceCode": null, "conditionId": null, "enabledParamCheck": false}, {"code": "ERP_SCM$SO_SLS_QTY_MIN_CHECK_ACTION", "desc": null, "convert": null, "express": "(ERP_SCM$sls_so_item_tr)soItems[]", "nextCode": "ERP_SCM$SO_SLS_BLOCK_CHECK_ACTION", "actionType": "Action", "conditions": [[{"key": "325", "left": {"model": {"isList": true, "fieldKey": "so_items", "hasChild": true, "modelKey": "ERP_SCM$sls_so_head_tr", "fieldName": "订单项目行", "fieldType": "OBJECT", "modelName": "销售订单抬头表", "childModel": {"isList": false, "fieldKey": "so_item_type_id", "hasChild": true, "modelKey": "ERP_SCM$sls_so_item_tr", "fieldName": "行项目类型", "fieldType": "OBJECT", "modelName": "销售订单项目行表", "childModel": {"isList": false, "fieldKey": "is_sls_min_qty_check", "hasChild": false, "modelKey": "ERP_SCM$sls_so_item_type_cf", "fieldName": "是否检查最小订购量", "fieldType": "BOOL", "modelName": "订单项目类型定义表", "childModel": null, "fieldAlias": "isSlsMinQtyCheck", "modelAlias": "ERP_SCM$sls_so_item_type_cf"}, "fieldAlias": "soItemTypeId", "modelAlias": "ERP_SCM$sls_so_item_tr"}, "fieldAlias": "soItems", "modelAlias": "ERP_SCM$sls_so_head_tr"}, "factor": null, "valueType": "MODEL"}, "right": {"model": null, "value": "true", "factor": null, "function": null, "fieldType": "BOOL", "valueType": "CONSTANT"}, "rights": null, "operator": "EQ"}]], "sourceCode": null, "conditionId": null, "enabledParamCheck": false}, {"code": "ERP_SCM$SO_SLS_BLOCK_CHECK_ACTION", "desc": null, "convert": null, "express": null, "nextCode": "ERP_SCM$SO_DEL_BLOCK_CHECK_ACTION", "actionType": "Action", "conditions": null, "sourceCode": null, "conditionId": null, "enabledParamCheck": false}, {"code": "ERP_SCM$SO_DEL_BLOCK_CHECK_ACTION", "desc": null, "convert": null, "express": null, "nextCode": "TERP_MIGRATE$SO_REASON_CONFIG_HANDLE_ACTION", "actionType": "Action", "conditions": null, "sourceCode": null, "conditionId": null, "enabledParamCheck": false}, {"code": "TERP_MIGRATE$SO_REASON_CONFIG_HANDLE_ACTION", "desc": null, "convert": null, "express": null, "nextCode": "TERP_MIGRATE$SO_AUTO_LATEST_PRICE_ACTION", "actionType": "Action", "conditions": null, "sourceCode": null, "conditionId": null, "enabledParamCheck": false}, {"code": "TERP_MIGRATE$SO_AUTO_LATEST_PRICE_ACTION", "desc": null, "convert": null, "express": null, "nextCode": "ERP_SCM$SO_PAY_PLAN_LINE_ACTION", "actionType": "Action", "conditions": null, "sourceCode": null, "conditionId": null, "enabledParamCheck": false}, {"code": "ERP_SCM$SO_PAY_PLAN_LINE_ACTION", "desc": null, "convert": null, "express": null, "nextCode": "ERP_SCM$SO_SUBMIT_ACTION", "actionType": "Action", "conditions": null, "sourceCode": null, "conditionId": null, "enabledParamCheck": false}, {"code": "ERP_SCM$SO_SUBMIT_ACTION", "desc": null, "convert": null, "express": null, "nextCode": "TERP_MIGRATE$SO_REVERSAL_RETURN_QTY_ACTION", "actionType": "Action", "conditions": null, "sourceCode": null, "conditionId": null, "enabledParamCheck": false}, {"code": "TERP_MIGRATE$SO_REVERSAL_RETURN_QTY_ACTION", "desc": null, "convert": null, "express": null, "nextCode": "TERP_MIGRATE$GEN_DOC_TRACE_SAVE_ACTION", "actionType": "Action", "conditions": [[{"key": "58", "left": {"model": {"isList": false, "fieldKey": "so_type_id", "hasChild": true, "modelKey": "ERP_SCM$sls_so_head_tr", "fieldName": "单据类型", "fieldType": "OBJECT", "modelName": "销售订单抬头表", "childModel": {"isList": false, "fieldKey": "is_rev", "hasChild": false, "modelKey": "ERP_SCM$sls_so_type_cf", "fieldName": "是否逆向", "fieldType": "BOOL", "modelName": "订单类型定义表", "childModel": null, "fieldAlias": "isRev", "modelAlias": "ERP_SCM$sls_so_type_cf"}, "fieldAlias": "soTypeId", "modelAlias": "ERP_SCM$sls_so_head_tr"}, "factor": null, "valueType": "MODEL"}, "right": {"model": null, "value": "true", "factor": null, "function": null, "fieldType": "BOOL", "valueType": "CONSTANT"}, "rights": null, "operator": "EQ"}]], "sourceCode": null, "conditionId": null, "enabledParamCheck": false}, {"code": "TERP_MIGRATE$GEN_DOC_TRACE_SAVE_ACTION", "desc": null, "convert": "import com.alibaba.fastjson.JSON\nimport com.alibaba.fastjson.JSONArray\nimport com.alibaba.fastjson.JSONObject\nimport java.time.LocalDateTime\nimport io.terminus.erp.md.spi.model.dto.GenDocTraceTrDTO\n\ndef convert(param){\n    JSONObject soJson = (JSONObject) param\n    JSONArray soItemJsonArray = soJson.getJSONArray(\"soItems\")\n    JSONArray docJsonArray = new JSONArray();\n    for (int i = 0; i < soItemJsonArray.size(); i++) {\n        JSONObject soItemJson = (JSONObject) soItemJsonArray.get(i)\n        String itemCode = soItemJson.getString(\"soItemCode\")\n        long soId = soItemJson.getJSONObject(\"soId\").getLong(\"id\");\n        String soCode = soItemJson.getString(\"soCode\");\n\n        JSONObject docJson = new JSONObject()\n        docJson.put(\"doc_code\", itemCode)\n        docJson.put(\"doc_head_id\", soId)\n        docJson.put(\"doc_head_code\", soCode)\n        docJson.put(\"doc_ref_type\", \"SO\")\n        docJson.put(\"doc_time\", LocalDateTime.now())\n\n        docJsonArray.add(docJson)\n    }\n    JSONObject result = new JSONObject()\n    result.put(\"genDocTraceTrPOList\", docJsonArray)\n\n    return JSON.toJavaObject(result, GenDocTraceTrDTO.class)\n}", "express": null, "nextCode": "ERP_SCM$ATP_CHECK_ACTION", "actionType": "Action", "conditions": null, "sourceCode": null, "conditionId": null, "enabledParamCheck": false}, {"code": "ERP_SCM$ATP_CHECK_ACTION", "desc": null, "convert": null, "express": "(ERP_SCM$sls_so_item_tr)soItems[]", "nextCode": "ERP_SCM$SO_AUTO_CONDITION_GROUP_ACTION", "actionType": "Action", "conditions": [[{"key": "322", "left": {"model": {"isList": true, "fieldKey": "so_items", "hasChild": true, "modelKey": "ERP_SCM$sls_so_head_tr", "fieldName": "订单项目行", "fieldType": "OBJECT", "modelName": "销售订单抬头表", "childModel": {"isList": false, "fieldKey": "so_item_type_id", "hasChild": true, "modelKey": "ERP_SCM$sls_so_item_tr", "fieldName": "行项目类型", "fieldType": "OBJECT", "modelName": "销售订单项目行表", "childModel": {"isList": false, "fieldKey": "is_atp_check", "hasChild": false, "modelKey": "ERP_SCM$sls_so_item_type_cf", "fieldName": "是否可用性检查", "fieldType": "BOOL", "modelName": "订单项目类型定义表", "childModel": null, "fieldAlias": "isAtpCheck", "modelAlias": "ERP_SCM$sls_so_item_type_cf"}, "fieldAlias": "soItemTypeId", "modelAlias": "ERP_SCM$sls_so_item_tr"}, "fieldAlias": "soItems", "modelAlias": "ERP_SCM$sls_so_head_tr"}, "factor": null, "valueType": "MODEL"}, "right": {"model": null, "value": "true", "factor": null, "function": null, "fieldType": "BOOL", "valueType": "CONSTANT"}, "rights": null, "operator": "EQ"}]], "sourceCode": null, "conditionId": null, "enabledParamCheck": false}, {"code": "ERP_SCM$SO_AUTO_CONDITION_GROUP_ACTION", "desc": null, "convert": null, "express": null, "nextCode": "ERP_SCM$DN_SAVE_EVENT", "actionType": "Action", "conditions": [[{"key": "2588", "left": {"model": null, "factor": {"factorKey": "slsSoTrQueryAppService.soQueryNeedDelivery(TerpMigrate$slsSoHeadTr.id)", "fieldType": "BOOL", "factorName": "是否需要执行发货"}, "valueType": "FACTOR"}, "right": {"model": null, "value": "true", "factor": null, "function": null, "fieldType": "BOOL", "valueType": "CONSTANT"}, "rights": null, "operator": "EQ"}]], "sourceCode": null, "conditionId": null, "enabledParamCheck": false}, {"code": "ERP_SCM$DN_SAVE_EVENT", "desc": null, "convert": null, "express": "[]", "nextCode": "ERP_SCM$SO_CONVERT_PO_ACTION", "actionType": "Event", "conditions": null, "sourceCode": "ERP_SCM$SO_AUTO_CONDITION_GROUP_ACTION", "conditionId": null, "enabledParamCheck": false}, {"code": "ERP_SCM$SO_CONVERT_PO_ACTION", "desc": null, "convert": null, "express": null, "nextCode": "ERP_SCM$PO_CREATE_SUBMIT_EVENT", "actionType": "Action", "conditions": [[{"key": "1045", "left": {"model": {"isList": true, "fieldKey": "so_items", "hasChild": true, "modelKey": "ERP_SCM$sls_so_head_tr", "fieldName": "订单项目行", "fieldType": "OBJECT", "modelName": "销售订单抬头表", "childModel": {"isList": false, "fieldKey": "so_item_type_id", "hasChild": true, "modelKey": "ERP_SCM$sls_so_item_tr", "fieldName": "行项目类型", "fieldType": "OBJECT", "modelName": "销售订单项目行表", "childModel": {"isList": false, "fieldKey": "is_auto_po", "hasChild": false, "modelKey": "ERP_SCM$sls_so_item_type_cf", "fieldName": "是否自动生成采购订单", "fieldType": "BOOL", "modelName": "订单项目类型定义表", "childModel": null, "fieldAlias": "isAutoPo", "modelAlias": "ERP_SCM$sls_so_item_type_cf"}, "fieldAlias": "soItemTypeId", "modelAlias": "ERP_SCM$sls_so_item_tr"}, "fieldAlias": "soItems", "modelAlias": "ERP_SCM$sls_so_head_tr"}, "factor": null, "valueType": "MODEL"}, "right": {"model": null, "value": "true", "factor": null, "function": null, "fieldType": "BOOL", "valueType": "CONSTANT"}, "rights": null, "operator": "EQ"}]], "sourceCode": null, "conditionId": null, "enabledParamCheck": false}, {"code": "ERP_SCM$PO_CREATE_SUBMIT_EVENT", "desc": null, "convert": null, "express": "[]", "nextCode": "TERP_MIGRATE$SO_SETTLE_ACTION", "actionType": "Event", "conditions": null, "sourceCode": "ERP_SCM$SO_CONVERT_PO_ACTION", "conditionId": null, "enabledParamCheck": false}, {"code": "TERP_MIGRATE$SO_SETTLE_ACTION", "desc": null, "convert": null, "express": "(ERP_SCM$sls_so_item_tr)soItems[]", "nextCode": null, "actionType": "Action", "conditions": [[{"key": "74", "left": {"model": {"isList": true, "fieldKey": "so_items", "hasChild": true, "modelKey": "ERP_SCM$sls_so_head_tr", "fieldName": "订单项目行", "fieldType": "OBJECT", "modelName": "销售订单抬头表", "childModel": {"isList": false, "fieldKey": "so_item_type_id", "hasChild": true, "modelKey": "ERP_SCM$sls_so_item_tr", "fieldName": "行项目类型", "fieldType": "OBJECT", "modelName": "销售订单项目行表", "childModel": {"isList": false, "fieldKey": "is_del_relv", "hasChild": false, "modelKey": "ERP_SCM$sls_so_item_type_cf", "fieldName": "是否交货相关", "fieldType": "BOOL", "modelName": "订单项目类型定义表", "childModel": null, "fieldAlias": "isDelRelv", "modelAlias": "ERP_SCM$sls_so_item_type_cf"}, "fieldAlias": "soItemTypeId", "modelAlias": "ERP_SCM$sls_so_item_tr"}, "fieldAlias": "soItems", "modelAlias": "ERP_SCM$sls_so_head_tr"}, "factor": null, "valueType": "MODEL"}, "right": {"model": null, "value": "false", "factor": null, "function": null, "fieldType": "BOOL", "valueType": "CONSTANT"}, "rights": null, "operator": "EQ"}]], "sourceCode": null, "conditionId": null, "enabledParamCheck": false}], "states": [{"modelKey": "ERP_SCM$sls_so_head_tr", "fieldKey": "soCollectStatus", "sourceState": "WAIT_COLLECT", "targetState": "COLLECT_COMPLETE", "conditions": null}, {"modelKey": "ERP_SCM$sls_so_head_tr", "fieldKey": "soCollectStatus", "sourceState": "INIT", "targetState": "COLLECT_COMPLETE", "conditions": null}, {"modelKey": "ERP_SCM$sls_so_head_tr", "fieldKey": "soStatus", "sourceState": "DRAFT", "targetState": "CREATED", "conditions": null}, {"modelKey": "ERP_SCM$sls_so_head_tr", "fieldKey": "soStatus", "sourceState": "INIT", "targetState": "CREATED", "conditions": null}, {"modelKey": "ERP_SCM$sls_so_schl_tr", "fieldKey": "soSchlStatus", "conditions": [[{"key": "91", "left": {"model": {"isList": false, "fieldKey": "so_schl_type_id", "hasChild": true, "modelKey": "ERP_SCM$sls_so_schl_tr", "fieldName": "行项目类型", "fieldType": "OBJECT", "modelName": "发货计划行表", "childModel": {"isList": false, "fieldKey": "is_del_relv", "hasChild": false, "modelKey": "ERP_SCM$sls_so_item_type_cf", "fieldName": "是否交货相关", "fieldType": "BOOL", "modelName": "订单项目类型定义表", "childModel": null, "fieldAlias": "isDelRelv", "modelAlias": "ERP_SCM$sls_so_item_type_cf"}, "fieldAlias": "soSchlTypeId", "modelAlias": "ERP_SCM$sls_so_schl_tr"}, "factor": null, "valueType": "MODEL"}, "right": {"model": null, "value": "true", "factor": null, "function": null, "fieldType": "BOOL", "valueType": "CONSTANT"}, "rights": null, "operator": "EQ"}]], "conditionId": null, "sourceState": "INIT", "targetState": "ODEL_TODO", "conditionExpress": "((DATA_LOAD.dataLoad(\"ERP_SCM$sls_so_item_type_cf\", \"is_del_relv\", (ErpScm$slsSoSchlTr.soSchlTypeId instanceof Number ? ErpScm$slsSoSchlTr.soSchlTypeId : ErpScm$slsSoSchlTr.soSchlTypeId.id)) == \"true\")) "}, {"modelKey": "ERP_SCM$sls_so_schl_tr", "fieldKey": "soSchlStatus", "conditions": [[{"key": "201", "left": {"model": {"isList": false, "fieldKey": "so_schl_type_id", "hasChild": true, "modelKey": "ERP_SCM$sls_so_schl_tr", "fieldName": "行项目类型", "fieldType": "OBJECT", "modelName": "发货计划行表", "childModel": {"isList": false, "fieldKey": "is_del_relv", "hasChild": false, "modelKey": "ERP_SCM$sls_so_item_type_cf", "fieldName": "是否交货相关", "fieldType": "BOOL", "modelName": "订单项目类型定义表", "childModel": null, "fieldAlias": "isDelRelv", "modelAlias": "ERP_SCM$sls_so_item_type_cf"}, "fieldAlias": "soSchlTypeId", "modelAlias": "ERP_SCM$sls_so_schl_tr"}, "factor": null, "valueType": "MODEL"}, "right": {"model": null, "value": "true", "factor": null, "function": null, "fieldType": "BOOL", "valueType": "CONSTANT"}, "rights": null, "operator": "EQ"}]], "conditionId": null, "sourceState": "DRAF", "targetState": "ODEL_TODO", "conditionExpress": "((DATA_LOAD.dataLoad(\"ERP_SCM$sls_so_item_type_cf\", \"is_del_relv\", (ErpScm$slsSoSchlTr.soSchlTypeId instanceof Number ? ErpScm$slsSoSchlTr.soSchlTypeId : ErpScm$slsSoSchlTr.soSchlTypeId.id)) == \"true\")) "}, {"modelKey": "ERP_SCM$sls_so_schl_tr", "fieldKey": "soSchlStatus", "conditions": [[{"key": "157", "left": {"model": {"isList": false, "fieldKey": "so_schl_type_id", "hasChild": true, "modelKey": "ERP_SCM$sls_so_schl_tr", "fieldName": "行项目类型", "fieldType": "OBJECT", "modelName": "发货计划行表", "childModel": {"isList": false, "fieldKey": "is_del_relv", "hasChild": false, "modelKey": "ERP_SCM$sls_so_item_type_cf", "fieldName": "是否交货相关", "fieldType": "BOOL", "modelName": "订单项目类型定义表", "childModel": null, "fieldAlias": "isDelRelv", "modelAlias": "ERP_SCM$sls_so_item_type_cf"}, "fieldAlias": "soSchlTypeId", "modelAlias": "ERP_SCM$sls_so_schl_tr"}, "factor": null, "valueType": "MODEL"}, "right": {"model": null, "value": "false", "factor": null, "function": null, "fieldType": "BOOL", "valueType": "CONSTANT"}, "rights": null, "operator": "EQ"}, {"key": "158", "left": {"model": {"isList": false, "fieldKey": "so_schl_type_id", "hasChild": true, "modelKey": "ERP_SCM$sls_so_schl_tr", "fieldName": "行项目类型", "fieldType": "OBJECT", "modelName": "发货计划行表", "childModel": {"isList": false, "fieldKey": "bil_rul_type", "hasChild": false, "modelKey": "ERP_SCM$sls_so_item_type_cf", "fieldName": "开票规则", "fieldType": "ENUM", "modelName": "订单项目类型定义表", "childModel": null, "fieldAlias": "bilRulType", "modelAlias": "ERP_SCM$sls_so_item_type_cf"}, "fieldAlias": "soSchlTypeId", "modelAlias": "ERP_SCM$sls_so_schl_tr"}, "factor": null, "valueType": "MODEL"}, "right": {"model": null, "value": "SO", "factor": null, "function": null, "fieldType": "ENUM", "valueType": "CONSTANT"}, "rights": null, "operator": "EQ"}]], "conditionId": null, "sourceState": "INIT", "targetState": "BILL_TODO", "conditionExpress": "((DATA_LOAD.dataLoad(\"ERP_SCM$sls_so_item_type_cf\", \"is_del_relv\", (ErpScm$slsSoSchlTr.soSchlTypeId instanceof Number ? ErpScm$slsSoSchlTr.soSchlTypeId : ErpScm$slsSoSchlTr.soSchlTypeId.id)) == \"false\")&&(DATA_LOAD.dataLoad(\"ERP_SCM$sls_so_item_type_cf\", \"bil_rul_type\", (ErpScm$slsSoSchlTr.soSchlTypeId instanceof Number ? ErpScm$slsSoSchlTr.soSchlTypeId : ErpScm$slsSoSchlTr.soSchlTypeId.id)) == \"SO\")) "}, {"modelKey": "ERP_SCM$sls_so_schl_tr", "fieldKey": "soSchlStatus", "conditions": [[{"key": "201", "left": {"model": {"isList": false, "fieldKey": "so_schl_type_id", "hasChild": true, "modelKey": "ERP_SCM$sls_so_schl_tr", "fieldName": "行项目类型", "fieldType": "OBJECT", "modelName": "发货计划行表", "childModel": {"isList": false, "fieldKey": "is_del_relv", "hasChild": false, "modelKey": "ERP_SCM$sls_so_item_type_cf", "fieldName": "是否交货相关", "fieldType": "BOOL", "modelName": "订单项目类型定义表", "childModel": null, "fieldAlias": "isDelRelv", "modelAlias": "ERP_SCM$sls_so_item_type_cf"}, "fieldAlias": "soSchlTypeId", "modelAlias": "ERP_SCM$sls_so_schl_tr"}, "factor": null, "valueType": "MODEL"}, "right": {"model": null, "value": "false", "factor": null, "function": null, "fieldType": "BOOL", "valueType": "CONSTANT"}, "rights": null, "operator": "EQ"}, {"key": "202", "left": {"model": {"isList": false, "fieldKey": "so_schl_type_id", "hasChild": true, "modelKey": "ERP_SCM$sls_so_schl_tr", "fieldName": "行项目类型", "fieldType": "OBJECT", "modelName": "发货计划行表", "childModel": {"isList": false, "fieldKey": "bil_rul_type", "hasChild": false, "modelKey": "ERP_SCM$sls_so_item_type_cf", "fieldName": "开票规则", "fieldType": "ENUM", "modelName": "订单项目类型定义表", "childModel": null, "fieldAlias": "bilRulType", "modelAlias": "ERP_SCM$sls_so_item_type_cf"}, "fieldAlias": "soSchlTypeId", "modelAlias": "ERP_SCM$sls_so_schl_tr"}, "factor": null, "valueType": "MODEL"}, "right": {"model": null, "value": "SO", "factor": null, "function": null, "fieldType": "ENUM", "valueType": "CONSTANT"}, "rights": null, "operator": "EQ"}]], "conditionId": null, "sourceState": "DRAF", "targetState": "BILL_TODO", "conditionExpress": "((DATA_LOAD.dataLoad(\"ERP_SCM$sls_so_item_type_cf\", \"is_del_relv\", (ErpScm$slsSoSchlTr.soSchlTypeId instanceof Number ? ErpScm$slsSoSchlTr.soSchlTypeId : ErpScm$slsSoSchlTr.soSchlTypeId.id)) == \"false\")&&(DATA_LOAD.dataLoad(\"ERP_SCM$sls_so_item_type_cf\", \"bil_rul_type\", (ErpScm$slsSoSchlTr.soSchlTypeId instanceof Number ? ErpScm$slsSoSchlTr.soSchlTypeId : ErpScm$slsSoSchlTr.soSchlTypeId.id)) == \"SO\")) "}, {"modelKey": "ERP_SCM$sls_so_schl_tr", "fieldKey": "soSchlStatus", "conditions": [[{"key": "292", "left": {"model": {"isList": false, "fieldKey": "so_schl_type_id", "hasChild": true, "modelKey": "ERP_SCM$sls_so_schl_tr", "fieldName": "行项目类型", "fieldType": "OBJECT", "modelName": "发货计划行表", "childModel": {"isList": false, "fieldKey": "is_del_relv", "hasChild": false, "modelKey": "ERP_SCM$sls_so_item_type_cf", "fieldName": "是否交货相关", "fieldType": "BOOL", "modelName": "订单项目类型定义表", "childModel": null, "fieldAlias": "isDelRelv", "modelAlias": "ERP_SCM$sls_so_item_type_cf"}, "fieldAlias": "soSchlTypeId", "modelAlias": "ERP_SCM$sls_so_schl_tr"}, "factor": null, "valueType": "MODEL"}, "right": {"model": null, "value": "false", "factor": null, "function": null, "fieldType": "BOOL", "valueType": "CONSTANT"}, "rights": null, "operator": "EQ"}, {"key": "293", "left": {"model": {"isList": false, "fieldKey": "so_schl_type_id", "hasChild": true, "modelKey": "ERP_SCM$sls_so_schl_tr", "fieldName": "行项目类型", "fieldType": "OBJECT", "modelName": "发货计划行表", "childModel": {"isList": false, "fieldKey": "bil_rul_type", "hasChild": false, "modelKey": "ERP_SCM$sls_so_item_type_cf", "fieldName": "开票规则", "fieldType": "ENUM", "modelName": "订单项目类型定义表", "childModel": null, "fieldAlias": "bilRulType", "modelAlias": "ERP_SCM$sls_so_item_type_cf"}, "fieldAlias": "soSchlTypeId", "modelAlias": "ERP_SCM$sls_so_schl_tr"}, "factor": null, "valueType": "MODEL"}, "right": {"model": null, "value": "SO", "factor": null, "function": null, "fieldType": "ENUM", "valueType": "CONSTANT"}, "rights": null, "operator": "NEQ"}]], "conditionId": null, "sourceState": "INIT", "targetState": "DONE", "conditionExpress": "((DATA_LOAD.dataLoad(\"ERP_SCM$sls_so_item_type_cf\", \"is_del_relv\", (ErpScm$slsSoSchlTr.soSchlTypeId instanceof Number ? ErpScm$slsSoSchlTr.soSchlTypeId : ErpScm$slsSoSchlTr.soSchlTypeId.id)) == \"false\")&&(DATA_LOAD.dataLoad(\"ERP_SCM$sls_so_item_type_cf\", \"bil_rul_type\", (ErpScm$slsSoSchlTr.soSchlTypeId instanceof Number ? ErpScm$slsSoSchlTr.soSchlTypeId : ErpScm$slsSoSchlTr.soSchlTypeId.id)) != \"SO\")) "}, {"modelKey": "ERP_SCM$sls_so_schl_tr", "fieldKey": "soSchlStatus", "conditions": [[{"key": "352", "left": {"model": {"isList": false, "fieldKey": "so_schl_type_id", "hasChild": true, "modelKey": "ERP_SCM$sls_so_schl_tr", "fieldName": "行项目类型", "fieldType": "OBJECT", "modelName": "发货计划行表", "childModel": {"isList": false, "fieldKey": "is_del_relv", "hasChild": false, "modelKey": "ERP_SCM$sls_so_item_type_cf", "fieldName": "是否交货相关", "fieldType": "BOOL", "modelName": "订单项目类型定义表", "childModel": null, "fieldAlias": "isDelRelv", "modelAlias": "ERP_SCM$sls_so_item_type_cf"}, "fieldAlias": "soSchlTypeId", "modelAlias": "ERP_SCM$sls_so_schl_tr"}, "factor": null, "valueType": "MODEL"}, "right": {"model": null, "value": "false", "factor": null, "function": null, "fieldType": "BOOL", "valueType": "CONSTANT"}, "rights": null, "operator": "EQ"}, {"key": "353", "left": {"model": {"isList": false, "fieldKey": "so_schl_type_id", "hasChild": true, "modelKey": "ERP_SCM$sls_so_schl_tr", "fieldName": "行项目类型", "fieldType": "OBJECT", "modelName": "发货计划行表", "childModel": {"isList": false, "fieldKey": "bil_rul_type", "hasChild": false, "modelKey": "ERP_SCM$sls_so_item_type_cf", "fieldName": "开票规则", "fieldType": "ENUM", "modelName": "订单项目类型定义表", "childModel": null, "fieldAlias": "bilRulType", "modelAlias": "ERP_SCM$sls_so_item_type_cf"}, "fieldAlias": "soSchlTypeId", "modelAlias": "ERP_SCM$sls_so_schl_tr"}, "factor": null, "valueType": "MODEL"}, "right": {"model": null, "value": "SO", "factor": null, "function": null, "fieldType": "ENUM", "valueType": "CONSTANT"}, "rights": null, "operator": "NEQ"}]], "conditionId": null, "sourceState": "DRAF", "targetState": "DONE", "conditionExpress": "((DATA_LOAD.dataLoad(\"ERP_SCM$sls_so_item_type_cf\", \"is_del_relv\", (ErpScm$slsSoSchlTr.soSchlTypeId instanceof Number ? ErpScm$slsSoSchlTr.soSchlTypeId : ErpScm$slsSoSchlTr.soSchlTypeId.id)) == \"false\")&&(DATA_LOAD.dataLoad(\"ERP_SCM$sls_so_item_type_cf\", \"bil_rul_type\", (ErpScm$slsSoSchlTr.soSchlTypeId instanceof Number ? ErpScm$slsSoSchlTr.soSchlTypeId : ErpScm$slsSoSchlTr.soSchlTypeId.id)) != \"SO\")) "}, {"modelKey": "ERP_SCM$sls_so_head_tr", "fieldKey": "approveStatus", "sourceState": "INIT", "targetState": "APPROVED", "conditions": null}], "access": "Public", "notice": null, "enabledStatusVerify": false, "enabledTransaction": true}