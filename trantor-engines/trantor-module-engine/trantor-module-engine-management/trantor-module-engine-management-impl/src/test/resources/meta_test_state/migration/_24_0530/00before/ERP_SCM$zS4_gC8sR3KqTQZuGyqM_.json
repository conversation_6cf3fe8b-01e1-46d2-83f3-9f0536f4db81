{"type": "View", "name": "ERP_SCM$zS4_gC8sR3KqTQZuGyqM_", "parentKey": "ERP_SCM$TERP_MIGRATE_DN01", "children": [], "props": {"containerSelect": {"CH_pQe4OW-FJxdoMur64k": [{"field": "trDnItems", "selectFields": []}]}, "content": {"children": [{"children": [], "key": "f8-eIE_neHKZCoq53nc60", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑交货单抬头表\" : \"创建交货单抬头表\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "5WpvAnC5CXk9EoJDqjbx1", "name": "FormField", "props": {"componentProps": {"placeholder": "请输入ID"}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "RoVI1BIZvepBcnl7EHYT8", "name": "FormField", "props": {"componentProps": {"columns": ["nickname"], "label": "选择创建人", "modelAlias": "ERP_SCM$user", "placeholder": "请选择创建人", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "esZZX4c90GQzsLUKpEYqG", "name": "FormField", "props": {"componentProps": {"columns": ["nickname"], "label": "选择更新人", "modelAlias": "ERP_SCM$user", "placeholder": "请选择更新人", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "kmYLeGAoKMVyAlXLQhNdY", "name": "FormField", "props": {"componentProps": {"placeholder": "请选择创建时间"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "nC_s5RqzV9HrzYkVIRz9W", "name": "FormField", "props": {"componentProps": {"placeholder": "请选择更新时间"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "AtyYELV-FDukj4GgL0q4b", "name": "FormField", "props": {"componentProps": {"placeholder": "请输入版本号"}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "OIF1tlcHNXLiBhLJLK0qZ", "name": "FormField", "props": {"componentProps": {"placeholder": "请输入逻辑删除标识"}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "flmZvQ1r8rwmScJ5h9Ufs", "name": "FormField", "props": {"componentProps": {"columns": ["btClass", "dnTypeCode", "dnTypeName", "remark", "numRangeTypeId", "dnItemNoIncrement", "delTypeRef"], "label": "选择单据类型", "labelField": "dnTypeName", "modelAlias": "ERP_SCM$del_dn_type_cf", "placeholder": "请选择单据类型", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "单据类型", "name": "dnType", "rules": [{"message": "请输入单据类型", "required": true}], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "yQ0g_zIgAdWtKe0rRyAM3", "name": "FormField", "props": {"componentProps": {"options": [{"label": "销售", "value": "sls"}, {"label": "采购 ", "value": "pur"}], "placeholder": "请选择业务类型"}, "hidden": false, "initialValue": null, "label": "业务类型", "name": "btClass", "rules": [{"message": "请输入业务类型", "required": true}], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "eihF2z5jiZr2I8vwFx1Is", "name": "FormField", "props": {"componentProps": {"placeholder": "请输入单据编号"}, "hidden": false, "initialValue": null, "label": "单据编号", "name": "dnCode", "rules": [{"message": "请输入单据编号", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "ibz-otaKjjup5iy3VSLcA", "name": "FormField", "props": {"componentProps": {"placeholder": "请选择单据日期"}, "hidden": false, "initialValue": null, "label": "单据日期", "name": "dnDateDoc", "rules": [], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "o4FxLr4eNGwzuGLoKK8sv", "name": "FormField", "props": {"componentProps": {"placeholder": "请选择过账日期"}, "hidden": false, "initialValue": null, "label": "过账日期", "name": "dnDatePst", "rules": [], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "_n3E9RK_JPdqDlvU9b9oJ", "name": "FormField", "props": {"componentProps": {"placeholder": "请输入外部单据号"}, "hidden": false, "initialValue": null, "label": "外部单据号", "name": "dnIdExt", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "k4Vxadv85YP424KY9iBYE", "name": "FormField", "props": {"componentProps": {"placeholder": "请输入参考单据"}, "hidden": false, "initialValue": null, "label": "参考单据", "name": "dnIdRef", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "L6fmo9nVpr9aXMJbjdGki", "name": "FormField", "props": {"componentProps": {"columns": ["code", "name", "languageId", "area", "tele", "fax", "mail", "phone"], "label": "选择库存组", "modelAlias": "ERP_GEN$org_inv_tm_cf", "placeholder": "请选择库存组", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "库存组", "name": "invTmId", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "8NmaQQfPmymKviZbMl-Sk", "name": "FormField", "props": {"componentProps": {"options": [{"label": "自提", "value": "01"}, {"label": "库发", "value": "02"}], "placeholder": "请选择交货方式"}, "hidden": false, "initialValue": null, "label": "交货方式", "name": "shpType", "rules": [], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "IP0BvXY-ue1BdYDH68t8K", "name": "FormField", "props": {"componentProps": {"options": [{"label": "系统", "value": "sys"}, {"label": "人工", "value": "manual"}], "placeholder": "请选择创建方式"}, "hidden": false, "initialValue": null, "label": "创建方式", "name": "dn<PERSON><PERSON><PERSON>", "rules": [{"message": "请输入创建方式", "required": true}], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "fqfHyFrZIM-lXyHsTiECy", "name": "FormField", "props": {"componentProps": {"options": [{"label": "待发货", "value": "dnhdel201"}, {"label": "部分发货", "value": "dnhdel202"}, {"label": "发货完成", "value": "dnhdel203"}, {"label": "草稿-采购", "value": "DNHDEL201"}, {"label": "待收货-采购", "value": "DNHDEL202"}, {"label": "部分收货-采购", "value": "DNHDEL203"}, {"label": "收货完成-采购", "value": "DNHDEL204"}], "placeholder": "请选择交货状态"}, "hidden": false, "initialValue": null, "label": "交货状态", "name": "dnStatusDel", "rules": [], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "3gJBMEECbmF5nhcioZxg0", "name": "FormField", "props": {"componentProps": {"options": [{"label": "待开票", "value": "dnhbil201"}, {"label": "部分开票", "value": "dnhbil202"}, {"label": "开票完成", "value": "dnhbil203"}, {"label": "无需开票", "value": "dnhbil204"}, {"label": "草稿-采购", "value": "DNHINVOICE201"}, {"label": "待开票-采购", "value": "DNHINVOICE202"}, {"label": "部分开票-采购", "value": "DNHINVOICE203"}, {"label": "开票完成-采购", "value": "DNHINVOICE204"}], "placeholder": "请选择发票状态"}, "hidden": false, "initialValue": null, "label": "发票状态", "name": "dnStatusBil", "rules": [], "type": "SELECT"}, "type": "Widget"}, {"children": [], "key": "pBHqZt0VuCzClnABhzQGU", "name": "FormField", "props": {"componentProps": {"placeholder": "请选择审核时间"}, "hidden": false, "initialValue": null, "label": "审核时间", "name": "dnTimeApply", "rules": [], "type": "TIME"}, "type": "Widget"}, {"children": [], "key": "02r9hrAneKTUAbOTmtFxk", "name": "FormField", "props": {"componentProps": {"placeholder": "请选择交货完成时间"}, "hidden": false, "initialValue": null, "label": "交货完成时间", "name": "dnTimeDel", "rules": [], "type": "TIME"}, "type": "Widget"}, {"children": [], "key": "OCqAlko2AUpP-_eJR-0DU", "name": "FormField", "props": {"componentProps": {"placeholder": "请选择过账完成时间"}, "hidden": false, "initialValue": null, "label": "过账完成时间", "name": "dnTimeIssue", "rules": [], "type": "TIME"}, "type": "Widget"}, {"children": [], "key": "Z1i-PfRHeHCSUICLKGZic", "name": "FormField", "props": {"componentProps": {"placeholder": "请选择开票完成时间"}, "hidden": false, "initialValue": null, "label": "开票完成时间", "name": "dnTimeBil", "rules": [], "type": "TIME"}, "type": "Widget"}], "key": "HdfmT2BJHZMFLNyQ1zS2y", "name": "FormGroupItem", "props": {"title": "基本信息"}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "xVWBVm-rMI22HIpFKsDi8", "name": "<PERSON><PERSON>", "props": {"actionConfig": {}, "buttonKey": "button_lmPPQDPnwlYzqlS6upnf", "confirmOn": "off", "label": "查看定价", "type": "default"}, "type": "Widget"}], "key": "mHUIRIpQXwSqchq_CJh9t", "name": "RecordActions", "props": {"width": 120}, "type": "Layout"}, {"children": [{"children": [], "key": "_LB4JoB8PJRlFREunUZWd", "name": "Field", "props": {"componentProps": {"placeholder": "请输入ID"}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "ID必填", "required": true}], "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "vQKMV4OuvQW2SXKOwIMDd", "name": "Field", "props": {"componentProps": {"placeholder": "请输入交货行项目编号"}, "hidden": false, "initialValue": null, "label": "交货行项目编号", "name": "dnItemCode", "rules": [{"message": "交货行项目编号必填", "required": true}], "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "MrifCukVq7wGI56NoHnh4", "name": "Field", "props": {"componentProps": {"placeholder": "请输入关联交货单编号"}, "hidden": false, "initialValue": null, "label": "关联交货单编号", "name": "dnCode", "rules": [{"message": "关联交货单编号必填", "required": true}], "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "JGJWmIoC15CENNsbaTvSX", "name": "Field", "props": {"componentProps": {"columns": ["soSchlCode", "soId", "soItemId", "soSchlExptdate", "soSchlTypeId", "uomIdSls", "uomIdBase", "soItemQtySls", "soSchlQtyDel", "soSchlQtyConfirmed", "soSchlDelQtySub", "soSchlBilQtySub", "soSchlQtyIssue", "soSchlQtyPod", "soSchlStatus", "isDelBlock", "isBilBlock", "trSoIId"], "label": "选择关联订单计划行编号", "labelField": "soSchlCode", "modelAlias": "tr_so_s", "placeholder": "请选择关联订单计划行编号", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "关联订单计划行编号", "name": "soSchlIdRef", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "7GJggyqzMowGXmeggEZja", "name": "Field", "props": {"componentProps": {"columns": ["soItemCode", "soId", "soIdPre", "soItemIdPre", "matId", "<PERSON><PERSON><PERSON>", "soItemUsge", "soItemTypeId", "uomIdSls", "uomIdBase", "soItemQtySls", "soItemQtyBase", "soItemQtyDel", "soItemQtyPod", "soItemPriceNet", "soItemPriceGross", "taxratePercent", "taxAmount", "amountGrossBase", "amountNetBase", "invOrgId", "invLocId", "invTmId", "soSchlDateDel", "soItemStatus", "soSchlStatusDel", "soItemDateBil", "isDelBlock", "isBilBlock", "trSoSDTOS", "invAtpRecords"], "label": "选择关联订单行项目编号", "labelField": "soItemCode", "modelAlias": "tr_so_i", "placeholder": "请选择关联订单行项目编号", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "关联订单行项目编号", "name": "soItemIdRef", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "hCM27NDAwq-bu9owqb5eu", "name": "Field", "props": {"componentProps": {"columns": ["soCode", "soDateDoc", "soCodeExt", "soCodeRef", "slsOrgId", "slsDcId", "slsTmId", "slsRangeId", "currBase", "currSls", "exchrateDate", "exchratePercent", "amountGrossBase", "amountNetBase", "taxAmount", "soCreatedy", "soStatusDel", "soStatusBil", "soStatusPay", "soTimeApply", "soTimeDel", "soTimeBil", "soTimePay", "trSoIDTOS", "soType", "trPartnerLinks", "trReasonLinks", "trTextLinks", "trAttachmentLinks"], "label": "选择关联订单编号", "labelField": "soCode", "modelAlias": "tr_so_h", "placeholder": "请选择关联订单编号", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "关联订单编号", "name": "soIdRef", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "D2_07geMDTgCenmN6nGmE", "name": "Field", "props": {"componentProps": {"columns": ["matCode", "<PERSON><PERSON><PERSON>", "matOldId", "matTypeId", "uomBaseId", "uomMassId", "mat<PERSON>w", "matGw", "uomVolumId", "matVolum", "cateId", "matDateFrom", "brandId", "attriId", "statusId", "matSlsId", "mat<PERSON>ur<PERSON>d", "matInvId", "matMrpId"], "label": "选择物料编码", "labelField": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择物料编码", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "物料编码", "name": "matId", "rules": [{"message": "物料编码必填", "required": true}], "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "0QSnlFP-oVZc8s_d8_2lN", "name": "Field", "props": {"componentProps": {"placeholder": "请输入物料名称"}, "hidden": false, "initialValue": null, "label": "物料名称", "name": "<PERSON><PERSON><PERSON>", "rules": [], "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "T9_tPwNv3A20FSYN9ROQB", "name": "Field", "props": {"componentProps": {"columns": ["soItemTypeCode", "soItemTypeName", "remark", "soItemReasonTypeId", "spcstkTypeId", "mvmId", "isAtpCheck", "isDelRelv", "dnTypeIdSub", "isImmdel", "isSlsQtyMinCheck", "isDelQtyMinCheck", "isDelPercentageSurplusCheck", "isDelPercentShortageChekc", "isPickRelv", "isInvlocCheck", "bilTypeRul", "bilTypeIdSub", "atpTypeId"], "label": "选择行项目类型", "labelField": "soItemTypeName", "modelAlias": "cf_so_item_type", "placeholder": "请选择行项目类型", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "行项目类型", "name": "dnSoItemTypeId", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "VvlU-YGRYpHMhdkVlR3R7", "name": "Field", "props": {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "label": "选择基本单位", "labelField": "uomCode", "modelAlias": "ERP_GEN$gen_uom_type_cf", "placeholder": "请选择基本单位", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "基本单位", "name": "uomIdBase", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "YmHwNRvXE7MYs5JeIV8xV", "name": "Field", "props": {"componentProps": {"placeholder": "请输入计划交货数量"}, "hidden": false, "initialValue": null, "label": "计划交货数量", "name": "dnQtyDel", "rules": [], "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "bHeYHu0ZYA1YMFFUUv-Wn", "name": "Field", "props": {"componentProps": {"placeholder": "请选择计划交货日期"}, "hidden": false, "initialValue": null, "label": "计划交货日期", "name": "dnExptdate", "rules": [], "type": "DATE", "width": 134}, "type": "Widget"}, {"children": [], "key": "3F35msMcqKtghq0f05rr6", "name": "Field", "props": {"componentProps": {"placeholder": "请输入交货数量"}, "hidden": false, "initialValue": null, "label": "交货数量", "name": "dnItemQtyPst", "rules": [{"message": "交货数量必填", "required": true}], "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "C2lm1tpzHwmP1twdCoTPx", "name": "Field", "props": {"componentProps": {"placeholder": "请输入签收确认数量"}, "hidden": false, "initialValue": null, "label": "签收确认数量", "name": "dnItemQtyPod", "rules": [], "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "-TL5oy12NtuCFu81MbdDv", "name": "Field", "props": {"componentProps": {"placeholder": "请输入累计开票数量"}, "hidden": false, "initialValue": null, "label": "累计开票数量", "name": "dnItemBilQtySub", "rules": [], "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "XrSu5NoXZKBbFPkbJL9M_", "name": "Field", "props": {"componentProps": {"columns": ["code", "name", "comOrgId", "languageId", "area", "tele", "fax", "mail", "phone", "addressId", "addressDetail", "timezoneId", "postcode", "coordinate", "status"], "label": "选择调拨出库存组织", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择调拨出库存组织", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "调拨出库存组织", "name": "invOrgStoFromId", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "J8NcB8WyDcAEnShJeR6r5", "name": "Field", "props": {"componentProps": {"columns": ["code", "name", "languageId", "area", "tele", "fax", "mail", "phone", "addressId", "addressDetail", "timezoneId", "postcode", "coordinate", "orgId"], "label": "选择调拨出库存地点", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择调拨出库存地点", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "调拨出库存地点", "name": "invLocStoFromId", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "iLsm3U9yDAsrg_OR5X_2i", "name": "Field", "props": {"componentProps": {"columns": ["code", "name", "comOrgId", "languageId", "area", "tele", "fax", "mail", "phone", "addressId", "addressDetail", "timezoneId", "postcode", "coordinate", "status"], "label": "选择库存组织", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择库存组织", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "库存组织", "name": "invOrgId", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "g65om-kSCNS-QAcXzf27p", "name": "Field", "props": {"componentProps": {"columns": ["code", "name", "languageId", "area", "tele", "fax", "mail", "phone", "addressId", "addressDetail", "timezoneId", "postcode", "coordinate", "orgId"], "label": "选择库存地点", "labelField": "name", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择库存地点", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "库存地点", "name": "invLocId", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "sZRJwHDVwcCqnjKKxNmo8", "name": "Field", "props": {"componentProps": {"options": [{"label": "草稿", "value": "dni101"}, {"label": "待出库", "value": "dni102"}, {"label": "待开票", "value": "dni103"}, {"label": "部分开票", "value": "dni104"}, {"label": "完成", "value": "dni105"}, {"label": "草稿-采购", "value": "DNI101"}, {"label": "待收货-采购", "value": "DNI102"}, {"label": "待开票-采购", "value": "DNI103"}, {"label": "部分开票-采购", "value": "DNI104"}, {"label": "完成-采购", "value": "DNI105"}], "placeholder": "请选择行状态"}, "hidden": false, "initialValue": null, "label": "行状态", "name": "dnItemStatus", "rules": [{"message": "行状态必填", "required": true}], "type": "SELECT", "width": 116}, "type": "Widget"}, {"children": [], "key": "3NCAADfV4Nu3ynbUBQZnq", "name": "Field", "props": {"componentProps": {"placeholder": "请选择收货冻结"}, "hidden": false, "initialValue": null, "label": "收货冻结", "name": "isGrBlock", "rules": [], "type": "BOOL", "width": 116}, "type": "Widget"}, {"children": [], "key": "FKj48bpnWSDvbybv4nJ4-", "name": "Field", "props": {"componentProps": {"placeholder": "请选择开票冻结"}, "hidden": false, "initialValue": null, "label": "开票冻结", "name": "isBilBlock", "rules": [], "type": "BOOL", "width": 116}, "type": "Widget"}, {"children": [], "key": "SgOEMk9nlBzgkKU2rmlFL", "name": "Field", "props": {"componentProps": {"columns": ["poSchlCode", "poItemCode", "poCode", "remark", "poSchlStatus", "matId", "<PERSON><PERSON><PERSON>", "poSchlQtyDel", "poSchlQtyFul", "poSchlQtyInvoice", "poSchlDateDel", "poSchlDateFul", "invOrgStoFrom", "invLocStoFrom", "invOrgId", "invLocId", "trPoIId"], "label": "选择关联采购订单计划行编号", "labelField": "poSchlCode", "modelAlias": "ERP_SCM$pur_po_schl_tr", "placeholder": "请选择关联采购订单计划行编号", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "关联采购订单计划行编号", "name": "poSchlIdRef", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "pSdJ4KGx8ojruh7e1_C3D", "name": "Field", "props": {"componentProps": {"columns": ["poItemCode", "poCode", "poItemType", "purInfoRecPurId", "purTypeRef", "poIdRef", "poItemIdRef", "remark", "poItemStatusDel", "poItemStatusInvoice", "matId", "<PERSON><PERSON><PERSON>", "isGift", "poItemQtyPur", "poItemQtyFul", "poItemQtyInvoice", "uomBase", "priceGross", "currTypePur", "taxratePercent", "amountGross", "amountNet", "taxAmount", "poSchl", "trPoHId", "poItemUsge"], "label": "选择关联采购订单行项目编号", "labelField": "poItemCode", "modelAlias": "ERP_SCM$pur_po_item_tr", "placeholder": "请选择关联采购订单行项目编号", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "关联采购订单行项目编号", "name": "poItemIdRef", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "IJmX3HcmGz_W_hXpLgY1s", "name": "Field", "props": {"componentProps": {"columns": ["poCode", "poName", "poType", "poIdRef", "poIdExt", "poStatusDel", "poStatusInvoice", "isGrBlock", "comOrgId", "purOrgId", "purTm", "vendId", "invOrg", "invType", "currTypePay", "paymentTerm", "paymentMethod", "totalamountGross", "amountNet", "taxAmount", "poItem", "text", "attachment", "partner", "docTypeRef"], "label": "选择关联采购订单编号", "labelField": "poCode", "modelAlias": "ERP_SCM$pur_po_head_tr", "placeholder": "请选择关联采购订单编号", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "关联采购订单编号", "name": "poIdRef", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "2ZFlx5Og7pfSEwaRh9sOc", "name": "Field", "props": {"componentProps": {"columns": ["poItemType", "poItemTypeName", "remark", "poItemTypeStatus", "mvmType", "spcstkType", "isGrRelv", "isDnAutoRelv", "dnType", "invoiceTypeRul"], "label": "选择采购行项目类型", "labelField": "poItemType", "modelAlias": "ERP_SCM$pur_po_item_type_cf", "placeholder": "请选择采购行项目类型", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "采购行项目类型", "name": "dnPoItemTypeId", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "XyePtSUKW1Aigvf5LAfLP", "name": "Field", "props": {"componentProps": {"columns": ["soSchlCode", "soId", "soItemId", "soSchlExptdate", "soSchlTypeId", "uomIdSls", "uomIdBase", "soItemQtySls", "soSchlQtyDel", "soSchlQtyConfirmed", "soSchlDelQtySub", "soSchlBilQtySub", "soSchlQtyIssue", "soSchlQtyPod", "soSchlStatus", "isDelBlock", "isBilBlock", "trSoIId"], "label": "选择关联销售计划行模型", "labelField": "soSchlCode", "modelAlias": "tr_so_s", "placeholder": "请选择关联销售计划行模型", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": false, "initialValue": null, "label": "关联销售计划行模型", "name": "soSDTO", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}], "key": "3nOew8naa3gXYGhCvjxLg", "name": "Fields", "props": {}, "type": "Meta"}], "key": "47NTD2HbHKf8ZbawAeuX4", "name": "TableForm", "props": {"fieldName": "trDnItems", "modelAlias": "tr_dn_item"}}], "key": "A88hrjqeWkQF7U-hFKBzu", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "trDnItems"}, "type": "Meta"}], "key": "vtvdNJUQjGF6m23xoWza8", "name": "FormGroupItem", "props": {"allowSplit": true, "title": "交货行项目编号"}, "type": "Layout"}], "key": "CH_pQe4OW-FJxdoMur64k", "name": "FormGroup", "props": {"loadFlow$": "() => route.action === 'edit' ? invokeSystemServiceQuery('ERP_SCM$SYS_FindDataByIdService', 'tr_dn_h') : null"}, "type": "Container"}, {"children": [{"children": [], "key": "IAJHm0HiVSicP39XYxGxx", "name": "<PERSON><PERSON>", "props": {"label": "取消", "onClick$": "systemActions.cancel($context)"}, "type": "Widget"}, {"children": [], "key": "PiSugRAd3ocTzgT2MOwS4", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindFlowConfig": {"service": "ERP_SCM$DEL_DN_CALCULATE_PRICE_EVENT_SERVICE", "serviceParams": [{"key": "request", "leftValue": {"value": {"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Object", "required": true}, "valueType": "VAR"}, "operator": "EQ", "rightValue": [{"value": {}, "valueType": "MODEL"}], "type": "ConditionLeaf"}]}, "executeLogic": "BindFlow"}, "buttonKey": "button_cLTfdpUOcDCc3RatON7M", "confirmOn": "off", "label": "获取定价", "type": "default"}, "type": "Widget"}, {"children": [], "key": "NSlz0Rf7LhRGwr0JaewvF", "name": "<PERSON><PERSON>", "props": {"actionConfig": {}, "label": "保存", "onClick$": "systemActions.save($context, \"ERP_SCM$SYS_SaveDataService\", \"tr_dn_h\")", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "moEA-PEAjDXybZUkkc2cI", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindFlowConfig": {"service": "DN_CREATE_SLS_SUBMIT_EVENT_SERVICE", "serviceParams": [{"key": "request", "leftValue": {"value": {"fieldAlias": "request", "fieldKey": "request", "fieldName": "request", "fieldType": "Model", "relatedModel": {"modelAlias": "tr_dn_item", "modelKey": "tr_dn_item", "modelName": "tr_dn_item"}, "required": true}, "valueType": "VAR"}, "operator": "EQ", "rightValue": [{"value": {"alias": "tr_dn_item", "appId": -7, "children": [{"alias": "id", "appId": -7, "key": "id", "name": "ID", "props": {"autoGenerated": false, "columnName": "id", "comment": "ID", "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "required": true, "unique": true}, "teamId": -7, "type": "DataStructField"}, {"alias": "created<PERSON>y", "appId": -7, "key": "created_by", "name": "创建人", "props": {"autoGenerated": false, "columnName": "created_by", "comment": "创建人", "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"relationModelAlias": "ERP_SCM$user", "relationModelKey": "ERP_SCM$user", "relationType": "LINK"}, "required": false, "unique": true}, "teamId": -7, "type": "DataStructField"}, {"alias": "updatedBy", "appId": -7, "key": "updated_by", "name": "更新人", "props": {"autoGenerated": false, "columnName": "updated_by", "comment": "更新人", "fieldType": "OBJECT", "intLength": 20, "isSystemField": true, "length": 20, "relationMeta": {"relationModelAlias": "ERP_SCM$user", "relationModelKey": "ERP_SCM$user", "relationType": "LINK"}, "required": false, "unique": true}, "teamId": -7, "type": "DataStructField"}, {"alias": "createdAt", "appId": -7, "key": "created_at", "name": "创建时间", "props": {"autoGenerated": false, "columnName": "created_at", "comment": "创建时间", "fieldType": "DATE", "isSystemField": true, "required": true, "unique": true}, "teamId": -7, "type": "DataStructField"}, {"alias": "updatedAt", "appId": -7, "key": "updated_at", "name": "更新时间", "props": {"autoGenerated": false, "columnName": "updated_at", "comment": "更新时间", "fieldType": "DATE", "isSystemField": true, "required": true, "unique": true}, "teamId": -7, "type": "DataStructField"}, {"alias": "version", "appId": -7, "key": "version", "name": "版本号", "props": {"autoGenerated": false, "columnName": "version", "comment": "版本号", "defaultValue": 0, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "required": true, "unique": true}, "teamId": -7, "type": "DataStructField"}, {"alias": "deleted", "appId": -7, "key": "deleted", "name": "逻辑删除标识", "props": {"autoGenerated": false, "columnName": "deleted", "comment": "逻辑删除标识", "defaultValue": 0, "fieldType": "NUMBER", "intLength": 20, "isSystemField": true, "length": 20, "required": true, "unique": true}, "teamId": -7, "type": "DataStructField"}, {"alias": "dnItemCode", "appId": -7, "key": "dn_item_code", "name": "交货行项目编号", "props": {"autoGenerated": false, "columnName": "dn_item_code", "comment": "交货行项目编号", "fieldType": "TEXT", "isSystemField": false, "length": 256, "required": true, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "dnCode", "appId": -7, "key": "dn_code", "name": "关联交货单编号", "props": {"autoGenerated": false, "columnName": "dn_code", "comment": "关联交货单编号", "fieldType": "TEXT", "isSystemField": false, "length": 256, "required": true, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "soSchlIdRef", "appId": -7, "key": "so_schl_id_ref", "name": "关联订单计划行编号", "props": {"autoGenerated": false, "columnName": "so_schl_id_ref", "comment": "关联订单计划行编号", "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"relationModelAlias": "tr_so_s", "relationModelKey": "tr_so_s", "relationType": "LINK"}, "required": false, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "soItemIdRef", "appId": -7, "key": "so_item_id_ref", "name": "关联订单行项目编号", "props": {"autoGenerated": false, "columnName": "so_item_id_ref", "comment": "关联订单行项目编号", "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"relationModelAlias": "tr_so_i", "relationModelKey": "tr_so_i", "relationType": "LINK"}, "required": false, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "soIdRef", "appId": -7, "key": "so_id_ref", "name": "关联订单编号", "props": {"autoGenerated": false, "columnName": "so_id_ref", "comment": "关联订单编号", "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"relationModelAlias": "tr_so_h", "relationModelKey": "tr_so_h", "relationType": "LINK"}, "required": false, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "matId", "appId": -7, "key": "mat_id", "name": "物料编码", "props": {"autoGenerated": false, "columnName": "mat_id", "comment": "物料编码", "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"relationModelAlias": "ERP_GEN$gen_mat_md", "relationModelKey": "ERP_GEN$gen_mat_md", "relationType": "LINK"}, "required": true, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "<PERSON><PERSON><PERSON>", "appId": -7, "key": "mat_name", "name": "物料名称", "props": {"autoGenerated": false, "columnName": "mat_name", "comment": "物料名称", "fieldType": "TEXT", "isSystemField": false, "length": 256, "required": true, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "dnSoItemTypeId", "appId": -7, "key": "dn_so_item_type_id", "name": "行项目类型", "props": {"autoGenerated": false, "columnName": "dn_so_item_type_id", "comment": "行项目类型", "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"relationModelAlias": "cf_so_item_type", "relationModelKey": "cf_so_item_type", "relationType": "LINK"}, "required": false, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "uomIdBase", "appId": -7, "key": "uom_id_base", "name": "基本单位", "props": {"autoGenerated": false, "columnName": "uom_id_base", "comment": "基本单位", "fieldType": "TEXT", "isSystemField": false, "length": 256, "required": true, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "dnQtyDel", "appId": -7, "key": "dn_qty_del", "name": "计划交货数量", "props": {"autoGenerated": false, "columnName": "dn_qty_del", "comment": "计划交货数量", "fieldType": "NUMBER", "isSystemField": false, "required": true, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "dnExptdate", "appId": -7, "key": "dn_exptdate", "name": "计划交货日期", "props": {"autoGenerated": false, "columnName": "dn_exptdate", "comment": "计划交货日期", "fieldType": "DATE", "isSystemField": false, "required": false, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "dnItemQtyPst", "appId": -7, "key": "dn_item_qty_pst", "name": "交货数量", "props": {"autoGenerated": false, "columnName": "dn_item_qty_pst", "comment": "交货数量", "fieldType": "NUMBER", "isSystemField": false, "required": true, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "dnItemQtyPod", "appId": -7, "key": "dn_item_qty_pod", "name": "签收确认数量", "props": {"autoGenerated": false, "columnName": "dn_item_qty_pod", "comment": "签收确认数量", "fieldType": "NUMBER", "isSystemField": false, "required": true, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "dnItemBilQtySub", "appId": -7, "key": "dn_item_bil_qty_sub", "name": "累计开票数量", "props": {"autoGenerated": false, "columnName": "dn_item_bil_qty_sub", "comment": "累计开票数量", "fieldType": "NUMBER", "isSystemField": false, "required": true, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "invOrgStoFromId", "appId": -7, "key": "inv_org_sto_from_id", "name": "调拨出库存组织", "props": {"autoGenerated": false, "columnName": "inv_org_sto_from_id", "comment": "调拨出库存组织", "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"relationModelAlias": "org_inv_org_cf", "relationModelKey": "org_inv_org_cf", "relationType": "LINK"}, "required": false, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "invLocStoFromId", "appId": -7, "key": "inv_loc_sto_from_id", "name": "调拨出库存地点", "props": {"autoGenerated": false, "columnName": "inv_loc_sto_from_id", "comment": "调拨出库存地点", "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"relationModelAlias": "org_inv_loc_cf", "relationModelKey": "org_inv_loc_cf", "relationType": "LINK"}, "required": false, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "invOrgId", "appId": -7, "key": "inv_org_id", "name": "库存组织", "props": {"autoGenerated": false, "columnName": "inv_org_id", "comment": "库存组织", "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"relationModelAlias": "org_inv_org_cf", "relationModelKey": "org_inv_org_cf", "relationType": "LINK"}, "required": false, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "invLocId", "appId": -7, "key": "inv_loc_id", "name": "库存地点", "props": {"autoGenerated": false, "columnName": "inv_loc_id", "comment": "库存地点", "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"relationModelAlias": "org_inv_loc_cf", "relationModelKey": "org_inv_loc_cf", "relationType": "LINK"}, "required": false, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "dnItemStatus", "appId": -7, "key": "dn_item_status", "name": "行状态", "props": {"autoGenerated": false, "columnName": "dn_item_status", "comment": "行状态", "dictPros": {"dictValues": [{"label": "草稿", "value": "dni101"}, {"label": "待出库", "value": "dni102"}, {"label": "待开票", "value": "dni103"}, {"label": "部分开票", "value": "dni104"}, {"label": "完成", "value": "dni105"}, {"label": "草稿-采购", "value": "DNI101"}, {"label": "待收货-采购", "value": "DNI102"}, {"label": "待开票-采购", "value": "DNI103"}, {"label": "部分开票-采购", "value": "DNI104"}, {"label": "完成-采购", "value": "DNI105"}], "multiSelect": false}, "fieldType": "ENUM", "isSystemField": false, "length": 256, "required": true, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "isGrBlock", "appId": -7, "key": "is_gr_block", "name": "收货冻结", "props": {"autoGenerated": false, "columnName": "is_gr_block", "comment": "收货冻结", "dictPros": {"dictValues": [{"label": "1.正常；2.冻结", "value": "1.正常；2.冻结"}], "multiSelect": true}, "fieldType": "BOOL", "isSystemField": false, "length": 1, "required": true, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "isBilBlock", "appId": -7, "key": "is_bil_block", "name": "开票冻结", "props": {"autoGenerated": false, "columnName": "is_bil_block", "comment": "开票冻结", "fieldType": "BOOL", "isSystemField": false, "length": 1, "required": true, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "poSchlIdRef", "appId": -7, "key": "po_schl_id_ref", "name": "关联采购订单计划行编号", "props": {"autoGenerated": false, "columnName": "po_schl_id_ref", "comment": "关联采购订单计划行编号", "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"relationModelAlias": "ERP_SCM$pur_po_schl_tr", "relationModelKey": "ERP_SCM$pur_po_schl_tr", "relationType": "LINK"}, "required": false, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "poItemIdRef", "appId": -7, "key": "po_item_id_ref", "name": "关联采购订单行项目编号", "props": {"autoGenerated": false, "columnName": "po_item_id_ref", "comment": "关联采购订单行项目编号", "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"relationModelAlias": "ERP_SCM$pur_po_item_tr", "relationModelKey": "ERP_SCM$pur_po_item_tr", "relationType": "LINK"}, "required": false, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "poIdRef", "appId": -7, "key": "po_id_ref", "name": "关联采购订单编号", "props": {"autoGenerated": false, "columnName": "po_id_ref", "comment": "关联采购订单编号", "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"relationModelAlias": "ERP_SCM$pur_po_head_tr", "relationModelKey": "ERP_SCM$pur_po_head_tr", "relationType": "LINK"}, "required": false, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "dnPoItemTypeId", "appId": -7, "key": "dn_po_item_type_id", "name": "采购行项目类型", "props": {"autoGenerated": false, "columnName": "dn_po_item_type_id", "comment": "采购行项目类型", "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"relationModelAlias": "ERP_SCM$pur_po_item_type_cf", "relationModelKey": "ERP_SCM$pur_po_item_type_cf", "relationType": "LINK"}, "required": false, "unique": false}, "teamId": -7, "type": "DataStructField"}, {"alias": "trDnHId", "appId": -7, "key": "tr_dn_h_id", "name": "trDnHId", "props": {"autoGenerated": true, "columnName": "tr_dn_h_id", "comment": "trDnHId", "fieldType": "OBJECT", "isSystemField": false, "relationMeta": {"currentModelAlias": "tr_dn_item", "relationModelAlias": "tr_dn_h", "relationModelKey": "tr_dn_h", "relationType": "LINK"}, "required": false, "unique": false}, "teamId": -7, "type": "DataStructField"}], "createdAt": 1680177168997, "createdBy": 1635905327166333000, "desc": "交货单行项目表", "id": 70, "key": "tr_dn_item", "name": "交货单行项目表", "props": {"config": {"persist": false, "system": false}, "mainField": "dn_item_code", "tableName": "tr_dn_item", "type": "PERSIST"}, "teamId": -7, "type": "DataStruct", "updatedAt": 1680522526495, "updatedBy": 1635566363007983600}, "valueType": "MODEL"}], "type": "ConditionLeaf"}]}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "keMXH76gmRtseE25IJxR0"}], "executeLogic": "BindFlow"}, "buttonKey": "button_MhCyqOWxR2UiDkfs1FKq", "buttonType": "default", "confirmOn": "off", "label": "提交"}, "type": "Widget"}], "key": "vEvM9L6rUX0MgxUy4ShaY", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "3Y8P1FS9VLiE65UNau5rC", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "Q0U6JP0_iA_6KOY9_q7pc", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "5bixAJbwF-Q5FDaDMG1Is", "name": "Field", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": null}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "AJFqaPH1OMrUo3YRogAiH", "name": "Field", "props": {"componentProps": {"fieldAlias": "priceTypeLogCode", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "编码", "name": "priceTypeLogCode", "rules": [{"message": "请输入编码", "required": true}], "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "LZXj4MIXOH_XU0E1AAyV3", "name": "Field", "props": {"componentProps": {"fieldAlias": "priceStrategyLogId", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "定价策略日志id", "name": "priceStrategyLogId", "rules": [{"message": "请输入定价策略日志id", "required": true}], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "OfDAqcfm7nt28c6PK4ZuC", "name": "Field", "props": {"componentProps": {"fieldAlias": "priceStrategyLogCode", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "定价策略日志编码", "name": "priceStrategyLogCode", "rules": [{"message": "请输入定价策略日志编码", "required": true}], "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "mOFb1rTxZtri9If2Wecxx", "name": "Field", "props": {"componentProps": {"fieldAlias": "docCode", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "单据编号", "name": "docCode", "rules": [{"message": "请输入单据编号", "required": true}], "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "3GU7QbfeljHLBeQq__RXw", "name": "Field", "props": {"componentProps": {"fieldAlias": "docLineCode", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "单据行编号", "name": "docLineCode", "rules": [], "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "71TI4cGG0vdLoaM-uOOTV", "name": "Field", "props": {"componentProps": {"fieldAlias": "docType", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "单据类型", "name": "docType", "rules": [{"message": "请输入单据类型", "required": true}], "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "l0jJXQyZGBGOB5HPZne6C", "name": "Field", "props": {"componentProps": {"fieldAlias": "priceProcedureId", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "定价过程id", "name": "priceProcedureId", "rules": [{"message": "请输入定价过程id", "required": true}], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "Ml_W94D8ue2ohcbFgtmZK", "name": "Field", "props": {"componentProps": {"fieldAlias": "priceProcedureCode", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "定价过程编码", "name": "priceProcedureCode", "rules": [{"message": "请输入定价过程编码", "required": true}], "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "0ijprzFFmcluZWc8kqViC", "name": "Field", "props": {"componentProps": {"fieldAlias": "priceTypeId", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "定价类型id", "name": "priceTypeId", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "JwDd0s7U3OWNTBJBtk-yv", "name": "Field", "props": {"componentProps": {"fieldAlias": "priceTypeCode", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "定价类型编码", "name": "priceTypeCode", "rules": [], "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "lZFyxQ2S4HzMVYXyhUPKx", "name": "Field", "props": {"componentProps": {"fieldAlias": "priceTypeName", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "定价类型名称", "name": "priceTypeName", "rules": [{"message": "请输入定价类型名称", "required": true}], "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "HoGgaGNbIrDdZM_RGj5J6", "name": "Field", "props": {"componentProps": {"fieldAlias": "calculationType", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "计算类别", "name": "calculationType", "rules": [], "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "xZcOoSXAx5qv4aPefA_Pc", "name": "Field", "props": {"componentProps": {"fieldAlias": "priceUnitType", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "计价单位类别", "name": "priceUnitType", "rules": [], "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "bWnI7RG9HfTSDe-i_-lhX", "name": "Field", "props": {"componentProps": {"fieldAlias": "isAllowManual", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请选择"}, "hidden": false, "initialValue": false, "label": "允许手工录入", "name": "isAllowManual", "rules": [{"message": "请输入允许手工录入", "required": true}], "type": "BOOL", "width": 120}, "type": "Widget"}, {"children": [], "key": "L6-kNgPepqUXRApFQPJPD", "name": "Field", "props": {"componentProps": {"fieldAlias": "isHeaderCondition", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "抬头条件", "name": "isHeaderCondition", "rules": [], "type": "BOOL", "width": 120}, "type": "Widget"}, {"children": [], "key": "vkMPd86FfV4O6nkY_b21T", "name": "Field", "props": {"componentProps": {"fieldAlias": "matchSeqId", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "存取顺序id", "name": "matchSeqId", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "nZgHssEMXlkmKRTLMNAh6", "name": "Field", "props": {"componentProps": {"fieldAlias": "matchSeqCode", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "存取顺序编码", "name": "matchSeqCode", "rules": [], "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "4W41YldWd036l0keCaGlF", "name": "Field", "props": {"componentProps": {"fieldAlias": "<PERSON><PERSON>o", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "步骤", "name": "<PERSON><PERSON>o", "rules": [{"message": "请输入步骤", "required": true}], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "IHA7Eu2NQs-U4jlMalQSS", "name": "Field", "props": {"componentProps": {"fieldAlias": "fromNo", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "从", "name": "fromNo", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "EEKUrsOSS-SqH3NSHB531", "name": "Field", "props": {"componentProps": {"fieldAlias": "toNo", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "到", "name": "toNo", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "5kKsQpw9cWSE-tVaNtx1A", "name": "Field", "props": {"componentProps": {"fieldAlias": "baseCurrId", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "本位币种", "name": "baseCurrId", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "fXs6EUUdJwmI-i4aljyuR", "name": "Field", "props": {"componentProps": {"fieldAlias": "baseCurrValue", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": 6}, "hidden": false, "initialValue": null, "label": "本位币基础值", "name": "baseCurrValue", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "FXo32u6yrlsVdEYN7hjgl", "name": "Field", "props": {"componentProps": {"fieldAlias": "baseCurrPrice", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": 6}, "hidden": false, "initialValue": null, "label": "本位币单价", "name": "baseCurrPrice", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "-bJrT9hghGvsZxkvkjQsv", "name": "Field", "props": {"componentProps": {"fieldAlias": "baseCurrAmount", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": 6}, "hidden": false, "initialValue": null, "label": "本位币金额", "name": "baseCurrAmount", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "5tWeRntXCv-NfpxX2uhng", "name": "Field", "props": {"componentProps": {"fieldAlias": "isTransferValue", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请选择"}, "hidden": false, "initialValue": false, "label": "是否传值", "name": "isTransferValue", "rules": [], "type": "BOOL", "width": 120}, "type": "Widget"}, {"children": [], "key": "QA90kJBmb5ilqeIOYL1g4", "name": "Field", "props": {"componentProps": {"fieldAlias": "isOnly<PERSON>iew", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "仅显示", "name": "isOnly<PERSON>iew", "rules": [], "type": "BOOL", "width": 120}, "type": "Widget"}, {"children": [], "key": "l8QMrLAmgNyshfkuv4i4Z", "name": "Field", "props": {"componentProps": {"fieldAlias": "isMustHaveRecord", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "必须有记录", "name": "isMustHaveRecord", "rules": [], "type": "BOOL", "width": 120}, "type": "Widget"}, {"children": [], "key": "p2g0tHWvGtNlGaj2bX6UP", "name": "Field", "props": {"componentProps": {"fieldAlias": "subtotalType", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "小计项类型", "name": "subtotalType", "rules": [{"message": "请输入小计项类型", "required": true}], "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "Jj57ULUO8I07ta0GTnNqo", "name": "Field", "props": {"componentProps": {"fieldAlias": "formulaType", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "自定义函数类别", "name": "formulaType", "rules": [], "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "N0Y0gamnd7hc-5SQziaPS", "name": "Field", "props": {"componentProps": {"fieldAlias": "formula", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "自定义函数", "name": "formula", "rules": [], "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "rMRUErF5FMBLbh7uOw8k0", "name": "Field", "props": {"componentProps": {"fieldAlias": "matchFormula", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "匹配函数", "name": "matchFormula", "rules": [], "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "z31MAVgCdlg19y92hNFKJ", "name": "Field", "props": {"componentProps": {"fieldAlias": "matchStatus", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "匹配状态", "name": "matchStatus", "rules": [], "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "VkBXNBpsEpDzc4b8caYX4", "name": "Field", "props": {"componentProps": {"fieldAlias": "specialCode", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "特殊编码。用于记录单据code或物料code\u0000", "name": "specialCode", "rules": [], "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "IGb3Vxc2edTLjTGHsgDJC", "name": "Field", "props": {"componentProps": {"fieldAlias": "exchRate", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": 6}, "hidden": false, "initialValue": null, "label": "本位币汇率", "name": "exchRate", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "UmAMpzMxORxIJT36tVKJC", "name": "Field", "props": {"componentProps": {"fieldAlias": "baseCurrGrossPrice", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": 6}, "hidden": false, "initialValue": null, "label": "本位币单价(含税)", "name": "baseCurrGrossPrice", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "2K8-BsPmH1j6g2jkMDroo", "name": "Field", "props": {"componentProps": {"fieldAlias": "baseCurrGrossAmount", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": 6}, "hidden": false, "initialValue": null, "label": "本位币金额(含税)\u0000", "name": "baseCurrGrossAmount", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "IFicNX0CZhnhF-d6v7WFS", "name": "Field", "props": {"componentProps": {"fieldAlias": "docCurrId", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "单据币种", "name": "docCurrId", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "90u5_Ets0J0le6uNGzdgm", "name": "Field", "props": {"componentProps": {"fieldAlias": "docExchRate", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": 6}, "hidden": false, "initialValue": null, "label": "单据币汇率", "name": "docExchRate", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "oUVuEwk-3oI2qIYQrsYbe", "name": "Field", "props": {"componentProps": {"fieldAlias": "docCurrValue", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": 6}, "hidden": false, "initialValue": null, "label": "单据币基础值", "name": "docCurrValue", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "0e2eInBcU9eTDxZGCdwML", "name": "Field", "props": {"componentProps": {"fieldAlias": "docCurrPrice", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": 6}, "hidden": false, "initialValue": null, "label": "单据币单价(不含税)", "name": "docCurrPrice", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ORvnH4PsUtyRAq9g9sUWa", "name": "Field", "props": {"componentProps": {"fieldAlias": "docCurrGrossPrice", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": 6}, "hidden": false, "initialValue": null, "label": "单据币单价(含税)\u0000", "name": "docCurrGrossPrice", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "9656_6U1WlgKtrLkfhJbN", "name": "Field", "props": {"componentProps": {"fieldAlias": "docCurrAmount", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": 6}, "hidden": false, "initialValue": null, "label": "单据币金额(不含税)", "name": "docCurrAmount", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "Rdy3OL1U2uL4VgFkJ1aC2", "name": "Field", "props": {"componentProps": {"fieldAlias": "docCurrGrossAmount", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": 6}, "hidden": false, "initialValue": null, "label": "单据币金额(含税)\u0000", "name": "docCurrGrossAmount", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "8KjGsS7E8MXdvhIr0kC6T", "name": "Field", "props": {"componentProps": {"fieldAlias": "transfer<PERSON><PERSON>", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入"}, "hidden": false, "initialValue": null, "label": "传值json", "name": "transfer<PERSON><PERSON>", "rules": [], "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "xQuS2e3Taql6-2shdzC-p", "name": "Field", "props": {"componentProps": {"fieldAlias": "headerPriceAllocationType", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "抬头定价分摊方式", "name": "headerPriceAllocationType", "rules": [], "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "TiELWpd2SWU2cd3VdZC-d", "name": "Field", "props": {"componentProps": {"fieldAlias": "partnerTypeId", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "相关方类型", "name": "partnerTypeId", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "bceZkvXl3qbUEHmfx7lL5", "name": "Field", "props": {"componentProps": {"fieldAlias": "settItemTypeId", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "结算行项目类型", "name": "settItemTypeId", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "043nOG9EWFIl1jTVTiMLV", "name": "Field", "props": {"componentProps": {"fieldAlias": "taxRate", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": 6}, "hidden": false, "initialValue": null, "label": "税率", "name": "taxRate", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "UvoopJnfHyA1H_w2HEsXZ", "name": "Field", "props": {"componentProps": {"fieldAlias": "taxCode", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请输入", "precision": null}, "hidden": false, "initialValue": null, "label": "税码", "name": "taxCode", "rules": [], "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "rn_bYozNZRrx5FNxi_h8Z", "name": "Field", "props": {"componentProps": {"fieldAlias": "priceTypeClass", "modelAlias": "ERP_GEN$price_type_log", "placeholder": "请选择"}, "hidden": false, "initialValue": null, "label": "定价类别", "name": "priceTypeClass", "rules": [], "type": "SELECT", "width": 120}, "type": "Widget"}], "key": "OYKqMoPG_nUEsJTir8OL0", "name": "Fields", "props": {}, "type": "Meta"}], "key": "ad4150S2nzcOcgPO54kGv", "name": "Table", "props": {"flow": {"params": {"docLineCode$": "parentRecord?.soItemCode", "docType": "SLS"}, "serviceKey": "ERP_GEN$DOC_PRODUCE_RESULT_EVENT_SERVICE", "type": "InvokeService"}, "modelAlias": "ERP_GEN$price_type_log"}, "type": "Container"}], "key": "QdAEKj9OrrTB5vyWurP1l", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "KF_ntVKhUd3Hd_rF48RQJ", "name": "<PERSON><PERSON>", "props": {"actionConfig": {}, "buttonKey": "button_jT5KkRz9P7rQQDl2eS6p", "confirmOn": "off", "label": "取消", "type": "default"}, "type": "Widget"}, {"children": [], "key": "mtmkqy1xWHedp3pxNcHqk", "name": "<PERSON><PERSON>", "props": {"buttonKey": "button_qG08evQP_V2HIIEwFvSx", "confirmOn": "off", "label": "保存", "type": "default"}, "type": "Widget"}], "key": "FmW4mbSRNUiuw0EXRvPzZ", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "key": "NKx9m9A3NQWQKxd6xlQ74", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": true, "title": "弹窗标题"}, "type": "Container"}, {"children": [], "key": "PwD6SC5b7-nqJN7_Htrn6", "name": "FormGroup", "props": {}, "type": "Container"}], "key": "keMXH76gmRtseE25IJxR0", "name": "Page", "props": {"params": [{"label": "详情ID", "value": "recordId"}, {"label": "复制ID", "value": "copyId"}]}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "key": "ERP_SCM$zS4_gC8sR3KqTQZuGyqM_", "resources": [{"description": null, "key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}], "relations": [], "type": "Container"}, {"description": null, "key": "PwD6SC5b7-nqJN7_Htrn6", "label": "表单组", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}], "relations": [], "type": "Container"}, {"description": null, "key": "IAJHm0HiVSicP39XYxGxx", "label": "取消", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "vEvM9L6rUX0MgxUy4ShaY", "label": "页面底部", "type": "<PERSON>Footer"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "PiSugRAd3ocTzgT2MOwS4", "label": "获取定价", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "vEvM9L6rUX0MgxUy4ShaY", "label": "页面底部", "type": "<PERSON>Footer"}], "relations": [{"key": "ERP_SCM$DEL_DN_CALCULATE_PRICE_EVENT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "NSlz0Rf7LhRGwr0JaewvF", "label": "保存", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "vEvM9L6rUX0MgxUy4ShaY", "label": "页面底部", "type": "<PERSON>Footer"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "moEA-PEAjDXybZUkkc2cI", "label": "提交", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "vEvM9L6rUX0MgxUy4ShaY", "label": "页面底部", "type": "<PERSON>Footer"}], "relations": [{"key": "DN_CREATE_SLS_SUBMIT_EVENT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "5WpvAnC5CXk9EoJDqjbx1", "label": "ID", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "RoVI1BIZvepBcnl7EHYT8", "label": "创建人", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "esZZX4c90GQzsLUKpEYqG", "label": "更新人", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "kmYLeGAoKMVyAlXLQhNdY", "label": "创建时间", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "nC_s5RqzV9HrzYkVIRz9W", "label": "更新时间", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "AtyYELV-FDukj4GgL0q4b", "label": "版本号", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "OIF1tlcHNXLiBhLJLK0qZ", "label": "逻辑删除标识", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "flmZvQ1r8rwmScJ5h9Ufs", "label": "单据类型", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$del_dn_type_cf"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$del_dn_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "yQ0g_zIgAdWtKe0rRyAM3", "label": "业务类型", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "eihF2z5jiZr2I8vwFx1Is", "label": "单据编号", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ibz-otaKjjup5iy3VSLcA", "label": "单据日期", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "o4FxLr4eNGwzuGLoKK8sv", "label": "过账日期", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "_n3E9RK_JPdqDlvU9b9oJ", "label": "外部单据号", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "k4Vxadv85YP424KY9iBYE", "label": "参考单据", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "L6fmo9nVpr9aXMJbjdGki", "label": "库存组", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$org_inv_tm_cf"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$org_inv_tm_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "8NmaQQfPmymKviZbMl-Sk", "label": "交货方式", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "IP0BvXY-ue1BdYDH68t8K", "label": "创建方式", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "fqfHyFrZIM-lXyHsTiECy", "label": "交货状态", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "3gJBMEECbmF5nhcioZxg0", "label": "发票状态", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "pBHqZt0VuCzClnABhzQGU", "label": "审核时间", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "02r9hrAneKTUAbOTmtFxk", "label": "交货完成时间", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "OCqAlko2AUpP-_eJR-0DU", "label": "过账完成时间", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "Z1i-PfRHeHCSUICLKGZic", "label": "开票完成时间", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "HdfmT2BJHZMFLNyQ1zS2y", "label": "基本信息", "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}], "relations": [{"key": "ERP_GEN$DOC_PRODUCE_RESULT_EVENT_SERVICE", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "KF_ntVKhUd3Hd_rF48RQJ", "label": "取消", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "FmW4mbSRNUiuw0EXRvPzZ", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "mtmkqy1xWHedp3pxNcHqk", "label": "保存", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "FmW4mbSRNUiuw0EXRvPzZ", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}], "relations": [], "type": "Container"}, {"description": null, "key": "5bixAJbwF-Q5FDaDMG1Is", "label": "ID", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "wFB_34gaxyQHRrtdDBJzH", "label": "创建人", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "wFcfjhAEUniBQdgun_F0E", "label": "更新人", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "UyFpb5JbOTxzNE0lwffGx", "label": "创建时间", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "73RpxtFGovOOdhcwiemgo", "label": "更新时间", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "F6kLVk2XuGFUiIsn6993o", "label": "版本号", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "bfkFX89m2LD7uTCsCi0ip", "label": "逻辑删除标识", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "AJFqaPH1OMrUo3YRogAiH", "label": "编码", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "LZXj4MIXOH_XU0E1AAyV3", "label": "定价策略日志id", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "OfDAqcfm7nt28c6PK4ZuC", "label": "定价策略日志编码", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "mOFb1rTxZtri9If2Wecxx", "label": "单据编号", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "3GU7QbfeljHLBeQq__RXw", "label": "单据行编号", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "71TI4cGG0vdLoaM-uOOTV", "label": "单据类型", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "l0jJXQyZGBGOB5HPZne6C", "label": "定价过程id", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "Ml_W94D8ue2ohcbFgtmZK", "label": "定价过程编码", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "0ijprzFFmcluZWc8kqViC", "label": "定价类型id", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "JwDd0s7U3OWNTBJBtk-yv", "label": "定价类型编码", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "lZFyxQ2S4HzMVYXyhUPKx", "label": "定价类型名称", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "HoGgaGNbIrDdZM_RGj5J6", "label": "计算类别", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "xZcOoSXAx5qv4aPefA_Pc", "label": "计价单位类别", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "bWnI7RG9HfTSDe-i_-lhX", "label": "允许手工录入", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "L6-kNgPepqUXRApFQPJPD", "label": "抬头条件", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "vkMPd86FfV4O6nkY_b21T", "label": "存取顺序id", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "nZgHssEMXlkmKRTLMNAh6", "label": "存取顺序编码", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "4W41YldWd036l0keCaGlF", "label": "步骤", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "IHA7Eu2NQs-U4jlMalQSS", "label": "从", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "EEKUrsOSS-SqH3NSHB531", "label": "到", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "5kKsQpw9cWSE-tVaNtx1A", "label": "本位币种", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "fXs6EUUdJwmI-i4aljyuR", "label": "本位币基础值", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "FXo32u6yrlsVdEYN7hjgl", "label": "本位币单价", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "-bJrT9hghGvsZxkvkjQsv", "label": "本位币金额", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "5tWeRntXCv-NfpxX2uhng", "label": "是否传值", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "QA90kJBmb5ilqeIOYL1g4", "label": "仅显示", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "l8QMrLAmgNyshfkuv4i4Z", "label": "必须有记录", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "p2g0tHWvGtNlGaj2bX6UP", "label": "小计项类型", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "Jj57ULUO8I07ta0GTnNqo", "label": "自定义函数类别", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "N0Y0gamnd7hc-5SQziaPS", "label": "自定义函数", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "rMRUErF5FMBLbh7uOw8k0", "label": "匹配函数", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "z31MAVgCdlg19y92hNFKJ", "label": "匹配状态", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "VkBXNBpsEpDzc4b8caYX4", "label": "特殊编码。用于记录单据code或物料code\u0000", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "IGb3Vxc2edTLjTGHsgDJC", "label": "本位币汇率", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "UmAMpzMxORxIJT36tVKJC", "label": "本位币单价(含税)", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "2K8-BsPmH1j6g2jkMDroo", "label": "本位币金额(含税)\u0000", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "IFicNX0CZhnhF-d6v7WFS", "label": "单据币种", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "90u5_Ets0J0le6uNGzdgm", "label": "单据币汇率", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "oUVuEwk-3oI2qIYQrsYbe", "label": "单据币基础值", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "0e2eInBcU9eTDxZGCdwML", "label": "单据币单价(不含税)", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ORvnH4PsUtyRAq9g9sUWa", "label": "单据币单价(含税)\u0000", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "9656_6U1WlgKtrLkfhJbN", "label": "单据币金额(不含税)", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "Rdy3OL1U2uL4VgFkJ1aC2", "label": "单据币金额(含税)\u0000", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "8KjGsS7E8MXdvhIr0kC6T", "label": "传值json", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "xQuS2e3Taql6-2shdzC-p", "label": "抬头定价分摊方式", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TiELWpd2SWU2cd3VdZC-d", "label": "相关方类型", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "bceZkvXl3qbUEHmfx7lL5", "label": "结算行项目类型", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "043nOG9EWFIl1jTVTiMLV", "label": "税率", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "UvoopJnfHyA1H_w2HEsXZ", "label": "税码", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "rn_bYozNZRrx5FNxi_h8Z", "label": "定价类别", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "NKx9m9A3NQWQKxd6xlQ74", "label": "弹窗标题", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "QdAEKj9OrrTB5vyWurP1l", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "ad4150S2nzcOcgPO54kGv", "label": "表格", "type": "Table"}, {"key": "OYKqMoPG_nUEsJTir8OL0", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "xVWBVm-rMI22HIpFKsDi8", "label": "查看定价", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "mHUIRIpQXwSqchq_CJh9t", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "_LB4JoB8PJRlFREunUZWd", "label": "ID", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "wefZwJqkHS8OFVNMKnirU", "label": "创建人", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "r-T7-VYPLABNLMJDPvXVq", "label": "更新人", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "y0y6-U-9ofJgCUq6XrRXO", "label": "创建时间", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TOcvJb2zFthimXGzUiILm", "label": "更新时间", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "onaW6RoBuKweMFZPomSlx", "label": "版本号", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "65ilIKJzc3IjoFFgqn3ey", "label": "逻辑删除标识", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "vQKMV4OuvQW2SXKOwIMDd", "label": "交货行项目编号", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "MrifCukVq7wGI56NoHnh4", "label": "关联交货单编号", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "JGJWmIoC15CENNsbaTvSX", "label": "关联订单计划行编号", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "tr_so_s"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "tr_so_s"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "7GJggyqzMowGXmeggEZja", "label": "关联订单行项目编号", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "tr_so_i"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "tr_so_i"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "hCM27NDAwq-bu9owqb5eu", "label": "关联订单编号", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "tr_so_h"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "tr_so_h"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "D2_07geMDTgCenmN6nGmE", "label": "物料编码", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "0QSnlFP-oVZc8s_d8_2lN", "label": "物料名称", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "T9_tPwNv3A20FSYN9ROQB", "label": "行项目类型", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "cf_so_item_type"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "cf_so_item_type"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "VvlU-YGRYpHMhdkVlR3R7", "label": "基本单位", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_uom_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "YmHwNRvXE7MYs5JeIV8xV", "label": "计划交货数量", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "bHeYHu0ZYA1YMFFUUv-Wn", "label": "计划交货日期", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "3F35msMcqKtghq0f05rr6", "label": "交货数量", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "C2lm1tpzHwmP1twdCoTPx", "label": "签收确认数量", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "-TL5oy12NtuCFu81MbdDv", "label": "累计开票数量", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "XrSu5NoXZKBbFPkbJL9M_", "label": "调拨出库存组织", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "J8NcB8WyDcAEnShJeR6r5", "label": "调拨出库存地点", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "iLsm3U9yDAsrg_OR5X_2i", "label": "库存组织", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "g65om-kSCNS-QAcXzf27p", "label": "库存地点", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "sys_common$org_struct_md"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "sZRJwHDVwcCqnjKKxNmo8", "label": "行状态", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "3NCAADfV4Nu3ynbUBQZnq", "label": "收货冻结", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "FKj48bpnWSDvbybv4nJ4-", "label": "开票冻结", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "SgOEMk9nlBzgkKU2rmlFL", "label": "关联采购订单计划行编号", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$pur_po_schl_tr"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$pur_po_schl_tr"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "pSdJ4KGx8ojruh7e1_C3D", "label": "关联采购订单行项目编号", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$pur_po_item_tr"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$pur_po_item_tr"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "IJmX3HcmGz_W_hXpLgY1s", "label": "关联采购订单编号", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$pur_po_head_tr"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$pur_po_head_tr"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "2ZFlx5Og7pfSEwaRh9sOc", "label": "采购行项目类型", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$pur_po_item_type_cf"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$pur_po_item_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "XyePtSUKW1Aigvf5LAfLP", "label": "关联销售计划行模型", "path": [{"key": "keMXH76gmRtseE25IJxR0", "label": "页面", "type": "Page"}, {"key": "CH_pQe4OW-FJxdoMur64k", "label": "表单组", "type": "FormGroup"}, {"key": "vtvdNJUQjGF6m23xoWza8", "label": "交货行项目编号", "type": "FormGroupItem"}, {"key": "A88hrjqeWkQF7U-hFKBzu", "label": "自定义表单字段", "type": "CustomFormField"}, {"key": "47NTD2HbHKf8ZbawAeuX4", "label": "表格表单", "type": "TableForm"}, {"key": "3nOew8naa3gXYGhCvjxLg", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "tr_so_s"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "tr_so_s"}, "type": "SystemService"}], "type": "Container"}], "title": "edit", "type": "FORM"}}