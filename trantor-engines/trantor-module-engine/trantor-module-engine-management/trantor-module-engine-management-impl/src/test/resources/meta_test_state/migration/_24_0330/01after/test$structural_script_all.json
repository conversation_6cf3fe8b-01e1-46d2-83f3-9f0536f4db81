{"type": "View", "name": "Structural script for all", "children": [], "props": {"content": {"children": [{"key": "b1", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "SystemAction", "name": "append"}]}}, "type": "Widget"}, {"key": "b2", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "SystemAction", "name": "remove"}]}}, "type": "Widget"}], "key": "111", "name": "Page", "props": {"showBack": true, "showFooter": true, "showHeader": true}, "type": "Container"}}}