{"type": "View", "name": "PageTitle Children Text And Other 1", "children": [], "props": {"content": {"children": [{"children": [], "key": "sys_common$ORG_BUSINESS_TYPE_VIEW-detailView-page-title-text", "name": "Text", "props": {"value$": "((data?.code || \"\") + (data?.name || \"\")) || \"组织业务类型表详情\""}}, {"children": [], "key": "sys_common$ORG_BUSINESS_TYPE_VIEW-eoEerkEnrnnLvWxdn_YMH", "name": "Status", "props": {"text$": "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.text", "type$": "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.type"}}], "key": "sys_common$ORG_BUSINESS_TYPE_VIEW-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}}}