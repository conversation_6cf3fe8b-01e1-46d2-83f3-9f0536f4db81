{"type": "View", "name": "Remove Hidden Fields 3", "children": [], "props": {"content": {"children": [{"children": [], "key": "ERP_PRD$PRD_VRS_MD_VIEW-editView-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "编辑生产版本", "useExpression": false}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "ERP_PRD$PRD_VRS_MD_VIEW-total-config-container-ERP_PRD$prd_prdvrs_header_md_dto-for-widget-invOrgId", "name": "FormField", "props": {"componentProps": {"columns": ["orgDimensionId", "attrGroupId", "orgCode", "orgName", "orgParentId", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "attrGroupCode", "orgSort"], "fieldAlias": "invOrgId", "label": "选择库存组织", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_PRD$prd_prdvrs_header_md_dto", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "displayComponentProps": {"modelAlias": "sys_common$org_struct_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"columns": ["orgDimensionCode", "orgDimensionName", "orgDimensionDescribe", "status"], "fieldAlias": "orgDimensionId", "label": "选择组织维度id", "labelField": "orgDimensionCode", "modelAlias": "sys_common$org_dimension_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "组织维度id", "name": "orgDimensionId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["code", "name", "attrClassCode", "status", "remark", "attrClassId"], "fieldAlias": "attrGroupId", "label": "选择属性分组", "labelField": "name", "modelAlias": "ERP_GEN$gen_attr_group_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "属性分组", "name": "attrGroupId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "组织编码", "name": "orgCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "组织名称", "name": "orgName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["orgDimensionId", "attrGroupId", "orgCode", "orgName", "orgParentId", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "attrGroupCode", "orgSort"], "fieldAlias": "orgParentId", "label": "选择父组织id", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "父组织id", "name": "orgParentId", "required": false, "type": "SELFRELATION", "width": 120}, {"componentProps": {"fieldAlias": "orgEnableDate", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "启用日期", "name": "orgEnableDate", "required": false, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "orgStatus", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "状态", "name": "orgStatus", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"defaultValue": true, "fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "initialValue": true, "isRelationColumn": true, "label": "是否叶子节点", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"fieldAlias": "def1", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段1", "name": "def1", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def2", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段2", "name": "def2", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def3", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段3", "name": "def3", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def4", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段4", "name": "def4", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def5", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段5", "name": "def5", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def6", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段6", "name": "def6", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def7", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段7", "name": "def7", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def8", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段8", "name": "def8", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def9", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段9", "name": "def9", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def10", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段10", "name": "def10", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def11", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段11", "name": "def11", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def12", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段12", "name": "def12", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def13", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段13", "name": "def13", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def14", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段14", "name": "def14", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def15", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段15", "name": "def15", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def16", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段16", "name": "def16", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def17", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段17", "name": "def17", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def18", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段18", "name": "def18", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def19", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段19", "name": "def19", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def20", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "预留字段20", "name": "def20", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgDimensionCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "组织维度编码", "name": "orgDimensionCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "attachment1", "modelAlias": "sys_common$org_struct_md", "placeholder": "请上传"}, "hidden": true, "isRelationColumn": true, "label": "附件预留字段1", "name": "attachment1", "required": false, "type": "ATTACHMENT", "width": 120}, {"componentProps": {"fieldAlias": "attachment2", "modelAlias": "sys_common$org_struct_md", "placeholder": "请上传"}, "hidden": true, "isRelationColumn": true, "label": "附件预留字段2", "name": "attachment2", "required": false, "type": "ATTACHMENT", "width": 120}, {"componentProps": {"fieldAlias": "attrGroupCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "属性分组编码", "name": "attrGroupCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgSort", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "排序", "name": "orgSort", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "id", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "ERP_PRD$user", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "ERP_PRD$user", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "originOrgId", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "所属组织", "name": "originOrgId", "required": true, "type": "NUMBER", "width": 120}], "filterFields": [{"componentProps": {"columns": ["orgDimensionCode", "orgDimensionName", "orgDimensionDescribe", "status"], "fieldAlias": "orgDimensionId", "label": "选择组织维度id", "labelField": "orgDimensionCode", "modelAlias": "sys_common$org_dimension_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "label": "组织维度id", "name": "orgDimensionId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["code", "name", "attrClassCode", "status", "remark", "attrClassId"], "fieldAlias": "attrGroupId", "label": "选择属性分组", "labelField": "name", "modelAlias": "ERP_GEN$gen_attr_group_cf", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "label": "属性分组", "name": "attrGroupId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "orgCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织编码", "name": "orgCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgName", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": false, "label": "组织名称", "name": "orgName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["orgDimensionId", "attrGroupId", "orgCode", "orgName", "orgParentId", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "attrGroupCode", "orgSort"], "fieldAlias": "orgParentId", "label": "选择父组织id", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "label": "父组织id", "name": "orgParentId", "required": false, "type": "SELFRELATION", "width": 120}, {"componentProps": {"fieldAlias": "orgEnableDate", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "label": "启用日期", "name": "orgEnableDate", "required": false, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "orgStatus", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "label": "状态", "name": "orgStatus", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"defaultValue": true, "fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "initialValue": true, "label": "是否叶子节点", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"fieldAlias": "def1", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "预留字段1", "name": "def1", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def2", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "预留字段2", "name": "def2", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def3", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "预留字段3", "name": "def3", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def4", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "预留字段4", "name": "def4", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def5", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "预留字段5", "name": "def5", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def6", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "预留字段6", "name": "def6", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def7", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "预留字段7", "name": "def7", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def8", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "预留字段8", "name": "def8", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def9", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "预留字段9", "name": "def9", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def10", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "预留字段10", "name": "def10", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def11", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "预留字段11", "name": "def11", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def12", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "预留字段12", "name": "def12", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def13", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "预留字段13", "name": "def13", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def14", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "预留字段14", "name": "def14", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def15", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "预留字段15", "name": "def15", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def16", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "预留字段16", "name": "def16", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def17", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "预留字段17", "name": "def17", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def18", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "预留字段18", "name": "def18", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def19", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "预留字段19", "name": "def19", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "def20", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "预留字段20", "name": "def20", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgDimensionCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "组织维度编码", "name": "orgDimensionCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "attachment1", "modelAlias": "sys_common$org_struct_md", "placeholder": "请上传"}, "hidden": true, "label": "附件预留字段1", "name": "attachment1", "required": false, "type": "ATTACHMENT", "width": 120}, {"componentProps": {"fieldAlias": "attachment2", "modelAlias": "sys_common$org_struct_md", "placeholder": "请上传"}, "hidden": true, "label": "附件预留字段2", "name": "attachment2", "required": false, "type": "ATTACHMENT", "width": 120}, {"componentProps": {"fieldAlias": "attrGroupCode", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "属性分组编码", "name": "attrGroupCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "orgSort", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "排序", "name": "orgSort", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "id", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "ERP_PRD$user", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "ERP_PRD$user", "parentModelAlias": "sys_common$org_struct_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "sys_common$org_struct_md", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "originOrgId", "modelAlias": "sys_common$org_struct_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "所属组织", "name": "originOrgId", "required": true, "type": "NUMBER", "width": 120}], "labelField": "orgName", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "sys_common$org_struct_md", "showFilterFields": true, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "XAnGIWqpkXpszRhAw2x70", "leftValue": {"fieldType": "Text", "title": "业务类型编码", "type": "VarValue", "val": "orgBusinessTypeCode", "value": "sys_common$org_struct_md.orgBusinessTypeCode", "valueType": "VAR", "varVal": "orgBusinessTypeCode", "varValue": [{"valueKey": "orgBusinessTypeCode", "valueName": "orgBusinessTypeCode"}]}, "operator": "EQ", "rightValue": {"constValue": "INV_ORG", "fieldType": "Text", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "iWuxm4b7MLXLp1V5KSaPR", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "zT0oJIH2JzlZ1Qk20iJJM", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": "$context"}, "editComponentType": "RelationSelect", "hidden": false, "label": "库存组织", "lookup": [{"action": null, "conditionGroup": {"conditions": [{"conditions": [{"id": "5I2oyeUipWvfLLQ5JaML4", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "操作类型", "type": "VarValue", "val": "opType", "value": "ERP_PRD$prd_prdvrs_header_md_dto.opType", "valueType": "VAR", "varVal": "opType", "varValue": [{"valueKey": "opType", "valueName": "opType"}]}, "operator": "EQ", "rightValue": {"constValue": "UPDATE", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "2cQCpAzdyyYU27-Wpt5xD", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "kAYqU2lyNjYY53plt64cF", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "readOnly": true, "required": false}, "key": "ERP_PRD$PRD_VRS_MD_VIEW-f-pFui2nBMUZ7U3JTyudd", "operator": null, "trigger": "onload", "valueRules": null}, {"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "ERP_PRD$PRD_VRS_MD_VIEW-2dzkCPzmq4w8CkC69L1xp", "valueRules": null}], "name": "invOrgId", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_VRS_MD_VIEW-total-config-container-ERP_PRD$prd_prdvrs_header_md_dto-for-widget-genMatPrdMdId", "name": "FormField", "props": {"componentProps": {"columns": ["matCode", "<PERSON><PERSON><PERSON>", "matOldId", "genMatTypeCfId", "baseUomId", "weightUomId", "grossWeight", "netWeight", "volumUomId", "matVolum", "cateId", "effectiveDate", "brandId", "attriId", "statusId", "batchRelv", "isBatchDetermination", "genCharaClassId", "shelfLife", "charas", "isAutoCountPlan", "cost", "matCodeExt", "invUomId", "status"], "fieldAlias": "matId", "label": "选择物料编码", "labelField": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "parentModelAlias": "ERP_PRD$prd_prdvrs_header_md_dto", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "displayComponentProps": {"labelField": ["<PERSON><PERSON><PERSON>"], "modelAlias": "ERP_GEN$gen_mat_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "matCode", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "物料编码", "name": "matCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "物料名称", "name": "<PERSON><PERSON><PERSON>", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "matOldId", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "旧物料 Material号", "name": "matOldId", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["matTypeCode", "matTypeName"], "fieldAlias": "genMatTypeCfId", "label": "选择物料类型", "labelField": "matTypeCode", "modelAlias": "ERP_GEN$gen_mat_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "物料类型", "name": "genMatTypeCfId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "baseUomId", "label": "选择基本计量单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "基本计量单位", "name": "baseUomId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "weightUomId", "label": "选择重量单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "重量单位", "name": "weightUomId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "grossWeight", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "毛重", "name": "grossWeight", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "netWeight", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "净重", "name": "netWeight", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "volumUomId", "label": "选择体积单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "体积单位", "name": "volumUomId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "matVolum", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "体积", "name": "matVolum", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["matCateCode", "matCateName", "<PERSON><PERSON><PERSON><PERSON>", "status", "genCounClassMatTaxItemCfId", "qualificationsGroupId", "isLimitPoQualifications", "isLimitSoQualifications", "isLimitCtQualifications", "path", "wqMatType", "mat<PERSON>ate<PERSON><PERSON>nt"], "fieldAlias": "cateId", "label": "选择类目ID", "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "类目ID", "name": "cateId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "effectiveDate", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "有效起始期", "name": "effectiveDate", "required": false, "type": "DATE", "width": 120}, {"componentProps": {"columns": ["brandCode", "brandName", "brandPic"], "fieldAlias": "brandId", "label": "选择品牌ID", "labelField": "brandCode", "modelAlias": "ERP_GEN$gen_brand_md", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "品牌ID", "name": "brandId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["attriCode", "attriName", "attriValue", "status", "isRequired", "genMatCateMdId"], "fieldAlias": "attriId", "label": "选择属性ID", "labelField": "attriCode", "modelAlias": "ERP_GEN$gen_attri_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "属性ID", "name": "attriId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["matStatusCode", "matStatusName", "eventId", "msgType"], "fieldAlias": "statusId", "label": "选择跨工厂物料状态", "labelField": "matStatusCode", "modelAlias": "ERP_GEN$gen_mat_status_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "跨工厂物料状态", "name": "statusId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": false, "fieldAlias": "batchRelv", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": true, "initialValue": false, "isRelationColumn": true, "label": "批次管理", "name": "batchRelv", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"defaultValue": false, "fieldAlias": "isBatchDetermination", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": true, "initialValue": false, "isRelationColumn": true, "label": "是否必须匹配批次确定", "name": "isBatchDetermination", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"columns": ["code", "charaClassType", "name", "status", "remark"], "fieldAlias": "genCharaClassId", "label": "选择物料分类类别", "labelField": "name", "modelAlias": "ERP_GEN$gen_chara_class_md", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "物料分类类别", "name": "genCharaClassId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "shelfLife", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入", "precision": 0}, "hidden": true, "isRelationColumn": true, "label": "物料保质期", "name": "shelfLife", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "charas", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "特征数据", "name": "charas", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "isAutoCountPlan", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "是否支持自动生成盘点方案", "name": "isAutoCountPlan", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"fieldAlias": "cost", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入", "precision": 6}, "hidden": true, "isRelationColumn": true, "label": "成本价", "name": "cost", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "matCodeExt", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "外部物料编码", "name": "matCodeExt", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "invUomId", "label": "选择默认存储单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "默认存储单位", "name": "invUomId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "status", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "状态", "name": "status", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "ERP_PRD$user", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "ERP_PRD$user", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "originOrgId", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "所属组织", "name": "originOrgId", "required": true, "type": "NUMBER", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "matCode", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "label": "物料编码", "name": "matCode", "required": true, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": false, "label": "物料名称", "name": "<PERSON><PERSON><PERSON>", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "matOldId", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": true, "label": "旧物料 Material号", "name": "matOldId", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["matTypeCode", "matTypeName"], "fieldAlias": "genMatTypeCfId", "label": "选择物料类型", "labelField": "matTypeCode", "modelAlias": "ERP_GEN$gen_mat_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "label": "物料类型", "name": "genMatTypeCfId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "baseUomId", "label": "选择基本计量单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "label": "基本计量单位", "name": "baseUomId", "required": true, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "weightUomId", "label": "选择重量单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "label": "重量单位", "name": "weightUomId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "grossWeight", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": true, "label": "毛重", "name": "grossWeight", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "netWeight", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": true, "label": "净重", "name": "netWeight", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "volumUomId", "label": "选择体积单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "label": "体积单位", "name": "volumUomId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "matVolum", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": true, "label": "体积", "name": "matVolum", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["matCateCode", "matCateName", "<PERSON><PERSON><PERSON><PERSON>", "status", "genCounClassMatTaxItemCfId", "qualificationsGroupId", "isLimitPoQualifications", "isLimitSoQualifications", "isLimitCtQualifications", "path", "wqMatType", "mat<PERSON>ate<PERSON><PERSON>nt"], "fieldAlias": "cateId", "label": "选择类目ID", "labelField": "matCateName", "modelAlias": "ERP_GEN$gen_mat_cate_md", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "label": "类目ID", "name": "cateId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "effectiveDate", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": true, "label": "有效起始期", "name": "effectiveDate", "required": false, "type": "DATE", "width": 120}, {"componentProps": {"columns": ["brandCode", "brandName", "brandPic"], "fieldAlias": "brandId", "label": "选择品牌ID", "labelField": "brandCode", "modelAlias": "ERP_GEN$gen_brand_md", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "label": "品牌ID", "name": "brandId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["attriCode", "attriName", "attriValue", "status", "isRequired", "genMatCateMdId"], "fieldAlias": "attriId", "label": "选择属性ID", "labelField": "attriCode", "modelAlias": "ERP_GEN$gen_attri_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "label": "属性ID", "name": "attriId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["matStatusCode", "matStatusName", "eventId", "msgType"], "fieldAlias": "statusId", "label": "选择跨工厂物料状态", "labelField": "matStatusCode", "modelAlias": "ERP_GEN$gen_mat_status_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "label": "跨工厂物料状态", "name": "statusId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"defaultValue": false, "fieldAlias": "batchRelv", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": true, "initialValue": false, "label": "批次管理", "name": "batchRelv", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"defaultValue": false, "fieldAlias": "isBatchDetermination", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": true, "initialValue": false, "label": "是否必须匹配批次确定", "name": "isBatchDetermination", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"columns": ["code", "charaClassType", "name", "status", "remark"], "fieldAlias": "genCharaClassId", "label": "选择物料分类类别", "labelField": "name", "modelAlias": "ERP_GEN$gen_chara_class_md", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "label": "物料分类类别", "name": "genCharaClassId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "shelfLife", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入", "precision": 0}, "hidden": true, "label": "物料保质期", "name": "shelfLife", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "charas", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": true, "label": "特征数据", "name": "charas", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "isAutoCountPlan", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": true, "label": "是否支持自动生成盘点方案", "name": "isAutoCountPlan", "required": false, "type": "BOOL", "width": 120}, {"componentProps": {"fieldAlias": "cost", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入", "precision": 6}, "hidden": true, "label": "成本价", "name": "cost", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "matCodeExt", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": true, "label": "外部物料编码", "name": "matCodeExt", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "invUomId", "label": "选择默认存储单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "label": "默认存储单位", "name": "invUomId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "status", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": true, "label": "状态", "name": "status", "required": false, "type": "SELECT", "width": 120}, {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "ERP_PRD$user", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "ERP_PRD$user", "parentModelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "originOrgId", "modelAlias": "ERP_GEN$gen_mat_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "所属组织", "name": "originOrgId", "required": true, "type": "NUMBER", "width": 120}], "flow": {"context$": "$context", "outputParams": {"expression": "data", "serviceKey": "ERP_PRD$PRD_MAT_FIND_GEN_MAT_BY_ORG_EVENT_SERVICE", "type": "expression"}, "params": [{"elements": [{"fieldAlias": "invOrgId", "fieldName": "工厂（库存组织）", "fieldType": "OBJECT", "valueConfig": {"action": {"selector": "invOrgId", "target": "ERP_PRD$PRD_VRS_MD_VIEW-total-config-ERP_PRD$prd_prdvrs_header_md_dto"}, "type": "action"}}, {"fieldAlias": "factoryStatusId", "fieldName": "工厂状态", "fieldType": "OBJECT"}, {"fieldAlias": "invLocId", "fieldName": "生产存储库存地点", "fieldType": "OBJECT"}, {"fieldAlias": "productionCycle", "fieldName": "生产周期（天）", "fieldType": "NUMBER"}, {"fieldAlias": "inspectionCycle", "fieldName": "质检周期（天）", "fieldType": "NUMBER"}, {"fieldAlias": "plannedCalendarId", "fieldName": "计划日历", "fieldType": "OBJECT"}, {"fieldAlias": "componentScrap", "fieldName": "组件报废率%", "fieldType": "NUMBER"}, {"fieldAlias": "genMatMdId", "fieldName": "genMatMdId", "fieldType": "OBJECT"}, {"fieldAlias": "prdUomId", "fieldName": "生产计量单位", "fieldType": "OBJECT"}, {"fieldAlias": "postInspection", "fieldName": "过账到检验库存", "fieldType": "BOOL"}, {"fieldAlias": "insufficientDeliveryTolerance", "fieldName": "不足交货容差", "fieldType": "NUMBER"}, {"fieldAlias": "excessiveDeliveryTolerance", "fieldName": "过度交货容差", "fieldType": "NUMBER"}, {"fieldAlias": "unlimitedOverDelivery", "fieldName": "未限制的过度交货", "fieldType": "BOOL"}, {"fieldAlias": "procurementType", "fieldName": "采购类型", "fieldType": "ENUM"}, {"fieldAlias": "backFlush", "fieldName": "反冲标识", "fieldType": "ENUM"}, {"fieldAlias": "individualColl", "fieldName": "独立/集中", "fieldType": "ENUM"}, {"fieldAlias": "prdMangerId", "fieldName": "生产管理员", "fieldType": "OBJECT"}, {"fieldAlias": "batchParaId", "fieldName": "批次参数文件", "fieldType": "OBJECT"}, {"fieldAlias": "serialParaId", "fieldName": "序列号参数文件", "fieldType": "OBJECT"}, {"fieldAlias": "prdvrsList", "fieldName": "生产版本", "fieldType": "Array"}, {"fieldAlias": "prdBomHeader", "fieldName": "Bom头表关联", "fieldType": "Array"}, {"fieldAlias": "id", "fieldName": "ID", "fieldType": "NUMBER"}, {"fieldAlias": "created<PERSON>y", "fieldName": "创建人", "fieldType": "OBJECT"}, {"fieldAlias": "updatedBy", "fieldName": "更新人", "fieldType": "OBJECT"}, {"fieldAlias": "createdAt", "fieldName": "创建时间", "fieldType": "DATE"}, {"fieldAlias": "updatedAt", "fieldName": "更新时间", "fieldType": "DATE"}, {"fieldAlias": "version", "fieldName": "版本号", "fieldType": "NUMBER"}, {"fieldAlias": "deleted", "fieldName": "逻辑删除标识", "fieldType": "NUMBER"}, {"fieldAlias": "originOrgId", "fieldName": "所属组织", "fieldType": "NUMBER"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "ERP_GEN$gen_mat_prd_md", "required": true}], "serviceKey": "ERP_PRD$PRD_MAT_FIND_GEN_MAT_BY_ORG_EVENT_SERVICE", "type": "InvokeService"}, "labelField": ["<PERSON><PERSON><PERSON>"], "modelAlias": "ERP_GEN$gen_mat_md", "showFilterFields": true, "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "RelationSelect", "hidden": false, "label": "物料编码", "lookup": [{"conditionGroup": {"conditions": [{"conditions": [{"id": "MQ0AdqbolmT0UB6kPDpvh", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "操作类型", "type": "VarValue", "val": "opType", "value": "ERP_PRD$prd_prdvrs_header_md_dto.opType", "valueType": "VAR", "varVal": "opType", "varValue": [{"valueKey": "opType", "valueName": "opType"}]}, "operator": "EQ", "rightValue": {"constValue": "UPDATE", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "OmwTmjcVmkkZ_4zwpQn9P", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "gmz7YIoJ9ep6_pV4X70yu", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"hidden": false, "readOnly": true, "required": true}, "key": "ERP_PRD$PRD_VRS_MD_VIEW-5y5blNDFcCqpeRDc8YCKr", "trigger": "onload", "valueRules": null}, {"action": "clear", "conditionGroup": {"conditions": [{"conditions": [{"id": "4cmvsOSkv0RNc3WEecVt_", "leftValue": {"fieldType": "Model", "scope": "form", "title": "库存组织", "type": "VarValue", "val": "invOrgId", "value": "ERP_PRD$prd_prdvrs_header_md_dto.invOrgId", "valueType": "VAR", "varVal": "invOrgId", "varValue": [{"valueKey": "invOrgId", "valueName": "invOrgId"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}], "id": "76kC_APm2vAAT3W7hh9XL", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "l1aHSq832INOJDf-szFsW", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "CiDFFg6z_I8XWwkAGa-Sx", "operator": "clear", "trigger": "onchange", "valueRules": null}], "name": "matId", "required": false, "rules": [], "type": "OBJECT", "width": 120}, "type": "Widget"}], "key": "ERP_PRD$PRD_VRS_MD_VIEW-total-config-container-ERP_PRD$prd_prdvrs_header_md_dto", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "title": false}, "type": "Layout"}, {"children": [], "key": "ERP_PRD$PRD_VRS_MD_VIEW-GwlJa7P-Y53bUNBJZYvmn", "name": "FormField", "props": {"componentProps": {"fieldAlias": "opType", "modelAlias": "ERP_PRD$prd_prdvrs_header_md_dto", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "Select", "label": "操作类型", "lookup": [{"fieldRules": {"hidden": true, "readOnly": true, "required": false}, "key": "ERP_PRD$PRD_VRS_MD_VIEW-HsVoIOW_VIWmN49sbRteg", "valueRules": null}], "name": "opType", "required": false, "type": "SELECT", "width": 120}, "type": "Widget"}, {"children": [{"children": [{"children": [{"children": [], "key": "ERP_PRD$PRD_VRS_MD_VIEW-8wV1VuP23OqRNChBSJhf7", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_PRD$PRD_VRS_MD_VIEW-rL4HH1B5X0xabaJPXbKmu", "name": "Field", "props": {"componentProps": {"fieldAlias": "prdVrsCode", "modelAlias": "ERP_PRD$prd_prdvrs_item_md_dto", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "生产版本号", "lookup": [{"fieldRules": {"hidden": false, "readOnly": true, "required": false}, "key": "ERP_PRD$PRD_VRS_MD_VIEW-JeVDdq7kydkllX3Gy5dPO", "valueRules": null}], "name": "prdVrsCode", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_VRS_MD_VIEW-ybuLVvU5M3AsV-gyn7-W1", "name": "Field", "props": {"componentProps": {"fieldAlias": "prdVrsName", "modelAlias": "ERP_PRD$prd_prdvrs_item_md_dto", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "label": "版本名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "ERP_PRD$PRD_VRS_MD_VIEW-D8GfNUZdYpnQIQHWY5FeS", "valueRules": null}], "name": "prdVrsName", "required": false, "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_VRS_MD_VIEW-o8_JGLc46TdjLwQ0nc08o", "name": "Field", "props": {"componentProps": {"fieldAlias": "lotSizeFrom", "modelAlias": "ERP_PRD$prd_prdvrs_item_md_dto", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "hidden": false, "initialValue": 0, "label": "批量从", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "kUxZPf3wFd6pvQJPS5y-G", "valueRules": null}], "name": "lotSizeFrom", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_VRS_MD_VIEW-AP-KJR6f3UYNC_G7_vm-E", "name": "Field", "props": {"componentProps": {"fieldAlias": "lotSizeTo", "modelAlias": "ERP_PRD$prd_prdvrs_item_md_dto", "placeholder": "请输入"}, "displayComponentType": "Number", "editComponentProps": {"shape": "line"}, "editComponentType": "InputNumber", "hidden": false, "initialValue": *********, "label": "批量至", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "tHItLRnVbiaoZ8jw-nTua", "valueRules": null}], "name": "lotSizeTo", "required": false, "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_VRS_MD_VIEW-ZLfsl6yiBXEj0mkAdyHHK", "name": "Field", "props": {"componentProps": {"columns": ["vrsCode", "vrsName", "validFrom", "validTo", "invOrgId", "routingsName", "bomHeaderId", "routingsStatusId", "baseQty", "batchFrom", "batchTo", "uomId", "routingsUsageId", "matId", "routingsType"], "fieldAlias": "routingsVrsId", "label": "选择工艺路线版本号", "labelField": "vrsCode", "modelAlias": "ERP_GEN$gen_routings_header_md", "parentModelAlias": "ERP_PRD$prd_prdvrs_item_md_dto", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "displayComponentProps": {"modelAlias": "ERP_GEN$gen_routings_header_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "vrsCode", "modelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "版本号", "name": "vrsCode", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "vrsName", "modelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请输入"}, "hidden": false, "isRelationColumn": true, "label": "版本描述", "name": "vrsName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"fieldAlias": "validFrom", "modelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "有效期始", "name": "validFrom", "required": false, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "validTo", "modelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "有效期至", "name": "validTo", "required": false, "type": "DATE", "width": 120}, {"componentProps": {"columns": ["orgDimensionId", "attrGroupId", "orgCode", "orgName", "orgParentId", "orgEnableDate", "orgStatus", "<PERSON><PERSON><PERSON><PERSON>", "def1", "def2", "def3", "def4", "def5", "def6", "def7", "def8", "def9", "def10", "def11", "def12", "def13", "def14", "def15", "def16", "def17", "def18", "def19", "def20", "orgDimensionCode", "attachment1", "attachment2", "attrGroupCode", "orgSort"], "fieldAlias": "invOrgId", "label": "选择库存组织", "labelField": "orgName", "modelAlias": "sys_common$org_struct_md", "parentModelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "库存组织", "name": "invOrgId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "routingsName", "modelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "工艺路线名称", "name": "routingsName", "required": false, "type": "TEXT", "width": 120}, {"componentProps": {"columns": ["matCode", "<PERSON><PERSON><PERSON>", "invOrgId", "dateFrom", "dateTo", "matId", "bomUseId", "vrsCode", "vrsName", "baseQty", "batchFrom", "batchTo", "statusId", "genMatPrdMdId"], "fieldAlias": "bomHeaderId", "label": "选择BOM头表关联", "labelField": "vrsCode", "modelAlias": "ERP_GEN$gen_bom_head_md", "parentModelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "BOM头表关联", "name": "bomHeaderId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["statusCode", "statusName", "isPrd", "isQc", "isCost"], "fieldAlias": "routingsStatusId", "label": "选择工艺路线状态", "labelField": "statusName", "modelAlias": "ERP_GEN$gen_routings_status_cf", "parentModelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": false, "isRelationColumn": true, "label": "工艺路线状态", "name": "routingsStatusId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "baseQty", "modelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请输入", "precision": 2}, "hidden": true, "isRelationColumn": true, "label": "基础数量", "name": "baseQty", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "batchFrom", "modelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "批量从", "name": "batchFrom", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"fieldAlias": "batchTo", "modelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "批量到", "name": "batchTo", "required": false, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["uomType", "uomCode", "uomDesc"], "fieldAlias": "uomId", "label": "选择计量单位", "labelField": "uomDesc", "modelAlias": "ERP_GEN$gen_uom_type_cf", "parentModelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "计量单位", "name": "uomId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["usageCode", "usageName", "isEngineerDesign", "isPrd", "isQc", "isCost"], "fieldAlias": "routingsUsageId", "label": "选择工艺路线用途", "labelField": "usageName", "modelAlias": "ERP_GEN$gen_routings_usage_cf", "parentModelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": false, "isRelationColumn": true, "label": "工艺路线用途", "name": "routingsUsageId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["matCode", "<PERSON><PERSON><PERSON>", "genMatTypeCfId", "baseUomId", "weightUomId", "grossWeight", "netWeight", "volumUomId", "matVolum", "cateId", "effectiveDate", "brandId", "attriId", "statusId", "batchRelv", "isBatchDetermination", "genCharaClassId", "shelfLife", "charas", "isAutoCountPlan", "cost", "matCodeExt", "invUomId", "status"], "fieldAlias": "matId", "label": "选择物料编码", "labelField": "<PERSON><PERSON><PERSON>", "modelAlias": "ERP_GEN$gen_mat_md", "parentModelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": false, "isRelationColumn": true, "label": "物料编码", "name": "matId", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["typeCode", "typeName"], "fieldAlias": "routingsType", "label": "选择工艺路线类别", "labelField": "typeName", "modelAlias": "ERP_GEN$gen_routings_type_cf", "parentModelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": false, "isRelationColumn": true, "label": "工艺路线类别", "name": "routingsType", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请输入"}, "hidden": true, "isRelationColumn": true, "label": "ID", "name": "id", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "ERP_PRD$user", "parentModelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "创建人", "name": "created<PERSON>y", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "ERP_PRD$user", "parentModelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_PRD$SYS_FindDataByIdService", "searchServiceKey": "ERP_PRD$SYS_PagingDataService"}}, "hidden": true, "isRelationColumn": true, "label": "更新人", "name": "updatedBy", "required": false, "type": "OBJECT", "width": 120}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "创建时间", "name": "createdAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请选择"}, "hidden": true, "isRelationColumn": true, "label": "更新时间", "name": "updatedAt", "required": true, "type": "DATE", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "版本号", "name": "version", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "逻辑删除标识", "name": "deleted", "required": true, "type": "NUMBER", "width": 120}, {"componentProps": {"defaultValue": 0, "fieldAlias": "originOrgId", "modelAlias": "ERP_GEN$gen_routings_header_md", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "isRelationColumn": true, "label": "所属组织", "name": "originOrgId", "required": false, "type": "NUMBER", "width": 120}], "labelField": "vrsCode", "modelAlias": "ERP_GEN$gen_routings_header_md", "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "cd5ifqP2M7zgE3Sh4bUQ-", "leftValue": {"fieldType": "Model", "title": "物料编码", "type": "VarValue", "val": "matId", "value": "ERP_GEN$gen_routings_header_md.matId", "valueType": "VAR", "varVal": "matId", "varValue": [{"valueKey": "matId", "valueName": "matId"}]}, "operator": "EQ", "rightValue": {"fieldType": "Object", "scope": "form", "target": "ERP_PRD$PRD_VRS_MD_VIEW-total-config-ERP_PRD$prd_prdvrs_header_md_dto", "title": "matId", "type": "VarValue", "val": "matId", "value": "matId", "valueType": "VAR", "varVal": "matId", "varValue": [{"valueKey": "matId", "valueName": "matId"}]}, "type": "ConditionLeaf"}], "id": "ll2ZrfokPYX8TI8ouKcvj", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "GBLVDbkqHimagSWli9LhM", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": "$context"}, "editComponentType": "RelationSelect", "hidden": false, "label": "工艺路线版本号", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "pj-4l9f58W_kFoGamxVSE", "valueRules": null}], "name": "routingsVrsId", "required": false, "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_VRS_MD_VIEW-VK3FY_Jch1dwQrNR-aidE", "name": "Field", "props": {"componentProps": {"fieldAlias": "validFrom", "modelAlias": "ERP_PRD$prd_prdvrs_item_md_dto", "placeholder": "请选择"}, "displayComponentType": "Date", "editComponentType": "DatePicker", "hidden": false, "label": "有效期始", "lookup": [{"action": "get", "fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "QALOty2XtQaFwRFGRp_tQ", "operator": "FIELD", "trigger": "all", "valueRules": {"scope": "", "type": "FIELD", "val": "currentDate", "value": "currentDate", "valueType": "system"}}], "name": "validFrom", "required": false, "type": "DATE", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_VRS_MD_VIEW-46j92ieYz66LFrtOVNRnR", "name": "Field", "props": {"componentProps": {"fieldAlias": "validTo", "modelAlias": "ERP_PRD$prd_prdvrs_item_md_dto", "placeholder": "请选择"}, "displayComponentType": "Date", "editComponentType": "DatePicker", "hidden": false, "initialValue": "9999-12-30T16:00:00.000Z", "label": "有效期至", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "UuypDGummQuzlKB8HtT_j", "trigger": "onload", "valueRules": null}], "name": "validTo", "required": false, "type": "DATE", "width": 120}, "type": "Widget"}], "key": "ERP_PRD$PRD_VRS_MD_VIEW-eoL9OtamdQxtJDjgiFMcb", "name": "Fields", "props": {}, "type": "Meta"}], "key": "ERP_PRD$PRD_VRS_MD_VIEW-D94U10yQXy7_LYVrTByjg", "name": "TableForm", "props": {"creatorPosition": "bottom", "fieldName": "itemList", "fields": [], "hideCreator": false, "label": "表格表单", "lookup": [{"action": "get", "conditionGroup": {"conditions": [{"conditions": [{"id": "TF8s_zMETLofLgyiOCG33", "leftValue": {"fieldType": "Model", "scope": "form", "title": "物料编码", "type": "VarValue", "val": "matId", "value": "ERP_PRD$prd_prdvrs_header_md_dto.matId", "valueType": "VAR", "varVal": "matId", "varValue": [{"valueKey": "matId", "valueName": "matId"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}, {"id": "Bua7wW1XjqWYJMesLkswi", "leftValue": {"fieldType": "Model", "scope": "form", "title": "库存组织", "type": "VarValue", "val": "invOrgId", "value": "ERP_PRD$prd_prdvrs_header_md_dto.invOrgId", "valueType": "VAR", "varVal": "invOrgId", "varValue": [{"valueKey": "invOrgId", "valueName": "invOrgId"}]}, "operator": "IS_NOT_NULL", "type": "ConditionLeaf"}, {"id": "D8CD52ZGOfozrKIbSskXF", "leftValue": {"fieldType": "Enum", "scope": "form", "title": "操作类型", "type": "VarValue", "val": "opType", "value": "ERP_PRD$prd_prdvrs_header_md_dto.opType", "valueType": "VAR", "varVal": "opType", "varValue": [{"valueKey": "opType", "valueName": "opType"}]}, "operator": "NEQ", "rightValue": {"constValue": "UPDATE", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "qEN-gNRfRuyVUADkHAIMl", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "lTUnPEJ0LTUhl78nF_klU", "logicOperator": "OR", "type": "ConditionGroup"}, "fieldRules": {"disabled": false, "hidden": false, "required": false}, "key": "ERP_PRD$PRD_VRS_MD_VIEW-2FcEA4TiaWkKRiV6DR-iS", "operator": "SERVICE", "trigger": "onchange", "valueRules": {"scope": null, "serviceParams": {"entryNewParams": [{"elements": [{"fieldAlias": "invOrgId", "fieldName": "库存组织", "fieldType": "OBJECT", "valueConfig": {"action": {"selector": "invOrgId", "target": "ERP_PRD$PRD_VRS_MD_VIEW-total-config-ERP_PRD$prd_prdvrs_header_md_dto"}, "type": "action"}}, {"fieldAlias": "matId", "fieldName": "物料编码", "fieldType": "OBJECT", "valueConfig": {"action": {"selector": "matId", "target": "ERP_PRD$PRD_VRS_MD_VIEW-total-config-ERP_PRD$prd_prdvrs_header_md_dto"}, "type": "action"}}, {"fieldAlias": "itemList", "fieldName": "版本列表", "fieldType": "Array"}, {"fieldAlias": "opType", "fieldName": "操作类型", "fieldType": "ENUM"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "ERP_PRD$prd_prdvrs_header_md_dto", "required": true}], "outputParams": {"expression": "data.itemList", "serviceKey": "ERP_PRD$PRD_VRS_MD_DETAIL_EVENT_SERVICE", "type": "expression"}}, "type": "SERVICE", "val": "ERP_PRD$PRD_VRS_MD_DETAIL_EVENT_SERVICE", "value": "ERP_PRD$PRD_VRS_MD_DETAIL_EVENT_SERVICE"}}], "modelAlias": "ERP_PRD$prd_prdvrs_item_md_dto"}, "type": "Widget"}], "key": "ERP_PRD$PRD_VRS_MD_VIEW-UxAnZnyfZeaCxcpOYXD5z", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}}, "type": "Meta"}], "key": "ERP_PRD$PRD_VRS_MD_VIEW-SYAeSCqr8yNtJw505ZnE2", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "showSplit": true, "title": "生产版本"}, "type": "Layout"}], "key": "ERP_PRD$PRD_VRS_MD_VIEW-total-config-ERP_PRD$prd_prdvrs_header_md_dto", "name": "FormGroup", "props": {"actionConfig": [], "colon": false, "flow": {"children": [{"context$": "$context", "outputParams": {"expression": "data", "serviceKey": "ERP_PRD$PRD_VRS_MD_DETAIL_EVENT_SERVICE", "type": "expression"}, "params": [{"elements": [{"fieldAlias": "invOrgId", "fieldName": "库存组织", "fieldType": "OBJECT", "valueConfig": {"expression": "route.query.invOrgId", "type": "expression"}}, {"fieldAlias": "matId", "fieldName": "物料编码", "fieldType": "OBJECT", "valueConfig": {"expression": "route.query.matId", "type": "expression"}}, {"fieldAlias": "itemList", "fieldName": "版本列表", "fieldType": "Array"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "ERP_PRD$prd_prdvrs_header_md_dto", "required": true}], "serviceKey": "ERP_PRD$PRD_VRS_MD_DETAIL_EVENT_SERVICE", "test$": "true", "type": "InvokeService"}], "type": "Condition"}, "modelAlias": "ERP_PRD$prd_prdvrs_header_md_dto", "serviceKey": "ERP_PRD$SYS_FindDataByIdService"}, "type": "Container"}, {"children": [{"children": [], "key": "ERP_PRD$PRD_VRS_MD_VIEW-editView-footer-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "previous"}]}, "label": "取消", "showCondition": {}}, "type": "Widget"}, {"children": [], "key": "ERP_PRD$PRD_VRS_MD_VIEW-editView-footer-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [], "service": "ERP_PRD$PRD_VRS_MD_SAVE_EVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Message", "message": "保存成功！"}, {"action": "Close", "target": ["ROOT"]}, {"action": "RefreshTab", "target": ["previous"]}, {"action": "Refresh", "target": ["ERP_PRD$PRD_VRS_MD_VIEW-editView", "ERP_PRD$PRD_VRS_MD_VIEW-total-config-ERP_PRD$prd_prdvrs_header_md_dto"]}], "executeLogic": "BindService"}, "label": "保存", "showCondition": {}, "type": "primary"}, "type": "Widget"}], "key": "ERP_PRD$PRD_VRS_MD_VIEW-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "ERP_PRD$PRD_VRS_MD_VIEW-editView", "name": "Page", "props": {"showBack": true, "showFooter": true, "showHeader": true}, "type": "Container"}}}