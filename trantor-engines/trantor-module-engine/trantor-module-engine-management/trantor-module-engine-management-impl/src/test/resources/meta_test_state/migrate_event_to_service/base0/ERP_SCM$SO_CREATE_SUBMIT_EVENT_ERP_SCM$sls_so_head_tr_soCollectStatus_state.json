{"name": "ERP_SCM$sls_so_head_tr_soCollectStatus", "type": "StateConfig", "children": [], "props": {"name": "ERP_SCM$sls_so_head_tr_soCollectStatus", "modelName": null, "moduleKey": null, "fieldAlisa": "soCollectStatus", "modelAlisa": "ERP_SCM$sls_so_head_tr", "moduleName": null, "transitionNodes": [{"conditions": null, "conditionId": null, "sourceState": "WAIT_COLLECT", "targetState": "COLLECT_COMPLETE", "conditionExpress": null}, {"conditions": null, "conditionId": null, "sourceState": "INIT", "targetState": "COLLECT_COMPLETE", "conditionExpress": null}], "modelRelationInfos": null}}