{"type": "View", "name": "ERP_SCM$KAX3xk2N_r6FzKdZtbolq", "parentKey": "ERP_SCM$TERP_MIGRATE_DN01", "children": [], "props": {"containerSelect": {"F44bbybsdBZpxdGMpb7MQ": [], "iN-3YEq-YNR6nH8kw5R4I": [], "qVPGv8gqnWLI1L5X39afi": []}, "content": {"children": [{"children": [{"children": [{"children": [], "key": "5sNlvf46I2EQ2CsQtTrtX", "name": "<PERSON><PERSON>", "props": {"label": "查看", "onClick$": "systemActions.show($context)"}}, {"children": [], "key": "msibzjMi3QDRWSc9V6fDq", "name": "<PERSON><PERSON>", "props": {"label": "编辑", "onClick$": "systemActions.edit($context)"}}, {"children": [], "key": "sGC0zZ0p0s597HqVxpctp", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "tr_dn_h"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "record?.id", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "ERP_SCM$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ["qVPGv8gqnWLI1L5X39afi"]}, {"action": "Message", "level": "success", "message": "删除成功!"}], "executeLogic": "BindService"}, "label": "删除"}}, {"children": [], "key": "RRtvU68PA2ZXOjuH4YhNN", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindFlowConfig": {"params": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Text", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "service": "DN_PST_EVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "IICIU3hj0Qv0insAcT00_"}], "executeLogic": "BindFlow"}, "buttonKey": "button_zEHjKOl0RKyY_bOIPXsX", "buttonType": "default", "confirmOn": "off", "label": "发货过账", "variant": "primary"}, "type": "Widget"}, {"children": [], "key": "DpF64AZOLrZ8uzrAcvpfT", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindFlowConfig": {"params": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Text", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "service": "DN_CANCELPST_EVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "IICIU3hj0Qv0insAcT00_"}], "executeLogic": "BindFlow"}, "buttonKey": "button_fVXfnNAlJZr3TYCnQVdG", "buttonType": "default", "confirmOn": "off", "label": "取消过账", "variant": "primary"}, "type": "Widget"}, {"children": [], "key": "if1-mly1pAyUH0zKvbo8j", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "OpenView", "endLogicOpenViewConfig": {"page": {"key": "tkxjqgDbqEWu3xhePh-Rj", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "params": [{"key": "id", "value": [{"value": {"alias": "id", "defaultValue": null, "fieldKey": "id", "fieldName": "id", "fieldType": "Number", "hidden": false, "hiddenOnCreate": false, "label": "id", "modelKey": "cf_reason_type", "modelName": "原因代码表", "nodeKey": "none", "nodeTitle": "", "readonly": false, "required": true, "tips": "ID"}, "valueType": "MODEL"}]}], "type": "Modal"}}, "buttonKey": "button_du5fo1lnzlYHJ8BMAvoi", "buttonType": "default", "confirmOn": "off", "label": "开票冻结", "variant": "primary"}, "type": "Widget"}, {"children": [], "key": "2-UpdpoSmjEQ8Y5Hz_Yxy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "OpenView", "endLogicOpenViewConfig": {"page": {"key": "sjtXD9TIfj7J19RnJYXaB", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "params": [{"key": "id", "value": [{"value": {"alias": "id", "defaultValue": null, "fieldKey": "id", "fieldName": "id", "fieldType": "Number", "hidden": false, "hiddenOnCreate": false, "label": "id", "modelKey": "cf_reason_type", "modelName": "原因代码表", "nodeKey": "none", "nodeTitle": "", "readonly": false, "required": true, "tips": "ID"}, "valueType": "MODEL"}]}], "type": "Modal"}}, "buttonKey": "button_sGl__FqEli7gqm6eMabZ", "buttonType": "default", "confirmOn": "off", "label": "开票解冻", "variant": "primary"}, "type": "Widget"}, {"children": [], "key": "rKG7AfjIPP8i_NA6lV0AL", "name": "<PERSON><PERSON>", "props": {"actionConfig": {}, "buttonKey": "button_wBdT_bZnlw6G1VGBTS_a", "confirmOn": "off", "label": "单据追踪", "type": "default"}, "type": "Widget"}], "key": "lJY6-w_dxL4H4P5T96W-H", "name": "RecordActions", "props": {"label": "操作"}}, {"children": [{"children": [], "key": "d--OPIPue9ssf-WNRBup-", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "OpenView", "endLogicOpenViewConfig": {"page": {"key": "ERP_SCM$_mF4KFRq8yiSpPX696mlR", "type": "View"}, "type": "NewPage"}}, "buttonKey": "button_1BDpmuN_Ra4RX2FU0asP", "buttonType": "default", "confirmOn": "off", "label": "基于订单生成"}, "type": "Widget"}], "key": "HOVGbsNwT-yDK-0sdUMu0", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "KcqzbUU3mhVoMEyJo3WEh", "name": "Field", "props": {"componentProps": {"placeholder": "请输入ID"}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "P1f5uq76EtSYICWd00MX9", "name": "Field", "props": {"componentProps": {"columns": ["dnTypeName"], "label": "选择单据类型", "modelAlias": "ERP_SCM$del_dn_type_cf", "placeholder": "请选择单据类型", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": false, "label": "单据类型", "name": "dnType", "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "5sDug6kLgAKn_hZUKtC04", "name": "Field", "props": {"componentProps": {"placeholder": "请输入单据编号"}, "hidden": false, "label": "单据编号", "name": "dnCode", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "QnVby0yOtIrbiEXaBt5xo", "name": "Field", "props": {"componentProps": {"placeholder": "请选择单据日期"}, "hidden": false, "label": "单据日期", "name": "dnDateDoc", "type": "DATE", "width": 134}, "type": "Widget"}, {"children": [], "key": "rji-xlYX4LYEc3X0mZ8aq", "name": "Field", "props": {"componentProps": {"placeholder": "请选择过账日期"}, "hidden": false, "label": "过账日期", "name": "dnDatePst", "type": "DATE", "width": 134}, "type": "Widget"}, {"children": [], "key": "IpaFY_IRoMi_OGYv7foLf", "name": "Field", "props": {"componentProps": {"placeholder": "请输入外部单据号"}, "hidden": false, "label": "外部单据号", "name": "dnIdExt", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "D3al9gSGEh2dFGH07kXMe", "name": "Field", "props": {"componentProps": {"placeholder": "请输入参考单据"}, "hidden": false, "label": "参考单据", "name": "dnIdRef", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "uNaoaHLw9QM0U34RFh59u", "name": "Field", "props": {"componentProps": {"columns": [null], "label": "选择库存组", "modelAlias": "ERP_GEN$org_inv_tm_cf", "placeholder": "请选择库存组", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "hidden": false, "label": "库存组", "name": "invTmId", "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "0Wlm1FjuluwXnVEWsqP0D", "name": "Field", "props": {"componentProps": {"options": [{"label": "自提", "value": "01"}, {"label": "库发", "value": "02"}], "placeholder": "请选择交货方式"}, "hidden": false, "label": "交货方式", "name": "shpType", "type": "SELECT", "width": 116}, "type": "Widget"}, {"children": [], "key": "s8hJF8OVokTqPp0g_4Jyt", "name": "Field", "props": {"componentProps": {"options": [{"label": "系统", "value": "sys"}, {"label": "人工", "value": "manual"}], "placeholder": "请选择创建方式"}, "hidden": false, "label": "创建方式", "name": "dn<PERSON><PERSON><PERSON>", "type": "SELECT", "width": 116}, "type": "Widget"}, {"children": [], "key": "uBEji3ldvy4MABDaKWgHl", "name": "Field", "props": {"componentProps": {"options": [{"label": "待发货", "value": "dnhdel201"}, {"label": "部分发货", "value": "dnhdel202"}, {"label": "发货完成", "value": "dnhdel203"}, {"label": "草稿-采购", "value": "DNHDEL201"}, {"label": "待收货-采购", "value": "DNHDEL202"}, {"label": "部分收货-采购", "value": "DNHDEL203"}, {"label": "收货完成-采购", "value": "DNHDEL204"}], "placeholder": "请选择交货状态"}, "hidden": false, "label": "交货状态", "name": "dnStatusDel", "type": "SELECT", "width": 116}, "type": "Widget"}, {"children": [], "key": "lKYKvq-UreKcnapUy-nCi", "name": "Field", "props": {"componentProps": {"options": [{"label": "待开票", "value": "dnhbil201"}, {"label": "部分开票", "value": "dnhbil202"}, {"label": "开票完成", "value": "dnhbil203"}, {"label": "无需开票", "value": "dnhbil204"}, {"label": "草稿-采购", "value": "DNHINVOICE201"}, {"label": "待开票-采购", "value": "DNHINVOICE202"}, {"label": "部分开票-采购", "value": "DNHINVOICE203"}, {"label": "开票完成-采购", "value": "DNHINVOICE204"}], "placeholder": "请选择发票状态"}, "hidden": false, "label": "发票状态", "name": "dnStatusBil", "type": "SELECT", "width": 116}, "type": "Widget"}, {"children": [], "key": "ReuAluLFnTDcwgF0ib6xs", "name": "Field", "props": {"componentProps": {"placeholder": "请选择审核时间"}, "hidden": false, "label": "审核时间", "name": "dnTimeApply", "type": "TIME", "width": 90}, "type": "Widget"}, {"children": [], "key": "g4bg103N8aOA1kC7cAF9H", "name": "Field", "props": {"componentProps": {"placeholder": "请选择交货完成时间"}, "hidden": false, "label": "交货完成时间", "name": "dnTimeDel", "type": "TIME", "width": 90}, "type": "Widget"}, {"children": [], "key": "sFSA8dNbE6AuhDtu9xSTy", "name": "Field", "props": {"componentProps": {"placeholder": "请选择过账完成时间"}, "hidden": false, "label": "过账完成时间", "name": "dnTimeIssue", "type": "TIME", "width": 90}, "type": "Widget"}, {"children": [], "key": "-qaR-WI2iEM43LrKsf3FC", "name": "Field", "props": {"componentProps": {"placeholder": "请选择开票完成时间"}, "hidden": false, "label": "开票完成时间", "name": "dnTimeBil", "type": "TIME", "width": 90}, "type": "Widget"}], "key": "5wU_kCJxwt_5c4xx4oMNZ", "name": "Fields", "props": {}, "type": "Meta"}], "key": "qVPGv8gqnWLI1L5X39afi", "name": "Table", "props": {"filterFields": [{"componentProps": {"fieldAlias": "dnCode", "modelAlias": "tr_dn_h", "placeholder": "请输入"}, "label": "单据编号", "name": "dnCode", "type": "TEXT"}, {"componentProps": {"fieldAlias": "dnDateDoc", "modelAlias": "tr_dn_h", "placeholder": "请选择"}, "label": "单据日期", "name": "dnDateDoc", "type": "DATE"}, {"componentProps": {"fieldAlias": "dnDatePst", "modelAlias": "tr_dn_h", "placeholder": "请选择"}, "label": "过账日期", "name": "dnDatePst", "type": "DATE"}, {"componentProps": {"fieldAlias": "dnIdRef", "modelAlias": "tr_dn_h", "placeholder": "请输入"}, "label": "参考单据", "name": "dnIdRef", "type": "TEXT"}, {"componentProps": {"fieldAlias": "shpType", "modelAlias": "tr_dn_h", "placeholder": "请选择"}, "label": "交货方式", "name": "shpType", "type": "SELECT"}, {"componentProps": {"columns": ["btClass", "dnTypeCode", "dnTypeName", "remark", "numRangeTypeId", "dnItemNoIncrement", "delTypeRef", "purSettItemTypeCfId"], "fieldAlias": "dnType", "label": "选择", "labelField": "dnTypeName", "modelAlias": "ERP_SCM$del_dn_type_cf", "parentModelAlias": "tr_dn_h", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_SCM$SYS_FindDataByIdService", "searchServiceKey": "ERP_SCM$SYS_PagingDataService"}}, "label": "单据类型", "name": "dnType", "type": "OBJECT"}], "flow": {"containerKey": "qVPGv8gqnWLI1L5X39afi", "context$": "$context", "modelAlias": "tr_dn_h", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "tr_dn_h"}}], "serviceKey": "ERP_SCM$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "tr_dn_h", "showFilterFields": true}, "type": "Container"}, {"children": [{"children": [{"children": [{"children": [], "key": "VVbVf_VfxOEloFtxok53f", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "j7OaxnL2n2mvLoQvU6cWg", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "I2rGH3t28CCQuqavvnm_Z", "name": "Field", "props": {"componentProps": {"options": [{"label": "销售", "value": "SLS"}, {"label": "采购", "value": "PUR"}], "placeholder": "请选择业务类别"}, "initialValue": null, "label": "业务类别", "name": "btClass", "rules": [{"message": "请输入业务类别", "required": true}], "type": "MULTISELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "Pr2ZorXISoHR8TaSGIpdq", "name": "Field", "props": {"componentProps": {"placeholder": "请输入类型"}, "initialValue": null, "label": "类型", "name": "reasonTypeCode", "rules": [{"message": "请输入类型", "required": true}], "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "k41T44OkqFNGsL4HFHsce", "name": "Field", "props": {"componentProps": {"placeholder": "请输入名称"}, "initialValue": null, "label": "名称", "name": "reasonName", "rules": [{"message": "请输入名称", "required": true}], "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "hlIqbRsXd_AkX2aDLQvdj", "name": "Field", "props": {"componentProps": {"placeholder": "请输入类别"}, "initialValue": null, "label": "类别", "name": "reasonCate", "rules": [{"message": "请输入类别", "required": true}], "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "x8jAxKam4myNfMxeIsrNQ", "name": "Field", "props": {"componentProps": {"placeholder": "请输入备注"}, "initialValue": null, "label": "备注", "name": "remark", "rules": [], "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "yUN-c04cv9E099qTpnBf0", "name": "Field", "props": {"componentProps": {"placeholder": "请选择是否开票冻结"}, "initialValue": null, "label": "是否开票冻结", "name": "isBilBlock", "rules": [], "type": "BOOL", "width": 120}, "type": "Widget"}, {"children": [], "key": "g96qxrj3dy3frHORbc2xE", "name": "Field", "props": {"componentProps": {"placeholder": "请选择是否交货冻结"}, "initialValue": null, "label": "是否交货冻结", "name": "isDelBlock", "rules": [], "type": "BOOL", "width": 120}, "type": "Widget"}], "key": "jed1wTlWptx6DEdEkTCst", "name": "Fields", "props": {}, "type": "Meta"}], "key": "iN-3YEq-YNR6nH8kw5R4I", "name": "Table", "props": {"allowRowSelect": true, "flow": {"containerKey": "iN-3YEq-YNR6nH8kw5R4I", "context$": "$context", "modelAlias": "cf_reason_type", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "cf_reason_type"}}], "serviceKey": "ERP_SCM$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "cf_reason_type"}, "type": "Container"}], "key": "JasMx7pn3D5gw-SafCdh-", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "FBSTlECiCdKftB3mcLZEQ", "name": "<PERSON><PERSON>", "props": {"buttonKey": "button_j_0aQShlCOI7GG2YCBqV", "buttonType": "default", "confirmOn": "off", "label": "取消"}, "type": "Widget"}, {"children": [], "key": "FGOTXselu4FeNA3-vkRhY", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindFlowConfig": {"params": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Text", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}, {"fieldAlias": "trReasonLinks", "fieldName": "trReasonLinks", "fieldType": "Text", "valueConfig": {"action": {"target": "iN-3YEq-YNR6nH8kw5R4I"}, "name": "trReasonLinks", "type": "action"}}], "service": "DN_UNBLOCK_BIL_EVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Close", "target": "ROOT"}], "executeLogic": "BindFlow"}, "buttonKey": "button_FSIBQO7fHCFVheTwJ1L2", "buttonType": "default", "confirmOn": "off", "label": "保存"}, "type": "Widget"}], "key": "hfGyhR6Bd0ObPJmVTuH-P", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "key": "sjtXD9TIfj7J19RnJYXaB", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": false, "title": "选择解冻原因"}, "type": "Container"}, {"children": [{"children": [{"children": [{"children": [], "key": "SxXt8OpbU9NpG1n9mMqHp", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "noFEYlwH8CUpAzD23fAR4", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "djDj9foB0FReCzNL1J5Qb", "name": "Field", "props": {"componentProps": {"options": [{"label": "销售", "value": "SLS"}, {"label": "采购", "value": "PUR"}], "placeholder": "请选择业务类别"}, "initialValue": null, "label": "业务类别", "name": "btClass", "rules": [{"message": "请输入业务类别", "required": true}], "type": "MULTISELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "CIWvLV7k1uB428W7JnCQJ", "name": "Field", "props": {"componentProps": {"placeholder": "请输入类型"}, "initialValue": null, "label": "类型", "name": "reasonTypeCode", "rules": [{"message": "请输入类型", "required": true}], "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "9ihUnh0kARoUbNSippbxo", "name": "Field", "props": {"componentProps": {"placeholder": "请输入名称"}, "initialValue": null, "label": "名称", "name": "reasonName", "rules": [{"message": "请输入名称", "required": true}], "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "tuEt6jLRvCBLJ4UHqUQFU", "name": "Field", "props": {"componentProps": {"placeholder": "请输入类别"}, "initialValue": null, "label": "类别", "name": "reasonCate", "rules": [{"message": "请输入类别", "required": true}], "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "fRivLsr4wlEhI3EpfdJlx", "name": "Field", "props": {"componentProps": {"placeholder": "请输入备注"}, "initialValue": null, "label": "备注", "name": "remark", "rules": [], "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "seHlju-wtVYoONKHAqmO7", "name": "Field", "props": {"componentProps": {"placeholder": "请选择是否开票冻结"}, "initialValue": null, "label": "是否开票冻结", "name": "isBilBlock", "rules": [], "type": "BOOL", "width": 120}, "type": "Widget"}, {"children": [], "key": "pZhc7RqEZHvbz51fXt0gw", "name": "Field", "props": {"componentProps": {"placeholder": "请选择是否交货冻结"}, "initialValue": null, "label": "是否交货冻结", "name": "isDelBlock", "rules": [], "type": "BOOL", "width": 120}, "type": "Widget"}], "key": "VohlyBKe6WEwUosG7Q3bd", "name": "Fields", "props": {}, "type": "Meta"}], "key": "F44bbybsdBZpxdGMpb7MQ", "name": "Table", "props": {"allowRowSelect": true, "flow": {"containerKey": "F44bbybsdBZpxdGMpb7MQ", "context$": "$context", "modelAlias": "cf_reason_type", "params": [{"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}, {"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "cf_reason_type"}}], "serviceKey": "ERP_SCM$SYS_PagingDataService", "type": "InvokeSystemService"}, "modelAlias": "cf_reason_type", "showScope": "filter"}, "type": "Container"}], "key": "1jWmgyHNIDm0v19itIoko", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "xUB0HivBLl3XmeCw5BWfu", "name": "<PERSON><PERSON>", "props": {"actionConfig": {}, "buttonKey": "button_g6iD2VZjXD1rjTlt9_FS", "buttonType": "default", "confirmOn": "off", "label": "取消"}, "type": "Widget"}, {"children": [], "key": "bxLHi-tfI_Xrn12nlrGGl", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindFlowConfig": {"params": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Text", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}, {"fieldAlias": "trReasonLinks", "fieldName": "trReasonLinks", "fieldType": "Text", "valueConfig": {"action": {"target": "F44bbybsdBZpxdGMpb7MQ"}, "name": "trReasonLinks", "type": "action"}}], "service": "DN_BLOCK_BIL_EVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "GoTo", "target": "ROOT"}], "executeLogic": "BindFlow"}, "buttonKey": "button_sy5duN557xPODsR9LsKw", "buttonType": "default", "confirmOn": "off", "label": "保存"}, "type": "Widget"}], "key": "lqFy0iqCj7AfTpxkFrx11", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "key": "tkxjqgDbqEWu3xhePh-Rj", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": false, "layout": "modal", "title": "选择冻结原因"}, "type": "Container"}], "key": "IICIU3hj0Qv0insAcT00_", "name": "Page", "props": {"showHeader": false}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "key": "ERP_SCM$KAX3xk2N_r6FzKdZtbolq", "resources": [{"description": null, "key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$del_dn_type_cf"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$del_dn_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "5sNlvf46I2EQ2CsQtTrtX", "label": "查看", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "lJY6-w_dxL4H4P5T96W-H", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "msibzjMi3QDRWSc9V6fDq", "label": "编辑", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "lJY6-w_dxL4H4P5T96W-H", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "sGC0zZ0p0s597HqVxpctp", "label": "删除", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "lJY6-w_dxL4H4P5T96W-H", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "RRtvU68PA2ZXOjuH4YhNN", "label": "发货过账", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "lJY6-w_dxL4H4P5T96W-H", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "DN_PST_EVENT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "DpF64AZOLrZ8uzrAcvpfT", "label": "取消过账", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "lJY6-w_dxL4H4P5T96W-H", "label": "按钮组", "type": "RecordActions"}], "relations": [{"key": "DN_CANCELPST_EVENT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "if1-mly1pAyUH0zKvbo8j", "label": "开票冻结", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "lJY6-w_dxL4H4P5T96W-H", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "2-UpdpoSmjEQ8Y5Hz_Yxy", "label": "开票解冻", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "lJY6-w_dxL4H4P5T96W-H", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "rKG7AfjIPP8i_NA6lV0AL", "label": "单据追踪", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "lJY6-w_dxL4H4P5T96W-H", "label": "按钮组", "type": "RecordActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "d--OPIPue9ssf-WNRBup-", "label": "基于订单生成", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "HOVGbsNwT-yDK-0sdUMu0", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "KcqzbUU3mhVoMEyJo3WEh", "label": "ID", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "plTIB3y3wQPCZUmB_7VTT", "label": "创建人", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "gymtlx0wPySRfULiqBfnv", "label": "更新人", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "I7AmSiPUfFt_qXtaaqdsQ", "label": "创建时间", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "pFzJERdp0xyRKtYuk89WL", "label": "更新时间", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "TyGhZH7pxNmguJf0fzAYa", "label": "版本号", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "SucfSbgvRqAoYGhD5hqOV", "label": "逻辑删除标识", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "P1f5uq76EtSYICWd00MX9", "label": "单据类型", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$del_dn_type_cf"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_SCM$del_dn_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "JYQh3UJwOlE8wmTSRJbc9", "label": "业务类型", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "5sDug6kLgAKn_hZUKtC04", "label": "单据编号", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "QnVby0yOtIrbiEXaBt5xo", "label": "单据日期", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "rji-xlYX4LYEc3X0mZ8aq", "label": "过账日期", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "IpaFY_IRoMi_OGYv7foLf", "label": "外部单据号", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "D3al9gSGEh2dFGH07kXMe", "label": "参考单据", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "uNaoaHLw9QM0U34RFh59u", "label": "库存组", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [{"key": "ERP_SCM$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$org_inv_tm_cf"}, "type": "SystemService"}, {"key": "ERP_SCM$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$org_inv_tm_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "0Wlm1FjuluwXnVEWsqP0D", "label": "交货方式", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "s8hJF8OVokTqPp0g_4Jyt", "label": "创建方式", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "uBEji3ldvy4MABDaKWgHl", "label": "交货状态", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "lKYKvq-UreKcnapUy-nCi", "label": "发票状态", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ReuAluLFnTDcwgF0ib6xs", "label": "审核时间", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "g4bg103N8aOA1kC7cAF9H", "label": "交货完成时间", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "sFSA8dNbE6AuhDtu9xSTy", "label": "过账完成时间", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "-qaR-WI2iEM43LrKsf3FC", "label": "开票完成时间", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "qVPGv8gqnWLI1L5X39afi", "label": "表格", "type": "Table"}, {"key": "5wU_kCJxwt_5c4xx4oMNZ", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "iN-3YEq-YNR6nH8kw5R4I", "label": "表格", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "sjtXD9TIfj7J19RnJYXaB", "label": "选择解冻原因", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "JasMx7pn3D5gw-SafCdh-", "label": "弹窗内容", "type": "ChildViewBody"}], "relations": [], "type": "Container"}, {"description": null, "key": "FBSTlECiCdKftB3mcLZEQ", "label": "取消", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "sjtXD9TIfj7J19RnJYXaB", "label": "选择解冻原因", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "hfGyhR6Bd0ObPJmVTuH-P", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "FGOTXselu4FeNA3-vkRhY", "label": "保存", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "sjtXD9TIfj7J19RnJYXaB", "label": "选择解冻原因", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "hfGyhR6Bd0ObPJmVTuH-P", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [{"key": "DN_UNBLOCK_BIL_EVENT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "F44bbybsdBZpxdGMpb7MQ", "label": "表格", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "tkxjqgDbqEWu3xhePh-Rj", "label": "选择冻结原因", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "1jWmgyHNIDm0v19itIoko", "label": "弹窗内容", "type": "ChildViewBody"}], "relations": [], "type": "Container"}, {"description": null, "key": "xUB0HivBLl3XmeCw5BWfu", "label": "取消", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "tkxjqgDbqEWu3xhePh-Rj", "label": "选择冻结原因", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "lqFy0iqCj7AfTpxkFrx11", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "bxLHi-tfI_Xrn12nlrGGl", "label": "保存", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "tkxjqgDbqEWu3xhePh-Rj", "label": "选择冻结原因", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "lqFy0iqCj7AfTpxkFrx11", "label": "弹窗底部", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "relations": [{"key": "DN_BLOCK_BIL_EVENT_SERVICE", "name": null, "props": null, "type": "Service"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "I2rGH3t28CCQuqavvnm_Z", "label": "业务类别", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "sjtXD9TIfj7J19RnJYXaB", "label": "选择解冻原因", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "JasMx7pn3D5gw-SafCdh-", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "iN-3YEq-YNR6nH8kw5R4I", "label": "表格", "type": "Table"}, {"key": "jed1wTlWptx6DEdEkTCst", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "Pr2ZorXISoHR8TaSGIpdq", "label": "类型", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "sjtXD9TIfj7J19RnJYXaB", "label": "选择解冻原因", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "JasMx7pn3D5gw-SafCdh-", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "iN-3YEq-YNR6nH8kw5R4I", "label": "表格", "type": "Table"}, {"key": "jed1wTlWptx6DEdEkTCst", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "k41T44OkqFNGsL4HFHsce", "label": "名称", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "sjtXD9TIfj7J19RnJYXaB", "label": "选择解冻原因", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "JasMx7pn3D5gw-SafCdh-", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "iN-3YEq-YNR6nH8kw5R4I", "label": "表格", "type": "Table"}, {"key": "jed1wTlWptx6DEdEkTCst", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "hlIqbRsXd_AkX2aDLQvdj", "label": "类别", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "sjtXD9TIfj7J19RnJYXaB", "label": "选择解冻原因", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "JasMx7pn3D5gw-SafCdh-", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "iN-3YEq-YNR6nH8kw5R4I", "label": "表格", "type": "Table"}, {"key": "jed1wTlWptx6DEdEkTCst", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "x8jAxKam4myNfMxeIsrNQ", "label": "备注", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "sjtXD9TIfj7J19RnJYXaB", "label": "选择解冻原因", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "JasMx7pn3D5gw-SafCdh-", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "iN-3YEq-YNR6nH8kw5R4I", "label": "表格", "type": "Table"}, {"key": "jed1wTlWptx6DEdEkTCst", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "yUN-c04cv9E099qTpnBf0", "label": "是否开票冻结", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "sjtXD9TIfj7J19RnJYXaB", "label": "选择解冻原因", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "JasMx7pn3D5gw-SafCdh-", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "iN-3YEq-YNR6nH8kw5R4I", "label": "表格", "type": "Table"}, {"key": "jed1wTlWptx6DEdEkTCst", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "g96qxrj3dy3frHORbc2xE", "label": "是否交货冻结", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "sjtXD9TIfj7J19RnJYXaB", "label": "选择解冻原因", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "JasMx7pn3D5gw-SafCdh-", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "iN-3YEq-YNR6nH8kw5R4I", "label": "表格", "type": "Table"}, {"key": "jed1wTlWptx6DEdEkTCst", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "djDj9foB0FReCzNL1J5Qb", "label": "业务类别", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "tkxjqgDbqEWu3xhePh-Rj", "label": "选择冻结原因", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "1jWmgyHNIDm0v19itIoko", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "F44bbybsdBZpxdGMpb7MQ", "label": "表格", "type": "Table"}, {"key": "VohlyBKe6WEwUosG7Q3bd", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "CIWvLV7k1uB428W7JnCQJ", "label": "类型", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "tkxjqgDbqEWu3xhePh-Rj", "label": "选择冻结原因", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "1jWmgyHNIDm0v19itIoko", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "F44bbybsdBZpxdGMpb7MQ", "label": "表格", "type": "Table"}, {"key": "VohlyBKe6WEwUosG7Q3bd", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "9ihUnh0kARoUbNSippbxo", "label": "名称", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "tkxjqgDbqEWu3xhePh-Rj", "label": "选择冻结原因", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "1jWmgyHNIDm0v19itIoko", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "F44bbybsdBZpxdGMpb7MQ", "label": "表格", "type": "Table"}, {"key": "VohlyBKe6WEwUosG7Q3bd", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "tuEt6jLRvCBLJ4UHqUQFU", "label": "类别", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "tkxjqgDbqEWu3xhePh-Rj", "label": "选择冻结原因", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "1jWmgyHNIDm0v19itIoko", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "F44bbybsdBZpxdGMpb7MQ", "label": "表格", "type": "Table"}, {"key": "VohlyBKe6WEwUosG7Q3bd", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "fRivLsr4wlEhI3EpfdJlx", "label": "备注", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "tkxjqgDbqEWu3xhePh-Rj", "label": "选择冻结原因", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "1jWmgyHNIDm0v19itIoko", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "F44bbybsdBZpxdGMpb7MQ", "label": "表格", "type": "Table"}, {"key": "VohlyBKe6WEwUosG7Q3bd", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "seHlju-wtVYoONKHAqmO7", "label": "是否开票冻结", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "tkxjqgDbqEWu3xhePh-Rj", "label": "选择冻结原因", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "1jWmgyHNIDm0v19itIoko", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "F44bbybsdBZpxdGMpb7MQ", "label": "表格", "type": "Table"}, {"key": "VohlyBKe6WEwUosG7Q3bd", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "pZhc7RqEZHvbz51fXt0gw", "label": "是否交货冻结", "path": [{"key": "IICIU3hj0Qv0insAcT00_", "label": "页面", "type": "Page"}, {"key": "tkxjqgDbqEWu3xhePh-Rj", "label": "选择冻结原因", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "1jWmgyHNIDm0v19itIoko", "label": "弹窗内容", "type": "ChildViewBody"}, {"key": "F44bbybsdBZpxdGMpb7MQ", "label": "表格", "type": "Table"}, {"key": "VohlyBKe6WEwUosG7Q3bd", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}}