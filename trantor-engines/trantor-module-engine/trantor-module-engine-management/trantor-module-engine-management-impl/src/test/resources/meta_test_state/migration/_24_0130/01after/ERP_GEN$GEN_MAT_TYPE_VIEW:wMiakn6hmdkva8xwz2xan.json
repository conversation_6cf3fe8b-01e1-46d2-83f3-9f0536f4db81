{"type": "View", "name": "LIST", "children": [], "props": {"containerSelect": {"ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form": [{"field": "version", "selectFields": null}, {"field": "matTypeCode", "selectFields": null}, {"field": "matTypeName", "selectFields": null}, {"field": "syncNode", "selectFields": null}, {"field": "syncParty", "selectFields": null}], "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf": [{"field": "matTypeCode", "selectFields": null}, {"field": "matTypeName", "selectFields": null}]}, "content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf-new", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "label": "新建", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-vGtaJfWTpMPR66GxpNIGU", "name": "DropdownButton", "props": {"items": [{"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "systemActions.delete($context, \"ERP_GEN$gen_mat_type_cf\")()"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-gen_mat_type_cf-multi-delete", "label": "删除"}, {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认统一启用吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_type_cf"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "ERP_GEN$SYS_MasterData_MultiEnableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf"}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-gen_mat_type_cf-multi-start", "label": "启用"}, {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认统一停用吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "model<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_type_cf"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "ERP_GEN$SYS_MasterData_MultiDisableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf"}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-gen_mat_type_cf-multi-stop", "label": "停用"}], "label": "批量操作"}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf-import", "name": "ImportButton", "props": {"addApprovalManageServiceProps": {"predictFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/gei/task/config/predict"}}, "deleteApprovalManageServiceProps": {"saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/workflow/v2/{param0}"}}, "editApprovalManageServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/workflow/v2/{param0}"}}, "isCustomServiceProps": {"isCustomFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "type": "InvokeService"}}, "label": "导入", "predictServiceProps": {"predictFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/gei/task/config/predict"}}, "saveServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/workflow/v2/{param0}"}}, "saveSubServiceProps": {"saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/workflow/v2/{param0}"}}, "serviceKey": "ERP_GEN$SYS_BatchCreateDataService", "serviceProps": {"downloadFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download"}, "isCustomFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "type": "InvokeService"}, "predictFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "type": "InvokeService"}, "saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-sub-model"}, "saveServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "type": "InvokeService"}, "saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-direct-by-oss"}, "saveSubServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "type": "InvokeService"}}}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf-export", "name": "ExportButton", "props": {"exportButtonServiceProps": {"getUserInfoFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/trantor/portal/user/current"}, "saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "label": "导出", "serviceProps": {"saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf-batch-actions", "name": "BatchActions", "props": {}}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-WIWrRWXNyN2F8JbIIF3Lw", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-mdTe1sOzyZC09rJiQedI6", "name": "Field", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-Z1blhDok2aVfcRUSRkmDf", "name": "Field", "props": {"componentProps": {"fieldAlias": "matTypeCode", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "物料类型编码", "name": "matTypeCode", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-190ei1VyOmSVKln_6FBAB", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "matTypeName", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "hidden": false, "label": "物料类型名称", "modelAlias": "ERP_GEN$gen_mat_type_cf", "name": "matTypeName", "type": "TEXT", "width": 146}, "type": "Widget"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-xzm1P3JrgS9nLUfUGYEK2", "name": "Fields", "props": {}, "type": "Meta"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf", "name": "Table", "props": {"filterFields": [{"componentProps": {"fieldAlias": "matTypeCode", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "物料类型编码", "name": "matTypeCode", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "matTypeName", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请输入"}, "hidden": false, "label": "物料类型名称", "name": "matTypeName", "type": "TEXT", "width": 146}], "flow": {"containerKey": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_type_cf", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_type_cf"}}, {"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_GEN$SYS_PagingDataService", "test$": "mode !== \"design\"", "type": "InvokeSystemService"}, "isProFilter": false, "label": "表格", "mode": "simple", "modelAlias": "ERP_GEN$gen_mat_type_cf", "onRowActionConfig": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}]}, "enable": true}, "pagination": {"size": "small"}, "serviceKey": "ERP_GEN$SYS_PagingDataService", "showConfigure": false, "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{((data?.code || \"\") + (data?.name || \"\")) || \"物料类型配置表详情\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "systemActions.delete($context, \"ERP_GEN$gen_mat_type_cf\")()"}, "label": "删除"}}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-actions-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "navigate({ action: 'new', query: { recordId: route.recordId } })"}, "label": "复制"}, "type": "Widget"}, {"children": [{"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-actions-disable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"name": "model<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_type_cf"}, {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}], "service": "ERP_GEN$SYS_MasterData_DisableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView"}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "label": "停用"}, "type": "Widget"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-actions-disable-show", "name": "Show", "props": {"value$": "data?.status === \"ENABLED\""}, "type": "Meta"}, {"children": [{"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-actions-enable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_type_cf"}, {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}], "service": "ERP_GEN$SYS_MasterData_EnableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView"}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "label": "启用"}, "type": "Widget"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-actions-enable-show", "name": "Show", "props": {"value$": "data?.status === \"INACTIVE\" || data?.status === \"DISABLED\""}, "type": "Meta"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "label": "编辑", "showCondition": {}, "type": "primary"}, "type": "Widget"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-TojTlsvKcX4LsSlzlAzNt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "matTypeCode", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请输入"}, "editable": false, "label": "物料类型编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "RlIbTDUiVi5uBCH7RfvD3", "valueRules": null}], "name": "matTypeCode", "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-bPAOzbVFK2i4KJrIPOKzU", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "matTypeName", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请输入"}, "editable": false, "label": "物料类型名称", "name": "matTypeName", "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-IHoLAERfWhxY010zA2mYC", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "syncNode", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请选择"}, "editable": false, "label": "同步节点", "name": "syncNode", "type": "MULTISELECT"}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-XyyUQBUsrd6SfLuVYn3Xw", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "syncParty", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请选择"}, "editable": false, "label": "同步三方", "name": "syncParty", "type": "MULTISELECT"}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-MRtEsdU6JgNYnxD9Gi2Ge", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请输入"}, "displayComponentType": "Number", "editable": false, "label": "ID", "lookup": [{"fieldRules": {"hidden": true, "readOnly": false, "required": true}, "key": "rdTiLQwJQjxCVGS-EZr_J", "valueRules": null}], "name": "id", "type": "NUMBER"}, "type": "Widget"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-gen_mat_type_cf-detail", "name": "Detail", "props": {"layout": "horizontal", "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "Container"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-NbrxvqNT25bN9OosTgyMx", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "ERP_GEN$user", "parentModelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "ERP_GEN$user", "parentModelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请选择"}, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请选择"}, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-system-detail-group", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-system-detail", "name": "Detail", "props": {"flow": {"context$": "$context", "name": null, "test$": "mode !== \"design\"", "type": "RelationData"}, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "Container"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-X31wemFP3xpjoA3EyDZNS", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "系统信息"}], "lookup": [], "underline": true}, "type": "Layout"}], "flow": {"containerKey": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-defaultTabs-detail", "context$": "$context", "modelAlias": "ERP_GEN$gen_mat_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-defaultTabs-detail", "name": "Detail", "props": {"modelAlias": "ERP_GEN$gen_mat_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "type": "Container"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page", "name": "Page", "props": {}, "type": "Container"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "{{route?.action === \"edit\" ? \"编辑物料类型配置表\" : \"创建物料类型配置表\"}}", "useExpression": true}, "type": "Meta"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-field-id", "name": "FormField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请输入"}, "hidden": true, "initialValue": null, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-field-createdBy", "name": "FormField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择", "labelField": "username", "modelAlias": "ERP_GEN$user", "parentModelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-field-updatedBy", "name": "FormField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择", "labelField": "username", "modelAlias": "ERP_GEN$user", "parentModelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "ERP_GEN$SYS_FindDataByIdService", "searchServiceKey": "ERP_GEN$SYS_PagingDataService"}}, "hidden": true, "initialValue": null, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-field-createdAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-field-updatedAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请选择"}, "hidden": true, "initialValue": null, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-field-version", "name": "FormField", "props": {"componentProps": {"fieldAlias": "version", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-field-deleted", "name": "FormField", "props": {"componentProps": {"fieldAlias": "deleted", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-field-matTypeCode", "name": "FormField", "props": {"componentProps": {"fieldAlias": "matTypeCode", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "initialValue": null, "label": "物料类型编码", "name": "matTypeCode", "rules": [{"message": "请输入物料类型编码", "required": true}], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-field-matTypeName", "name": "FormField", "props": {"componentProps": {"fieldAlias": "matTypeName", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请输入"}, "displayComponentType": "Text", "editComponentType": "InputText", "hidden": false, "initialValue": null, "label": "物料类型名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": true, "required": false}, "key": "IWoeGrGD8HmQLylJSztGF", "valueRules": null}], "name": "matTypeName", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-wDhPRj0EAMsBHHCztQPDg", "name": "FormField", "props": {"colSize": 1, "componentProps": {"fieldAlias": "syncNode", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "MultiSelect", "hidden": false, "label": "同步节点", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "Q3dd2RzEYWJjaANyGSS0w", "valueRules": null}], "name": "syncNode", "rules": [], "type": "MULTISELECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-lGYnkvtgaa4LrpHVOSYbo", "name": "FormField", "props": {"componentProps": {"fieldAlias": "syncParty", "modelAlias": "ERP_GEN$gen_mat_type_cf", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal"}, "displayComponentType": "Enum", "editComponentType": "MultiSelect", "hidden": false, "label": "同步三方", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "WRj0qpVn4jRKtBgnKHQTq", "valueRules": null}], "name": "syncParty", "rules": [], "type": "MULTISELECT", "width": 120}, "type": "Widget"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form-defaultGroup", "name": "FormGroupItem", "props": {"defaultCollapsed": false, "title": false}, "type": "Layout"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-hduWHXNVE5aGYr_FXy4W5", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}], "underline": true}, "type": "Layout"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form", "name": "FormGroup", "props": {"colon": false, "flow": {"containerKey": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form", "modelAlias": "ERP_GEN$gen_mat_type_cf", "params$": "{ id: route.recordId || route.query.recordId }", "serviceKey": "ERP_GEN$SYS_FindDataByIdService", "test$": "!!(route.recordId || (route.query && route.query.recordId))", "type": "InvokeSystemService"}, "layout": "horizontal", "modelAlias": "ERP_GEN$gen_mat_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target$": "route?.action === \"edit\" ? \"show\" : \"list\""}]}, "label": "取消"}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Validate", "validate": true}], "bindServiceConfig": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_type_cf"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "valueConfig": {"action": {"target": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form"}, "type": "action"}}], "service": "ERP_GEN$SYS_MasterData_SaveDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "show", "name": "详情页", "type": "View"}, "params": [{"expression": "data.id", "name": "recordId", "serviceKey": null, "type": "expression"}], "refresh": true, "type": "NewPage"}}, {"action": "Refresh", "target": ["ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf"]}, {"action": "Message", "level": "success", "message": "保存成功!"}], "executeLogic": "BindService"}, "label": "保存", "showCondition": {}, "type": "primary"}, "type": "Widget"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView", "name": "Page", "props": {"showBack": true}, "type": "Container"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "name": "ColumnPage", "props": {}, "type": "Layout"}], "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "name": "Page", "props": {"showHeader": false, "title": "物料类型"}, "type": "Container"}, "frontendConfig": {"modules": ["base", "service"]}, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-ERP_GEN$wMiakn6hmdkva8xwz2xan", "resources": [{"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf", "label": "表格", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}], "relations": [{"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf-new", "label": "新建", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf", "label": "表格", "type": "Table"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-vGtaJfWTpMPR66GxpNIGU/items/ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-gen_mat_type_cf-multi-delete", "label": "删除", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf", "label": "表格", "type": "Table"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-vGtaJfWTpMPR66GxpNIGU", "label": "批量操作", "type": "DropdownButton"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-vGtaJfWTpMPR66GxpNIGU/items/ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-gen_mat_type_cf-multi-start", "label": "启用", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf", "label": "表格", "type": "Table"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-vGtaJfWTpMPR66GxpNIGU", "label": "批量操作", "type": "DropdownButton"}], "relations": [{"key": "ERP_GEN$SYS_MasterData_MultiEnableDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-vGtaJfWTpMPR66GxpNIGU/items/ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-gen_mat_type_cf-multi-stop", "label": "停用", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf", "label": "表格", "type": "Table"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-vGtaJfWTpMPR66GxpNIGU", "label": "批量操作", "type": "DropdownButton"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf-import", "label": "导入", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf", "label": "表格", "type": "Table"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "/api/gei/task/config/predict", "name": null, "props": {"httpMethod": "POST", "modelAlias": null}, "type": "Api"}, {"key": "/api/trantor/workflow/v2/{param0}", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/import-sub-model", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/task/import-direct-by-oss", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}, {"key": "/api/gei/template/download", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf-export", "label": "导出", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf", "label": "表格", "type": "Table"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf-batch-actions", "label": "按钮组", "type": "BatchActions"}], "relations": [{"key": "/api/trantor/portal/user/current", "name": null, "props": {"httpMethod": "GET", "modelAlias": null}, "type": "Api"}, {"key": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "name": null, "props": {"httpMethod": null, "modelAlias": null}, "type": "Service"}], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-mdTe1sOzyZC09rJiQedI6", "label": "ID", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf", "label": "表格", "type": "Table"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-xzm1P3JrgS9nLUfUGYEK2", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-Z1blhDok2aVfcRUSRkmDf", "label": "物料类型编码", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf", "label": "表格", "type": "Table"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-xzm1P3JrgS9nLUfUGYEK2", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-190ei1VyOmSVKln_6FBAB", "label": "物料类型名称", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-list-gen_mat_type_cf", "label": "表格", "type": "Table"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-xzm1P3JrgS9nLUfUGYEK2", "label": "字段组", "type": "Fields"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form", "label": "表单组", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView", "label": "页面", "type": "Page"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-action-cancel", "label": "取消", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-action-save", "label": "保存", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-footer", "label": "页面底部", "type": "<PERSON>Footer"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-actions", "label": "间距", "type": "Space"}], "relations": [{"key": "ERP_GEN$SYS_MasterData_SaveDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-actions-delete", "label": "删除", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView", "label": "视图", "type": "View"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-actions-copy", "label": "复制", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView", "label": "视图", "type": "View"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-actions-edit", "label": "编辑", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView", "label": "视图", "type": "View"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-actions", "label": "间距", "type": "Space"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-gen_mat_type_cf-detail", "label": "详情", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView", "label": "视图", "type": "View"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-NbrxvqNT25bN9OosTgyMx", "label": "页签项", "type": "TabItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-system-detail", "label": "详情", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView", "label": "视图", "type": "View"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-X31wemFP3xpjoA3EyDZNS", "label": "页签项", "type": "TabItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-actions-disable", "label": "停用", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView", "label": "视图", "type": "View"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-actions", "label": "间距", "type": "Space"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-actions-disable-show", "label": "展示逻辑", "type": "Show"}], "relations": [], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-actions-enable", "label": "启用", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView", "label": "视图", "type": "View"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page-header", "label": "页头操作栏", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-actions", "label": "间距", "type": "Space"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-actions-enable-show", "label": "展示逻辑", "type": "Show"}], "relations": [{"key": "ERP_GEN$SYS_MasterData_EnableDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$gen_mat_type_cf"}, "type": "SystemService"}], "type": "<PERSON><PERSON>"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-TojTlsvKcX4LsSlzlAzNt", "label": "物料类型编码", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView", "label": "视图", "type": "View"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-NbrxvqNT25bN9OosTgyMx", "label": "页签项", "type": "TabItem"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-gen_mat_type_cf-detail", "label": "详情", "type": "Detail"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-bPAOzbVFK2i4KJrIPOKzU", "label": "物料类型名称", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView", "label": "视图", "type": "View"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-NbrxvqNT25bN9OosTgyMx", "label": "页签项", "type": "TabItem"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-gen_mat_type_cf-detail", "label": "详情", "type": "Detail"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-IHoLAERfWhxY010zA2mYC", "label": "同步节点", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView", "label": "视图", "type": "View"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-NbrxvqNT25bN9OosTgyMx", "label": "页签项", "type": "TabItem"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-gen_mat_type_cf-detail", "label": "详情", "type": "Detail"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-XyyUQBUsrd6SfLuVYn3Xw", "label": "同步三方", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView", "label": "视图", "type": "View"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-NbrxvqNT25bN9OosTgyMx", "label": "页签项", "type": "TabItem"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-gen_mat_type_cf-detail", "label": "详情", "type": "Detail"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-MRtEsdU6JgNYnxD9Gi2Ge", "label": "ID", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView", "label": "视图", "type": "View"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-NbrxvqNT25bN9OosTgyMx", "label": "页签项", "type": "TabItem"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-gen_mat_type_cf-detail", "label": "详情", "type": "Detail"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-field-id", "label": "ID", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-hduWHXNVE5aGYr_FXy4W5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-field-createdBy", "label": "创建人", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-hduWHXNVE5aGYr_FXy4W5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-field-updatedBy", "label": "更新人", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-hduWHXNVE5aGYr_FXy4W5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-field-createdAt", "label": "创建时间", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-hduWHXNVE5aGYr_FXy4W5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-field-updatedAt", "label": "更新时间", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-hduWHXNVE5aGYr_FXy4W5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-field-version", "label": "版本号", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-hduWHXNVE5aGYr_FXy4W5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-field-deleted", "label": "逻辑删除标识", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-hduWHXNVE5aGYr_FXy4W5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-field-matTypeCode", "label": "物料类型编码", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-hduWHXNVE5aGYr_FXy4W5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-field-matTypeName", "label": "物料类型名称", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-hduWHXNVE5aGYr_FXy4W5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-wDhPRj0EAMsBHHCztQPDg", "label": "同步节点", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-hduWHXNVE5aGYr_FXy4W5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-lGYnkvtgaa4LrpHVOSYbo", "label": "同步三方", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form", "label": "表单组", "type": "FormGroup"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-hduWHXNVE5aGYr_FXy4W5", "label": "页签项", "type": "TabItem"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-editView-gen_mat_type_cf-form-defaultGroup", "label": false, "type": "FormGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-system-detail-field-createdBy", "label": "创建人", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView", "label": "视图", "type": "View"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-X31wemFP3xpjoA3EyDZNS", "label": "页签项", "type": "TabItem"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-system-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-system-detail-field-updatedBy", "label": "更新人", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView", "label": "视图", "type": "View"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-X31wemFP3xpjoA3EyDZNS", "label": "页签项", "type": "TabItem"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-system-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [{"key": "ERP_GEN$SYS_FindDataByIdService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}, "type": "SystemService"}, {"key": "ERP_GEN$SYS_PagingDataService", "name": null, "props": {"httpMethod": null, "modelAlias": "ERP_GEN$user"}, "type": "SystemService"}], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-system-detail-field-createdAt", "label": "创建时间", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView", "label": "视图", "type": "View"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-X31wemFP3xpjoA3EyDZNS", "label": "页签项", "type": "TabItem"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-system-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}, {"description": null, "key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-system-detail-field-updatedAt", "label": "更新时间", "path": [{"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page", "label": "双栏页面", "type": "ColumnPage"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-column-page-stackView", "label": "栈视图", "type": "StackView"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView", "label": "视图", "type": "View"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-page", "label": "页面", "type": "Page"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-defaultTabs", "label": "页签", "type": "Tabs"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-X31wemFP3xpjoA3EyDZNS", "label": "页签项", "type": "TabItem"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-system-detail", "label": "详情", "type": "Detail"}, {"key": "ERP_GEN$GEN_MAT_TYPE_VIEW-gen_mat_type-detailView-system-detail-group", "label": false, "type": "DetailGroupItem"}], "relations": [], "type": "Container"}], "title": "list", "type": "LIST"}}