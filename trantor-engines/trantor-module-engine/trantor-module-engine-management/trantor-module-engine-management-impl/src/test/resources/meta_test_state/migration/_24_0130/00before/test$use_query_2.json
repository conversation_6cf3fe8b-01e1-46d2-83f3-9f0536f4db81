{"type": "View", "name": "UseQuery 2", "children": [], "props": {"content": {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-list-sys_common$org_dimension_cf-new", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "new"}]}, "disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "新建", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-list-sys_common$org_dimension_cf-batch", "name": "DropdownButton", "props": {"combinationMode": true, "disabled$": "$context.selectedKeys?.length === 0 || $context.route?.action === \"new\" || $context.route?.action === \"edit\"", "items": [{"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_dimension_cf"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "sys_common$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-list-sys_common$org_dimension_cf"}, {"action": "PageJump", "target": "list"}, {"action": "Message", "message": "删除成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-sys_common$org_dimension_cf-multi-delete", "label": "批量删除"}, {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认统一启用吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_dimension_cf"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "sys_common$SYS_MasterData_MultiEnableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ["sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-list-sys_common$org_dimension_cf"]}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-sys_common$org_dimension_cf-multi-start", "label": "批量启用"}, {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认统一停用吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_dimension_cf"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "sys_common$SYS_MasterData_MultiDisableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-list-sys_common$org_dimension_cf"}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-sys_common$org_dimension_cf-multi-stop", "label": "批量停用"}], "label": "批量操作"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-list-sys_common$org_dimension_cf-import", "name": "ImportButton", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "downloadServiceProps": {"saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/template/download"}}, "isCustomServiceProps": {"isCustomFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_JUDGE_IF_CUSTOM_IMPORT_POST", "type": "InvokeService"}}, "label": "导入", "predictServiceProps": {"predictFlow": {"serviceKey": "sys_common$API_GEI_TASK_CONFIG_PREDICT_POST", "type": "InvokeService"}}, "saveServiceProps": {"saveServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST", "type": "InvokeService"}, "saveSubFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-direct-by-oss"}}, "saveSubServiceProps": {"saveFlow": {"method": "GET", "type": "InvokeApi", "url": "/api/gei/task/import-sub-model"}, "saveSubServiceFlow": {"serviceKey": "sys_common$API_GEI_TASK_IMPORT_SUB_MODEL_POST", "type": "InvokeService"}}}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-list-sys_common$org_dimension_cf-export", "name": "ExportButton", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "exportButtonServiceProps": {"saveFlow": {"serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST", "type": "InvokeService"}}, "label": "导出"}, "type": "Widget"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-list-sys_common$org_dimension_cf-batch-actions", "name": "BatchActions", "props": {}}, {"children": [{"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-list-sys_common$org_dimension_cf-logs", "name": "Logs", "props": {"disabled$": "$context.route?.action === \"new\" || $context.route?.action === \"edit\"", "label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-list-sys_common$org_dimension_cf-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-u08oc5v6ILaHJRLw38Dj-", "name": "Field", "props": {"componentProps": {"fieldAlias": "orgDimensionCode", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": false, "label": "维度编码", "name": "orgDimensionCode", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-toh7HpCSAT1WX7QtM0TMD", "name": "Field", "props": {"componentProps": {"fieldAlias": "orgDimensionName", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": false, "label": "维度名称", "name": "orgDimensionName", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-b6prlhboKZM-ADSYrpFaO", "name": "Field", "props": {"componentProps": {"fieldAlias": "orgDimensionDescribe", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": true, "label": "维度说明", "name": "orgDimensionDescribe", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-Oh1alLXE_DnIc1x8aS78a", "name": "Field", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-zb_28Lyn-u4f5dvobAwt4", "name": "Field", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_dimension_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-31IQgwYxwhaoTfuJVsUXP", "name": "Field", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_dimension_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-uwXHzj-5miMtulWtiJKF6", "name": "Field", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "type": "DATE", "width": 134}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-p0_5PoesM9Q3EoxnpNkaf", "name": "Field", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "type": "DATE", "width": 134}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-Ogspt0LzLroh3_JMZWkkP", "name": "Field", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": true, "label": "版本号", "name": "version", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-WplcOnD_-JZI5UU_qXD8_", "name": "Field", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": true, "label": "逻辑删除标识", "name": "deleted", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-H5SYQDmZIwmGMjwL2tH1U", "name": "Field", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "originOrgId", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": true, "label": "所属组织", "name": "originOrgId", "type": "NUMBER", "width": 144}, "type": "Widget"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-o-Yhub1wtbasVkNYEhK_F", "name": "Fields", "props": {}, "type": "Meta"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-list-sys_common$org_dimension_cf", "name": "Table", "props": {"allowRowSelect": true, "filterFields": [{"componentProps": {"fieldAlias": "orgDimensionCode", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": false, "label": "维度编码", "name": "orgDimensionCode", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "orgDimensionName", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": false, "label": "维度名称", "name": "orgDimensionName", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "orgDimensionDescribe", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": false, "label": "维度说明", "name": "orgDimensionDescribe", "type": "TEXT", "width": 146}, {"componentProps": {"fieldAlias": "id", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER", "width": 144}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_dimension_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT", "width": 168}, {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_dimension_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "type": "OBJECT", "width": 168}, {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "type": "DATE", "width": 134}, {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "type": "DATE", "width": 134}, {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": true, "label": "版本号", "name": "version", "type": "NUMBER", "width": 144}, {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": true, "label": "逻辑删除标识", "name": "deleted", "type": "NUMBER", "width": 144}, {"componentProps": {"defaultValue": 0, "fieldAlias": "originOrgId", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": true, "label": "所属组织", "name": "originOrgId", "type": "NUMBER", "width": 144}], "flow": {"containerKey": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-list-sys_common$org_dimension_cf", "modelAlias": "sys_common$org_dimension_cf", "serviceKey": "sys_common$SYS_PagingDataService", "type": "InvokeSystemService"}, "mode": "simple", "modelAlias": "sys_common$org_dimension_cf", "onRow$": "record => ({ onClick: systemActions.show({ record }, {openType: \"slot\"}) })", "pagination": {"size": "small"}, "serviceKey": "sys_common$SYS_PagingDataService"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-list-sys_common$org_dimension_cf-empty", "name": "Empty", "props": {"description": "从左侧选中数据后查看详情~", "imageStyle": {"height": 220, "width": 220}}}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-list-sys_common$org_dimension_cf-empty-box", "name": "Box", "props": {"style": {"align-items": "center", "display": "flex", "height": "100%", "justify-content": "center"}}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-page-title-text", "name": "Text", "props": {"value$": "((data?.code || \"\") + (data?.name || \"\")) || \"组织维度表详情\""}}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-waJjFj5BNTCns5V9Dn0x-", "name": "Status", "props": {"text$": "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.text", "type$": "(t=>({DRAFT:{type:\"failure\",text:\"草稿\"},INACTIVE:{type:\"default\",text:\"未启用\"},ENABLED:{type:\"success\",text:\"已启用\"},DISABLED:{type:\"failure\",text:\"已停用\"}})[t])(data?.status)?.type"}}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-actions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认删除吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_dimension_cf"}, {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}], "service": "sys_common$SYS_DeleteDataByIdService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-list-sys_common$org_dimension_cf"}, {"action": "Message", "message": "删除成功"}, {"action": "RefreshTab", "target": ["current"]}], "executeLogic": "BindService"}, "label": "删除", "showCondition": {"conditions": [{"conditions": [{"id": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detail-shanchu-condition-id1", "leftValue": {"fieldType": "Enum", "scope": "form", "target": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_cf", "title": "status", "type": "VarValue", "val": "status", "value": "sys_common$org_dimension_cf.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detail-shanchu-condition-id2", "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}}}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-actions-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "navigate({ action: 'new', query: { copyId: route.recordId } })"}, "label": "复制"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-actions-disable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认停用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_dimension_cf"}, {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}], "service": "sys_common$SYS_MasterData_DisableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ["sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-list-sys_common$org_dimension_cf", "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-sys_common$org_dimension_cf-detail"]}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "label": "停用", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "form", "title": "status", "type": "VarValue", "val": "status", "value": "sys_common$org_dimension_cf.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-actions-enable", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认启用吗？", "type": "Popconfirm"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "sys_common$org_dimension_cf"}, {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}], "service": "sys_common$SYS_MasterData_EnableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ["sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-list-sys_common$org_dimension_cf", "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-sys_common$org_dimension_cf-detail"]}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "label": "启用", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "form", "title": "status", "type": "VarValue", "val": "status", "value": "sys_common$org_dimension_cf.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-actions-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "edit"}]}, "label": "编辑", "showCondition": {"conditions": [{"conditions": [{"id": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detail-bianji-condition-id1", "leftValue": {"fieldType": "Enum", "scope": "form", "target": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_cf", "title": "status", "type": "VarValue", "val": "status", "value": "sys_common$org_dimension_cf.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detail-bianji-condition-id2", "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detail-sys_common$org_dimension_cf-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/oplog/admin/paging/log"}}}, "type": "Widget"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-sys_common$org_dimension_cf-field-orgDimensionCode", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "orgDimensionCode", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "editable": false, "label": "维度编码", "name": "orgDimensionCode", "type": "TEXT"}}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-sys_common$org_dimension_cf-field-orgDimensionName", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "orgDimensionName", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "editable": false, "label": "维度名称", "name": "orgDimensionName", "type": "TEXT"}}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-sys_common$org_dimension_cf-field-orgDimensionDescribe", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "orgDimensionDescribe", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "editable": false, "label": "维度说明", "name": "orgDimensionDescribe", "type": "TEXT"}}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-sys_common$org_dimension_cf-field-status", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "status", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请选择"}, "editable": false, "label": "维度状态", "name": "status", "type": "SELECT"}}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-sys_common$org_dimension_cf-detail-defaultGroup", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-z_9ZgPAmWQadupCrA4bsl", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-sys_common$org_dimension_business_link-list-batch-actions", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-sys_common$org_dimension_business_link-list-toolbar-actions", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-68NC3Co9ciCeBGM2-z8n1", "name": "Field", "props": {"componentProps": {"columns": ["code", "name", "status", "attrGroupId"], "fieldAlias": "orgBusinessTypeId", "label": "选择组织业务类型", "labelField": "name", "modelAlias": "sys_common$org_business_type_cf", "parentModelAlias": "sys_common$org_dimension_business_link", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": false, "label": "组织业务类型", "name": "orgBusinessTypeId", "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-M2kdXU_IDEb71QCKQO6kC", "name": "Field", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "sys_common$org_dimension_business_link", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-Z0qDwG2wHtoPNcwp-5G8u", "name": "Field", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_dimension_business_link", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-s32jEyNyHRWo17Zjaa9tj", "name": "Field", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_dimension_business_link", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-U3QaXgfqzgsd79xiDfY8w", "name": "Field", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "sys_common$org_dimension_business_link", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-gsLBvAPu73rWIrrDOnkpq", "name": "Field", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "sys_common$org_dimension_business_link", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-GP-cVoyCOs789KzpnSWDj", "name": "Field", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "sys_common$org_dimension_business_link", "placeholder": "请输入"}, "hidden": true, "label": "版本号", "name": "version", "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-du8tciQ9pfkjG9xX7tU9C", "name": "Field", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "sys_common$org_dimension_business_link", "placeholder": "请输入"}, "hidden": true, "label": "逻辑删除标识", "name": "deleted", "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-QTjhcG0Ee9JqaA24kzKJr", "name": "Field", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "originOrgId", "modelAlias": "sys_common$org_dimension_business_link", "placeholder": "请输入"}, "hidden": true, "label": "所属组织", "name": "originOrgId", "type": "NUMBER"}, "type": "Widget"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-V_QY3syiTwbEv8TFO6Q5m", "name": "Fields", "props": {}, "type": "Meta"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-sys_common$org_dimension_business_link-list", "name": "Table", "props": {"fieldName": "orgBusinessTypeList", "flow": {"context$": "$context", "name": "orgBusinessTypeList", "type": "RelationData"}, "modelAlias": "sys_common$org_dimension_business_link", "serviceKey": "$context", "subTableEnabled": false}, "type": "Container"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-sys_common$org_dimension_business_link-customDetailField", "name": "CustomDetailField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "orgBusinessTypeList"}, "type": "Meta"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-QIIAODqRBFjbgC3T-JcZV", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-system-detail-field-createdBy", "name": "DetailField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_dimension_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "editable": false, "label": "创建人", "name": "created<PERSON>y", "type": "OBJECT"}}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-system-detail-field-updatedBy", "name": "DetailField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_dimension_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "editable": false, "label": "更新人", "name": "updatedBy", "type": "OBJECT"}}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-system-detail-field-createdAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请选择"}, "editable": false, "label": "创建时间", "name": "createdAt", "type": "DATE"}}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-system-detail-field-updatedAt", "name": "DetailField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请选择"}, "editable": false, "label": "更新时间", "name": "updatedAt", "type": "DATE"}}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-system-detail-group", "name": "DetailGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-1hmjDgM3KzLLp-Q8mk1SI", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "业务类型"}, {"label": "系统信息"}], "underline": true}, "type": "Layout"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-sys_common$org_dimension_cf-detail", "name": "Detail", "props": {"flow": {"containerKey": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-sys_common$org_dimension_cf-detail", "context$": "$context", "modelAlias": "sys_common$org_dimension_cf", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_dimension_cf", "onFinish$": "(values) => invokeSystemService(\"sys_common$SYS_MasterData_SaveDataService\", \"sys_common$org_dimension_cf\", {...data, ...values}).then(() => $(\"sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-page\").action(\"reload\"))", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "type": "Container"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-page", "name": "Page", "props": {"showFooter": false, "showHeader": true}, "type": "Container"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-useQuery", "name": "UseQuery", "props": {"flow": {"context$": "$context", "modelAlias": "sys_common$org_dimension_cf", "serviceKey": "sys_common$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "sys_common$org_dimension_cf", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "type": "Meta"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-detailView-view", "name": "View", "props": {}, "type": "Container"}, {"children": [{"children": [{"children": [{"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-page-title-text", "name": "Text", "props": {"value$": "\"编辑\" + ((data?.code || \"\") + (data?.name || \"组织维度表\"))"}}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-2jV-bfGf5Gfhnwz-blbTK", "name": "Show", "props": {"value$": "route?.action === \"edit\""}, "type": "Meta"}, {"children": [{"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-page-title-text-create", "name": "Text", "props": {"value": "新建组织维度表"}}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-HwVLNp8dm0zDIgxZEboEw", "name": "Show", "props": {"value$": "route?.action === \"new\""}, "type": "Meta"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_cf-field-orgDimensionCode", "name": "FormField", "props": {"componentProps": {"fieldAlias": "orgDimensionCode", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": false, "label": "维度编码", "name": "orgDimensionCode", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_cf-field-orgDimensionName", "name": "FormField", "props": {"componentProps": {"fieldAlias": "orgDimensionName", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": false, "label": "维度名称", "name": "orgDimensionName", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_cf-field-orgDimensionDescribe", "name": "FormField", "props": {"componentProps": {"fieldAlias": "orgDimensionDescribe", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": false, "label": "维度说明", "name": "orgDimensionDescribe", "rules": [], "type": "TEXT"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_cf-field-id", "name": "FormField", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "rules": [{"message": "请输入ID", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_cf-field-createdBy", "name": "FormField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_dimension_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_cf-field-updatedBy", "name": "FormField", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_dimension_cf", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_cf-field-createdAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "rules": [{"message": "请输入创建时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_cf-field-updatedAt", "name": "FormField", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "请输入更新时间", "required": true}], "type": "DATE"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_cf-field-version", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "版本号", "name": "version", "rules": [{"message": "请输入版本号", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_cf-field-deleted", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "请输入逻辑删除标识", "required": true}], "type": "NUMBER"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_cf-field-originOrgId", "name": "FormField", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "originOrgId", "modelAlias": "sys_common$org_dimension_cf", "placeholder": "请输入"}, "hidden": true, "initialValue": 0, "label": "所属组织", "name": "originOrgId", "rules": [{"message": "请输入所属组织", "required": true}], "type": "NUMBER"}, "type": "Widget"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_cf-form-defaultGroup", "name": "FormGroupItem", "props": {"title": false}, "type": "Layout"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-bm7xBBOykmMnq46zZ3xQC", "name": "TabItem", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [{"children": [{"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_business_link-tableform-batchActions-delete", "name": "<PERSON><PERSON>", "props": {"actionConfig": {}, "confirmOn": "off", "disabled$": "mode === \"design\" ? undefined : $context.selectedKeys?.length === 0", "label": "删除", "onClick$": "() => $context.batchRemove?.($context.selectedKeys || [])", "type": "primary"}, "type": "Widget"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_business_link-tableform-batchActions", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_business_link-tableform-action-deleteLine", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "systemActions.remove($context)()"}, "label": "删除", "type": "text"}, "type": "Widget"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_business_link-tableform-reordActions", "name": "RecordActions", "props": {"width": 120}, "type": "Layout"}, {"children": [{"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-9Xl5vMifnHj5C7pVsmrXB", "name": "Field", "props": {"componentProps": {"columns": ["code", "name", "status", "attrGroupId"], "fieldAlias": "orgBusinessTypeId", "label": "选择组织业务类型", "labelField": "name", "modelAlias": "sys_common$org_business_type_cf", "parentModelAlias": "sys_common$org_dimension_business_link", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": false, "label": "组织业务类型", "name": "orgBusinessTypeId", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-s07VoxHMQvioiLRQaqJCI", "name": "Field", "props": {"componentProps": {"fieldAlias": "id", "modelAlias": "sys_common$org_dimension_business_link", "placeholder": "请输入"}, "hidden": true, "label": "ID", "name": "id", "rules": [{"message": "ID必填", "required": true}], "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-NiOUi3p6Ff-HrKrSqv6r-", "name": "Field", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "created<PERSON>y", "label": "选择创建人", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_dimension_business_link", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": true, "label": "创建人", "name": "created<PERSON>y", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-O6f0vJ4YT6U2IsTBTAICe", "name": "Field", "props": {"componentProps": {"columns": ["nickname", "username", "email", "mobile"], "fieldAlias": "updatedBy", "label": "选择更新人", "labelField": "username", "modelAlias": "sys_common$user", "parentModelAlias": "sys_common$org_dimension_business_link", "placeholder": "请选择", "serviceInfo": {"findServiceKey": "sys_common$SYS_FindDataByIdService", "searchServiceKey": "sys_common$SYS_PagingDataService"}}, "hidden": true, "label": "更新人", "name": "updatedBy", "rules": [], "type": "OBJECT", "width": 168}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW--h2nqwzLwD0R1-mLaoT-t", "name": "Field", "props": {"componentProps": {"fieldAlias": "createdAt", "modelAlias": "sys_common$org_dimension_business_link", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "rules": [{"message": "创建时间必填", "required": true}], "type": "DATE", "width": 134}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-ub-uVt4R0eVXT2uCcj7lR", "name": "Field", "props": {"componentProps": {"fieldAlias": "updatedAt", "modelAlias": "sys_common$org_dimension_business_link", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "rules": [{"message": "更新时间必填", "required": true}], "type": "DATE", "width": 134}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-k7c-F1Wd0SDiSC8BIPcdS", "name": "Field", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "version", "modelAlias": "sys_common$org_dimension_business_link", "placeholder": "请输入"}, "hidden": true, "label": "版本号", "name": "version", "rules": [{"message": "版本号必填", "required": true}], "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-SNdIdo-1coJoGhwC8RNE4", "name": "Field", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "deleted", "modelAlias": "sys_common$org_dimension_business_link", "placeholder": "请输入"}, "hidden": true, "label": "逻辑删除标识", "name": "deleted", "rules": [{"message": "逻辑删除标识必填", "required": true}], "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-1lF3x9n74dOJbFpOmRu1a", "name": "Field", "props": {"componentProps": {"defaultValue": 0, "fieldAlias": "originOrgId", "modelAlias": "sys_common$org_dimension_business_link", "placeholder": "请输入"}, "hidden": true, "label": "所属组织", "name": "originOrgId", "rules": [], "type": "NUMBER", "width": 144}, "type": "Widget"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-TjqWnoXsJw9v1qLRzg-1g", "name": "Fields", "props": {}, "type": "Meta"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_business_link-tableform", "name": "TableForm", "props": {"fieldName": "orgBusinessTypeList", "hideDefaultDelete": true, "modelAlias": "sys_common$org_dimension_business_link", "subTableEnabled": false}}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_business_link-form-field-orgBusinessTypeList", "name": "CustomFormField", "props": {"colSizeConfig": {"lg": 4, "md": 3, "sm": 2, "xl": 5, "xs": 1}, "name": "orgBusinessTypeList"}, "type": "Meta"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-zt8ym_0CSb2dONH4Js5Py", "name": "TabItem", "props": {}, "type": "Layout"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-defaultTabs", "name": "Tabs", "props": {"items": [{"label": "主体信息"}, {"label": "业务类型"}], "underline": true}, "type": "Layout"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_cf-form", "name": "FormGroup", "props": {"colon": false, "flow": {"children": [{"containerKey": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_cf-form", "modelAlias": "sys_common$org_dimension_cf", "params$": "{ id: route.recordId }", "serviceKey": "sys_common$SYS_FindDataByIdService", "test$": "route.action === \"edit\"", "type": "InvokeSystemService"}, {"containerKey": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-sys_common$org_dimension_cf-form", "modelAlias": "sys_common$org_dimension_cf", "params$": "{ id: route?.query?.copyId }", "serviceKey": "sys_common$SYS_CopyDataConverterService", "test$": "!!route.query?.copyId", "type": "InvokeSystemService"}], "type": "Condition"}, "layout": "horizontal", "modelAlias": "sys_common$org_dimension_cf", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "type": "Container"}, {"children": [{"children": [{"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-action-cancel", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target$": "route?.action === \"edit\" ? \"show\" : \"list\""}]}, "label": "取消"}, "type": "Widget"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-action-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "systemActions.save($context, \"sys_common$SYS_MasterData_SaveDataService\", \"sys_common$org_dimension_cf\")()"}, "label": "保存", "type": "primary"}, "type": "Widget"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-actions", "name": "Space", "props": {}, "type": "Layout"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-editView", "name": "Page", "props": {"showFooter": true, "showHeader": true}, "type": "Container"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-column-page-stackView", "name": "StackView", "props": {"items": [{"key": "list", "label": "列表页"}, {"key": "show", "label": "详情页"}, {"key": "edit", "label": "编辑页"}], "key": "list"}, "type": "Container"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-column-page", "name": "ColumnPage", "props": {}, "type": "Layout"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-page-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-page-title", "name": "Page<PERSON><PERSON>le", "props": {}, "type": "Meta"}, {"children": [], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-page-footer", "name": "<PERSON>Footer", "props": {}, "type": "Layout"}], "key": "sys_common$ORG_DIMENSION_BUSINESS_TYPE_LINK_VIEW-page", "name": "Page", "props": {"showFooter": false, "showHeader": false, "title": "组织维度"}, "type": "Container"}}}