{"type": "View", "name": "Structural script for masterData", "children": [], "props": {"content": {"children": [{"key": "t0", "name": "Table", "props": {"modelAlias": "not related"}, "type": "Container"}, {"key": "t1", "name": "Table", "props": {"modelAlias": "TERP_MIGRATE$gen_mat_cate_md"}, "type": "Container"}, {"key": "fg0", "name": "FormGroup", "props": {"modelAlias": "not related"}, "type": "Widget"}, {"key": "fg1", "name": "FormGroup", "props": {"modelAlias": "TERP_MIGRATE$gen_mat_cate_md"}, "type": "Widget"}, {"key": "b1", "name": "<PERSON><PERSON>", "props": {"onRow$": "record => ({ onClick: systemActions.show({ record }, {openType: \"slot\"}) })"}, "type": "Widget"}, {"key": "b2", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "systemActions.save($context, \"TERP_MIGRATE$SYS_MasterData_SaveDataService\", \"TERP_MIGRATE$gen_mat_cate_md\")()"}}, "type": "Widget"}, {"key": "b3", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "navigate({ action: 'new', query: { copyId: route.recordId } })"}}, "type": "Widget"}, {"key": "b4", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "systemActions.save($context, 'TERP_MIGRATE$SYS_MasterData_SaveDataService', 'TERP_MIGRATE$gen_mat_cate_md')().then(() => { getAction('mock-scene-tree', 'reload')(getAction('mock-scene-editView-TERP_MIGRATE$gen_mat_cate_md-form', 'getData')()?.matCateParent?.id, false) })", "endLogic": "Other"}}, "type": "Widget"}], "key": "111", "name": "Page", "props": {"showBack": true, "showFooter": true, "showHeader": true}, "type": "Container"}}}