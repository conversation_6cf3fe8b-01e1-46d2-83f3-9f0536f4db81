spring:
  application:
    name: trantor2
  jpa:
    show-sql: false
    format-sql: false
    properties:
      hibernate:
        show_sql: false
        format_sql: false
        globally_quoted_identifiers: false
        jdbc:
          batch_size: 500
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.MySQLDialect
  sql:
    init:
      mode: always

terminus:
  config:
    enabled: false
logging:
  level:
    org.springframework.security:
      - info
    org.springframework.web: info
    org.springframework.jdbc.core.JdbcTemplate: info
    org.hibernate.SQL: info
    org.hibernate.engine.QueryParameters: info
    org.hibernate.engine.query.HQLQueryPlan: info
    org.hibernate.type.descriptor.sql.BasicBinder: info

trantor2:
  datasource:
    data-source-password-decrypt-iv: terminus_trantor
    data-source-password-decrypt-key: terminus_trantor
  meta:
    event:
      type: redis
  console:
    enabled: true

debug: true
