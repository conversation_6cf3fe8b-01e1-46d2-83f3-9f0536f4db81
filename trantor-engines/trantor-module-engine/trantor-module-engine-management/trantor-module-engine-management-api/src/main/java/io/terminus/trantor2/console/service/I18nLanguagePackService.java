package io.terminus.trantor2.console.service;

import io.terminus.trantor2.module.meta.I18nLanguagePack;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Nonnull;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface I18nLanguagePackService {

    /**
     * 上传语言包到 i18n 应用和 oss
     *
     * @param languagePack     语言包
     * @param languagePackFile 语言包文件
     * @return 语言包文件 oss 地址
     */
    String uploadLanguagePack(@Nonnull I18nLanguagePack languagePack, @Nonnull MultipartFile languagePackFile);

    /**
     * 下载语言包
     *
     * @param key 语言包 key
     * @return 语言包文件
     */
    Map.Entry<String, ByteArrayResource> downloadLanguagePack(@Nonnull String key);
}
