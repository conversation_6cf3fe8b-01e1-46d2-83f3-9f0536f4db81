package io.terminus.trantor2.console.api.request;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.model.management.meta.consts.DataStructType;
import io.terminus.trantor2.model.management.meta.domain.DataStructNodeResourceProps;
import io.terminus.trantor2.model.management.meta.domain.DataStructProperties;
import io.terminus.trantor2.service.common.enums.ServiceType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Schema(title = "分页查询请求")
@Data
@JsonTypeInfo(use = JsonTypeInfo.Id.DEDUCTION, defaultImpl = PagingQueryRequest.DefaultPagingQueryRequest.class)
@JsonSubTypes({
        @JsonSubTypes.Type(value = PagingQueryRequest.PagingServiceQueryRequest.class),
        @JsonSubTypes.Type(value = PagingQueryRequest.PagingModelQueryRequest.class),
        @JsonSubTypes.Type(value = PagingQueryRequest.DefaultPagingQueryRequest.class)
})
public abstract class PagingQueryRequest {
    public abstract Cond buildCond(Cond cond, MetaType metaType);

    @Data
    @Schema(title = "默认分页查询请求")
    @EqualsAndHashCode(callSuper = true)
    public static class DefaultPagingQueryRequest extends PagingQueryRequest {
        @Schema(description = "模糊查询")
        protected String fuzzyValue;
        @Schema(description = "父节点 key，一般为目录 key")
        protected String parentKey;

        @Override
        public Cond buildCond(Cond cond, MetaType metaType) {
            if (cond == null) {
                cond = Cond.all();
            }
            if (StringUtils.isNotBlank(fuzzyValue)) {
                Cond fuzzyValueCond = Field.key().like("%" + fuzzyValue + "%")
                        .or(Field.name().like("%" + fuzzyValue + "%"));
                Cond customFuzzyValueCond = customFuzzyValueCond(metaType, fuzzyValue);
                if (customFuzzyValueCond != null) {
                    fuzzyValueCond = fuzzyValueCond.or(customFuzzyValueCond);
                }
                cond = cond.and(fuzzyValueCond);
            }
            if (StringUtils.isNotBlank(parentKey)) {
                if (!parentKey.contains(KeyUtil.MODULE_SEPARATOR)) {
                    // parentKey is module, append folder root
                    cond = cond.and(Field.parentKey().in(parentKey, KeyUtil.newKeyUnderModule(parentKey, "__folder__")));
                } else {
                    cond = cond.and(Field.parentKey().equal(parentKey));
                }
            }
            return cond;
        }

        private Cond customFuzzyValueCond(MetaType metaType, String fuzzyValue) {
            if (Objects.requireNonNull(metaType) == MetaType.ErrorCode) {
                return Field.props(String.class, "errorMsg").like("%" + fuzzyValue + "%");
            }
            return null;
        }
    }

    @Data
    @Schema(title = "服务分页查询请求")
    @EqualsAndHashCode(callSuper = true)
    public static class PagingServiceQueryRequest extends DefaultPagingQueryRequest {
        @Schema(description = "服务类型")
        private List<ServiceType> serviceTypes;

        @Override
        public Cond buildCond(Cond cond, MetaType metaType) {
            cond = super.buildCond(cond, metaType);
            if (CollectionUtils.isNotEmpty(serviceTypes)) {
                cond = cond.and(Field.props(String.class, "serviceType")
                        .in(serviceTypes.stream().map(Enum::name).toArray(Object[]::new)));
            }
            return cond;
        }
    }

    @Data
    @Schema(title = "模型分页查询请求")
    @EqualsAndHashCode(callSuper = true)
    public static class PagingModelQueryRequest extends DefaultPagingQueryRequest {
        @Schema(description = "模型类型")
        private List<DataStructType> modelTypes;

        @Override
        public Cond buildCond(Cond cond, MetaType metaType) {
            cond = super.buildCond(cond, metaType);
            if (CollectionUtils.isNotEmpty(modelTypes)) {
                cond = cond.and(Field.subType()
                        .in(modelTypes.stream().map(Enum::name).toArray(Object[]::new)))
                        .and(Field.props(Boolean.class, "props", DataStructProperties.Fields.sourceModelKey).isNull());
            }
            return cond;
        }
    }
}
