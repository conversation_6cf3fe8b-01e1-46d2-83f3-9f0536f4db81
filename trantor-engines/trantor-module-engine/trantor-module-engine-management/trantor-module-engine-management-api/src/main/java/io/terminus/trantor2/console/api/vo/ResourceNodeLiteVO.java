package io.terminus.trantor2.console.api.vo;

import io.terminus.trantor2.meta.api.dto.ResourceNodeLite;
import io.terminus.trantor2.meta.api.dto.ResourceNodeLiteImpl;
import io.terminus.trantor2.meta.api.model.MetaNodeAccessLevel;
import io.terminus.trantor2.meta.customization.Customization;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 **/
@Getter
@RequiredArgsConstructor
public class ResourceNodeLiteVO implements ResourceNodeLite {
    private final String type;
    private final String key;
    private final String name;
    private final String description;
    private final MetaNodeAccessLevel access;
    private final Customization customizedField;
    private final Long updatedBy;
    private final Date updatedAt;

    public static ResourceNodeLiteVO from(ResourceNodeLiteImpl lite, Long updatedBy, Date updatedAt) {
        return new ResourceNodeLiteVO(
                lite.getType(),
                lite.getKey(),
                lite.getName(),
                lite.getDescription(),
                lite.getAccess(),
                lite.getCustomizedField(),
                updatedBy,
                updatedAt
        );
    }
}
