package io.terminus.trantor2.application.controller;


import com.google.common.util.concurrent.RateLimiter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.application.ApplicationType;
import io.terminus.trantor2.application.dto.*;
import io.terminus.trantor2.application.item.AppBaseInfo;
import io.terminus.trantor2.application.item.AppContent;
import io.terminus.trantor2.application.po.TrantorAppItemPO;
import io.terminus.trantor2.application.po.TrantorVersionedAppItemPO;
import io.terminus.trantor2.application.service.AbstractAppManagerService;
import io.terminus.trantor2.application.vo.TrantorApplicationItemVO;
import io.terminus.trantor2.application.vo.TrantorApplicationItemVersion;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.internal.InjectUserInfos;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.PostConstruct;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "应用市场管理")
@RestController
@RequestMapping("/api/trantor/console/app")
@RequiredArgsConstructor
public class AppItemManagerController {
    @SuppressWarnings("all")
    private final RateLimiter rateLimiter = RateLimiter.create(1.0 / 30);

    @Autowired
    private List<AbstractAppManagerService<?, ?>> serviceList;

    private Map<ApplicationType, AbstractAppManagerService<?, ?>> serviceMap;

    @PostConstruct
    public void init() {
        serviceMap = serviceList.stream().collect(Collectors.toMap(
                AbstractAppManagerService::getApplicationType,
                Function.identity())
        );
    }

    @PostMapping("/profile/save/{type}")
    @Operation(summary = "新建或编辑应用基本信息")
    public <R extends AppItemSaveRequest> Response<Void> saveApp(@PathVariable ApplicationType type, @Valid @RequestBody R request) {
        serviceMap.get(type).saveAppOp(request);
        return Response.ok();
    }

    @PostMapping("/create-with-version/{type}")
    @Operation(summary = "新建应用和版本")
    public <R extends AppItemWithVersionCreateRequest> Response<Void> createAppWithVersion(@PathVariable ApplicationType type, @Valid @RequestBody R request) {
        serviceMap.get(type).createAppWithVersionOp(request);
        return Response.ok();
    }

    @GetMapping("/list/{type}")
    @Operation(summary = "应用列表")
    @InjectUserInfos
    public <R extends AppItemListRequest> Response<List<? extends TrantorApplicationItemVO>> list(@PathVariable ApplicationType type, R request) {
        return Response.ok(serviceMap.get(type).listItems(request));
    }

    @Operation(summary = "应用详情")
    @GetMapping("/{type}/{key}")
    public Response<TrantorAppItemPO<? extends AppBaseInfo, ? extends AppContent>> findApp(@PathVariable ApplicationType type,
                                                                    @PathVariable String key,
                                                                    @Parameter(description = "是否为市场模版, 默认为 true, 即查询中心市场模版")
                                                                     @RequestParam(required = false, defaultValue = "true") boolean fromMarket) {
        return Response.ok(serviceMap.get(type).findApp(key, fromMarket));
    }

    @PostMapping("/sort/{type}")
    @Operation(summary = "排序")
    public Response<Void> sort(@PathVariable ApplicationType type, @Valid @RequestBody AppItemSortRequest request) {
        serviceMap.get(type).sortLocalOp(request);
        return Response.ok();
    }

    @PostMapping("/unlist/{type}/{key}")
    @Operation(summary = "下架")
    public Response<Void> unlistApp(@PathVariable ApplicationType type, @PathVariable String key) {
        serviceMap.get(type).unlistOp(key, null);
        return Response.ok();
    }

    @GetMapping("/market/refresh/{type}")
    @Operation(summary = "刷新市场")
    @SuppressWarnings("all")
    public Response<Void> refresh(@PathVariable ApplicationType type) {
        if (rateLimiter.tryAcquire()) {
            serviceMap.get(type).asyncFromNexus(TrantorContext.copy());
        } else {
            log.info("rate limit, skip do async from Nexus");
        }
        return Response.ok();
    }

    @PostMapping("/versions/save/{type}")
    @Operation(summary = "新建或编辑应用版本")
    public<R extends VersionedAppItemSaveRequest> Response<Void> saveVersionedApp(@PathVariable ApplicationType type, @Valid @RequestBody R request) {
        serviceMap.get(type).saveVersionedAppOp(request);
        return Response.ok();
    }

    @GetMapping("/versions/{type}/{key}/list")
    @Operation(summary = "版本列表")
    public Response<List<TrantorApplicationItemVersion>> versions(@PathVariable ApplicationType type, @PathVariable String key,
                                                                  @RequestParam(required = false) boolean loadForLocalDevMode) {
        return Response.ok(serviceMap.get(type).versions(key, loadForLocalDevMode));
    }

    @PostMapping("/versions/enable/{type}")
    @Operation(summary = "启用版本")
    public Response<Void> enable(@PathVariable ApplicationType type, @Valid @RequestBody AppVersionRequest request) {
        serviceMap.get(type).enable(request.getKey(), request.getVersion());
        return Response.ok();
    }

    @PostMapping("/versions/disable/{type}")
    @Operation(summary = "停用版本, 仅支持停用已启用的版本")
    public Response<Void> disable(@PathVariable ApplicationType type, @Valid @RequestBody AppVersionRequest request) {
        serviceMap.get(type).disable(request.getKey(), request.getVersion());
        return Response.ok();
    }

    @PostMapping("/versions/release/{type}")
    @Operation(summary = "发布版本")
    public Response<Void> release(@PathVariable ApplicationType type, @Valid @RequestBody AppVersionRequest request) {
        serviceMap.get(type).releaseOp(request.getKey(), request.getVersion(), request.getCompatibilityRange());
        return Response.ok();
    }

    @PostMapping("/versions/unlist/{type}/{key}/{version}")
    @Operation(summary = "下架指定版本")
    public Response<Void> unlistVersion(@PathVariable ApplicationType type, @PathVariable String key, @PathVariable String version) {
        serviceMap.get(type).unlistOp(key, version);
        return Response.ok();
    }

    @Operation(summary = "版本详情")
    @GetMapping("/versions/{type}/{key}")
    public Response<TrantorVersionedAppItemPO<? extends AppContent>> findVersion(@PathVariable ApplicationType type,
                                                                                 @PathVariable String key,
                                                                                 @Parameter(description = "版本，fromMarket = true 或 enabled = true 时忽略版本")
                                                                             @RequestParam(required = false) String version,
                                                                                 @Parameter(description = "是否启用, 默认为 true, 即查询已启用的模版")
                                                                             @RequestParam(required = false, defaultValue = "true") boolean enabled,
                                                                                 @Parameter(description = "是否为市场模版, 默认为 true, 即查询中心市场模版")
                                                                             @RequestParam(required = false, defaultValue = "true") boolean fromMarket) {
        return Response.ok(serviceMap.get(type).findVersion(key, version, enabled, fromMarket));
    }
}
