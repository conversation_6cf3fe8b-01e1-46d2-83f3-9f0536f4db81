package io.terminus.trantor2.application.service;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import io.terminus.trantor2.application.ApplicationType;
import io.terminus.trantor2.application.constants.AppConsts;
import io.terminus.trantor2.application.dto.AppItemVersion;
import io.terminus.trantor2.application.dto.AppListNexusResource;
import io.terminus.trantor2.application.dto.NexusResource;
import io.terminus.trantor2.application.item.AppBaseInfo;
import io.terminus.trantor2.application.item.AppContent;
import io.terminus.trantor2.application.po.TrantorAppItemPO;
import io.terminus.trantor2.application.po.TrantorVersionedAppItemPO;
import io.terminus.trantor2.application.repo.AbstractTrantorAppRepo;
import io.terminus.trantor2.application.repo.AbstractVersionedTrantorAppRepo;
import io.terminus.trantor2.application.util.AppOssUtils;
import io.terminus.trantor2.application.util.AppVersionUtils;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.feature.ComparableVersion;
import io.terminus.trantor2.common.feature.VersionRange;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.common.utils.VersionUtil;
import io.terminus.trantor2.console.service.ConsoleService;
import io.terminus.trantor2.module.service.OSSService;
import io.terminus.trantor2.nexus.dto.Asset;
import io.terminus.trantor2.nexus.dto.Checksum;
import io.terminus.trantor2.nexus.dto.Maven;
import io.terminus.trantor2.nexus.dto.NexusComponent;
import io.terminus.trantor2.nexus.service.NexusApiClient;
import io.terminus.trantor2.nexus.service.NexusApiClientFactory;
import io.terminus.trantor2.properties.management.nexus.NexusConfigProperties;
import io.terminus.trantor2.properties.management.nexus.NexusProperties;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.io.ByteArrayInputStream;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static io.terminus.trantor2.application.constants.AppConsts.*;

/**
 * <AUTHOR>
 */
@Slf4j
@ConditionalOnBean(ConsoleService.class)
public abstract class ApplicationSyncNexusService
        <T extends TrantorAppItemPO<? extends AppBaseInfo, ? extends AppContent>,
                V extends TrantorVersionedAppItemPO<? extends AppContent>> {
    protected final NexusApiClientFactory nexusApiClientFactory;
    protected final NexusConfigProperties nexusConfigProperties;
    protected final ConsoleService consoleService;
    protected final Class<T> appClass;
    protected final Class<V> verionedAppClass;
    @Autowired
    protected OSSService ossService;
    private static final Cache<String, AppListNexusResource> appListNexusResourceCache = Caffeine.newBuilder()
            .maximumSize(5)
            .expireAfterWrite(1, TimeUnit.DAYS).build();
    protected ApplicationSyncNexusService(NexusApiClientFactory nexusApiClientFactory,
                                          NexusConfigProperties nexusConfigProperties,
                                          ConsoleService consoleService) {
        this.nexusApiClientFactory = nexusApiClientFactory;
        this.nexusConfigProperties = nexusConfigProperties;
        this.consoleService = consoleService;
        Type type = getClass().getGenericSuperclass();
        ParameterizedType parameterizedType = (ParameterizedType) type;
        Type[] typeArguments = parameterizedType.getActualTypeArguments();
        appClass = (Class<T>) typeArguments[0];
        verionedAppClass = (Class<V>) typeArguments[1];
    }

    public abstract ApplicationType getApplicationType();

    public abstract String getGroup();

    public abstract String getExtension();

    public abstract AbstractTrantorAppRepo<T> getRepo();

    public abstract AbstractVersionedTrantorAppRepo<V> getVersionedRepo();

    /**
     * 从 Nexus 同步应用资源
     */
    @Transactional
    public List<V> syncFromNexus(NexusApiClient nexusApiClient) {
        if (nexusApiClient == null) {
            return Collections.emptyList();
        }
        Boolean local = nexusApiClient.getLocal();
        if (!needSync(local)) {
            return Collections.emptyList();
        }
        log.info("******* start to sync apps from nexus[local={}] *******", local);
        AbstractTrantorAppRepo<T> repo = getRepo();
        AbstractVersionedTrantorAppRepo<V> versionedRepo = getVersionedRepo();

        Map<T, Map<AppItemVersion, V>> dbApps = new HashMap<>();
        repo.findAllByLocal(local).forEach(app ->
                dbApps.put(app, versionedRepo.findAllByKeyAndLocal(app.getKey(), local).stream()
                        .collect(Collectors.toMap(AppItemVersion::new, it -> it)))
        );
        Map<T, List<V>> marketApps = getMarketApps(nexusApiClient, dbApps);

        deleteAllUnlistAppsAndVersions(local, marketApps, dbApps);

        if (!MapUtil.isEmpty(marketApps)) {
            List<V> allVersions = new ArrayList<>(marketApps.size());
            marketApps.forEach((app, versions) -> {
                versions.forEach(ver -> ver.setApp(app));
                allVersions.addAll(versions);
            });
            repo.saveAllAndFlush(marketApps.keySet());
            versionedRepo.saveAllAndFlush(allVersions);
            syncResourcesToOSS(allVersions, local);
            if (local && consoleService.isPublicMarketDevMode()) {
                // 应用顺序由本地市场维护，如果是中心市场开发模式拉取后同步到公仓
                asyncAppList2Nexus(false);
            }
            if (log.isDebugEnabled()) {
                log.debug("******* sync apps from nexus[local={}] success, total {} templates saved *******",
                        local, allVersions.stream().map(it -> it.getKey() + ":" + it.getAppVersion()).collect(Collectors.joining(",")));
            }
            return allVersions;
        }
        return Collections.emptyList();
    }

    protected void syncToNexus(@Nonnull V versionedApp, @Nonnull Boolean pub) {
        NexusApiClient nexusApiClient = nexusApiClientFactory.getClient(pub);

        VersionRange compatibilityRange = versionedApp.getCompatibilityRange();
        String displayRange = AppVersionUtils.getDisplayRange(compatibilityRange);

        Set<Maven> mavens = Optional.ofNullable(versionedApp.getMavensAssets())
                .orElseGet(HashSet::new);

        mavens.add(Maven.builder()
                .groupId(getGroup())
                .version(versionedApp.getAppVersion())
                .artifactId(versionedApp.getKey())
                .extension(getExtension())
                .classifier(displayRange)
                .resource(versionedApp.covert2Resource())
                .build()
        );
        uploadMavens(nexusApiClient, mavens);

        Set<Maven> resourceMavenAssets = buildResourceMavenAssets(versionedApp.getApp());
        if (!CollectionUtils.isEmpty(resourceMavenAssets)) {
            removeFromNexus(versionedApp.getKey(), pub, AppConsts.RESOURCES_VERSION);
            uploadMavens(nexusApiClient, resourceMavenAssets);
        }

        // 如果当前发布的兼容版本包含历史维护版本，则同步到历史维护版本 1.0.0-SNAPSHOT
        if (pub && (compatibilityRange == null || compatibilityRange.includeRange(AppConsts.HISTORICAL_MAINTAINED_RANGE))) {
            mavens.forEach(it -> {
                it.setVersion(APPLICATION_MAVEN_VERSION);
                it.setClassifier(null);
            });
            uploadMavens(nexusApiClient, mavens);
        }
    }

    protected void removeFromNexus(@Nullable String key, @Nonnull Boolean pub, @Nullable String version) {
        NexusApiClient nexusApiClient = nexusApiClientFactory.getClient(pub);
        try {
            List<NexusComponent> result = nexusApiClient.search(
                    getGroup(),
                    version,
                    key,
                    null);
            if (!CollectionUtils.isEmpty(result)) {
                result.forEach(component -> nexusApiClient.delete(component.getId()));
            }
        } catch (Exception e) {
            log.error("failure sync app to nexus", e);
            throw new TrantorRuntimeException("failure sync app to nexus");
        }
    }

    protected byte[] download(boolean local, String downloadUrl) {
        return nexusApiClientFactory.getClient(!local).download(downloadUrl);
    }

    protected String getDownloadUrl(boolean local, Maven maven) {
        NexusProperties nexusProperties = local ? nexusConfigProperties.getLocal() : nexusConfigProperties.getPub();
        StringBuilder url = new StringBuilder(nexusProperties.getHost());
        url.append("/repository/").append(nexusProperties.getRepository())
                .append("/").append(getGroup().replace(".", "/"))
                .append("/").append(maven.getArtifactId())
                .append("/").append(maven.getVersion())
                .append("/").append(maven.getArtifactId()).append("-").append(maven.getVersion());
        if (StringUtils.isNotBlank(maven.getClassifier())) {
            url.append("-").append(maven.getClassifier());
        }
        url.append(".").append(maven.getExtension());
        return url.toString();
    }

    protected void uploadMavens(NexusApiClient nexusApiClient, Collection<Maven> mavens) {
        try {
            if (CollectionUtils.isEmpty(mavens)) {
                return;
            }
            nexusApiClient.uploadMaven(mavens);
            // 公有仓库版本有兼容范围（classifier），需要删除非当前兼容范围的资源
            if (BooleanUtil.isFalse(nexusApiClient.getLocal())) {
                for (Maven maven : mavens) {
                    if (AppConsts.RESOURCES_VERSION.equals(maven.getVersion())) {
                        continue;
                    }
                    List<String> assetsIds = nexusApiClient.searchAssets(getGroup(), maven.getVersion(), maven.getArtifactId(), maven.getExtension())
                            .stream().filter(it -> {
                                String classifier = it.getMaven2().getClassifier();
                                return classifier != null ? !classifier.equals(maven.getClassifier()) : StringUtils.isNotBlank(maven.getClassifier());
                            })
                            .map(Asset::getId)
                            .collect(Collectors.toList());
                    nexusApiClient.deleteAssets(assetsIds);
                }
            }
        } catch (Exception e) {
            log.error("failure sync resource to nexus", e);
            throw new TrantorRuntimeException("failure sync resource to nexus");
        }
    }

    @Nonnull
    protected Set<String> getAllOssUrls(@NonNull T appItem) {
        Set<String> ossUrls = new HashSet<>();
        if (appItem.getIcon() != null) {
            ossUrls.add(appItem.getIcon());
        }
        return ossUrls;
    }

    @Nonnull
    protected Set<Maven> buildResourceMavenAssets(@NonNull T appItem) {
        Set<String> allOssUrls = getAllOssUrls(appItem);
        return AppOssUtils.buildOSSAssets(ossService, appItem.getKey(), AppConsts.RESOURCES_VERSION, getGroup(), allOssUrls);
    }

    @Async
    @SneakyThrows
    protected void asyncAppList2Nexus(boolean local) {
        try {
            List<T> apps = getRepo().findAllByLocal(true);
            NexusApiClient nexusApiClient = nexusApiClientFactory.getClientOptional(!local).orElse(null);
            if (nexusApiClient == null) {
                return;
            }
            AppListNexusResource appListNexusResource = new AppListNexusResource();
            appListNexusResource.setApps(apps.stream().map(app -> AppListNexusResource.AppNexusResource.builder()
                    .key(app.getKey())
                    .sort(app.getSort())
                    .build()).collect(Collectors.toList())
            );
            nexusApiClient.uploadMaven(
                    getGroup() + APP_SUB_GROUP,
                    APP_LIST_ARTIFACT,
                    APPLICATION_MAVEN_VERSION,
                    new ByteArrayResource(JsonUtil.NON_INDENT.getObjectMapper().writeValueAsBytes(appListNexusResource)),
                    "json",
                    null
            );
        } catch (Exception e) {
            log.error("failure while uploading app list to public nexus", e);
        }
    }

    @Nonnull
    private AppListNexusResource fetchAppsFromNexus(NexusApiClient nexusApiClient) {
        if (nexusApiClient == null) {
            return new AppListNexusResource();
        }
        try {
            String group = getGroup() + APP_SUB_GROUP;
            Optional<Asset> assetOpt = nexusApiClient.searchAssets(group, APPLICATION_MAVEN_VERSION, APP_LIST_ARTIFACT, getExtension())
                    .stream().findFirst();

            if (assetOpt.isPresent()) {
                Asset asset = assetOpt.get();
                String sha256 = asset.getChecksum().getSha256();
                AppListNexusResource appListNexusResource = appListNexusResourceCache.getIfPresent(sha256);
                if (appListNexusResource != null) {
                    return appListNexusResource;
                }
                Boolean local = nexusApiClient.getLocal();
                byte[] download = download(local, asset.getDownloadUrl());
                if (download != null) {
                    appListNexusResource = JsonUtil.NON_INDENT.getObjectMapper().readValue(download, AppListNexusResource.class);
                    appListNexusResourceCache.put(sha256, appListNexusResource);
                    return appListNexusResource;
                }
            }
        } catch (Exception e) {
            log.error("failure while fetching app list from public nexus", e);
        }
        return new AppListNexusResource();
    }

    private V downloadVersionedApp(Map<T, Map<AppItemVersion, V>> dbApps, NexusApiClient nexusApiClient, Asset asset) {
        byte[] jsonBytes = nexusApiClient.download(asset.getDownloadUrl());
        NexusResource resource;
        try {
            resource = JsonUtil.NON_INDENT.getObjectMapper().readValue(jsonBytes, NexusResource.class);
        } catch (Exception e) {
            log.error("failure while fetching asset from public nexus", e);
            throw new TrantorRuntimeException("failure while fetching assert from public nexus");
        }
        return rebuildVersionedApp(dbApps, asset.getChecksum().getChecksumStr(), nexusApiClient.getLocal(), resource);
    }
    private boolean needSync(Boolean local) {
        if (local) {
            if (BooleanUtil.isTrue(consoleService.isLocalMarketDevMode())) {
                return false;
            }
            if (!BooleanUtil.isTrue(nexusConfigProperties.getLocal().getSyncEnabled())) {
                log.warn("******* [TRANTOR_LOCAL_NEXUS_SYNC_ENABLED] is disabled." +
                        " Skipping synchronization of app from local Nexus *******");
                return false;
            }
            return true;
        }
        return !BooleanUtil.isTrue(consoleService.isPublicMarketDevMode());
    }

    private Map<T, List<V>> getMarketApps(@Nonnull NexusApiClient nexusApiClient,
                                          @NonNull Map<T, Map<AppItemVersion, V>> dbApps) {
        Map<T, List<V>> marketApps = new HashMap<>();
        Boolean local = nexusApiClient.getLocal();
        String trantorVersion = VersionUtil.getTrantorVersion();
        List<Asset> assets = nexusApiClient.searchAssets(getGroup(), null, null, getExtension())
                .stream().filter(asset -> {
                    String classifier = asset.getMaven2().getClassifier();
                    if (ObjectUtils.isEmpty(classifier)) {
                        return true;
                    }
                    return AppVersionUtils.displayRange2VersionRange(classifier).containVersion(trantorVersion);
                })
                .sorted(Comparator.comparing(o -> ComparableVersion.of(o.getMaven2().getVersion())))
                .collect(Collectors.toList());

        Map<String, V> existedAssetId2VersionedApp = analyzeExistedAssets(assets, dbApps);
        Map<String, AppListNexusResource.AppNexusResource> appKey2Resource = fetchAppsFromNexus(nexusApiClient).toMap();
        assets.forEach(asset -> {
            V version = existedAssetId2VersionedApp.containsKey(asset.getId()) ?
                    existedAssetId2VersionedApp.get(asset.getId()) :
                    downloadVersionedApp(dbApps, nexusApiClient, asset);

            T app = version.getApp();
            if (appKey2Resource.containsKey(app.getKey())) {
                AppListNexusResource.AppNexusResource appNexusResource = appKey2Resource.get(app.getKey());
                app.setSort(appNexusResource.getSort());
            }
            if (!CollectionUtils.isEmpty(marketApps.get(app))) {
                if (local) {
                    marketApps.get(app).add(version);
                } else {
                    // 中心市场只保留一个版本，这里比较版本，保留大版本
                    V old = marketApps.get(app).get(0);
                    if (VersionUtil.compareVersions(version.getAppVersion(), old.getAppVersion()) > 0) {
                        marketApps.put(app, Lists.newArrayList(version));
                    }
                }
            } else {
                marketApps.put(app, Lists.newArrayList(version));
            }
        });
        return marketApps;
    }

    private Map<String, V> analyzeExistedAssets(List<Asset> assets, Map<T, Map<AppItemVersion, V>> dbApps) {
        Map<String, List<Asset>> key2assets = new HashMap<>();
        for (Asset asset : assets) {
            String key = asset.getMaven2().getArtifactId();
            if (key2assets.containsKey(key)) {
                key2assets.get(key).add(asset);
            } else {
                key2assets.put(key, Lists.newArrayList(asset));
            }
        }
        Map<String, V> assetId2VersionedApp = new HashMap<>();
        dbApps.forEach((app, amap) -> {
            if (!key2assets.containsKey(app.getKey())) {
                return;
            }
            amap.values().forEach(versionedApp -> {
                key2assets.get(app.getKey()).stream()
                        .filter(it -> it.getChecksum().equals(Checksum.of(versionedApp.getChecksum())))
                        .findFirst()
                        .ifPresent(it -> {
                            versionedApp.setNotChanged(true);
                            assetId2VersionedApp.put(it.getId(), versionedApp);
                        });
            });
        });
        return assetId2VersionedApp;
    }

    private V rebuildVersionedApp(@NonNull Map<T, Map<AppItemVersion, V>> dbApps, @Nonnull String checksum,
                                  @Nonnull Boolean local, @Nonnull NexusResource resource) {
        try {
            // deal with app
            Optional<T> dbAppOpt = dbApps.keySet().stream()
                    .filter(dbApp -> dbApp.getKey().equals(resource.getKey()) && dbApp.getLocal().equals(local)).findFirst();
            T app = dbAppOpt.isPresent() ? dbAppOpt.get() : appClass.newInstance();
            BeanUtils.copyProperties(resource, app);
            app.setLocal(local);
            if (app.getSort() == null) {
                app.setSort(Integer.MAX_VALUE);
            }

            // deal with version app
            AppItemVersion appItemVersion = new AppItemVersion(resource.getKey(), resource.getAppVersion());
            Optional<V> dbVersionedApp = Optional.ofNullable(dbApps.get(app)).map(vmap -> vmap.get(appItemVersion));
            V version = dbVersionedApp.isPresent() ? dbVersionedApp.get() : verionedAppClass.newInstance();
            version.setLocal(local);
            BeanUtils.copyProperties(resource, version);
            if (!local && !consoleService.isPublicMarketDevMode()) {
                version.setEnabled(true);
                version.setReleased(true);
            } else {
                if (version.getEnabled() == null) {
                    version.setEnabled(false);
                }
                if (version.getReleased() == null) {
                    version.setReleased(false);
                }
            }
            version.setApp(app);
            version.setChecksum(checksum);
            return version;
        } catch (Exception e) {
            log.error("failure while fetching asset from public nexus", e);
            throw new TrantorRuntimeException("failure while fetching assert from public nexus");
        }
    }

    private void deleteAllUnlistAppsAndVersions(Boolean local,
                                                Map<T, List<V>> marketApps,
                                                Map<T, Map<AppItemVersion, V>> dbApps) {
        if (dbApps.isEmpty()) {
            return;
        }
        if (local && consoleService.isPublicMarketDevMode()) {
            deleteAllUnlistForPublicDev(marketApps, dbApps);
            return;
        }
        Set<String> marketAppKeys = marketApps.keySet().stream().map(TrantorAppItemPO::getKey).collect(Collectors.toSet());

        AbstractVersionedTrantorAppRepo<V> versionedRepo = getVersionedRepo();
        // 已下架的应用
        List<String> deleteAppKeys = dbApps.keySet().stream()
                .map(TrantorAppItemPO::getKey)
                .filter(key -> !marketAppKeys.contains(key))
                .collect(Collectors.toList());
        // 删除已下架应用和其所有版本
        deleteAllApps(deleteAppKeys, local);

        // 删除其他版本
        dbApps.forEach((app, amap) -> {
            if (!marketApps.containsKey(app)) {
                return;
            }
            Set<String> marketVersions = marketApps.get(app).stream().map(v -> v.getAppVersion()).collect(Collectors.toSet());
            versionedRepo.deleteAllByKeyAndLocalAndAppVersionNotIn(app.getKey(), getApplicationType(), local, marketVersions);
            log.info("sync delete app[{}] version whose version is not in [{}] from nexus[local={}]", app.getKey(), marketVersions, local);
        });
    }

    /**
     * 作为中心市场的环境同步私有仓库，删除逻辑比较特殊，需要保留已发布版本。
     */
    private void deleteAllUnlistForPublicDev(Map<T, List<V>> marketApps, Map<T, Map<AppItemVersion, V>> dbApps) {
        Set<String> marketAppKeys = marketApps.keySet().stream().map(TrantorAppItemPO::getKey).collect(Collectors.toSet());

        AbstractVersionedTrantorAppRepo<V> versionedRepo = getVersionedRepo();
        // 已下架未发布的应用
        List<String> deleteAppKeys = dbApps.keySet().stream()
                .filter(app -> CollectionUtils.isEmpty(app.getReleasedVersion()))
                .map(TrantorAppItemPO::getKey)
                .filter(key -> !marketAppKeys.contains(key))
                .collect(Collectors.toList());

        // 删除已下架并未发布的应用和其所有版本
        deleteAllApps(deleteAppKeys, true);

        // 删除私有市场没有且未发布其他版本
        dbApps.forEach((app, amap) -> {
            if (!marketApps.containsKey(app)) {
                return;
            }
            Set<String> marketVersions = marketApps.get(app).stream().map(v -> v.getAppVersion()).collect(Collectors.toSet());
            versionedRepo.deleteAllNotReleasedByKeyAndLocalAndAppVersionNotIn(app.getKey(), getApplicationType(), true, marketVersions);
            log.info("sync delete app[{}] version whose version is not in [{}] and not released from local nexus", app.getKey(), marketVersions);
        });
    }

    private void deleteAllApps(List<String> appKeys, boolean local) {
        if (CollectionUtils.isEmpty(appKeys)) {
            return;
        }
        ApplicationType appType = getApplicationType();
        getRepo().deleteAllByKeyInAndLocal(appKeys, local, appType);
        getVersionedRepo().deleteAllByKeysInAndLocal(appKeys, local, appType);
        log.warn("sync delete apps {} from local nexus", appKeys);
    }

    private void syncResourcesToOSS(List<V> versions, Boolean local) {
        if (CollectionUtils.isEmpty(versions)) {
            return;
        }
        Map<String, ByteArrayInputStream> ossUrl2Resource = new HashMap<>();
        versions.stream().filter(it -> BooleanUtils.isNotTrue(it.getNotChanged())).forEach(it -> {
            T app = it.getApp();
            Set<String> allOssUrls = getAllOssUrls(app);
            if (CollectionUtils.isEmpty(allOssUrls)) {
                return;
            }

            allOssUrls.forEach(ossUrl -> {
                try {
                    String fileName = ossUrl.substring(ossUrl.lastIndexOf("/") + 1);
                    String extension = fileName.substring(fileName.lastIndexOf(".") + 1);
                    String classifier = AppOssUtils.getOssClassifier(ossUrl);
                    if (classifier == null) {
                        return;
                    }
                    String downloadUrl = getDownloadUrl(local, Maven.builder()
                            .groupId(getGroup())
                            .version(AppConsts.RESOURCES_VERSION)
                            .artifactId(app.getKey())
                            .classifier(classifier)
                            .extension(extension)
                            .build());
                    byte[] download = download(local, downloadUrl);
                    if (download == null) {
                        return;
                    }
                    ossUrl2Resource.put(ossUrl, new ByteArrayInputStream(download));
                } catch (Exception e) {
                    log.error("download resource from nexus failed", e);
                }
            });
        });
        AppOssUtils.syncToOSS(ossService, ossUrl2Resource);
    }
}
