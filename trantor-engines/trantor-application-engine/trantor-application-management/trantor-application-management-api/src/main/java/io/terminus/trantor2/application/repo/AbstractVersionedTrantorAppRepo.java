package io.terminus.trantor2.application.repo;

import io.terminus.trantor2.application.ApplicationType;
import io.terminus.trantor2.application.po.TrantorVersionedAppItemPO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface AbstractVersionedTrantorAppRepo<T extends TrantorVersionedAppItemPO<?>> extends JpaRepository<T, Long> {
    @Transactional
    @Modifying
    @Query("update TrantorVersionedAppItemPO item set item.deleted = NULL where item.key in (:keys) and item.local = :local" +
            " and item.appType = :appType")
    void deleteAllByKeysInAndLocal(@Param(value = "keys") List<String> keys, @Param(value = "local") @NonNull Boolean local, @Param("appType") ApplicationType appType);

    @Transactional
    @Modifying
    @Query("update TrantorVersionedAppItemPO item set item.deleted = NULL where item.key = :key and item.local = :local and item.appVersion not in (:versions) " +
            "and item.appType = :appType")
    void deleteAllByKeyAndLocalAndAppVersionNotIn(@Param(value = "key") @NonNull String key,
                                                  @Param(value = "appType") @NonNull ApplicationType appType,
                                                  @Param(value = "local") @NonNull Boolean local,
                                                  @Param(value = "versions") @NonNull Collection<String> versions);

    @Transactional
    @Modifying
    @Query("update TrantorVersionedAppItemPO item set item.deleted = NULL where item.key = :key and item.local = :local and item.appVersion not in (:versions) " +
            "and item.released = false " +
            "and item.appType = :appType")
    void deleteAllNotReleasedByKeyAndLocalAndAppVersionNotIn(@Param(value = "key") @NonNull String key,
                                                             @Param(value = "appType") @NonNull ApplicationType appType,
                                                             @Param(value = "local") @NonNull Boolean local,
                                                             @Param(value = "versions") @NonNull Collection<String> versions);

    @Transactional
    @Modifying
    @Query("update TrantorVersionedAppItemPO item set item.deleted = NULL where item.key = :key and item.local = true" +
            " and item.appType = :appType")
    void deleteAllByLocalTrueAndKey(@NonNull @Param(value = "key") String key, @Param(value = "appType") @NonNull ApplicationType appType);

    Optional<T> findOneByKeyAndEnabledTrueAndLocalTrue(@NonNull String key);


    Optional<T> findOneByKeyAndEnabledAndLocal(@NonNull String key, @NonNull Boolean enabled, @NonNull Boolean local);

    Optional<T> findOneByKeyAndAppVersionAndLocal(@NonNull String key, @NonNull String appVer, @NonNull Boolean local);

    Optional<T> findOneByKeyAndAppVersionAndLocalTrue(@NonNull String key, @NonNull String appVer);

    List<T> findAllByKeyAndReleasedTrueAndLocalTrue(@NonNull String key);

    List<T> findAllByKeyAndLocal(@NonNull String key, @NonNull Boolean local);

    default List<T> findAllLocalReleasedByKey(@NonNull String key) {
        return findAllByKeyAndReleasedTrueAndLocalTrue(key);
    }

}
