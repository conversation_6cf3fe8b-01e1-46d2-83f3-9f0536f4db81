package io.terminus.trantor2.application.po.convert;

import com.vladmihalcea.hibernate.util.StringUtils;
import io.terminus.trantor2.application.item.AppBaseInfo;
import io.terminus.trantor2.common.utils.JsonUtil;
import org.springframework.util.ObjectUtils;

import jakarta.persistence.AttributeConverter;

import static com.mysql.cj.xdevapi.JsonLiteral.NULL;

/**
 * <AUTHOR>
 */
public class ApplicationBaseInfoConverter implements AttributeConverter<AppBaseInfo, String> {

    @Override
    public String convertToDatabaseColumn(AppBaseInfo attribute) {
        if (ObjectUtils.isEmpty(attribute)) {
            return null;
        }
        return JsonUtil.NON_INDENT.toJson(attribute);
    }

    @Override
    public AppBaseInfo convertToEntityAttribute(String dbData) {
        if (StringUtils.isBlank(dbData) || NULL.name().equals(dbData)) {
            return null;
        }
        return JsonUtil.from<PERSON>son(dbData, AppBaseInfo.class);
    }
}
