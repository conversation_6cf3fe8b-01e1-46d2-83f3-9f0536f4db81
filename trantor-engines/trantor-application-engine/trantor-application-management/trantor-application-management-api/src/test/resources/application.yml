spring:
  main:
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  jpa:
    properties:
      hibernate:
        globally_quoted_identifiers: false
        format_sql: true
        hbm2ddl:
          increment_size: 10
          initial_value: 100
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.MySQLDialect
    defer-datasource-initialization: true
    show-sql: true

  liquibase:
    enabled: false
iam:
  mock: true
logging:
  level:
    org.springframework.security:
      - debug
      - info
    org.springframework.web: error
    org.hibernate.SQL: debug
    org.hibernate.engine.QueryParameters: debug
    org.hibernate.engine.query.HQLQueryPlan: debug
    org.hibernate.type.descriptor.sql.BasicBinder: trace
trantor2:
  nexus:
    pub:
      host:
      username: admin
      password:
      repository: trantor2-artifact
    local:
      host:
      username: admin
      password:
      repository: trantor2-artifact-private
      sync-enabled: ${TRANTOR_LOCAL_NEXUS_SYNC_ENABLED:false}
      dev-enabled: ${TRANTOR_LOCAL_NEXUS_DEV_ENABLED:false}
      enabled: ${TRANTOR_LOCAL_NEXUS_SYNC_ENABLED:${TRANTOR_LOCAL_NEXUS_DEV_ENABLED:false}}
