INSERT INTO `trantor_app_item` (`app_type`, `id`, `created_at`, `created_by`, `updated_at`, `updated_by`, `base_info`,
                                `deleted`, `description`, `icon`, `key`, `local`, `name`, `sort`, `version`)
VALUES ('TEST', 1, '2023-12-28 16:28:38.699000', 1, '2023-12-29 12:04:10.969000', 1, '{\"test\": \"EMPTY\"}', 0, NULL,
        NULL, 'empty', 0, 'a空白模板', 0, 0),
       ('TEST', 2, '2023-12-28 16:28:38.702000', 1, '2023-12-28 16:28:38.702000', 1,
        '{\"test\": \"BUSINESS_DOCUMENT\"}', 0, '包含列表、详情、编辑三个页面', NULL, 'business_document', 0,
        '单据场景abc模版', 1, 0),
       ('TEST', 3, '2023-12-28 16:28:38.703000', 1, '2023-12-29 12:04:10.967000', 1, '{\"test\": \"WORKBENCH\"}', 0,
        NULL, NULL, 'app_workbench', 0, '工作台模板', 2, 0),
       ('TEST', 4, '2023-12-29 12:26:29.773000', 1, '2023-12-29 12:26:29.773000', 1, '{\"test\": \"IAM\"}', 0, NULL,
        NULL, 'user_list', 1, '用户列表模版abcd', 0, 0),
       ('TEST', 5, '2023-12-29 12:26:29.793000', 1, '2023-12-29 12:26:29.793000', 1, '{\"test\": \"RULE_CONFIG\"}', 0,
        NULL, NULL, 'rule_config', 1, '规则配置模版abcd', 1, 0),
       ('TEST', 6, '2023-12-29 12:26:29.815000', 1, '2023-12-29 12:26:29.815000', 1, '{\"test\": \"REPORT\"}', 0,
        '默认有一个报表组件，主要用于查看统计信息', NULL, 'report', 1, '报表模版', 2, 0),
       ('TEST', 7, '2023-12-29 12:26:29.815000', 1, '2023-12-29 12:26:29.815000', 1, '{\"test\": \"REPORT\"}', 0,
        'unknown', NULL, 'unknown', 1, 'unknown', 3, 0);


INSERT INTO `trantor_versioned_app_item` (`app_type`, `id`, `created_at`, `created_by`, `updated_at`, `updated_by`,
                                          `version`, `app_version`, `content`, `deleted`, `enabled`, `key`, `local`,
                                          `released`, `app_id`, `compatibility_range`)
VALUES ('TEST', 1, '2023-12-29 12:04:11.144000', 1, '2023-12-29 12:04:11.144000', 1, 0, '1.0.0-SNAPSHOT',
        '{\"test\": \"hhh\"}',
        0, 1, 'empty', 0, 1, 1, '{\"start\": \"2.5.24.0430\"}'),
       ('TEST', 2, '2023-12-29 12:04:11.144000', 1, '2023-12-29 12:04:11.144000', 1, 0, '1.0.0-SNAPSHOT',
        '{\"test\": \"hhh\"}',
        0, 1, 'business_document', 0, 1, 2, '{\"start\": \"2.5.24.0430\"}'),
       ('TEST', 3, '2023-12-29 12:04:11.144000', 1, '2023-12-29 12:04:11.144000', 1, 0, '1.0.0-SNAPSHOT',
        '{\"test\": \"hhh\"}',
        0, 1, 'app_workbench', 0, 1, 3, '{\"start\": \"2.5.24.0430\"}'),
       ('TEST', 4, '2023-12-29 12:26:30.629000', 1, '2023-12-29 12:26:30.629000', 1, 0, '1.0.0',
        '{\"test\": \"hhh\"}', 0, 1, 'user_list', 1, 1, 4, '{\"start\": \"2.5.24.0430\"}'),
       ('TEST', 5, '2023-12-29 12:26:30.629000', 1, '2023-12-29 12:26:30.629000', 1, 0, '2.0.0',
        '{\"test\": \"hhh\"}', 0, 1, 'rule_config', 1, 1, 5, '{\"start\": \"2.5.24.0430\"}'),
       ('TEST', 6, '2023-12-29 12:26:30.629000', 1, '2023-12-29 12:26:30.629000', 1, 0, '3.0.0',
        '{\"test\": \"hhh\"}', 0, 1, 'report', 1, 0, 6, '{\"start\": \"2.5.24.0430\"}'),
       ('TEST', 7, '2023-12-29 12:26:30.629000', 1, '2023-12-29 12:26:30.629000', 1, 0, '2.0.0',
        '{\"test\": \"hhh\"}', 0, 0, 'user_list', 1, 0, 4, '{\"start\": \"2.5.24.0430\"}');
