package io.terminus.trantor2.application;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages =
        {"io.terminus.trantor2.application", "io.terminus.trantor2.nexus", "io.terminus.trantor2.properties.management.nexus"}
)
@EntityScan(basePackages = {"io.terminus.trantor2.application"})
@EnableJpaRepositories(basePackages = {"io.terminus.trantor2.application"})
public class ApplicationTestApplication {
    public static void main(String[] args) {
        SpringApplication.run(ApplicationTestApplication.class, args);
    }
}
