package io.terminus.trantor2.application.service;

import io.terminus.trantor2.application.TestAppConfig;
import io.terminus.trantor2.application.po.TestAppItemPO;
import io.terminus.trantor2.application.po.TestVersionedAppItemPO;
import io.terminus.trantor2.application.repo.AbstractTrantorAppRepo;
import io.terminus.trantor2.application.repo.AbstractVersionedTrantorAppRepo;
import io.terminus.trantor2.console.service.ConsoleService;
import io.terminus.trantor2.nexus.dto.NexusComponent;
import io.terminus.trantor2.nexus.service.NexusApiClient;
import io.terminus.trantor2.nexus.service.NexusApiClientFactory;
import io.terminus.trantor2.properties.management.nexus.NexusConfigProperties;
import io.terminus.trantor2.test.tool.mysql.MysqlSpringTest;
import io.terminus.trantor2.test.tool.nexus.NexusSpringTest;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.json.AutoConfigureJson;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DataJpaTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@AutoConfigureJson
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@Import(TestAppConfig.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class ApplicationSyncNexusServiceTest implements NexusSpringTest, MysqlSpringTest {
    private final String group = "io.terminus.trantor2.test";

    @Autowired
    NexusConfigProperties nexusConfigProperties;
    @Autowired
    NexusApiClientFactory nexusApiClientFactory;
    @Autowired
    TestAppSyncNexusService service;
    @MockBean
    ConsoleService consoleService;

    @Order(1)
    @Test
    @Sql("/sql/apps.sql")
    void syncToPubNexus() throws InterruptedException {
        AbstractTrantorAppRepo<TestAppItemPO> repo = service.getRepo();
        List<TestVersionedAppItemPO> versions = repo.findAllByLocal(true).stream().map(it -> it.getEnabledVersionedApp().isPresent() ? (TestVersionedAppItemPO) it.getEnabledVersionedApp().get() : null)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        NexusApiClient nexusApiClient = nexusApiClientFactory.getClient(true);
        List<NexusComponent> search = nexusApiClient.search(group, null, null, null);
        assertTrue(search.isEmpty());

        versions.forEach(v -> service.syncToNexus(v, true));

        Thread.sleep(1000);
        search = nexusApiClient.search(group, null, null, null);
        assertFalse(search.isEmpty());
        assertEquals(3, search.size());
    }

    @Test
    @Order(2)
    void syncFromNexus() {
        AbstractTrantorAppRepo<TestAppItemPO> repo = service.getRepo();
        List<TestAppItemPO> all = repo.findAll();
        assertTrue(all.isEmpty());

        NexusApiClient nexusApiClient = nexusApiClientFactory.getClient(true);
        // 从 nexus 同步
        service.syncFromNexus(nexusApiClient);
        // 校验
        all = repo.findAll();
        assertFalse(all.isEmpty());
        assertEquals(3, all.size());
    }

    @Test
    @Order(3)
    @Sql("/sql/apps_with_checksum.sql")
    void syncFromNexus_2_not_changed() {
        NexusApiClient nexusApiClient = nexusApiClientFactory.getClient(true);
        // 从 nexus 同步
        service.syncFromNexus(nexusApiClient);
        AbstractVersionedTrantorAppRepo<TestVersionedAppItemPO> versionedRepo = service.getVersionedRepo();

        List<TestVersionedAppItemPO> all = versionedRepo.findAll();
        assertEquals(3, all.size());
        assertEquals(2, all.stream().filter(it -> it.getVersion() == 0).count());
        assertEquals(1, all.stream().filter(it -> it.getVersion() > 0).count());
        assertEquals("rule_config", all.stream().filter(it -> it.getVersion() > 0).findFirst().get().getKey());
    }

    @Test
    @Order(4)
    void removeFromNexus() throws InterruptedException {
        service.removeFromNexus(null, true, null);
        Thread.sleep(1000);
        List<NexusComponent> search = nexusApiClientFactory.getClient(true)
                .search(group, null, null, null);
        assertTrue(search.isEmpty());
    }

    @Test
    @Order(Integer.MAX_VALUE)
    void clearNexusRepo() {
        clearNexusRepo(nexusApiClientFactory.getClient(true));
        clearNexusRepo(nexusApiClientFactory.getClient(false));
    }

    private void clearNexusRepo(NexusApiClient client) {
        List<NexusComponent> result = client.search(
                group,
                null,
                null,
                null);
        if (!CollectionUtils.isEmpty(result)) {
            result.forEach(component -> client.delete(component.getId()));
        }
    }
}