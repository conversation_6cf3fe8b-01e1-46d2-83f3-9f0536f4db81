package io.terminus.trantor2.application.service;

import io.terminus.trantor2.application.ApplicationType;
import io.terminus.trantor2.application.po.TestAppItemPO;
import io.terminus.trantor2.application.po.TestVersionedAppItemPO;
import io.terminus.trantor2.application.repo.AbstractTrantorAppRepo;
import io.terminus.trantor2.application.repo.AbstractVersionedTrantorAppRepo;
import io.terminus.trantor2.application.repo.TestTrantorAppRepo;
import io.terminus.trantor2.application.repo.TestVersionedTrantorAppRepo;
import io.terminus.trantor2.iam.service.TrantorIAMUserService;
import io.terminus.trantor2.console.service.ConsoleService;
import io.terminus.trantor2.properties.management.nexus.NexusConfigProperties;
import io.terminus.trantor2.nexus.service.NexusApiClientFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class TestAppManagerService extends AbstractAppManagerService<TestAppItemPO, TestVersionedAppItemPO> {
    private final TestTrantorAppRepo trantorAppRepo;
    private final TestVersionedTrantorAppRepo versionedTrantorAppRepo;

    protected TestAppManagerService(ConsoleService consoleService,
                                    NexusApiClientFactory nexusApiClientFactory,
                                    NexusConfigProperties nexusConfigProperties,
                                    TrantorIAMUserService trantorIAMUserService,
                                    TestTrantorAppRepo trantorAppRepo,
                                    TestVersionedTrantorAppRepo versionedTrantorAppRepo) {
        super(consoleService, nexusApiClientFactory, nexusConfigProperties, trantorIAMUserService);
        this.trantorAppRepo = trantorAppRepo;
        this.versionedTrantorAppRepo = versionedTrantorAppRepo;
    }

    @Override
    public ApplicationType getApplicationType() {
        return ApplicationType.valueOf("TEST");
    }

    @Override
    public String getGroup() {
        return "io.terminus.trantor2.test";
    }

    @Override
    public String getExtension() {
        return "json";
    }

    @Override
    public AbstractTrantorAppRepo<TestAppItemPO> getRepo() {
        return trantorAppRepo;
    }

    @Override
    public AbstractVersionedTrantorAppRepo<TestVersionedAppItemPO> getVersionedRepo() {
        return versionedTrantorAppRepo;
    }

}
