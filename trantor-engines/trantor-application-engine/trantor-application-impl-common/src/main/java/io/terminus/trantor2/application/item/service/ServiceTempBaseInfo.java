package io.terminus.trantor2.application.item.service;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.application.item.AppBaseInfo;
import io.terminus.trantor2.service.common.enums.ServiceType;
import lombok.Data;

import java.util.List;

/**
 * ServiceTempContent
 *
 * <AUTHOR> Created on 2024/1/8 13:55
 */
@Data
public class ServiceTempBaseInfo implements AppBaseInfo {

    @Schema(description = "模版类型")
    private ServiceType serviceType;

    @Schema(description = "模版描述")
    private String templateDesc;

    @Schema(description = "模版分类 Key")
    private String templateCategoryKey;

    @Schema(description = "模版分类名称")
    private String templateCategoryName;

    @Schema(description = "模版缩略图")
    private String templateThumbnail;

    @Schema(description = "子服务模版列表")
    private List<SubServiceInfo> subTemplateInfos;

}
