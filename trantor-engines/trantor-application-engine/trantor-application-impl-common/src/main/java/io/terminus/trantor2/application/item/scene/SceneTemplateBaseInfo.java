package io.terminus.trantor2.application.item.scene;

import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.application.item.AppBaseInfo;
import io.terminus.trantor2.module.meta.EndpointType;
import io.terminus.trantor2.scene.template.TemplateType;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SceneTemplateBaseInfo implements AppBaseInfo {
    @Schema(description = "模版自身配置")
    private JsonNode templateConfig;

    @Schema(description = "模版类型")
    private TemplateType type;

    @Schema(description = "场景类型")
    private String sceneType;

    @Schema(description = "终端类型")
    private EndpointType endpointType;

    @Schema(description = "视图模版预览图")
    private List<String> images;
}
