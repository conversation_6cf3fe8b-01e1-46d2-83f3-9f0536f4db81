package io.terminus.trantor2.application.item;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.terminus.trantor2.application.item.connector.CtTempContent;
import io.terminus.trantor2.application.item.scene.SceneTemplateContent;
import io.terminus.trantor2.application.item.service.ServiceTempContent;
import io.terminus.trantor2.application.item.sysmodule.SystemModuleContent;

/**
 * <AUTHOR>
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.DEDUCTION)
@JsonSubTypes({
    @JsonSubTypes.Type(value = SceneTemplateContent.class),
    @JsonSubTypes.Type(value = CtTempContent.class),
    @JsonSubTypes.Type(value = ServiceTempContent.class),
    @JsonSubTypes.Type(value = SystemModuleContent.class)
})
public interface AppContent {
}
