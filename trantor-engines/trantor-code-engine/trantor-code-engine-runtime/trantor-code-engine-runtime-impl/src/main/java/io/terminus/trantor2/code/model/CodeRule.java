package io.terminus.trantor2.code.model;

import io.terminus.trantor2.code.dict.RefreshCycleDict;
import io.terminus.trantor2.code.dict.SerialDirectionDict;
import io.terminus.trantor2.rule.engine.api.enums.LogicOperatorEnum;
import io.terminus.trantor2.rule.engine.api.model.dto.condition.ConditionDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CodeRule implements Serializable {
    private static final long serialVersionUID = -3630159718310856502L;

    private String ruleKey;
    private Long serialFrom;
    private Long serialTo;
    private Integer serialLength;
    private SerialDirectionDict serialDirection;
    private RefreshCycleDict circleType;
    private String circleKey;
    private Boolean overflow;
    private Boolean defaultFalg;
    private List<Item> rules;
    private ConditionExpress express;

    @Data
    public static final class Item implements Serializable {
        private static final long serialVersionUID = -7186259685874068021L;
        private String ruleType;
        private String value;
    }

    @Data
    public static final class ConditionExpress implements Serializable {
        private static final long serialVersionUID = -98535193643999990L;
        private LogicOperatorEnum logicOperator;
        private List<List<ConditionDTO>> conditions;
    }
}
