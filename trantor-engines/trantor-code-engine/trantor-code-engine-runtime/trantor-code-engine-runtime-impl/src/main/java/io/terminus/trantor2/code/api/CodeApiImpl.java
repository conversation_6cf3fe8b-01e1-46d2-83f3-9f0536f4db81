package io.terminus.trantor2.code.api;

import io.terminus.common.sequence.code.CodeGenerateV2Service;
import io.terminus.trantor2.code.exception.CodeGenerateException;
import io.terminus.trantor2.code.request.BatchTakeCodeRequest;
import io.terminus.trantor2.code.request.TakeCodeRequest;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.exception.ErrorType;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: 诸立达
 * @Date: 2023/7/31 9:15 下午
 */
@RestController
@RequiredArgsConstructor
public class CodeApiImpl implements CodeApi {
    // private final CodeGenerateService codeGenerateService;
    private final CodeGenerateV2Service codeGenerateV2Service;

    @Override
    public Response<String> takeCode(TakeCodeRequest takeCodeRequest) {
//        try {
//            String code = codeGenerateService.generateShardingCode(takeCodeRequest);
//            return Response.ok(code);
//        } catch (CodeGenerateException e) {
//            return Response.error(e.getCode(), e.getErrorMsg());
//        }
        throw new UnsupportedOperationException("此接口trantor-241030版本之后废弃，请升级编码中心SDK版本至1.2.0");
    }

    @Override
    public Response<String> sysTakeCode(TakeCodeRequest takeCodeRequest) {
        if (!StringUtils.hasText(takeCodeRequest.getModelKey())) {
            throw new CodeGenerateException(ErrorType.NOT_FIND_MODEL_ERROR, String.format(ErrorType.NOT_FIND_MODEL_ERROR.getMessage(), TrantorContext.getTeamId(), takeCodeRequest.getModelKey()));
        }
        return Response.ok(codeGenerateV2Service.generateShardingCode(takeCodeRequest));
    }

    public Response<List<String>> takeCodes(BatchTakeCodeRequest batchTakeCodeRequest) {
//        try {
//            List<String> codes = codeGenerateService.generateShardingCode(batchTakeCodeRequest.getTakeCodeRequest(), batchTakeCodeRequest.getCount());
//            return Response.ok(codes);
//        } catch (CodeGenerateException e) {
//            return Response.error(e.getCode(), e.getMessage());
//        }
        throw new UnsupportedOperationException("此接口trantor-241030版本之后废弃，请升级编码中心SDK版本至1.2.0");
    }

    @Override
    public Response<Void> flushCache(TakeCodeRequest takeCodeRequest) {
//        codeGenerateService.flushCache(takeCodeRequest);
//        return Response.ok();
        throw new UnsupportedOperationException("此接口trantor-241030版本之后废弃，请升级编码中心SDK版本至1.2.0");
    }

}
