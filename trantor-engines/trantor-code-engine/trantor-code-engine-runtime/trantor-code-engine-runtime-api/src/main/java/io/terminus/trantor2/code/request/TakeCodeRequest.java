package io.terminus.trantor2.code.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @Author: 诸立达
 * @Date: 2023/5/9 1:54 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TakeCodeRequest {

    private String modelKey;

    private String ruleKey;

    /**
     * 为了兼容历史项目，不要删除
     */
    @Deprecated
    private Long appId;

    private Long teamId;

    private Map<String,Object> conditionMap;

    private Map<String,Object> dynamicParamMap;

}
