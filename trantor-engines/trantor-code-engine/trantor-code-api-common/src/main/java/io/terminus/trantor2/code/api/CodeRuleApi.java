package io.terminus.trantor2.code.api;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.common.api.model.Paging;
import io.terminus.sequence.model.CodeRuleDeleteRequest;
import io.terminus.sequence.model.CodeRuleDetailRequest;
import io.terminus.sequence.model.CodeRulePagingRequest;
import io.terminus.sequence.model.CodeRuleSaveRequest;
import io.terminus.sequence.model.CodeRuleVO;
import io.terminus.trantor2.common.dto.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Author: 诸立达
 * @Date: 2023/3/7 11:06 上午
 */
@Tag(name = "取号规则管理接口")
@FeignClient(name = "code-rule")
public interface CodeRuleApi {

    @Operation(summary = "创建编码规则")
    @PostMapping("/api/trantor/code-rule/create")
    Response<Long> create(@RequestBody CodeRuleSaveRequest request);

    @Operation(summary = "修改编码规则")
    @PostMapping("/api/trantor/code-rule/update")
    Response<Long> update(@RequestBody CodeRuleSaveRequest request);

    @Operation(summary = "删除编码规则")
    @PostMapping("/api/trantor/code-rule/delete")
    Response<Boolean> delete(@RequestBody CodeRuleDeleteRequest request);

    @Operation(summary = "删除编码规则")
    @PostMapping("/api/trantor/code-rule/page")
    Response<Paging<CodeRuleVO>> page(@RequestBody CodeRulePagingRequest request);

    @Operation(summary = "删除编码规则")
    @PostMapping("/api/trantor/code-rule/detail")
    Response<CodeRuleVO> detail(@RequestBody CodeRuleDetailRequest request);

}
