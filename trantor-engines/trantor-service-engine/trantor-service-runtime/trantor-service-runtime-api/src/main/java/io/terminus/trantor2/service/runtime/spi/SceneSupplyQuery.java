package io.terminus.trantor2.service.runtime.spi;


import io.terminus.trantor2.service.runtime.spi.bean.SceneSupply;
import io.terminus.trantor2.service.runtime.spi.bean.SceneSupplyRequest;

import jakarta.annotation.Nullable;

/**
 * SceneSupplyQuery
 *
 * <AUTHOR> Created on 2023/4/10 10:48
 * @deprecated 请使用 {@link io.terminus.trantor2.service.common.component.spi.SceneSupplyQuery}
 */
@Deprecated
public interface SceneSupplyQuery {

    @Nullable
    SceneSupply query(SceneSupplyRequest request);
}
