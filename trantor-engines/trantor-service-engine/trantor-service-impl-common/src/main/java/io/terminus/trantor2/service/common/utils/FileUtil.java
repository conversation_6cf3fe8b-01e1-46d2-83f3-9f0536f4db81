package io.terminus.trantor2.service.common.utils;

import org.apache.commons.io.FileUtils;

import java.io.File;
import java.util.UUID;

/**
 * FileUtil
 *
 * <AUTHOR> Created on 2024/9/25 11:27
 */
public class FileUtil {

    public static File createTempFile(String filename) {
        File tempDirectory = new File(FileUtils.getTempDirectoryPath() + File.separator + UUID.randomUUID());
        if (!tempDirectory.mkdirs() && !tempDirectory.isDirectory()) {
            throw new IllegalArgumentException("Cannot create directory '" + tempDirectory + "'.");
        }
        return new File(tempDirectory, filename);
    }

    public static String getPostfix(String filename) {
        if (filename != null && filename.contains(".")) {
            return filename.substring(filename.lastIndexOf(".") + 1);
        }
        return null;
    }
}
