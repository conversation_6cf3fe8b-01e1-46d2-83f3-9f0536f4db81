package io.terminus.trantor2.service.common.utils;

import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.Duration;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Setter
public class TrantorSseEmitter extends SseEmitter {
    private Map<String, StringBuilder> events = Maps.newConcurrentMap();
    @Getter
    private boolean finished;

    public TrantorSseEmitter() {
        super(Duration.ofMinutes(10).toMillis());
    }

    public Map<String, String> getEvents() {
        return events.entrySet()
                     .stream()
                     .collect(Collectors.toMap(Map.Entry::getKey, item -> item.getValue().toString()));
    }

    public void append(Object data) {
        append("text", data);
    }

    public void append(String eventName, Object data) {
        StringBuilder stringBuilder;
        if (events.containsKey(eventName)) {
            stringBuilder = events.get(eventName);
        } else {
            stringBuilder = new StringBuilder();
        }
        stringBuilder.append(data);
        events.put(eventName, stringBuilder);
    }

    public void complete() {
        finished = true;
        super.complete();
    }
}
