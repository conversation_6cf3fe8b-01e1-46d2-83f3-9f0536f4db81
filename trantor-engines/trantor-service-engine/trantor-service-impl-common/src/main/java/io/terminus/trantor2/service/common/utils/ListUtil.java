package io.terminus.trantor2.service.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2018/5/7
 */
@Slf4j
public class ListUtil {

    private ListUtil() {}

    @SafeVarargs
    public static <T> List<T> of(T... ts) {
        List<T> list = new ArrayList<>();
        Collections.addAll(list, ts);
        return list;
    }

    public static <T> List<T> emptyList() {
        return new ArrayList<>();
    }

    public static <T> boolean isEmpty(List<T> list) {
        return list == null || list.isEmpty();
    }

    public static <T> boolean nonEmpty(List<T> list) {
        return list != null && !list.isEmpty();
    }

    public static <T> Optional<T> first(List<T> list) {
        if (nonEmpty(list)) {
            return Optional.of(list.get(0));
        } else {
            return Optional.empty();
        }
    }

    public static <T> Optional<T> last(List<T> list) {
        if (nonEmpty(list)) {
            return Optional.of(list.get(list.size() - 1));
        } else {
            return Optional.empty();
        }
    }
}
