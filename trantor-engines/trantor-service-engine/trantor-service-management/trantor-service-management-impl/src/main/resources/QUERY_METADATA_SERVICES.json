[{"metaServiceName": "查询所有模型元数据", "metaServiceKey": "QUERY_ALL_MODELS", "input": [{"fieldKey": "keyword", "fieldName": "搜索关键词", "fieldType": "Text"}, {"fieldKey": "containsFields", "fieldName": "是否包含模型字段", "fieldType": "Boolean"}, {"fieldKey": "pageNo", "fieldName": "页码", "fieldType": "Number", "required": true}, {"fieldKey": "pageSize", "fieldName": "每页大小", "fieldType": "Number", "required": true}], "output": [{"fieldKey": "data", "fieldName": "模型元数据分页结果", "fieldType": "Paging", "elements": [{"fieldKey": "total", "fieldName": "total", "fieldType": "Number"}, {"fieldKey": "data", "fieldName": "data", "fieldType": "Array", "element": {"fieldKey": "element", "fieldName": "element", "fieldType": "Object", "elements": [{"fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "模型Key", "fieldType": "Text"}, {"fieldKey": "modelName", "fieldName": "模型名称", "fieldType": "Text"}, {"fieldKey": "modelDescription", "fieldName": "模型描述", "fieldType": "Text"}, {"fieldKey": "fields", "fieldName": "模型字段", "fieldType": "Array"}]}}]}]}, {"metaServiceName": "查询模型详情元数据", "metaServiceKey": "QUERY_MODEL_DETAIL", "input": [{"fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "模型Key", "fieldType": "Text", "required": true}], "output": [{"fieldKey": "data", "fieldName": "模型元数据详情", "fieldType": "Object", "elements": [{"fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "模型Key", "fieldType": "Text"}, {"fieldKey": "modelName", "fieldName": "模型名称", "fieldType": "Text"}, {"fieldKey": "modelDescription", "fieldName": "模型描述", "fieldType": "Text"}, {"fieldKey": "fields", "fieldName": "模型字段", "fieldType": "Array"}]}]}, {"metaServiceName": "查询所有服务元数据", "metaServiceKey": "QUERY_ALL_SERVICES", "input": [{"fieldKey": "keyword", "fieldName": "搜索关键词", "fieldType": "Text"}, {"fieldKey": "containsInputAndOutput", "fieldName": "是否包含服务入参和出参", "fieldType": "Boolean"}, {"fieldKey": "pageNo", "fieldName": "页码", "fieldType": "Number", "required": true}, {"fieldKey": "pageSize", "fieldName": "每页大小", "fieldType": "Number", "required": true}], "output": [{"fieldKey": "data", "fieldName": "服务元数据分页结果", "fieldType": "Paging", "elements": [{"fieldKey": "total", "fieldName": "total", "fieldType": "Number"}, {"fieldKey": "data", "fieldName": "data", "fieldType": "Array", "element": {"fieldKey": "element", "fieldName": "element", "fieldType": "Object", "elements": [{"fieldKey": "serviceKey", "fieldName": "服务Key", "fieldType": "Text"}, {"fieldKey": "serviceName", "fieldName": "服务名称", "fieldType": "Text"}, {"fieldKey": "serviceDescription", "fieldName": "服务描述", "fieldType": "Text"}, {"fieldKey": "input", "fieldName": "服务入参", "fieldType": "Array"}, {"fieldKey": "output", "fieldName": "服务出参", "fieldType": "Array"}]}}]}]}, {"metaServiceName": "查询服务详情元数据", "metaServiceKey": "QUERY_SERVICE_DETAIL", "input": [{"fieldKey": "serviceKey", "fieldName": "服务Key", "fieldType": "Text", "required": true}], "output": [{"fieldKey": "data", "fieldName": "服务详情元数据", "fieldType": "Object", "elements": [{"fieldKey": "serviceKey", "fieldName": "服务Key", "fieldType": "Text"}, {"fieldKey": "serviceName", "fieldName": "服务名称", "fieldType": "Text"}, {"fieldKey": "serviceDescription", "fieldName": "服务描述", "fieldType": "Text"}, {"fieldKey": "input", "fieldName": "服务入参", "fieldType": "Array"}, {"fieldKey": "output", "fieldName": "服务出参", "fieldType": "Array"}]}]}, {"metaServiceName": "查询场景关联的模型元数据", "metaServiceKey": "QUERY_MODELS_FROM_SCENE", "input": [{"fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "场景Key", "fieldType": "Text", "required": true}, {"fieldKey": "containsFields", "fieldName": "是否包含模型字段", "fieldType": "Boolean"}], "output": [{"fieldKey": "data", "fieldName": "模型元数据列表", "fieldType": "Array", "element": {"fieldKey": "element", "fieldName": "element", "fieldType": "Object", "elements": [{"fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "模型Key", "fieldType": "Text"}, {"fieldKey": "modelName", "fieldName": "模型名称", "fieldType": "Text"}, {"fieldKey": "modelDescription", "fieldName": "模型描述", "fieldType": "Text"}, {"fieldKey": "fields", "fieldName": "模型字段", "fieldType": "Array"}]}}]}, {"metaServiceName": "查询场景关联服务元数据（包含业务事件）", "metaServiceKey": "QUERY_SERVICES_FROM_SCENE", "input": [{"fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "场景Key", "fieldType": "Text", "required": true}, {"fieldKey": "containsInputAndOutput", "fieldName": "是否包含服务入参和出参", "fieldType": "Boolean"}], "output": [{"fieldKey": "data", "fieldName": "服务元数据列表", "fieldType": "Array", "element": {"fieldKey": "element", "fieldName": "element", "fieldType": "Object", "elements": [{"fieldKey": "serviceKey", "fieldName": "服务Key", "fieldType": "Text"}, {"fieldKey": "serviceName", "fieldName": "服务名称", "fieldType": "Text"}, {"fieldKey": "serviceDescription", "fieldName": "服务描述", "fieldType": "Text"}, {"fieldKey": "input", "fieldName": "服务入参", "fieldType": "Array"}, {"fieldKey": "output", "fieldName": "服务出参", "fieldType": "Array"}]}}]}, {"metaServiceName": "查询门户下的菜单元数据", "metaServiceKey": "QUERY_MENUS_FROM_PORTAL", "input": [{"fieldKey": "portalKey", "fieldName": "门户Key", "fieldType": "Text", "required": true}], "output": [{"fieldKey": "data", "fieldName": "菜单元数据列表", "fieldType": "Array", "element": {"fieldKey": "element", "fieldName": "element", "fieldType": "Object", "elements": [{"fieldKey": "<PERSON><PERSON>ey", "fieldName": "菜单Key", "fieldType": "Text"}, {"fieldKey": "menuName", "fieldName": "菜单名称", "fieldType": "Text"}]}}]}]