{
    "type": "ServiceDefinition",
    "id": null,
    "key": "((modelKeyUpperCase))_REVERSE_CONSTRUCT_TREE_SERVICE_FIND_PARENT_NODES((serviceUniqueKey))",
        "name": "((name))-反向构建树-查询父节点服务((serviceUniqueKey))",
        "props": {
            "type": "ServiceProperties",
            "name": null,
            "desc": null,
            "transactionPropagation": "NOT_SUPPORTED",
            "aiService": null,
            "aiChatMode": null,
            "aiRoundsStrategy": null,
            "permissionKey": "((#ifCond functionPermissionKey "==" null))((modelKeyUpperCase)):DATA_QUERY_PERMISSION((else))((functionPermissionKey))((/ifCond))"
        },
        "headNodeKeys": [
            "node_1hoopt7ks4"
        ],
        "children": [
            {
                "type": "StartNode",
                "id": null,
                "key": "node_1hoopt7ks4",
                "name": "开始",
                "props": {
                    "type": "StartProperties",
                    "name": null,
                    "desc": null,
                    "input": [
                        {
                            "id": null,
                            "fieldKey": "depth",
                            "fieldAlias": "depth",
                            "fieldName": "depth",
                            "fieldType": "Number",
                            "description": null,
                            "required": null,
                            "defaultValue": null
                        },
                        {
                            "id": null,
                            "fieldKey": "currentNode",
                            "fieldAlias": "currentNode",
                            "fieldName": "currentNode",
                            "fieldType": "Model",
                            "description": null,
                            "required": null,
                            "defaultValue": null,
                            "relatedModel": {
                                "modelKey": "((key))",
                                "modelAlias": "((key))",
                                "modelName": null
                            },
                            "relation": null
                        },
                        {
                            "id": null,
                            "fieldKey": "treeNodeMap",
                            "fieldAlias": "treeNodeMap",
                            "fieldName": "treeNodeMap",
                            "fieldType": "Object",
                            "description": null,
                            "required": null,
                            "defaultValue": null,
                            "elements": null
                        }
                    ],
                    "output": [
                        {
                            "id": null,
                            "fieldKey": "data",
                            "fieldAlias": "data",
                            "fieldName": "data",
                            "fieldType": "Object",
                            "description": null,
                            "required": null,
                            "defaultValue": null,
                            "elements": null
                        }
                    ],
                    "globalVariable": null
                },
                "headNodeKeys": null,
                "children": null,
                "renderType": null,
                "preNodeKey": null,
                "nextNodeKey": "node_1hooqqol26"
            },
            {
                "type": "ExclusiveBranchNode",
                "id": null,
                "key": "node_1hooqqol26",
                "name": "排他分支",
                "props": {
                    "type": "ExclusiveBranchProperties",
                    "name": null,
                    "desc": null
                },
                "headNodeKeys": [
                    "node_1hooqqol27",
                    "node_1hooqqol28"
                ],
                "children": [
                    {
                        "type": "ConditionNode",
                        "id": null,
                        "key": "node_1hooqqol27",
                        "name": "最大深度不能超过 10",
                        "props": {
                            "type": "ConditionProperties",
                            "name": null,
                            "desc": null,
                            "conditionGroup": {
                                "type": "ConditionGroup",
                                "id": "iwbI0FRwx7IkWRl2iRNpr",
                                "conditions": [
                                    {
                                        "type": "ConditionGroup",
                                        "id": "WFthWkgNtnxIUiZN-j6pF",
                                        "conditions": [
                                            {
                                                "type": "ConditionLeaf",
                                                "id": "ITP_gM092_aXvHdjOrnmg",
                                                "key": null,
                                                "leftValue": {
                                                    "type": "VarValue",
                                                    "id": null,
                                                    "varValue": [
                                                        {
                                                            "valueKey": "REQUEST",
                                                            "valueName": "服务入参",
                                                            "modelAlias": null,
                                                            "relatedModel": null
                                                        },
                                                        {
                                                            "valueKey": "depth",
                                                            "valueName": "depth",
                                                            "modelAlias": null,
                                                            "relatedModel": null
                                                        }
                                                    ],
                                                    "constValue": null,
                                                    "valueType": "VAR",
                                                    "fieldType": "Number"
                                                },
                                                "operator": "LTE",
                                                "rightValue": {
                                                    "type": "ConstValue",
                                                    "id": null,
                                                    "fieldType": "Number",
                                                    "constValue": "10"
                                                },
                                                "rightValues": null
                                            }
                                        ],
                                        "logicOperator": "AND"
                                    }
                                ],
                                "logicOperator": "OR"
                            }
                        },
                        "headNodeKeys": null,
                        "children": null,
                        "renderType": null,
                        "preNodeKey": null,
                        "nextNodeKey": "node_1hooqt1qs10"
                    },
                    {
                        "type": "ScriptNode",
                        "id": null,
                        "key": "node_1hooqt1qs10",
                        "name": "在线JS脚本",
                        "props": {
                            "type": "ScriptProperties",
                            "name": null,
                            "desc": null,
                            "language": "JS",
                            "script": "  if (treeNodeMap == null) {    treeNodeMap = new java.util.HashMap();  }    if (currentNode != null) {    treeNodeMap.put(currentNode.get('id'), currentNode);  }  return treeNodeMap;",
                            "inputMapping": [
                                {
                                    "id": "1hooqvu3111",
                                    "field": {
                                        "id": null,
                                        "fieldKey": "treeNodeMap",
                                        "fieldAlias": "treeNodeMap",
                                        "fieldName": "treeNodeMap",
                                        "fieldType": "Object",
                                        "description": null,
                                        "required": null,
                                        "defaultValue": null,
                                        "elements": null
                                    },
                                    "value": {
                                        "type": "VarValue",
                                        "id": null,
                                        "varValue": [
                                            {
                                                "valueKey": "REQUEST",
                                                "valueName": "服务入参",
                                                "modelAlias": null,
                                                "relatedModel": null
                                            },
                                            {
                                                "valueKey": "treeNodeMap",
                                                "valueName": "treeNodeMap",
                                                "modelAlias": null,
                                                "relatedModel": null
                                            }
                                        ],
                                        "constValue": null,
                                        "valueType": "VAR",
                                        "fieldType": "Object"
                                    }
                                },
                                {
                                    "id": "1hoor39h712",
                                    "field": {
                                        "id": null,
                                        "fieldKey": "currentNode",
                                        "fieldAlias": "currentNode",
                                        "fieldName": "currentNode",
                                        "fieldType": "Object",
                                        "description": null,
                                        "required": null,
                                        "defaultValue": null,
                                        "elements": null
                                    },
                                    "value": {
                                        "type": "VarValue",
                                        "id": null,
                                        "varValue": [
                                            {
                                                "valueKey": "REQUEST",
                                                "valueName": "服务入参",
                                                "modelAlias": null,
                                                "relatedModel": null
                                            },
                                            {
                                                "valueKey": "currentNode",
                                                "valueName": "currentNode",
                                                "modelAlias": null,
                                                "relatedModel": null
                                            }
                                        ],
                                        "constValue": null,
                                        "valueType": "VAR",
                                        "fieldType": "Object"
                                    }
                                }
                            ],
                            "output": [
                                {
                                    "id": null,
                                    "fieldKey": "data",
                                    "fieldAlias": "data",
                                    "fieldName": "data",
                                    "fieldType": "Object",
                                    "description": null,
                                    "required": false,
                                    "defaultValue": null,
                                    "elements": null
                                }
                            ],
                            "outputAssign": {
                                "outputAssignType": "SYSTEM",
                                "customAssignments": null
                            }
                        },
                        "headNodeKeys": null,
                        "children": null,
                        "renderType": null,
                        "preNodeKey": null,
                        "nextNodeKey": "node_1hoormn8m14"
                    },
                    {
                        "type": "ExclusiveBranchNode",
                        "id": null,
                        "key": "node_1hoormn8m14",
                        "name": "排他分支",
                        "props": {
                            "type": "ExclusiveBranchProperties",
                            "name": null,
                            "desc": null
                        },
                        "headNodeKeys": [
                            "node_1hoormn8m15",
                            "node_1hoormn8m16"
                        ],
                        "children": [
                            {
                                "type": "ConditionNode",
                                "id": null,
                                "key": "node_1hoormn8m15",
                                "name": "父 ID 不为空",
                                "props": {
                                    "type": "ConditionProperties",
                                    "name": null,
                                    "desc": null,
                                    "conditionGroup": {
                                        "type": "ConditionGroup",
                                        "id": "8K2IhZdK78J32tuRWUwei",
                                        "conditions": [
                                            {
                                                "type": "ConditionGroup",
                                                "id": "bq8KEa5KiQ24USv0r47eu",
                                                "conditions": [
                                                    {
                                                        "type": "ConditionLeaf",
                                                        "id": "Z1WtNTyc59_gcg17KNMCk",
                                                        "key": null,
                                                        "leftValue": {
                                                            "type": "VarValue",
                                                            "id": null,
                                                            "varValue": [
                                                                {
                                                                    "valueKey": "REQUEST",
                                                                    "valueName": "服务入参",
                                                                    "modelAlias": null,
                                                                    "relatedModel": null
                                                                },
                                                                {
                                                                    "valueKey": "currentNode",
                                                                    "valueName": "currentNode",
                                                                    "modelAlias": null,
                                                                    "relatedModel": null
                                                                },
                                                                {
                                                                    "valueKey": "((pid))",
                                                                    "valueName": "((pid))",
                                                                    "modelAlias": null,
                                                                    "relatedModel": null
                                                                }
                                                            ],
                                                            "constValue": null,
                                                            "valueType": "VAR",
                                                            "fieldType": "Number"
                                                        },
                                                        "operator": "IS_NOT_NULL",
                                                        "rightValue": null,
                                                        "rightValues": null
                                                    }
                                                ],
                                                "logicOperator": "AND"
                                            }
                                        ],
                                        "logicOperator": "OR"
                                    }
                                },
                                "headNodeKeys": null,
                                "children": null,
                                "renderType": null,
                                "preNodeKey": null,
                                "nextNodeKey": "node_1hooqs18d9"
                            },
                            {
                                "type": "RetrieveDataNode",
                                "id": null,
                                "key": "node_1hooqs18d9",
                                "name": "查询数据",
                                "props": {
                                    "type": "RetrieveDataProperties",
                                    "name": null,
                                    "desc": null,
                                    "relatedModel": {
                                        "modelKey": "((key))",
                                        "modelAlias": "((key))",
                                        "modelName": "((name))"
                                    },
                                    "dataType": "MODEL",
                                    "pageable": null,
                                    "conditionGroup": {
                                        "type": "ConditionGroup",
                                        "id": "Sc5igoAFuAZYXOHWtpOfq",
                                        "conditions": [
                                            {
                                                "type": "ConditionGroup",
                                                "id": "rIYQV9fQ7b0MlqDPcB9HH",
                                                "conditions": [
                                                    {
                                                        "type": "ConditionLeaf",
                                                        "id": "hctWNBV56hU5Mttl9knqF",
                                                        "key": null,
                                                        "leftValue": {
                                                            "type": "VarValue",
                                                            "id": null,
                                                            "varValue": [
                                                                {
                                                                    "valueKey": "id",
                                                                    "valueName": "ID",
                                                                    "modelAlias": "((key))",
                                                                    "relatedModel": null
                                                                }
                                                            ],
                                                            "constValue": null,
                                                            "valueType": "MODEL",
                                                            "fieldType": "Number"
                                                        },
                                                        "operator": "EQ",
                                                        "rightValue": {
                                                            "type": "VarValue",
                                                            "id": null,
                                                            "varValue": [
                                                                {
                                                                    "valueKey": "REQUEST",
                                                                    "valueName": "服务入参",
                                                                    "modelAlias": null,
                                                                    "relatedModel": null
                                                                },
                                                                {
                                                                    "valueKey": "currentNode",
                                                                    "valueName": "currentNode",
                                                                    "modelAlias": null,
                                                                    "relatedModel": {
                                                                        "modelKey": "((key))",
                                                                        "modelAlias": "((key))",
                                                                        "modelName": null
                                                                    }
                                                                },
                                                                {
                                                                    "valueKey": "((pid))",
                                                                    "valueName": "父ID",
                                                                    "modelAlias": "((key))",
                                                                    "relatedModel": {
                                                                        "modelKey": "((key))",
                                                                        "modelAlias": "((key))",
                                                                        "modelName": "((key))"
                                                                    }
                                                                },
                                                                {
                                                                    "valueKey": "id",
                                                                    "valueName": "ID",
                                                                    "modelAlias": "((key))",
                                                                    "relatedModel": {
                                                                        "modelKey": null,
                                                                        "modelAlias": null,
                                                                        "modelName": null
                                                                    }
                                                                }
                                                            ],
                                                            "constValue": null,
                                                            "valueType": "VAR",
                                                            "fieldType": "Number"
                                                        },
                                                        "rightValues": null
                                                    }
                                                ],
                                                "logicOperator": "AND"
                                            }
                                        ],
                                        "logicOperator": "OR"
                                    },
                                    "sortOrder": null,
                                    "sortOrders": null,
                                    "stopWhenDataEmpty": false,
                                    "queryModelFields": ((queryModelFields)),
                                    "maximum": null,
                                    "outputAssign": {
                                        "outputAssignType": "SYSTEM",
                                        "customAssignments": null
                                    }
                                },
                                "headNodeKeys": null,
                                "children": null,
                                "renderType": null,
                                "preNodeKey": null,
                                "nextNodeKey": "node_1hoquvo9a36"
                            },
                            {
                                "type": "CallServiceNode",
                                "id": null,
                                "key": "node_1hoquvo9a36",
                                "name": "调用编排服务",
                                "props": {
                                    "type": "CallServiceProperties",
                                    "name": null,
                                    "desc": null,
                                    "serviceKey": "((modelKeyUpperCase))_REVERSE_CONSTRUCT_TREE_SERVICE_FIND_PARENT_NODES((serviceUniqueKey))",
                                    "serviceName": "((name))-反向构建树-查询父节点服务((serviceUniqueKey))",
                                    "transactionPropagation": "NOT_SUPPORTED",
                                    "inputMapping": [
                                        {
                                            "id": "1hoqv0ag140",
                                            "field": {
                                                "id": null,
                                                "fieldKey": "depth",
                                                "fieldAlias": "depth",
                                                "fieldName": "depth",
                                                "fieldType": "Number",
                                                "description": null,
                                                "required": null,
                                                "defaultValue": null
                                            },
                                            "value": {
                                                "type": "FuncValue",
                                                "id": null,
                                                "funcExpression": "ADD(${REQUEST.depth},1)",
                                                "fieldType": null
                                            }
                                        },
                                        {
                                            "id": "1hoqv0ag141",
                                            "field": {
                                                "id": null,
                                                "fieldKey": "currentNode",
                                                "fieldAlias": "currentNode",
                                                "fieldName": "currentNode",
                                                "fieldType": "Model",
                                                "description": null,
                                                "required": null,
                                                "defaultValue": null,
                                                "relatedModel": {
                                                    "modelKey": "((key))",
                                                    "modelAlias": "((key))",
                                                    "modelName": null
                                                },
                                                "relation": null
                                            },
                                            "value": {
                                                "type": "VarValue",
                                                "id": null,
                                                "varValue": [
                                                    {
                                                        "valueKey": "GLOBAL",
                                                        "valueName": "全局变量",
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    },
                                                    {
                                                        "valueKey": "NODE_OUTPUT_node_1hooqs18d9",
                                                        "valueName": "[查询数据]节点出参",
                                                        "modelAlias": null,
                                                        "relatedModel": {
                                                            "modelKey": "((key))",
                                                            "modelAlias": "((key))",
                                                            "modelName": "((name))"
                                                        }
                                                    }
                                                ],
                                                "constValue": null,
                                                "valueType": "VAR",
                                                "fieldType": "Model"
                                            }
                                        },
                                        {
                                            "id": "1hoqv0ag142",
                                            "field": {
                                                "id": null,
                                                "fieldKey": "treeNodeMap",
                                                "fieldAlias": "treeNodeMap",
                                                "fieldName": "treeNodeMap",
                                                "fieldType": "Object",
                                                "description": null,
                                                "required": null,
                                                "defaultValue": null,
                                                "elements": null
                                            },
                                            "value": {
                                                "type": "VarValue",
                                                "id": null,
                                                "varValue": [
                                                    {
                                                        "valueKey": "GLOBAL",
                                                        "valueName": "全局变量",
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    },
                                                    {
                                                        "valueKey": "NODE_OUTPUT_node_1hooqt1qs10",
                                                        "valueName": "[在线JS脚本]节点出参",
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    },
                                                    {
                                                        "valueKey": "data",
                                                        "valueName": "data",
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    }
                                                ],
                                                "constValue": null,
                                                "valueType": "VAR",
                                                "fieldType": "Object"
                                            }
                                        }
                                    ],
                                    "output": [
                                        {
                                            "id": null,
                                            "fieldKey": "data",
                                            "fieldAlias": "data",
                                            "fieldName": "data",
                                            "fieldType": "Object",
                                            "description": null,
                                            "required": null,
                                            "defaultValue": null,
                                            "elements": null
                                        }
                                    ],
                                    "outputAssign": {
                                        "outputAssignType": "CUSTOM",
                                        "customAssignments": [
                                            {
                                                "id": "1hoqv1ne843",
                                                "field": {
                                                    "type": "VarValue",
                                                    "id": null,
                                                    "varValue": [
                                                        {
                                                            "valueKey": "OUTPUT",
                                                            "valueName": "服务出参",
                                                            "modelAlias": null,
                                                            "relatedModel": null
                                                        },
                                                        {
                                                            "valueKey": "data",
                                                            "valueName": "data",
                                                            "modelAlias": null,
                                                            "relatedModel": null
                                                        }
                                                    ],
                                                    "constValue": null,
                                                    "valueType": "VAR",
                                                    "fieldType": "Object"
                                                },
                                                "operator": "EQ",
                                                "value": {
                                                    "type": "VarValue",
                                                    "id": null,
                                                    "varValue": [
                                                        {
                                                            "valueKey": "NODE_OUTPUT_node_1hoquvo9a36",
                                                            "valueName": "出参结构体",
                                                            "modelAlias": null,
                                                            "relatedModel": null
                                                        },
                                                        {
                                                            "valueKey": "data",
                                                            "valueName": "data",
                                                            "modelAlias": null,
                                                            "relatedModel": null
                                                        }
                                                    ],
                                                    "constValue": null,
                                                    "valueType": "MODEL",
                                                    "fieldType": "Object"
                                                }
                                            }
                                        ]
                                    }
                                },
                                "headNodeKeys": null,
                                "children": null,
                                "renderType": null,
                                "preNodeKey": null,
                                "nextNodeKey": null
                            },
                            {
                                "type": "ConditionElseNode",
                                "id": null,
                                "key": "node_1hoormn8m16",
                                "name": "else",
                                "props": {
                                    "type": "ConditionElseProperties",
                                    "name": null,
                                    "desc": null
                                },
                                "headNodeKeys": null,
                                "children": null,
                                "renderType": null,
                                "preNodeKey": null,
                                "nextNodeKey": "node_1hoqt1lbv32"
                            },
                            {
                                "type": "AssignNode",
                                "id": null,
                                "key": "node_1hoqt1lbv32",
                                "name": "赋值",
                                "props": {
                                    "type": "AssignProperties",
                                    "name": null,
                                    "desc": null,
                                    "assignments": [
                                        {
                                            "id": "1hoqt1okg33",
                                            "field": {
                                                "type": "VarValue",
                                                "id": null,
                                                "varValue": [
                                                    {
                                                        "valueKey": "OUTPUT",
                                                        "valueName": "服务出参",
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    },
                                                    {
                                                        "valueKey": "data",
                                                        "valueName": "data",
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    }
                                                ],
                                                "constValue": null,
                                                "valueType": "VAR",
                                                "fieldType": "Object"
                                            },
                                            "operator": "EQ",
                                            "value": {
                                                "type": "VarValue",
                                                "id": null,
                                                "varValue": [
                                                    {
                                                        "valueKey": "GLOBAL",
                                                        "valueName": "全局变量",
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    },
                                                    {
                                                        "valueKey": "NODE_OUTPUT_node_1hooqt1qs10",
                                                        "valueName": "[在线JS脚本]节点出参",
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    },
                                                    {
                                                        "valueKey": "data",
                                                        "valueName": "data",
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    }
                                                ],
                                                "constValue": null,
                                                "valueType": "VAR",
                                                "fieldType": "Object"
                                            }
                                        }
                                    ]
                                },
                                "headNodeKeys": null,
                                "children": null,
                                "renderType": null,
                                "preNodeKey": null,
                                "nextNodeKey": null
                            }
                        ],
                        "renderType": null,
                        "preNodeKey": null,
                        "nextNodeKey": null
                    },
                    {
                        "type": "ConditionElseNode",
                        "id": null,
                        "key": "node_1hooqqol28",
                        "name": "else",
                        "props": {
                            "type": "ConditionElseProperties",
                            "name": null,
                            "desc": null
                        },
                        "headNodeKeys": null,
                        "children": null,
                        "renderType": null,
                        "preNodeKey": null,
                        "nextNodeKey": null
                    }
                ],
                "renderType": null,
                "preNodeKey": null,
                "nextNodeKey": "node_1hoopt7ks5"
            },
            {
                "type": "EndNode",
                "id": null,
                "key": "node_1hoopt7ks5",
                "name": "结束",
                "props": {
                    "type": "EndProperties",
                    "name": null,
                    "desc": null,
                    "outputMapping": null
                },
                "headNodeKeys": null,
                "children": null,
                "renderType": null,
                "preNodeKey": null,
                "nextNodeKey": null
            }
        ],
        "input": [
            {
                "id": null,
                "fieldKey": "depth",
                "fieldAlias": "depth",
                "fieldName": "depth",
                "fieldType": "Number",
                "description": null,
                "required": null,
                "defaultValue": null
            },
            {
                "id": null,
                "fieldKey": "currentNode",
                "fieldAlias": "currentNode",
                "fieldName": "currentNode",
                "fieldType": "Model",
                "description": null,
                "required": null,
                "defaultValue": null,
                "relatedModel": {
                    "modelKey": "((key))",
                    "modelAlias": "((key))",
                    "modelName": null
                },
                "relation": null
            },
            {
                "id": null,
                "fieldKey": "treeNodeMap",
                "fieldAlias": "treeNodeMap",
                "fieldName": "treeNodeMap",
                "fieldType": "Object",
                "description": null,
                "required": null,
                "defaultValue": null,
                "elements": null
            }
        ],
        "output": [
            {
                "id": null,
                "fieldKey": "data",
                "fieldAlias": "data",
                "fieldName": "data",
                "fieldType": "Object",
                "description": null,
                "required": null,
                "defaultValue": null,
                "elements": null
            }
        ]
    }
