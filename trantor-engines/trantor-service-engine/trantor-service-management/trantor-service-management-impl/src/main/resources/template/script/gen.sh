#!/bin/bash

# 初始化一个空的 JSON 对象
json="{}"

# 遍历 template 目录下的所有 .template 文件
for file in ../*.template; do
  # 获取文件名，不包括路径和扩展名
  filename=$(basename -- "$file" .template)

  # 使用 jq 来添加新的属性到 JSON 对象
  json=$(echo "$json" | jq --arg filename "$filename" '. + {($filename): {"templateCategoryKey": "", "templateCategoryName": "", "templateThumbnail": "", "templateDesc": ""}}')
done

# 将 JSON 对象写入到 output.json 文件
echo "$json" > TemplateBaseInfo.json
