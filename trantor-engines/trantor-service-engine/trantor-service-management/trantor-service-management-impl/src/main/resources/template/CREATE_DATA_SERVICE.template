{
    "type": "ServiceDefinition",
    "id": null,
    "key": "((modelKeyUpperCase))_CREATE_DATA_SERVICE",
    "name": "((name))-创建数据服务",
    "props": {
        "type": "ServiceProperties",
        "name": null,
        "desc": null,
        "transactionPropagation": "NOT_SUPPORTED",
        "aiService": null,
        "aiChatMode": null,
        "aiRoundsStrategy": null,
        "permissionKey": "((#ifCond functionPermissionKey "==" null))((modelKeyUpperCase)):CREATE_PERMISSION((else))((functionPermissionKey))((/ifCond))"
    },
    "headNodeKeys": [
        "node_1horq9bdf31"
    ],
    "children": [
        {
            "type": "StartNode",
            "id": null,
            "key": "node_1horq9bdf31",
            "name": "开始",
            "props": {
                "type": "StartProperties",
                "name": null,
                "desc": null,
                "input": [
                    {
                        "id": null,
                        "fieldKey": "request",
                        "fieldAlias": "request",
                        "fieldName": "requset",
                        "fieldType": "Model",
                        "description": null,
                        "required": null,
                        "defaultValue": null,
                        "relatedModel": {
                            "modelKey": "((key))",
                            "modelAlias": "((key))",
                            "modelName": null
                        },
                        "relation": null
                    }
                ],
                "output": [
                    {
                        "id": null,
                        "fieldKey": "data",
                        "fieldAlias": "data",
                        "fieldName": "data",
                        "fieldType": "Object",
                        "description": null,
                        "required": null,
                        "defaultValue": null,
                        "elements": null
                    }
                ],
                "globalVariable": null
            },
            "headNodeKeys": null,
            "children": null,
            "renderType": null,
            "preNodeKey": null,
            "nextNodeKey": "node_1horq9f5k33"
        },
        {
            "type": "CascadeCreateDataNode",
            "id": null,
            "key": "node_1horq9f5k33",
            "name": "新增数据",
            "props": {
                "type": "CascadeCreateDataProperties",
                "name": null,
                "desc": null,
                "relatedModel": {
                    "modelKey": "((key))",
                    "modelAlias": "((key))",
                    "modelName": "((name))"
                },
                "modelValue": {
                    "type": "VarValue",
                    "id": null,
                    "varValue": [
                        {
                            "valueKey": "REQUEST",
                            "valueName": "服务入参",
                            "modelAlias": null,
                            "relatedModel": null
                        },
                        {
                            "valueKey": "request",
                            "valueName": "requset",
                            "modelAlias": null,
                            "relatedModel": {
                                "modelKey": "((key))",
                                "modelAlias": "((key))",
                                "modelName": null
                            }
                        }
                    ],
                    "constValue": null,
                    "valueType": "VAR",
                    "fieldType": "Model"
                },
                "outputAssign": {
                    "outputAssignType": "CUSTOM",
                    "customAssignments": [
                        {
                            "id": "1horqac9g34",
                            "field": {
                                "type": "VarValue",
                                "id": null,
                                "varValue": [
                                    {
                                        "valueKey": "OUTPUT",
                                        "valueName": "服务出参",
                                        "modelAlias": null,
                                        "relatedModel": null
                                    },
                                    {
                                        "valueKey": "data",
                                        "valueName": "data",
                                        "modelAlias": null,
                                        "relatedModel": null
                                    }
                                ],
                                "constValue": null,
                                "valueType": "VAR",
                                "fieldType": "Object"
                            },
                            "operator": "EQ",
                            "value": {
                                "type": "VarValue",
                                "id": null,
                                "varValue": [
                                    {
                                        "valueKey": "NODE_OUTPUT_node_1horq9f5k33",
                                        "valueName": "出参结构体",
                                        "modelAlias": null,
                                        "relatedModel": {
                                            "modelKey": null,
                                            "modelAlias": null,
                                            "modelName": null
                                        }
                                    }
                                ],
                                "constValue": null,
                                "valueType": "MODEL",
                                "fieldType": "Model"
                            }
                        }
                    ]
                }
            },
            "headNodeKeys": null,
            "children": null,
            "renderType": null,
            "preNodeKey": null,
            "nextNodeKey": "node_1horq9bdf32"
        },
        {
            "type": "EndNode",
            "id": null,
            "key": "node_1horq9bdf32",
            "name": "结束",
            "props": {
                "type": "EndProperties",
                "name": null,
                "desc": null,
                "outputMapping": null
            },
            "headNodeKeys": null,
            "children": null,
            "renderType": null,
            "preNodeKey": null,
            "nextNodeKey": null
        }
    ],
    "input": [
        {
            "id": null,
            "fieldKey": "request",
            "fieldAlias": "request",
            "fieldName": "requset",
            "fieldType": "Model",
            "description": null,
            "required": null,
            "defaultValue": null,
            "relatedModel": {
                "modelKey": "((key))",
                "modelAlias": "((key))",
                "modelName": null
            },
            "relation": null
        }
    ],
    "output": [
        {
            "id": null,
            "fieldKey": "data",
            "fieldAlias": "data",
            "fieldName": "data",
            "fieldType": "Object",
            "description": null,
            "required": null,
            "defaultValue": null,
            "elements": null
        }
    ]
}
