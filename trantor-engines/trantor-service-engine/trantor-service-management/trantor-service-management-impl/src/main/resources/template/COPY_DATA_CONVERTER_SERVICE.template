{
    "type": "ServiceDefinition",
    "id": null,
    "key": "((modelKeyUpperCase))_COPY_DATA_CONVERTER_SERVICE((serviceUniqueKey))",
    "name": "((name))-复制数据转换服务((serviceUniqueKey))",
    "props": {
        "type": "ServiceProperties",
        "name": null,
        "desc": null,
        "transactionPropagation": "NOT_SUPPORTED",
        "aiService": false,
        "aiChatMode": null,
        "aiRoundsStrategy": null,
        "permissionKey": "((#ifCond functionPermissionKey "==" null))((modelKeyUpperCase)):CREATE_PERMISSION((else))((functionPermissionKey))((/ifCond))"
    },
    "headNodeKeys": [
        "node_1hodvr3rr21"
    ],
    "children": [
        {
            "type": "StartNode",
            "id": null,
            "key": "node_1hodvr3rr21",
            "name": "开始",
            "props": {
                "type": "StartProperties",
                "name": null,
                "desc": null,
                "input": [
                        {
                            "fieldKey": "request",
                            "fieldName": "request",
                            "fieldType": "Object",
                            "required": null,
                            "elements": [
                                {
                                    "fieldKey": "id",
                                    "fieldName": "id",
                                    "fieldType": "Number"
                                }
                            ]
                        }
                ],
                "output": [
                    {
                        "id": null,
                        "fieldKey": "data",
                        "fieldAlias": "data",
                        "fieldName": "data",
                        "fieldType": "Object",
                        "description": null,
                        "required": null,
                        "defaultValue": null,
                        "elements": null
                    }
                ],
                "globalVariable": null
            },
            "headNodeKeys": null,
            "children": null,
            "renderType": null,
            "preNodeKey": null,
            "nextNodeKey": "node_1hoeaee6n3"
        },
        {
            "type": "RetrieveDataNode",
            "id": null,
            "key": "node_1hoeaee6n3",
            "name": "查询数据",
            "props": {
                "type": "RetrieveDataProperties",
                "name": null,
                "desc": null,
                "relatedModel": {
                    "modelKey": "((key))",
                    "modelAlias": "((key))",
                    "modelName": "((name))"
                },
                "dataConditionPermissionKey": "((#ifCond dataConditionPermissionKey "==" null))null((else))((dataConditionPermissionKey))((/ifCond))",
                "dataType": "MODEL",
                "pageable": null,
                "conditionGroup": {
                    "type": "ConditionGroup",
                    "id": "oyl_zC9fUgIgdv34IfPGv",
                    "conditions": [
                        {
                            "type": "ConditionGroup",
                            "id": "U7CLSQW0myNcLM0yfZhLu",
                            "conditions": [
                                {
                                    "type": "ConditionLeaf",
                                    "id": "QlSGhw2ZYeDLBp-xcxcN1",
                                    "key": null,
                                    "leftValue": {
                                        "type": "VarValue",
                                        "id": null,
                                        "varValue": [
                                            {
                                                "valueKey": "id",
                                                "valueName": "ID",
                                                "modelAlias": "((key))",
                                                "relatedModel": null
                                            }
                                        ],
                                        "constValue": null,
                                        "valueType": "MODEL",
                                        "fieldType": "Number"
                                    },
                                    "operator": "EQ",
                                    "rightValue": {
                                        "type": "VarValue",
                                        "id": null,
                                        "varValue": [
                                                {
                                                    "valueKey": "REQUEST",
                                                    "valueName": "服务入参"
                                                },
                                                {
                                                    "valueKey": "request",
                                                    "valueName": "request"
                                                },
                                                {
                                                    "valueKey": "id",
                                                    "valueName": "id"
                                                }
                                        ],
                                        "constValue": null,
                                        "valueType": "VAR",
                                        "fieldType": "Number"
                                    },
                                    "rightValues": null
                                }
                            ],
                            "logicOperator": "AND"
                        }
                    ],
                    "logicOperator": "OR"
                },
                "sortOrder": null,
                "sortOrders": null,
                "stopWhenDataEmpty": false,
                "queryModelFields": ((queryModelFields)),
                "maximum": null,
                "outputAssign": {
                    "outputAssignType": "SYSTEM",
                    "customAssignments": null
                }
            },
            "headNodeKeys": null,
            "children": null,
            "renderType": null,
            "preNodeKey": null,
            "nextNodeKey": "node_1hom531cq86"
        },
        {
            "type": "CallServiceNode",
            "id": null,
            "key": "node_1hom531cq86",
            "name": "调用编排服务",
            "props": {
                "type": "CallServiceProperties",
                "name": null,
                "desc": null,
                "serviceKey": "((modelKeyUpperCase))_COPY_DATA_CONVERTER_SERVICE_CHILD((serviceUniqueKey))",
                "serviceName": "((name))-复制数据转换子服务((serviceUniqueKey))",
                "transactionPropagation": "NOT_SUPPORTED",
                "inputMapping": [
                    {
                        "id": "1hom5376487",
                        "field": {
                            "id": null,
                            "fieldKey": "request",
                            "fieldAlias": "request",
                            "fieldName": "request",
                            "fieldType": "Object",
                            "description": null,
                            "required": null,
                            "defaultValue": null,
                            "elements": [
                                {
                                    "id": null,
                                    "fieldKey": "idid",
                                    "fieldAlias": "idid",
                                    "fieldName": "id",
                                    "fieldType": "Text",
                                    "description": null,
                                    "required": null,
                                    "defaultValue": null
                                }
                            ]
                        },
                        "value": {
                            "type": "VarValue",
                            "id": null,
                            "varValue": [
                                {
                                    "valueKey": "GLOBAL",
                                    "valueName": "全局变量",
                                    "modelAlias": null,
                                    "relatedModel": null
                                },
                                {
                                    "valueKey": "NODE_OUTPUT_node_1hoeaee6n3",
                                    "valueName": "[查询数据]节点出参",
                                    "modelAlias": null,
                                    "relatedModel": {
                                        "modelKey": "((key))",
                                        "modelAlias": "((key))",
                                        "modelName": "((name))"
                                    }
                                }
                            ],
                            "constValue": null,
                            "valueType": "VAR",
                            "fieldType": "Object"
                        }
                    },
                    {
                        "id": "1hom5376488",
                        "field": {
                            "id": null,
                            "fieldKey": "modelKey",
                            "fieldAlias": "modelKey",
                            "fieldName": "modelKey",
                            "fieldType": "Text",
                            "description": null,
                            "required": null,
                            "defaultValue": null
                        },
                        "value": {
                            "type": "ConstValue",
                            "id": null,
                            "fieldType": "Text",
                            "constValue": "((key))"
                        }
                    }
                ],
                "output": [
                    {
                        "id": null,
                        "fieldKey": "data",
                        "fieldAlias": "data",
                        "fieldName": "data",
                        "fieldType": "Object",
                        "description": null,
                        "required": null,
                        "defaultValue": null,
                        "elements": null
                    }
                ],
                "outputAssign": {
                    "outputAssignType": "CUSTOM",
                    "customAssignments": [
                        {
                            "id": "1hom5p1tn94",
                            "field": {
                                "type": "VarValue",
                                "id": null,
                                "varValue": [
                                    {
                                        "valueKey": "OUTPUT",
                                        "valueName": "服务出参",
                                        "modelAlias": null,
                                        "relatedModel": null
                                    },
                                    {
                                        "valueKey": "data",
                                        "valueName": "data",
                                        "modelAlias": null,
                                        "relatedModel": null
                                    }
                                ],
                                "constValue": null,
                                "valueType": "VAR",
                                "fieldType": "Object"
                            },
                            "operator": "EQ",
                            "value": {
                                "type": "VarValue",
                                "id": null,
                                "varValue": [
                                    {
                                        "valueKey": "NODE_OUTPUT_node_1hom531cq86",
                                        "valueName": "出参结构体",
                                        "modelAlias": null,
                                        "relatedModel": null
                                    },
                                    {
                                        "valueKey": "data",
                                        "valueName": "data",
                                        "modelAlias": null,
                                        "relatedModel": null
                                    }
                                ],
                                "constValue": null,
                                "valueType": "MODEL",
                                "fieldType": "Object"
                            }
                        }
                    ]
                }
            },
            "headNodeKeys": null,
            "children": null,
            "renderType": null,
            "preNodeKey": null,
            "nextNodeKey": "node_1hodvr3rr22"
        },
        {
            "type": "EndNode",
            "id": null,
            "key": "node_1hodvr3rr22",
            "name": "结束",
            "props": {
                "type": "EndProperties",
                "name": null,
                "desc": null,
                "outputMapping": null
            },
            "headNodeKeys": null,
            "children": null,
            "renderType": null,
            "preNodeKey": null,
            "nextNodeKey": null
        }
    ],
    "input": [
        {
            "id": null,
            "fieldKey": "id",
            "fieldAlias": "id",
            "fieldName": "id",
            "fieldType": "Number",
            "description": null,
            "required": null,
            "defaultValue": null
        }
    ],
    "output": [
        {
            "id": null,
            "fieldKey": "data",
            "fieldAlias": "data",
            "fieldName": "data",
            "fieldType": "Object",
            "description": null,
            "required": null,
            "defaultValue": null,
            "elements": null
        }
    ]
}
