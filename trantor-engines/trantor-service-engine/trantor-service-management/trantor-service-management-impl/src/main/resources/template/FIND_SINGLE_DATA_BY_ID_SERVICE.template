{
    "type": "ServiceDefinition",
    "id": null,
    "key": "((modelKeyUpperCase))_FIND_SINGLE_DATA_BY_ID_SERVICE",
    "name": "((name))-根据ID查找单表数据服务",
    "props": {
        "type": "ServiceProperties",
        "name": null,
        "desc": null,
        "transactionPropagation": "NOT_SUPPORTED",
        "aiService": null,
        "aiChatMode": null,
        "aiRoundsStrategy": null,
        "permissionKey": "((#ifCond functionPermissionKey "==" null))((modelKeyUpperCase)):DATA_QUERY_PERMISSION((else))((functionPermissionKey))((/ifCond))"
    },
    "headNodeKeys": [
        "node_1horqbhng35"
    ],
    "children": [
        {
            "type": "StartNode",
            "id": null,
            "key": "node_1horqbhng35",
            "name": "开始",
            "props": {
                "type": "StartProperties",
                "name": null,
                "desc": null,
                "input": [
                    {
                        "id": null,
                        "fieldKey": "request",
                        "fieldAlias": "request",
                        "fieldName": "request",
                        "fieldType": "Model",
                        "description": null,
                        "required": null,
                        "defaultValue": null,
                        "relatedModel": {
                            "modelKey": "((key))",
                            "modelAlias": "((key))",
                            "modelName": null
                        },
                        "relation": null
                    }
                ],
                "output": [
                    {
                        "id": null,
                        "fieldKey": "data",
                        "fieldAlias": "data",
                        "fieldName": "data",
                        "fieldType": "Model",
                        "description": null,
                        "required": null,
                        "defaultValue": null,
                        "relatedModel": {
                            "modelKey": "((key))",
                            "modelAlias": "((key))",
                            "modelName": null
                        },
                        "relation": null
                    }
                ],
                "globalVariable": null
            },
            "headNodeKeys": null,
            "children": null,
            "renderType": null,
            "preNodeKey": null,
            "nextNodeKey": "node_1horqcdfb37"
        },
        {
            "type": "RetrieveDataNode",
            "id": null,
            "key": "node_1horqcdfb37",
            "name": "查询数据",
            "props": {
                "type": "RetrieveDataProperties",
                "name": null,
                "desc": null,
                "relatedModel": {
                    "modelKey": "((key))",
                    "modelAlias": "((key))",
                    "modelName": "((name))"
                },
                "dataConditionPermissionKey": "((#ifCond dataConditionPermissionKey "==" null))null((else))((dataConditionPermissionKey))((/ifCond))",
                "dataType": "MODEL",
                "pageable": null,
                "conditionGroup": {
                    "type": "ConditionGroup",
                    "id": "sej2nLHSeRiC3xMr97xmH",
                    "conditions": [
                        {
                            "type": "ConditionGroup",
                            "id": "-c_2AErlwlSmklLooq0C8",
                            "conditions": [
                                {
                                    "type": "ConditionLeaf",
                                    "id": "OfibYE84yNDLN_Sx8_jN5",
                                    "key": null,
                                    "leftValue": {
                                        "type": "VarValue",
                                        "id": null,
                                        "varValue": [
                                            {
                                                "valueKey": "id",
                                                "valueName": "ID",
                                                "modelAlias": "((key))",
                                                "relatedModel": null
                                            }
                                        ],
                                        "constValue": null,
                                        "valueType": "MODEL",
                                        "fieldType": "Number"
                                    },
                                    "operator": "EQ",
                                    "rightValue": {
                                        "type": "VarValue",
                                        "id": null,
                                        "varValue": [
                                            {
                                                "valueKey": "REQUEST",
                                                "valueName": "服务入参",
                                                "modelAlias": null,
                                                "relatedModel": null
                                            },
                                            {
                                                "valueKey": "request",
                                                "valueName": "request",
                                                "modelAlias": null,
                                                "relatedModel": {
                                                    "modelKey": "((key))",
                                                    "modelAlias": "((key))",
                                                    "modelName": null
                                                }
                                            },
                                            {
                                                "valueKey": "id",
                                                "valueName": "ID",
                                                "modelAlias": "((key))",
                                                "relatedModel": {
                                                    "modelKey": null,
                                                    "modelAlias": null,
                                                    "modelName": null
                                                }
                                            }
                                        ],
                                        "constValue": null,
                                        "valueType": "VAR",
                                        "fieldType": "Number"
                                    },
                                    "rightValues": null
                                }
                            ],
                            "logicOperator": "AND"
                        }
                    ],
                    "logicOperator": "OR"
                },
                "sortOrder": null,
                "sortOrders": null,
                "stopWhenDataEmpty": false,
                "maximum": null,
                "outputAssign": {
                    "outputAssignType": "CUSTOM",
                    "customAssignments": [
                        {
                            "id": "1horqdk7n38",
                            "field": {
                                "type": "VarValue",
                                "id": null,
                                "varValue": [
                                    {
                                        "valueKey": "OUTPUT",
                                        "valueName": "服务出参",
                                        "modelAlias": null,
                                        "relatedModel": null
                                    },
                                    {
                                        "valueKey": "data",
                                        "valueName": "data",
                                        "modelAlias": null,
                                        "relatedModel": {
                                            "modelKey": "((key))",
                                            "modelAlias": "((key))",
                                            "modelName": null
                                        }
                                    }
                                ],
                                "constValue": null,
                                "valueType": "VAR",
                                "fieldType": "Model"
                            },
                            "operator": "EQ",
                            "value": {
                                "type": "VarValue",
                                "id": null,
                                "varValue": [
                                    {
                                        "valueKey": "NODE_OUTPUT_node_1horqcdfb37",
                                        "valueName": "出参结构体",
                                        "modelAlias": null,
                                        "relatedModel": {
                                            "modelKey": "((key))",
                                            "modelAlias": "((key))",
                                            "modelName": "((name))"
                                        }
                                    }
                                ],
                                "constValue": null,
                                "valueType": "MODEL",
                                "fieldType": "Model"
                            }
                        }
                    ]
                }
            },
            "headNodeKeys": null,
            "children": null,
            "renderType": null,
            "preNodeKey": null,
            "nextNodeKey": "node_1horqbhng36"
        },
        {
            "type": "EndNode",
            "id": null,
            "key": "node_1horqbhng36",
            "name": "结束",
            "props": {
                "type": "EndProperties",
                "name": null,
                "desc": null,
                "outputMapping": null
            },
            "headNodeKeys": null,
            "children": null,
            "renderType": null,
            "preNodeKey": null,
            "nextNodeKey": null
        }
    ],
    "input": [
        {
            "id": null,
            "fieldKey": "request",
            "fieldAlias": "request",
            "fieldName": "request",
            "fieldType": "Model",
            "description": null,
            "required": null,
            "defaultValue": null,
            "relatedModel": {
                "modelKey": "((key))",
                "modelAlias": "((key))",
                "modelName": null
            },
            "relation": null
        }
    ],
    "output": [
        {
            "id": null,
            "fieldKey": "data",
            "fieldAlias": "data",
            "fieldName": "data",
            "fieldType": "Model",
            "description": null,
            "required": null,
            "defaultValue": null,
            "relatedModel": {
                "modelKey": "((key))",
                "modelAlias": "((key))",
                "modelName": null
            },
            "relation": null
        }
    ]
}
