{
    "type": "ServiceDefinition",
    "id": null,
    "key": "((modelKeyUpperCase))_MASTER_DATA_CREATE_AND_ENABLE_DATA_SERVICE",
    "name": "((name))-创建并启用主数据服务",
    "props": {
        "type": "ServiceProperties",
        "name": null,
        "desc": null,
        "transactionPropagation": "NOT_SUPPORTED",
        "aiService": null,
        "aiChatMode": null,
        "aiRoundsStrategy": null,
        "schedulerJob": null,
        "stateMachine": null,
        "permissionKey": "((#ifCond functionPermissionKey "==" null))((modelKeyUpperCase)):CREATE_PERMISSION((else))((functionPermissionKey))((/ifCond))"
    },
    "headNodeKeys": [
        "node_1hotgf60t26"
    ],
    "children": [
        {
            "key": "node_1hotgf60t26",
            "type": "StartNode",
            "name": "开始",
            "nextNodeKey": "node_1hothopqm34",
            "props": {
                "type": "StartProperties",
                "name": null,
                "desc": null,
                "input": [
                    {
                        "id": null,
                        "fieldKey": "request",
                        "fieldAlias": "request",
                        "fieldName": "request",
                        "fieldType": "Model",
                        "description": null,
                        "required": null,
                        "defaultValue": null,
                        "relatedModel": {
                            "modelKey": "((key))",
                            "modelAlias": "((key))",
                            "modelName": null
                        },
                        "relation": null
                    }
                ],
                "output": [
                    {
                        "id": null,
                        "fieldKey": "data",
                        "fieldAlias": "data",
                        "fieldName": "data",
                        "fieldType": "Object",
                        "description": null,
                        "required": null,
                        "defaultValue": null,
                        "elements": null
                    }
                ],
                "globalVariable": [
                    {
                        "fieldKey": "data",
                        "fieldName": "data",
                        "fieldType": "Model",
                        "relatedModel": {
                            "modelKey": "((key))"
                        }
                    }
                ],
                "nodeTitle": "开始",
                "nodeKey": "node_1hotgf60t26"
            }
        },
        {
            "key": "node_1hothopqm34",
            "type": "ExclusiveBranchNode",
            "name": "排他分支",
            "nextNodeKey": "node_1hotgf60t27",
            "props": {
                "type": "ExclusiveBranchProperties",
                "name": null,
                "desc": null,
                "nodeTitle": "排他分支"
            },
            "children": [
                {
                    "key": "node_1hothopqm35",
                    "type": "ConditionNode",
                    "name": "条件",
                    "nextNodeKey": "node_1hotghmj028",
                    "props": {
                        "type": "ConditionProperties",
                        "name": null,
                        "desc": null,
                        "conditionGroup": {
                            "type": "ConditionGroup",
                            "id": "35a4jFmrkfu9lw_hiJqEk",
                            "conditions": [
                                {
                                    "type": "ConditionGroup",
                                    "id": "vBmc9iFvsp0jO4p3VIVS4",
                                    "conditions": [
                                        {
                                            "type": "ConditionLeaf",
                                            "id": "vWDCoM_kGm7NPsBMbqHB-",
                                            "key": null,
                                            "leftValue": {
                                                "type": "VarValue",
                                                "id": null,
                                                "varValue": [
                                                    {
                                                        "valueKey": "REQUEST",
                                                        "valueName": "服务入参",
                                                        "fieldType": null,
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    },
                                                    {
                                                        "valueKey": "request",
                                                        "valueName": "request",
                                                        "fieldType": null,
                                                        "modelAlias": null,
                                                        "relatedModel": {
                                                            "modelKey": "((key))",
                                                            "modelAlias": "((key))",
                                                            "modelName": null
                                                        }
                                                    },
                                                    {
                                                        "valueKey": "id",
                                                        "valueName": "ID",
                                                        "fieldType": null,
                                                        "modelAlias": "((key))",
                                                        "relatedModel": {
                                                            "modelKey": null,
                                                            "modelAlias": null,
                                                            "modelName": null
                                                        }
                                                    }
                                                ],
                                                "fieldPaths": [
                                                    {
                                                        "fieldKey": "REQUEST",
                                                        "fieldName": "服务入参",
                                                        "fieldType": null,
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    },
                                                    {
                                                        "fieldKey": "request",
                                                        "fieldName": "request",
                                                        "fieldType": null,
                                                        "modelAlias": null,
                                                        "relatedModel": {
                                                            "modelKey": "((key))",
                                                            "modelAlias": "((key))",
                                                            "modelName": null
                                                        }
                                                    },
                                                    {
                                                        "fieldKey": "id",
                                                        "fieldName": "ID",
                                                        "fieldType": null,
                                                        "modelAlias": "((key))",
                                                        "relatedModel": {
                                                            "modelKey": null,
                                                            "modelAlias": null,
                                                            "modelName": null
                                                        }
                                                    }
                                                ],
                                                "constValue": null,
                                                "valueType": "VAR",
                                                "fieldType": "Number"
                                            },
                                            "operator": "IS_NULL",
                                            "rightValue": null,
                                            "rightValues": null
                                        }
                                    ],
                                    "logicOperator": "AND"
                                }
                            ],
                            "logicOperator": "OR"
                        },
                        "nodeTitle": "条件",
                        "nodeKey": "node_1hothopqm35"
                    }
                },
                {
                    "key": "node_1hotghmj028",
                    "type": "HttpServiceNode",
                    "name": "HTTP服务",
                    "nextNodeKey": "node_1hotgi5ic29",
                    "props": {
                        "type": "HttpServiceProperties",
                        "name": null,
                        "desc": null,
                        "serviceName": "查询模型元数据",
                        "url": "http://localhost:8080/api/trantor/struct-node/find-by-key/((key))",
                        "method": "GET",
                        "headers": [

                        ],
                        "pathVariables": [

                        ],
                        "inputMapping": null,
                        "bodyType": "VALUE",
                        "body": null,
                        "jsonBody": null,
                        "stream": false,
                        "output": [
                            {
                                "id": null,
                                "fieldKey": "data",
                                "fieldAlias": "data",
                                "fieldName": "data",
                                "fieldType": "Object",
                                "description": null,
                                "required": null,
                                "defaultValue": null,
                                "elements": [
                                    {
                                        "id": null,
                                        "fieldKey": "data",
                                        "fieldAlias": "data",
                                        "fieldName": "data",
                                        "fieldType": "Object",
                                        "description": null,
                                        "required": null,
                                        "defaultValue": null,
                                        "elements": [
                                            {
                                                "id": null,
                                                "fieldKey": "children",
                                                "fieldAlias": "children",
                                                "fieldName": "children",
                                                "fieldType": "Array",
                                                "description": null,
                                                "required": null,
                                                "defaultValue": null,
                                                "element": {
                                                    "id": null,
                                                    "fieldKey": "element",
                                                    "fieldAlias": "element",
                                                    "fieldName": "element",
                                                    "fieldType": "Object",
                                                    "description": null,
                                                    "required": null,
                                                    "defaultValue": null,
                                                    "elements": [
                                                        {
                                                            "id": null,
                                                            "fieldKey": "props",
                                                            "fieldAlias": "props",
                                                            "fieldName": "props",
                                                            "fieldType": "Object",
                                                            "description": null,
                                                            "required": null,
                                                            "defaultValue": null,
                                                            "elements": [
                                                                {
                                                                    "id": null,
                                                                    "fieldKey": "fieldType",
                                                                    "fieldAlias": "fieldType",
                                                                    "fieldName": "fieldType",
                                                                    "fieldType": "Text",
                                                                    "description": null,
                                                                    "required": null,
                                                                    "defaultValue": null
                                                                },
                                                                {
                                                                    "id": null,
                                                                    "fieldKey": "relation_meta",
                                                                    "fieldAlias": "relation_meta",
                                                                    "fieldName": "relationMeta",
                                                                    "fieldType": "Object",
                                                                    "description": null,
                                                                    "required": null,
                                                                    "defaultValue": null,
                                                                    "elements": [
                                                                        {
                                                                            "id": null,
                                                                            "fieldKey": "relationType",
                                                                            "fieldAlias": "relationType",
                                                                            "fieldName": "relationType",
                                                                            "fieldType": "Text",
                                                                            "description": null,
                                                                            "required": null,
                                                                            "defaultValue": null
                                                                        },
                                                                        {
                                                                            "id": null,
                                                                            "fieldKey": "sync",
                                                                            "fieldAlias": "sync",
                                                                            "fieldName": "sync",
                                                                            "fieldType": "Boolean",
                                                                            "description": null,
                                                                            "required": null,
                                                                            "defaultValue": null
                                                                        },
                                                                        {
                                                                            "id": null,
                                                                            "fieldKey": "relationModelAlias",
                                                                            "fieldAlias": "relationModelAlias",
                                                                            "fieldName": "relationModelAlias",
                                                                            "fieldType": "Text",
                                                                            "description": null,
                                                                            "required": null,
                                                                            "defaultValue": null
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            "id": null,
                                                            "fieldKey": "alias",
                                                            "fieldAlias": "alias",
                                                            "fieldName": "alias",
                                                            "fieldType": "Text",
                                                            "description": null,
                                                            "required": null,
                                                            "defaultValue": null
                                                        }
                                                    ]
                                                }
                                            }
                                        ]
                                    }
                                ]
                            }
                        ],
                        "outputAssign": {
                            "outputAssignType": "SYSTEM",
                            "customAssignments": null
                        },
                        "nodeTitle": "HTTP服务"
                    }
                },
                {
                    "key": "node_1hotgi5ic29",
                    "type": "ScriptNode",
                    "name": "在线JS脚本",
                    "nextNodeKey": "node_1hotgictr30",
                    "props": {
                        "type": "ScriptProperties",
                        "name": null,
                        "desc": null,
                        "language": "JS",
                        "script": "for each (var field in fields) {    if (field.alias.toUpperCase() === 'STATUS') {      return field;    }  }  return null;",
                        "inputMapping": [
                            {
                                "id": "1horf8iiq10",
                                "field": {
                                    "id": null,
                                    "fieldKey": "fields",
                                    "fieldAlias": "fields",
                                    "fieldName": "fields",
                                    "fieldType": "Array",
                                    "description": null,
                                    "required": null,
                                    "defaultValue": null,
                                    "element": null
                                },
                                "value": {
                                    "type": "VarValue",
                                    "id": null,
                                    "varValue": [
                                        {
                                            "valueKey": "GLOBAL",
                                            "valueName": "全局变量",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "valueKey": "NODE_OUTPUT_node_1hotghmj028",
                                            "valueName": "[HTTP服务]节点出参",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "valueKey": "data",
                                            "valueName": "data",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "valueKey": "data",
                                            "valueName": "data",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "valueKey": "children",
                                            "valueName": "children",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        }
                                    ],
                                    "fieldPaths": [
                                        {
                                            "fieldKey": "GLOBAL",
                                            "fieldName": "全局变量",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "fieldKey": "NODE_OUTPUT_node_1hotghmj028",
                                            "fieldName": "[HTTP服务]节点出参",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "fieldKey": "data",
                                            "fieldName": "data",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "fieldKey": "data",
                                            "fieldName": "data",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "fieldKey": "children",
                                            "fieldName": "children",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        }
                                    ],
                                    "constValue": null,
                                    "valueType": "VAR",
                                    "fieldType": "Array"
                                }
                            }
                        ],
                        "output": [
                            {
                                "id": null,
                                "fieldKey": "field",
                                "fieldAlias": "field",
                                "fieldName": "field",
                                "fieldType": "Object",
                                "description": null,
                                "required": null,
                                "defaultValue": null,
                                "elements": null
                            }
                        ],
                        "outputAssign": {
                            "outputAssignType": "SYSTEM",
                            "customAssignments": null
                        },
                        "nodeTitle": "在线JS脚本"
                    }
                },
                {
                    "key": "node_1hotgictr30",
                    "type": "ExclusiveBranchNode",
                    "name": "排他分支",
                    "nextNodeKey": "node_1hothse9c38",
                    "props": {
                        "type": "ExclusiveBranchProperties",
                        "name": null,
                        "desc": null,
                        "nodeTitle": "排他分支"
                    },
                    "children": [
                        {
                            "key": "node_1hotgictr31",
                            "type": "ConditionNode",
                            "name": "条件",
                            "nextNodeKey": "node_1hotgjt6533",
                            "props": {
                                "type": "ConditionProperties",
                                "name": null,
                                "desc": null,
                                "conditionGroup": {
                                    "type": "ConditionGroup",
                                    "id": "482wMTGyahxNKI0L46urI",
                                    "conditions": [
                                        {
                                            "type": "ConditionGroup",
                                            "id": "OufUx4XmMEieBBqNRHcy2",
                                            "conditions": [
                                                {
                                                    "type": "ConditionLeaf",
                                                    "id": "D6hL0rwMkoKw1rlEUCndI",
                                                    "key": null,
                                                    "leftValue": {
                                                        "type": "VarValue",
                                                        "id": null,
                                                        "varValue": [
                                                            {
                                                                "valueKey": "GLOBAL",
                                                                "valueName": "全局变量",
                                                                "fieldType": null,
                                                                "modelAlias": null,
                                                                "relatedModel": null
                                                            },
                                                            {
                                                                "valueKey": "NODE_OUTPUT_node_1hotgi5ic29",
                                                                "valueName": "[在线JS脚本]节点出参",
                                                                "fieldType": null,
                                                                "modelAlias": null,
                                                                "relatedModel": null
                                                            },
                                                            {
                                                                "valueKey": "field",
                                                                "valueName": "field",
                                                                "fieldType": null,
                                                                "modelAlias": null,
                                                                "relatedModel": null
                                                            }
                                                        ],
                                                        "fieldPaths": [
                                                            {
                                                                "fieldKey": "GLOBAL",
                                                                "fieldName": "全局变量",
                                                                "fieldType": null,
                                                                "modelAlias": null,
                                                                "relatedModel": null
                                                            },
                                                            {
                                                                "fieldKey": "NODE_OUTPUT_node_1hotgi5ic29",
                                                                "fieldName": "[在线JS脚本]节点出参",
                                                                "fieldType": null,
                                                                "modelAlias": null,
                                                                "relatedModel": null
                                                            },
                                                            {
                                                                "fieldKey": "field",
                                                                "fieldName": "field",
                                                                "fieldType": null,
                                                                "modelAlias": null,
                                                                "relatedModel": null
                                                            }
                                                        ],
                                                        "constValue": null,
                                                        "valueType": "VAR",
                                                        "fieldType": "Object"
                                                    },
                                                    "operator": "IS_NOT_NULL",
                                                    "rightValue": null,
                                                    "rightValues": null
                                                }
                                            ],
                                            "logicOperator": "AND"
                                        }
                                    ],
                                    "logicOperator": "OR"
                                },
                                "nodeTitle": "条件",
                                "nodeKey": "node_1hotgictr31"
                            }
                        },
                        {
                            "key": "node_1hotgjt6533",
                            "type": "ScriptNode",
                            "name": "调用在线JS脚本，根据字段类型判断并设置状态",
                            "props": {
                                "type": "ScriptProperties",
                                "name": null,
                                "desc": null,
                                "language": "JS",
                                "script": "if (field.props.fieldType === 'ENUM') {    if (field.props.dictPros !== null && field.props.dictPros.multiSelect === true) {      request.status = ['ENABLED'];    }else {      request.status = 'ENABLED';    }    }  return request;",
                                "inputMapping": [
                                    {
                                        "field": {
                                            "fieldKey": "field",
                                            "fieldType": "Object"
                                        },
                                        "value": {
                                            "type": "VarValue",
                                            "id": null,
                                            "varValue": [
                                                {
                                                    "valueKey": "GLOBAL",
                                                    "valueName": "全局变量",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                },
                                                {
                                                    "valueKey": "NODE_OUTPUT_node_1hotgi5ic29",
                                                    "valueName": "[在线JS脚本]节点出参",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                },
                                                {
                                                    "valueKey": "field",
                                                    "valueName": "field",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                }
                                            ],
                                            "fieldPaths": [
                                                {
                                                    "fieldKey": "GLOBAL",
                                                    "fieldName": "全局变量",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                },
                                                {
                                                    "fieldKey": "NODE_OUTPUT_node_1hotgi5ic29",
                                                    "fieldName": "[在线JS脚本]节点出参",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                },
                                                {
                                                    "fieldKey": "field",
                                                    "fieldName": "field",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                }
                                            ],
                                            "constValue": null,
                                            "valueType": "VAR",
                                            "fieldType": "Object"
                                        }
                                    },
                                    {
                                        "field": {
                                            "fieldKey": "request",
                                            "fieldType": "Object"
                                        },
                                        "value": {
                                            "type": "VarValue",
                                            "id": null,
                                            "varValue": [
                                                {
                                                    "valueKey": "REQUEST",
                                                    "valueName": "服务入参",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                },
                                                {
                                                    "valueKey": "request",
                                                    "valueName": "request",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": {
                                                        "modelKey": "((key))",
                                                        "modelAlias": "((key))",
                                                        "modelName": null
                                                    }
                                                }
                                            ],
                                            "fieldPaths": [
                                                {
                                                    "fieldKey": "REQUEST",
                                                    "fieldName": "服务入参",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                },
                                                {
                                                    "fieldKey": "request",
                                                    "fieldName": "request",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": {
                                                        "modelKey": "((key))",
                                                        "modelAlias": "((key))",
                                                        "modelName": null
                                                    }
                                                }
                                            ],
                                            "constValue": null,
                                            "valueType": "VAR",
                                            "fieldType": "Object"
                                        }
                                    }
                                ],
                                "output": [
                                    {
                                        "id": null,
                                        "fieldKey": "request",
                                        "fieldAlias": "request",
                                        "fieldName": "request",
                                        "fieldType": "Object",
                                        "description": null,
                                        "required": null,
                                        "defaultValue": null,
                                        "elements": null
                                    }
                                ],
                                "outputAssign": {
                                    "outputAssignType": "CUSTOM",
                                    "customAssignments": [
                                        {
                                            "id": "1hr5f7cch12",
                                            "field": {
                                                "type": "VarValue",
                                                "valueType": "VAR",
                                                "fieldType": "Object",
                                                "varValue": [
                                                    {
                                                        "valueKey": "GLOBAL",
                                                        "valueName": "全局变量"
                                                    },
                                                    {
                                                        "valueKey": "data",
                                                        "valueName": "data"
                                                    }
                                                ]
                                            },
                                            "value": {
                                                "type": "VarValue",
                                                "valueType": "MODEL",
                                                "fieldType": "Object",
                                                "varValue": [
                                                    {
                                                        "valueKey": "NODE_OUTPUT_node_1hotgjt6533",
                                                        "valueName": "出参结构体"
                                                    },
                                                    {
                                                        "valueKey": "request",
                                                        "valueName": "request"
                                                    }
                                                ]
                                            },
                                            "operator": "EQ"
                                        }
                                    ]
                                },
                                "nodeTitle": "调用在线JS脚本，根据字段类型判断并设置状态",
                                "nodeContent": "脚本节点",
                                "nodeKey": "node_1hotgjt6533"
                            }
                        },
                        {
                            "key": "node_1hotgictr32",
                            "type": "ConditionElseNode",
                            "name": "else",
                            "nextNodeKey": "node_1hr5f9ha313",
                            "props": {
                                "type": "ConditionElseProperties",
                                "name": null,
                                "desc": null,
                                "nodeTitle": "else"
                            }
                        },
                        {
                            "key": "node_1hr5f9ha313",
                            "type": "AssignNode",
                            "name": "赋值给data字段为服务入参request",
                            "props": {
                                "nodeTitle": "赋值给data字段为服务入参request",
                                "type": "AssignProperties",
                                "assignments": [
                                    {
                                        "id": "1hr5f9iv914",
                                        "field": {
                                            "type": "VarValue",
                                            "valueType": "VAR",
                                            "fieldType": "Model",
                                            "varValue": [
                                                {
                                                    "valueKey": "GLOBAL",
                                                    "valueName": "全局变量"
                                                },
                                                {
                                                    "valueKey": "data",
                                                    "valueName": "data",
                                                    "relatedModel": {
                                                        "modelKey": "((key))",
                                                        "modelAlias": "((key))",
                                                        "modelName": null
                                                    }
                                                }
                                            ]
                                        },
                                        "value": {
                                            "type": "VarValue",
                                            "valueType": "VAR",
                                            "fieldType": "Model",
                                            "varValue": [
                                                {
                                                    "valueKey": "REQUEST",
                                                    "valueName": "服务入参"
                                                },
                                                {
                                                    "valueKey": "request",
                                                    "valueName": "request",
                                                    "relatedModel": {
                                                        "modelKey": "((key))",
                                                        "modelAlias": "((key))",
                                                        "modelName": null
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                ],
                                "nodeKey": "node_1hr5f9ha313"
                            }
                        }
                    ],
                    "headNodeKeys": [
                        "node_1hotgictr31",
                        "node_1hotgictr32"
                    ]
                },
                {
                    "key": "node_1hothse9c38",
                    "type": "CascadeCreateDataNode",
                    "name": "新增数据",
                    "props": {
                        "type": "CascadeCreateDataProperties",
                        "name": null,
                        "desc": null,
                        "relatedModel": {
                            "modelKey": "((key))",
                            "modelAlias": "((key))",
                            "modelName": "((name))"
                        },
                        "modelValue": {
                            "type": "VarValue",
                            "valueType": "VAR",
                            "fieldType": "Model",
                            "varValue": [
                                {
                                    "valueKey": "GLOBAL",
                                    "valueName": "全局变量"
                                },
                                {
                                    "valueKey": "data",
                                    "valueName": "data",
                                    "relatedModel": {
                                        "modelKey": "((key))",
                                        "modelAlias": "((key))",
                                        "modelName": null
                                    }
                                }
                            ]
                        },
                        "outputAssign": {
                            "outputAssignType": "CUSTOM",
                            "customAssignments": [
                                {
                                    "id": "1hothsv7e39",
                                    "field": {
                                        "type": "VarValue",
                                        "id": null,
                                        "varValue": [
                                            {
                                                "valueKey": "OUTPUT",
                                                "valueName": "服务出参",
                                                "fieldType": null,
                                                "modelAlias": null,
                                                "relatedModel": null
                                            },
                                            {
                                                "valueKey": "data",
                                                "valueName": "data",
                                                "fieldType": null,
                                                "modelAlias": null,
                                                "relatedModel": null
                                            }
                                        ],
                                        "fieldPaths": [
                                            {
                                                "fieldKey": "OUTPUT",
                                                "fieldName": "服务出参",
                                                "fieldType": null,
                                                "modelAlias": null,
                                                "relatedModel": null
                                            },
                                            {
                                                "fieldKey": "data",
                                                "fieldName": "data",
                                                "fieldType": null,
                                                "modelAlias": null,
                                                "relatedModel": null
                                            }
                                        ],
                                        "constValue": null,
                                        "valueType": "VAR",
                                        "fieldType": "Object"
                                    },
                                    "operator": "EQ",
                                    "value": {
                                        "type": "VarValue",
                                        "id": null,
                                        "varValue": [
                                            {
                                                "valueKey": "NODE_OUTPUT_node_1hothse9c38",
                                                "valueName": "出参结构体",
                                                "fieldType": null,
                                                "modelAlias": null,
                                                "relatedModel": {
                                                    "modelKey": "((key))",
                                                    "modelAlias": "((key))",
                                                    "modelName": "((name))"
                                                }
                                            }
                                        ],
                                        "fieldPaths": [
                                            {
                                                "fieldKey": "NODE_OUTPUT_node_1hothse9c38",
                                                "fieldName": "出参结构体",
                                                "fieldType": null,
                                                "modelAlias": null,
                                                "relatedModel": {
                                                    "modelKey": "((key))",
                                                    "modelAlias": "((key))",
                                                    "modelName": "((name))"
                                                }
                                            }
                                        ],
                                        "constValue": null,
                                        "valueType": "MODEL",
                                        "fieldType": "Model"
                                    }
                                }
                            ]
                        },
                        "nodeTitle": "新增数据",
                        "nodeContent": "((key))",
                        "nodeKey": "node_1hothse9c38"
                    }
                },
                {
                    "key": "node_1hothopqm36",
                    "type": "ConditionElseNode",
                    "name": "else",
                    "nextNodeKey": "node_1hothpo0h37",
                    "props": {
                        "type": "ConditionElseProperties",
                        "name": null,
                        "desc": null,
                        "nodeTitle": "else"
                    }
                },
                {
                    "key": "node_1hothpo0h37",
                    "type": "CascadeUpdateDataNode",
                    "name": "更新数据",
                    "props": {
                        "type": "CascadeUpdateDataProperties",
                        "name": null,
                        "desc": null,
                        "relatedModel": {
                            "modelKey": "((key))",
                            "modelAlias": "((key))",
                            "modelName": "((name))"
                        },
                        "modelValue": {
                            "type": "VarValue",
                            "id": null,
                            "varValue": [
                                {
                                    "valueKey": "REQUEST",
                                    "valueName": "服务入参",
                                    "fieldType": null,
                                    "modelAlias": null,
                                    "relatedModel": null
                                },
                                {
                                    "valueKey": "request",
                                    "valueName": "request",
                                    "fieldType": null,
                                    "modelAlias": null,
                                    "relatedModel": {
                                        "modelKey": "((key))",
                                        "modelAlias": "((key))",
                                        "modelName": null
                                    }
                                }
                            ],
                            "fieldPaths": [
                                {
                                    "fieldKey": "REQUEST",
                                    "fieldName": "服务入参",
                                    "fieldType": null,
                                    "modelAlias": null,
                                    "relatedModel": null
                                },
                                {
                                    "fieldKey": "request",
                                    "fieldName": "request",
                                    "fieldType": null,
                                    "modelAlias": null,
                                    "relatedModel": {
                                        "modelKey": "((key))",
                                        "modelAlias": "((key))",
                                        "modelName": null
                                    }
                                }
                            ],
                            "constValue": null,
                            "valueType": "VAR",
                            "fieldType": "Model"
                        },
                        "outputAssign": null,
                        "nodeTitle": "更新数据",
                        "nodeContent": "((key))",
                        "nodeKey": "node_1hothpo0h37"
                    }
                }
            ],
            "headNodeKeys": [
                "node_1hothopqm35",
                "node_1hothopqm36"
            ]
        },
        {
            "key": "node_1hotgf60t27",
            "type": "EndNode",
            "name": "结束",
            "props": {
                "type": "EndProperties",
                "name": null,
                "desc": null,
                "nodeTitle": "结束"
            }
        }
    ],
    "input": [
        {
            "id": null,
            "fieldKey": "request",
            "fieldAlias": "request",
            "fieldName": "request",
            "fieldType": "Model",
            "description": null,
            "required": null,
            "defaultValue": null,
            "relatedModel": {
                "modelKey": "((key))",
                "modelAlias": "((key))",
                "modelName": null
            },
            "relation": null
        }
    ],
    "output": [
        {
            "id": null,
            "fieldKey": "data",
            "fieldAlias": "data",
            "fieldName": "data",
            "fieldType": "Object",
            "description": null,
            "required": null,
            "defaultValue": null,
            "elements": null
        }
    ]
}
