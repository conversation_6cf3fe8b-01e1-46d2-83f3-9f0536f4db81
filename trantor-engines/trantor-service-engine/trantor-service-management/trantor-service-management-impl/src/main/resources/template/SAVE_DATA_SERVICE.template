{
    "type": "ServiceDefinition",
    "id": null,
    "key": "((modelKeyUpperCase))_SAVE_DATA_SERVICE",
        "name": "((name))-保存数据服务",
        "props": {
            "type": "ServiceProperties",
            "name": null,
            "desc": null,
            "transactionPropagation": "NOT_SUPPORTED",
            "aiService": null,
            "aiChatMode": null,
            "aiRoundsStrategy": null,
            "permissionKey": "((#ifCond functionPermissionKey "==" null))((modelKeyUpperCase)):MODIFY_PERMISSION((else))((functionPermissionKey))((/ifCond))"
        },
        "headNodeKeys": [
            "node_1horqfchs41"
        ],
        "children": [
            {
                "type": "StartNode",
                "id": null,
                "key": "node_1horqfchs41",
                "name": "开始",
                "props": {
                    "type": "StartProperties",
                    "name": null,
                    "desc": null,
                    "input": [
                        {
                            "id": null,
                            "fieldKey": "request",
                            "fieldAlias": "request",
                            "fieldName": "request",
                            "fieldType": "Model",
                            "description": null,
                            "required": null,
                            "defaultValue": null,
                            "relatedModel": {
                                "modelKey": "((key))",
                                "modelAlias": "((key))",
                                "modelName": null
                            },
                            "relation": null
                        }
                    ],
                    "output": [
                        {
                            "id": null,
                            "fieldKey": "data",
                            "fieldAlias": "data",
                            "fieldName": "data",
                            "fieldType": "Model",
                            "description": null,
                            "required": null,
                            "defaultValue": null,
                            "relatedModel": {
                                "modelKey": "((key))",
                                "modelAlias": "((key))",
                                "modelName": null
                            },
                            "relation": null
                        }
                    ],
                    "globalVariable": null
                },
                "headNodeKeys": null,
                "children": null,
                "renderType": null,
                "preNodeKey": null,
                "nextNodeKey": "node_1horqghbi43"
            },
            {
                "type": "ExclusiveBranchNode",
                "id": null,
                "key": "node_1horqghbi43",
                "name": "排他分支",
                "props": {
                    "type": "ExclusiveBranchProperties",
                    "name": null,
                    "desc": null
                },
                "headNodeKeys": [
                    "node_1horqghbi44",
                    "node_1horqghbi45"
                ],
                "children": [
                    {
                        "type": "ConditionNode",
                        "id": null,
                        "key": "node_1horqghbi44",
                        "name": "ID 为空",
                        "props": {
                            "type": "ConditionProperties",
                            "name": null,
                            "desc": null,
                            "conditionGroup": {
                                "type": "ConditionGroup",
                                "id": "TTT-mVk80GfKuG9vnl-tY",
                                "conditions": [
                                    {
                                        "type": "ConditionGroup",
                                        "id": "cgYnbeRjo-BBgJgDQqNJx",
                                        "conditions": [
                                            {
                                                "type": "ConditionLeaf",
                                                "id": "naqZN04_HHqs7JiHCLrnd",
                                                "key": null,
                                                "leftValue": {
                                                    "type": "VarValue",
                                                    "id": null,
                                                    "varValue": [
                                                        {
                                                            "valueKey": "REQUEST",
                                                            "valueName": "服务入参",
                                                            "modelAlias": null,
                                                            "relatedModel": null
                                                        },
                                                        {
                                                            "valueKey": "request",
                                                            "valueName": "request",
                                                            "modelAlias": null,
                                                            "relatedModel": {
                                                                "modelKey": "((key))",
                                                                "modelAlias": "((key))",
                                                                "modelName": null
                                                            }
                                                        },
                                                        {
                                                            "valueKey": "id",
                                                            "valueName": "ID",
                                                            "modelAlias": "((key))",
                                                            "relatedModel": {
                                                                "modelKey": null,
                                                                "modelAlias": null,
                                                                "modelName": null
                                                            }
                                                        }
                                                    ],
                                                    "constValue": null,
                                                    "valueType": "VAR",
                                                    "fieldType": "Number"
                                                },
                                                "operator": "IS_NULL",
                                                "rightValue": null,
                                                "rightValues": null
                                            }
                                        ],
                                        "logicOperator": "AND"
                                    }
                                ],
                                "logicOperator": "OR"
                            }
                        },
                        "headNodeKeys": null,
                        "children": null,
                        "renderType": null,
                        "preNodeKey": null,
                        "nextNodeKey": "node_1horqhh7246"
                    },
                    {
                        "type": "CascadeCreateDataNode",
                        "id": null,
                        "key": "node_1horqhh7246",
                        "name": "新增数据",
                        "props": {
                            "type": "CascadeCreateDataProperties",
                            "name": null,
                            "desc": null,
                            "relatedModel": {
                                "modelKey": "((key))",
                                "modelAlias": "((key))",
                                "modelName": "((name))"
                            },
                            "modelValue": {
                                "type": "VarValue",
                                "id": null,
                                "varValue": [
                                    {
                                        "valueKey": "REQUEST",
                                        "valueName": "服务入参",
                                        "modelAlias": null,
                                        "relatedModel": null
                                    },
                                    {
                                        "valueKey": "request",
                                        "valueName": "request",
                                        "modelAlias": null,
                                        "relatedModel": {
                                            "modelKey": "((key))",
                                            "modelAlias": "((key))",
                                            "modelName": null
                                        }
                                    }
                                ],
                                "constValue": null,
                                "valueType": "VAR",
                                "fieldType": "Model"
                            },
                            "outputAssign": {
                                "outputAssignType": "CUSTOM",
                                "customAssignments": [
                                    {
                                        "id": "1horqjd0h48",
                                        "field": {
                                            "type": "VarValue",
                                            "id": null,
                                            "varValue": [
                                                {
                                                    "valueKey": "OUTPUT",
                                                    "valueName": "服务出参",
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                },
                                                {
                                                    "valueKey": "data",
                                                    "valueName": "data",
                                                    "modelAlias": null,
                                                    "relatedModel": {
                                                        "modelKey": "((key))",
                                                        "modelAlias": "((key))",
                                                        "modelName": null
                                                    }
                                                }
                                            ],
                                            "constValue": null,
                                            "valueType": "VAR",
                                            "fieldType": "Model"
                                        },
                                        "operator": "EQ",
                                        "value": {
                                            "type": "VarValue",
                                            "id": null,
                                            "varValue": [
                                                {
                                                    "valueKey": "NODE_OUTPUT_node_1horqhh7246",
                                                    "valueName": "出参结构体",
                                                    "modelAlias": null,
                                                    "relatedModel": {
                                                        "modelKey": null,
                                                        "modelAlias": null,
                                                        "modelName": null
                                                    }
                                                }
                                            ],
                                            "constValue": null,
                                            "valueType": "MODEL",
                                            "fieldType": "Model"
                                        }
                                    }
                                ]
                            }
                        },
                        "headNodeKeys": null,
                        "children": null,
                        "renderType": null,
                        "preNodeKey": null,
                        "nextNodeKey": null
                    },
                    {
                        "type": "ConditionElseNode",
                        "id": null,
                        "key": "node_1horqghbi45",
                        "name": "else",
                        "props": {
                            "type": "ConditionElseProperties",
                            "name": null,
                            "desc": null
                        },
                        "headNodeKeys": null,
                        "children": null,
                        "renderType": null,
                        "preNodeKey": null,
                        "nextNodeKey": "node_1horqhvvh47"
                    },
                    {
                        "type": "CascadeUpdateDataNode",
                        "id": null,
                        "key": "node_1horqhvvh47",
                        "name": "更新数据",
                        "props": {
                            "type": "CascadeUpdateDataProperties",
                            "name": null,
                            "desc": null,
                            "relatedModel": {
                                "modelKey": "((key))",
                                "modelAlias": "((key))",
                                "modelName": "((name))"
                            },
                            "dataConditionPermissionKey": "((#ifCond dataConditionPermissionKey "==" null))null((else))((dataConditionPermissionKey))((/ifCond))",
                            "modelValue": {
                                "type": "VarValue",
                                "id": null,
                                "varValue": [
                                    {
                                        "valueKey": "REQUEST",
                                        "valueName": "服务入参",
                                        "modelAlias": null,
                                        "relatedModel": null
                                    },
                                    {
                                        "valueKey": "request",
                                        "valueName": "request",
                                        "modelAlias": null,
                                        "relatedModel": {
                                            "modelKey": "((key))",
                                            "modelAlias": "((key))",
                                            "modelName": null
                                        }
                                    }
                                ],
                                "constValue": null,
                                "valueType": "VAR",
                                "fieldType": "Model"
                            },
                            "outputAssign": null
                        },
                        "headNodeKeys": null,
                        "children": null,
                        "renderType": null,
                        "preNodeKey": null,
                        "nextNodeKey": "node_1horqjtfb49"
                    },
                    {
                        "type": "AssignNode",
                        "id": null,
                        "key": "node_1horqjtfb49",
                        "name": "赋值",
                        "props": {
                            "type": "AssignProperties",
                            "name": null,
                            "desc": null,
                            "assignments": [
                                {
                                    "id": "1horqjuvl50",
                                    "field": {
                                        "type": "VarValue",
                                        "id": null,
                                        "varValue": [
                                            {
                                                "valueKey": "OUTPUT",
                                                "valueName": "服务出参",
                                                "modelAlias": null,
                                                "relatedModel": null
                                            },
                                            {
                                                "valueKey": "data",
                                                "valueName": "data",
                                                "modelAlias": null,
                                                "relatedModel": {
                                                    "modelKey": "((key))",
                                                    "modelAlias": "((key))",
                                                    "modelName": null
                                                }
                                            }
                                        ],
                                        "constValue": null,
                                        "valueType": "VAR",
                                        "fieldType": "Model"
                                    },
                                    "operator": "EQ",
                                    "value": {
                                        "type": "VarValue",
                                        "id": null,
                                        "varValue": [
                                            {
                                                "valueKey": "REQUEST",
                                                "valueName": "服务入参",
                                                "modelAlias": null,
                                                "relatedModel": null
                                            },
                                            {
                                                "valueKey": "request",
                                                "valueName": "request",
                                                "modelAlias": null,
                                                "relatedModel": {
                                                    "modelKey": "((key))",
                                                    "modelAlias": "((key))",
                                                    "modelName": null
                                                }
                                            }
                                        ],
                                        "constValue": null,
                                        "valueType": "VAR",
                                        "fieldType": "Model"
                                    }
                                }
                            ]
                        },
                        "headNodeKeys": null,
                        "children": null,
                        "renderType": null,
                        "preNodeKey": null,
                        "nextNodeKey": null
                    }
                ],
                "renderType": null,
                "preNodeKey": null,
                "nextNodeKey": "node_1horqfchs42"
            },
            {
                "type": "EndNode",
                "id": null,
                "key": "node_1horqfchs42",
                "name": "结束",
                "props": {
                    "type": "EndProperties",
                    "name": null,
                    "desc": null,
                    "outputMapping": null
                },
                "headNodeKeys": null,
                "children": null,
                "renderType": null,
                "preNodeKey": null,
                "nextNodeKey": null
            }
        ],
        "input": [
            {
                "id": null,
                "fieldKey": "request",
                "fieldAlias": "request",
                "fieldName": "request",
                "fieldType": "Model",
                "description": null,
                "required": null,
                "defaultValue": null,
                "relatedModel": {
                    "modelKey": "((key))",
                    "modelAlias": "((key))",
                    "modelName": null
                },
                "relation": null
            }
        ],
        "output": [
            {
                "id": null,
                "fieldKey": "data",
                "fieldAlias": "data",
                "fieldName": "data",
                "fieldType": "Model",
                "description": null,
                "required": null,
                "defaultValue": null,
                "relatedModel": {
                    "modelKey": "((key))",
                    "modelAlias": "((key))",
                    "modelName": null
                },
                "relation": null
            }
        ]
    }
