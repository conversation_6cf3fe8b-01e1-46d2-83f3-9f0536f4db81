{
    "type": "ServiceDefinition",
    "id": null,
    "key": "((modelKeyUpperCase))_PAGING_DATA_SERVICE((serviceUniqueKey))",
        "name": "((name))-分页数据服务((serviceUniqueKey))",
        "props": {
            "type": "ServiceProperties",
            "name": null,
            "desc": null,
            "transactionPropagation": "NOT_SUPPORTED",
            "aiService": null,
            "aiChatMode": null,
            "aiRoundsStrategy": null,
            "permissionKey": "((#ifCond functionPermissionKey "==" null))((modelKeyUpperCase)):DATA_QUERY_PERMISSION((else))((functionPermissionKey))((/ifCond))"
        },
        "headNodeKeys": [
            "node_1h9a9hm2n23"
        ],
        "children": [
            {
                "type": "StartNode",
                "id": null,
                "key": "node_1h9a9hm2n23",
                "name": "开始",
                "props": {
                    "type": "StartProperties",
                    "name": null,
                    "desc": null,
                    "input": [
                        {
                            "id": null,
                            "fieldKey": "request",
                            "fieldAlias": "request",
                            "fieldName": "request",
                            "fieldType": "Object",
                            "description": null,
                            "required": null,
                            "defaultValue": null,
                            "elements": [
                                {
                                    "id": null,
                                    "fieldKey": "pageable",
                                    "fieldAlias": "pageable",
                                    "fieldName": "pageable",
                                    "fieldType": "Pageable",
                                    "description": null,
                                    "required": null,
                                    "defaultValue": null,
                                    "elements": [
                                        {
                                            "id": null,
                                            "fieldKey": "conditionGroup",
                                            "fieldAlias": "conditionGroup",
                                            "fieldName": "条件组",
                                            "fieldType": "Object",
                                            "description": null,
                                            "required": null,
                                            "defaultValue": null,
                                            "elements": null
                                        },
                                        {
                                            "id": null,
                                            "fieldKey": "conditionItems",
                                            "fieldAlias": "conditionItems",
                                            "fieldName": "简化版条件组",
                                            "fieldType": "Object",
                                            "description": null,
                                            "required": null,
                                            "defaultValue": null,
                                            "elements": null
                                        },
                                        {
                                            "id": null,
                                            "fieldKey": "sortOrders",
                                            "fieldAlias": "sortOrders",
                                            "fieldName": "字段排序",
                                            "fieldType": "Array",
                                            "description": null,
                                            "required": null,
                                            "defaultValue": null,
                                            "element": null
                                        },
                                        {
                                            "id": null,
                                            "fieldKey": "pageNo",
                                            "fieldAlias": "pageNo",
                                            "fieldName": "页码",
                                            "fieldType": "Number",
                                            "description": null,
                                            "required": null,
                                            "defaultValue": null
                                        },
                                        {
                                            "id": null,
                                            "fieldKey": "pageSize",
                                            "fieldAlias": "pageSize",
                                            "fieldName": "每页数量",
                                            "fieldType": "Number",
                                            "description": null,
                                            "required": null,
                                            "defaultValue": null
                                        }
                                    ]
                                }
                            ]
                        }
                    ],
                    "output": [
                        {
                            "id": null,
                            "fieldKey": "data",
                            "fieldAlias": "data",
                            "fieldName": "data",
                            "fieldType": "Paging",
                            "description": null,
                            "required": null,
                            "defaultValue": null,
                            "elements": [
                                {
                                    "id": null,
                                    "fieldKey": "total",
                                    "fieldAlias": "total",
                                    "fieldName": "total",
                                    "fieldType": "Number",
                                    "description": null,
                                    "required": null,
                                    "defaultValue": null
                                },
                                {
                                    "id": null,
                                    "fieldKey": "data",
                                    "fieldAlias": "data",
                                    "fieldName": "data",
                                    "fieldType": "Array",
                                    "description": null,
                                    "required": null,
                                    "defaultValue": null,
                                    "element": {
                                        "id": null,
                                        "fieldKey": "element",
                                        "fieldAlias": "element",
                                        "fieldName": "element",
                                        "fieldType": "Model",
                                        "description": null,
                                        "required": null,
                                        "defaultValue": null,
                                        "relatedModel": {
                                            "modelKey": "((key))",
                                            "modelAlias": "((key))",
                                            "modelName": "((name))"
                                        },
                                        "relation": null
                                    }
                                }
                            ]
                        }
                    ],
                    "globalVariable": null
                },
                "headNodeKeys": null,
                "children": null,
                "renderType": null,
                "preNodeKey": null,
                "nextNodeKey": "node_1h9aa6nhd27"
            },
            {
                "type": "RetrieveDataNode",
                "id": null,
                "key": "node_1h9aa6nhd27",
                "name": "查询数据",
                "props": {
                    "type": "RetrieveDataProperties",
                    "name": null,
                    "desc": null,
                    "relatedModel": {
                        "modelKey": "((key))",
                        "modelAlias": "((key))",
                        "modelName": "((name))"
                    },
                    "dataConditionPermissionKey": "((#ifCond dataConditionPermissionKey "==" null))null((else))((dataConditionPermissionKey))((/ifCond))",
                    "dataType": "PAGING",
                    "pageable": {
                        "type": "VarValue",
                        "id": null,
                        "varValue": [
                            {
                                "valueKey": "REQUEST",
                                "valueName": "服务入参",
                                "modelAlias": null,
                                "relatedModel": null
                            },
                            {
                                "valueKey": "request",
                                "valueName": "request",
                                "modelAlias": null,
                                "relatedModel": null
                            },
                            {
                                "valueKey": "pageable",
                                "valueName": "pageable",
                                "modelAlias": null,
                                "relatedModel": null
                            }
                        ],
                        "constValue": null,
                        "valueType": "VAR",
                        "fieldType": "Pageable"
                    },
                    "conditionGroup": ((conditionGroup)),
                    "sortOrder": null,
                    "sortOrders": [{"fieldAlias": "id", "sortType": "DESC"}],
                    "stopWhenDataEmpty": false,
                    "queryModelFields": ((queryModelFields)),
                    "maximum": null,
                    "outputAssign": {
                        "outputAssignType": "CUSTOM",
                        "customAssignments": [
                            {
                                "id": "1h9pl7l121",
                                "field": {
                                    "type": "VarValue",
                                    "id": null,
                                    "varValue": [
                                        {
                                            "valueKey": "OUTPUT",
                                            "valueName": "服务出参",
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "valueKey": "data",
                                            "valueName": "data",
                                            "modelAlias": null,
                                            "relatedModel": null
                                        }
                                    ],
                                    "constValue": null,
                                    "valueType": "VAR",
                                    "fieldType": "Paging"
                                },
                                "operator": "EQ",
                                "value": {
                                    "type": "VarValue",
                                    "id": null,
                                    "varValue": [
                                        {
                                            "valueKey": "NODE_OUTPUT_node_1h9aa6nhd27",
                                            "valueName": "出参结构体",
                                            "modelAlias": null,
                                            "relatedModel": null
                                        }
                                    ],
                                    "constValue": null,
                                    "valueType": "MODEL",
                                    "fieldType": "Paging"
                                }
                            }
                        ]
                    }
                },
                "headNodeKeys": null,
                "children": null,
                "renderType": null,
                "preNodeKey": null,
                "nextNodeKey": "node_1h9a9hm2n24"
            },
            {
                "type": "EndNode",
                "id": null,
                "key": "node_1h9a9hm2n24",
                "name": "结束",
                "props": {
                    "type": "EndProperties",
                    "name": null,
                    "desc": null,
                    "outputMapping": null
                },
                "headNodeKeys": null,
                "children": null,
                "renderType": null,
                "preNodeKey": null,
                "nextNodeKey": null
            }
        ],
        "input": [
            {
                "id": null,
                "fieldKey": "request",
                "fieldAlias": "request",
                "fieldName": "request",
                "fieldType": "Object",
                "description": null,
                "required": null,
                "defaultValue": null,
                "elements": [
                    {
                        "id": null,
                        "fieldKey": "pageable",
                        "fieldAlias": "pageable",
                        "fieldName": "pageable",
                        "fieldType": "Pageable",
                        "description": null,
                        "required": null,
                        "defaultValue": null,
                        "elements": [
                            {
                                "id": null,
                                "fieldKey": "conditionGroup",
                                "fieldAlias": "conditionGroup",
                                "fieldName": "条件组",
                                "fieldType": "Object",
                                "description": null,
                                "required": null,
                                "defaultValue": null,
                                "elements": null
                            },
                            {
                                "id": null,
                                "fieldKey": "conditionItems",
                                "fieldAlias": "conditionItems",
                                "fieldName": "简化版条件组",
                                "fieldType": "Object",
                                "description": null,
                                "required": null,
                                "defaultValue": null,
                                "elements": null
                            },
                            {
                                "id": null,
                                "fieldKey": "sortOrders",
                                "fieldAlias": "sortOrders",
                                "fieldName": "字段排序",
                                "fieldType": "Array",
                                "description": null,
                                "required": null,
                                "defaultValue": null,
                                "element": null
                            },
                            {
                                "id": null,
                                "fieldKey": "pageNo",
                                "fieldAlias": "pageNo",
                                "fieldName": "页码",
                                "fieldType": "Number",
                                "description": null,
                                "required": null,
                                "defaultValue": null
                            },
                            {
                                "id": null,
                                "fieldKey": "pageSize",
                                "fieldAlias": "pageSize",
                                "fieldName": "每页数量",
                                "fieldType": "Number",
                                "description": null,
                                "required": null,
                                "defaultValue": null
                            }
                        ]
                    }
                ]
            }
        ],
        "output": [
            {
                "id": null,
                "fieldKey": "data",
                "fieldAlias": "data",
                "fieldName": "data",
                "fieldType": "Paging",
                "description": null,
                "required": null,
                "defaultValue": null,
                "elements": [
                    {
                        "id": null,
                        "fieldKey": "total",
                        "fieldAlias": "total",
                        "fieldName": "total",
                        "fieldType": "Number",
                        "description": null,
                        "required": null,
                        "defaultValue": null
                    },
                    {
                        "id": null,
                        "fieldKey": "data",
                        "fieldAlias": "data",
                        "fieldName": "data",
                        "fieldType": "Array",
                        "description": null,
                        "required": null,
                        "defaultValue": null,
                        "element": {
                            "id": null,
                            "fieldKey": "element",
                            "fieldAlias": "element",
                            "fieldName": "element",
                            "fieldType": "Model",
                            "description": null,
                            "required": null,
                            "defaultValue": null,
                            "relatedModel": {
                                "modelKey": "((key))",
                                "modelAlias": "((key))",
                                "modelName": "((name))"
                            },
                            "relation": null
                        }
                    }
                ]
            }
        ]
    }
