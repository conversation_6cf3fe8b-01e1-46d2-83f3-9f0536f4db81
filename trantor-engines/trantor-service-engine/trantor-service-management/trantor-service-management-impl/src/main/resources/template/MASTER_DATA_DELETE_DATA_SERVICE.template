{
    "type": "ServiceDefinition",
    "id": null,
    "key": "((modelKeyUpperCase))_MASTER_DATA_DELETE_DATA_SERVICE",
    "name": "((name))-删除主数据服务",
    "props": {
        "type": "ServiceProperties",
        "name": null,
        "desc": null,
        "transactionPropagation": "NOT_SUPPORTED",
        "aiService": null,
        "aiChatMode": null,
        "aiRoundsStrategy": null,
        "schedulerJob": null,
        "stateMachine": null,
        "permissionKey": "((#ifCond functionPermissionKey "==" null))((modelKeyUpperCase)):DELETE_PERMISSION((else))((functionPermissionKey))((/ifCond))"
    },
    "headNodeKeys": [
        "node_1horimt9g32"
    ],
    "children": [
        {
            "key": "node_1horimt9g32",
            "type": "StartNode",
            "name": "开始",
            "nextNodeKey": "node_1horisapu44",
            "props": {
                "type": "StartProperties",
                "name": null,
                "desc": null,
                "input": [
                    {
                        "id": null,
                        "fieldKey": "request",
                        "fieldAlias": "request",
                        "fieldName": "request",
                        "fieldType": "Object",
                        "description": null,
                        "required": null,
                        "defaultValue": null,
                        "elements": [
                            {
                                "id": null,
                                "fieldKey": "id",
                                "fieldAlias": "id",
                                "fieldName": "id",
                                "fieldType": "Number",
                                "description": null,
                                "required": null,
                                "defaultValue": null
                            }
                        ]
                    }
                ],
                "output": null,
                "globalVariable": [
                    {
                        "id": null,
                        "fieldKey": "data",
                        "fieldAlias": "data",
                        "fieldName": "data",
                        "fieldType": "Model",
                        "description": null,
                        "required": null,
                        "defaultValue": null,
                        "relatedModel": {
                            "modelKey": "((key))",
                            "modelAlias": "((key))",
                            "modelName": null
                        },
                        "relation": null
                    }
                ],
                "nodeTitle": "开始"
            }
        },
        {
            "key": "node_1horisapu44",
            "type": "ExclusiveBranchNode",
            "name": "排他分支",
            "nextNodeKey": "node_1horimt9g33",
            "props": {
                "type": "ExclusiveBranchProperties",
                "name": null,
                "desc": null,
                "nodeTitle": "排他分支"
            },
            "children": [
                {
                    "key": "node_1horisapu45",
                    "type": "ConditionNode",
                    "name": "条件",
                    "nextNodeKey": "node_1horin00i34",
                    "props": {
                        "type": "ConditionProperties",
                        "name": null,
                        "desc": null,
                        "conditionGroup": {
                            "type": "ConditionGroup",
                            "id": "UoiWXh6J6G9rsti-SIe27",
                            "conditions": [
                                {
                                    "type": "ConditionGroup",
                                    "id": "td5qxr6yBsNIg935VTxpV",
                                    "conditions": [
                                        {
                                            "type": "ConditionLeaf",
                                            "id": "lOJypXBroFKpVZKvJI5uv",
                                            "key": null,
                                            "leftValue": {
                                                "type": "VarValue",
                                                "id": null,
                                                "varValue": [
                                                    {
                                                        "valueKey": "REQUEST",
                                                        "valueName": "服务入参",
                                                        "fieldType": null,
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    },
                                                    {
                                                        "valueKey": "request",
                                                        "valueName": "request",
                                                        "fieldType": null,
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    },
                                                    {
                                                        "valueKey": "id",
                                                        "valueName": "id",
                                                        "fieldType": null,
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    }
                                                ],
                                                "fieldPaths": [
                                                    {
                                                        "fieldKey": "REQUEST",
                                                        "fieldName": "服务入参",
                                                        "fieldType": null,
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    },
                                                    {
                                                        "fieldKey": "request",
                                                        "fieldName": "request",
                                                        "fieldType": null,
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    },
                                                    {
                                                        "fieldKey": "id",
                                                        "fieldName": "id",
                                                        "fieldType": null,
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    }
                                                ],
                                                "constValue": null,
                                                "valueType": "VAR",
                                                "fieldType": "Number"
                                            },
                                            "operator": "IS_NOT_NULL",
                                            "rightValue": null,
                                            "rightValues": null
                                        }
                                    ],
                                    "logicOperator": "AND"
                                }
                            ],
                            "logicOperator": "OR"
                        },
                        "nodeTitle": "条件"
                    }
                },
                {
                    "key": "node_1horin00i34",
                    "type": "HttpServiceNode",
                    "name": "HTTP服务",
                    "nextNodeKey": "node_1horipjal39",
                    "props": {
                        "type": "HttpServiceProperties",
                        "name": null,
                        "desc": null,
                        "serviceName": "查询模型元数据",
                        "url": "http://localhost:8080/api/trantor/struct-node/find-by-key/((key))",
                        "method": "GET",
                        "headers": [

                        ],
                        "pathVariables": [

                        ],
                        "inputMapping": null,
                        "bodyType": "VALUE",
                        "body": null,
                        "jsonBody": null,
                        "stream": false,
                        "output": [
                            {
                                "id": null,
                                "fieldKey": "data",
                                "fieldAlias": "data",
                                "fieldName": "data",
                                "fieldType": "Object",
                                "description": null,
                                "required": null,
                                "defaultValue": null,
                                "elements": [
                                    {
                                        "id": null,
                                        "fieldKey": "data",
                                        "fieldAlias": "data",
                                        "fieldName": "data",
                                        "fieldType": "Object",
                                        "description": null,
                                        "required": null,
                                        "defaultValue": null,
                                        "elements": [
                                            {
                                                "id": null,
                                                "fieldKey": "children",
                                                "fieldAlias": "children",
                                                "fieldName": "children",
                                                "fieldType": "Array",
                                                "description": null,
                                                "required": null,
                                                "defaultValue": null,
                                                "element": {
                                                    "id": null,
                                                    "fieldKey": "element",
                                                    "fieldAlias": "element",
                                                    "fieldName": "element",
                                                    "fieldType": "Object",
                                                    "description": null,
                                                    "required": null,
                                                    "defaultValue": null,
                                                    "elements": [
                                                        {
                                                            "id": null,
                                                            "fieldKey": "props",
                                                            "fieldAlias": "props",
                                                            "fieldName": "props",
                                                            "fieldType": "Object",
                                                            "description": null,
                                                            "required": null,
                                                            "defaultValue": null,
                                                            "elements": [
                                                                {
                                                                    "id": null,
                                                                    "fieldKey": "fieldType",
                                                                    "fieldAlias": "fieldType",
                                                                    "fieldName": "fieldType",
                                                                    "fieldType": "Text",
                                                                    "description": null,
                                                                    "required": null,
                                                                    "defaultValue": null
                                                                },
                                                                {
                                                                    "id": null,
                                                                    "fieldKey": "relation_meta",
                                                                    "fieldAlias": "relation_meta",
                                                                    "fieldName": "relationMeta",
                                                                    "fieldType": "Object",
                                                                    "description": null,
                                                                    "required": null,
                                                                    "defaultValue": null,
                                                                    "elements": [
                                                                        {
                                                                            "id": null,
                                                                            "fieldKey": "relationType",
                                                                            "fieldAlias": "relationType",
                                                                            "fieldName": "relationType",
                                                                            "fieldType": "Text",
                                                                            "description": null,
                                                                            "required": null,
                                                                            "defaultValue": null
                                                                        },
                                                                        {
                                                                            "id": null,
                                                                            "fieldKey": "sync",
                                                                            "fieldAlias": "sync",
                                                                            "fieldName": "sync",
                                                                            "fieldType": "Boolean",
                                                                            "description": null,
                                                                            "required": null,
                                                                            "defaultValue": null
                                                                        },
                                                                        {
                                                                            "id": null,
                                                                            "fieldKey": "relationModelAlias",
                                                                            "fieldAlias": "relationModelAlias",
                                                                            "fieldName": "relationModelAlias",
                                                                            "fieldType": "Text",
                                                                            "description": null,
                                                                            "required": null,
                                                                            "defaultValue": null
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            "id": null,
                                                            "fieldKey": "alias",
                                                            "fieldAlias": "alias",
                                                            "fieldName": "alias",
                                                            "fieldType": "Text",
                                                            "description": null,
                                                            "required": null,
                                                            "defaultValue": null
                                                        }
                                                    ]
                                                }
                                            }
                                        ]
                                    }
                                ]
                            }
                        ],
                        "outputAssign": {
                            "outputAssignType": "SYSTEM",
                            "customAssignments": null
                        },
                        "nodeTitle": "HTTP服务"
                    }
                },
                {
                    "key": "node_1horipjal39",
                    "type": "ScriptNode",
                    "name": "在线JS脚本",
                    "nextNodeKey": "node_1horipqb940",
                    "props": {
                        "type": "ScriptProperties",
                        "name": null,
                        "desc": null,
                        "language": "JS",
                        "script": "for each (var field in fields) {    if (field.alias.toUpperCase() === 'STATUS') {      return field;    }  }  return null;",
                        "inputMapping": [
                            {
                                "id": null,
                                "field": {
                                    "id": null,
                                    "fieldKey": "fields",
                                    "fieldAlias": "fields",
                                    "fieldName": null,
                                    "fieldType": "Array",
                                    "description": null,
                                    "required": null,
                                    "defaultValue": null,
                                    "element": null
                                },
                                "value": {
                                    "type": "VarValue",
                                    "id": null,
                                    "varValue": [
                                        {
                                            "valueKey": "GLOBAL",
                                            "valueName": "全局变量",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "valueKey": "NODE_OUTPUT_node_1horin00i34",
                                            "valueName": "[HTTP服务]节点出参",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "valueKey": "data",
                                            "valueName": "data",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "valueKey": "data",
                                            "valueName": "data",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "valueKey": "children",
                                            "valueName": "children",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        }
                                    ],
                                    "fieldPaths": [
                                        {
                                            "fieldKey": "GLOBAL",
                                            "fieldName": "全局变量",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "fieldKey": "NODE_OUTPUT_node_1horin00i34",
                                            "fieldName": "[HTTP服务]节点出参",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "fieldKey": "data",
                                            "fieldName": "data",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "fieldKey": "data",
                                            "fieldName": "data",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "fieldKey": "children",
                                            "fieldName": "children",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        }
                                    ],
                                    "constValue": null,
                                    "valueType": "VAR",
                                    "fieldType": "Array"
                                }
                            }
                        ],
                        "output": [
                            {
                                "id": null,
                                "fieldKey": "field",
                                "fieldAlias": "field",
                                "fieldName": "field",
                                "fieldType": "Object",
                                "description": null,
                                "required": null,
                                "defaultValue": null,
                                "elements": null
                            }
                        ],
                        "outputAssign": {
                            "outputAssignType": "SYSTEM",
                            "customAssignments": null
                        },
                        "nodeTitle": "在线JS脚本"
                    }
                },
                {
                    "key": "node_1horipqb940",
                    "type": "ExclusiveBranchNode",
                    "name": "排他分支",
                    "props": {
                        "type": "ExclusiveBranchProperties",
                        "name": null,
                        "desc": null,
                        "nodeTitle": "排他分支"
                    },
                    "children": [
                        {
                            "key": "node_1horipqb941",
                            "type": "ConditionNode",
                            "name": "条件",
                            "nextNodeKey": "node_1horiqbe643",
                            "props": {
                                "type": "ConditionProperties",
                                "name": null,
                                "desc": null,
                                "conditionGroup": {
                                    "type": "ConditionGroup",
                                    "id": "E2BgoDL-DUAuOKeoslz3U",
                                    "conditions": [
                                        {
                                            "type": "ConditionGroup",
                                            "id": "vnDomR82r9nyNL1zQvTtY",
                                            "conditions": [
                                                {
                                                    "type": "ConditionLeaf",
                                                    "id": "AAn41y6jqQvTlsK2RVpr4",
                                                    "key": null,
                                                    "leftValue": {
                                                        "type": "VarValue",
                                                        "id": null,
                                                        "varValue": [
                                                            {
                                                                "valueKey": "GLOBAL",
                                                                "valueName": "全局变量",
                                                                "fieldType": null,
                                                                "modelAlias": null,
                                                                "relatedModel": null
                                                            },
                                                            {
                                                                "valueKey": "NODE_OUTPUT_node_1horipjal39",
                                                                "valueName": "[在线JS脚本]节点出参",
                                                                "fieldType": null,
                                                                "modelAlias": null,
                                                                "relatedModel": null
                                                            },
                                                            {
                                                                "valueKey": "field",
                                                                "valueName": "field",
                                                                "fieldType": null,
                                                                "modelAlias": null,
                                                                "relatedModel": null
                                                            }
                                                        ],
                                                        "fieldPaths": [
                                                            {
                                                                "fieldKey": "GLOBAL",
                                                                "fieldName": "全局变量",
                                                                "fieldType": null,
                                                                "modelAlias": null,
                                                                "relatedModel": null
                                                            },
                                                            {
                                                                "fieldKey": "NODE_OUTPUT_node_1horipjal39",
                                                                "fieldName": "[在线JS脚本]节点出参",
                                                                "fieldType": null,
                                                                "modelAlias": null,
                                                                "relatedModel": null
                                                            },
                                                            {
                                                                "fieldKey": "field",
                                                                "fieldName": "field",
                                                                "fieldType": null,
                                                                "modelAlias": null,
                                                                "relatedModel": null
                                                            }
                                                        ],
                                                        "constValue": null,
                                                        "valueType": "VAR",
                                                        "fieldType": "Object"
                                                    },
                                                    "operator": "IS_NOT_NULL",
                                                    "rightValue": null,
                                                    "rightValues": null
                                                }
                                            ],
                                            "logicOperator": "AND"
                                        }
                                    ],
                                    "logicOperator": "OR"
                                },
                                "nodeTitle": "条件"
                            }
                        },
                        {
                            "key": "node_1horiqbe643",
                            "type": "ScriptNode",
                            "name": "在线JS脚本",
                            "nextNodeKey": "node_1horj5av951",
                            "props": {
                                "type": "ScriptProperties",
                                "name": null,
                                "desc": null,
                                "language": "JS",
                                "script": "if (field.props.fieldType === 'ENUM') {    if (field.props.dictPros !== null && field.props.dictPros.multiSelect === true) {      request.status = ['DELETED'];    }else {      request.status = 'DELETED';    }    }  request.id = request.id + '';  return request;",
                                "inputMapping": [
                                    {
                                        "field": {
                                            "fieldKey": "field",
                                            "fieldType": "Object"
                                        },
                                        "value": {
                                            "type": "VarValue",
                                            "id": null,
                                            "varValue": [
                                                {
                                                    "valueKey": "GLOBAL",
                                                    "valueName": "全局变量",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                },
                                                {
                                                    "valueKey": "NODE_OUTPUT_node_1horipjal39",
                                                    "valueName": "[在线JS脚本]节点出参",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                },
                                                {
                                                    "valueKey": "field",
                                                    "valueName": "field",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                }
                                            ],
                                            "fieldPaths": [
                                                {
                                                    "fieldKey": "GLOBAL",
                                                    "fieldName": "全局变量",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                },
                                                {
                                                    "fieldKey": "NODE_OUTPUT_node_1horipjal39",
                                                    "fieldName": "[在线JS脚本]节点出参",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                },
                                                {
                                                    "fieldKey": "field",
                                                    "fieldName": "field",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                }
                                            ],
                                            "constValue": null,
                                            "valueType": "VAR",
                                            "fieldType": "Object"
                                        }
                                    },
                                    {
                                        "field": {
                                            "fieldKey": "request",
                                            "fieldType": "Object"
                                        },
                                        "value": {
                                            "type": "VarValue",
                                            "id": null,
                                            "varValue": [
                                                {
                                                    "valueKey": "REQUEST",
                                                    "valueName": "服务入参",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                },
                                                {
                                                    "valueKey": "request",
                                                    "valueName": "request",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                }
                                            ],
                                            "fieldPaths": [
                                                {
                                                    "fieldKey": "REQUEST",
                                                    "fieldName": "服务入参",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                },
                                                {
                                                    "fieldKey": "request",
                                                    "fieldName": "request",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                }
                                            ],
                                            "constValue": null,
                                            "valueType": "VAR",
                                            "fieldType": "Object"
                                        }
                                    }
                                ],
                                "output": [
                                    {
                                        "fieldKey": "request",
                                        "fieldName": "request",
                                        "fieldType": "Model",
                                        "required": null,
                                        "relatedModel": {
                                            "modelKey": "((key))"
                                        }
                                    }
                                ],
                                "outputAssign": {
                                    "outputAssignType": "CUSTOM",
                                    "customAssignments": [
                                        {
                                            "id": "1horj3jjq50",
                                            "field": {
                                                "type": "VarValue",
                                                "id": null,
                                                "varValue": [
                                                    {
                                                        "valueKey": "GLOBAL",
                                                        "valueName": "全局变量",
                                                        "fieldType": null,
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    },
                                                    {
                                                        "valueKey": "data",
                                                        "valueName": "data",
                                                        "fieldType": null,
                                                        "modelAlias": null,
                                                        "relatedModel": {
                                                            "modelKey": "((key))",
                                                            "modelAlias": "((key))",
                                                            "modelName": null
                                                        }
                                                    }
                                                ],
                                                "fieldPaths": [
                                                    {
                                                        "fieldKey": "GLOBAL",
                                                        "fieldName": "全局变量",
                                                        "fieldType": null,
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    },
                                                    {
                                                        "fieldKey": "data",
                                                        "fieldName": "data",
                                                        "fieldType": null,
                                                        "modelAlias": null,
                                                        "relatedModel": {
                                                            "modelKey": "((key))",
                                                            "modelAlias": "((key))",
                                                            "modelName": null
                                                        }
                                                    }
                                                ],
                                                "constValue": null,
                                                "valueType": "VAR",
                                                "fieldType": "Model"
                                            },
                                            "operator": "EQ",
                                            "value": {
                                                "type": "VarValue",
                                                "id": null,
                                                "varValue": [
                                                    {
                                                        "valueKey": "NODE_OUTPUT_node_1horiqbe643",
                                                        "valueName": "出参结构体",
                                                        "fieldType": null,
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    },
                                                    {
                                                        "valueKey": "request",
                                                        "valueName": "request",
                                                        "fieldType": null,
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    }
                                                ],
                                                "fieldPaths": [
                                                    {
                                                        "fieldKey": "NODE_OUTPUT_node_1horiqbe643",
                                                        "fieldName": "出参结构体",
                                                        "fieldType": null,
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    },
                                                    {
                                                        "fieldKey": "request",
                                                        "fieldName": "request",
                                                        "fieldType": null,
                                                        "modelAlias": null,
                                                        "relatedModel": null
                                                    }
                                                ],
                                                "constValue": null,
                                                "valueType": "MODEL",
                                                "fieldType": "Object"
                                            }
                                        }
                                    ]
                                },
                                "nodeTitle": "在线JS脚本",
                                "nodeContent": "脚本节点",
                                "nodeKey": "node_1horiqbe643"
                            }
                        },
                        {
                            "key": "node_1horj5av951",
                            "type": "CascadeUpdateDataNode",
                            "name": "更新数据",
                            "props": {
                                "type": "CascadeUpdateDataProperties",
                                "name": null,
                                "desc": null,
                                "relatedModel": {
                                    "modelKey": "((key))",
                                    "modelAlias": "((key))",
                                    "modelName": "((name))"
                                },
                                "modelValue": {
                                    "type": "VarValue",
                                    "id": null,
                                    "varValue": [
                                        {
                                            "valueKey": "GLOBAL",
                                            "valueName": "全局变量",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "valueKey": "data",
                                            "valueName": "data",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": {
                                                "modelKey": "((key))",
                                                "modelAlias": "((key))",
                                                "modelName": null
                                            }
                                        }
                                    ],
                                    "fieldPaths": [
                                        {
                                            "fieldKey": "GLOBAL",
                                            "fieldName": "全局变量",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "fieldKey": "data",
                                            "fieldName": "data",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": {
                                                "modelKey": "((key))",
                                                "modelAlias": "((key))",
                                                "modelName": null
                                            }
                                        }
                                    ],
                                    "constValue": null,
                                    "valueType": "VAR",
                                    "fieldType": "Model"
                                },
                                "outputAssign": null,
                                "nodeTitle": "更新数据"
                            }
                        },
                        {
                            "key": "node_1horipqb942",
                            "type": "ConditionElseNode",
                            "name": "else",
                            "nextNodeKey": "node_1hotrmveq1",
                            "props": {
                                "type": "ConditionElseProperties",
                                "name": null,
                                "desc": null,
                                "nodeTitle": "else"
                            }
                        },
                        {
                            "key": "node_1hotrmveq1",
                            "type": "AssignNode",
                            "name": "赋值",
                            "nextNodeKey": "node_1horj1mpl49",
                            "props": {
                                "type": "AssignProperties",
                                "name": null,
                                "desc": null,
                                "assignments": [
                                    {
                                        "id": "1hotrn17j2",
                                        "field": {
                                            "type": "VarValue",
                                            "id": null,
                                            "varValue": [
                                                {
                                                    "valueKey": "GLOBAL",
                                                    "valueName": "全局变量",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                },
                                                {
                                                    "valueKey": "data",
                                                    "valueName": "data",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": {
                                                        "modelKey": "((key))",
                                                        "modelAlias": "((key))",
                                                        "modelName": null
                                                    }
                                                },
                                                {
                                                    "valueKey": "id",
                                                    "valueName": "ID",
                                                    "fieldType": null,
                                                    "modelAlias": "((key))",
                                                    "relatedModel": null
                                                }
                                            ],
                                            "fieldPaths": [
                                                {
                                                    "fieldKey": "GLOBAL",
                                                    "fieldName": "全局变量",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                },
                                                {
                                                    "fieldKey": "data",
                                                    "fieldName": "data",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": {
                                                        "modelKey": "((key))",
                                                        "modelAlias": "((key))",
                                                        "modelName": null
                                                    }
                                                },
                                                {
                                                    "fieldKey": "id",
                                                    "fieldName": "ID",
                                                    "fieldType": null,
                                                    "modelAlias": "((key))",
                                                    "relatedModel": null
                                                }
                                            ],
                                            "constValue": null,
                                            "valueType": "VAR",
                                            "fieldType": "Number"
                                        },
                                        "operator": "EQ",
                                        "value": {
                                            "type": "VarValue",
                                            "id": null,
                                            "varValue": [
                                                {
                                                    "valueKey": "REQUEST",
                                                    "valueName": "服务入参",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                },
                                                {
                                                    "valueKey": "request",
                                                    "valueName": "request",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                },
                                                {
                                                    "valueKey": "id",
                                                    "valueName": "id",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                }
                                            ],
                                            "fieldPaths": [
                                                {
                                                    "fieldKey": "REQUEST",
                                                    "fieldName": "服务入参",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                },
                                                {
                                                    "fieldKey": "request",
                                                    "fieldName": "request",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                },
                                                {
                                                    "fieldKey": "id",
                                                    "fieldName": "id",
                                                    "fieldType": null,
                                                    "modelAlias": null,
                                                    "relatedModel": null
                                                }
                                            ],
                                            "constValue": null,
                                            "valueType": "VAR",
                                            "fieldType": "Number"
                                        }
                                    }
                                ],
                                "nodeTitle": "赋值"
                            }
                        },
                        {
                            "key": "node_1horj1mpl49",
                            "type": "CascadeDeleteDataNode",
                            "name": "删除数据",
                            "props": {
                                "type": "CascadeDeleteDataProperties",
                                "name": null,
                                "desc": null,
                                "relatedModel": {
                                    "modelKey": "((key))",
                                    "modelAlias": "((key))",
                                    "modelName": "((name))"
                                },
                                "dataConditionPermissionKey": "((#ifCond dataConditionPermissionKey "==" null))null((else))((dataConditionPermissionKey))((/ifCond))",
                                "modelValue": {
                                    "type": "VarValue",
                                    "id": null,
                                    "varValue": [
                                        {
                                            "valueKey": "GLOBAL",
                                            "valueName": "全局变量",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "valueKey": "data",
                                            "valueName": "data",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": {
                                                "modelKey": "((key))",
                                                "modelAlias": "((key))",
                                                "modelName": null
                                            }
                                        }
                                    ],
                                    "fieldPaths": [
                                        {
                                            "fieldKey": "GLOBAL",
                                            "fieldName": "全局变量",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": null
                                        },
                                        {
                                            "fieldKey": "data",
                                            "fieldName": "data",
                                            "fieldType": null,
                                            "modelAlias": null,
                                            "relatedModel": {
                                                "modelKey": "((key))",
                                                "modelAlias": "((key))",
                                                "modelName": null
                                            }
                                        }
                                    ],
                                    "constValue": null,
                                    "valueType": "VAR",
                                    "fieldType": "Model"
                                },
                                "outputAssign": null,
                                "nodeTitle": "删除数据"
                            }
                        }
                    ],
                    "headNodeKeys": [
                        "node_1horipqb941",
                        "node_1horipqb942"
                    ]
                },
                {
                    "key": "node_1horisapu46",
                    "type": "ConditionElseNode",
                    "name": "else",
                    "props": {
                        "type": "ConditionElseProperties",
                        "name": null,
                        "desc": null,
                        "nodeTitle": "else"
                    }
                }
            ],
            "headNodeKeys": [
                "node_1horisapu45",
                "node_1horisapu46"
            ]
        },
        {
            "key": "node_1horimt9g33",
            "type": "EndNode",
            "name": "结束",
            "props": {
                "type": "EndProperties",
                "name": null,
                "desc": null,
                "nodeTitle": "结束"
            }
        }
    ],
    "input": [
        {
            "id": null,
            "fieldKey": "request",
            "fieldAlias": "request",
            "fieldName": "request",
            "fieldType": "Object",
            "description": null,
            "required": null,
            "defaultValue": null,
            "elements": [
                {
                    "id": null,
                    "fieldKey": "id",
                    "fieldAlias": "id",
                    "fieldName": "id",
                    "fieldType": "Number",
                    "description": null,
                    "required": null,
                    "defaultValue": null
                }
            ]
        }
    ],
    "output": null
}
