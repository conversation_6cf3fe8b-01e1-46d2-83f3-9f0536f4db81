package io.terminus.trantor2.service.management.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class GenSysServiceByTemplateVO implements Serializable {

    private static final long serialVersionUID = 5631226304125573456L;

    @Schema(description = "模型 Key")
    private String modelKey;

    @Schema(description = "服务 Key Map")
    private Map<String, String> serviceKeyMap;

}
