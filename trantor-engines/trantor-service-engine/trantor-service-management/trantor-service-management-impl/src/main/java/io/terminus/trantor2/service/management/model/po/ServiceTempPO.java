package io.terminus.trantor2.service.management.model.po;

import io.terminus.trantor2.application.item.service.ServiceTempBaseInfo;
import io.terminus.trantor2.application.item.service.ServiceTempContent;
import io.terminus.trantor2.application.po.TrantorAppItemPO;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;

/**
 * ServiceTempPO
 *
 * <AUTHOR> Created on 2024/1/8 14:04
 */
@Slf4j
@Generated
@Entity
@Getter
@Setter
@ToString
@NoArgsConstructor
@DiscriminatorValue("SERVICE_TEMPLATE")
public class ServiceTempPO extends TrantorAppItemPO<ServiceTempBaseInfo, ServiceTempContent> {
    private static final long serialVersionUID = 3594329547285805293L;
}
