package io.terminus.trantor2.service.management.model.po;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.application.item.service.SubServiceInfo;
import io.terminus.trantor2.service.common.enums.ServiceType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/23 1:52 下午
 */
@NoArgsConstructor
@Data
@Schema(description = "服务定义模型")
@EqualsAndHashCode(of = "id", callSuper = false)
public class ServiceTemplatePO implements Serializable {

    private static final long serialVersionUID = -6367315052390322203L;

    @Id
    @Schema(description = "主键", type = "long")
    private Long id;

    @Column(name = "template_key", nullable = false)
    @Schema(description = "模板 Key")
    private String templateKey;

    @Column(name = "template_name", nullable = false)
    @Schema(description = "模板名称")
    private String templateName;

    @Enumerated(EnumType.STRING)
    @Column(name = "service_type", nullable = false)
    @Schema(description = "服务类型")
    private ServiceType serviceType;

    @Schema(description = "子服务模版列表")
    private List<SubServiceInfo> subTemplateInfos;

    @Column(name = "template_content", columnDefinition="TEXT", nullable = false)
    @Schema(description = "模板内容")
    private String templateContent;

}
