package io.terminus.trantor2.service.management.service.impl;

import io.terminus.common.api.response.Response;
import io.terminus.common.scheduler.client.JobDefinitionClient;
import io.terminus.common.scheduler.enums.JobTypeEnum;
import io.terminus.common.scheduler.model.JobDefinitionDTO;
import io.terminus.common.scheduler.request.JobDefinitionEditRequest;
import io.terminus.common.scheduler.request.JobDefinitionSaveRequest;
import io.terminus.common.scheduler.request.JobKeyRequest;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.service.dsl.properties.SchedulerJob;
import io.terminus.trantor2.service.management.service.ServiceSchedulerJobService;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Service
@AllArgsConstructor
@EnableFeignClients(clients = JobDefinitionClient.class)
public class ServiceSchedulerJobServiceImpl implements ServiceSchedulerJobService {
    private final JobDefinitionClient jobDefinitionClient;

    private final ExecutorService pool = Executors.newCachedThreadPool(r -> {
        Thread thread = new Thread(r);
        thread.setName("invoke-scheduler-job");
        return thread;
    });

    @Override
    public void asyncCreateSchedulerJob(String metaKey, SchedulerJob schedulerJob, boolean followEnable) {
        TrantorContext.Context context = TrantorContext.copy();
        CompletableFuture.runAsync(() -> {
            TrantorContext.init();
            TrantorContext.setContext(context);

            String jobKey = jobKeyEmptyValidate(schedulerJob.getJobKey(), metaKey);
            String jobName = schedulerJob.getJobName();
            JobTypeEnum currentJobType = EnumUtils.getEnum(JobTypeEnum.class, schedulerJob.getJobType());
            if (Objects.isNull(currentJobType)) {
                log.error("unknown scheduler job type");
            } else {
                // @formatter:off
                Map<String, Object> extraInfo = MapUtil.toMap(ExtraInfo.builder()
                                                                       .userId(1L)
                                                                       .teamId(context.getTeamId())
                                                                       .teamCode(context.getTeamCode())
                                                                       .appId(context.getModuleId())
                                                                       .appCode(context.getModuleKey())
                                                                       .moduleKey(context.getModuleKey())
                                                                       .portalCode(context.getPortalCode())
                                                                       .metaKey(metaKey)
                                                                       .build());
                // @formatter:on
                JobDefinitionSaveRequest saveRequest = new JobDefinitionSaveRequest();
                saveRequest.setApp(context.getTeamCode());
                saveRequest.setModule(context.getModuleKey());
                saveRequest.setJobKey(jobKey);
                saveRequest.setJobName(jobName);
                saveRequest.setJobType(currentJobType);
                saveRequest.setCrontab(schedulerJob.getExpression());
                saveRequest.setDesc(schedulerJob.getDesc());
                saveRequest.setParams(schedulerJob.getParams());
                saveRequest.setExecType(schedulerJob.getExecType());
                saveRequest.setExtraInfo(JsonUtil.toJson(extraInfo));
                Response<Long> createResponse = jobDefinitionClient.create(saveRequest);
                if (createResponse.isSuccess()) {
                    if (followEnable) {
                        JobKeyRequest jobKeyRequest = new JobKeyRequest();
                        jobKeyRequest.setJobKey(jobKey);
                        Response<Boolean> enableResponse = jobDefinitionClient.enable(jobKeyRequest);
                        if (enableResponse.isSuccess()) {
                            log.error("execute enable scheduler job when create finished moment error: error msg: {}, error code: {}", enableResponse.getErrorMsg(), enableResponse.getErrorCode());
                        }
                    }
                } else {
                    log.error("execute create scheduler job error: error msg: {}, error code: {}", createResponse.getErrorMsg(), createResponse.getErrorCode());
                }
            }

            TrantorContext.clear();
        }, pool);
    }

    @Override
    public void asyncDeleteSchedulerJob(String metaKey, String jobKey) {
        TrantorContext.Context context = TrantorContext.copy();
        CompletableFuture.runAsync(() -> {
            TrantorContext.init();
            TrantorContext.setContext(context);

            String key = jobKeyEmptyValidate(jobKey, metaKey);
            JobKeyRequest keyRequest = new JobKeyRequest();
            keyRequest.setJobKey(key);
            Response<Boolean> disableResponse = jobDefinitionClient.disable(keyRequest);
            if (disableResponse.isSuccess()) {
                Response<Boolean> deleteResponse = jobDefinitionClient.delete(keyRequest);
                if (!deleteResponse.isSuccess()) {
                    log.error("execute delete scheduler job error: error msg: {}, error code: {}", deleteResponse.getErrorMsg(), deleteResponse.getErrorCode());
                }
            } else {
                log.error("execute disable scheduler job when delete moment error: error msg: {}, error code: {}", disableResponse.getErrorMsg(), disableResponse.getErrorCode());
            }

            TrantorContext.clear();
        }, pool);
    }

    @Override
    public void asyncUpdateSchedulerJob(String metaKey, SchedulerJob schedulerJob, boolean followEnable) {
        TrantorContext.Context context = TrantorContext.copy();
        CompletableFuture.runAsync(() -> {
            TrantorContext.init();
            TrantorContext.setContext(context);

            String jobKey = jobKeyEmptyValidate(schedulerJob.getJobKey(), metaKey);
            // 先停止调度实例，再进行修改
            JobKeyRequest jobKeyRequest = new JobKeyRequest();
            jobKeyRequest.setJobKey(jobKey);
            Response<Boolean> disableResponse = jobDefinitionClient.disable(jobKeyRequest);
            if (disableResponse.isSuccess()) {
                JobTypeEnum currentJobType = EnumUtils.getEnum(JobTypeEnum.class, schedulerJob.getJobType());
                if (Objects.isNull(currentJobType)) {
                    log.error("unknown scheduler job type");
                } else {
                    // @formatter:off
                    Map<String, Object> extraInfo = MapUtil.toMap(ExtraInfo.builder()
                                                                           .userId(1L)
                                                                           .teamId(context.getTeamId())
                                                                           .teamCode(context.getTeamCode())
                                                                           .appId(context.getModuleId())
                                                                           .appCode(context.getModuleKey())
                                                                           .moduleKey(context.getModuleKey())
                                                                           .portalCode(context.getPortalCode())
                                                                           .metaKey(metaKey)
                                                                           .build());
                    // @formatter:on
                    JobDefinitionEditRequest editRequest = new JobDefinitionEditRequest();
                    editRequest.setApp(context.getTeamCode());
                    editRequest.setModule(context.getModuleKey());
                    editRequest.setJobKey(jobKey);
                    editRequest.setJobName(schedulerJob.getJobName());
                    editRequest.setCrontab(schedulerJob.getExpression());
                    editRequest.setDesc(schedulerJob.getDesc());
                    editRequest.setParams(schedulerJob.getParams());
                    editRequest.setExecType(schedulerJob.getExecType());
                    editRequest.setExtraInfo(JsonUtil.toJson(extraInfo));
                    Response<Boolean> updateResponse = jobDefinitionClient.update(editRequest);
                    if (updateResponse.isSuccess()) {
                        if (followEnable) {
                            Response<Boolean> enableResponse = jobDefinitionClient.enable(jobKeyRequest);
                            if (!enableResponse.isSuccess()) {
                                log.error("execute enable scheduler job when update finished moment error: error msg: {}, error code: {}", disableResponse.getErrorMsg(), disableResponse.getErrorCode());
                            }
                        }
                    } else {
                        log.error("execute update scheduler job error: error msg: {}, error code: {}", updateResponse.getErrorMsg(), updateResponse.getErrorCode());
                    }
                }
            } else {
                log.error("execute disable scheduler job when update moment error: error msg: {}, error code: {}", disableResponse.getErrorMsg(), disableResponse.getErrorCode());
            }

            TrantorContext.clear();
        }, pool);
    }

    @Override
    public void asyncEnableSchedulerJob(String metaKey, String jobKey) {
        TrantorContext.Context context = TrantorContext.copy();
        CompletableFuture.runAsync(() -> {
            TrantorContext.init();
            TrantorContext.setContext(context);

            String key = jobKeyEmptyValidate(jobKey, metaKey);
            JobKeyRequest keyRequest = new JobKeyRequest();
            keyRequest.setJobKey(key);
            Response<Boolean> response = jobDefinitionClient.enable(keyRequest);
            if (!response.isSuccess()) {
                log.error("execute enable scheduler job error: error msg: {}, error code: {}", response.getErrorMsg(), response.getErrorCode());
            }

            TrantorContext.clear();
        }, pool);
    }

    @Override
    public boolean existsSchedulerJob(String metaKey, SchedulerJob schedulerJob) {
        String jobKey = schedulerJob.getJobKey();
        String key = jobKeyEmptyValidate(jobKey, metaKey);
        JobKeyRequest keyRequest = new JobKeyRequest();
        keyRequest.setJobKey(key);
        Response<JobDefinitionDTO> response = jobDefinitionClient.findByJobKey(keyRequest);
        if (response.isSuccess()) {
            return Objects.nonNull(response.getData());
        } else {
            log.error("find scheduler job error: error msg: {}, error code: {}", response.getErrorMsg(), response.getErrorCode());
            return false;
        }
    }

    @Override
    public void asyncDisableSchedulerJob(String metaKey, String jobKey) {
        TrantorContext.Context context = TrantorContext.copy();
        CompletableFuture.runAsync(() -> {
            TrantorContext.init();
            TrantorContext.setContext(context);

            String key = jobKeyEmptyValidate(jobKey, metaKey);
            JobKeyRequest keyRequest = new JobKeyRequest();
            keyRequest.setJobKey(key);
            Response<Boolean> response = jobDefinitionClient.disable(keyRequest);
            if (!response.isSuccess()) {
                log.error("execute disable scheduler job error: error msg: {}, error code: {}", response.getErrorMsg(), response.getErrorCode());
            }

            TrantorContext.clear();
        }, pool);
    }

    private String jobKeyEmptyValidate(String jobKey, String metaKey) {
        if (StringUtils.isBlank(jobKey)) {
            log.warn("current scheduler job key is miss");
            return String.format("%s_JOB", metaKey);
        }
        return jobKey;
    }

    @Data
    @Builder
    public static class ExtraInfo {
        private Long userId = 1L;
        private Long teamId;
        private String teamCode;
        // 兼容老版本
        private Long appId;
        private String appCode;
        // 兼容老版本
        private String moduleKey;
        private String portalCode;
        @Deprecated
        private String serviceKey;
        private String metaKey;
    }
}
