package io.terminus.trantor2.service.management.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.service.management.model.vo.KnowledgeBaseDocumentSplitVO;
import io.terminus.trantor2.service.management.model.vo.KnowledgeBaseQuickCreateVO;
import io.terminus.trantor2.service.management.model.vo.KnowledgeBaseVO;
import io.terminus.trantor2.service.management.service.KnowledgeBaseConsoleService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * KnowledgeBaseManagementController
 *
 * <AUTHOR> Created on 2025/5/6 16:37
 */
@Tag(name = "知识库配置管理")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/api/trantor/knowledgeBase/management")
public class KnowledgeBaseManagementController {
    private final KnowledgeBaseConsoleService knowledgeBaseConsoleService;

    @Operation(summary = "预览知识库文档")
    @PostMapping(value = "/preview")
    public Response<Boolean> previewKnowledgeBase(@RequestBody KnowledgeBaseVO knowledgeBaseVO) {
        // TODO 通过知识库DSL的配置加载OSS上的切片内容，返回给前端
        return Response.ok(Boolean.TRUE);
    }

    @Operation(summary = "保存知识库")
    @PostMapping(value = "/save")
    public Response<Boolean> saveKnowledgeBase(@RequestBody KnowledgeBaseVO knowledgeBaseVO) {
        knowledgeBaseConsoleService.saveKnowledgeBase(knowledgeBaseVO);
        return Response.ok(Boolean.TRUE);
    }

    @Operation(summary = "删除知识库")
    @PostMapping(value = "/delete")
    public Response<Boolean> deleteKnowledgeBase(@RequestParam("key") String knowledgeBaseKey) {
        knowledgeBaseConsoleService.deleteKnowledgeBase(knowledgeBaseKey);
        return Response.ok(Boolean.TRUE);
    }

    @Operation(summary = "快捷创建知识库")
    @PostMapping(value = "/quick-create")
    public Response<KnowledgeBaseVO> quickCreateKnowledgeBase(@RequestBody KnowledgeBaseQuickCreateVO knowledgeBaseQuickCreateVO) {
        return Response.ok(knowledgeBaseConsoleService.quickCreateKnowledgeBase(knowledgeBaseQuickCreateVO.getKey(),
                knowledgeBaseQuickCreateVO.getName(),
                knowledgeBaseQuickCreateVO.getDocuments()));
    }

    @Operation(summary = "知识库文档切片")
    @PostMapping(value = "/split")
    public Response<Boolean> splitKnowledgeBaseDocument(@RequestBody KnowledgeBaseDocumentSplitVO knowledgeBaseDocumentSplitVO) {
        knowledgeBaseConsoleService.split(knowledgeBaseDocumentSplitVO.getKey(), knowledgeBaseDocumentSplitVO.getDocument());
        return Response.ok(Boolean.TRUE);
    }

    @Operation(summary = "启用知识库")
    @GetMapping(value = "/enable")
    public Response<Boolean> enableKnowledgeBase(@RequestParam("key") String knowledgeBaseKey) {
        knowledgeBaseConsoleService.enable(knowledgeBaseKey);
        return Response.ok(Boolean.TRUE);
    }

    @Operation(summary = "停用知识库")
    @GetMapping(value = "/disable")
    public Response<Boolean> disableKnowledgeBase(@RequestParam("key") String knowledgeBaseKey) {
        knowledgeBaseConsoleService.disable(knowledgeBaseKey);
        return Response.ok(Boolean.TRUE);
    }
}
