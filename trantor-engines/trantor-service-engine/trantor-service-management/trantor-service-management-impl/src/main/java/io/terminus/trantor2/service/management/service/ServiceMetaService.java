package io.terminus.trantor2.service.management.service;

import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.doc.api.dto.PermissionDTO;
import io.terminus.trantor2.service.common.meta.AIAgentMeta;
import io.terminus.trantor2.service.management.api.management.ServiceMetaApi;
import io.terminus.trantor2.service.management.model.criteria.ServiceCriteria;
import io.terminus.trantor2.service.management.model.po.ServicePO;
import io.terminus.trantor2.service.management.model.vo.AgentVO;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface ServiceMetaService extends ServiceMetaApi {

    /**
     * 查询服务元信息详情
     *
     * @param id 服务元信息 ID
     * @return BusinessFlow
     */
    Optional<ServicePO> findById(Long id);

    /**
     * 查询服务元信息详情
     *
     * @param serviceDslMd5 服务 Dsl Md5
     * @return BusinessFlow
     */
    Optional<ServicePO> findByMd5Code(String serviceDslMd5, Long teamId);

    /**
     * 查询服务元信息详情
     *
     * @param serviceKey 服务 Key
     * @param teamId     团队 ID
     * @return 服务元信息
     */
    Optional<ServicePO> findByServiceKey(String serviceKey, Long teamId);

    /**
     * 根据 Agent Key 查询 Agent 元数据
     * @param serviceKey
     * @param teamId
     * @return
     */
    Optional<AIAgentMeta> findAgentByAgentKey(String serviceKey, Long teamId);

    /**
     * 查询服务元信息分页
     *
     * @param serviceCriteria 服务查询条件
     * @return 服务元信息分页
     */
    Paging<ServicePO> paging(ServiceCriteria serviceCriteria);

    /**
     * 创建
     *
     * @param servicePO 服务元信息
     * @return Boolean 是否创建成功
     */
    ServicePO create(ServicePO servicePO);

    /**
     * 保存服务元信息
     *
     * @param servicePO 服务元信息
     * @return Boolean 是否保存成功
     */
    ServicePO save(ServicePO servicePO);

    void saveAgent(AgentVO servicePO);

    Boolean delete(Long teamId, String serviceKey);

    ServicePO disable(Long teamId, String serviceKey);

    ServicePO enable(Long teamId, String serviceKey);

    void delete(String serviceKey);

    List<ServicePO> findAllInModule(Long teamId, String moduleKey);

    /**
     * 保存服务权限信息
     */
    void savePermission(String serviceKey, Map<String, PermissionDTO> permissions);

}
