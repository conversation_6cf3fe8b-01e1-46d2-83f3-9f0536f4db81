package io.terminus.trantor2.service.management.controller;

import com.google.common.base.Throwables;
import io.terminus.trantor2.meta.api.service.MetaEditService;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.service.common.utils.LongUtil;
import io.terminus.trantor2.service.management.api.management.ServiceMetaApi;
import io.terminus.trantor2.service.management.api.template.ServiceTemplateApi;
import io.terminus.trantor2.service.management.api.template.model.request.GenerateEventServiceRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@Slf4j
@RequestMapping("/api/trantor/service/migrate")
public class MetaMigrateController {

    @Autowired
    private ServiceTemplateApi serviceTemplateApi;

    @Autowired
    private ServiceMetaApi serviceMetaApi;

    @Autowired
    private MetaQueryService queryService;
    @Autowired
    private MetaEditService editService;

    @PostMapping("/event-service")
    public void migrateEventService(@RequestBody List<Map<String, Object>> datas) {
        for (Map<String, Object> data : datas) {
            GenerateEventServiceRequest request = new GenerateEventServiceRequest();
            request.setEventKey(data.get("key").toString());
            request.setEventName(data.get("name").toString());
            request.setSpiKey(data.get("spiKey").toString());
            request.setTeamId(LongUtil.valueOf(data.get("teamId")));
            try {
                serviceTemplateApi.generate(request);
            } catch (Exception e) {
                log.error("gen error, cause:{}", Throwables.getStackTraceAsString(e));
            }
        }
    }

    @GetMapping("/init-system-service")
    public void initSystemService(String moduleKey, String parentKey, Long teamId) {
        serviceMetaApi.initSysServices(moduleKey, parentKey, teamId, null);
    }
}
