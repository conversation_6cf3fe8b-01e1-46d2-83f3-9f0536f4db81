package io.terminus.trantor2.service.management.permission;

import io.terminus.trantor2.common.dto.PermissionExecuteContext;
import io.terminus.trantor2.service.engine.permission.ActionExecutionDataPermissionHandler;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * 2024/8/30 20:42
 **/
public class ActionExecutionDataPermissionHandlerMockImpl implements ActionExecutionDataPermissionHandler {

    @Override
    public void handle(@NotNull final PermissionExecuteContext context, String dataConditionKey, Object params) {
        // console 不需要启用数据权限，不做任何处理
    }
}
