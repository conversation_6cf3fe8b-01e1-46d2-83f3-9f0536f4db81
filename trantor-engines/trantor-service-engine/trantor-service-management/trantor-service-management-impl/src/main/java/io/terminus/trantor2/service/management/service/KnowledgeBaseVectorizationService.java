package io.terminus.trantor2.service.management.service;

import io.terminus.trantor2.service.dsl.properties.knowledgebase.KnowledgeBaseDocument;

import java.util.List;

public interface KnowledgeBaseVectorizationService {
    void saveKnowledgeBase(String knowledgeBaseKey, String knowledgeBaseName, List<KnowledgeBaseDocument> documents);

    void deleteKnowledgeBase(String knowledgeBaseKey);

    void split(String knowledgeBaseKey, String knowledgeBaseName, KnowledgeBaseDocument document);

    void deleteDocument(String knowledgeBaseKey, String knowledgeBaseName, KnowledgeBaseDocument document);
}
