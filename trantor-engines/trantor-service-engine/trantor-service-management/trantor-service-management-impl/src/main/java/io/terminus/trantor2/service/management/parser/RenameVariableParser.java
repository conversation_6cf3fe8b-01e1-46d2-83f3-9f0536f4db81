package io.terminus.trantor2.service.management.parser;

import io.terminus.trantor2.service.dsl.AIKnowledgeNode;
import io.terminus.trantor2.service.dsl.AINode;
import io.terminus.trantor2.service.dsl.ActionNode;
import io.terminus.trantor2.service.dsl.AssignNode;
import io.terminus.trantor2.service.dsl.CacheNode;
import io.terminus.trantor2.service.dsl.CallEventServiceNode;
import io.terminus.trantor2.service.dsl.CallServiceNode;
import io.terminus.trantor2.service.dsl.CascadeCreateDataNode;
import io.terminus.trantor2.service.dsl.CascadeDeleteDataNode;
import io.terminus.trantor2.service.dsl.CascadeUpdateDataNode;
import io.terminus.trantor2.service.dsl.ConditionElseNode;
import io.terminus.trantor2.service.dsl.ConnectorNode;
import io.terminus.trantor2.service.dsl.ErrorNode;
import io.terminus.trantor2.service.dsl.EventNode;
import io.terminus.trantor2.service.dsl.ExclusiveBranchNode;
import io.terminus.trantor2.service.dsl.ExclusiveConditionNode;
import io.terminus.trantor2.service.dsl.HasChildrenNode;
import io.terminus.trantor2.service.dsl.HttpServiceNode;
import io.terminus.trantor2.service.dsl.LoopNode;
import io.terminus.trantor2.service.dsl.NoticeNode;
import io.terminus.trantor2.service.dsl.ParallelBranchNode;
import io.terminus.trantor2.service.dsl.ParallelConditionNode;
import io.terminus.trantor2.service.dsl.RetrieveDataNode;
import io.terminus.trantor2.service.dsl.ScriptNode;
import io.terminus.trantor2.service.dsl.ServiceElement;
import io.terminus.trantor2.service.dsl.ServiceNode;
import io.terminus.trantor2.service.dsl.SqlNode;
import io.terminus.trantor2.service.dsl.StartNode;
import io.terminus.trantor2.service.dsl.SwitchCaseNode;
import io.terminus.trantor2.service.dsl.SwitchNode;
import io.terminus.trantor2.service.dsl.ValidationNode;
import io.terminus.trantor2.service.dsl.WorkflowNode;
import io.terminus.trantor2.service.dsl.enums.VariableType;
import io.terminus.trantor2.service.dsl.properties.ArrayField;
import io.terminus.trantor2.service.dsl.properties.AssignEntry;
import io.terminus.trantor2.service.dsl.properties.Condition;
import io.terminus.trantor2.service.dsl.properties.ConditionGroup;
import io.terminus.trantor2.service.dsl.properties.ConditionLeaf;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.dsl.properties.FieldEntry;
import io.terminus.trantor2.service.dsl.properties.OutputAssign;
import io.terminus.trantor2.service.dsl.properties.QueryRelatedModel;
import io.terminus.trantor2.service.dsl.properties.StringEntry;
import io.terminus.trantor2.service.dsl.properties.ValidationProperties;
import io.terminus.trantor2.service.dsl.properties.Value;
import io.terminus.trantor2.service.dsl.properties.VarValue;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 变量重命名解析器
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class RenameVariableParser {

    private final Map<Class<? extends ServiceNode<?>>, Consumer<RenameVariableContext>> parsers = new HashMap<>();

    @PostConstruct
    public void init() {
        initNodeParsers();
    }

    /**
     * 初始化节点Parser
     */
    private void initNodeParsers() {
        // @formatter:off
        parsers.put(StartNode.class,                (context) -> doParseStartNode(context, (StartNode) context.getCurrentNode()));
        parsers.put(CascadeCreateDataNode.class,    (context) -> doParseCascadeCreateDataNode(context, (CascadeCreateDataNode) context.getCurrentNode()));
        parsers.put(CascadeUpdateDataNode.class,    (context) -> doParseCascadeUpdateDataNode(context, (CascadeUpdateDataNode) context.getCurrentNode()));
        parsers.put(CascadeDeleteDataNode.class,    (context) -> doParseCascadeDeleteDataNode(context, (CascadeDeleteDataNode) context.getCurrentNode()));
        parsers.put(RetrieveDataNode.class,         (context) -> doParseRetrieveDataNode(context, (RetrieveDataNode) context.getCurrentNode()));
        parsers.put(ScriptNode.class,               (context) -> doParseScriptNode(context, (ScriptNode) context.getCurrentNode()));
        parsers.put(CallServiceNode.class,          (context) -> doParseCallServiceNode(context, (CallServiceNode) context.getCurrentNode()));
        parsers.put(CallEventServiceNode.class,     (context) -> doParseCallEventServiceNode(context, (CallEventServiceNode) context.getCurrentNode()));
        parsers.put(HttpServiceNode.class,          (context) -> doParseHttpServiceNode(context, (HttpServiceNode) context.getCurrentNode()));
        parsers.put(ActionNode.class,               (context) -> doParseActionNode(context, (ActionNode) context.getCurrentNode()));
        parsers.put(AssignNode.class,               (context) -> doParseAssignNode(context, (AssignNode) context.getCurrentNode()));
        parsers.put(EventNode.class,                (context) -> doParseEventNode(context, (EventNode) context.getCurrentNode()));
        parsers.put(SqlNode.class,                  (context) -> doParseSqlNode(context, (SqlNode) context.getCurrentNode()));
        parsers.put(AINode.class,                   (context) -> doParseAiNode(context, (AINode) context.getCurrentNode()));
        parsers.put(AIKnowledgeNode.class,          (context) -> doParseAIKnowledgeNode(context, (AIKnowledgeNode) context.getCurrentNode()));
        parsers.put(ConnectorNode.class,            (context) -> doParseConnectorNode(context, (ConnectorNode) context.getCurrentNode()));
        parsers.put(CacheNode.class,                (context) -> doParseCacheNode(context, (CacheNode) context.getCurrentNode()));
        parsers.put(LoopNode.class,                 (context) -> doParseLoopNode(context, (LoopNode) context.getCurrentNode()));
        parsers.put(ErrorNode.class,                (context) -> doParseErrorNode(context, (ErrorNode) context.getCurrentNode()));
        parsers.put(NoticeNode.class,               (context) -> doParseNoticeNode(context, (NoticeNode) context.getCurrentNode()));
        parsers.put(WorkflowNode.class,             (context) -> doParseWorkflowNode(context, (WorkflowNode) context.getCurrentNode()));
        parsers.put(ValidationNode.class,           (context) -> doParseValidationNode(context, (ValidationNode) context.getCurrentNode()));
        parsers.put(ExclusiveBranchNode.class,      (context) -> doParseHasChildrenNode(context, (ExclusiveBranchNode) context.getCurrentNode()));
        parsers.put(ExclusiveConditionNode.class,   (context) -> doParseExclusiveConditionNode(context, (ExclusiveConditionNode) context.getCurrentNode()));
        parsers.put(ParallelBranchNode.class,       (context) -> doParseHasChildrenNode(context, (ParallelBranchNode) context.getCurrentNode()));
        parsers.put(ParallelConditionNode.class,    (context) -> doParseParallelConditionNode(context, (ParallelConditionNode) context.getCurrentNode()));
        parsers.put(SwitchNode.class,               (context) -> doParseSwitchNode(context, (SwitchNode) context.getCurrentNode()));
        parsers.put(SwitchCaseNode.class,           (context) -> doParseHasChildrenNode(context, (SwitchCaseNode) context.getCurrentNode()));
        parsers.put(ConditionElseNode.class,        (context) -> doParseHasChildrenNode(context, (ConditionElseNode) context.getCurrentNode()));
        // @formatter:on
    }

    /**
     * 解析变量重命名
     */
    public void parse(RenameVariableContext context) {
        if (context.getCurrentNode() == null) {
            return;
        }

        Consumer<RenameVariableContext> parser = getNodeParser(context.getCurrentNode());
        if (parser != null) {
            parser.accept(context);
        }

        parse(context.nextNode());
    }

    private Consumer<RenameVariableContext> getNodeParser(ServiceElement<?> serviceNode) {
        return parsers.get(serviceNode.getClass());
    }

    private void doParseStartNode(RenameVariableContext context, StartNode node) {
        if (!context.isRenaming() && context.getRequest().getNodeKey().equals(node.getKey())) {
            // 字段定义变更
            switch (context.getRequest().getVarType()) {
                case REQUEST:
                case OUTPUT:
                case GLOBAL:
                    String changedPath = context.getRequest().getVarType().getKey() + "." + context.getRequest().getBeforePath();
                    if (context.getRequest().isDelete()) {
                        context.addDeletedPath(changedPath);
                        context.setRenaming(true);
                    } else if (context.getRequest().isModify()) {
                        context.addChangedPath(new RenameVariableContext.ChangedPath(changedPath,
                                context.getRequest().getBeforeField(), context.getRequest().getAfterField()));
                        context.setRenaming(true);
                    }
            }
        }
    }

    private void doParseEventNode(RenameVariableContext context, EventNode node) {
        if (context.isRenaming()) {
            renameStringEntryList(node.getProps().getInputMapping(), context);
        }
    }

    private void doParseCascadeDeleteDataNode(RenameVariableContext context, CascadeDeleteDataNode node) {
        if (context.isRenaming()) {
            boolean deleted = renameValue(node.getProps().getModelValue(), context);
            if (deleted) {
                node.getProps().setModelValue(null);
            }
            renameConditionGroup(node.getProps().getConditionGroup(), context);
        }
    }

    private void doParseCascadeUpdateDataNode(RenameVariableContext context, CascadeUpdateDataNode node) {
        if (context.isRenaming()) {
            boolean deleted = renameValue(node.getProps().getModelValue(), context);
            if (deleted) {
                node.getProps().setModelValue(null);
            }
            renameConditionGroup(node.getProps().getConditionGroup(), context);
        }
    }

    private void doParseCascadeCreateDataNode(RenameVariableContext context, CascadeCreateDataNode node) {
        if (context.isRenaming()) {
            boolean deleted = renameValue(node.getProps().getModelValue(), context);
            if (deleted) {
                node.getProps().setModelValue(null);
            }
            renameOutputAssign(node.getProps().getOutputAssign(), context);
        }
    }

    private void doParseRetrieveDataNode(RenameVariableContext context, RetrieveDataNode node) {
        if (context.isRenaming()) {
            boolean deleted = renameValue(node.getProps().getPageable(), context);
            if (deleted) {
                node.getProps().setPageable(null);
            }
            deleted = renameValue(node.getProps().getDynamicCondition(), context);
            if (deleted) {
                node.getProps().setDynamicCondition(null);
            }
            renameConditionGroup(node.getProps().getConditionGroup(), context);
            renameQueryRelatedModel(node.getProps().getSubQueryRelatedModels(), context);
            renameOutputAssign(node.getProps().getOutputAssign(), context);
        }
    }

    private void doParseScriptNode(RenameVariableContext context, ScriptNode node) {
        // 字段定义变更
        if (!context.isRenaming()
                && context.getRequest().getNodeKey().equals(node.getKey())
                && VariableType.NODE_OUTPUT.equals(context.getRequest().getVarType())) {
            String changedPath = changingPath(node.getKey(), node.getProps().getOutputAssign(), node.getProps().getOutput(), context);
            if (context.getRequest().isDelete()) {
                context.addDeletedPath(changedPath);
                context.setRenaming(true);
            } else if (context.getRequest().isModify()) {
                context.addChangedPath(new RenameVariableContext.ChangedPath(changedPath,
                        context.getRequest().getBeforeField(), context.getRequest().getAfterField()));
                context.setRenaming(true);
            }
        }
        if (context.isRenaming()) {
            renameFieldEntryList(node.getProps().getInputMapping(), context);
            renameOutputAssign(node.getProps().getOutputAssign(), context);
        }
    }

    private void doParseCallServiceNode(RenameVariableContext context, CallServiceNode node) {
        if (context.isRenaming()) {
            renameFieldEntryList(node.getProps().getInputMapping(), context);
            renameOutputAssign(node.getProps().getOutputAssign(), context);
        }
    }

    private void doParseErrorNode(RenameVariableContext context, ErrorNode node) {
        if (context.isRenaming()) {
            renameStringEntryList(node.getProps().getPlaceholderMapping(), context);
        }
    }

    private void doParseWorkflowNode(RenameVariableContext context, WorkflowNode node) {
        if (context.isRenaming()) {
            renameValue(node.getProps().getInputMapping(), context);
            renameFieldEntryList(node.getProps().getCustomVariables(), context);
        }
    }

    private void doParseNoticeNode(RenameVariableContext context, NoticeNode node) {
        if (context.isRenaming()) {
            renameStringEntryList(node.getProps().getInputMapping(), context);
            renameValueList(node.getProps().getReceiverEmails(), context);
            renameValueList(node.getProps().getReceiverUserIds(), context);
            renameValueList(node.getProps().getReceiverMobiles(), context);
        }
    }

    private void doParseCallEventServiceNode(RenameVariableContext context, CallEventServiceNode node) {
        if (context.isRenaming()) {
            renameConditionGroup(node.getProps().getConditionGroup(), context);
            renameOutputAssign(node.getProps().getOutputAssign(), context);
        }
    }

    private void doParseHttpServiceNode(RenameVariableContext context, HttpServiceNode node) {
        // 字段定义变更
        if (!context.isRenaming()
                && context.getRequest().getNodeKey().equals(node.getKey())
                && VariableType.NODE_OUTPUT.equals(context.getRequest().getVarType())) {
            String changedPath = changingPath(node.getKey(), node.getProps().getOutputAssign(), node.getProps().getOutput(), context);
            if (context.getRequest().isDelete()) {
                context.addDeletedPath(changedPath);
                context.setRenaming(true);
            } else if (context.getRequest().isModify()) {
                context.addChangedPath(new RenameVariableContext.ChangedPath(changedPath,
                        context.getRequest().getBeforeField(), context.getRequest().getAfterField()));
                context.setRenaming(true);
            }
        }

        if (context.isRenaming()) {
            renameStringEntryList(node.getProps().getHeaders(), context);
            renameStringEntryList(node.getProps().getPathVariables(), context);
            renameStringEntryList(node.getProps().getInputMapping(), context);
            renameValue(node.getProps().getBody(), context);
            renameOutputAssign(node.getProps().getOutputAssign(), context);
        }
    }

    private String changingPath(String nodeKey, OutputAssign outputAssign, List<Field> output, RenameVariableContext context) {
        if (outputAssign != null && outputAssign.isSystemDefault()) {
            return VariableType.GLOBAL.getKey() + "." +
                    VariableType.NODE_OUTPUT.formatKey(nodeKey) + "." + context.getRequest().getBeforePath();
        } else {
            return VariableType.NODE_OUTPUT.formatKey(nodeKey) + "." + context.getRequest().getBeforePath();
        }
    }

    private void doParseActionNode(RenameVariableContext context, ActionNode node) {
        if (context.isRenaming()) {
            renameFieldEntryList(node.getProps().getInputMapping(), context);
            renameConditionGroup(node.getProps().getConditionGroup(), context);
            renameOutputAssign(node.getProps().getOutputAssign(), context);
        }
    }

    private void doParseAssignNode(RenameVariableContext context, AssignNode node) {
        if (context.isRenaming()) {
            renameAssignEntryList(node.getProps().getAssignments(), context);
        }
    }

    private void doParseValidationNode(RenameVariableContext context, ValidationNode node) {
        if (context.isRenaming()) {
            if (CollectionUtils.isNotEmpty(node.getProps().getValidationRules())) {
                for (ValidationProperties.ValidationRule rule : node.getProps().getValidationRules()) {
                    renameValue(rule.getValidateValue(), context);
                }
            }
        }
    }

    private void doParseAiNode(RenameVariableContext context, AINode node) {
        if (context.isRenaming()) {
            renameStringEntryList(node.getProps().getSysMessagePlaceholderMapping(), context);
            renameStringEntryList(node.getProps().getUserMessagePlaceholderMapping(), context);
            renameValue(node.getProps().getAttachments(), context);
            renameStringEntryList(node.getProps().getAudioPathPlaceholderMapping(), context);
            renameOutputAssign(node.getProps().getOutputAssign(), context);
        }
    }

    private void doParseSqlNode(RenameVariableContext context, SqlNode node) {
        // 字段定义变更
        if (!context.isRenaming()
                && context.getRequest().getNodeKey().equals(node.getKey())
                && VariableType.NODE_OUTPUT.equals(context.getRequest().getVarType())) {
            String changedPath = changingPath(node.getKey(), node.getProps().getOutputAssign(), node.getProps().getOutput(), context);
            if (context.getRequest().isDelete()) {
                context.addDeletedPath(changedPath);
                context.setRenaming(true);
            } else if (context.getRequest().isModify()) {
                context.addChangedPath(new RenameVariableContext.ChangedPath(changedPath,
                        context.getRequest().getBeforeField(), context.getRequest().getAfterField()));
                context.setRenaming(true);
            }
        }
        if (context.isRenaming()) {
            renameStringEntryList(node.getProps().getSqlPlaceholderMapping(), context);
            renameOutputAssign(node.getProps().getOutputAssign(), context);
        }
    }

    private void doParseAIKnowledgeNode(RenameVariableContext context, AIKnowledgeNode node) {
        if (context.isRenaming()) {
            renameOutputAssign(node.getProps().getOutputAssign(), context);
        }
    }

    private void doParseConnectorNode(RenameVariableContext context, ConnectorNode node) {
        if (context.isRenaming()) {
            renameStringEntryList(node.getProps().getInputMapping(), context);
            renameOutputAssign(node.getProps().getOutputAssign(), context);
        }
    }

    private void doParseCacheNode(RenameVariableContext context, CacheNode node) {
        // 字段定义变更
        if (!context.isRenaming()
                && context.getRequest().getNodeKey().equals(node.getKey())
                && VariableType.NODE_OUTPUT.equals(context.getRequest().getVarType())) {
            String changedPath = changingPath(node.getKey(), node.getProps().getOutputAssign(), node.getProps().getOutput(), context);
            if (context.getRequest().isDelete()) {
                context.addDeletedPath(changedPath);
                context.setRenaming(true);
            } else if (context.getRequest().isModify()) {
                context.addChangedPath(new RenameVariableContext.ChangedPath(changedPath,
                        context.getRequest().getBeforeField(), context.getRequest().getAfterField()));
                context.setRenaming(true);
            }
        }
        if (context.isRenaming()) {
            renameValue(node.getProps().getCacheValue(), context);
            renameOutputAssign(node.getProps().getOutputAssign(), context);
        }
    }

    private void doParseLoopNode(RenameVariableContext context, LoopNode node) {
        if (context.isRenaming()) {
            boolean deleted = renameValue(node.getProps().getLoopData(), context);
            if (deleted) {
                node.getProps().setLoopData(null);
                node.getProps().setLoopElement(null);
                context.addDeletedPath(VariableType.LOOP.formatKey(node.getKey()));
            } else {
                // 把循环element重新设置
                if (node.getProps().getLoopData() != null && context.getChangedPaths() != null) {
                    String loopPath = node.getProps().getLoopData().joinVars();
                    context.getChangedPaths().stream()
                            .filter(p -> p.getChangedPath().equals(loopPath))
                            .findFirst()
                            .ifPresent(p -> node.getProps().setLoopElement(((ArrayField) p.getAfterField()).getElement()));
                }
            }

            doParseHasChildrenNode(context, node);

            // 循环变量仅对循环内节点有效，处理完后清除掉
            if (deleted) {
                context.removeDeletedPath(VariableType.LOOP.formatKey(node.getKey()));
            }
        }
    }

    private void doParseExclusiveConditionNode(RenameVariableContext context, ExclusiveConditionNode node) {
        if (context.isRenaming()) {
            renameConditionGroup(node.getProps().getConditionGroup(), context);
            doParseHasChildrenNode(context, node);
        }
    }

    private void doParseParallelConditionNode(RenameVariableContext context, ParallelConditionNode node) {
        if (context.isRenaming()) {
            renameConditionGroup(node.getProps().getConditionGroup(), context);
            doParseHasChildrenNode(context, node);
        }
    }

    private void doParseSwitchNode(RenameVariableContext context, SwitchNode node) {
        if (context.isRenaming()) {
            renameValue(node.getProps().getSwitchVariable(), context);
            doParseHasChildrenNode(context, node);
        }
    }

    private void doParseHasChildrenNode(RenameVariableContext context, HasChildrenNode<?> node) {
        if (CollectionUtils.isEmpty(node.getChildren())) {
            return;
        }

        RenameVariableContext childContext = new RenameVariableContext(node, context.getRequest());
        childContext.setRenaming(context.isRenaming());
        childContext.setChangedPaths(context.getChangedPaths());
        childContext.setDeletedPaths(context.getDeletedPaths());

        parse(childContext);
    }

    private void renameOutputAssign(OutputAssign outputAssign, RenameVariableContext context) {
        if (outputAssign != null && !outputAssign.isSystemDefault()) {
            renameAssignEntryList(outputAssign.getCustomAssignments(), context);
        }
    }

    private void renameAssignEntryList(List<AssignEntry> assignEntryList, RenameVariableContext context) {
        if (CollectionUtils.isNotEmpty(assignEntryList)) {
            for (AssignEntry entry : assignEntryList) {
                boolean deleted = renameValue(entry.getField(), context);
                if (deleted) {
                    entry.setField(null);
                }
                boolean deleted2 = renameValue(entry.getValue(), context);
                if (deleted2) {
                    entry.setValue(null);
                }
            }
        }
    }

    private void renameFieldEntryList(List<FieldEntry> fieldEntryList, RenameVariableContext context) {
        if (CollectionUtils.isNotEmpty(fieldEntryList)) {
            for (FieldEntry entry : fieldEntryList) {
                boolean deleted = renameValue(entry.getValue(), context);
                if (deleted) {
                    entry.setValue(null);
                }
            }
        }
    }

    private void renameConditionGroup(ConditionGroup conditionGroup, RenameVariableContext context) {
        if (conditionGroup != null) {
            if (CollectionUtils.isNotEmpty(conditionGroup.getConditions())) {
                for (Condition condition : conditionGroup.getConditions()) {
                    if (condition instanceof ConditionGroup) {
                        renameConditionGroup((ConditionGroup) condition, context);
                    } else if (condition instanceof ConditionLeaf) {
                        ConditionLeaf conditionLeaf = (ConditionLeaf) condition;
                        boolean deleted = renameValue(conditionLeaf.getLeftValue(), context);
                        if (deleted) {
                            conditionLeaf.setLeftValue(null);
                        }

                        boolean deleted2 = renameValue(conditionLeaf.getRightValue(), context);
                        if (deleted2) {
                            conditionLeaf.setRightValue(null);
                        }

                        renameValueList(conditionLeaf.getRightValues(), context);
                    }
                }
            }
        }
    }

    private void renameStringEntryList(List<StringEntry> stringEntryList, RenameVariableContext context) {
        if (CollectionUtils.isNotEmpty(stringEntryList)) {
            for (StringEntry entry : stringEntryList) {
                boolean deleted = renameValue(entry.getValue(), context);
                if (deleted) {
                    entry.setValue(null);
                }
            }
        }
    }

    /**
     * 修改Value
     *
     * @return true，表明删除改表了，false:修改完成或者无需修改
     */
    private boolean renameValue(Value value, RenameVariableContext context) {
        if (value != null) {
            if (value instanceof VarValue) {
                VarValue varValue = (VarValue) value;

                // 如果是常量类型,不需要处理
                if (varValue.getValueType() == null
                        || varValue.getValueType().isConst()
                        || CollectionUtils.isEmpty(varValue.getVarValue())) {
                    return false;
                }

                // 获取变量的完整路径
                String varPath = varValue.joinVars();
                if (CollectionUtils.isNotEmpty(context.getChangedPaths())) {
                    for (RenameVariableContext.ChangedPath changedPath : context.getChangedPaths()) {
                        // 修改操作 - 检查变量路径是否匹配
                        // changedPath可能是varPath的前缀,需要定位到具体的VarStage
                        if (varPath.startsWith(changedPath.getChangedPath())) {

                            // 找到需要修改的VarStage
                            String[] changingPathParts = changedPath.getChangedPath().split("\\.");
                            int targetIndex = changingPathParts.length - 1;
                            VarValue.VarStage targetStage = varValue.getVarValue().get(targetIndex);

                            // 获取修改前后的字段信息
                            Field afterField = changedPath.getAfterField();

                            // 更新变量名称
                            targetStage.setValueKey(afterField.getFieldKey());
                            targetStage.setValueName(afterField.getFieldName());
                        }
                    }
                }

                if (CollectionUtils.isNotEmpty(context.getDeletedPaths())) {
                    // 删除操作 - 检查变量路径是否匹配
                    // deletedPath可能是varPath的前缀,比如:
                    // deletedPath: REQUEST.user
                    // varPath: REQUEST.user.name
                    if (context.getDeletedPaths().stream().anyMatch(varPath::startsWith)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private void renameValueList(List<Value> valueList, RenameVariableContext context) {
        if (CollectionUtils.isNotEmpty(valueList)) {
            valueList.removeIf(value -> renameValue(value, context));
        }
    }

    private void renameQueryRelatedModel(List<QueryRelatedModel> subQueryRelatedModels, RenameVariableContext context) {
        if (CollectionUtils.isNotEmpty(subQueryRelatedModels)) {
            for (QueryRelatedModel model : subQueryRelatedModels) {
                renameConditionGroup(model.getConditionGroup(), context);
                renameQueryRelatedModel(model.getSubQueryRelatedModels(), context);
            }
        }
    }
}
