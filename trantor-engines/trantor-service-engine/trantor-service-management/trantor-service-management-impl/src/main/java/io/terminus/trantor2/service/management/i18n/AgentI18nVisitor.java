package io.terminus.trantor2.service.management.i18n;

import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.i18n.I18nVisitor;
import io.terminus.trantor2.service.common.meta.AIAgentMeta;
import io.terminus.trantor2.service.dsl.properties.Tool;
import io.terminus.trantor2.service.management.repo.AIAgentRepo;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class AgentI18nVisitor implements I18nVisitor {
    private final AIAgentRepo aiAgentRepo;

    @Override
    public MetaType getType() {
        return MetaType.AIAgent;
    }

    @Override
    public Set<String> visit(Cond cond, @Nullable Predicate<String> predicate) {
        cond = cond.and(Field.type().equal(getType().name()));
        List<AIAgentMeta> agents = aiAgentRepo.findAll(cond, ResourceContext.ctxFromThreadLocal())
                .stream()
                .filter(it -> predicate == null || predicate.test(KeyUtil.moduleKey(it.getKey())))
                .collect(Collectors.toList());

        Set<String> res = new HashSet<>();

        for (AIAgentMeta agent : agents) {
            if (StringUtils.isNotBlank(agent.getName())) {
                res.add(agent.getName());
            }

            if (agent.getResourceProps() != null && agent.getResourceProps().getAgent() != null) {
                var agentProps = agent.getResourceProps().getAgent().getProps();
                if (agentProps != null) {
                    if (StringUtils.isNotBlank(agentProps.getGreetings())) {
                        res.add(agentProps.getGreetings());
                    }

                    if (!CollectionUtils.isEmpty(agentProps.getUserQuestions())) {
                        res.addAll(agentProps.getUserQuestions().stream()
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toSet()));
                    }

                    if (!CollectionUtils.isEmpty(agentProps.getSkillTools())) {
                        res.addAll(agentProps.getSkillTools().stream()
                                .map(Tool::getName)
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toSet()));
                    }
                }
            }
        }
        return res;
    }
}
