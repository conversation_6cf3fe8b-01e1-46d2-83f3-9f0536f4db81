package io.terminus.trantor2.service.management.service.impl;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.dsl.KnowledgeBase;
import io.terminus.trantor2.service.management.service.KnowledgeBaseCacheService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@AllArgsConstructor
public class KnowledgeBaseCacheServiceImpl implements KnowledgeBaseCacheService {
    /**
     * 知识库向量化进度状态缓存Key前缀
     */
    private static final String CACHE_KEY_PREFIX = "knowledge-base:document-vectorization-progress";
    /**
     * 知识库向量化进度状态缓存失效时间，单位分钟
     */
    private static final Integer CACHE_KEY_TIMEOUT = 10;

    private final RedisTemplate<String, Object> progressRedisTemplate;

    public String getCacheKey(String knowledgeBaseKey) {
        return String.format("%s:%s", CACHE_KEY_PREFIX, knowledgeBaseKey);
    }

    @Override
    public KnowledgeBaseVectorizationProgress getCacheValue(String knowledgeBaseKey) {
        String cacheKey = getCacheKey(knowledgeBaseKey);
        Object cacheValue = progressRedisTemplate.opsForValue().get(cacheKey);
        return cacheValue == null ? null : JsonUtil.fromJson(cacheValue.toString(), KnowledgeBaseVectorizationProgress.class);
    }

    @Override
    public void initCacheValue(KnowledgeBase knowledgeBase) {
        int documentTotal = knowledgeBase.getProps().getDocuments().size();
        KnowledgeBaseVectorizationProgress progress = new KnowledgeBaseVectorizationProgress();
        progress.setKnowledgeBaseKey(knowledgeBase.getKey());
        progress.setKnowledgeBaseName(knowledgeBase.getName());
        progress.setTotal(documentTotal);
        progress.setProcessed(0);
        progress.setFailed(0);
        progress.setProgress(0.00D);
        progress.setStatus(ProgressStatus.DOING.name());
        progress.setCreatedAt(new Date());
        progressRedisTemplate.opsForValue().set(getCacheKey(knowledgeBase.getKey()), JsonUtil.toJson(progress), CACHE_KEY_TIMEOUT, TimeUnit.MINUTES);
    }

    @Override
    public void updateCacheValue(String knowledgeBaseKey, ProgressStatus progressStatus) {
        KnowledgeBaseVectorizationProgress vectorizationProgress = getCacheValue(knowledgeBaseKey);
        vectorizationProgress.setProcessed(vectorizationProgress.getProcessed() + 1);
        double progress = vectorizationProgress.getProcessed() * 1.0D / vectorizationProgress.getTotal();
        vectorizationProgress.setProgress(progress);
        vectorizationProgress.setStatus(progressStatus.name());
        progressRedisTemplate.opsForValue().set(getCacheKey(knowledgeBaseKey), JsonUtil.toJson(vectorizationProgress), CACHE_KEY_TIMEOUT, TimeUnit.MINUTES);
    }

    @Override
    public void finishCacheValue(String knowledgeBaseKey) {
        KnowledgeBaseVectorizationProgress vectorizationProgress = getCacheValue(knowledgeBaseKey);
        vectorizationProgress.setStatus(ProgressStatus.DONE.name());
        progressRedisTemplate.opsForValue().set(getCacheKey(knowledgeBaseKey), JsonUtil.toJson(vectorizationProgress), CACHE_KEY_TIMEOUT, TimeUnit.MINUTES);
    }
}
