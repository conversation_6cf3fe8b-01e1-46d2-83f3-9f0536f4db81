package io.terminus.trantor2.service.management.converter;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.common.utils.DataLoader;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * ServiceMetaConverterTest
 *
 * <AUTHOR> Created on 2023/11/27 16:15
 */
class ServiceMetaConverterTest {

    @Test
    void testReverseDSL() {
        ServiceDefinition dsl = DataLoader.loadObj("/template/TestContext.json", ServiceDefinition.class);

        System.out.println(JsonUtil.toJson(dsl));
//        dsl.convertToOld();

        System.out.println(JsonUtil.toJson(dsl));

        Assertions.assertNotNull(dsl);
    }
}
