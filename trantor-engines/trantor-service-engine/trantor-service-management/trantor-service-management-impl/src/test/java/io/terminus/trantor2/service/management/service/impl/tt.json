{"key": "node_1h3vq0ce76", "type": "CreateDataNode", "name": "添加数据", "nextNodeKey": "node_1h448ha8b7", "props": {"type": "CreateDataProperties", "relatedModel": {"modelKey": "<PERSON><PERSON>", "modelName": "商品信息"}, "inputMapping": [{"field": {"fieldKey": "itemName", "fieldType": "Text", "fieldName": "商品名称"}, "value": {"type": "VarValue", "valueType": "VAR", "fieldType": "Text", "varValue": [{"valueKey": "REQUEST", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request", "relatedModel": {"modelKey": "<PERSON><PERSON>", "modelName": "商品信息"}}, {"valueKey": "itemName", "valueName": "商品名称", "modelAlias": "商品信息"}]}}, {"field": {"fieldKey": "itemPrice", "fieldType": "Number", "fieldName": "商品价格"}, "value": {"type": "VarValue", "valueType": "VAR", "fieldType": "Number", "varValue": [{"valueKey": "REQUEST", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request", "relatedModel": {"modelKey": "<PERSON><PERSON>", "modelName": "商品信息"}}, {"valueKey": "itemPrice", "valueName": "商品价格", "modelAlias": "商品信息"}]}}], "outputAssign": {"outputAssignType": "SYSTEM"}, "nodeTitle": "添加数据", "nodeContent": "商品信息", "nodeKey": "node_1h3vq0ce76"}}