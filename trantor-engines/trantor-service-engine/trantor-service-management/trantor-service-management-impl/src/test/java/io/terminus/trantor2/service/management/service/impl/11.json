{"key": "", "name": "添加数据", "nextNodeKey": "", "props": {"inputMapping": [{"field": {"fieldKey": "name", "fieldName": "name", "fieldType": "Text"}, "value": {"fieldType": "Text", "type": "VarValue", "valueType": "VAR", "varValue": [{"valueKey": "REQUEST", "valueName": "服务入参"}, {"relatedModel": {"modelKey": "TERP_MIGRATE$test005", "modelName": "test005"}, "valueKey": "request", "valueName": "request"}, {"valueKey": "name", "valueName": "name"}]}}, {"field": {"fieldKey": "price", "fieldName": "价格", "fieldType": "Number"}, "value": {"fieldType": "Number", "type": "VarValue", "valueType": "VAR", "varValue": [{"valueKey": "REQUEST", "valueName": "服务入参"}, {"relatedModel": {"modelKey": "TERP_MIGRATE$test005", "modelName": "test005"}, "valueKey": "request", "valueName": "request"}, {"valueKey": "price", "valueName": "价格"}]}}], "outputAssign": {"outputAssignType": "SYSTEM"}, "relatedModel": {"modelKey": "TERP_MIGRATE$test005", "modelName": "test005"}, "type": "CreateDataProperties"}, "type": "CreateDataNode"}