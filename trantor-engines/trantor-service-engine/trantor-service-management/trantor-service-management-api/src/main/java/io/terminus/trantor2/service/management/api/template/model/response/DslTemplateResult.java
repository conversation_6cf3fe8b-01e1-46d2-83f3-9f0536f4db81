package io.terminus.trantor2.service.management.api.template.model.response;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class DslTemplateResult implements Serializable {

    private static final long serialVersionUID = 7476394149118741162L;

    /**
     * 模板 Key
     */
    private String templateKey;

    /**
     * 模板名
     */
    private String templateName;

    /**
     * 生成的服务模板 Key，格式为 templateKey + "_" + 唯一 ID
     */
    private String serviceKey;

}
