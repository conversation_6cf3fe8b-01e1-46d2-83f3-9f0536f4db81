package io.terminus.trantor2.service.management.api.management.model;

import io.terminus.trantor2.service.common.enums.ServiceType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/5/18 3:50 PM
 */
@Data
public class FindServiceByKeyResponse implements Serializable {

    private static final long serialVersionUID = 8971569731023431226L;

    private Long id;

    /**
     * 目录节点 Key
     */
    private String parentKey;

    /**
     * 服务 Key
     */
    private String serviceKey;

    private ServiceType serviceType;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 关联模型 Key
     */
    private String modelKey;

    /**
     * 服务名称
     */
    private String serviceDslMd5;

    /**
     * 服务定义 DSL
     */
    private String serviceDslJson;

    /**
     * 是否为启用状态
     */
    private Boolean isEnabled;

    /**
     * 是否被删除
     */
    private Boolean isDeleted;

    /**
     * 创建人 ID
     */
    private Long createdBy;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新人 ID
     */
    private Long updatedBy;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * TeamId
     */
    private Long teamId;

    /**
     * AppId
     */
    private Long appId;

}
