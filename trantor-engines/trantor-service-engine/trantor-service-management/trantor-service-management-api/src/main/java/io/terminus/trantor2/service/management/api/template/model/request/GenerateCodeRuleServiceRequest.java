package io.terminus.trantor2.service.management.api.template.model.request;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/5/4 4:07 PM
 */
@Data
public class GenerateCodeRuleServiceRequest extends AbstractGenerateServiceRequest {

    private static final long serialVersionUID = 8749243994372461099L;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则标识
     */
    private String ruleKey;

    /**
     * 服务 SPI Key
     */
    private String spiKey;

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
        this.name = ruleName;
    }

    public void setRuleKey(String ruleKey) {
        this.ruleKey = ruleKey;
        this.key = ruleKey;
    }

}
