package io.terminus.trantor2.service.management.api.template;

import io.terminus.trantor2.service.management.api.template.model.request.*;
import io.terminus.trantor2.service.management.api.template.model.response.*;

/**
 * <AUTHOR>
 */
public interface ServiceTemplateApi {

    /**
     * 根据模板生成服务
     *
     * @param request 模板生成服务请求
     * @return 统一 ID
     */
    @Deprecated
    GenerateServiceByTemplateResponse generate(GenerateServiceByTemplateRequest request);

    /**
     * 根据模板生成服务，支持传递不同模型元数据
     *
     * @param request 模板生成服务请求
     * @return 统一 ID
     */
    @Deprecated
    GenerateServiceByTemplateResponse generate(GenerateServiceByTemplateWithMultiModelRequest request);

    /**
     * 通过服务模板 Key 与模型 Key 生成对应服务
     *
     * @param request 模板生成服务请求
     * @return 服务 ID
     */
    GenerateServiceByTemplateKeyResponse generate(GenerateServiceByTemplateKeyRequest request);

    /**
     * 生成事件服务
     *
     * @param request 模板生成系统服务请求
     * @return 服务 ID
     * @deprecated 事件被废弃，有服务直接编排出事件服务
     */
    @Deprecated
    GenerateEventServiceResponse generate(GenerateEventServiceRequest request);

    /**
     * 生成取号规则服务
     *
     * @param request 模板生成取号规则服务请求
     * @return 服务 ID
     */
    @Deprecated
    GenerateCodeRuleServiceResponse generate(GenerateCodeRuleServiceRequest request);

    /**
     * 根据模板生成系统服务
     *
     * @param request 模板生成系统服务请求
     * @return 统一 ID
     */

    GenerateSysServiceByTemplateResponse generate(GenerateSysServiceByTemplateRequest request);

}
