package io.terminus.trantor2.service.management.api.template.model.category;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
public enum ServiceTemplateCategory {
    BASIC("基础服务"),
    STATISTIC("统计服务"),
    TREE("树操作服务"),
    MASTER_DATA("主数据服务");

    private final String desc;

    ServiceTemplateCategory(String desc) {
        this.desc = desc;
    }

    public static List<ServiceTemplateCategoryVO> getAll() {
        return Arrays.stream(ServiceTemplateCategory.values()).map(
            category -> new ServiceTemplateCategoryVO(category.name(), category.getDesc())
        ).collect(Collectors.toList());
    }

}
