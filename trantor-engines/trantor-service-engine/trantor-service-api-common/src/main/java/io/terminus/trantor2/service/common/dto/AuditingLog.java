package io.terminus.trantor2.service.common.dto;

import io.terminus.trantor2.common.dto.PageInfo;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

/**
 * AuditingLog
 *
 * <AUTHOR> Created on 2023/4/26 11:14
 */
@Slf4j
@Data
@Builder
public class AuditingLog {

    private Type type;
    private PageInfo pageContent;
    private Long teamId;

    private String traceId;

    private String modelKey;
    private String modelName;

    private Map<String, Object> before;
    private Map<String, Object> after;

    private Long userId;
    private String userName;

    private LocalDateTime operateAt;

    public Long getId() {
        if (after != null) {
            return (Long) after.get("id");
        }
        if (before != null) {
            return (Long) before.get("id");
        }
        return null;
    }

    public Long getUpdateAt() {
        Long date = null;
        if (after != null) {
            date = getUpdateAt(after);
        }
        if (date == null && before != null) {
            date = getUpdateAt(before);
        }
        return date;
    }

    private Long getUpdateAt(Map<String, Object> data) {
        Object updatedAt = data.get("updatedAt");
        if (updatedAt != null) {
            if (updatedAt instanceof String) {
                Date date = toDate(updatedAt.toString());
                if (date != null) {
                    return date.getTime();
                }
            } else if (updatedAt instanceof Date) {
                return ((Date) updatedAt).getTime();
            } else if (NumberUtils.isCreatable(updatedAt.toString())) {
                return new BigDecimal(updatedAt.toString()).longValue();
            }
        }
        return null;
    }

    private static Date toDate(String value) {
        try {
            return DateUtils.parseDate(value, "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", "yyyy-MM-dd'T'HH:mm:ss.SSSXXX", "yyyy-MM-dd");
        } catch (ParseException e) {
            log.error("parse Data error,str:{}", value, e);
            return null;
        }
    }

    public String getMainFieldValue(String mainFieldAlias) {
        if (mainFieldAlias == null) {
            return null;
        }
        Object value = null;
        if (after != null) {
            value = after.get(mainFieldAlias);
        }
        if (value == null && before != null) {
            value = before.get(mainFieldAlias);
        }
        if (value != null) {
            if (value instanceof Map) {
                // 表明是关联对象，这返回Id值
                return String.valueOf(getId());
            } else {
                return value.toString();
            }

        } else {
            return null;
        }
    }

    public enum Type {
        ClickButton,
        Create,
        Update,
        Delete,
    }
}
