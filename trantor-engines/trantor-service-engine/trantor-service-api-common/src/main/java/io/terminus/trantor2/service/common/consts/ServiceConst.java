package io.terminus.trantor2.service.common.consts;

/**
 * <AUTHOR>
 * @since 2023-03-21 11:21:21
 */
public abstract class ServiceConst {


    public static final String TEAM_ID = "teamId";

    public static final String APP_ID = "appId";
    public static final String MODEL_KEY = "modelKey";
    public static final String RETURN_MODEL_KEY = "returnModelKey";
    public static final String PAGEABLE = "pageable";

    public static final String SORT_ORDERS = "sortOrders";
    public static final String CONDITION_GROUP = "conditionGroup";
    public static final String CONDITION_ITEMS = "conditionItems";
    public static final Long MAX_COUNT = 50000L;

    public static final String EVENT_SERVICE_TEMPLATE_KEY = "EventServiceTemplate";

    public static final String CODE_RULE_SERVICE_TEMPLATE_KEY = "CodeRuleServiceTemplate";

    public static final String BATCH_EVENT_SERVICE_TEMPLATE_KEY = "BatchEventServiceTemplate";

    public static final String REQUEST = "request";
    public static final String DATA = "data";
    public static final String PARAM = "param";

    public static final String REQUEST_ID = "requestId";
    public static final String SUCCESS = "success";
    public static final String RES_INFO = "info";
    public static final String RES_ERR = "err";

    public static final String SYS_PREFIX = "SYS_";
    public static final String IS_LEAF = "isLeaf";

    /**
     * 服务的接口Path
     */
    public static final String SERVICE_PATH_PATTERN = "/api/trantor/service/engine/execute/{serviceKey}";
}
