package io.terminus.trantor2.service.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.user.User;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * ServiceRequest
 *
 * <AUTHOR> Created on 2024/3/5 09:35
 */
@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public abstract class ServiceRequest extends PageContentRequest implements Serializable {
    private static final long serialVersionUID = -7257022870439623282L;

    private Map<String, Object> attributes;

    public abstract Object getParams();

    public void addAttribute(String key, Object value) {
        if (key != null && value != null) {
            if (Objects.isNull(attributes)) {
                attributes = new HashMap<>(1);
            }
            attributes.put(key, value);
        }
    }

    public Object getAttribute(String key) {
        if (Objects.isNull(attributes)) {
            return null;
        }
        return attributes.get(key);
    }

}
