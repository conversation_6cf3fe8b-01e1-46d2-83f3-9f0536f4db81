package io.terminus.trantor2.service.common.exception;

/**
 * ServiceErrorCode
 *
 * <AUTHOR> Created on 2023/4/21 14:45
 */
public interface ServiceErrorCode {

    String getCode();

    String getMessage();

    int getHttpStatus();

    static ServiceErrorCode create(String code) {
        return create(code, code, 500);
    }

    static ServiceErrorCode create(String code, String message) {
        return create(code, message, 500);
    }

    static ServiceErrorCode create(String code, String message, int httpCode) {
        return new ServiceErrorCode() {
            @Override
            public String getCode() {
                return code;
            }

            @Override
            public String getMessage() {
                return message;
            }

            @Override
            public int getHttpStatus() {
                return httpCode;
            }
        };
    }
}
