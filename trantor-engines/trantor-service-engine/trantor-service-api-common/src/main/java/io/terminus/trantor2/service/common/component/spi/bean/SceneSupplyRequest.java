package io.terminus.trantor2.service.common.component.spi.bean;

import io.terminus.trantor2.service.common.dto.ViewCondition;
import lombok.Data;

/**
 * SceneSupplyRequest
 *
 * <AUTHOR> Created on 2023/4/10 10:59
 */
@Data
public class SceneSupplyRequest {

    /**
     * TeamId
     */
    private Long teamId;

    /**
     * 场景id
     */
    private Long sceneId;

    /**
     * 场景id
     */
    private String sceneKey;

    /**
     * 视图key
     */
    private String viewKey;

    /**
     * 容器 key
     */
    private String containerKey;

    /**
     * 筛选条件
     */
    private ViewCondition viewCondition;

    /**
     * 模型Key
     */
    private String modelKey;
}
