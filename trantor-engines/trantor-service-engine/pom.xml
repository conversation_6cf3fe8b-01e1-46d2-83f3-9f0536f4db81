<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>trantor-engines</artifactId>
        <groupId>io.terminus.trantor2</groupId>
        <version>${revision}</version>
    </parent>
    <packaging>pom</packaging>
    <modules>
        <module>trantor-service-dsl</module>
        <module>trantor-service-api-common</module>
        <module>trantor-service-impl-common</module>
        <module>trantor-service-management</module>
        <module>trantor-service-runtime</module>
        <module>trantor-service-business</module>
        <module>trantor-service-report</module>
        <module>trantor-service-engine-impl</module>
    </modules>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>trantor-service-engine</artifactId>

</project>
