package io.terminus.trantor2.service.engine.impl.value.converter;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.engine.impl.value.ValueConverter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * RangeDateConverter
 *
 * <AUTHOR> Created on 2023/6/8 18:49
 */
public class RangeDateConverter implements ValueConverter {

    @Override
    public Object convert(Field field, Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof List) {
            return value;
        }

        if (value instanceof String) {
            final List<?> source;
            if (value.toString().startsWith("[") && value.toString().endsWith("]")) {
                source = JsonUtil.fromJson(value.toString(), ArrayList.class);
            } else {
                source = Arrays.stream(value.toString().split(",")).collect(Collectors.toList());
            }

            if (source != null) {
                return convertList(source);
            }
        }

        return value;
    }

    private List<?> convertList(List<?> source) {
        if (source.isEmpty()) {
            return source;
        }

        final List<Object> result = new ArrayList<>(source.size());
        for (Object o : source) {
            result.add(Long.parseLong(o.toString()));
        }

        return result;
    }
}
