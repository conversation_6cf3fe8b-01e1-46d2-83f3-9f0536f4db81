package io.terminus.trantor2.service.engine.ai.platform.model.vo;

import io.terminus.trantor2.service.engine.ai.platform.impl.AIProxyRestHandler;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class AiModelConfigVO {
    private String key;
    private String name;
    @Deprecated
    private List<String> models;
    private List<AIProxyRestHandler.AIProxyModel> modelNames;
}
