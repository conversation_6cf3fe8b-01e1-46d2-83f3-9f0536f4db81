package io.terminus.trantor2.service.engine.ai.agent.client;

import io.terminus.trantor2.service.engine.ai.agent.client.request.AgentRequest;
import io.terminus.trantor2.service.engine.ai.agent.client.request.MemoryExtractAndUpdateRequest;
import io.terminus.trantor2.service.engine.ai.agent.client.response.MemoryExtractAndUpdateResponse;
import io.terminus.trantor2.service.engine.ai.client.httpclient.StreamingFuture;
import io.terminus.trantor2.service.engine.ai.client.httpclient.StreamingHandler;
import jakarta.annotation.Nullable;

/**
 * AgentClient
 *
 * <AUTHOR> Created on 2025/3/18 10:50
 */
public interface AgentClient {

    String completion(AgentRequest request);

    StreamingFuture chatCompletion(AgentRequest request, StreamingHandler listener);

    @Nullable
    MemoryExtractAndUpdateResponse extractAndUpdateMemory(MemoryExtractAndUpdateRequest request);

}
