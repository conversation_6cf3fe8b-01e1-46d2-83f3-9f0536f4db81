package io.terminus.trantor2.service.engine.ai.core.message;

import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.service.engine.ai.core.memory.ChatMemory;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * MessageParser
 *
 * <AUTHOR> Created on 2025/4/12 12:24
 */
@Slf4j
@UtilityClass
public class MessageHelper {

    public <T> List<T> getHistoryMessages(ChatMemory chatMemory, Integer chatRound, Function<Message, T> mapper) {
        return getHistoryMessages(chatMemory, null, chatRound, mapper);
    }

    /**
     * 获取历史消息，按轮次组织，
     * <p>
     * 历史记录包含的消息类型：
     * 1. 用户类型的消息
     * 2. AI的TextContent消息
     * 3. AI的HandoffContent消息
     * 4. AI的ToolCallContent消息（如果有对应的ToolOutputContent，也一并返回）
     * 5. 其他类型的消息（如system）
     */
    public <T> List<T> getHistoryMessages(ChatMemory chatMemory,
                                          String startMessageId,
                                          Integer chatRound,
                                          Function<Message, T> mapper) {
        if (chatMemory == null || CollectionUtils.isEmpty(chatMemory.getMessages())) {
            return Collections.emptyList();
        }

        List<T> historyInputItems = new ArrayList<>();
        List<Message> messages = chatMemory.getMessages();
        Map<String, Message> overallMessageMap = new HashMap<>();

        // 构建消息索引和用户消息映射
        for (Message message : messages) {
            if (message.isOverall()) {
                overallMessageMap.put(message.getMessageId(), message);
            }
        }

        // 确定起始消息ID，然后往前找
        String currentMessageId = null;
        if (StringUtils.isNotBlank(startMessageId)) {
            // 如果指定了userMessageId，从它的前一个UserMessage开始
            Message startMessage = overallMessageMap.get(startMessageId);
            if (startMessage != null) {
                currentMessageId = startMessage.getPreviousMessageId();
            }
        } else {
            // 如果没有指定userMessageId，从最后一个UserMessage开始
            currentMessageId = chatMemory.getLastMessageId();
        }

        // 如果找不到起始消息，返回空列表
        if (StringUtils.isBlank(currentMessageId)) {
            return Collections.emptyList();
        }

        // 按轮次收集消息，每轮的问答作为一个整体
        List<List<Message>> messageRounds = new ArrayList<>(chatRound);
        int collectedRounds = 0;

        // Traverse messages using previousMessageId and parentId
        while (currentMessageId != null && collectedRounds < chatRound) {
            Message currentMessage = overallMessageMap.get(currentMessageId);
            if (currentMessage == null) {
                break;
            }

            // 如果当前消息是轮次消息，并且不是TextContent类型，则不参与轮次
            if (currentMessage instanceof AiMessage aiMessage && !(aiMessage.getContent() instanceof TextContent)) {
                currentMessageId = currentMessage.getPreviousMessageId();
                continue;
            }

            collectedRounds++;

            // 当前轮次的所有消息
            List<Message> currentRound = new ArrayList<>();
            // 添加问
            currentRound.add(currentMessage);

            Map<String, AiMessage> pendingToolCalls = new HashMap<>();

            // 按照顺序处理答
            for (Message message : messages) {
                if (message instanceof AiMessage aiMessage && currentMessageId.equals(aiMessage.getParentId())) {
                    Content content = aiMessage.getContent();

                    if (content instanceof ToolCallContent callContent) {
                        String callId = callContent.getCallId();
                        if (callId != null) {
                            pendingToolCalls.put(callId, aiMessage);
                        }
                    } else if (content instanceof ToolArgumentsContent argumentsContent) {
                        String callId = argumentsContent.getCallId();
                        if (callId != null && StringUtils.isNotBlank(argumentsContent.getArguments())) {
                            // 检查是否有对应的ToolCall需要更新参数
                            AiMessage toolCallMessage = pendingToolCalls.get(callId);
                            if (toolCallMessage != null) {
                                ToolCallContent callContent = (ToolCallContent) toolCallMessage.getContent();
                                callContent.setArguments(argumentsContent.getArguments());
                            }
                        }
                    } else if (content instanceof ToolOutputContent outputContent) {
                        String callId = outputContent.getCallId();
                        if (callId != null) {
                            AiMessage toolCallMessage = pendingToolCalls.get(callId);
                            if (toolCallMessage != null) {
                                // 成对出现时，先放ToolCall，再放ToolOutput
                                currentRound.add(toolCallMessage);
                                currentRound.add(aiMessage);
                            }
                        }
                    } else if (content instanceof TextContent) {
                        currentRound.add(aiMessage);
                    }
                }
            }

            // 将当前轮次添加到轮次列表
            messageRounds.add(currentRound);

            // Move to previous user message
            currentMessageId = currentMessage.getPreviousMessageId();
        }

        // 反转轮次顺序，并将每一轮的消息按顺序添加到结果中
        Collections.reverse(messageRounds);
        for (List<Message> round : messageRounds) {
            historyInputItems.addAll(round.stream().map(mapper).toList());
        }

        return historyInputItems;
    }

    public Map<String, Object> convertInputItem(Message message) {
        if (message instanceof AiMessage aiMessage) {
            if (aiMessage.isOverall()) {
                if (aiMessage.getContent() instanceof TextContent textContent) {
                    return MapUtil.of("role", ChatMessageRole.SYSTEM.value(), "content", textContent.getText());
                }
            }
        }
        return MapUtil.of("role", message.getRole().value(), "content", message.messageContent());
    }

    public static Message censorMessage(Message message) {
        if (message instanceof AiMessage aiMessage) {
            if (aiMessage.getContent() instanceof ToolOutputContent content) {
                // 对工具的输出进行截取
                if (content.invokeSuccess()) {
                    // copy一个新内容
                    ToolOutputContent newContent = new ToolOutputContent();
                    newContent.setCallId(content.getCallId());
                    newContent.setOutput(StringUtils.abbreviate(content.getOutput(), 500));
                    newContent.setInvokeStatus(content.getInvokeStatus());
                    newContent.setInvokeCostTime(content.getInvokeCostTime());
                    newContent.setVisible(content.isVisible());

                    // copy一个新消息
                    return MessageBuilder.newBuilder()
                            .messageId(aiMessage.getMessageId())
                            .parentId(aiMessage.getParentId())
                            .conversationId(aiMessage.getConversationId())
                            .previousMessageId(aiMessage.getPreviousMessageId())
                            .createdAt(aiMessage.getCreatedAt())
                            .meta(aiMessage.getMeta())
                            .build(newContent);
                }
            }
        }

        return message;
    }
}
