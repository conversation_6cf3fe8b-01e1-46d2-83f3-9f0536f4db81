package io.terminus.trantor2.service.engine.delegate;

import io.terminus.trantor2.doc.api.dto.PermissionDTO;
import io.terminus.trantor2.service.common.enums.ServiceType;
import io.terminus.trantor2.service.dsl.Definition;
import io.terminus.trantor2.service.dsl.properties.DefinitionProperties;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Map;

/**
 * Metadata
 *
 * <AUTHOR> Created on 2024/1/10 11:45
 */
@Getter
@RequiredArgsConstructor(staticName = "of")
public class Metadata {
    private final Key definitionKey;
    private final Definition<? extends DefinitionProperties> definition;
    private final ServiceType serviceType;
    private final boolean enabled;
    private final Map<String, PermissionDTO> permissions;

    public static Metadata of(Key definitionKey,
                              Definition<? extends DefinitionProperties> definition) {
        return Metadata.of(definitionKey, definition, ServiceType.PROGRAMMABLE);
    }

    public static Metadata of(Key definitionKey,
                              Definition<? extends DefinitionProperties> definition,
                              ServiceType serviceType) {
        return Metadata.of(definitionKey, definition, serviceType, true);
    }

    public static Metadata of(Key definitionKey,
                              Definition<? extends DefinitionProperties> definition,
                              ServiceType serviceType,
                              boolean enabled) {
        return Metadata.of(definitionKey, definition, serviceType, enabled, null);
    }
}
