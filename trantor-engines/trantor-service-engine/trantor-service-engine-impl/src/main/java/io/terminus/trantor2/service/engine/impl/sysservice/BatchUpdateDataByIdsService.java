package io.terminus.trantor2.service.engine.impl.sysservice;

import com.google.auto.service.AutoService;
import io.terminus.trantor2.service.common.consts.VariableKey;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.engine.impl.component.ModelDataProcessHelper;
import io.terminus.trantor2.service.engine.impl.component.bean.GenericModel;
import io.terminus.trantor2.service.engine.impl.component.bean.QueryModel;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * BatchUpdateDataByIdsService
 *
 * <AUTHOR> Created on 2025/5/29 16:35
 */
@AutoService(SystemService.class)
public class BatchUpdateDataByIdsService extends AbstractSystemServiceImpl {

    public static final String KEY = "SYS_BatchUpdateDataByIdsService";

    @Override
    public String getServiceKey() {
        return KEY;
    }

    @Override
    public String getServiceName() {
        return "(系统)根据IDS批量更新数据服务";
    }

    @Override
    protected void supplyDefinition(ServiceDefinition definition) {
        definition.addInput(MODEL_KEY_FIELD);
        definition.addInput(ARRAY_MODEL_REQUEST);
    }

    @Override
    protected void validateRequest(SystemServiceExecContext context) {
        validateRequest(VariableKey.MODEL_KEY, context.getModelKey());
        validateRequest("request", context.getRequestAsList(), data -> CollectionUtils.isNotEmpty((List<?>) data));
        for (Object data : context.getRequestAsList()) {
            @SuppressWarnings("unchecked")
            Map<String, Object> model = (Map<String, Object>) data;
            validateRequest(VariableKey.ID, model.get("id"), Objects::nonNull);
        }
    }


    @Override
    protected void execute(SystemServiceExecContext context) {
        List<GenericModel> genericModels = ModelDataProcessHelper.buildGenericModelList(
                context.getModelKey(), context, context.getRequestAsList());
        for (GenericModel genericModel : genericModels) {
            QueryModel queryModel = ModelDataProcessHelper.buildOrgZoneAndTenantQueryModel(context);
            initUpdateDataPermissions(KEY, context, queryModel, genericModel);
            context.getServiceEngine().getModelDataRepository().updateById(context, genericModel, queryModel);
        }
    }
}
