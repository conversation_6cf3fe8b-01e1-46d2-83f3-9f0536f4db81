package io.terminus.trantor2.service.engine.web;

import io.terminus.trantor2.service.common.consts.VariableKey;
import io.terminus.trantor2.service.engine.ai.client.httpclient.StreamingHandler;
import io.terminus.trantor2.service.engine.ai.core.chat.session.ChatSessionManager;
import io.terminus.trantor2.service.engine.ai.core.session.Session;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.executor.ServiceExecutor;
import io.terminus.trantor2.service.runtime.api.model.request.ServiceExecuteRequest;
import io.terminus.trantor2.service.runtime.api.request.ServiceExecuteAgentRequest;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.Duration;

/**
 * AgentExecuteWebBase
 *
 * <AUTHOR> Created on 2025/6/3 13:38
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/trantor/agent")
public class AgentExecuteWebBase {

    protected final ServiceExecutor serviceExecutor;
    protected final ChatSessionManager chatSessionManager;

    /**
     * 服务执行的统一方法
     *
     * @param agentKey 智能体Key
     * @param request  执行请求
     * @return 执行结果
     */
    @PostMapping(value = "/execute/{agentKey}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter execute(@PathVariable("agentKey") String agentKey,
                              @RequestBody ServiceExecuteAgentRequest request) {
        SseEmitter sseEmitter = new SseEmitter(Duration.ofHours(3).toMillis());
        StreamingHandler handler = new StreamingHandler() {
            @SneakyThrows
            @Override
            public void onMessage(String event, String message) {
                sseEmitter.send(SseEmitter.event().name(event).data(message));
            }

            @Override
            public void onComplete() {
                sseEmitter.complete();
            }

            @Override
            public void onError(Throwable error) {
                sseEmitter.completeWithError(error);
            }
        };

        final Session session;
        if (Boolean.TRUE.equals(request.getNoticeConnection())) {
            session = chatSessionManager.create(request.getSessionId(), agentKey, handler, true);
        } else {
            session = (Session) serviceExecutor.execute(agentKey, createArguments(request, handler));
        }

        sseEmitter.onError(err -> {
            log.warn("SSE error: {}", err.getMessage(), err);
            session.close();
        });

        sseEmitter.onTimeout(() -> {
            log.warn("SSE timeout");
            session.close();
        });

        session.sendPing();
        return sseEmitter;
    }

    protected Arguments createArguments(ServiceExecuteRequest request, StreamingHandler handler) {
        Arguments arguments = Arguments.of(request.getTeamId(), request.getParams());
        if (request.getAttributes() != null) {
            arguments.addAttribute(request.getAttributes());
        }
        if (request.isPageContentNotNull()) {
            arguments.addAttribute(VariableKey.PAGE_INFO, request.getPageInfo());
        }
        if (handler != null) {
            arguments.setStreamingHandler(handler);
        }
        return arguments;
    }
}
