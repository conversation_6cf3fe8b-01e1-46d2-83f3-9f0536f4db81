package io.terminus.trantor2.service.engine.impl.function.impl;

import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.function.ServiceFunction;
import org.apache.commons.lang3.StringUtils;

/**
 * ToJsonFunc
 *
 * <AUTHOR> Created on 2023/12/1 16:38
 */
@ServiceFunction(key = "LEN")
public class LenFunc extends FuncImpl {

    @Override
    public Object apply(Object[] objects, VariableContext context) {
        if (objects == null || objects.length == 0) {
            return 0;
        }
        return StringUtils.length((String) objects[0]);
    }
}
