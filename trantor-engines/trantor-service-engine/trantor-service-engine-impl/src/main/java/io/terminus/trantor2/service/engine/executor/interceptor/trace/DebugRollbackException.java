package io.terminus.trantor2.service.engine.executor.interceptor.trace;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * DebugRollbackException
 *
 * <AUTHOR> Created on 2023/9/5 21:51
 */
@Getter
@RequiredArgsConstructor
public class DebugRollbackException extends RuntimeException {
    private static final long serialVersionUID = -7173188386021335678L;

    private final Object data;
}
