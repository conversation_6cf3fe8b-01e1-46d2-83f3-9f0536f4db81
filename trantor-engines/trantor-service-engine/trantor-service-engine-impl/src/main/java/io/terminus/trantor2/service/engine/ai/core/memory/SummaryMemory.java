package io.terminus.trantor2.service.engine.ai.core.memory;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.io.Serial;

/**
 * SummaryMemory
 *
 * <AUTHOR> Created on 2025/8/23 11:27
 */
@Getter
@RequiredArgsConstructor
public class SummaryMemory implements Memory {

    @Serial
    private static final long serialVersionUID = -3764056860077309969L;
    /**
     * 总结的消息, 哪个消息被总结
     */
    private final String coveredMessageId;
    /**
     * 总结内容
     */
    private final String summary;

    @Override
    public String getId() {
        return coveredMessageId;
    }
}
