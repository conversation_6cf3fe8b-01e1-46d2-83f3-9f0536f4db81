package io.terminus.trantor2.service.engine.impl.value.mapper;

import io.terminus.trantor2.service.dsl.properties.ArrayAppendValue;
import io.terminus.trantor2.service.dsl.properties.AssignEntry;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.dsl.properties.Value;
import io.terminus.trantor2.service.engine.impl.context.SimpleVariableContext;
import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.value.ValueFactory;
import io.terminus.trantor2.service.engine.impl.value.ValueMapper;
import lombok.Getter;

import java.util.Map;

/**
 * ArrayAppendValueMapper
 *
 * <AUTHOR> Created on 2023/7/20 18:13
 */
public class ArrayAppendValueMapper implements ValueMapper {
    @Override
    public Object getValue(Field field, Value value, VariableContext context) {
        ArrayAppendValue appendValue = (ArrayAppendValue) value;

        if (appendValue.getAppendValue() != null) {
            return ValueFactory.getValue(field, appendValue.getAppendValue(), context);
        }

        // 当该字段不为空时，表明集合对象，需要add一条数据，则该方法返回一个对象，虽然很怪，但是这是需求
        if (appendValue.getSingleItemFieldMapping() != null) {
            return convertSingleValueForAdd(context, appendValue);
        }

        return null;
    }

    private static Map<String, Object> convertSingleValueForAdd(VariableContext context, ArrayAppendValue appendValue) {
        final ArrayContext arrayContext = new ArrayContext(context);

        for (AssignEntry assignValue : appendValue.getSingleItemFieldMapping()) {
            Object val = ValueFactory.getValue(Field.ofByFieldType(assignValue.getValue()), assignValue.getValue(), context);
            arrayContext.setVariable(assignValue.getField().getPaths(), val, assignValue.getOperator());
        }

        return arrayContext.getVariables();
    }

    @Getter
    private static class ArrayContext extends SimpleVariableContext {

        private final VariableContext context;

        public ArrayContext(VariableContext context) {
            super(context.getServiceEngine());
            this.context = context;
        }

        @Override
        public Long getTeamId() {
            return context.getTeamId();
        }
    }
}
