package io.terminus.trantor2.service.engine.impl.astservice.execution;

import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.service.common.utils.GraalvmScriptUtil;
import io.terminus.trantor2.service.common.utils.NashornScriptUtil;
import io.terminus.trantor2.service.dsl.ServiceElement;
import io.terminus.trantor2.service.dsl.properties.ScriptProperties;
import io.terminus.trantor2.service.engine.executor.interceptor.Contexts;
import io.terminus.trantor2.service.engine.impl.astservice.ExecutionContext;
import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.value.ValueFactory;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Map;

/**
 * ScriptExecution
 *
 * <AUTHOR> Created on 2023/2/18 19:07
 */
public class ScriptExecution extends AbstractExecution {

    public ScriptExecution(ServiceElement<?> currentNode) {
        super(currentNode);
    }

    @Override
    public void execute(ExecutionContext context) {
        final ScriptProperties props = getProps(ScriptProperties.class);
        final Object result = executeScript(props, context);
        // 处理结果
        if (CollectionUtils.isNotEmpty(props.getOutput())) {
            final String fieldName = props.getOutput().get(0).getFieldKey();
            setExecutionResult(context, MapUtil.of(fieldName, result), props.getOutputAssign());
        } else {
            Contexts.getServiceTrace().trace("无节点出参", "未配置节点出参结构");
        }
    }

    public static Object executeScript(ScriptProperties props, VariableContext context) {
        Contexts.getServiceTrace().trace("脚本", props.getScript());
        // 根据上下文信息填充脚本参数
        final Map<String, Object> inputValue = ValueFactory.getValue(props.getInputMapping(), context);
        Contexts.getServiceTrace().trace("脚本参数", inputValue);
        // 脚本执行
        final Object result;
        if ("graalvm".equals(props.getScriptEngine())) {
            result = GraalvmScriptUtil.executeScript(props.getScript(), inputValue);
        } else {
            result = NashornScriptUtil.executeScript(props.getScript(), inputValue);
        }
        Contexts.getServiceTrace().trace("脚本执行的结果", result);
        return result;
    }

}
