package io.terminus.trantor2.service.engine.impl.sysservice;

import com.google.common.collect.Lists;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.View;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.doc.api.dto.PermissionDTO;
import io.terminus.trantor2.meta.api.dto.ModuleInfo;
import io.terminus.trantor2.meta.context.MetaContext;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.system.SystemUser;
import io.terminus.trantor2.properties.ServiceProperties;
import io.terminus.trantor2.service.common.consts.ServiceConst;
import io.terminus.trantor2.service.common.consts.VariableKey;
import io.terminus.trantor2.service.common.enums.ServiceType;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.dsl.enums.FieldType;
import io.terminus.trantor2.service.dsl.enums.Propagation;
import io.terminus.trantor2.service.dsl.enums.RelationType;
import io.terminus.trantor2.service.dsl.properties.ArrayField;
import io.terminus.trantor2.service.dsl.properties.BaseField;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.dsl.properties.ModelField;
import io.terminus.trantor2.service.dsl.properties.ObjectField;
import io.terminus.trantor2.service.dsl.properties.RelatedModel;
import io.terminus.trantor2.service.dsl.properties.SelectField;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.delegate.Metadata;
import io.terminus.trantor2.service.engine.exception.ServiceExecuteException;
import io.terminus.trantor2.service.engine.impl.component.bean.GenericModel;
import io.terminus.trantor2.service.engine.impl.component.bean.QueryModel;
import io.terminus.trantor2.service.engine.impl.helper.PermissionHelper;
import io.terminus.trantor2.service.engine.impl.sysservice.bean.SystemServiceResult;
import io.terminus.trantor2.service.engine.loader.metadata.MetadataCache;
import io.terminus.trantor2.service.engine.loader.metadata.ServiceMetadataQuery;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

/**
 * AbstractSystemServiceImpl
 *
 * <AUTHOR> Created on 2023/4/21 16:19
 */
@SuppressWarnings("unchecked")
public abstract class AbstractSystemServiceImpl implements SystemService {

    protected static final Field MODEL_KEY_FIELD = new BaseField("modelKey", FieldType.Text, Boolean.TRUE);
    protected static final Field SELECT_FIELDS = new ArrayField("selectFields",
            new ObjectField("element",
                    new BaseField("field", FieldType.Text),
                    new ArrayField("selectFields", new ObjectField("element"))));
    protected static final Field FIELDS = new ArrayField("fields", new BaseField(FieldType.Text));

    protected static final Field AGGREGATE_REQUEST = new ObjectField("request",
            new BaseField("aggregateFunc", "聚合函数", FieldType.Text),
            new BaseField("aggregateField", "聚合字段", FieldType.Text),
            new ArrayField("groupByFields", "分组字段", new BaseField("element", FieldType.Text)),
            new ObjectField(FieldType.ConditionGroup, ServiceConst.CONDITION_GROUP),
            new ObjectField(FieldType.ConditionItems, ServiceConst.CONDITION_ITEMS));

    protected static final Field PAGEABLE = new ObjectField(FieldType.Pageable, "pageable", "pageable",
            Lists.newArrayList(
                    new BaseField("pageNo", FieldType.Number),
                    new BaseField("pageSize", FieldType.Number),
                    new ObjectField(FieldType.ConditionGroup, ServiceConst.CONDITION_GROUP),
                    new ObjectField(FieldType.ConditionItems, ServiceConst.CONDITION_ITEMS)));

    protected static final Field SORT_ORDERS = new ArrayField("sortOrders",
            new ObjectField("element",
                    new BaseField("fieldAlias", FieldType.Text),
                    new BaseField("sortType", FieldType.Text)));

    protected static final Field MODEL_REQUEST = new ModelField("request", new RelatedModel("${modelKey}"));
    protected static final Field ARRAY_MODEL_REQUEST = new ArrayField("request", new ModelField("element", new RelatedModel("${modelKey}")));
    protected static final Field ID_REQUEST = new ObjectField("request", new BaseField("id", FieldType.Number));
    protected static final Field IDS_REQUEST = new ObjectField("request", new ArrayField("ids", new BaseField("element", FieldType.Number)));
    protected static final Field CONDITION_REQUEST = new ObjectField("request",
            new ObjectField(FieldType.ConditionGroup, ServiceConst.CONDITION_GROUP),
            new ObjectField(FieldType.ConditionItems, ServiceConst.CONDITION_ITEMS));

    // 反查树条件
    protected static final Field REVERSE_CONSTRUCT_TREE_CONDITION_REQUEST = new ObjectField("request",
            new ObjectField(FieldType.ConditionGroup, ServiceConst.CONDITION_GROUP),
            new ObjectField(FieldType.ConditionItems, ServiceConst.CONDITION_ITEMS),
            new ObjectField(FieldType.ConditionGroup, "commonConditionGroup") // 这个添加在反查时，会添加在每个层级的查询条件里
    );

    protected static final Field UPDATE_DATA_CONDITION_REQUEST = new ObjectField("request",
            new ModelField("data", new RelatedModel("${modelKey}")),
            new ObjectField(FieldType.ConditionGroup, ServiceConst.CONDITION_GROUP),
            new ObjectField(FieldType.ConditionItems, ServiceConst.CONDITION_ITEMS));
    protected static final Field PAGEABLE_REQUEST = new ObjectField("request", PAGEABLE, SORT_ORDERS);
    protected static final Field ARRAY_CONDITION_REQUEST = new ArrayField("request",
            new ObjectField(FieldType.ConditionGroup, ServiceConst.CONDITION_GROUP));

    protected static final Field MODEL_DATA_OUTPUT = new ModelField("data", new RelatedModel("${modelKey}"));
    protected static final Field ARRAY_MODEL_DATA_OUTPUT = new ArrayField("data", new ModelField("element", new RelatedModel("${modelKey}")));
    protected static final Field OBJECT_DATA_OUTPUT = new ObjectField("data");
    protected static final Field PAGING_DATA_OUTPUT = new ObjectField(FieldType.Paging, "data", "data",
            Lists.newArrayList(
                    new BaseField("total", FieldType.Number),
                    new ArrayField("data", new ObjectField("element"))
            ));
    protected static final Field ARRAY_NUMBER_DATA_OUTPUT = new ArrayField("data", new BaseField("element", FieldType.Number));

    protected static final Field AGGREGATE_DATA_OUTPUT = new ArrayField("data",
            new ObjectField("element",
                    new BaseField("name", FieldType.Text),
                    new BaseField("aggregateValue", FieldType.Number)));

    @Resource
    private ServiceProperties serviceProperties;

    @Resource
    private ServiceMetadataQuery serviceMetadataQuery;

    protected boolean isNewStatusEnabled() {
        return serviceProperties.isNewStatusEnabled();
    }

    @Override
    public Metadata getMeta() {
        return MetadataCache.getMeta(getServiceKey(), (key) -> Metadata.of(Key.ofSystemServiceKey(key), createDefinition(), ServiceType.SYSTEM));
    }

    protected abstract void supplyDefinition(ServiceDefinition definition);

    protected void initFindDataPermissions(String serviceKey, SystemServiceExecContext context, QueryModel queryModel) {
        loadServicePermissionIfAbsent(serviceKey, context);
        // 如果有数据权限，植入数据权限查询条件
        PermissionHelper.fillQueryModelDataPermission(queryModel, context);
    }

    protected void initUpdateDataPermissions(String serviceKey, SystemServiceExecContext context,
                                             QueryModel queryModel, @NotNull GenericModel genericModel) {
        loadServicePermissionIfAbsent(serviceKey, context);
        // 如果有数据权限，植入数据权限查询条件
        PermissionHelper.fillDataPermissionAndThenEvaluateAuthorization(queryModel, genericModel, context);
    }

    protected void initDeleteDataPermissions(String serviceKey, SystemServiceExecContext context, QueryModel queryModel) {
        loadServicePermissionIfAbsent(serviceKey, context);
        // 如果有数据权限，植入数据权限查询条件
        PermissionHelper.fillDataPermissionAndThenEvaluateAuthorization(queryModel, context);  // 植入数据权限查询条件后验权
    }

    @Override
    public SystemServiceResult invoke(Arguments args) {
        SystemServiceExecContext context = new SystemServiceExecContext(args);
        try {
            validateRequest(context);
            execute(context);
            return context.getResult();
        } finally {
            context.close();
        }
    }

    protected abstract void execute(SystemServiceExecContext context);

    protected void validateRequest(SystemServiceExecContext context) {
        validateRequest(VariableKey.MODEL_KEY, context.getModelKey());
    }

    protected void validateRequest(String field, Object value) {
        if (value == null) {
            throw new ServiceExecuteException(ErrorType.SERVICE_EXECUTE_PARAM_NULL, new Object[]{field});
        }
    }

    protected void validateRequest(String field, Object value, Function<Object, Boolean> validator) {
        if (value == null || !validator.apply(value)) {
            throw new ServiceExecuteException(ErrorType.SERVICE_EXECUTE_PARAM_NULL, new Object[]{field});
        }
    }

    private ServiceDefinition createDefinition() {
        ServiceDefinition definition = new ServiceDefinition(getServiceKey(), getServiceName());
        definition.setTransactionPropagation(Propagation.REQUIRED);
        supplyDefinition(definition);
        return definition;
    }

    protected List<ModelField> getCrossModuleModels(SystemServiceExecContext context) {
        List<ModelField> result = new ArrayList<>();
        List<Field> fields = context.getServiceEngine()
                .getModelMetaQuery().getModelFields(context.getTeamId(), context.getModelKey());
        for (Field field : fields) {
            if (field instanceof ModelField) {
                ModelField modelField = (ModelField) field;
                if (!SystemUser.isSystemUser(modelField.getRelatedModel().getModelKey())
                        && isCrossModuleModel(modelField, context)) {
                    result.add(modelField);
                }
            }
        }

        return result;
    }

    private boolean isCrossModuleModel(ModelField modelField, SystemServiceExecContext context) {
        // 跨模块模型，只能是1对1不同步
        if (!modelField.getRelation().isSync() && RelationType.LINK.name().equals(modelField.getRelation().getRelationType())) {
            String teamCode = context.getServiceEngine().getTeamService().getTeamCode(context.getTeamId());
            return !MetaContext.isCurrentDeployModule(ModuleInfo.of(teamCode, KeyUtil.moduleKey(modelField.getRelatedModel().getModelKey())));
        }
        return false;
    }

    protected void fillCrossModuleModelValue(List<Map<String, Object>> result, QueryModel sourceQueryModel, SystemServiceExecContext context) {
        if (result != null && !result.isEmpty()) {
            List<ModelField> crossModuleModels = getCrossModuleModels(context);
            if (CollectionUtils.isEmpty(crossModuleModels)) {
                return;
            }
            for (Map<String, Object> date : result) {
                doFillCrossModuleModelValue(date, sourceQueryModel, context, crossModuleModels);
            }
        }
    }

    protected void fillCrossModuleModelValue(Map<String, Object> result, QueryModel sourceQueryModel, SystemServiceExecContext context) {
        if (result == null || result.isEmpty()) {
            return;
        }
        List<ModelField> crossModuleModels = getCrossModuleModels(context);
        if (CollectionUtils.isEmpty(crossModuleModels)) {
            return;
        }
        doFillCrossModuleModelValue(result, sourceQueryModel, context, crossModuleModels);
    }

    private void doFillCrossModuleModelValue(Map<String, Object> result,
                                             QueryModel sourceQueryModel,
                                             SystemServiceExecContext context,
                                             List<ModelField> crossModuleModels) {
        View view = TrantorContext.getCurrentView().orElse(null);
        // 这里清空view，防止跨模块查询时，查询条件，select等字段会被带到下游查询中
        TrantorContext.setCurrentView(null);
        for (ModelField modelField : crossModuleModels) {
            String field = modelField.getFieldKey();
            String modelKey = modelField.getRelatedModel().getModelKey();

            // 关联对象的ID, 不能为空
            Object relatedModelId = null;
            Map<String, Object> relatedValue = (Map<String, Object>) result.get(field);
            if (relatedValue != null && !relatedValue.isEmpty()) {
                relatedModelId = relatedValue.get(VariableKey.ID);
            }
            if (relatedModelId == null) {
                // 如果关联模型的ID值为空，则不去查询模型值
                continue;
            }

            // 获取Select
            List<SelectField> selectFieldList = CollectionUtils.emptyIfNull(sourceQueryModel.getSelectFields()).stream()
                    .filter(s -> s.getField().equals(field)).findFirst().map(SelectField::getSelectFields).orElse(null);
            if (CollectionUtils.isEmpty(selectFieldList)) {
                // 如果SELECT为空，则说明不需要下砖查询
                continue;
            }

            // 跨模块查询数据
            Map<String, Object> data = context.getServiceEngine().getSystemServiceWrapper()
                    .findById(context.getTeamId(), modelKey, relatedModelId, selectFieldList);
            if (data != null && !data.isEmpty()) {
                // 值回填到result中
                result.put(field, data);
            }
        }
        // 还原view
        TrantorContext.setCurrentView(view);
    }

    protected String getParentFieldKey(String modelKey, SystemServiceExecContext context) {
        DataStructNode dataStructNode = context.getServiceEngine().getModelMetaQuery()
                .getModelDataStruct(context.getTeamId(), modelKey);
        return dataStructNode.getTreeParentField();
    }

    private void loadServicePermissionIfAbsent(String serviceKey, SystemServiceExecContext context) {
        String permissionKey = context.getVariable(VariableKey.PERMISSION_KEY, String.class);
        String dataConditionKey = context.getVariable(VariableKey.DATA_CONDITION_KEY, String.class);
        if (Objects.nonNull(permissionKey) && Objects.nonNull(dataConditionKey)) {
            return;
        }
        // 上下文不存在，重新读取
        Metadata metadata = serviceMetadataQuery.findMeta(Key.of(
                TrantorContext.getTeamId(),
                serviceKey.contains(KeyUtil.MODULE_SEPARATOR) ? serviceKey : KeyUtil.newKeyUnderModule(context.getModuleKey(), serviceKey)
        ));
        Map<String, PermissionDTO> permissions;
        PermissionDTO permissionDTO;
        if (Objects.isNull(metadata)
                || MapUtils.isEmpty(permissions = metadata.getPermissions())
                || Objects.isNull(permissionDTO = permissions.get(context.getModelKey()))) {
            return;
        }
        if (Objects.nonNull(permissionDTO.getFunctionPermissionKey())) {
            context.setVariable(VariableKey.PERMISSION_KEY, permissionDTO.getFunctionPermissionKey());
        }
        if (Objects.nonNull(permissionDTO.getDataPermissionKey())) {
            context.setVariable(VariableKey.DATA_CONDITION_KEY, permissionDTO.getDataPermissionKey());
        }
    }
}
