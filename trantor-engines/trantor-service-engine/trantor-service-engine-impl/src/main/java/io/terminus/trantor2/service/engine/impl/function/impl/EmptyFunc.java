package io.terminus.trantor2.service.engine.impl.function.impl;

import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.function.ServiceFunction;
import org.apache.commons.lang3.StringUtils;

/**
 * EmptyFunc
 *
 * <AUTHOR> Created on 2024/7/12 20:39
 */
@ServiceFunction(key = "EMPTY")
public class EmptyFunc extends FuncImpl {
    @Override
    public Object apply(Object[] objects, VariableContext context) {
        return StringUtils.EMPTY;
    }
}
