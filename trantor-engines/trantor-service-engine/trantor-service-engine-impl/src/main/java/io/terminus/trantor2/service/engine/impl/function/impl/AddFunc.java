package io.terminus.trantor2.service.engine.impl.function.impl;

import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.function.ServiceFunction;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Objects;

/**
 * AddFunc
 *
 * <AUTHOR> Created on 2023/7/13 14:13
 */
@ServiceFunction(key = "ADD")
public class AddFunc extends FuncImpl {

    @Override
    public Object apply(Object[] objects, VariableContext context) {
        if (objects != null) {
            return Arrays.stream(objects)
                .filter(Objects::nonNull)
                .map(Object::toString)
                .map(BigDecimal::new)
                .reduce(BigDecimal::add)
                .orElse(null);
        }
        return null;
    }
}
