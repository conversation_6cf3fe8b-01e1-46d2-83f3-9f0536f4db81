package io.terminus.trantor2.service.engine.impl.expression.ast;


import io.terminus.trantor2.service.engine.impl.expression.ParserException;
import io.terminus.trantor2.service.engine.impl.expression.Scope;
import io.terminus.trantor2.service.engine.impl.expression.value.PrimFunc;
import io.terminus.trantor2.service.engine.impl.expression.value.Value;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 函数调用
 * <AUTHOR>
 * @date 2020/8/29
 */
public class CallNode extends AbstractNode {

    private final OperatorNode operator;

    private final List<AbstractNode> args;

    public CallNode(OperatorNode operator, List<AbstractNode> args) {
        this.operator = operator;
        this.args = args;
        this.start = operator.start;
        this.end = operator.end;
        this.row = operator.row;
        this.col = operator.col;
    }

    @Override
    public Value interp(Scope scope) {
        PrimFunc primFunc = scope.lookupFunc(operator.getOperator());
        if (Objects.isNull(primFunc)) {
            throw new ParserException("operator not supported, current operator:" + operator.getOperator(), operator);
        }
        List<Value> values = args.stream().map(abstractNode -> abstractNode.interp(scope)).collect(Collectors.toList());
        return primFunc.apply(values, operator);
    }

}
