package io.terminus.trantor2.service.engine.impl.astservice.execution;

import io.terminus.trantor2.service.dsl.ServiceElement;
import io.terminus.trantor2.service.engine.impl.astservice.ExecutionContext;

import java.util.List;

/**
 * ConditionExecution
 *
 * <AUTHOR> Created on 2024/3/10 03:12
 */
public abstract class ConditionExecution extends ChildrenExecution {
    public ConditionExecution(ServiceElement<?> currentNode, List<Execution> children) {
        super(currentNode, children);
    }

    @Override
    public void execute(ExecutionContext context) {
        if (isBranchTaken(context)) {
            context.branchTaken(this);
            super.execute(context);
        }
    }

    protected abstract boolean isBranchTaken(ExecutionContext context);
}
