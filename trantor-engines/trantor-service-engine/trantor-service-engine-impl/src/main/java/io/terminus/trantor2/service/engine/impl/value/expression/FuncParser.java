package io.terminus.trantor2.service.engine.impl.value.expression;

import com.google.common.collect.Lists;
import io.terminus.trantor2.service.engine.impl.value.expression.ast.ASTNode;
import io.terminus.trantor2.service.engine.impl.value.expression.ast.AstFunction;
import io.terminus.trantor2.service.engine.impl.value.expression.ast.NullAstParameter;
import io.terminus.trantor2.service.engine.impl.value.expression.ast.NumberAstParameter;
import io.terminus.trantor2.service.engine.impl.value.expression.ast.StringAstParameter;
import io.terminus.trantor2.service.engine.impl.value.expression.ast.VarAstParameter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Deque;
import java.util.LinkedList;
import java.util.List;

/**
 * FuncParser
 *
 * <AUTHOR> Created on 2023/8/1 21:25
 */
class FuncParser {

    static ASTNode parse(String expression) {
        if (expression.contains("(") && expression.endsWith(")")) {
            String functionName = expression.substring(0, expression.indexOf('('));
            if (StringUtils.isNotBlank(functionName) && isFunction(functionName)) {
                return parseExpression(expression);
            }
        }
        return new AstFunction("EXP", Lists.newArrayList(new StringAstParameter(expression)));
    }

    private static ASTNode parseExpression(String expression) {
        int openParenIndex = expression.indexOf('(');
        int closeParenIndex = expression.lastIndexOf(')');

        String functionName = expression.substring(0, openParenIndex);
        List<ASTNode> parameters;
        try {
            parameters = parseParameters(expression.substring(openParenIndex + 1, closeParenIndex));
        } catch (Exception ignored) {
            // 兼容历史数据中的函数解析，新的函数要去在''内如果有'需要转义(\\')
            // 如"SQL('select id from table where name = \\'(a)\\',bc\\' and code = \\'\\'')"
            parameters = parseParametersHistory(expression.substring(openParenIndex + 1, closeParenIndex));
        }

        return new AstFunction(functionName, parameters);
    }

    private static List<ASTNode> parseParameters(String params) {
        List<ASTNode> parameters = new ArrayList<>(4);
        StringBuilder current = new StringBuilder();
        Deque<Character> stack = new LinkedList<>();

        for (int i = 0; i < params.length(); i++) {
            char c = params.charAt(i);

            if (c == '\'' && (i == 0 || params.charAt(i - 1) != '\\')) {
                if (!stack.isEmpty() && stack.peek() == '\'') {
                    stack.pop();
                } else {
                    stack.push('\'');
                }
            }

            if (c == ',' && stack.isEmpty()) {
                ASTNode p = processParameter(current.toString().trim());
                if (p != null) {
                    parameters.add(p);
                }
                current.setLength(0);
            } else {
                if (c != '\\') current.append(c);
                if (c == '(' && (stack.isEmpty() || stack.peek() != '\'')) stack.push('(');
                if (c == ')' && (!stack.isEmpty() && stack.peek() == '(')) stack.pop();
            }
        }

        ASTNode p = processParameter(current.toString().trim());
        if (p != null) {
            parameters.add(p);
        }
        return parameters;
    }

    /**
     * 兼容历史数据
     * <p>
     * 该方法对于常量参数中包含'且不添加转义的情况无法正确解析
     */
    @Deprecated
    private static List<ASTNode> parseParametersHistory(String params) {
        List<ASTNode> parameters = new ArrayList<>();
        StringBuilder current = new StringBuilder();
        int level = 0;
        int level2 = 0;
        for (int i = 0; i < params.length(); i++) {
            char c = params.charAt(i);
            if (c == ',' && level == 0 && level2 % 2 == 0) {
                ASTNode p = processParameter(current.toString().trim());
                if (p != null) {
                    parameters.add(p);
                }
                level2 = 0;
                current = new StringBuilder();
            } else {
                current.append(c);
                if (c == '\'') level2++;
                if (c == '\'' && level2 % 2 == 0 & i < params.length() - 1 && params.charAt(i + 1) != ',') level2--;
                if (c == '(' && level2 % 2 == 0) level++;
                if (c == ')' && level2 % 2 == 0) level--;
            }
        }
        ASTNode p = processParameter(current.toString().trim());
        if (p != null) {
            parameters.add(p);
        }
        return parameters;
    }

    private static ASTNode processParameter(String param) {
        if (StringUtils.isBlank(param)) {
            return null;
        } else if ("null".equals(param)) {
            return NullAstParameter.NULL;
        } else if (param.startsWith("${") && param.endsWith("}")) {
            return new VarAstParameter(param);
        } else if (param.startsWith("'") && param.endsWith("'")) {
            return new StringAstParameter(param.substring(1, param.length() - 1));
        } else if (param.contains("(") && param.endsWith(")")) {
            return parseExpression(param);
        } else {
            return new NumberAstParameter(param);
        }
    }

    private static boolean isFunction(String str) {
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c != '_' && !Character.isUpperCase(c)) {
                return false;
            }
        }
        return true;
    }
}
