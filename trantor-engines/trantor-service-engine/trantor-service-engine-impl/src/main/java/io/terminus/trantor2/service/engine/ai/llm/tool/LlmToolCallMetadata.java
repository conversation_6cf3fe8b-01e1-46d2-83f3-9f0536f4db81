package io.terminus.trantor2.service.engine.ai.llm.tool;

import com.openai.models.chat.completions.ChatCompletionMessageToolCall;
import com.openai.models.chat.completions.ChatCompletionToolMessageParam;
import lombok.Data;

import java.io.Serializable;

@Data
public class LlmToolCallMetadata implements Serializable {
    private static final long serialVersionUID = 3723287805663367087L;

    private String key;
    private String name;
    private String moduleKey;
    private String callId;
    private String tooName;
    private String arguments;
    private String toolOutput;
    private boolean finalOutput = false;
    private ChatCompletionMessageToolCall toolCallArgumentsMessage;
    private ChatCompletionToolMessageParam toolCallOutputMessage;
}
