package io.terminus.trantor2.service.engine.ai.core.message;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * TaskEndContent
 *
 * <AUTHOR> Created on 2025/6/11 14:55
 */
@Setter
@Getter
@NoArgsConstructor
public class TaskEndContent implements Content {
    private static final long serialVersionUID = 1401253236644845576L;

    private ContentType type = ContentType.TASK_END;
    private AIProgressStatus status = AIProgressStatus.DONE;

    private String taskId;
    private String taskName;
    private String output;
    private Double invokeCostTime;
    // success， error
    private String invokeStatus;

    public static TaskEndContent ofError(String taskId, String taskName, String output) {
        TaskEndContent taskEndContent = new TaskEndContent();
        taskEndContent.setTaskId(taskId);
        taskEndContent.setTaskName(taskName);
        taskEndContent.setOutput(output);
        taskEndContent.setInvokeStatus("error");
        taskEndContent.setInvokeCostTime(0.0);
        return taskEndContent;
    }

}
