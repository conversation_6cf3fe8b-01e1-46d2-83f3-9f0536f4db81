package io.terminus.trantor2.service.engine.impl.astservice.trace;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.common.consts.VariableKey;
import io.terminus.trantor2.service.dsl.enums.VariableType;
import io.terminus.trantor2.service.engine.executor.interceptor.trace.TraceInfo;
import io.terminus.trantor2.service.engine.impl.astservice.ExecutionContext;
import io.terminus.trantor2.service.engine.impl.astservice.execution.Execution;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * ExecutionTraceInfo
 *
 * <AUTHOR> Created on 2023/9/4 13:43
 */
@EqualsAndHashCode(of = "nodeKey", callSuper = false)
@Getter
@Setter
public class ExecutionTraceInfo extends TraceInfo {

    private String nodeType;
    private String nodeKey;
    private String nodeName;

    private String variables;
    private String afterVariables;

    @JsonIgnore
    private ExecutionContext context;

    public ExecutionTraceInfo(Execution execution, ExecutionContext context) {
        super(execution.getCurrentServiceElement().getKey());
        this.nodeType = execution.getCurrentServiceElement().getClass().getSimpleName();
        this.nodeKey = execution.getCurrentServiceElement().getKey() + "##" + UUID.randomUUID();
        this.nodeName = execution.getCurrentServiceElement().getName();
        this.context = context;
    }

    @Override
    public void started() {
        super.started();
        this.variables = getVariables(context);
    }

    @Override
    public void completed() {
        super.completed();
        this.afterVariables = getVariables(context);
    }

    @Override
    public void failed(Throwable e) {
        super.failed(e);
        this.afterVariables = getVariables(context);
    }

    private String getVariables(ExecutionContext context) {
        Map<String, Object> variables = new HashMap<>(context.getVariables());
        // 排除掉一些变量
        variables.remove(VariableType.SYS.getKey());
        variables.remove(VariableKey.DEBUG_ENABLED);
        variables.remove(VariableKey.DEBUG_ROLLBACK);
        return JsonUtil.NON_INDENT.toJson(variables);
    }

    @Override
    protected void modifyWhenDuplicated(long repeatCount) {
        this.nodeKey = nodeKey + "_" + repeatCount;
        this.nodeName = nodeName + "(" + repeatCount + ")";
    }

    @Override
    protected void addPrefix(String prefix) {
        this.nodeKey = prefix + nodeKey;
    }
}
