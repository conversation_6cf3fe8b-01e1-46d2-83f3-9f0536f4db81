package io.terminus.trantor2.service.engine.ai.core.memory.history;

import com.google.common.collect.Lists;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.condition.ConditionItem;
import io.terminus.trantor2.condition.ConditionItems;
import io.terminus.trantor2.condition.enums.Operator;
import io.terminus.trantor2.service.dsl.enums.SortType;
import io.terminus.trantor2.service.dsl.properties.Pageable;
import io.terminus.trantor2.service.dsl.properties.SortOrder;
import io.terminus.trantor2.service.engine.ai.core.message.ChatMessageRole;
import io.terminus.trantor2.service.engine.impl.helper.Conditions;
import io.terminus.trantor2.service.engine.impl.sysservice.BatchCreateDataService;
import io.terminus.trantor2.service.engine.impl.sysservice.BatchDeleteDataByConditionService;
import io.terminus.trantor2.service.engine.impl.sysservice.BatchUpdateDataByConditionService;
import io.terminus.trantor2.service.engine.impl.sysservice.CreateDataService;
import io.terminus.trantor2.service.engine.impl.sysservice.bean.SystemServiceParameter;
import io.terminus.trantor2.service.engine.impl.sysservice.wrapper.SystemServiceWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * HistoryRepository
 *
 * <AUTHOR> Created on 2025/8/22 14:55
 */
@RequiredArgsConstructor
public class HistoryRepository {

    private static final String DEFAULT_CHAT_MEMORY_MODEL = "sys_common$chat_history_record";
    private static final String DEFAULT_CHAT_MESSAGE_MODEL = "sys_common$chat_history_message_record";

    private final SystemServiceWrapper systemServiceWrapper;
    private final String historyRecordModelKey;
    private final String historyMessageModelKey;

    private Long getTeamId() {
        return TrantorContext.getTeamId();
    }

    private String getHistoryMessageModelKey() {
        return StringUtils.defaultString(historyMessageModelKey, DEFAULT_CHAT_MESSAGE_MODEL);
    }

    private String getHistoryRecordModelKey() {
        return StringUtils.defaultString(historyRecordModelKey, DEFAULT_CHAT_MEMORY_MODEL);
    }

    public void saveHistory(HistoryRecord historyRecord) {
        systemServiceWrapper.execute(
                getTeamId(),
                CreateDataService.KEY,
                new SystemServiceParameter(getHistoryRecordModelKey(), JsonUtil.toMap(historyRecord)));
    }

    public void saveMessages(List<HistoryMessage> historyMessages) {
        systemServiceWrapper.execute(
                getTeamId(),
                BatchCreateDataService.KEY,
                new SystemServiceParameter(getHistoryMessageModelKey(),
                        historyMessages.stream().map(JsonUtil::toMap).collect(Collectors.toList())));
    }

    public HistoryRecord findHistoryByConversationId(String conversationId) {
        Map<String, Object> data = systemServiceWrapper.findOne(
                getTeamId(),
                getHistoryRecordModelKey(),
                MapUtil.of(HistoryRecord.Fields.conversationId, conversationId));
        if (MapUtils.isEmpty(data)) {
            return null;
        }
        return JsonUtil.convert(data, HistoryRecord.class);
    }

    public HistoryMessage findHistoryMessageByMessageId(String messageId) {
        Map<String, Object> data = systemServiceWrapper.findOne(
                getTeamId(),
                getHistoryMessageModelKey(),
                MapUtil.of(HistoryMessage.Fields.messageId, messageId));
        if (MapUtils.isEmpty(data)) {
            return null;
        }
        return JsonUtil.convert(data, HistoryMessage.class);
    }

    public void like(String messageId) {
        HistoryMessage data = findHistoryMessageByMessageId(messageId);
        if (data != null && data.getId() != null) {
            Map<String, Object> params = new HashMap<>();
            params.put(HistoryMessage.Fields.id, data.getId());
            params.put(HistoryMessage.Fields.likeFlag, 1);
            systemServiceWrapper.updateById(getTeamId(), getHistoryMessageModelKey(), params);
        }
    }

    public void dislike(String messageId) {
        HistoryMessage data = findHistoryMessageByMessageId(messageId);
        if (data != null && data.getId() != null) {
            Map<String, Object> params = new HashMap<>();
            params.put(HistoryMessage.Fields.id, data.getId());
            params.put(HistoryMessage.Fields.likeFlag, -1);
            systemServiceWrapper.updateById(getTeamId(), getHistoryMessageModelKey(), params);
        }
    }

    public Paging<Map<String, Object>> pageHistory(String agentKey, Long userId, String title, Integer pageNumber, Integer pageSize) {
        Map<String, ConditionItem> conditions = new HashMap<>();
        conditions.put(HistoryRecord.Fields.key, Conditions.createItem(Operator.EQ, agentKey));
        conditions.put(HistoryRecord.Fields.userId, Conditions.createItem(Operator.EQ, userId));
        if (StringUtils.isNotBlank(title)) {
            conditions.put(HistoryRecord.Fields.title, Conditions.createItem(Operator.CONTAINS, title));
        }

        Pageable pageable = Pageable.create(conditions, pageNumber, pageSize,
                new SortOrder(HistoryRecord.Fields.createdAt, SortType.DESC),
                new SortOrder(HistoryRecord.Fields.id, SortType.DESC));


        return systemServiceWrapper.paging(getTeamId(), getHistoryRecordModelKey(), pageable);
    }

    public Paging<Map<String, Object>> pageHistoryMessage(Pageable pageable) {
        return systemServiceWrapper.paging(getTeamId(), getHistoryMessageModelKey(), pageable);
    }

    public Paging<Map<String, Object>> pageHistoryMessage(String conversationId, String startMessageId, Integer pageSize) {
        List<HistoryMessage> historyMessages;
        if (startMessageId == null) {
            historyMessages = getHistoryMessageFormLast(conversationId, pageSize);
        } else {
            historyMessages = getHistoryMessageFromStartMessage(startMessageId, pageSize);
        }

        // 历史为空，则直接返回空列表
        if (CollectionUtils.isEmpty(historyMessages)) {
            return Paging.empty();
        }

        // historyMessages 根据id倒叙排序
        List<Map<String, Object>> result = historyMessages.stream()
                .sorted(Comparator.comparing(HistoryMessage::getCreatedAt, Comparator.reverseOrder())
                        .thenComparing(HistoryMessage::getId, Comparator.reverseOrder()))
                .map(JsonUtil::toMap).toList();

        // 查询每条消息的回答
        for (Map<String, Object> item : result) {
            String messageId = (String) item.get(HistoryMessage.Fields.messageId);
            String role = (String) item.get(HistoryMessage.Fields.role);

            if (StringUtils.isNotBlank(messageId) && ChatMessageRole.USER.value().equals(role)) {

                Map<String, ConditionItem> answerConditions = new HashMap<>();
                answerConditions.put(HistoryMessage.Fields.conversationId,
                        Conditions.createItem(Operator.EQ, conversationId));
                answerConditions.put(HistoryMessage.Fields.parentId,
                        Conditions.createItem(Operator.EQ, messageId));

                Pageable answerPageable = Pageable.create(answerConditions, 1, 1000,
                        new SortOrder(HistoryMessage.Fields.createdAt, SortType.ASC),
                        new SortOrder(HistoryMessage.Fields.id, SortType.ASC));

                Paging<Map<String, Object>> answerResult = systemServiceWrapper.paging(getTeamId(), getHistoryMessageModelKey(), answerPageable);

                item.put("#answers", answerResult.getData());
            }
        }

        return new Paging<>(0, result);
    }

    public void modifyHistoryTitle(String conversationId, String title) {
        SystemServiceParameter parameter = new SystemServiceParameter(getHistoryRecordModelKey());
        Map<String, Object> updateRequest = new HashMap<>();
        updateRequest.put("data", MapUtil.of(HistoryRecord.Fields.title, title));
        updateRequest.put("conditionItems",
                Conditions.create(HistoryRecord.Fields.conversationId, Operator.EQ, conversationId));
        parameter.setRequest(updateRequest);
        systemServiceWrapper.execute(getTeamId(), BatchUpdateDataByConditionService.KEY, parameter);
    }

    public void deleteHistory(String conversationId) {
        SystemServiceParameter parameter = new SystemServiceParameter(getHistoryRecordModelKey());
        parameter.setRequest(MapUtil.of("conditionItems",
                Conditions.create(HistoryRecord.Fields.conversationId, Operator.EQ, conversationId)));
        systemServiceWrapper.execute(getTeamId(), BatchDeleteDataByConditionService.KEY, parameter);
    }

    private HistoryMessage findLastHistoryMessageByConversationId(String conversationId) {
        Map<String, ConditionItem> conditions = new HashMap<>();
        conditions.put(HistoryMessage.Fields.conversationId,
                Conditions.createItem(Operator.EQ, conversationId));
        conditions.put(HistoryMessage.Fields.parentId,
                Conditions.createItem(Operator.IS_NULL, null));

        ConditionItems conditionItems = new ConditionItems();
        conditionItems.setConditions(conditions);

        Pageable pageable = new Pageable();
        pageable.setConditionItems(conditionItems);
        pageable.setPageNo(1);
        pageable.setPageSize(1);
        pageable.setSortOrders(Lists.newArrayList(
                new SortOrder(HistoryMessage.Fields.createdAt, SortType.DESC),
                new SortOrder(HistoryMessage.Fields.id, SortType.DESC)));

        Paging<Map<String, Object>> paging = systemServiceWrapper.paging(getTeamId(), getHistoryMessageModelKey(), pageable);
        if (paging == null || paging.getData() == null || paging.getData().isEmpty()) {
            return null;
        }

        Map<String, Object> data = paging.getData().get(0);
        return JsonUtil.convert(data, HistoryMessage.class);
    }

    private List<HistoryMessage> getHistoryMessageFromStartMessage(String startMessageId, Integer pageSize) {

        HistoryMessage startMessage = findHistoryMessageByMessageId(startMessageId);
        if (startMessage == null) {
            return Collections.emptyList();
        }

        List<HistoryMessage> historyMessages = new ArrayList<>();

        // 往前找5条用户信息
        for (int i = 0; i < pageSize; i++) {
            if (startMessage == null || startMessage.getPreviousMessageId() == null) {
                break;
            }
            startMessage = findHistoryMessageByMessageId(startMessage.getPreviousMessageId());
            if (startMessage != null) {
                historyMessages.add(startMessage);
            }
        }

        return historyMessages;
    }

    private List<HistoryMessage> getHistoryMessageFormLast(String conversationId, Integer pageSize) {

        // 第一条消息不一定是用户消息
        HistoryMessage startMessage = findLastHistoryMessageByConversationId(conversationId);
        if (startMessage == null) {
            return Collections.emptyList();
        }

        List<HistoryMessage> historyMessages = new ArrayList<>();
        historyMessages.add(startMessage);

        for (int i = 0; i < pageSize - 1; i++) {
            if (startMessage == null || startMessage.getPreviousMessageId() == null) {
                break;
            }
            startMessage = findHistoryMessageByMessageId(startMessage.getPreviousMessageId());
            if (startMessage != null) {
                historyMessages.add(startMessage);
            }
        }

        return historyMessages;
    }

    public void appendMetaById(String messageId, Map<String, Object> meta) {
        if (meta == null) {
            return;
        }
        HistoryMessage message = findHistoryMessageByMessageId(messageId);
        if (message != null) {
            Map<String, Object> newMeta = new HashMap<>(meta);
            // 把原来的也放进来
            if (StringUtils.isNotBlank(message.getMeta())) {
                Map<String, Object> existingMeta = JsonUtil.fromJson(message.getMeta(), JsonUtil.MAP_OBJ_TYPE_REF);
                if (existingMeta != null) {
                    newMeta.putAll(existingMeta);
                }
            }

            Map<String, Object> updateRequest = new HashMap<>(4);
            updateRequest.put("meta", JsonUtil.toNonIndentNonEmptyJson(newMeta));
            updateRequest.put("id", Conditions.create(HistoryRecord.Fields.id, Operator.EQ, message.getId()));
            systemServiceWrapper.updateById(getTeamId(), getHistoryMessageModelKey(), updateRequest);
        }
    }
}
