package io.terminus.trantor2.service.engine.impl.value.converter;

import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.engine.impl.value.ValueConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;
import java.util.Date;
import java.util.Objects;

/**
 * Date转换器， 转成 {@link Long} 类型，按照模型约定规范
 *
 * <AUTHOR> Created on 2023/2/20 16:20
 */
@Slf4j
public class DateConverter implements ValueConverter {

    @Override
    public Object convert(Field field, Object value) {
        if (Objects.isNull(value)) {
            return null;
        }

        if (value instanceof Long) {
            return value;
        }

        if (value instanceof Date) {
            return ((Date) value).getTime();
        }

        if (value instanceof String) {
            if (isDateTimestamp(value.toString())) {
                return Long.parseLong(value.toString());
            }
            Date date = toDate(value.toString());
            if (date != null) {
                return date.getTime();
            }
        }

        return value;
    }

    public static boolean isDateTimestamp(String input) {
        try {
            Long.parseLong(input);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    private static Date toDate(String value) {
        try {
            return DateUtils.parseDate(value, "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd", "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        } catch (ParseException e) {
            log.warn("parse Data error,str:{}", value, e);
            return null;
        }
    }
}
