package io.terminus.trantor2.service.engine.exception;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.service.common.exception.ServiceException;

/**
 * ServiceNotFoundException
 *
 * <AUTHOR> Created on 2023/2/17 00:28
 */
public class ServiceNotFoundException extends ServiceException {
    private static final long serialVersionUID = 2329753579003627555L;

    public ServiceNotFoundException(ErrorType type) {
        super(type);
    }

    public ServiceNotFoundException(ErrorType type, Object[] params) {
        super(type, params);
    }
}
