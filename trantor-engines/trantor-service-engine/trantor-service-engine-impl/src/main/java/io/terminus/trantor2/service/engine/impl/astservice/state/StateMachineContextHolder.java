package io.terminus.trantor2.service.engine.impl.astservice.state;

import java.util.Stack;

/**
 * StateMachineContextHolder
 *
 * <AUTHOR> Created on 2024/5/11 11:00
 */
public class StateMachineContextHolder {

    protected static ThreadLocal<Stack<StateMachineContext>> stateMachineContextThreadLocal = new ThreadLocal<>();

    public static StateMachineContext getServiceStateMachineContext() {
        Stack<StateMachineContext> stack = getStack(stateMachineContextThreadLocal);
        if (stack.isEmpty()) {
            return null;
        }
        return stack.peek();
    }

    public static void setServiceStateMachineContext(StateMachineContext serviceStateMachineContext) {
        getStack(stateMachineContextThreadLocal).push(serviceStateMachineContext);
    }

    public static void removeServiceStateMachineContext() {
         getStack(stateMachineContextThreadLocal).pop();
    }

    private static <T> Stack<T> getStack(ThreadLocal<Stack<T>> threadLocal) {
        Stack<T> stack = threadLocal.get();
        if (stack == null) {
            stack = new Stack<>();
            threadLocal.set(stack);
        }
        return stack;
    }
}
