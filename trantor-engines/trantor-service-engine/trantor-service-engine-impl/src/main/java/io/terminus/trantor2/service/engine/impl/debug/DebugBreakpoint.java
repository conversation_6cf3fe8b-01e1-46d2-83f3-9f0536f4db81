package io.terminus.trantor2.service.engine.impl.debug;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * DebugBreakpoint
 *
 * <AUTHOR> Created on 2024/11/13 17:13
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DebugBreakpoint implements Serializable {
    private static final long serialVersionUID = -2038701964732203656L;
    private DebugEndpoint endpoint;
    private List<Breakpoint> breakpoints;
}
