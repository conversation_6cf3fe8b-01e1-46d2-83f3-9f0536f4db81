package io.terminus.trantor2.service.engine.impl.context;

import io.terminus.trantor2.service.dsl.enums.VariableType;
import io.terminus.trantor2.service.engine.ServiceEngine;
import lombok.experimental.UtilityClass;

/**
 * VariableContextFactory
 *
 * <AUTHOR> Created on 2025/6/27 11:15
 */
@UtilityClass
public class SimpleVariableContextFactory {

    public static SimpleVariableContext create(ServiceEngine serviceEngine, Object request) {
        SimpleVariableContext context = new SimpleVariableContext(serviceEngine);
        if (request != null) {
            context.setVariable(VariableType.REQUEST.getKey(), request);
        }
        return context;
    }

    public static SimpleVariableContext create(ServiceEngine serviceEngine) {
        return create(serviceEngine, null);
    }
}
