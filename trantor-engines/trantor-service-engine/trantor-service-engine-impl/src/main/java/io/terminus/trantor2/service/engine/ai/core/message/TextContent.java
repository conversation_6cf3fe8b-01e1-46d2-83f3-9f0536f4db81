package io.terminus.trantor2.service.engine.ai.core.message;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * TextContent
 *
 * <AUTHOR> Created on 2025/4/12 12:54
 */
@Setter
@Getter
@NoArgsConstructor
public class TextContent implements DeltaContent, Content {
    private static final long serialVersionUID = 1995538292756936251L;

    private ContentType type;
    private AIProgressStatus status;
    private String text;

    public TextContent(ContentType type, AIProgressStatus status, String text) {
        this.type = type;
        this.status = status;
        this.text = text;
    }

    public void appendText(String text) {
        if (this.text == null) {
            this.text = text;
        } else {
            this.text += text;
        }
    }

    public void appendTextAndDone(String text) {
        this.appendText(text);
        this.setStatus(AIProgressStatus.DONE);
    }

    public static TextContent ofText(String text) {
        return new TextContent(ContentType.TEXT, AIProgressStatus.DONE, text);
    }

    public static TextContent ofText(String text, AIProgressStatus status) {
        return new TextContent(ContentType.TEXT, status, text);
    }
}
