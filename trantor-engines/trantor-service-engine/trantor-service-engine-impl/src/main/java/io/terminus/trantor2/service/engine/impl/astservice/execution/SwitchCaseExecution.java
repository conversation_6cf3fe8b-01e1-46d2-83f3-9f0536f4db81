package io.terminus.trantor2.service.engine.impl.astservice.execution;

import io.terminus.trantor2.service.dsl.ServiceElement;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.dsl.properties.SwitchCaseProperties;
import io.terminus.trantor2.service.engine.executor.interceptor.Contexts;
import io.terminus.trantor2.service.engine.impl.astservice.ExecutionContext;
import io.terminus.trantor2.service.engine.impl.value.ValueFactory;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * SwitchCaseExecution
 *
 * <AUTHOR> Created on 2023/11/21 14:48
 */
public class SwitchCaseExecution extends ConditionExecution {
    public SwitchCaseExecution(ServiceElement<?> currentNode, List<Execution> children) {
        super(currentNode, children);
    }

    @Override
    protected boolean isBranchTaken(ExecutionContext context) {
        SwitchCaseProperties properties = (SwitchCaseProperties) getProps();
        List<Object> caseValues = properties.getCaseValues().stream()
            .map(v -> ValueFactory.getValue(Field.ofByFieldType(v), v, context))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        Contexts.getServiceTrace().trace("case", caseValues);

        if (caseValues.isEmpty()) {
            return false;
        }

        Object switchValue = context.getBranchContext(this);

        return caseValues.stream().anyMatch(caseValue -> caseValue.equals(switchValue));
    }
}
