package io.terminus.trantor2.service.engine.impl.function.impl;

import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.function.ServiceFunction;

import java.math.BigDecimal;
import java.util.Arrays;

/**
 * MultiplyFunc
 *
 * <AUTHOR> Created on 2023/7/13 14:14
 */
@ServiceFunction(key = "MULTIPLY")
public class MultiplyFunc extends FuncImpl {
    @Override
    public Object apply(Object[] objects, VariableContext context) {
        if (objects != null) {
            return Arrays.stream(objects)
                .map(Object::toString)
                .map(BigDecimal::new)
                .reduce(BigDecimal::multiply)
                .orElse(null);
        }
        return null;
    }
}
