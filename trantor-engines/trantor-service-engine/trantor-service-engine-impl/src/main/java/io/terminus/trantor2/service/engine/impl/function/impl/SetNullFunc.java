package io.terminus.trantor2.service.engine.impl.function.impl;

import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.function.ServiceFunction;

/**
 * SetNullFunc
 *
 * <AUTHOR> Created on 2023/10/10 14:24
 */
@ServiceFunction(key = "NULL")
public class SetNullFunc extends FuncImpl {
    @Override
    public Object apply(Object[] objects, VariableContext context) {
        return null;
    }
}
