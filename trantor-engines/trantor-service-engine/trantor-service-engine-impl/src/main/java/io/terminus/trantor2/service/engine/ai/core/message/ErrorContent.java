package io.terminus.trantor2.service.engine.ai.core.message;

import io.terminus.common.api.exception.BusinessException;
import io.terminus.trantor2.common.exception.TrantorBizException;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * ErrorMessageContent
 *
 * <AUTHOR> Created on 2025/4/12 12:54
 */
@Setter
@Getter
@NoArgsConstructor
public class ErrorContent implements Content {
    private static final long serialVersionUID = 4523700020695294084L;

    private ContentType type = ContentType.ERROR;
    private AIProgressStatus status = AIProgressStatus.DONE;
    private String code = "500";
    private String message = "AI开小差了，请稍后重试";
    private String details;

    public ErrorContent(Throwable error) {
        if (error instanceof BusinessException be) {
            this.code = be.getErrorCode();
            this.message = be.getErrorMsg();
            if (be instanceof TrantorBizException tbe) {
                if (tbe.getInnerMsg() != null) {
                    this.details = tbe.getInnerMsg();
                }
            }
        } else {
            this.message = error.getMessage() != null ? error.getMessage() : "未知错误";
        }
    }
}
