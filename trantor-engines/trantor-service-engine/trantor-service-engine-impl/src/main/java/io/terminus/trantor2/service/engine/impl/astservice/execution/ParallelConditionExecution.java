package io.terminus.trantor2.service.engine.impl.astservice.execution;

import io.terminus.trantor2.service.dsl.ServiceElement;
import io.terminus.trantor2.service.dsl.properties.ParallelConditionProperties;
import io.terminus.trantor2.service.engine.executor.interceptor.Contexts;
import io.terminus.trantor2.service.engine.impl.astservice.ExecutionContext;
import io.terminus.trantor2.service.engine.impl.expression.ExpressionEvaluator;

import java.util.List;

/**
 * ParallelConditionExecution
 *
 * <AUTHOR> Created on 2024/6/24 13:54
 */
public class ParallelConditionExecution extends ConditionExecution {

    public ParallelConditionExecution(ServiceElement<?> serviceElement, List<Execution> children) {
        super(serviceElement, children);
    }

    @Override
    protected boolean isBranchTaken(ExecutionContext context) {
        ParallelConditionProperties props = (ParallelConditionProperties) getProps();
        if (props.getConditionGroup() == null || props.getConditionGroup().isEmpty()) {
            return true;
        } else {
            String expression = ExpressionEvaluator.parseExpression(props.getConditionGroup());
            Contexts.getServiceTrace().trace("表达式", expression);

            boolean flag = ExpressionEvaluator.evalBoolean(expression, context);
            Contexts.getServiceTrace().trace("表达式结果", String.valueOf(flag));

            return flag;
        }
    }
}
