package io.terminus.trantor2.service.engine.executor.rpc;

import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.service.engine.executor.rpc.request.ServiceRPCRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.net.URI;

/**
 * ServiceRPCProvider
 *
 * <AUTHOR> Created on 2023/9/15 17:46
 */
@FeignClient(name = "ServiceRPCProvider")
public interface ServiceRPCProvider {

    String RPC_API = "/api/internal/trantor/service/engine/execute";

    @PostMapping
    Response<Object> execute(URI uri, @RequestBody ServiceRPCRequest request);
}
