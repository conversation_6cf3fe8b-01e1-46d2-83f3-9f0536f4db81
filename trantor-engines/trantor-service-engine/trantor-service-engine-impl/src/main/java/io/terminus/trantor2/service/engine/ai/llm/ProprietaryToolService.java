package io.terminus.trantor2.service.engine.ai.llm;

import com.openai.models.chat.completions.ChatCompletionAssistantMessageParam;
import com.openai.models.chat.completions.ChatCompletionCreateParams;
import com.openai.models.chat.completions.ChatCompletionMessageParam;
import com.openai.models.chat.completions.ChatCompletionUserMessageParam;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.module.service.TeamService;
import io.terminus.trantor2.service.dsl.properties.SkillTool;
import io.terminus.trantor2.service.dsl.properties.ai.ProprietaryTool;
import io.terminus.trantor2.service.dsl.properties.ai.ToolDefinition;
import io.terminus.trantor2.service.engine.ai.core.memory.ChatMemory;
import io.terminus.trantor2.service.engine.ai.core.memory.MemoryManager;
import io.terminus.trantor2.service.engine.ai.core.message.AiMessage;
import io.terminus.trantor2.service.engine.ai.core.message.Content;
import io.terminus.trantor2.service.engine.ai.core.message.MessageHelper;
import io.terminus.trantor2.service.engine.ai.core.message.TextContent;
import io.terminus.trantor2.service.engine.ai.core.message.ToolArgumentsContent;
import io.terminus.trantor2.service.engine.ai.core.message.ToolCallContent;
import io.terminus.trantor2.service.engine.ai.core.message.ToolOutputContent;
import io.terminus.trantor2.service.engine.ai.core.message.UserMessage;
import io.terminus.trantor2.service.engine.ai.llm.util.ModelRetryUtil;
import io.terminus.trantor2.service.engine.ai.llm.util.ModelRetryUtil.ModelConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ProprietaryToolService {
    private static final String CACHE_KEY_MODULE_PREFIX = "agent:proprietary_tool";

    // 定义模型优先级重试配置，按优先级排序
    public ModelConfig[] modelConfigs = {
            ModelRetryUtil.createConfig("anthropic", "us.anthropic.claude-sonnet-4-20250514-v1:0", 8192, "主要模型"),
            ModelRetryUtil.createConfig("moonshotai", "kimi-k2", 4000, "降级Kimi-k2模型"),
            ModelRetryUtil.createConfig("qwen", "qwen3-coder-plus", 4000, "降级Qwen3-Coder-Plus模型")
    };

    @Lazy
    @Autowired
    private LlmClientService llmClientService;

    @Lazy
    @Autowired
    private MemoryManager memoryManager;

    @Autowired
    private TeamService teamService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public String build(String agentKey, String conversationId) {
        ChatMemory chatMemory = memoryManager.findChatMemory(conversationId);
        List<ChatCompletionMessageParam> historyMessages = buildHistoryMessages(chatMemory);

        // 使用带重试的工具定义生成
        String toolDefinitionJson = generateToolDefinition(historyMessages);
        storeProprietaryTools(agentKey, toolDefinitionJson);
        return toolDefinitionJson;
    }

    private List<ChatCompletionMessageParam> buildHistoryMessages(ChatMemory chatMemory) {
        // 查询历史记录
        return MessageHelper.getHistoryMessages(chatMemory, 10, message -> {
                    List<ChatCompletionMessageParam> messageParams = new ArrayList<>();
                    StringBuilder toolCallBuilder = new StringBuilder();
                    if (message instanceof UserMessage userMessage) {
                        for (Content content : userMessage.getContent()) {
                            if (content instanceof TextContent textContent) {
                                messageParams.add(ChatCompletionMessageParam.ofUser(ChatCompletionUserMessageParam.builder().content(textContent.getText()).build()));
                            }
                        }
                    } else if (message instanceof AiMessage aiMessage) {
                        if (aiMessage.getContent() instanceof TextContent textContent) {
                            messageParams.add(ChatCompletionMessageParam.ofAssistant(ChatCompletionAssistantMessageParam.builder().content(textContent.getText()).build()));
                        } else if (aiMessage.getContent() instanceof ToolCallContent toolCallContent) {
                            toolCallBuilder.append("call tool").append("\n");
                            toolCallBuilder.append(String.format("tool name: %s", toolCallContent.getName())).append("\n");
                        } else if (aiMessage.getContent() instanceof ToolArgumentsContent toolArgumentsContent) {
                            toolCallBuilder.append(String.format("tool arguments: %s", toolArgumentsContent.getArguments())).append("\n");
                        } else if (aiMessage.getContent() instanceof ToolOutputContent toolOutputContent) {
                            toolCallBuilder.append(String.format("tool output: %s", toolOutputContent.getOutput())).append("\n");
                            toolCallBuilder.append(String.format("call tool status: %s", toolOutputContent.getInvokeStatus())).append("\n");
                            messageParams.add(ChatCompletionMessageParam.ofAssistant(ChatCompletionAssistantMessageParam.builder().content(toolCallBuilder.toString()).build()));
                            toolCallBuilder.delete(0, toolCallBuilder.length());
                        }
                    } else {
                        messageParams.add(ChatCompletionMessageParam.ofAssistant(ChatCompletionAssistantMessageParam.builder().content(message.messageContent()).build()));
                    }

                    return messageParams;
                })
                .stream()
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    public String generateToolDefinition(List<ChatCompletionMessageParam> historyMessages) {
        return ModelRetryUtil.executeWithRetry(modelConfigs, modelConfig -> {
            ChatCompletionCreateParams.Builder builder = ChatCompletionCreateParams.builder()
                    .model(modelConfig.getModelName())
                    .addSystemMessage(getSysPrompt())
                    .maxCompletionTokens(modelConfig.getMaxCompletionTokens())
                    .temperature(0.1D);
            historyMessages.forEach(builder::addMessage);

            String output = llmClientService.chatCompletion(modelConfig.getProvider(), modelConfig.getModelName(), builder.build(), String.class);
            log.info("Generated result with model {} (maxTokens: {}): {}", modelConfig.getModelName(), modelConfig.getMaxCompletionTokens(), output);
            return censorOutput(output);
        });
    }

    /**
     * 对LLM输出的结果做补充处理，防止返回的并不是约定的JSON Schema
     *
     * @param output
     * @return
     */
    public String censorOutput(String output) {
        if (output.startsWith("```json")) {
            output = output.substring("```json".length());
            if (output.lastIndexOf("```") >= 0) {
                output = output.substring(0, output.lastIndexOf("```"));
            }
        }
        return output;
    }

    public String getCacheKeyPrefix(String teamCode, String agentKey) {
        if (StringUtils.isBlank(teamCode)) {
            log.error("teamCode is blank, use default value");
            teamCode = "default";
        }
        Long userId = TrantorContext.getCurrentUserId();
        if (Objects.isNull(userId)) {
            log.error("userId is null, use default value 1");
            userId = 1L;
        }
        return String.format("%s:%s:%s:%s", CACHE_KEY_MODULE_PREFIX, teamCode, agentKey, userId);
    }

    public List<ProprietaryTool> loadProprietaryTools(Long teamId, String agentKey) {
        String teamCode = teamService.getTeamCode(teamId);
        String cacheKeyPrefix = getCacheKeyPrefix(teamCode, agentKey);

        List<ProprietaryTool> tools = new ArrayList<>();

        // 从集合中获取所有工具名称
        String toolNamesKey = cacheKeyPrefix + ":tool_names";
        Set<Object> toolNames = redisTemplate.opsForSet().members(toolNamesKey);

        if (toolNames != null && !toolNames.isEmpty()) {
            // 构建完整的key列表
            List<String> fullKeys = toolNames.stream()
                    .map(toolName -> cacheKeyPrefix + ":" + toolName)
                    .collect(Collectors.toList());

            // 批量获取工具定义
            List<Object> toolDefinitions = redisTemplate.opsForValue().multiGet(fullKeys);
            if (Objects.nonNull(toolDefinitions)) {
                for (Object toolDef : toolDefinitions) {
                    if (toolDef instanceof ToolDefinition) {
                        ProprietaryTool tool = convertProprietaryTool((ToolDefinition) toolDef);
                        tools.add(tool);
                    }
                }
            }
        }

        return tools;
    }

    public void storeProprietaryTools(String agentKey, String toolDefinitionJson) {
        ToolDefinition toolDefinition = JsonUtil.fromJson(toolDefinitionJson, ToolDefinition.class);
        if (Objects.nonNull(toolDefinition)) {
            Long userId = TrantorContext.getCurrentUserId();
            String userName = TrantorContext.getContext().getCurrentUser().getUsername();
            toolDefinition.setMetadataInfo(userName, userId);

            String cacheKeyPrefix = getCacheKeyPrefix(TrantorContext.getTeamCode(), agentKey);
            String toolName = toolDefinition.getToolName();
            String cacheKey = String.format("%s:%s", cacheKeyPrefix, toolName);

            // 存储工具定义
            redisTemplate.opsForValue().set(cacheKey, toolDefinition);

            // 同时维护工具名称集合
            String toolNamesKey = cacheKeyPrefix + ":tool_names";
            redisTemplate.opsForSet().add(toolNamesKey, toolName);
        }
    }

    /**
     * 删除专有工具
     */
    public void deleteProprietaryTool(String agentKey, String toolName) {
        String cacheKeyPrefix = getCacheKeyPrefix(TrantorContext.getTeamCode(), agentKey);
        String cacheKey = String.format("%s:%s", cacheKeyPrefix, toolName);

        // 删除工具定义
        redisTemplate.delete(cacheKey);

        // 从集合中移除工具名称
        String toolNamesKey = cacheKeyPrefix + ":tool_names";
        redisTemplate.opsForSet().remove(toolNamesKey, toolName);
    }

    /**
     * 将ToolDefinition转换为SkillTool
     */
    private SkillTool convertProprietaryTool(ToolDefinition toolDefinition) {
        SkillTool skillTool = new SkillTool();
        skillTool.setName(toolDefinition.getToolName());
        skillTool.setDesc(toolDefinition.getDescription());
        skillTool.setKey(toolDefinition.getToolName()); // 使用工具名称作为key
        // 根据SkillTool的实际结构进行字段映射
        // 如果有更多字段需要映射，请在这里添加
        return skillTool;
    }

    public String getSysPrompt() {
        return "## 角色设定\n" +
                "你是一个**企业数智员工工具生成器（Tool Generator for Enterprise AI Assistant）**。\n" +
                "你擅长把业务上下文抽象为可注册的工具（ToolCall 元数据）和可执行的实现（Python 代码），用于在企业数智员工平台中自动注册与运行。\n" +
                "这些工具的具体实现由后端提供，但接口名与作用我会在业务上下文中说明。\n" +
                "\n" +
                "## 背景说明\n" +
                "我的系统中有若干底层可调用工具/接口（例如：文件解析器、OCR、客户查询、库存查询、供应商下单 API、数据库写入、邮件发送等）。这些工具都以函数或 HTTP 接口的形式存在。\n" +
                "目标是：当有新的业务场景出现时，你需要根据上下文**自动生成一个“专有技能工具”**，生成的内容需要包含：\n" +
                "- 工具注册信息（tool name、JSON Schema 参数定义）\n" +
                "- 工具实现（Python 源码，作为工具的实现文件）\n" +
                "\n" +
                "## 任务目标\n" +
                "1. 分析我提供的 **业务上下文**，分析该场景中涉及哪些工具，以及工具的职责，工具之间的依赖关系和调用顺序，工具的用途，工具的输入参数和输出结果\n" +
                "2. 抽象出该场景需要的工具职责与入参（生成 `tool_schema`）。\n" +
                "3. 生成一个 **单一 JSON 对象**（格式见“输出约定”），包括工具元数据与 Python 实现（base64 编码）。\n" +
                "4. Python 代码的实现需要严格按照**（“代码生成约定”）**\n" +
                "5. 输出必须可被 `json.loads()` 解析（**仅输出 JSON，不允许任何额外自然语言**）。\n" +
                "6. 严格禁止在 JSON 之外输出任何文字；只返回 JSON。\n" +
                "\n" +
                "---\n" +
                "\n" +
                "## 输出约定（**强制**）\n" +
                "必须 **只返回一个 JSON 对象**，且结构严格按照如下示例（必备字段）：\n" +
                "\n" +
                "```json\n" +
                "{\n" +
                "  \"tool_name\": \"string\",\n" +
                "  \"description\": \"一句话描述工具功能\",\n" +
                "  \"tool_schema\": { /* JSON Schema */ },\n" +
                "  \"implementation\": {\n" +
                "    \"language\": \"python\",\n" +
                "    \"encoding\": \"base64\",\n" +
                "    \"code\": \"base64-utf8-string\",\n" +
                "    \"entrypoint\": \"<tool_name>\",\n" +
                "    \"requirements\": [\"requests>=2.0\"]\n" +
                "  },\n" +
                "  \"metadata\": { \"version\": \"1.0\", \"author\": \"llm\" }\n" +
                "}\n" +
                "```\n" +
                "\n" +
                "---\n" +
                "\n" +
                "## 代码生成约定（**强制**）\n" +
                "1. 需用根据逻辑的需要，添加 if/else、循环、异常处理等控制语句\n" +
                "2. 在逻辑中串联调用步骤，包含必要的条件语句，循环语句和异常处理\n" +
                "3. 输出的 Python 代码一份完整的、可运行的 Python 代码，作为“专有技能工具”\n" +
                "4. 代码逻辑需要保留清晰的注释，说明每一步对应的业务含义\n" +
                "5. 每个工具调用应封装为独立函数，方便替换或扩展\n" +
                "\n" +
                "---\n" +
                "\n" +
                "**重要说明：**\n" +
                "- `implementation.code` 必须使用 **base64 编码的 UTF-8 源码字符串**（单行，无换行，避免 JSON 转义问题）；且`implementation.encoding` 必须为 \"base64\"。\n" +
                "- `implementation.entrypoint` **必须等于** tool_name，且为模块的顶级函数名，且函数签名（参数名/必填项）**需与 `tool_schema` 对齐或兼容**。\n" +
                "- 入口函数签名为 `def <tool_name>(params: dict) -> dict:`（允许后端把参数字典直接传入）。如果使用逐参数签名，则**必须**与 tool_schema.properties 精确对应。\n" +
                "- 不允许任何自然语言在 JSON 之外（包括注释性文字、解释、Markdown 等）。";
    }

    public String getBuildProprietaryToolPrompt() {
        return "";
    }
}
