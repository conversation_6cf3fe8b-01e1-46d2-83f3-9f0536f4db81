package io.terminus.trantor2.service.engine.loader.listener;

import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.event.MetaChangeEvent;
import io.terminus.trantor2.module.service.TeamService;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.loader.ServiceFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * ServiceChangedSpringListener
 *
 * <AUTHOR> Created on 2024/1/31 14:19
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ServiceChangedSpringListener {

    private final TeamService teamService;
    private final ServiceFactory factory;

    @Order(5)
    @EventListener(condition = "#event.oldMetas != null " +
            "&& !T(java.util.Arrays).asList(#event.oldMetas.?[type == 'ServiceDefinition']).isEmpty()")
    public void handleMetaChange(MetaChangeEvent event) {
        List<MetaChangeEvent.MetaId> oldMetas = event.getOldMetas();
        if (CollectionUtils.isEmpty(oldMetas)) {
            return;
        }

        if (log.isDebugEnabled()) {
            log.info("received meta change event for service cache update: {}", event.getOldMetas());
        }

        List<String> keys = oldMetas.stream()
            .filter(t -> MetaType.ServiceDefinition.name().equals(t.getType()))
            .map(MetaChangeEvent.MetaId::getKey)
            .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(keys)) {
            Long teamId = teamService.getTeamIdByCode(event.getTeamCode());
            keys.forEach(key -> factory.remove(Key.of(teamId, key)));
        }
    }
}
