package io.terminus.trantor2.service.engine.impl.function.impl;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.service.common.exception.ServiceException;
import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.function.ServiceFunction;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * ArrayMapFunc
 *
 * <AUTHOR> Created on 2023/11/9 15:16
 */
@SuppressWarnings("unchecked")
@ServiceFunction(key = "ARRAY_FIND")
public class ArrayFindFunc extends FuncImpl {

    @Override
    public Object apply(Object[] objects, VariableContext context) {
        if (objects == null || objects.length < 2) {
            throw new ServiceException(ErrorType.SERVICE_ERROR, "“ARRAY_FIND”函数参数个数不正确");
        }

        Object var = objects[0];
        if (var == null) {
            return new ArrayList<>(0);
        }

        if (!(var instanceof Collection)) {
            throw new ServiceException(ErrorType.SERVICE_ERROR, "“ARRAY_FIND”函数仅支持处理集合变量");
        }

        Collection<Object> array = (Collection<Object>) var;
        if (CollectionUtils.isEmpty(array)) {
            return null;
        }

        Object indexObj = objects[1];
        if (!isInteger(indexObj)) {
            throw new ServiceException(ErrorType.SERVICE_ERROR, "“ARRAY_FIND”函数下标参数不是整数");
        }

        int index = Integer.parseInt(indexObj.toString());
        if (array instanceof List) {
            return ((List<Object>) array).get(index);
        } else {
            return (new ArrayList<>(array)).get(index);
        }
    }
}
