package io.terminus.trantor2.service.engine.ai.core.message;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * MessageType
 *
 * <AUTHOR> Created on 2025/4/12 12:31
 */
@Getter
@AllArgsConstructor
public enum ContentType {

    @JsonProperty("text")
    TEXT(TextContent.class),

    @JsonProperty("reasoning")
    REASONING(ReasoningContent.class),

    @JsonProperty("plan")
    PLAN(TextContent.class),

    @JsonProperty("function_call")
    FUNCTION_CALL(ToolCallContent.class),

    @JsonProperty("function_call_arguments")
    FUNCTION_CALL_ARGUMENTS(ToolArgumentsContent.class),

    @JsonProperty("function_call_output")
    FUNCTION_CALL_OUTPUT(ToolOutputContent.class),

    @JsonProperty("agent_handoff")
    AGENT_HANDOFF(HandoffContent.class),

    @JsonProperty("error")
    ERROR(ErrorContent.class),

    @JsonProperty("tokens")
    TOKENS(TokenContent.class),

    @JsonProperty("attachments")
    ATTACHMENTS(AttachmentsContent.class),

    @JsonProperty("callback")
    CALLBACK(CallbackContent.class),

    @JsonProperty("notify")
    NOTIFY(NotifyContent.class),

    @JsonProperty("task_start")
    TASK_START(TaskStartContent.class),

    @JsonProperty("task_end")
    TASK_END(TaskEndContent.class),

    ;

    private final Class<? extends Content> contentClazz;

    public static ContentType from(String name) {
        return Arrays.stream(ContentType.values())
                .filter(contentType -> contentType.name().equalsIgnoreCase(name))
                .findFirst()
                .orElse(null);
    }
}
