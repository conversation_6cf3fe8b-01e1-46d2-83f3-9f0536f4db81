package io.terminus.trantor2.service.engine.impl.spi.event;

import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.service.common.consts.ServiceConst;
import io.terminus.trantor2.service.common.consts.VariableKey;
import io.terminus.trantor2.service.common.spi.annotation.ServiceSPI;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.common.utils.LongUtil;
import io.terminus.trantor2.service.engine.impl.component.ModelMetaQuery;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/5/9 2:46 PM
 */
@Component
public class EventParamReverseConverterSpi extends AbstractParamConverter {

    private static final String EVENT_PARAM_CONVERTER_SPI = "EVENT_PARAM_REVERSE_CONVERTER";

    public EventParamReverseConverterSpi(ModelMetaQuery modelMetaQuery) {
        super(modelMetaQuery);
    }

    @Override
    @ServiceSPI(key = EVENT_PARAM_CONVERTER_SPI)
    public Map<String, Object> convert(Map<String, Object> request) {
        Long teamId = LongUtil.valueOf(request.get(ServiceConst.TEAM_ID));
        String returnModelKey = (String) request.get(ServiceConst.RETURN_MODEL_KEY);
        Object param = request.get(ServiceConst.PARAM);
        if (Objects.nonNull(returnModelKey)) {
            List<Field> fields = modelMetaQuery.getModelFields(teamId, returnModelKey, false);
            convert(teamId, fields, param);
        }
        return request;
    }

    public Object convert(Object data, String modelKey, Long teamId) {
        List<Field> fields = modelMetaQuery.getModelFields(teamId, modelKey, false);
        convert(teamId, fields, data);
        return data;
    }

    /**
     * 后端返回给前端时，需要将一对一不同步关联关系字段由 Id 转成 Map 传递给前端
     *
     * @param data  当前模型数据 Map
     * @param field 当前关联关系字段
     */
    @Override
    void convertFieldValue(Map<String, Object> data, Field field) {
        Object obj = data.get(field.getFieldKey());
        if (!(obj instanceof Map)) {
            data.put(field.getFieldKey(), MapUtil.of(VariableKey.ID, obj));
        }
    }

}
