package io.terminus.trantor2.service.engine.loader.metadata;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * Agent i18n处理工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class AgentI18nProcessor {
    private static final String FIELD_GREETINGS = "greetings";
    private static final String FIELD_USER_QUESTIONS = "userQuestions";
    private static final String FIELD_SKILL_TOOLS = "skillTools";
    private static final String FIELD_NAME = "name";

    /**
     * 从Agent的ObjectNode结构中收集需要国际化的键
     */
    public static Set<String> collectI18nKeys(ObjectNode agentPropsNode) {
        Set<String> i18nKeys = new HashSet<>();

        var greetingsNode = agentPropsNode.get(FIELD_GREETINGS);
        if (greetingsNode != null && greetingsNode.isTextual()) {
            String greetings = greetingsNode.asText();
            if (StringUtils.isNotBlank(greetings)) {
                i18nKeys.add(greetings);
            }
        }

        var userQuestionsNode = agentPropsNode.get(FIELD_USER_QUESTIONS);
        if (userQuestionsNode != null && userQuestionsNode.isArray()) {
            userQuestionsNode.forEach(question -> {
                if (question.isTextual()) {
                    String questionText = question.asText();
                    if (StringUtils.isNotBlank(questionText)) {
                        i18nKeys.add(questionText);
                    }
                }
            });
        }

        var skillToolsNode = agentPropsNode.get(FIELD_SKILL_TOOLS);
        if (skillToolsNode != null && skillToolsNode.isArray()) {
            skillToolsNode.forEach(tool -> {
                if (tool.has(FIELD_NAME)) {
                    var nameNode = tool.get(FIELD_NAME);
                    if (nameNode != null && nameNode.isTextual()) {
                        String toolName = nameNode.asText();
                        if (StringUtils.isNotBlank(toolName)) {
                            i18nKeys.add(toolName);
                        }
                    }
                }
            });
        }

        return i18nKeys;
    }

    /**
     * 对Agent的ObjectNode结构应用国际化
     */
    public static void applyI18n(ObjectNode agentPropsNode, Map<String, String> allI18nResources) {
        var greetingsNode = agentPropsNode.get(FIELD_GREETINGS);
        if (greetingsNode != null && greetingsNode.isTextual()) {
            String greetings = greetingsNode.asText();
            if (StringUtils.isNotBlank(greetings)) {
                String i18nGreetings = allI18nResources.getOrDefault(greetings, greetings);
                agentPropsNode.put(FIELD_GREETINGS, i18nGreetings);
            }
        }

        var userQuestionsNode = agentPropsNode.get(FIELD_USER_QUESTIONS);
        if (userQuestionsNode != null && userQuestionsNode.isArray()) {
            ArrayNode userQuestionsArray = (ArrayNode) userQuestionsNode;
            for (int i = 0; i < userQuestionsArray.size(); i++) {
                var questionNode = userQuestionsArray.get(i);
                if (questionNode.isTextual()) {
                    String question = questionNode.asText();
                    if (StringUtils.isNotBlank(question)) {
                        String i18nQuestion = allI18nResources.getOrDefault(question, question);
                        userQuestionsArray.set(i, userQuestionsArray.textNode(i18nQuestion));
                    }
                }
            }
        }

        var skillToolsNode = agentPropsNode.get(FIELD_SKILL_TOOLS);
        if (skillToolsNode != null && skillToolsNode.isArray()) {
            skillToolsNode.forEach(tool -> {
                if (tool.has(FIELD_NAME)) {
                    var nameNode = tool.get(FIELD_NAME);
                    if (nameNode != null && nameNode.isTextual()) {
                        String toolName = nameNode.asText();
                        if (StringUtils.isNotBlank(toolName)) {
                            String i18nToolName = allI18nResources.getOrDefault(toolName, toolName);
                            ((ObjectNode) tool).put(FIELD_NAME, i18nToolName);
                        }
                    }
                }
            });
        }
    }

    /**
     * 高性能版本：合并收集和应用逻辑，一次遍历完成所有操作
     */
    public static Set<String> collectAndApplyI18n(ObjectNode agentPropsNode, Map<String, String> allI18nResources) {
        Set<String> i18nKeys = new HashSet<>();

        var greetingsNode = agentPropsNode.get(FIELD_GREETINGS);
        if (greetingsNode != null && greetingsNode.isTextual()) {
            String greetings = greetingsNode.asText();
            if (StringUtils.isNotBlank(greetings)) {
                i18nKeys.add(greetings);
                String i18nGreetings = allI18nResources.getOrDefault(greetings, greetings);
                agentPropsNode.put(FIELD_GREETINGS, i18nGreetings);
            }
        }

        var userQuestionsNode = agentPropsNode.get(FIELD_USER_QUESTIONS);
        if (userQuestionsNode != null && userQuestionsNode.isArray()) {
            ArrayNode userQuestionsArray = (ArrayNode) userQuestionsNode;
            for (int i = 0; i < userQuestionsArray.size(); i++) {
                var questionNode = userQuestionsArray.get(i);
                if (questionNode.isTextual()) {
                    String question = questionNode.asText();
                    if (StringUtils.isNotBlank(question)) {
                        i18nKeys.add(question);
                        String i18nQuestion = allI18nResources.getOrDefault(question, question);
                        userQuestionsArray.set(i, userQuestionsArray.textNode(i18nQuestion));
                    }
                }
            }
        }

        var skillToolsNode = agentPropsNode.get(FIELD_SKILL_TOOLS);
        if (skillToolsNode != null && skillToolsNode.isArray()) {
            skillToolsNode.forEach(tool -> {
                if (tool.has(FIELD_NAME)) {
                    var nameNode = tool.get(FIELD_NAME);
                    if (nameNode != null && nameNode.isTextual()) {
                        String toolName = nameNode.asText();
                        if (StringUtils.isNotBlank(toolName)) {
                            i18nKeys.add(toolName);
                            String i18nToolName = allI18nResources.getOrDefault(toolName, toolName);
                            ((ObjectNode) tool).put(FIELD_NAME, i18nToolName);
                        }
                    }
                }
            });
        }

        return i18nKeys;
    }
}