package io.terminus.trantor2.service.engine.impl.function.impl;

import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.function.ServiceFunction;

import java.math.BigDecimal;
import java.util.Arrays;

/**
 * SubtractFunc
 *
 * <AUTHOR> Created on 2023/7/13 14:13
 */
@ServiceFunction(key = "SUBTRACT")
public class SubtractFunc extends FuncImpl {

    @Override
    public Object apply(Object[] objects, VariableContext context) {
        if (objects != null) {
            return Arrays.stream(objects)
                .map(Object::toString)
                .map(BigDecimal::new)
                .reduce(BigDecimal::subtract)
                .orElse(null);
        }
        return null;
    }
}
