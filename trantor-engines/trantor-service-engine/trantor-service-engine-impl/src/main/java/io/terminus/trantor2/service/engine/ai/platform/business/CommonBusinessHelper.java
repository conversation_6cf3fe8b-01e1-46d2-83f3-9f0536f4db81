package io.terminus.trantor2.service.engine.ai.platform.business;

import com.google.common.collect.Lists;
import io.terminus.trantor2.model.management.meta.consts.SystemFieldGeneratorV2;
import io.terminus.trantor2.service.dsl.enums.*;
import io.terminus.trantor2.service.dsl.properties.*;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class CommonBusinessHelper {
    public static  <T> Map<Field, Object> buildQueryFields(T queryParams) {
        Map<Field, Object> params = new HashMap<>();
        java.lang.reflect.Field[] fields = queryParams.getClass().getDeclaredFields();
        for (java.lang.reflect.Field field : fields) {
            try {
                field.setAccessible(true);
                String fieldType = field.getType().getSimpleName();
                String fieldName = field.getName();
                Object fieldValue = null;
                Field dslField = null;
                switch (fieldType) {
                    case "String":
                        fieldValue = field.get(queryParams);
                        dslField = new BaseField(fieldName, fieldName, FieldType.Text);
                        break;
                    case "Float":
                    case "Double":
                    case "Integer":
                    case "Long":
                        fieldValue = field.get(queryParams);
                        dslField = new BaseField(fieldName, fieldName, FieldType.Number);
                        break;
                    case "Boolean":
                        fieldValue = field.get(queryParams);
                        dslField = new BaseField(fieldName, fieldName, FieldType.Boolean);
                    default:
                        break;
                }
                params.put(dslField, fieldValue);
            } catch (Exception e) {
                log.error("get generic field type value failed", e);
            }
        }
        return params;
    }

    public static List<Condition> buildQueryConditions(Map<Field, Object> queryFields) {
        List<Condition> conditions = new ArrayList<>();
        queryFields.forEach((field, value) -> {
            ConditionLeaf condition = new ConditionLeaf();

            // 左值
            VarValue leftValue = new VarValue();
            leftValue.setValueType(ValueType.MODEL);
            leftValue.setFieldType(FieldType.Text);
            leftValue.setVarValue(Lists.newArrayList(new VarValue.VarStage(field.getFieldKey())));
            condition.setLeftValue(leftValue);

            // 右值
            ConstValue constValue = new ConstValue();
            switch (field.getFieldType()) {
                case Text:
                    condition.setOperator(Operator.EQ);
                    constValue.setFieldType(FieldType.Text);
                    break;
                case Number:
                    condition.setOperator(Operator.EQ);
                    constValue.setFieldType(FieldType.Number);
                    break;
                case Boolean:
                    condition.setOperator(Operator.EQ);
                    constValue.setFieldType(FieldType.Boolean);
                    break;
                case Date:
                    condition.setOperator(Operator.LTE);
                    constValue.setFieldType(FieldType.DateTime);
                default:
                    break;
            }
            constValue.setConstValue(value);
            condition.setRightValue(constValue);

            conditions.add(condition);
        });
        return conditions;
    }

    @NotNull
    public static Pageable buildPageable(int pageSize, List<Condition> conditions, SortType sortType) {
        ConditionGroup conditionGroup = new ConditionGroup();
        conditionGroup.setLogicOperator(LogicOperator.AND);
        conditionGroup.setConditions(Lists.newArrayList(conditions));

        List<SortOrder> sortOrders = Lists.newArrayList(new SortOrder(SystemFieldGeneratorV2.CREATED_AT.getFieldAlias(), sortType));

        Pageable pageable = new Pageable(1, pageSize);
        pageable.setConditionGroup(conditionGroup);
        pageable.setSortOrders(sortOrders);
        return pageable;
    }

    public static <T> List<SelectField> buildSelectFields(Class<T> clazz) {
        List<String> fields = getObjectFieldProperties(clazz, null);
        return fields.stream()
            .map(SelectField::new)
            .collect(Collectors.toList());
    }

    public static <T> List<String> getObjectFieldProperties(Class<T> clazz, List<String> targetFields) {
        List<String> fields = Arrays.stream(clazz.getDeclaredFields())
            .map(java.lang.reflect.Field::getName)
            .collect(Collectors.toList());
        if (Objects.nonNull(targetFields) && !targetFields.isEmpty()) {
            return fields.stream()
                .filter(targetFields::contains)
                .collect(Collectors.toList());
        } else {
            return fields;
        }
    }
}
