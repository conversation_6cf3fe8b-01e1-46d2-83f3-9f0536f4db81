package io.terminus.trantor2.service.engine.impl.sysservice;

import com.google.auto.service.AutoService;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.dsl.enums.Propagation;
import io.terminus.trantor2.service.engine.impl.component.ModelDataProcessHelper;
import io.terminus.trantor2.service.engine.impl.component.bean.QueryModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Map;

/**
 * SumDataService
 *
 * <AUTHOR> Created on 2023/11/27 11:40
 */
@RequiredArgsConstructor
@Slf4j
@AutoService(SystemService.class)
public class SumDataService extends AbstractSystemServiceImpl {

    public static final String KEY = "SYS_SUM_DATA_SERVICE";

    @Override
    protected void supplyDefinition(ServiceDefinition definition) {
        definition.setTransactionPropagation(Propagation.NOT_SUPPORTED);
        definition.addInput(MODEL_KEY_FIELD);
        definition.addInput(FIELDS);
        definition.addInput(CONDITION_REQUEST);
        definition.addOutput(OBJECT_DATA_OUTPUT);
    }

    @Override
    protected void execute(SystemServiceExecContext context) {
        QueryModel queryModel = ModelDataProcessHelper.buildCondition(context, false);
        initFindDataPermissions(KEY, context, queryModel);
        Map<String, BigDecimal> sum = context.getServiceEngine().getModelDataRepository()
                .sum(queryModel, context.getParameter().getFields());
        context.setResult(sum);
    }

    @Override
    public String getServiceKey() {
        return KEY;
    }

    @Override
    public String getServiceName() {
        return "(系统)SUM服务";
    }
}
