package io.terminus.trantor2.service.engine.ai.core.message;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * TaskStartContent
 *
 * <AUTHOR> Created on 2025/6/11 14:55
 */
@Setter
@Getter
@NoArgsConstructor
public class TaskStartContent implements Content {
    private static final long serialVersionUID = 7506899859017006439L;

    private ContentType type = ContentType.TASK_START;
    private AIProgressStatus status = AIProgressStatus.DONE;

    private String taskId;
    private String taskName;
    private String newInput;
    private String arguments;
}
