package io.terminus.trantor2.service.engine.impl.debug;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Breakpoint
 *
 * <AUTHOR> Created on 2024/11/14 17:19
 */
@Data
@NoArgsConstructor
public class Breakpoint implements Serializable {
    private static final long serialVersionUID = -8702719018630252986L;
    private String nodeKey;
    private String conditionExpression;

    public Breakpoint(String nodeKey) {
        this.nodeKey = nodeKey;
    }
}
