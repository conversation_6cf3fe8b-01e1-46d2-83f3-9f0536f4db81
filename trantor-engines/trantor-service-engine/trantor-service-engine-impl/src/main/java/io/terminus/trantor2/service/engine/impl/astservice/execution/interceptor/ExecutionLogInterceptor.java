package io.terminus.trantor2.service.engine.impl.astservice.execution.interceptor;

import com.google.common.base.Throwables;
import io.terminus.trantor2.service.engine.executor.interceptor.Contexts;
import io.terminus.trantor2.service.engine.impl.astservice.ExecutionContext;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.Instant;

/**
 * ExecutionLogInterceptor
 *
 * <AUTHOR> Created on 2024/7/19 09:57
 */
@Slf4j
public class ExecutionLogInterceptor extends ExecutionInterceptor {
    @Override
    public void execute(ExecutionContext context) {
        Instant start = Instant.now();
        try {
            next.execute(context);
        } catch (Throwable e) {
            Contexts.getGlobalInvocationContext().exception(e, buildInnerMsg(context, e));
            throw e;
        } finally {
            long cost = Duration.between(start, Instant.now()).toMillis();
            if (cost > 500) {
                log.warn("[{}][{}][{}({})] service node execute cost:{}ms",
                        Contexts.getTraceId(),
                        context.getServiceKey(),
                        getCurrentServiceElement().getName(),
                        getCurrentServiceElement().getKey(),
                        cost);
            } else {
                log.info("[{}][{}][{}({})] service node execute cost:{}ms",
                        Contexts.getTraceId(),
                        context.getServiceKey(),
                        getCurrentServiceElement().getName(),
                        getCurrentServiceElement().getKey(),
                        cost);
            }
        }
    }

    private String buildInnerMsg(ExecutionContext context, Throwable e) {
        return String.format("[%s][%s(%s)]发生异常：%s",
                context.getServiceKey(),
                getCurrentServiceElement().getName(),
                getCurrentServiceElement().getKey(),
                Throwables.getRootCause(e).getMessage());
    }
}
