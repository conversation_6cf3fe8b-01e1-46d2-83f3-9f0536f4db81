package io.terminus.trantor2.service.engine.executor.interceptor;

import io.terminus.trantor2.service.engine.executor.ServiceInvocation;
import lombok.extern.slf4j.Slf4j;

/**
 * ServiceInvoker
 *
 * <AUTHOR> Created on 2023/2/18 17:22
 */
@Slf4j
public class ServiceInvoker extends ServiceInterceptor {
    @Override
    public Object execute(ServiceInvocation invocation) {
        return invocation.invoke();
    }

    @Override
    public void setNext(ServiceInterceptor next) {
        throw new UnsupportedOperationException("Cannot set next interceptor");
    }
}
