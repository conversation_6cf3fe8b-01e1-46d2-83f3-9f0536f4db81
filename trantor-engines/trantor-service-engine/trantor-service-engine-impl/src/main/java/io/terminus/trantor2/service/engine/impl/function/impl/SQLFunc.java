package io.terminus.trantor2.service.engine.impl.function.impl;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.engine.exception.ServiceExecuteException;
import io.terminus.trantor2.service.engine.impl.component.bean.SqlQueryModel;
import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.function.ServiceFunction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SQLFunc
 *
 * <AUTHOR> Created on 2023/8/2 13:48
 */
@Slf4j
@ServiceFunction(key = "SQL")
public class SQLFunc extends FuncImpl {

    private static final Pattern pattern = Pattern.compile("\\$\\{([^{}]+)}");

    @Override
    public Object apply(Object[] objects, VariableContext context) {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();

        String sql = (String) objects[0];
        if (StringUtils.contains(sql, ",")) {
            throw new ServiceExecuteException(ErrorType.SERVICE_ERROR, "'SQL'函数仅支持一个返回字段");
        }
        sql = replaceSqlParameter(sql, parameters, context);
        sql = appendLimitOne(sql);
        log.info("SQL function sql:{},parameters:{}", sql, JsonUtil.toJson(parameters));
        SqlQueryModel queryModel = new SqlQueryModel();
        queryModel.setSql(sql);
        queryModel.setTeamId(context.getTeamId());
        queryModel.setParameters(parameters);
        queryModel.setModuleKey(context.getModuleKey());
        Map<String, Object> result = context.getServiceEngine().getModelDataRepository().selectOne(queryModel);
        log.info("SQL function sql:{},parameters:{},result:{}", sql, JsonUtil.toJson(parameters), JsonUtil.toJson(result));
        if (result == null || result.isEmpty()) {
            return null;
        }
        return new ArrayList<>(result.values()).get(0);
    }

    private String appendLimitOne(String sql) {
        if (!StringUtils.containsIgnoreCase(sql, "limit")) {
            return sql + " limit 1 ";
        }
        return sql;
    }

    private String replaceSqlParameter(String sql, LinkedHashMap<String, Object> parameters, VariableContext context) {
        AtomicInteger argCounter = new AtomicInteger(0);
        StringBuffer replaceBuffer = new StringBuffer();
        Matcher matcher = pattern.matcher(sql);
        while (matcher.find()) {
            String key = matcher.group(1);
            String param = "arg" + argCounter.getAndIncrement();
            Object value = getValue(key, context);
            parameters.put(param, value);
            matcher.appendReplacement(replaceBuffer, renderParam(param));
        }
        matcher.appendTail(replaceBuffer);
        return replaceBuffer.toString();
    }

    private String renderParam(String param) {
        return ":" + param;
    }

    private Object getValue(String key, VariableContext context) {
        String[] paths = key.split("\\.");
        return context.getVariable(paths);
    }
}
