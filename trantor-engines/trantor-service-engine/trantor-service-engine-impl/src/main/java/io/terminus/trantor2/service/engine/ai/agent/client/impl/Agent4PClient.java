package io.terminus.trantor2.service.engine.ai.agent.client.impl;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.engine.ai.agent.client.AgentClient;
import io.terminus.trantor2.service.engine.ai.agent.client.request.AgentRequest;
import io.terminus.trantor2.service.engine.ai.agent.client.request.MemoryExtractAndUpdateRequest;
import io.terminus.trantor2.service.engine.ai.agent.client.response.MemoryExtractAndUpdateResponse;
import io.terminus.trantor2.service.engine.ai.client.httpclient.AiHttpClient;
import io.terminus.trantor2.service.engine.ai.client.httpclient.StreamingFuture;
import io.terminus.trantor2.service.engine.ai.client.httpclient.StreamingHandler;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;

/**
 * Agent4PClient
 *
 * <AUTHOR> Created on 2025/3/18 10:56
 */
@Slf4j
public class Agent4PClient implements AgentClient {

    private final String tAiDomain;
    private final AiHttpClient aiHttpClient;

    public Agent4PClient(String tAiDomain, AiHttpClient aiHttpClient) {
        this.tAiDomain = tAiDomain;
        this.aiHttpClient = aiHttpClient;
    }

    @Override
    public String completion(AgentRequest request) {
        String url = tAiDomain + "/api/ai/agent/run";
        return aiHttpClient.post(url, request);
    }

    @Override
    public StreamingFuture chatCompletion(AgentRequest request, StreamingHandler listener) {
        String url = tAiDomain + "/api/ai/agent/run_streamed";
        return aiHttpClient.stream(url, request, listener);
    }

    @Nullable
    @Override
    public MemoryExtractAndUpdateResponse extractAndUpdateMemory(MemoryExtractAndUpdateRequest request) {
        String url = tAiDomain + "/api/ai/agent/memory/extract-and-update";
        return aiHttpClient.post(url, request, responseBody -> JsonUtil.fromJson(responseBody, MemoryExtractAndUpdateResponse.class));
    }
}
