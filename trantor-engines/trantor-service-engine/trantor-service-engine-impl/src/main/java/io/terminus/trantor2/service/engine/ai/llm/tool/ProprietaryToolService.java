package io.terminus.trantor2.service.engine.ai.llm.tool;

import com.openai.models.chat.completions.ChatCompletionAssistantMessageParam;
import com.openai.models.chat.completions.ChatCompletionCreateParams;
import com.openai.models.chat.completions.ChatCompletionMessageParam;
import com.openai.models.chat.completions.ChatCompletionUserMessageParam;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.service.dsl.properties.SkillTool;
import io.terminus.trantor2.service.engine.ai.core.memory.ChatMemory;
import io.terminus.trantor2.service.engine.ai.core.memory.MemoryManager;
import io.terminus.trantor2.service.engine.ai.core.message.AiMessage;
import io.terminus.trantor2.service.engine.ai.core.message.ChatMessageRole;
import io.terminus.trantor2.service.engine.ai.core.message.Content;
import io.terminus.trantor2.service.engine.ai.core.message.MessageHelper;
import io.terminus.trantor2.service.engine.ai.core.message.TextContent;
import io.terminus.trantor2.service.engine.ai.core.message.ToolArgumentsContent;
import io.terminus.trantor2.service.engine.ai.core.message.ToolCallContent;
import io.terminus.trantor2.service.engine.ai.core.message.ToolOutputContent;
import io.terminus.trantor2.service.engine.ai.core.message.UserMessage;
import io.terminus.trantor2.service.engine.ai.llm.LlmClientService;
import io.terminus.trantor2.service.engine.impl.cache.CacheFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class ProprietaryToolService {
    private static final String GENERATE_CODE_MODEL_PROVIDER = "anthropic";
    private static final String GENERATE_CODE_MODEL_NAME = "us.anthropic.claude-sonnet-4-20250514-v1:0";
    private static final String DOWNGRADE_1_GENERATE_CODE_MODEL_PROVIDER = "moonshotai";
    private static final String DOWNGRADE_1_GENERATE_CODE_MODEL_NAME = "kimi-k2";
    private static final String DOWNGRADE_2_GENERATE_CODE_MODEL_PROVIDER = "qwen";
    private static final String DOWNGRADE_2_GENERATE_CODE_MODEL_NAME = "qwen3-coder-plus";

    @Lazy
    @Autowired
    private LlmClientService llmClientService;

    @Autowired
    private MemoryManager memoryManager;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired(required = false)
    private CacheFactory cacheFactory;

    public void build(String agentKey, String conversationId) {
        ChatMemory chatMemory = memoryManager.findChatMemory(conversationId);
        // 查询历史记录
        List<String> history = MessageHelper.getHistoryMessages(chatMemory, 10, message -> {
            List<ChatCompletionMessageParam> messageParams = new ArrayList<>();
                    StringBuilder historyBuilder = new StringBuilder();
                    StringBuilder toolCallBuilder = new StringBuilder();
                    if (message instanceof UserMessage userMessage) {
                        historyBuilder.append(message.getRole().value()).append(": ");
                        for (Content content : userMessage.getContent()) {
                            if (content instanceof TextContent textContent) {
                                messageParams.add(ChatCompletionMessageParam.ofUser(ChatCompletionUserMessageParam.builder().content(textContent.getText()).build()));
                            }
                        }
                    } else if (message instanceof AiMessage aiMessage) {
                        if (aiMessage.getContent() instanceof TextContent textContent) {
                            messageParams.add(ChatCompletionMessageParam.ofAssistant(ChatCompletionAssistantMessageParam.builder().content(textContent.getText()).build()));
                        } else if (aiMessage.getContent() instanceof ToolCallContent toolCallContent) {
                            toolCallBuilder.append("call tool").append("\n");
                            toolCallBuilder.append(String.format("tool name: %s", toolCallContent.getName())).append("\n");
                        } else if (aiMessage.getContent() instanceof ToolArgumentsContent toolArgumentsContent) {
                            toolCallBuilder.append(String.format("tool arguments: %s", toolArgumentsContent.getArguments())).append("\n");
                        } else if (aiMessage.getContent() instanceof ToolOutputContent toolOutputContent) {
                            toolCallBuilder.append(String.format("tool output: %s", toolOutputContent.getOutput())).append("\n");
                            toolCallBuilder.append(String.format("call tool status: %s", toolOutputContent.getInvokeStatus())).append("\n");
                            messageParams.add(ChatCompletionMessageParam.ofAssistant(ChatCompletionAssistantMessageParam.builder().content(toolCallBuilder.toString()).build()));
                            toolCallBuilder.delete(0, toolCallBuilder.length());
                        }
                    } else {
                        messageParams.add(ChatCompletionMessageParam.ofAssistant(ChatCompletionAssistantMessageParam.builder().content(message.messageContent()).build()));
                    }

                    if (historyBuilder.isEmpty()) {
                        return null;
                    } else {
                        return historyBuilder.toString();
                    }
                })
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<String> historyMessages = null;
        String pythonCode = generateCode(historyMessages);
        storeProprietaryTools(agentKey, pythonCode);
    }

    public String generateCode(List<String> historyMessages) {
        ChatCompletionCreateParams.Builder builder = ChatCompletionCreateParams.builder()
                                                                               .model(GENERATE_CODE_MODEL_PROVIDER)
                                                                               .addSystemMessage(getSysPrompt())
                                                                               .maxCompletionTokens(8192)
                                                                               .temperature(0.1D);
        historyMessages.forEach(historyMessage -> {
            if (historyMessage.getRole().equals(ChatMessageRole.USER)) {
                builder.addUserMessage(historyMessage.messageContent());
            } else if (historyMessage.getRole().equals(ChatMessageRole.ASSISTANT)) {
                builder.addAssistantMessage(historyMessage.messageContent());
            }
        });
        //String output = llmClientService.chatCompletion(GENERATE_CODE_MODEL_PROVIDER, GENERATE_CODE_MODEL_NAME, builder.build(), String.class);
        return "";
    }

    /// proprietary_tool:teamCode:agentKey:userId:tool_name
    ///
    /// code: XXXXX
    /// arguments_schema: XXXX
    public String getCacheKey(String agentKey) {
        String teamCode = TrantorContext.getTeamCode();
        Long userId = TrantorContext.getCurrentUserId();
        return String.format("agent:proprietary_tool:%s:%s:%s", teamCode, userId, agentKey);
    }

    public List<SkillTool> loadProprietaryTools(Long teamId, String key) {
        List<SkillTool> tools = new ArrayList<>();
        return tools;
    }

    public void storeProprietaryTools(String agentKey, String pythonCode) {
        String cacheKey = getCacheKey(agentKey);
        redisTemplate.opsForValue().set(cacheKey, pythonCode);
    }

    public String getSysPrompt() {
        return "# 角色：资深Python开发技术专家  \n" +
                "负责分析用户意图，并结合工具调用信息生成相应的Python代码。\n" +
                "\n" +
                "## 目标：\n" +
                "1. 分析对话中用户的意图。\n" +
                "2. 列举并分析在对话中使用到的工具。\n" +
                "3. 归纳出一个可以替代所有工具调用的综合工具名称，并生成该名称。\n" +
                "4. 生成Python代码以逻辑调用这些工具。\n" +
                "\n" +
                "## 技能：\n" +
                "1. 理解用户意图并进行语义分析。\n" +
                "2. 熟悉各种工具及其功能，能够进行有效归纳。\n" +
                "3. 精通Python编程，能够实现工具调用的逻辑。\n" +
                "\n" +
                "## 工作流：\n" +
                "1. 解析用户的对话内容以提取意图。\n" +
                "2. 确定并列出对话中提到的所有工具。\n" +
                "3. 基于工具的功能特点，提出一个综合工具的名称。\n" +
                "4. 编写Python代码以实现对这些工具的调用逻辑。\n" +
                "\n" +
                "## 输出格式：\n" +
                "生成的Python代码应包含必要的注释，并遵循PEP 8编码规范。\n" +
                "\n" +
                "## 限制：\n" +
                "- 在分析用户意图时，需保持中立，不做主观判断。\n" +
                "- 在工具名称的归纳中，应确保名称简洁且具有描述性。\n" +
                "- 生成的代码需经过逻辑验证，确保其可执行性和功能完整性。";
    }

    public String getBuildProprietaryToolPrompt() {
        return "";
    }
}
