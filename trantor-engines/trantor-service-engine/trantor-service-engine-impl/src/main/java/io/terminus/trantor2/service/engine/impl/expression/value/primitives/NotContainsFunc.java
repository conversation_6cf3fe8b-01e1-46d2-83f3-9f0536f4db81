package io.terminus.trantor2.service.engine.impl.expression.value.primitives;

import io.terminus.trantor2.service.engine.impl.expression.OperatorType;
import io.terminus.trantor2.service.engine.impl.expression.ParserException;
import io.terminus.trantor2.service.engine.impl.expression.ast.AbstractNode;
import io.terminus.trantor2.service.engine.impl.expression.value.BoolValue;
import io.terminus.trantor2.service.engine.impl.expression.value.ListValue;
import io.terminus.trantor2.service.engine.impl.expression.value.NullValue;
import io.terminus.trantor2.service.engine.impl.expression.value.PrimFunc;
import io.terminus.trantor2.service.engine.impl.expression.value.StringValue;
import io.terminus.trantor2.service.engine.impl.expression.value.Value;

import java.util.List;

/**
 * NotContainFunc
 *
 * <AUTHOR> Created on 2022/08/29
 */
public class NotContainsFunc implements PrimFunc {

    @Override
    public Value apply(List<Value> args, AbstractNode location) {

        if (args.size() != OperatorType.BINARY.getArgNum()) {
            throw new ParserException("not contain function args error:{}", location);
        }
        if (args.get(0) instanceof StringValue && args.get(1) instanceof StringValue) {
            StringValue arg1 = (StringValue) args.get(0);
            StringValue arg2 = (StringValue) args.get(1);
            return new BoolValue(!arg1.getValue().contains(arg2.getValue()));
        } else if (args.get(0) instanceof ListValue) {
            ListValue arg0 = (ListValue) args.get(0);
            List<Value> lstValue = arg0.getValue();
            return new BoolValue(!lstValue.contains(args.get(1)));
        } else if (args.get(0) instanceof NullValue || args.get(1) instanceof NullValue) {
            return new BoolValue(false);
        } else {
            throw new ParserException("not contain function args type error:{}", location);
        }
    }

}
