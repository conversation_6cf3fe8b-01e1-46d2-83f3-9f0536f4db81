package io.terminus.trantor2.service.engine.executor.interceptor;

import io.terminus.iam.api.response.permission.FieldRule;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.doc.api.dto.PermissionDTO;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.service.common.consts.ServiceConst;
import io.terminus.trantor2.service.dsl.ServiceElement;
import io.terminus.trantor2.service.dsl.properties.DefinitionProperties;
import io.terminus.trantor2.service.engine.delegate.Metadata;
import io.terminus.trantor2.service.engine.delegate.Service;
import io.terminus.trantor2.service.engine.executor.ServiceInvocation;
import io.terminus.trantor2.service.engine.permission.FieldPermissionHandlerDelegate;
import io.terminus.trantor2.service.engine.permission.ProtectStrategy;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 字段权限拦截器，在执行服务拦截链中插入处理服务入参、出参字段鉴权的处理逻辑
 *
 * <AUTHOR>
 * 2024/7/11 20:25
 **/
@Component
@RequiredArgsConstructor
public class FieldPermissionServiceInterceptor extends CustomPreInterceptor {

    @Override
    public Object execute(ServiceInvocation invocation) {

        // 系统服务暂不支持字段权限
        if (KeyUtil.isSysShortKey(invocation.getServiceKey())) {
            return next.execute(invocation);
        }

        // 查询服务绑定权限项标识
        String permissionKey = getServicePermissionKey(invocation);

        // 对服务入参执行写权限保护
        executeInputFieldPermission(invocation, invocation.getArgs().getPayload(), permissionKey);

        Object executeResult = next.execute(invocation);

        // 对服务出参执行读权限保护
        return executeOutputFieldPermission(invocation, executeResult, permissionKey);

    }

    /**
     * 执行服务入参字段的写权限校验
     */
    private void executeInputFieldPermission(ServiceInvocation invocation, Object payload, String permissionKey) {
        if (Objects.isNull(payload)) {
            return;
        }
        List<FieldRule> fieldRules = Optional.ofNullable(invocation)
                .map(ServiceInvocation::getService)
                .map(Service::getMeta)
                .map(Metadata::getDefinition)
                .map(ServiceElement::getProps)
                .map(DefinitionProperties::getInputFieldRules)
                .orElse(Collections.emptyList());
        if (CollectionUtils.isNotEmpty(fieldRules)) {   // 服务配置有入参字段权限规则
            FieldPermissionHandlerDelegate.getInstance().handle(payload, permissionKey, ProtectStrategy.WRITABLE_PROTECTION);
        }
    }

    /**
     * 执行服务出参字段的读权限校验
     */
    private Object executeOutputFieldPermission(ServiceInvocation invocation, Object resultData, String permissionKey) {
        if (Objects.isNull(resultData)) {
            return null;
        }
        List<FieldRule> fieldRules = Optional.ofNullable(invocation)
                .map(ServiceInvocation::getService)
                .map(Service::getMeta)
                .map(Metadata::getDefinition)
                .map(ServiceElement::getProps)
                .map(DefinitionProperties::getOutputFieldRules)
                .orElse(Collections.emptyList());
        if (CollectionUtils.isNotEmpty(fieldRules)) {   // 服务配置有出参字段权限规则
            return FieldPermissionHandlerDelegate.getInstance().handle(resultData, permissionKey, ProtectStrategy.READABLE_PROTECTION);
        } else {
            return resultData;
        }
    }

    private String getServicePermissionKey(ServiceInvocation invocation) {
        if (KeyUtil.isSysShortKey(invocation.getServiceKey())) {
            String modelKey = JsonUtil.INDENT.getObjectMapper().valueToTree(invocation.getArgs().getPayload()).get(ServiceConst.MODEL_KEY).asText();
            Map<String, PermissionDTO> permissions = invocation.getService().getMeta().getPermissions();
            if (MapUtils.isEmpty(permissions) || Objects.isNull(permissions.get(modelKey))) {
                return null;
            }
            return permissions.get(modelKey).getFunctionPermissionKey();
        } else {
            // 编排服务/事件服务
            return invocation.getService().getMeta().getDefinition().getProps().getPermissionKey();
        }
    }
}
