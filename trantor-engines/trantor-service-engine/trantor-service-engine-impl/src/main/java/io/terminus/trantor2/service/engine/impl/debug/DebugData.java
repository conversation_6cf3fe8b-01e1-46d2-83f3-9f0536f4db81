package io.terminus.trantor2.service.engine.impl.debug;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * DebugData
 *
 * <AUTHOR> Created on 2024/10/25 09:37
 */
@Data
@NoArgsConstructor
public class DebugData implements Serializable {
    private static final long serialVersionUID = -2234525970397061083L;
    private DebugLocation location;

    private Map<String, Object> nodeVariables;
    private Map<String, Object> globalVariables;
    private Map<String, Object> outputVariables;
    private Map<String, Object> allVariables;

    private String remoteDebugOpts;

    public DebugData(DebugLocation location) {
        this.location = location;
    }

    public void addNodeVariable(String field, Object value) {
        if (nodeVariables == null) {
            nodeVariables = new LinkedHashMap<>(8);
        }
        nodeVariables.put(field, value);
    }
}
