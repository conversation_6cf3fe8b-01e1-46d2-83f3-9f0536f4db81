package io.terminus.trantor2.service.engine.ai.platform.impl;

import com.alibaba.fastjson.JSONValidator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.utils.ApplicationContextUtil;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.common.utils.HttpClient;
import io.terminus.trantor2.service.engine.ai.configuration.AiProperties;
import io.terminus.trantor2.service.engine.ai.platform.AIExternalRestHandler;
import io.terminus.trantor2.service.engine.ai.platform.service.bean.QueryMcpServerListParam;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * <AUTHOR>
 */
@Slf4j
public class McpServerRestHandler implements AIExternalRestHandler {
    private volatile static McpServerRestHandler handler = null;
    /**
     * API接口前缀
     */
    public static final String API_PREFIX = "api/ai-proxy";
    /**
     * MCP相关接口请求地址
     */
    private final String urlPrefix;
    /**
     * AI-Proxy MCP接口必要的鉴权请求头信息
     */
    Map<String, String> headers;
    /**
     * AI 配置
     */
    private final AiProperties aiProperties;

    private McpServerRestHandler() {
        aiProperties = (AiProperties) ApplicationContextUtil.getApplicationContext().getBean("aiProperties");
        String domain = aiProperties.getAiProxy().getDomain();
        String token = aiProperties.getAiProxy().getAccessKeyId();
        urlPrefix = String.format("%s/%s", domain, API_PREFIX);
        headers = Maps.newHashMap();
        headers.put("Authorization", String.format("Bearer %s", token));
    }

    public static McpServerRestHandler newInstance() {
        // DCL 实例化
        if (null == handler) {
            synchronized (McpServerRestHandler.class) {
                if (null == handler) {
                    handler = new McpServerRestHandler();
                }
            }
        }
        return handler;
    }

    @Getter
    @Setter
    public static class McpServerDetails {
        private Long total;
        private List<McpServer> list;
    }

    @Getter
    @Setter
    public static class McpServer {
        private String id;
        private String name;
        private String version;
        private String description;
        private String instruction;
        private String endpoint;
        private List<McpTool> tools;
        private Boolean isDefaultVersion;
        private Boolean isPublished;
    }

    @Getter
    @Setter
    public static class McpTool {
        private String name;
        private String description;
        private McpToolInputSchema inputSchema;
    }

    @Getter
    @Setter
    public static class McpToolInputSchema {
        private String type;
        private Map<String, McpToolInputProperties> properties;
        private List<String> required;
    }

    @Getter
    @Setter
    public static class McpToolInputProperties {
        private String title;
        private String type;
        private String description;
        @JsonProperty("default")
        private Object defaultValue;
        @JsonProperty("enum")
        private List<String> enums;

        // 支持anyOf结构，用于处理复杂类型定义
        private List<Map<String, Object>> anyOf;

        /**
         * 获取实际的类型，如果type为空则尝试从anyOf中推断
         * 确保永远不返回null，避免解析字段时，switch语句出现NPE
         */
        public String getType() {
            if (type != null) {
                return type;
            }

            // 尝试从anyOf中推断类型（处理复杂Schema）
            if (anyOf != null && !anyOf.isEmpty()) {
                for (Map<String, Object> typeOption : anyOf) {
                    Object typeValue = typeOption.get("type");
                    if (typeValue != null && !"null".equals(typeValue.toString())) {
                        return typeValue.toString();
                    }
                }
            }

            // 默认返回string类型，确保不为null
            return "string";
        }
    }

    public McpServerDetails getMcpServerDetails(QueryMcpServerListParam param) {
        String url = String.format("%s?pageNum=%d&pageSize=%d", param.getProviderAddress(), param.getPageNo(), param.getPageSize());
        try {
            Map<String, String> customHeaders = new HashMap<>(headers);
            if (StringUtils.isNotBlank(param.getAccessKey())) {
                customHeaders.put("Authorization", String.format("Bearer %s", param.getAccessKey()));
            }
            String result = HttpClient.INSTANCE.get(url, customHeaders, null);
            if (JSONValidator.from(result).validate()) {
                Response<McpServerDetails> response = JsonUtil.fromJson(result, new TypeReference<Response<McpServerDetails>>() {
                });
                if (response.isSuccess()) {
                    log.info("mcp server details response :\n{}", JsonUtil.toJson(response.getData()));
                    return response.getData();
                } else {
                    String errorMessage = response.getErr().getSuggest();
                    if (StringUtils.isBlank(errorMessage)) {
                        errorMessage = response.getErrMsg();
                    }
                    log.error("invoke ai-proxy request mcp server details error: {}", errorMessage);
                }
            } else {
                log.error("invoke ai-proxy request mcp server details error, response is not json");
            }
        } catch (Exception e) {
            log.error("invoke {} details exception", param.getProviderAddress(), e);
            throw new RuntimeException("暂不支持该MCP服务源");
        }
        return null;
    }
}
