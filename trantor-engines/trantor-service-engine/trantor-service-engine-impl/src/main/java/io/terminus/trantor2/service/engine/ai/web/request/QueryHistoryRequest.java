package io.terminus.trantor2.service.engine.ai.web.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * QueryHistoryRequest
 *
 * <AUTHOR> Created on 2025/5/8 16:06
 */
@Data
public class QueryHistoryRequest {

    @Schema(description = "Agent Key")
    @NotNull(message = "agentKey不能为空")
    private String agentKey;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "分页页码")
    @NotNull(message = "分页页码不能为空")
    private Integer pageNumber = 1;

    @Schema(description = "分页页大小")
    @NotNull(message = "分页大小不能为空")
    private Integer pageSize = 10;
}
