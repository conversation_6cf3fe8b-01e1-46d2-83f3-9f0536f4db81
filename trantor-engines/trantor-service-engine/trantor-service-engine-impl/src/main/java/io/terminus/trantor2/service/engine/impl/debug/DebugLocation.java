package io.terminus.trantor2.service.engine.impl.debug;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * DebugLocation
 *
 * <AUTHOR> Created on 2024/11/13 11:02
 */
@Getter
@EqualsAndHashCode(of = {"endpoint", "nodeKey", "currentTimestamp"})
@RequiredArgsConstructor
public class DebugLocation implements Serializable {
    private static final long serialVersionUID = 5833084518833114524L;

    private final DebugEndpoint endpoint;
    private final String nodeKey;
    private final Long currentTimestamp;

    /**
     * 冗余
     */
    @Setter
    private String nodeName;

    /**
     * 冗余
     */
    @Setter
    private String stepIntoServiceKey;
}
