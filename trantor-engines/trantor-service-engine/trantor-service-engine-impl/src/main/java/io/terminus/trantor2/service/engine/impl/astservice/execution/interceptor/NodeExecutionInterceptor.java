package io.terminus.trantor2.service.engine.impl.astservice.execution.interceptor;

import io.terminus.trantor2.service.dsl.ServiceElement;

/**
 * 服务节点执行拦截器
 * <p>
 * 实现类需要添加注解<code>@AutoService(NodeExecutionInterceptor.class)</code>
 *
 * <AUTHOR> Created on 2024/6/12 16:23
 */
public abstract class NodeExecutionInterceptor extends ExecutionInterceptor {

    protected boolean supports(ServiceElement<?> serviceElement) {
        return true;
    }

    protected int order() {
        return Integer.MAX_VALUE;
    }

    /**
     * 创建新实列
     */
    protected NodeExecutionInterceptor createNewInstance() {
        try {
            return this.getClass().newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }
}
