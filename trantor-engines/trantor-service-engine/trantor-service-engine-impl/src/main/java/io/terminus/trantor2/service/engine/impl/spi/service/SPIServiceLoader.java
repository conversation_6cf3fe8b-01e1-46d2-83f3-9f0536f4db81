package io.terminus.trantor2.service.engine.impl.spi.service;

import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.delegate.Service;
import io.terminus.trantor2.service.engine.impl.spi.SPILoader;
import io.terminus.trantor2.service.engine.impl.spi.SPIProxy;
import io.terminus.trantor2.service.engine.loader.ServiceLoader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * SPIServiceLoader
 *
 * <AUTHOR> Created on 2023/3/28 17:35
 */
@Slf4j
@RequiredArgsConstructor
public class SPIServiceLoader extends ServiceLoader {

    private final SPILoader spiLoader;

    @Override
    protected int preloading() {
        // 当前SPI加载器初始化完成时，SPI还在扫描中，所以不进行预加载
        return -1;
    }

    @Override
    public Service load(Key serviceKey) {
        final Key targetKey = Key.ofSPIServiceKey(serviceKey.shortKey());
        return find(targetKey, (key) -> {
            final SPIProxy proxy = spiLoader.load(key.getKey());
            if (proxy != null) {
                return new SPIService(proxy);
            }
            return null;
        });
    }

    @Override
    public void remove(Key serviceKey) {
        // not remove
    }

    @Override
    public void clear() {
        // not remove
    }

    @Override
    public int order() {
        return 100;
    }
}
