package io.terminus.trantor2.service.engine.impl.value.mapper;

import io.terminus.trantor2.service.common.dto.InnerModel;
import io.terminus.trantor2.service.dsl.enums.VariableType;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.dsl.properties.Value;
import io.terminus.trantor2.service.dsl.properties.VarValue;
import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.value.ValueFactory;
import io.terminus.trantor2.service.engine.impl.value.ValueMapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * VarValueMapper
 *
 * <AUTHOR> Created on 2023/2/20 16:36
 */
public class VarValueMapper implements ValueMapper {
    @Override
    public Object getValue(Field field, Value value, VariableContext context) {
        VarValue varValue = (VarValue) value;
        Object objValue = doGetValue(varValue, context);
        objValue = convertType(field, objValue);
        return objValue;
    }

    private Object doGetValue(VarValue varValue, VariableContext context) {
        Object objValue;
        if (varValue.getValueType().isConst()) {
            objValue = varValue.getConstValue();
        } else {
            objValue = context.getVariable(varValue.getPaths());
            if (objValue == null) {
                // 兼容一下老版本的服务变量定义
                objValue = applyOlderVersion(varValue.getVarValue(), context);
            }
        }
        return objValue;
    }

    /**
     * 兼容老的服务逻辑，老的参数结构是：节点+模型+字段
     * <p>
     * <strong>当重构时或者看不懂时，可以直接删除下面代码</strong>
     */
    @Deprecated
    private Object applyOlderVersion(List<VarValue.VarStage> stages, VariableContext context) {
        List<String> path = new ArrayList<>(stages.size());
        VarValue.VarStage first = stages.get(0);
        VarValue.VarStage second = stages.size() > 1 ? stages.get(1) : null;

        if (second == null) {
            return null;
        }

        if (first.getValueKey().equals("global")) {
            if (second.getValueKey().equals(InnerModel.SYSTEM_VARIABLE.getModelKey())) {
                path.add(VariableType.SYS.getKey());
                path.addAll(stages.subList(2, stages.size()).stream().map(VarValue.VarStage::getValueKey).collect(Collectors.toList()));
                return context.getVariable(path);
            } else if (second.getValueKey().equals(InnerModel.GLOBAL_VARIABLE.getModelKey())) {
                path.add(VariableType.GLOBAL.getKey());
                path.addAll(stages.subList(2, stages.size()).stream().map(VarValue.VarStage::getValueKey).collect(Collectors.toList()));
                return context.getVariable(path);
            } else if (second.getValueKey().equals(InnerModel.SERVICE_REQUEST.getModelKey())) {
                path.add(VariableType.REQUEST.getKey());
                path.addAll(stages.subList(2, stages.size()).stream().map(VarValue.VarStage::getValueKey).collect(Collectors.toList()));
                return context.getVariable(path);
            }
        } else {
            // 认为第一个是节点
            if (second.getValueKey().equals(InnerModel.NODE_OUTPUT.getModelKey())) {
                path.add(VariableType.GLOBAL.getKey());
                path.add(VariableType.NODE_OUTPUT.formatKey(first.getValueKey()));
                path.addAll(stages.subList(2, stages.size()).stream().map(VarValue.VarStage::getValueKey).collect(Collectors.toList()));
                return context.getVariable(path);
            } else if (second.getValueKey().equals(InnerModel.LOOP_ITEM.getModelKey())) {
                path.add(VariableType.LOOP.formatKey(first.getValueKey()));
                path.addAll(stages.subList(2, stages.size()).stream().map(VarValue.VarStage::getValueKey).collect(Collectors.toList()));
                return context.getVariable(path);
            } else {
                // 认为第二个是模型Key
                if (second.getRelatedModel() != null
                        && second.getRelatedModel().getModelKey() != null
                        && second.getRelatedModel().getModelKey().equals(second.getValueKey())) {
                    path.add(VariableType.GLOBAL.getKey());
                    path.add(VariableType.NODE_OUTPUT.formatKey(first.getValueKey()));
                    path.addAll(stages.subList(2, stages.size()).stream().map(VarValue.VarStage::getValueKey).collect(Collectors.toList()));
                    return context.getVariable(path);
                }
            }
        }
        return null;
    }

    private Object convertType(Field field, Object value) {
        if (Objects.isNull(value) || Objects.isNull(field)) {
            return value;
        } else {
            return ValueFactory.convertValue(field, value);
        }
    }
}
