package io.terminus.trantor2.service.engine.impl.function.impl;

import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.function.ServiceFunction;

import java.util.UUID;

/**
 * UUIDFunc
 *
 * <AUTHOR> Created on 2024/7/4 15:48
 */
@ServiceFunction(key = "UUID")
public class UUIDFunc extends FuncImpl {
    @Override
    public Object apply(Object[] objects, VariableContext context) {
        return UUID.randomUUID().toString().replace("-", "");
    }
}
