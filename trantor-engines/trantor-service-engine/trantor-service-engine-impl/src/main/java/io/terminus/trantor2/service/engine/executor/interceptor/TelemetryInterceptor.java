package io.terminus.trantor2.service.engine.executor.interceptor;

import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.common.AttributesBuilder;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.service.common.enums.ServiceType;
import io.terminus.trantor2.service.engine.ServiceEngine;
import io.terminus.trantor2.service.engine.executor.ServiceInvocation;
import lombok.RequiredArgsConstructor;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;

/**
 * 监控拦截器
 *
 * <AUTHOR> Created on 2024/5/17 10:44
 */
@RequiredArgsConstructor
public class TelemetryInterceptor extends ServiceInterceptor {

    private final ServiceEngine serviceEngine;

    @Override
    public Object execute(ServiceInvocation invocation) {
        if (serviceEngine.getServiceTelemetry().isNoop()) {
            return getNext().execute(invocation);
        } else {
            Instant start = Instant.now();
            try {
                Attributes attributes = createServiceTelemetryAttributes(invocation);
                if (invocation.getService().getMeta().getServiceType() == ServiceType.SPI) {
                    serviceEngine.getServiceTelemetry().getActionCounter().add(1, attributes);
                } else {
                    serviceEngine.getServiceTelemetry().getServiceCounter().add(1, attributes);
                }

                Object result = getNext().execute(invocation);

                if (invocation.getService().getMeta().getServiceType() == ServiceType.SPI) {
                    serviceEngine.getServiceTelemetry().getActionSuccCounter().add(1, attributes);
                } else {
                    serviceEngine.getServiceTelemetry().getServiceSuccCounter().add(1, attributes);
                }

                long cost = Duration.between(start, Instant.now()).toMillis();
                if (invocation.getService().getMeta().getServiceType() == ServiceType.SPI) {
                    serviceEngine.getServiceTelemetry().getActionHistogram().record(cost, attributes);
                } else {
                    serviceEngine.getServiceTelemetry().getServiceHistogram().record(cost, attributes);
                }

                return result;
            } catch (Throwable e) {
                Attributes errAttributes = createServiceTelemetryErrAttributes(invocation, e);
                if (invocation.getService().getMeta().getServiceType() == ServiceType.SPI) {
                    serviceEngine.getServiceTelemetry().getActionErrCounter().add(1, errAttributes);
                } else {
                    serviceEngine.getServiceTelemetry().getServiceErrCounter().add(1, errAttributes);
                }
                throw e;
            }
        }
    }

    private Attributes createServiceTelemetryAttributes(ServiceInvocation invocation) {
        AttributesBuilder builder = Attributes.builder();
        builder.put("module", KeyUtil.moduleKey(invocation.getServiceKey()))
                .put("project_code", serviceEngine.getTeamService().getTeamCode(invocation.getArgs().getTeamId()))
                .put("service_key", invocation.getServiceKey())
                .put("date", LocalDate.now().toString())
                .put("portal_key", TrantorContext.getPortalCode())
                .put("service_type", invocation.getService().getMeta().getServiceType().name())
                .put("service_name", invocation.getService().getMeta().getDefinition().getName());

        if (invocation.getService().getMeta().getServiceType() == ServiceType.SPI) {
            builder.put("action_key", invocation.getServiceKey())
                    .put("action_name", invocation.getService().getMeta().getDefinition().getName());
        }

        return builder.build();
    }

    private Attributes createServiceTelemetryErrAttributes(ServiceInvocation invocation, Throwable e) {
        AttributesBuilder builder = Attributes.builder();
        builder.put("module", KeyUtil.moduleKey(invocation.getServiceKey()))
                .put("project_code", serviceEngine.getTeamService().getTeamCode(invocation.getArgs().getTeamId()))
                .put("date", LocalDate.now().toString())
                .put("portal_key", TrantorContext.getPortalCode())
                .put("service_key", invocation.getServiceKey())
                .put("service_name", invocation.getService().getMeta().getDefinition().getName())
                .put("service_type", invocation.getService().getMeta().getServiceType().name())
                .put("error_code", e instanceof BusinessException ? ((BusinessException) e).getErrorCode() : e.getMessage());

        if (invocation.getService().getMeta().getServiceType() == ServiceType.SPI) {
            builder.put("action_key", invocation.getServiceKey())
                    .put("action_name", invocation.getService().getMeta().getDefinition().getName());
        }

        return builder.build();
    }
}
