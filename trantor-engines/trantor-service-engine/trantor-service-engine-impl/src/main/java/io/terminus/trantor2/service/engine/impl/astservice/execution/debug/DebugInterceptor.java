package io.terminus.trantor2.service.engine.impl.astservice.execution.debug;

import com.google.auto.service.AutoService;
import io.terminus.trantor2.service.common.exception.DebuggerException;
import io.terminus.trantor2.service.dsl.ExclusiveBranchNode;
import io.terminus.trantor2.service.dsl.ParallelBranchNode;
import io.terminus.trantor2.service.engine.impl.astservice.ExecutionContext;
import io.terminus.trantor2.service.engine.impl.astservice.execution.interceptor.NodeExecutionInterceptor;
import io.terminus.trantor2.service.engine.impl.debug.Breakpoint;
import io.terminus.trantor2.service.engine.impl.expression.ExpressionEvaluator;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * DebugInterceptor
 *
 * <AUTHOR> Created on 2024/10/21 09:42
 */
@SuppressWarnings("unused")
@AutoService(NodeExecutionInterceptor.class)
public class DebugInterceptor extends NodeExecutionInterceptor {

    @Override
    public void execute(ExecutionContext context) {
        DebugClientContext debugContext = context.getDebugContext();
        if (Objects.isNull(debugContext)) {
            next.execute(context);
            return;
        }

        if (getCurrentServiceElement() instanceof ExclusiveBranchNode
                || getCurrentServiceElement() instanceof ParallelBranchNode) {
            next.execute(context);
            return;
        }

        if (debugContext.isInterrupt()) {
            throw new DebuggerException("debugger interrupt");
        }

        if (debugContext.isStepOver()) {
            pause(debugContext);
        } else {
            Breakpoint breakpoint = debugContext.getBreakpoint(getCurrentServiceElement().getKey());
            if (breakpoint != null) {
                if (StringUtils.isNotBlank(breakpoint.getConditionExpression())) {
                    if (ExpressionEvaluator.evalBoolean(breakpoint.getConditionExpression(), context)) {
                        pause(debugContext);
                    }
                } else {
                    pause(debugContext);
                }
            }
        }

        next.execute(context);
    }

    private void pause(DebugClientContext debugContext) {
        debugContext.pause();
        debugContext.accept(getCurrentServiceElement());
        debugContext.await();
    }
}
