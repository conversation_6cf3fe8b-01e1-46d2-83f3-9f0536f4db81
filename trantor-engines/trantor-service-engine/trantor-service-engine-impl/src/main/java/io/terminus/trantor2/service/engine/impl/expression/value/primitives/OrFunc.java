package io.terminus.trantor2.service.engine.impl.expression.value.primitives;

import io.terminus.trantor2.service.engine.impl.expression.OperatorType;
import io.terminus.trantor2.service.engine.impl.expression.ParserException;
import io.terminus.trantor2.service.engine.impl.expression.ast.AbstractNode;
import io.terminus.trantor2.service.engine.impl.expression.value.BoolValue;
import io.terminus.trantor2.service.engine.impl.expression.value.NullValue;
import io.terminus.trantor2.service.engine.impl.expression.value.PrimFunc;
import io.terminus.trantor2.service.engine.impl.expression.value.Value;

import java.util.List;

/**
 * <AUTHOR>
 */
public class OrFunc implements PrimFunc {

    @Override
    public Value apply(List<Value> args, AbstractNode location) {
        if (args.size() != OperatorType.BINARY.getArgNum()) {
            throw new ParserException("and function args error:{}", location);
        }

        if (args.get(0) instanceof BoolValue && args.get(1) instanceof BoolValue) {
            BoolValue arg1 = (BoolValue) args.get(0);
            BoolValue arg2 = (BoolValue) args.get(1);
            return new BoolValue(arg1.getValue() || arg2.getValue());
        }

        if (args.get(0) instanceof BoolValue) {
            BoolValue arg1 = (BoolValue) args.get(0);
            if (Boolean.TRUE.equals(arg1.getValue())) {
                return new BoolValue(true);
            }
        }

        if (args.get(1) instanceof BoolValue) {
            BoolValue arg2 = (BoolValue) args.get(1);
            if (Boolean.TRUE.equals(arg2.getValue())) {
                return new BoolValue(true);
            }
        }

        if (args.get(0) instanceof NullValue || args.get(1) instanceof NullValue) {
            return new BoolValue(false);
        } else {
            throw new ParserException("or function args type error:{}", location);
        }
    }
}
