package io.terminus.trantor2.service.engine.executor.interceptor;

import com.google.common.base.Throwables;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorBizException;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.service.common.dto.ActionOpLog;
import io.terminus.trantor2.service.common.dto.AuditingLog;
import io.terminus.trantor2.service.common.exception.ServiceException;
import io.terminus.trantor2.service.engine.ServiceEngine;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * InvocationContext
 *
 * <AUTHOR> Created on 2023/5/9 20:28
 */
@Getter
@Slf4j
@RequiredArgsConstructor
public class InvocationContext implements Context {
    private final List<Runnable> closeListeners = new ArrayList<>(4);
    private final List<Runnable> successListeners = new ArrayList<>(4);
    private final List<Runnable> failureListeners = new ArrayList<>(4);

    private final ServiceEngine serviceEngine;

    // 审计日志
    private final List<AuditingLog> auditingLogs = new ArrayList<>(8);
    private final Map<String, ActionOpLog> actionLops = new HashMap<>(8);

    // 异常信息
    private Throwable exception;
    private String innerExceptionMsg;

    public void addActionLop(Map<String, ActionOpLog> logs) {
        if (serviceEngine.getServiceProperties().isAuditingLogEnabled()) {
            actionLops.putAll(logs);
        }
    }

    public void addAuditingLog(AuditingLog log) {
        if (serviceEngine.getServiceProperties().isAuditingLogEnabled()) {
            auditingLogs.add(log);
        }
    }

    protected void clearActionLogs() {
        actionLops.clear();
    }

    protected void clearAuditingLogs() {
        auditingLogs.clear();
    }

    public void addTransactionCommittedListener(Runnable listener) {
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(
                    new TransactionSynchronization() {
                        @Override
                        public void afterCommit() {
                            listener.run();
                        }
                    }
            );
        } else {
            // 不在事务中，则服务结束后执行
            addSuccessListener(listener);
        }
    }

    /**
     * 添加关闭监听器，服务不管成功还是失败，都会执行
     */
    public void addCloseListener(Runnable listener) {
        closeListeners.add(listener);
    }

    /**
     * 添加成功监听器，服务成功后执行
     */
    public void addSuccessListener(Runnable listener) {
        successListeners.add(listener);
    }

    /**
     * 添加失败监听器，服务失败后执行
     */
    public void addFailureListener(Runnable listener) {
        failureListeners.add(listener);
    }

    @Override
    public void close() {
        try {
            executeCloseListeners();
            if (exception == null) {
                executeSuccessListeners();
            }
        } catch (Throwable e) {
            exception(e);
        } finally {
            if (exception != null) {
                executeFailureListeners();
            }
            clearListeners();
            clearAuditingLogs();
            clearActionLogs();
        }

        if (exception != null) {
            throw getProcessedException();
        }
    }

    protected void executeCloseListeners() {
        if (!closeListeners.isEmpty()) {
            try {
                closeListeners.forEach(Runnable::run);
            } catch (Throwable e) {
                exception(e);
            }
        }
    }

    protected void executeSuccessListeners() {
        if (!successListeners.isEmpty()) {
            try {
                successListeners.forEach(Runnable::run);
            } catch (Throwable e) {
                exception(e);
            }
        }
    }

    protected void executeFailureListeners() {
        if (!failureListeners.isEmpty()) {
            try {
                failureListeners.forEach(Runnable::run);
            } catch (Throwable e) {
                exception(e);
            }
        }
    }

    private void clearListeners() {
        closeListeners.clear();
        successListeners.clear();
        failureListeners.clear();
    }

    public boolean isExceptionLogged() {
        return this.exception != null;
    }

    public void exception(Throwable exception) {
        exception(exception, null);
    }

    public void exception(Throwable exception, String innerExceptionMsg) {
        if (this.exception == null) {
            this.exception = exception;
            this.innerExceptionMsg = innerExceptionMsg;
        } else {
            if (this.innerExceptionMsg == null) {
                this.innerExceptionMsg = innerExceptionMsg;
            }
            if (this.exception != exception) {
                log.error("[{}] masked exception!!! because it's not root cause. error:{} \n {}",
                        Contexts.getTraceId(),
                        Throwables.getRootCause(exception).getMessage(),
                        Throwables.getStackTraceAsString(exception));
            }
        }
    }

    protected RuntimeException getProcessedException() {
        if (exception instanceof TrantorBizException trantorBizException) {
            if (innerExceptionMsg != null && trantorBizException.getInnerMsg() == null) {
                trantorBizException.setInnerMsg(innerExceptionMsg);
            }
            return trantorBizException;
        } else if (exception instanceof TrantorRuntimeException) {
            return (TrantorRuntimeException) exception;
        } else if (exception instanceof BusinessException) {
            return (BusinessException) exception;
        } else {
            return new ServiceException(ErrorType.SERVER_ERROR, exception.getMessage(), innerExceptionMsg, exception);
        }
    }

    public void resetException() {
        this.exception = null;
        this.innerExceptionMsg = null;
    }

    public void merge(InvocationContext context) {
        if (context == null) {
            return;
        }
        this.closeListeners.addAll(context.getCloseListeners());
        this.successListeners.addAll(context.getSuccessListeners());
        this.failureListeners.addAll(context.getFailureListeners());
        if (context.getException() != null) {
            this.exception(context.getException(), context.getInnerExceptionMsg());
        }
        if (context.getActionLops() != null) {
            this.addActionLop(context.getActionLops());
        }
        if (context.getAuditingLogs() != null) {
            context.getAuditingLogs().forEach(this::addAuditingLog);
        }
    }
}
