package io.terminus.trantor2.service.engine.impl.expression;

import io.terminus.trantor2.service.engine.impl.expression.value.PrimFunc;
import io.terminus.trantor2.service.engine.impl.expression.value.Value;
import io.terminus.trantor2.service.engine.impl.expression.value.primitives.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/27
 */
public abstract class Scope {

    protected final Map<String, Object> constVariables = new HashMap<>();
    protected final Map<String, PrimFunc> primFuncMap = new HashMap<>();

    public Scope() {
        this.constVariables.put("true", true);
        this.constVariables.put("false", false);
        this.buildIn();
    }

    public abstract Value lookup(String key);

    public PrimFunc lookupFunc(String func) {
        return primFuncMap.get(func);
    }

    private void buildIn() {
        primFuncMap.put(".", new FieldAccessFunc());
        primFuncMap.put("==", new EqualFunc());
        primFuncMap.put("!=", new NotEqualFunc());
        primFuncMap.put("+", new AddFunc());
        primFuncMap.put("&&", new AndFunc());
        primFuncMap.put("#EQ", new EqualFunc());
        primFuncMap.put("#NEQ", new NotEqualFunc());
        primFuncMap.put("#AND", new AndFunc());
        primFuncMap.put("#OR", new OrFunc());
        primFuncMap.put("#GT", new GreaterThanFunc());
        primFuncMap.put("#LT", new LessThanFunc());
        primFuncMap.put("#GTE", new GreaterOrEqualFunc());
        primFuncMap.put("#LTE", new LessOrEqualFunc());
        primFuncMap.put("#START_WITH", new StartWithFunc());
        primFuncMap.put("#END_WITH", new EndWithFunc());
        primFuncMap.put("#CONTAINS", new ContainsFunc());
        primFuncMap.put("#NOT_CONTAINS", new NotContainsFunc());
        primFuncMap.put("#IS_NULL", new IsNullFunc());
        primFuncMap.put("#IS_NOT_NULL", new IsNotNullFunc());
        primFuncMap.put("#BETWEEN_AND", new BetweenAndFunc());
        primFuncMap.put("#IN", new InFunc());
        primFuncMap.put("#NOT_IN", new NotInFunc());
    }

}
