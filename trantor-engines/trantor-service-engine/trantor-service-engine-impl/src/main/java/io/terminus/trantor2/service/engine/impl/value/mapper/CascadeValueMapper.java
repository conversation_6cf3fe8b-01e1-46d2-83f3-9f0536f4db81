package io.terminus.trantor2.service.engine.impl.value.mapper;

import com.google.common.collect.Lists;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.model.management.meta.consts.FieldType;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.enums.ModelRelationTypeEnum;
import io.terminus.trantor2.service.common.exception.ServiceException;
import io.terminus.trantor2.service.dsl.enums.Operator;
import io.terminus.trantor2.service.dsl.properties.RelatedModel;
import io.terminus.trantor2.service.dsl.properties.SelectField;
import io.terminus.trantor2.service.dsl.properties.VarValue;
import io.terminus.trantor2.service.engine.impl.component.bean.QueryModel;
import io.terminus.trantor2.service.engine.impl.component.bean.condition.GroupQueryCondition;
import io.terminus.trantor2.service.engine.impl.component.bean.condition.SingleQueryCondition;
import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * CascadeValueMapper
 *
 * <AUTHOR> Created on 2023/6/26 19:35
 */
@AllArgsConstructor
class CascadeValueMapper {

    private VariableContext variableContext;

    private List<VarValue.VarStage> varStages;

    private Map<String, Object> sourceValue;

    @SuppressWarnings({"unchecked", "rawtypes"})
    public Object getValue() {
        Object tempValue = sourceValue;
        VarValue.VarStage preStage = null;

        for (int i = 0; i < varStages.size(); i++) {
            VarValue.VarStage currentStage = varStages.get(i);

            Object temp = ((Map) tempValue).get(currentStage.getValueKey());
            if (Objects.isNull(temp)) {
                temp = fetchValue((Map) tempValue, preStage, currentStage);
            }

            if (Objects.isNull(temp)) {
                tempValue = null;
                break;
            }

            if (i < varStages.size() - 1) {
                if (MapUtil.isMap(temp)) {
                    temp = MapUtil.toMap(temp);
                } else {
                    throw new ServiceException(ErrorType.SERVICE_ASSIGN_NOT_MODEL_VALUE,
                        new Object[]{varStages.subList(0, i + 1).stream().map(VarValue.VarStage::getValueKey).collect(Collectors.joining("."))});
                }
            }

            // 设置值
            tempValue = temp;
            // 设置上一级Stage
            preStage = currentStage;
        }

        return tempValue;
    }

    private Object fetchValue(Map<String, Object> tempValue, VarValue.VarStage preStage, VarValue.VarStage currentStage) {
        if (preStage != null) {
            // 上一级是对象
            if (hasModelAlias(preStage.getRelatedModel())) {
                // 查询上一级值
                Map<String, Object> fullValue = findModelData(
                    preStage.getRelatedModel().getModelKey(), tempValue,
                    currentStage.getValueKey(),
                    hasModelAlias(currentStage.getRelatedModel()) ? currentStage.getRelatedModel().getModelKey() : null
                );
                if (fullValue != null) {
                    // 这里只能把 当前Stage的值 设置到父值中，不然会有值覆盖的问题
                    Object value = fullValue.get(currentStage.getValueKey());
                    if (value != null) {
                        tempValue.put(currentStage.getValueKey(), value);
                    }
                }
                return tempValue.get(currentStage.getValueKey());
            }
        }
        return null;
    }

    private boolean hasModelAlias(RelatedModel relatedModel) {
        return relatedModel != null && StringUtils.isNotBlank(relatedModel.getModelKey());
    }

    private Map<String, Object> findModelData(String modelAlias, Map<String, Object> tempValue,
                                              String fieldAlias, String subModelAlias) {

        Object id = tempValue.get("id");
        if (id == null) {
            return null;
        }

        List<SelectField> selects = convertSelects(fieldAlias, subModelAlias);

        SingleQueryCondition singleQueryCondition = new SingleQueryCondition();
        singleQueryCondition.setField("id");
        singleQueryCondition.setType(Operator.EQ);
        singleQueryCondition.setValue(Long.valueOf(id.toString()));
        GroupQueryCondition condition = new GroupQueryCondition(Collections.singletonList(singleQueryCondition));

        QueryModel queryModel = new QueryModel(variableContext.getTeamId(), modelAlias, condition, selects, null);
        return variableContext.getServiceEngine().getModelDataRepository().findOne(queryModel);
    }

    private List<SelectField> convertSelects(String fieldAlias, String subModelAlias) {

        SelectField selectField = new SelectField(fieldAlias);

        if (StringUtils.isNotBlank(subModelAlias)) {
            DataStructNode subModelMeta = variableContext.getServiceEngine().getModelMetaQuery().getModelDataStruct(variableContext.getTeamId(), subModelAlias);
            if (subModelMeta != null) {
                List<SelectField> subSelectFields = new ArrayList<>();

                subModelMeta.getChildren().forEach(f -> {
                    if (f.getProps().getFieldType() != FieldType.OBJECT
                        || f.getProps().getRelationMeta().getRelationType() != ModelRelationTypeEnum.LINK) {
                        subSelectFields.add(new SelectField(f.getAlias()));
                    }
                });

                selectField.setSelectFields(subSelectFields);
            }
        }

        return Lists.newArrayList(selectField);
    }
}
