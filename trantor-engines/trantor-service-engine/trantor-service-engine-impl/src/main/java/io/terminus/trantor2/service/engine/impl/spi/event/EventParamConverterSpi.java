package io.terminus.trantor2.service.engine.impl.spi.event;

import io.terminus.trantor2.service.common.consts.VariableKey;
import io.terminus.trantor2.service.common.spi.annotation.ServiceSPI;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.engine.impl.component.ModelMetaQuery;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 为事件服务提供参数转换，兼容前端一对一不同步关联关系字段也传 Map 情况
 *
 * <AUTHOR>
 * @since 2023/5/8 11:38 AM
 */
@Component
public class EventParamConverterSpi extends AbstractParamConverter {

    private static final String EVENT_PARAM_CONVERTER_SPI = "EVENT_PARAM_CONVERTER";

    public EventParamConverterSpi(ModelMetaQuery modelMetaQuery) {
        super(modelMetaQuery);
    }

    @Override
    @ServiceSPI(key = EVENT_PARAM_CONVERTER_SPI)
    public Map<String, Object> convert(Map<String, Object> request) {
        return super.convert(request);
    }

    /**
     * 前端传递给后端时，需要将一对一不同步关联关系字段由 Map 转成 ID 传递给 Pro Code
     *
     * @param data  当前模型数据 Map
     * @param field 当前关联关系字段
     */
    @Override
    protected void convertFieldValue(Map<String, Object> data, Field field) {
        Object obj = data.get(field.getFieldKey());
        if (obj instanceof Map) {
            data.put(field.getFieldKey(), ((Map) obj).get(VariableKey.ID));
        }
    }

}
