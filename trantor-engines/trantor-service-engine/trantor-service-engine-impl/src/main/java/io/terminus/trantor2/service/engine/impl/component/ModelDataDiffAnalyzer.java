package io.terminus.trantor2.service.engine.impl.component;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.google.common.base.CaseFormat;
import io.terminus.trantor.workflow.common.flow.FlowParamKey;
import io.terminus.trantor.workflow.common.utils.MapUtil;
import io.terminus.trantor.workflow.runtime.v1.activiti.model.FieldRecord;
import io.terminus.trantor.workflow.runtime.v1.activiti.model.LineRecord;
import io.terminus.trantor2.model.management.meta.consts.DataStructType;
import io.terminus.trantor2.model.management.meta.consts.FieldType;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.enums.ModelRelationTypeEnum;
import io.terminus.trantor2.service.common.consts.VariableKey;
import io.terminus.trantor2.service.dsl.enums.Operator;
import io.terminus.trantor2.service.engine.impl.component.bean.QueryModel;
import io.terminus.trantor2.service.engine.impl.component.bean.condition.GroupQueryCondition;
import io.terminus.trantor2.service.engine.impl.component.bean.condition.SingleQueryCondition;
import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ModelDataDiffAnalyzer
 *
 * <AUTHOR> Created on 2024/4/25 00:11
 */
@UtilityClass
public class ModelDataDiffAnalyzer {

    /**
     * 计算单据变更记录
     *
     * @param mainModelKey 单据主模型 Key
     * @param modelMetas   单据主模型及关联模型数据结构
     * @param before       变更前单据记录
     * @param after        变更后单据记录
     * @return 单据变更记录
     */
    public List<FieldRecord> diff(String mainModelKey,
                                  Collection<DataStructNode> modelMetas,
                                  Map<String, Object> before,
                                  Map<String, Object> after,
                                  VariableContext context) {
        List<FieldRecord> fieldRecordList = new ArrayList<>();

        if (!MapUtils.isEmpty(before) && !MapUtils.isEmpty(after) && !CollectionUtils.isEmpty(modelMetas)) {
            Map<String, DataStructNode> modelMetaMap = modelMetas.stream().collect(Collectors.toMap(DataStructNode::getKey, d -> d));
            diffFields(mainModelKey, fieldRecordList, before, after, modelMetaMap, context);
        }

        return fieldRecordList;
    }

    private void diffFields(String modelKey,
                            List<FieldRecord> fieldRecordList,
                            Map<String, Object> before,
                            Map<String, Object> after,
                            Map<String, DataStructNode> modelMetaMap,
                            VariableContext context) {
        DataStructNode dataStructNode = modelMetaMap.get(modelKey);
        if (Objects.isNull(dataStructNode)) {
            return;
        }
        Map<String, DataStructFieldNode> fieldMap = dataStructNode.toFieldMap();

        // 处理当前值有但与原值不同的字段
        for (Map.Entry<String, Object> entry : after.entrySet()) {
            String key = entry.getKey();
            if (skip(key)) {
                continue;
            }

            Object presentValue = entry.getValue();
            Object originalValue = before.remove(key);

            DataStructFieldNode fieldNode = fieldMap.get(key);
            if (Objects.isNull(fieldNode) || Objects.equals(fieldNode.getProps().getIsSystemField(), true)) {
                continue;
            }

            if (!FieldType.OBJECT.equals(fieldNode.getProps().getFieldType())) {
                if (compare(presentValue, originalValue)) {
                    FieldRecord fieldRecord = new FieldRecord();
                    fieldRecord.setFieldKey(fieldNode.getAlias());
                    fieldRecord.setFieldName(fieldNode.getName());
                    fieldRecord.setPresentValue(presentValue);
                    fieldRecord.setOriginalValue(originalValue);
                    fieldRecord.setDataStructFieldNode(fieldNode);
                    fieldRecordList.add(fieldRecord);
                }
            } else {
                if (ModelRelationTypeEnum.PARENT_CHILD.equals(fieldNode.getProps().getRelationMeta().getRelationType())) {
                    if (originalValue instanceof List && presentValue instanceof List) {
                        diffListField(fieldNode, castToListOrEmpty(originalValue), castToListOrEmpty(presentValue), fieldRecordList, modelMetaMap, context);
                    }
                } else if ((originalValue instanceof Map || presentValue instanceof Map)) {
                    if (Objects.equals(fieldNode.getProps().getRelationMeta().isSync(), true)) {
                        diffMapSyncField(fieldNode, castToMapOrEmpty(originalValue), castToMapOrEmpty(presentValue), fieldRecordList, modelMetaMap, context);
                    } else {
                        diffMapNotSyncField(fieldNode, castToMapOrEmpty(originalValue), castToMapOrEmpty(presentValue), fieldRecordList, modelMetaMap, context);
                    }
                }
            }
        }

        // 处理当前值没有但原值有的字段
        for (Map.Entry<String, Object> entry : before.entrySet()) {
            String key = entry.getKey();
            Object originalValue = entry.getValue();

            DataStructFieldNode fieldNode = fieldMap.get(key);

            if (Objects.isNull(fieldNode) || Objects.equals(fieldNode.getProps().getIsSystemField(), true)
                || Objects.isNull(originalValue)) {
                continue;
            }

            if (originalValue instanceof List && CollectionUtils.isEmpty((Collection) originalValue)) {
                continue;
            }

            if (!FieldType.OBJECT.equals(fieldNode.getProps().getFieldType())) {
                FieldRecord fieldRecord = new FieldRecord();
                fieldRecord.setFieldKey(fieldNode.getAlias());
                fieldRecord.setFieldName(fieldNode.getName());
                fieldRecord.setOriginalValue(originalValue);
                fieldRecord.setDataStructFieldNode(fieldNode);
                fieldRecordList.add(fieldRecord);
            } else {
                if (ModelRelationTypeEnum.PARENT_CHILD.equals(fieldNode.getProps().getRelationMeta().getRelationType())) {
                    if (originalValue instanceof List) {
                        diffListField(fieldNode, castToListOrEmpty(originalValue), new ArrayList<>(), fieldRecordList, modelMetaMap, context);
                    }
                } else if (originalValue instanceof Map) {
                    if (Objects.equals(fieldNode.getProps().getRelationMeta().isSync(), true)) {
                        diffMapSyncField(fieldNode, castToMapOrEmpty(originalValue), new HashMap<>(), fieldRecordList, modelMetaMap, context);
                    } else {
                        diffMapNotSyncField(fieldNode, castToMapOrEmpty(originalValue), new HashMap<>(), fieldRecordList, modelMetaMap, context);
                    }
                }
            }
        }

    }

    private void diffListField(DataStructFieldNode field,
                               List<Map<String, Object>> before,
                               List<Map<String, Object>> after,
                               List<FieldRecord> fieldRecordList,
                               Map<String, DataStructNode> modelMetaMap,
                               VariableContext context) {
        DataStructNode modelMeta = modelMetaMap.get(field.getProps().getRelationMeta().getRelationModelAlias());
        if (Objects.isNull(modelMeta)) {
            return;
        }

        Map<String, DataStructFieldNode> fieldMap = modelMeta.toFieldMap();
        String mainField = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, modelMeta.getProps().getMainField());
        Map<String, Map<String, Object>> beforeMap = before.stream().collect(Collectors.toMap(m -> getLineKey(mainField, m), m -> m));
        List<LineRecord> lineRecordList = new ArrayList<>();

        // 处理当前值有但与原值不同的行
        for (Map<String, Object> afterLine : after) {
            String key = getLineKey(mainField, afterLine);
            Map<String, Object> beforeLine = beforeMap.remove(key);

            if (Objects.isNull(beforeLine)) {
                beforeLine = new HashMap<>();
            }

            LineRecord lineRecord = new LineRecord();
            lineRecord.setMainFieldKey(mainField);
            lineRecord.setMainFieldName(fieldMap.get(mainField).getName());
            Object mainFieldValue = afterLine.get(mainField);
            DataStructFieldNode fieldMeta = fieldMap.get(mainField);
            lineRecord.setMainFieldValue(getMainFieldValue(mainFieldValue, fieldMeta, modelMetaMap));

            List<FieldRecord> lineFieldRecordList = new ArrayList<>();
            diffFields(field.getProps().getRelationMeta().getRelationModelAlias(), lineFieldRecordList, beforeLine, afterLine, modelMetaMap, context);

            if (CollectionUtils.isEmpty(lineFieldRecordList)) {
                continue;
            }

            lineRecord.setFieldRecordList(lineFieldRecordList);
            lineRecordList.add(lineRecord);
        }

        // 处理当前值没有但原值有的行
        for (Map.Entry<String, Map<String, Object>> entry : beforeMap.entrySet()) {
            Map<String, Object> beforeLine = entry.getValue();
            LineRecord lineRecord = new LineRecord();
            lineRecord.setMainFieldKey(mainField);
            lineRecord.setMainFieldName(fieldMap.get(mainField).getName());

            Object mainFieldValue = beforeLine.get(mainField);
            DataStructFieldNode mainDataStructFieldNode = fieldMap.get(mainField);
            lineRecord.setMainFieldValue(getMainFieldValue(mainFieldValue, mainDataStructFieldNode, modelMetaMap));

            List<FieldRecord> lineFieldRecordList = new ArrayList<>();
            diffFields(field.getProps().getRelationMeta().getRelationModelAlias(), lineFieldRecordList, beforeLine, new HashMap<>(), modelMetaMap, context);

            if (CollectionUtils.isEmpty(lineFieldRecordList)) {
                continue;
            }

            lineRecord.setFieldRecordList(lineFieldRecordList);
            lineRecordList.add(lineRecord);
        }

        if (CollectionUtils.isEmpty(lineRecordList)) {
            return;
        }

        FieldRecord fieldRecord = new FieldRecord();
        fieldRecord.setFieldKey(field.getAlias());
        fieldRecord.setFieldName(field.getName());
        fieldRecord.setDataStructFieldNode(field);
        fieldRecord.setLineRecordList(lineRecordList);
        fieldRecordList.add(fieldRecord);
    }

    private static Object getMainFieldValue(Object mainFieldValue,
                                            DataStructFieldNode field,
                                            Map<String, DataStructNode> modelMetaMap) {
        if (mainFieldValue instanceof Map) {
            Map<String, Object> mainFieldValueMap = (Map<String, Object>) mainFieldValue;
            DataStructNode modelMeta = modelMetaMap.get(field.getProps().getRelationMeta().getRelationModelAlias());
            String subMainField = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, modelMeta.getProps().getMainField());
            return mainFieldValueMap.get(subMainField);
        } else {
            return mainFieldValue;
        }
    }

    private void diffMapNotSyncField(DataStructFieldNode field,
                                     Map<String, Object> before,
                                     Map<String, Object> after,
                                     List<FieldRecord> fieldRecordList,
                                     Map<String, DataStructNode> modelMetaMap,
                                     VariableContext context) {
        FieldRecord fieldRecord = new FieldRecord();
        fieldRecord.setFieldKey(field.getAlias());
        fieldRecord.setFieldName(field.getName());
        fieldRecord.setDataStructFieldNode(field);

        List<FieldRecord> lineFieldRecordList = new ArrayList<>();
        if (compare(before.get("id"), after.get("id"))) {
            DataStructNode dataStructNode = modelMetaMap.get(field.getProps().getRelationMeta().getRelationModelAlias());
            if (Objects.isNull(dataStructNode)) {
                return;
            }
            fieldRecord.setDataStructNode(dataStructNode);
            Map<String, DataStructFieldNode> dataStructFieldNodeMap = dataStructNode.toFieldMap();
            String mainFieldKey = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, dataStructNode.getProps().getMainField());
            DataStructFieldNode mainField = dataStructFieldNodeMap.get(mainFieldKey);
            DataStructFieldNode idField = dataStructFieldNodeMap.get("id");

            FieldRecord idFieldRecord = new FieldRecord();
            idFieldRecord.setFieldKey("id");
            idFieldRecord.setFieldName(idField.getName());
            idFieldRecord.setPresentValue(after.get("id"));
            idFieldRecord.setOriginalValue(before.get("id"));
            idFieldRecord.setDataStructFieldNode(idField);
            lineFieldRecordList.add(idFieldRecord);

            FieldRecord mainFieldRecord = new FieldRecord();
            mainFieldRecord.setFieldKey(mainField.getAlias());
            mainFieldRecord.setFieldName(mainField.getName());

            if (Objects.isNull(after.get(mainField.getAlias()))) {
                fillMainFieldValue(after, dataStructNode, context);
            }

            if (Objects.isNull(before.get(mainField.getAlias()))) {
                fillMainFieldValue(before, dataStructNode, context);
            }

            mainFieldRecord.setPresentValue(after.get(mainField.getAlias()));
            mainFieldRecord.setOriginalValue(before.get(mainField.getAlias()));
            mainFieldRecord.setDataStructFieldNode(mainField);
            lineFieldRecordList.add(mainFieldRecord);
        }

        if (CollectionUtils.isEmpty(lineFieldRecordList)) {
            return;
        }

        fieldRecord.setFieldRecordList(lineFieldRecordList);
        fieldRecordList.add(fieldRecord);
    }

    private void diffMapSyncField(DataStructFieldNode field,
                                  Map<String, Object> before,
                                  Map<String, Object> after,
                                  List<FieldRecord> fieldRecordList,
                                  Map<String, DataStructNode> modelMetaMap,
                                  VariableContext context) {
        FieldRecord fieldRecord = new FieldRecord();
        fieldRecord.setFieldKey(field.getAlias());
        fieldRecord.setFieldName(field.getName());
        fieldRecord.setDataStructFieldNode(field);

        List<FieldRecord> lineFieldRecordList = new ArrayList<>();
        diffFields(field.getProps().getRelationMeta().getRelationModelAlias(),
            lineFieldRecordList, before, after, modelMetaMap, context);

        if (CollectionUtils.isEmpty(lineFieldRecordList)) {
            return;
        }

        fieldRecord.setFieldRecordList(lineFieldRecordList);
        fieldRecordList.add(fieldRecord);
    }

    private static String getLineKey(String mainField, Map<String, Object> m) {
        Object mainFieldValue = m.get(mainField);
        if (mainFieldValue instanceof Map) {
            Map<String, Object> mainFieldValueMap = (Map<String, Object>) mainFieldValue;
            return m.get("id") + "_" + mainFieldValueMap.get("id");
        } else {
            return m.get("id") + "_" + mainFieldValue;
        }
    }

    private static List<Map<String, Object>> castToListOrEmpty(Object obj) {
        return Objects.isNull(obj) ? Collections.emptyList() : (List<Map<String, Object>>) obj;
    }

    private static Map<String, Object> castToMapOrEmpty(Object obj) {
        if (obj instanceof Map) {
            return (Map<String, Object>) obj;
        } else if (obj instanceof Number) {
            return MapUtil.of("id", obj);
        } else {
            return Collections.emptyMap();
        }
    }

    /**
     * 过滤非模型字段
     *
     * @param key 字段 Key
     * @return 是否需要过滤
     */
    private static Boolean skip(String key) {
        if (key.startsWith("#")) {
            return true;
        } else {
            return Objects.equals(FlowParamKey.COOKIE, key) || Objects.equals(FlowParamKey.REFERER, key);
        }
    }

    /**
     * 如果当前模型主字段不存在，补充当前模型主字段
     *
     * @param paramMap  当前模型数据
     * @param modelMeta 模型元信息
     */
    private void fillMainFieldValue(Map<String, Object> paramMap, DataStructNode modelMeta, VariableContext context) {
        if (Objects.isNull(modelMeta) || Objects.isNull(modelMeta.getProps())) {
            return;
        }
        String mainField = modelMeta.getProps().getMainField();
        // 过滤用户查询
        if (Objects.isNull(paramMap.get(mainField)) && Objects.equals(modelMeta.getProps().getType(), DataStructType.PERSIST)) {
            // 设置条件 id = ${全局变量.服务入参.id}
            SingleQueryCondition condition = new SingleQueryCondition();
            condition.setField(VariableKey.ID);
            condition.setType(Operator.EQ);
            condition.setValue(paramMap.get("id"));

            GroupQueryCondition group = new GroupQueryCondition();
            group.addCondition(condition);

            QueryModel queryModel = new QueryModel(context.getTeamId(), modelMeta.getKey(), group);

            Map<String, Object> result = context.getServiceEngine().getModelDataRepository().findOne(queryModel);
            if (result != null) {
                BeanUtil.copyProperties(result, paramMap, CopyOptions.create().setOverride(false));
            }
        }
    }

    private boolean compare(Object o1, Object o2) {
        if (o1 instanceof Number && o2 instanceof Number) {
            return !Double.valueOf(o1.toString()).equals(Double.valueOf(o2.toString()));
        } else if (Objects.nonNull(o1) && Objects.nonNull(o2)) {
            return !Objects.equals(o1.toString(), o2.toString());
        } else {
            return !Objects.equals(o1, o2);
        }
    }
}
