package io.terminus.trantor2.service.engine.ai.llm.tool.execution;

import com.fasterxml.jackson.core.type.TypeReference;
import com.openai.core.JsonValue;
import com.openai.models.chat.completions.ChatCompletionTool;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.dsl.properties.ServiceTool;
import io.terminus.trantor2.service.dsl.properties.SkillTool;
import io.terminus.trantor2.service.engine.ai.context.AIContext;
import io.terminus.trantor2.service.engine.ai.llm.util.ToolCallUtil;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.executor.ServiceExecutor;
import io.terminus.trantor2.service.engine.impl.value.ValueFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 服务工具执行器
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ServiceToolExecution extends ToolCallExecution {

    private final ServiceExecutor serviceExecutor;

    @Override
    public ChatCompletionTool buildTool(SkillTool tool) {
        if (!(tool instanceof ServiceTool serviceTool)) {
            throw new IllegalArgumentException("Tool must be ServiceTool instance");
        }
        List<Field> fields = serviceTool.getInput();
        Map<String, JsonValue> additionalProperty = buildToolCallInputSchema(fields);

        log.info("build service tool: {}({})] params json schema properties: {}", serviceTool.getKey(), serviceTool.getName(), additionalProperty);
        return buildChatCompletionTool(ToolCallUtil.getServiceKeyInLlmToolName(serviceTool.getKey()), serviceTool.getDesc(), additionalProperty);
    }

    @Override
    public String execute(SkillTool tool, String toolName, String toolArguments) {
        if (!(tool instanceof ServiceTool serviceTool)) {
            throw new IllegalArgumentException("Tool must be ServiceTool instance");
        }

        if (!ToolCallUtil.getServiceKeyInLlmToolName(serviceTool.getKey()).equals(toolName)) {
            log.warn("Tool name mismatch: expected {}, got {}", KeyUtil.shortKey(serviceTool.getKey()), toolName);
            return "";
        }

        Map<String, Object> payload = new HashMap<>();
        if (StringUtils.isNotBlank(toolArguments)) {
            payload = JsonUtil.fromJson(toolArguments, new TypeReference<Map<String, Object>>() {
            });
        }

        log.info("调用编排服务工具: {}[{}({})], arguments: {}", toolName, serviceTool.getKey(), serviceTool.getName(), toolArguments);
        Map<String, Object> defaultPayload = ValueFactory.getDefaultValue(serviceTool.getInput(), AIContext.getContext().getVariableContext());
        payload.putAll(defaultPayload);
        log.info("混合使用参数: {}", payload);
        Object invokeResponse = serviceExecutor.execute(tool.getKey(), Arguments.of(TrantorContext.getTeamId(), payload));
        log.info("调用编排服务结果: {}", JsonUtil.toNonIndentJson(invokeResponse));
        return JsonUtil.toJson(invokeResponse);
    }
}
