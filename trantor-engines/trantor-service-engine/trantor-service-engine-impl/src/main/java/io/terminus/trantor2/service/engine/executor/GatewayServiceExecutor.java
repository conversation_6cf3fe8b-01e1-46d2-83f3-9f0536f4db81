package io.terminus.trantor2.service.engine.executor;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.exception.ServiceNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * GatewayServiceExecutor
 *
 * <AUTHOR> Created on 2023/5/15 15:34
 */
@Slf4j
@RequiredArgsConstructor
public class GatewayServiceExecutor implements ServiceExecutor {

    private final List<RoutingServiceExecutor> routingServiceExecutors;
    private final ServiceExecutor defaultExecutor;

    @Override
    public Object execute(String serviceKey, Arguments arguments) {
        if (StringUtils.isBlank(serviceKey)) {
            throw new ServiceNotFoundException(ErrorType.SERVICE_NOT_FOUND, new Object[]{serviceKey});
        }

        final ServiceExecutor serviceExecutor = routingServiceExecutors.stream()
                .filter(executor -> executor.accept(serviceKey, arguments))
                .findFirst()
                .orElse(null);
        if (serviceExecutor != null) {
            return serviceExecutor.execute(serviceKey, arguments);
        } else {
            return defaultExecutor.execute(serviceKey, arguments);
        }
    }
}
