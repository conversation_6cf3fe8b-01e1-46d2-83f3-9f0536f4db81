package io.terminus.trantor2.service.engine.ai.core.message;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * NotifyContent
 *
 * <AUTHOR> Created on 2025/6/10 09:34
 */
@Setter
@Getter
@NoArgsConstructor
public class NotifyContent implements Content {
    private static final long serialVersionUID = 4659567099722853355L;

    private ContentType type = ContentType.NOTIFY;
    private AIProgressStatus status = AIProgressStatus.DONE;

    private String sessionId;
    private String agentKey;

    public NotifyContent(String sessionId, String agentKey) {
        this.sessionId = sessionId;
        this.agentKey = agentKey;
    }
}
