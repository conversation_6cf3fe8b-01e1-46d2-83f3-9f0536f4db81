package io.terminus.trantor2.service.engine.ai.core.chat.session;

import com.google.common.collect.Lists;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.service.dsl.Agent;
import io.terminus.trantor2.service.engine.ai.client.httpclient.StreamingHandler;
import io.terminus.trantor2.service.engine.ai.core.memory.MemoryManager;
import io.terminus.trantor2.service.engine.ai.core.message.Message;
import io.terminus.trantor2.service.engine.ai.core.message.MessageI18nProcessor;
import io.terminus.trantor2.service.engine.ai.core.session.Session;
import io.terminus.trantor2.service.engine.ai.core.session.SessionDispatcher;
import io.terminus.trantor2.service.engine.ai.memory.AIMemoryManager;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import java.time.Duration;
import java.util.List;
import java.util.UUID;

/**
 * ChatSessionManager
 *
 * <AUTHOR> Created on 2025/4/9 19:30
 */
public class ChatSessionManager {

    private static final String MAPPING_KEY = "session:user:mapping:%s:%s";

    private final MemoryManager memoryManager;
    private final SessionDispatcher sessionDispatcher;
    private final TaskHelper taskHelper;
    private final RedissonClient redissonClient;
    private final AIMemoryManager aiMemoryManager;
    private final MessageI18nProcessor messageI18nProcessor;

    public ChatSessionManager(SessionDispatcher sessionDispatcher,
                              TaskHelper taskHelper,
                              MemoryManager memoryManager,
                              RedissonClient redissonClient,
                              AIMemoryManager aiMemoryManager,
                              MessageI18nProcessor messageI18nProcessor) {
        this.memoryManager = memoryManager;
        this.taskHelper = taskHelper;
        this.sessionDispatcher = sessionDispatcher;
        this.aiMemoryManager = aiMemoryManager;
        this.messageI18nProcessor = messageI18nProcessor;
        this.sessionDispatcher.registerHandler("chat", (session, messages) -> {
            session.onStart();
            for (Message message : messages) {
                session.onMessage(message);
            }
            session.onComplete();
        });
        this.redissonClient = redissonClient;
    }

    /**
     * 创建会话
     */
    public ChatSession create(String sessionId, String bizKey, StreamingHandler handler, boolean register) {
        return create(sessionId, bizKey, TrantorContext.getCurrentUserId(), handler, register);
    }

    /**
     * 创建会话
     */
    public ChatSession create(String sessionId, String bizKey, Long userId, StreamingHandler handler, boolean register) {
        if (handler == null) {
            handler = getDefaultStreamingHandler();
        }

        if (sessionId == null) {
            sessionId = createNewSessionId(bizKey, userId);
        }

        ChatSession session = new ChatSession(sessionId, bizKey, userId, handler);
        session.setMemoryManager(memoryManager);
        session.setMessageI18nProcessor(messageI18nProcessor);
        session.setTaskHelper(taskHelper);

        if (register) {
            session.addCloseListener(this::remove);
            register(session);
        }

        return session;
    }

    public String createNewSessionId(String bizKey, Long currentUserId) {
        String sessionId = genSessionId();
        // 把会话ID存储到用户会话映射中
        saveNewChatSessionId(bizKey, currentUserId, sessionId);
        return sessionId;
    }

    private void saveNewChatSessionId(String bizKey, Long currentUserId, String sessionId) {
        if (StringUtils.isBlank(bizKey) || currentUserId == null) {
            return;
        }
        RBucket<String> userSessionTable = redissonClient.getBucket(String.format(MAPPING_KEY, currentUserId, bizKey));
        userSessionTable.set(sessionId, Duration.ofHours(24));
    }

    public String getLastChatSessionId(String bizKey, Long currentUserId) {
        RBucket<String> userSessionTable = redissonClient.getBucket(String.format(MAPPING_KEY, currentUserId, bizKey));
        String sessionId = userSessionTable.get();
        if (StringUtils.isBlank(sessionId)) {
            return createNewSessionId(bizKey, currentUserId);
        }
        return sessionId;
    }

    private String genSessionId() {
        return UUID.randomUUID().toString();
    }

    private StreamingHandler getDefaultStreamingHandler() {
        return new StreamingHandler() {
        };
    }

    /**
     * 注册会话到分布式管理器
     */
    public void register(Session chatSession) {
        sessionDispatcher.registerSession(sessionKey(chatSession.getSessionId()), chatSession);
    }

    /**
     * 检查Session是否存在
     */
    public boolean exists(String sessionId) {
        return sessionDispatcher.existsSession(sessionKey(sessionId));
    }

    /**
     * 移除会话
     */
    public void remove(Session session) {
        sessionDispatcher.unregisterSession(sessionKey(session.getSessionId()), session);
    }

    /**
     * 发送消息给指定用户
     * 自动处理跨服务器路由
     *
     * @param message 消息数据
     * @return 是否发送成功
     */
    public boolean sendMessage(String sessionId, Message message) {
        return sessionDispatcher.sendMessage(sessionKey(sessionId), Lists.newArrayList(message));
    }

    public boolean sendMessages(String sessionId, List<Message> message) {
        return sessionDispatcher.sendMessage(sessionKey(sessionId), message);
    }

    public String generateMemoryPrompt(Agent agent) {
        return aiMemoryManager.generateMemoryPrompt(agent);
    }

    /**
     * 生成Chat类型的sessionKey
     */
    private String sessionKey(String sessionId) {
        return "chat:" + sessionId;
    }
}
