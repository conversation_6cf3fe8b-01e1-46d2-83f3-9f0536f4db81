package io.terminus.trantor2.service.engine.impl.sysservice;

import com.google.auto.service.AutoService;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.service.common.consts.ServiceConst;
import io.terminus.trantor2.service.common.consts.VariableKey;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.dsl.enums.Propagation;
import io.terminus.trantor2.service.engine.exception.ServiceExecuteException;
import io.terminus.trantor2.service.engine.impl.component.ModelDataProcessHelper;
import io.terminus.trantor2.service.engine.impl.component.bean.QueryModel;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * (系统)树查询数据服务
 *
 * <AUTHOR>
 * @since 2023/3/21 11:07 上午
 */
@AutoService(SystemService.class)
public class FindTreeDataService extends AbstractSystemServiceImpl implements SystemService {

    public static final String KEY = "SYS_FindTreeDataService";

    @Override
    public String getServiceKey() {
        return KEY;
    }

    @Override
    public String getServiceName() {
        return "(系统)树查询数据服务";
    }

    @Override
    protected void supplyDefinition(ServiceDefinition definition) {
        definition.setTransactionPropagation(Propagation.NOT_SUPPORTED);
        definition.addInput(MODEL_KEY_FIELD);
        definition.addInput(MODEL_REQUEST);
        definition.addOutput(ARRAY_MODEL_DATA_OUTPUT);
    }

    @Override
    protected void execute(SystemServiceExecContext context) {
        String parentFieldKey = getParentFieldKey(context.getModelKey(), context);
        if (StringUtils.isBlank(parentFieldKey)) {
            throw new ServiceExecuteException(ErrorType.SERVICE_EXECUTE_MODEL_NOT_TREE, new Object[]{context.getModelKey()});
        }

        QueryModel queryModel = ModelDataProcessHelper.buildRootNodeCondition(parentFieldKey, context);
        initFindDataPermissions(KEY, context, queryModel);
        List<Map<String, Object>> results = queryTreeData(context, parentFieldKey, queryModel);

        context.setResult(results);
    }

    private List<Map<String, Object>> queryTreeData(SystemServiceExecContext context, String parentFieldKey, QueryModel queryModel) {
        List<Map<String, Object>> results = context.getServiceEngine().getModelDataRepository().find(queryModel, ServiceConst.MAX_COUNT);
        if (CollectionUtils.isNotEmpty(results)) {
            for (Map<String, Object> result : results) {
                findTreeChildren(parentFieldKey, context, result);
            }
        }
        return results;
    }

    private void findTreeChildren(String parentFieldKey, SystemServiceExecContext context, Map<String, Object> parent) {
        QueryModel queryModel = ModelDataProcessHelper.buildEqFieldCondition(parentFieldKey, parent.get(VariableKey.ID), context.getModelKey(), context);
        List<Map<String, Object>> results = queryTreeData(context, parentFieldKey, queryModel);
        parent.put("#children", results);
    }

}
