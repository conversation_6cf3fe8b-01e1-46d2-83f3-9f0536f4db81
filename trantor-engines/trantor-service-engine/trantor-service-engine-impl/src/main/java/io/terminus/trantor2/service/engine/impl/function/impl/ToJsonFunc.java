package io.terminus.trantor2.service.engine.impl.function.impl;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.function.ServiceFunction;

/**
 * ToJsonFunc
 *
 * <AUTHOR> Created on 2023/12/1 16:38
 */
@ServiceFunction(key = "TO_JSON")
public class ToJsonFunc extends FuncImpl {

    @Override
    public Object apply(Object[] objects, VariableContext context) {
        if (objects == null || objects.length == 0) {
            return "";
        }
        return JsonUtil.toNonIndentJson(objects[0]);
    }
}
