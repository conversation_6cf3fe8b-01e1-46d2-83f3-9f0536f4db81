package io.terminus.trantor2.service.engine.impl.sysservice;

import com.google.auto.service.AutoService;

/**
 * FindSingleDataByConditionService
 *
 * <AUTHOR> Created on 2025/5/29 16:36
 */
@AutoService(SystemService.class)
public class FindSingleDataByConditionService extends FindOneDataService {

    public static final String KEY = "SYS_FindSingleDataByConditionService";

    @Override
    public String getServiceKey() {
        return KEY;
    }

    @Override
    public String getServiceName() {
        return "(系统)根据条件查询单条数据服务";
    }
}
