package io.terminus.trantor2.service.engine.impl.debug.server;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.common.utils.HttpClient;
import io.terminus.trantor2.service.engine.impl.debug.DebugBreakpoint;
import io.terminus.trantor2.service.engine.impl.debug.DebugData;
import io.terminus.trantor2.service.engine.impl.debug.DebugEndpoint;
import io.terminus.trantor2.service.engine.impl.debug.DebugLocation;
import io.terminus.trantor2.service.engine.impl.debug.Debugger;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * DebuggerServer
 *
 * <AUTHOR> Created on 2024/10/21 11:44
 */
@Component
public class DebuggerServer implements Debugger {

    private static final String CLIENT_URI = "/api/trantor/devops/service/debug/client";

    @Override
    public void addBreakpoint(DebugBreakpoint breakpoint) {
        DebugServerContext debugContext = DebugServerContextManager.getDebugContext(breakpoint.getEndpoint());
        if (debugContext != null) {
            HttpClient.INSTANCE.post(getCallback(breakpoint.getEndpoint()) + CLIENT_URI + "/breakpoint/add", breakpoint);
        } else {
            DebugBreakpointBuffer.save(breakpoint);
        }
    }

    @Override
    public void removeBreakpoint(DebugBreakpoint breakpoint) {
        DebugServerContext debugContext = DebugServerContextManager.getDebugContext(breakpoint.getEndpoint());
        if (debugContext != null) {
            HttpClient.INSTANCE.post(getCallback(breakpoint.getEndpoint()) + CLIENT_URI + "/breakpoint/remove", breakpoint);
        } else {
            DebugBreakpointBuffer.remove(breakpoint);
        }
    }

    @Override
    public void stepOver(DebugEndpoint endpoint) {
        HttpClient.INSTANCE.put(getCallback(endpoint) + CLIENT_URI + "/step-over", endpoint);
    }

    @Override
    public void setStepOver(DebugEndpoint endpoint) {
        HttpClient.INSTANCE.put(getCallback(endpoint) + CLIENT_URI + "/set-step-over", endpoint);
    }

    @Override
    public void resume(DebugEndpoint endpoint) {
        HttpClient.INSTANCE.put(getCallback(endpoint) + CLIENT_URI + "/resume", endpoint);
    }

    @Override
    public void interrupt(DebugEndpoint endpoint) {
        try {
            HttpClient.INSTANCE.put(getCallback(endpoint) + CLIENT_URI + "/interrupt", endpoint);
        } finally {
            DebugServerContextManager.removeDebugContext(endpoint.getDebuggerId());
        }
    }

    @Override
    public DebugData getDebugData(DebugLocation location) {
        DebugServerContext context = DebugServerContextManager.getDebugContext(location.getEndpoint());
        if (context != null) {
            DebugData debugData = context.getDebugData(location);
            if (debugData != null) {
                return debugData;
            }
        }
        String content = HttpClient.INSTANCE.post(getCallback(location.getEndpoint()) + CLIENT_URI + "/data", location);
        return JsonUtil.fromJson(content, DebugData.class);
    }

    private String getCallback(DebugEndpoint endpoint) {
        return Objects.requireNonNull(DebugServerContextManager.getDebugContext(endpoint)).getClientCallback();
    }
}
