package io.terminus.trantor2.service.engine.loader.metadata;

import io.terminus.trantor2.service.engine.delegate.Metadata;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * MetadataCache
 *
 * <AUTHOR> Created on 2024/1/10 11:56
 */
public class MetadataCache {

    private static final Map<String, Metadata> CACHE = new ConcurrentHashMap<>();

    public static Optional<Metadata> getMeta(String key) {
        return Optional.ofNullable(CACHE.get(key));
    }

    public static Metadata getMeta(String key, Function<String, Metadata> metaGetter) {
        return CACHE.computeIfAbsent(key, metaGetter);
    }
}
