package io.terminus.trantor2.service.engine.impl.value.expression.ast;

import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import lombok.ToString;

/**
 * StringAstParameter
 *
 * <AUTHOR> Created on 2024/4/11 18:38
 */
@ToString
public class StringAstParameter extends AstParameter {
    private final String value;

    public StringAstParameter(String value) {
        this.value = value;
    }

    @Override
    public Object getValue(VariableContext context) {
        return value;
    }
}
