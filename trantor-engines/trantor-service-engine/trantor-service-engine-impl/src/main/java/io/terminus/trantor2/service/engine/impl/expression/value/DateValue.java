package io.terminus.trantor2.service.engine.impl.expression.value;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.engine.impl.expression.ParserException;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * DateValue
 *
 * <AUTHOR> Created on 2022/08/29
 */
@AllArgsConstructor
@Slf4j
@EqualsAndHashCode(callSuper = false, of = "value")
public class DateValue extends io.terminus.trantor2.service.engine.impl.expression.value.Value {

    @Getter
    private final Date value;

    @Override
    public boolean eq(io.terminus.trantor2.service.engine.impl.expression.value.Value value) {
        if (value instanceof DateValue) {
            DateValue that = (DateValue) value;
            return this.value.equals(that.value);
        } else if (value instanceof ObjectValue) {
            ObjectValue that = (ObjectValue) value;
            return this.value.equals(that.getValue());
        } else if (value instanceof StringValue) {
            StringValue that = (StringValue) value;
            return this.eq(DateValue.fromStr(that.getValue()));
        } else {
            return false;
        }
    }

    public static DateValue fromStr(String dateStr) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return new DateValue(dateFormat.parse(dateStr));
        } catch (ParseException e) {
            log.warn("date format error, str: {}", dateStr, e);
            throw new ParserException("date format error, should match 'yyyy-MM-dd HH:mm:ss' pattern, but current is " + dateStr);
        }
    }

    public static DateValue valOf(Value value) {
        if (value instanceof StringValue) {
            return DateValue.fromStr(((StringValue) value).getValue());
        } else if (value instanceof DateValue) {
            return (DateValue) value;
        } else {
            throw new ParserException("value can't cast to date, value:" + JsonUtil.toJson(value));
        }
    }

}
