package io.terminus.trantor2.service.engine.executor.interceptor;

import com.google.common.base.Throwables;
import io.terminus.trantor2.service.engine.ServiceEngine;
import io.terminus.trantor2.service.engine.executor.ServiceInvocation;
import lombok.RequiredArgsConstructor;

/**
 * ContextInterceptor
 *
 * <AUTHOR> Created on 2023/4/10 10:56
 */
@RequiredArgsConstructor
public class ContextInterceptor extends ServiceInterceptor {

    private final ServiceEngine serviceEngine;

    @Override
    public Object execute(ServiceInvocation invocation) {
        InvocationContext context = Contexts.getGlobalInvocationContext();

        boolean contextReused = false;

        if (context == null) {
            context = new InvocationContext(serviceEngine);
            Contexts.setInvocationContext(context);
        } else {
            contextReused = true;
        }

        try {
            return next.execute(invocation);
        } catch (Throwable e) {
            context.exception(e, buildInnerMsg(invocation, e));

        } finally {
            if (!contextReused) {
                try {
                    context.close();
                } finally {
                    Contexts.removeInvocationContext();
                }
            }
        }

        // 在上下文复用的情况下(即是：服务调服务)，如果有异常，需要抛出，让上游服务感知
        if (contextReused && context.getException() != null) {
            RuntimeException exception = context.getProcessedException();

            // 需要再次重置异常，否则上游调用可能会被错误地标记为发生了异常，例如嵌套调用可能会被try-catch捕获
            // 例如: A -> b(Action) -> C，在b中对服务C的异常进行try-catch捕获，这里如果不重置异常，会导致服务A会抛出异常
            context.resetException();

            throw exception;
        }

        return null;
    }

    private String buildInnerMsg(ServiceInvocation invocation, Throwable e) {
        return String.format("[%s]发生异常：%s", invocation.getServiceKey(), Throwables.getRootCause(e).getMessage());
    }
}
