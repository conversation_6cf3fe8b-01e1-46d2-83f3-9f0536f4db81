package io.terminus.trantor2.service.engine.impl.function.impl;

import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.function.ServiceFunction;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collection;
import java.util.Objects;

/**
 * SumFunc
 *
 * <AUTHOR> Created on 2023/7/13 14:14
 */
@ServiceFunction(key = "MAX")
public class MaxFunc extends FuncImpl {
    @Override
    public Object apply(Object[] objects, VariableContext context) {
        if (objects != null) {
            if (objects.length == 1 && objects[0] instanceof Collection) {
                return ((Collection<?>) objects[0]).stream()
                    .filter(Objects::nonNull)
                    .map(Object::toString)
                    .map(BigDecimal::new)
                    .max(BigDecimal::compareTo)
                    .orElse(null);
            }
            return Arrays.stream(objects)
                .filter(Objects::nonNull)
                .map(Object::toString)
                .map(BigDecimal::new)
                .max(BigDecimal::compareTo)
                .orElse(null);
        }
        return null;
    }
}
