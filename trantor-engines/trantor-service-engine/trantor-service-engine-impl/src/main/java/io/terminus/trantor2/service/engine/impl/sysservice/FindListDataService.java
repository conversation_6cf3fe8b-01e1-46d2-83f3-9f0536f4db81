package io.terminus.trantor2.service.engine.impl.sysservice;

import com.google.auto.service.AutoService;
import io.terminus.trantor2.service.common.consts.ServiceConst;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.dsl.enums.Propagation;
import io.terminus.trantor2.service.engine.impl.component.ModelDataProcessHelper;
import io.terminus.trantor2.service.engine.impl.component.bean.QueryModel;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/3/27 11:54 上午
 */
@AutoService(SystemService.class)
public class FindListDataService extends AbstractSystemServiceImpl implements SystemService {

    public static final String KEY = "SYS_FindListDataService";

    @Override
    public String getServiceKey() {
        return KEY;
    }

    @Override
    public String getServiceName() {
        return "(系统)通过条件组查询数据列表";
    }

    @Override
    protected void supplyDefinition(ServiceDefinition definition) {
        definition.setTransactionPropagation(Propagation.NOT_SUPPORTED);
        definition.addInput(MODEL_KEY_FIELD);
        definition.addInput(SELECT_FIELDS);
        definition.addInput(CONDITION_REQUEST);
        definition.addOutput(ARRAY_MODEL_DATA_OUTPUT);
    }

    @Override
    protected void execute(SystemServiceExecContext context) {
        QueryModel queryModel = ModelDataProcessHelper.buildCondition(context, true);
        initFindDataPermissions(KEY, context, queryModel);
        List<Map<String, Object>> listData = context.getServiceEngine().getModelDataRepository().find(queryModel, ServiceConst.MAX_COUNT);
        context.setResult(listData);
        fillCrossModuleModelValue(listData, queryModel, context);
    }

}
