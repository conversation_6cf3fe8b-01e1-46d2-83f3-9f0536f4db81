package io.terminus.trantor2.service.engine.impl.astservice;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.service.common.consts.ServiceConst;
import io.terminus.trantor2.service.common.consts.VariableKey;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.dsl.StreamOutputNode;
import io.terminus.trantor2.service.engine.ServiceEngine;
import io.terminus.trantor2.service.engine.ai.context.AIContext;
import io.terminus.trantor2.service.engine.ai.core.message.CurrentAgent;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.impl.astservice.execution.Execution;
import io.terminus.trantor2.service.engine.impl.astservice.execution.debug.DebugClientContext;
import io.terminus.trantor2.service.engine.impl.astservice.state.StateMachineContext;
import io.terminus.trantor2.service.engine.impl.astservice.state.StateMachineContextHolder;
import io.terminus.trantor2.service.engine.impl.context.ContextImpl;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.HashMap;
import java.util.Map;

/**
 * ExecutionContext
 *
 * <AUTHOR> Created on 2023/2/17 00:03
 */
@Getter
@Setter
public class ExecutionContext extends ContextImpl {
    private static final String BRANCH_TAKEN_FLAG = "BRANCH_TAKEN_%s";
    private static final String BRANCH_TAKEN_CASE = "BRANCH_CASE_%s";

    /**
     * 原始参数
     */
    private Map<String, Object> args;
    private ServiceDefinition metadata;

    private boolean oldSystemVariableUsed;
    private boolean systemVariableUsed;
    private boolean moduleVariableUsed;

    private Response.Info info;
    private boolean completed;
    private Object result;

    private DebugClientContext debugContext;

    public CurrentAgent currentAgent;

    public Map<String, StreamOutputNode> streamingHandlers = new HashMap<>();

    public ExecutionContext(Arguments args) {
        this.addVariableFromArguments(args);
        if (args.getPayload() != null) {
            this.args = MapUtil.cascadeToMap(args.getPayload());
        }
    }

    public ExecutionContext(Arguments args, ServiceEngine serviceEngine) {
        this(args);
        this.setServiceEngine(serviceEngine);
    }

    public void completed() {
        this.setCompleted(true);
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    public Object getResult() {
        if (info != null) {
            if (result instanceof Map && !((Map) result).containsKey(ServiceConst.RES_INFO)) {
                ((Map) result).put(ServiceConst.RES_INFO, info);
            } else if (result == null) {
                result = new HashMap<>();
                ((Map) result).put(ServiceConst.RES_INFO, info);
            }
        }
        return result;
    }

    public String getSessionId() {
        return (String) getServiceRequestParameter("sessionId");
    }

    public String getServiceKey() {
        return (String) getVariable(VariableKey.SERVICE_KEY);
    }

    public boolean isBranchTaken(Execution branch) {
        return Boolean.TRUE.equals(getVariable(String.format(BRANCH_TAKEN_FLAG, branch.getCurrentServiceElement().getKey())));
    }

    public void branchTaken(Execution branch) {
        setVariable(String.format(BRANCH_TAKEN_FLAG, branch.getCurrentServiceElement().getKey()), true);
    }

    public void clearBranchTaken(Execution branch) {
        removeVariable(String.format(BRANCH_TAKEN_FLAG, branch.getCurrentServiceElement().getKey()));
    }

    public Object getBranchContext(Execution branch) {
        return getVariable(String.format(BRANCH_TAKEN_CASE, branch.getCurrentServiceElement().getKey()));
    }

    public void setBranchContext(Execution branch, Object switchValue) {
        setVariable(String.format(BRANCH_TAKEN_CASE, branch.getCurrentServiceElement().getKey()), switchValue);
    }

    public void clearBranchContext(Execution branch) {
        removeVariable(String.format(BRANCH_TAKEN_CASE, branch.getCurrentServiceElement().getKey()));
    }

    public Object getServiceRequestParameter(String requestName) {
        return args != null ? args.get(requestName) : null;
    }

    void setStateMachineContext(StateMachineContext stateMachineContext) {
        StateMachineContextHolder.setServiceStateMachineContext(stateMachineContext);
    }

    void closeStateMachineContext() {
        StateMachineContext stateMachineContext = StateMachineContextHolder.getServiceStateMachineContext();
        if (stateMachineContext != null) {
            try {
                if (isCompleted()) {
                    stateMachineContext.close();
                }
            } finally {
                StateMachineContextHolder.removeServiceStateMachineContext();
            }
        }
    }

    void setDebugContext(DebugClientContext debugContext) {
        this.debugContext = debugContext;
    }

    void closeDebugContext() {
        if (debugContext != null) {
            debugContext.close();
        }
    }

    @JsonIgnore
    boolean isDebugger() {
        return Boolean.TRUE.equals(getVariable(VariableKey.DEBUGGER_ENABLED));
    }

    @JsonIgnore
    public SseEmitter getSseEmitter() {
        if (AIContext.getContext() != null && AIContext.getContext().getGlobalSseEmitter() != null) {
            return AIContext.getContext().getGlobalSseEmitter();
        }
        return null;
    }

    @Override
    public void close() {
        try {
            closeStateMachineContext();
            closeDebugContext();
        } finally {
            super.close();
        }
    }
}
