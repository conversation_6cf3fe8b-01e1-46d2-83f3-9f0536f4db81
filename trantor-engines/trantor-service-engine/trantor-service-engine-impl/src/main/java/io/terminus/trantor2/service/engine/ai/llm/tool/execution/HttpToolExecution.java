package io.terminus.trantor2.service.engine.ai.llm.tool.execution;

import com.openai.core.JsonValue;
import com.openai.models.chat.completions.ChatCompletionTool;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.common.utils.HttpClient;
import io.terminus.trantor2.service.common.utils.Placeholder;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.dsl.enums.BodyType;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.dsl.properties.HttpTool;
import io.terminus.trantor2.service.dsl.properties.ObjectField;
import io.terminus.trantor2.service.dsl.properties.SkillTool;
import io.terminus.trantor2.service.dsl.properties.StringEntry;
import io.terminus.trantor2.service.engine.ai.context.AIContext;
import io.terminus.trantor2.service.engine.ai.llm.util.HttpToolSchemaFieldUtil;
import io.terminus.trantor2.service.engine.impl.astservice.ExecutionContext;
import io.terminus.trantor2.service.engine.impl.value.ValueFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * HTTP工具执行器
 */
@Component
@Slf4j
public class HttpToolExecution extends ToolCallExecution {

    @Override
    public ChatCompletionTool buildTool(SkillTool tool) {
        if (!(tool instanceof HttpTool httpTool)) {
            throw new IllegalArgumentException("Tool must be HttpTool instance");
        }

        List<Field> fields = new ArrayList<>();
        ServiceDefinition metadata = ((ExecutionContext)AIContext.getContext().getVariableContext()).getMetadata();

        List<Field> headerElements = HttpToolSchemaFieldUtil.parse(metadata, httpTool.getHeaders());
        if (CollectionUtils.isNotEmpty(headerElements)) {
            fields.add(new ObjectField("header", headerElements));
        }

        List<Field> parameterElements = HttpToolSchemaFieldUtil.parse(metadata, httpTool.getParams());
        if (CollectionUtils.isNotEmpty(parameterElements)) {
            fields.add(new ObjectField("parameter", parameterElements));
        }

        if (httpTool.getBodyType().equals(BodyType.VALUE)) {
            fields.add(new ObjectField("body", HttpToolSchemaFieldUtil.getVariableField(metadata, httpTool.getBody())));
        } else if (httpTool.getBodyType().equals(BodyType.JSON)) {
            fields.add(new ObjectField("body", httpTool.getJsonBody()));
        }

        Map<String, JsonValue> additionalProperty = buildToolCallInputSchema(fields);
        log.info("build http tool: [{}({})] params json schema properties: {}", httpTool.getKey(), httpTool.getName(), additionalProperty);

        return buildChatCompletionTool(httpTool.getKey(), StringUtils.defaultString(httpTool.getDesc(), ""), additionalProperty);
    }

    @Override
    public String execute(SkillTool tool, String toolName, String toolArguments) {
        if (!(tool instanceof HttpTool httpTool)) {
            throw new IllegalArgumentException("Tool must be HttpTool instance");
        }

        if (!httpTool.getKey().equals(toolName)) {
            log.warn("Tool name mismatch: expected {}, got {}", httpTool.getKey(), toolName);
            return "";
        }

        log.info("调用Http工具: [{}({})], arguments: {}", toolName, httpTool.getName(), toolArguments);
        String invokeResponse = invokeHttpTool(httpTool, toolArguments);
        log.info("调用Http工具结果: {}", JsonUtil.toNonIndentJson(invokeResponse));
        return invokeResponse;
    }

    private String invokeHttpTool(HttpTool httpTool, String toolArguments) {
        HttpInvokeArguments arguments = null;
        try {
            if (StringUtils.isNotBlank(toolArguments)) {
                arguments = JsonUtil.fromJson(toolArguments, HttpInvokeArguments.class);
            }
        } catch (Exception e) {
            log.warn("解析 llm tool arguments 失败: {}", e.getMessage());
        }

        Map<String, String> header = getHttpHeader(arguments, httpTool);
        Map<String, Object> parameter = getHttpParameter(arguments, httpTool);
        Map<String, Object> pathVariables = getValue(httpTool.getPathVariables());
        Object bodyValue = getHttpBody(arguments, httpTool);
        String url = getHttpUrl(httpTool, pathVariables, parameter);

        String result = null;
        // @formatter:off
        log.info("发起Http调用详细参数: [{}({})], header: [{}], parameter: [{}], pathVariables: {}, url: [{}], body: [{}]",
                 httpTool.getKey(),
                 httpTool.getName(),
                 header,
                 parameter,
                 pathVariables,
                 url,
                 bodyValue);
        String method = httpTool.getMethod();
        switch (method) {
            case "GET":
                result = HttpClient.INSTANCE.get(url, header, parameter);
                break;
            case "POST":
                result = HttpClient.INSTANCE.post(url, header, ObjectUtils.defaultIfNull(bodyValue, parameter));
                break;
            case "PUT":
                result = HttpClient.INSTANCE.put(url, header, ObjectUtils.defaultIfNull(bodyValue, parameter));
                break;
            case "DELETE":
                result = HttpClient.INSTANCE.delete(url, header, parameter);
                break;
            default:
                log.warn("Unsupported http method {}", method);
                break;
        }
        // @formatter:on
        return result;
    }

    private String getHttpUrl(HttpTool httpTool, Map<String, Object> pathVariables, Map<String, Object> parameter) {
        String url = httpTool.getUrl();
        if (httpTool.getUrl().contains("{") && httpTool.getUrl().contains("}")) {
            Map<String, Object> variables = new HashMap<>();
            variables.putAll(pathVariables);
            variables.putAll(parameter);
            url = Placeholder.PLACE_CURLY_BRACES_HOLDER.replaceHolder(httpTool.getUrl(), variables);
        }

        if (MapUtils.isNotEmpty(parameter)) {
            try {
                return HttpClient.INSTANCE.buildUri(url, parameter).toString();
            } catch (Exception e) {
                log.warn("构建URL失败: {}", e.getMessage());
            }
        }
        return url;
    }

    private Map<String, String> getHttpHeader(HttpInvokeArguments arguments, HttpTool httpTool) {
        // 获取请求头，优先使用传入的参数
        Map<String, String> header;
        if (Objects.nonNull(arguments) && arguments.getHeader() != null && !arguments.getHeader().isEmpty()) {
            // 先取默认值，再用LLM生成的值进行覆盖
            header = getDefaultHttpHeader(httpTool.getHeaders());
            log.info("请求头默认值: {}", header);
            header.putAll(arguments.getHeader());
            log.info("使用LLM传入的请求头: {}", arguments.getHeader());
            log.info("混合使用请求头: {}", header);
        } else {
            header = getDefaultHttpHeader(httpTool.getHeaders());
        }
        return header;
    }

    private Map<String, Object> getHttpParameter(HttpInvokeArguments arguments, HttpTool httpTool) {
        // 获取查询参数，优先使用传入的参数
        Map<String, Object> parameter;
        if (Objects.nonNull(arguments) && Objects.nonNull(arguments.getParameter()) && !arguments.getParameter().isEmpty()) {
            // 先取默认值，再用LLM生成的值进行覆盖
            parameter = getValue(httpTool.getParams());
            parameter.putAll(arguments.getParameter());
            log.info("使用LLM传入的查询参数: {}", arguments.getParameter());
            log.info("混合使用查询参数: {}", parameter);
        } else {
            parameter = getValue(httpTool.getParams());
        }
        return parameter;
    }

    private Object getHttpBody(HttpInvokeArguments arguments, HttpTool httpTool) {
        // 获取请求体，优先使用传入的参数
        Object bodyValue;
        if (Objects.nonNull(arguments) && Objects.nonNull(arguments.getBody()) && !arguments.getBody().isEmpty()) {
            bodyValue = getDefaultHttpBody(httpTool);
            arguments.getBody().forEach((fieldName, fieldValue) -> {
                if (bodyValue instanceof Map<?, ?> map) {
                    ((Map<String, Object>) map).put(fieldName, fieldValue);
                }
            });
            log.info("使用LLM传入的请求体: {}", arguments.getBody());
            log.info("混合使用查询参数: {}", bodyValue);
        } else {
            bodyValue = getDefaultHttpBody(httpTool);
        }
        return bodyValue;
    }

    private Map<String, String> getDefaultHttpHeader(List<StringEntry> headers) {
        final Map<String, String> headerValue = new HashMap<>();

        // 设置在页面上请求头
        if (CollectionUtils.isNotEmpty(headers)) {
            final Map<String, Object> customHeaderValue = ValueFactory.getValueForStringEntry(headers, AIContext.getContext().getVariableContext());
            for (Map.Entry<String, Object> entry : customHeaderValue.entrySet()) {
                // 判断是否存在，如果不存在，才添加
                if (headerValue.keySet().stream().noneMatch(k -> k.equalsIgnoreCase(entry.getKey()))) {
                    headerValue.put(entry.getKey(), entry.getValue().toString());
                }
            }
        }
        return headerValue;
    }

    private Object getDefaultHttpBody(HttpTool httpTool) {
        if (httpTool.getBodyType() == BodyType.JSON) {
            Map<String, Object> body = new HashMap<>();
            httpTool.getJsonBody().forEach(field -> {
                if (Objects.nonNull(field.getDefaultValue())) {
                    body.put(field.getFieldKey(), gerDefaultValue(field));
                }
            });
            return body;
        } else {
            return ValueFactory.getValue(null, httpTool.getBody(), AIContext.getContext().getVariableContext());
        }
    }

    private Map<String, Object> getValue(List<StringEntry> values) {
        Map<String, Object> value = new HashMap<>();

        if (CollectionUtils.isNotEmpty(values)) {
            value = ValueFactory.getValueForStringEntry(values, AIContext.getContext().getVariableContext());
        }
        return value;
    }
}
