package io.terminus.trantor2.service.engine.impl.expression.ast;

import io.terminus.trantor2.service.engine.impl.expression.Scope;
import io.terminus.trantor2.service.engine.impl.expression.value.ListValue;
import io.terminus.trantor2.service.engine.impl.expression.value.Value;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ListNode extends AbstractNode {

    @Getter
    private final List<AbstractNode> elements;

    public ListNode(List<AbstractNode> elements) {
        this.elements = elements;
        if (!CollectionUtils.isEmpty(elements)) {
            AbstractNode first = elements.get(0);
            this.start = first.start;
            this.end = first.end;
            this.row = first.row;
            this.col = first.col;
        }
    }

    @Override
    public Value interp(Scope scope) {
        return new ListValue(elements.stream().map(e -> e.interp(scope)).collect(Collectors.toList()));
    }

}
