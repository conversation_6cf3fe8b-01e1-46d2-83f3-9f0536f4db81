package io.terminus.trantor2.service.engine.impl.function.impl;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.service.common.exception.ServiceException;
import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.expression.QLExpressionUtil;
import io.terminus.trantor2.service.engine.impl.function.ServiceFunction;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * ArrayMapFunc
 *
 * <AUTHOR> Created on 2023/11/9 15:16
 */
@SuppressWarnings("unchecked")
@ServiceFunction(key = "ARRAY_MAP")
public class ArrayMapFunc extends FuncImpl {

    private static final String FLAG = "->";

    @Override
    public Object apply(Object[] objects, VariableContext context) {
        if (objects == null || objects.length < 2) {
            throw new ServiceException(ErrorType.SERVICE_ERROR, "“ARRAY_MAP”函数参数个数不正确");
        }

        Object var = objects[0];
        if (var == null) {
            return new ArrayList<>(0);
        }

        if (!(var instanceof Collection)) {
            throw new ServiceException(ErrorType.SERVICE_ERROR, "“ARRAY_MAP”函数仅支持处理集合变量");
        }

        Collection<Object> array = (Collection<Object>) var;
        if (CollectionUtils.isEmpty(array)) {
            return new ArrayList<>(0);
        }

        String mapper = (String) objects[1];
        String filter = null;
        if (objects.length > 2) {
            filter = (String) objects[2];
        }

        String[] mappers = mapper.split(FLAG);
        Function<Object, Object> mapperFunction = (item) -> {
            Map<String, Object> env = MapUtil.of(mappers[0].trim(), item);
            return QLExpressionUtil.getValue(mappers[1].trim(), env);
        };

        // 过滤条件可以不设置
        Predicate<Object> predicate = null;
        if (StringUtils.isNotBlank(filter)) {
            String[] filters = filter.split(FLAG);
            predicate = (item) -> {
                Map<String, Object> env = MapUtil.of(filters[0].trim(), item);
                return Boolean.TRUE.equals(QLExpressionUtil.getValue(filters[1].trim(), env));
            };
        }

        if (predicate != null) {
            return array.stream().filter(predicate).map(mapperFunction).collect(Collectors.toList());
        } else {
            return array.stream().map(mapperFunction).collect(Collectors.toList());
        }
    }
}
