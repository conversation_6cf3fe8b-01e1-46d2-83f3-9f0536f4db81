package io.terminus.trantor2.service.engine.impl.value.converter;

import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.engine.impl.value.ValueConverter;

/**
 * BooleanConverter
 *
 * <AUTHOR> Created on 2023/2/20 17:03
 */
public class BooleanConverter implements ValueConverter {


    @Override
    public Object convert(Field field, Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof Boolean) {
            return value;
        } else if (value instanceof String) {
            return Boolean.valueOf(value.toString());
        }

        return value;
    }
}
