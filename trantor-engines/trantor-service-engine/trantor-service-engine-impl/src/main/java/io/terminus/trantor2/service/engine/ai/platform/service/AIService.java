package io.terminus.trantor2.service.engine.ai.platform.service;

import io.terminus.trantor2.service.common.dto.AIRequest;
import io.terminus.trantor2.service.engine.ai.platform.model.request.AITestRequest;
import io.terminus.trantor2.service.engine.ai.platform.model.request.PromptOptimizedRequest;
import io.terminus.trantor2.service.engine.ai.platform.model.vo.AiModelConfigVO;
import io.terminus.trantor2.service.engine.ai.platform.model.vo.McpServerPagingVO;
import io.terminus.trantor2.service.engine.ai.platform.service.bean.QueryMcpServerListParam;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AIService {
    /**
     * 发起执行
     *
     * @param request
     * @return
     */
    Object testExecute(AITestRequest request);

    /**
     * 发起编排服务AI Chat请求
     *
     * @param serviceKey
     * @param request
     * @return
     */
    Object chat(String serviceKey, AIRequest request);

    /**
     * 获取可用模型列表
     *
     * @return
     */
    List<AiModelConfigVO> getAvailableModelList();

    /**
     * 分页获取可用Mcp服务列表
     *
     * @param param
     * @return
     */
    McpServerPagingVO getAvailableMcpServerList(QueryMcpServerListParam param);

    /**
     * 获取优化后的 Prompt
     *
     * @param request 待优化 Prompt
     * @return 优化后 Prompt
     */
    SseEmitter getPromptOptimizedVO(PromptOptimizedRequest request);
}
