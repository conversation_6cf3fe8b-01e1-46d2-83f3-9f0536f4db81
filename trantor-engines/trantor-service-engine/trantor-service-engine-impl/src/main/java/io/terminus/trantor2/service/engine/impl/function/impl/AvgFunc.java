package io.terminus.trantor2.service.engine.impl.function.impl;

import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.function.ServiceFunction;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collection;
import java.util.Objects;

/**
 * AvgFunc
 *
 * <AUTHOR> Created on 2023/7/13 14:14
 */
@ServiceFunction(key = "AVG")
public class AvgFunc extends FuncImpl {
    @Override
    public Object apply(Object[] objects, VariableContext context) {
        if (objects != null) {
            if (objects.length == 1 && objects[0] instanceof Collection) {
                return ((Collection<?>) objects[0]).stream()
                        .filter(Objects::nonNull)
                        .map(Object::toString)
                        .map(BigDecimal::new)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .divide(new BigDecimal(objects.length), 6, RoundingMode.DOWN);
            }
            return Arrays.stream(objects)
                    .filter(Objects::nonNull)
                    .map(Object::toString)
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(new BigDecimal(objects.length), 6, RoundingMode.DOWN);
        }
        return null;
    }
}
