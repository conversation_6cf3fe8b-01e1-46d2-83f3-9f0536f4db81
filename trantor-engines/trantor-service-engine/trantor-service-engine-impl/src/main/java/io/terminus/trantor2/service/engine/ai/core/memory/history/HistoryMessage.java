package io.terminus.trantor2.service.engine.ai.core.memory.history;

import io.terminus.trantor2.common.user.User;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;

/**
 * HistoryMessage
 *
 * <AUTHOR> Created on 2025/6/6 17:37
 */
@Setter
@Getter
@FieldNameConstants
public class HistoryMessage implements Serializable {
    private static final long serialVersionUID = -1616179905599049000L;

    private Long id;
    private String messageId;
    private String previousMessageId;
    private String parentId;
    private String conversationId;
    private String role;
    private String content;
    private String meta;
    private Integer likeFlag;

    private Long createdAt;
    private Long updatedAt;
    private User createdBy;
    private User updatedBy;
}
