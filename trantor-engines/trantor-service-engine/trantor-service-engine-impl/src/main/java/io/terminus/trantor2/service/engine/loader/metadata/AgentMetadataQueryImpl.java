package io.terminus.trantor2.service.engine.loader.metadata;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.service.common.enums.ServiceType;
import io.terminus.trantor2.service.common.exception.ServiceException;
import io.terminus.trantor2.service.common.meta.AIAgentMeta;
import io.terminus.trantor2.service.common.meta.KnowledgeBaseMeta;
import io.terminus.trantor2.service.common.meta.ServiceMeta;
import io.terminus.trantor2.service.dsl.Agent;
import io.terminus.trantor2.service.dsl.KnowledgeBase;
import io.terminus.trantor2.service.dsl.ReferenceAgent;
import io.terminus.trantor2.service.dsl.ServiceElement;
import io.terminus.trantor2.service.dsl.ServiceElementsContainer;
import io.terminus.trantor2.service.dsl.properties.ServiceTool;
import io.terminus.trantor2.service.dsl.properties.SkillTool;
import io.terminus.trantor2.service.dsl.properties.knowledgebase.AgentKnowledgeBase;
import io.terminus.trantor2.service.engine.ai.llm.tool.ProprietaryToolService;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.delegate.Metadata;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * ServiceMetadataQueryImpl
 *
 * <AUTHOR> Created on 2023/2/27 15:22
 */
@Component
@RequiredArgsConstructor
public class AgentMetadataQueryImpl implements AgentMetadataQuery {
    private final MetaQueryService metaQueryService;
    private final ProprietaryToolService proprietaryToolService;

    @Override
    public Metadata findMeta(Key metaKey) {
        Optional<MetaTreeNodeExt> meta = metaQueryService.queryInTeam(metaKey.getTeamId())
                .find(MetaType.AIAgent, metaKey.getKey());
        if (meta.isEmpty()) {
            return null;
        }
        final AIAgentMeta obj = ServiceMeta.newInstanceFrom(meta.get(), AIAgentMeta.class);
        if (obj.getResourceProps() == null) {
            return null;
        }
        final Agent definition = obj.getResourceProps().getAgent();
        if (definition == null) {
            return null;
        }
        return Metadata.of(metaKey, definition,
                ServiceType.AGENT,
                Boolean.TRUE.equals(obj.getResourceProps().getIsEnabled()));
    }

    @Override
    public Agent findAgent(Key metaKey) {
        Optional<MetaTreeNodeExt> meta = metaQueryService.queryInTeam(metaKey.getTeamId())
                .find(MetaType.AIAgent, metaKey.getKey());
        if (meta.isEmpty()) {
            return null;
        }
        final AIAgentMeta obj = ServiceMeta.newInstanceFrom(meta.get(), AIAgentMeta.class);
        return obj.getResourceProps().getAgent();
    }

    @Override
    public Agent findFullAgent(Key metaKey) {
        Agent agent = findAgent(metaKey);
        fillFullAgent(metaKey, agent);
        return agent;
    }

    @Override
    public void fillFullAgent(Key metaKey, Agent agent) {
        if (agent != null) {
            injectProprietaryToolPrompt(agent);
            mountProprietaryTool(metaKey.getTeamId(), agent);
            setSseModeTools(metaKey.getTeamId(), agent);
            setKnowledgeBase(metaKey.getTeamId(), agent);
            setReferenceAgent(metaKey.getTeamId(), agent);
        }
    }

    private void injectProprietaryToolPrompt(Agent agent) {
        if (agent.getProps().isSelfGenerateProprietaryTool()) {
            String prompt = proprietaryToolService.getBuildProprietaryToolPrompt();
            agent.getProps().setSystemPrompt(agent.getProps().getSystemPrompt() + prompt);
        }
    }

    private void mountProprietaryTool(Long teamId, Agent agent) {
        List<SkillTool> tools = agent.getProps().getSkillTools();
        List<SkillTool> proprietaryTools = proprietaryToolService.loadProprietaryTools(teamId, agent.getKey());
        tools.addAll(proprietaryTools);
        agent.getProps().setSkillTools(tools);
    }

    /**
     * 针对编排服务工具，设置SSE模式标识
     *
     * @param teamId
     * @param agent
     */
    private void setSseModeTools(Long teamId, Agent agent) {
        List<SkillTool> tools = agent.getProps().getSkillTools();
        if (!CollectionUtils.isEmpty(tools)) {
            tools.forEach(tool -> {
                if (tool instanceof ServiceTool serviceTool) {
                    String serviceKey = serviceTool.getKey();
                    Optional<MetaTreeNodeExt> optional = metaQueryService.queryInTeam(teamId).find(MetaType.ServiceDefinition, serviceKey);
                    if (optional.isPresent()) {
                        MetaTreeNodeExt ext = optional.get();
                        ServiceMeta serviceMeta = ServiceMeta.newInstanceFrom(ext, ServiceMeta.class);
                        boolean chatMode = serviceMeta.getResourceProps().getServiceDslJson().isAiChatMode();
                        serviceTool.setSseMode(chatMode);
                    }
                }
            });
        }
        agent.getProps().setSkillTools(tools);
    }

    /**
     * 根据知识库的启用情况配置知识库
     *
     * @param teamId
     * @param agent
     * @return
     */
    public void setKnowledgeBase(Long teamId, Agent agent) {
        AgentKnowledgeBase agentKnowledgeBase = agent.getProps().getKnowledgeBase();
        if (Objects.isNull(agentKnowledgeBase)) {
            return;
        }
        List<KnowledgeBase> knowledgeBases = agentKnowledgeBase.getKnowledgeBases();
        if (Objects.isNull(knowledgeBases)) {
            return;
        }
        // @formatter:off
        List<KnowledgeBase> availableKnowledgeBases = knowledgeBases.stream()
                .filter(knowledgeBase -> {
                    Optional<MetaTreeNodeExt> optional = metaQueryService.queryInTeam(teamId).find(MetaType.KnowledgeBase, knowledgeBase.getKey());
                    if (optional.isEmpty()) {
                        String errorMsg = String.format("知识库[%s] 已不存在", knowledgeBase.getKey());
                        throw new ServiceException(ErrorType.SERVICE_ORIGIN_ERROR, errorMsg);
                    }

                    MetaTreeNodeExt ext = optional.get();
                    KnowledgeBaseMeta knowledgeBaseMeta = KnowledgeBaseMeta.newInstanceFrom(ext, KnowledgeBaseMeta.class);
                    // 将未启用的知识库在Agent中剔除
                    return knowledgeBaseMeta.getResourceProps().getIsEnabled();
                })
                .collect(Collectors.toList());
        // @formatter:off
        agentKnowledgeBase.setKnowledgeBases(availableKnowledgeBases);
        agent.getProps().setKnowledgeBase(agentKnowledgeBase);
    }

    public void setReferenceAgent(Long teamId, ServiceElementsContainer container) {
        if (container.hasChildren()) {
            for (ServiceElement<?> node : container.getChildren()) {
                // 处理引用代理
                if (node instanceof ReferenceAgent refAgent) {
                    // 处理引用代理
                    Agent targetAgent = findFullAgent(Key.of(teamId, refAgent.getProps().getRefAgentKey()));
                    refAgent.setTargetAgent(targetAgent);
                }
                if (node instanceof Agent agent) {
                    setSseModeTools(teamId, agent);
                    setKnowledgeBase(teamId, agent);
                }
                // 递归设置子
                if (node instanceof ServiceElementsContainer) {
                    setReferenceAgent(teamId, (ServiceElementsContainer) node);
                }
            }
        }
    }
}
