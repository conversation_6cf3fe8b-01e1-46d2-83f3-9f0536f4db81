package io.terminus.trantor2.service.engine.impl.context;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.service.common.exception.ServiceException;
import io.terminus.trantor2.service.dsl.enums.AssignOperator;
import io.terminus.trantor2.service.dsl.enums.VariableType;
import io.terminus.trantor2.service.engine.ServiceEngine;
import io.terminus.trantor2.service.engine.impl.value.expression.Expression;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * SimpleVariableContext
 *
 * <AUTHOR> Created on 2023/11/23 16:44
 */
@SuppressWarnings({"unchecked", "rawtypes"})
@NoArgsConstructor
public class SimpleVariableContext implements VariableContext {

    private final Map<String, Object> variables = new HashMap<>();

    @Setter
    protected ServiceEngine serviceEngine;

    public SimpleVariableContext(ServiceEngine serviceEngine) {
        this.serviceEngine = serviceEngine;
    }

    public SimpleVariableContext(Map<String, Object> variables) {
        if (variables != null && !variables.isEmpty()) {
            variables.forEach(this::setVariable);
        }
    }

    public SimpleVariableContext(Map<String, Object> variables, ServiceEngine serviceEngine) {
        this(variables);
        this.serviceEngine = serviceEngine;
    }

    @Override
    public Map<String, Object> getVariables() {
        return variables;
    }

    @Override
    public Map<String, Object> cloneVariables() {
        return MapUtil.clone(getVariables());
    }

    @Override
    public void setVariables(Map<String, Object> variables) {
        if (variables != null && !variables.isEmpty()) {
            variables.forEach(this::setVariable);
        }
    }

    @Override
    public void setVariable(String variableName, Object value) {
        if (variableName != null) {
            variables.put(variableName, value);
        }
    }

    @Override
    public void removeVariable(String variableName) {
        if (variableName != null) {
            variables.remove(variableName);
        }
    }

    @Override
    public Object getVariable(String variableName) {
        Object value = variables.get(variableName);
        if (value instanceof Expression) {
            value = ((Expression) value).getValue(this);
        }
        return value;
    }

    @SuppressWarnings("rawtypes")
    @Override
    public Object getVariable(List<String> paths) {
        if (VariableType.SYS.getKey().equals(paths.get(0))) {
            return getSystemVariableValue(paths);
        } else if (VariableType.MODULE.getKey().equals(paths.get(0))) {
            return getModuleVariableValue(paths);
        } else if (VariableType.PROMPT_VARIABLES.getKey().equals(paths.get(0))) {
            return getPromptVariableValue(paths);
        } else {
            Object tempValue = getVariables();
            for (int i = 0; i < paths.size() - 1; i++) {
                tempValue = ((Map) tempValue).get(paths.get(i));
                if (Objects.isNull(tempValue)) {
                    break;
                }
                if (tempValue instanceof Expression) {
                    tempValue = ((Expression) tempValue).getValue(this);
                }
                if (MapUtil.isMap(tempValue)) {
                    tempValue = MapUtil.toMap(tempValue);
                } else {
                    throw new ServiceException(ErrorType.SERVICE_ASSIGN_NOT_MODEL_VALUE, new Object[]{String.join(".", paths.subList(0, i + 1))});
                }
            }
            if (Objects.isNull(tempValue)) {
                return null;
            } else {
                tempValue = ((Map) tempValue).get(paths.get(paths.size() - 1));
                if (tempValue instanceof Expression) {
                    tempValue = ((Expression) tempValue).getValue(this);
                }
                return tempValue;
            }
        }
    }

    private String getPromptVariableValue(List<String> paths) {
        Assert.isTrue(paths.size() == 2, String.format("提示侧变量%s不正确", paths));
        return getServiceEngine().getPromptVariableService()
                .findPromptVariableValue(getTeamId(), getModuleKey(), paths.get(1));
    }

    private String getModuleVariableValue(List<String> paths) {
        Assert.isTrue(paths.size() == 2, String.format("模块变量%s不正确", paths));
        return getServiceEngine().getVariableService()
                .findValue(getTeamId(), getModuleKey(), paths.get(1));
    }

    private Object getSystemVariableValue(List<String> paths) {
        Assert.isTrue(paths.size() >= 2, String.format("系统变量%s不正确", paths));

        // 处理系统变量的header变量
        if (paths.get(1).equals("header")) {
            Assert.isTrue(paths.size() == 3, String.format("系统变量%s不正确", paths));
            Map<String, String> headers = TrantorContext.getHeadersForUpperCaseKey();
            if (headers != null) {
                return headers.get(paths.get(2));
            } else {
                return null;
            }
        }
        // 处理系统变量
        else {
            Object value = getServiceEngine().getRuleEngineFactorApi().findSystemVariableVale(paths.get(1)).getData();
            if (value instanceof Date) {
                return ((Date) value).getTime();
            } else if (value instanceof List) {
                List newValue = new ArrayList<>(((List) value).size());
                for (Object o : ((List) value)) {
                    if (o instanceof Date) {
                        newValue.add(((Date) o).getTime());
                    } else {
                        newValue.add(o);
                    }
                }
                return newValue;
            }
            return value;
        }
    }

    /**
     * 根据操作符号进行设置变量
     *
     * @param paths    path路径
     * @param value    需要设置的变量值
     * @param operator 赋值操作符
     */
    public void setVariable(List<String> paths, Object value, AssignOperator operator) {
        switch (operator) {
            case EQ:
                setVariable(paths, value);
                break;
            case ADD:
                List list = (List) getVariable(paths);
                if (list == null) {
                    list = new ArrayList<>();
                    setVariable(paths, list);
                }
                list.add(value);
                break;
            case REMOVE:
                List oldList = (List) getVariable(paths);
                if (oldList != null) {
                    oldList.remove(value);
                }
                break;
        }
    }

    @Override
    public void setVariable(List<String> paths, Object value) {
        Object tempValue = getVariables();
        for (int i = 0; i < paths.size() - 1; i++) {
            tempValue = ((Map) tempValue).compute(paths.get(i), (k, v) -> (v == null) ? new HashMap<>() : (MapUtil.isMap(v) ? MapUtil.toMap(v) : v));
            if (!MapUtil.isMap(tempValue)) {
                throw new ServiceException(ErrorType.SERVICE_ASSIGN_NOT_MODEL_VALUE, new Object[]{String.join(".", paths.subList(0, i + 1))});
            }
        }
        ((Map) tempValue).put(paths.get(paths.size() - 1), value);
    }

    @Override
    public void removeVariable(List<String> paths) {
        Object tempValue = getVariables();
        for (int i = 0; i < paths.size() - 1; i++) {
            tempValue = ((Map) tempValue).get(paths.get(i));
            if (Objects.isNull(tempValue)) {
                break;
            }
            if (MapUtil.isMap(tempValue)) {
                tempValue = MapUtil.toMap(tempValue);
            } else {
                break;
            }
        }
        if (Objects.nonNull(tempValue)) {
            ((Map) tempValue).remove(paths.get(paths.size() - 1));
        }
    }

    @Override
    public ServiceEngine getServiceEngine() {
        return serviceEngine;
    }

    @Override
    public void close() {
        variables.clear();
    }
}
