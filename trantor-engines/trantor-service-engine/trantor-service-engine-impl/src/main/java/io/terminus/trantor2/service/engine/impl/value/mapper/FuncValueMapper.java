package io.terminus.trantor2.service.engine.impl.value.mapper;

import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.dsl.properties.FuncValue;
import io.terminus.trantor2.service.dsl.properties.Value;
import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.value.ValueFactory;
import io.terminus.trantor2.service.engine.impl.value.ValueMapper;
import io.terminus.trantor2.service.engine.impl.value.expression.Expression;
import io.terminus.trantor2.service.engine.impl.value.expression.ExpressionFactory;

import java.util.Objects;

/**
 * FuncValueMapper
 *
 * <AUTHOR> Created on 2023/7/5 20:32
 */
public class FuncValueMapper implements ValueMapper {

    @Override
    public Object getValue(Field field, Value value, VariableContext context) {
        FuncValue funcValue = (FuncValue) value;
        Expression expression = ExpressionFactory.createFuncExpression(funcValue.getFuncExpression());
        return convertType(field, expression.getValue(context));
    }

    private Object convertType(Field field, Object value) {
        if (Objects.isNull(value) || Objects.isNull(field)) {
            return value;
        } else {
            return ValueFactory.convertValue(field, value);
        }
    }
}
