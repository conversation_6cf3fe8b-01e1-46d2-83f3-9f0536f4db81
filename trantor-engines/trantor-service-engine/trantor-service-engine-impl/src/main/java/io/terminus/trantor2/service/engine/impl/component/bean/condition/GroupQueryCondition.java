package io.terminus.trantor2.service.engine.impl.component.bean.condition;

import io.terminus.trantor2.service.dsl.enums.LogicOperator;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * GroupQueryCondition
 *
 * <AUTHOR> Created on 2022/8/26 17:32
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroupQueryCondition extends QueryCondition {

    private List<QueryCondition> conditions;

    public GroupQueryCondition(
        List<QueryCondition> conditions,
        LogicOperator nextOperator) {
        super(nextOperator);
        this.conditions = conditions;
    }

    public void addCondition(QueryCondition condition) {
        this.addCondition(LogicOperator.AND, condition);
    }

    public void addCondition(LogicOperator currentOperator, QueryCondition condition) {
        if (condition == null) {
            return;
        }
        if (conditions == null) {
            conditions = new ArrayList<>(4);
            conditions.add(condition);
        } else {
            QueryCondition lastCondition = conditions.get(conditions.size() - 1);
            lastCondition.setNextOperator(currentOperator);
            conditions.add(condition);
        }
    }
}
