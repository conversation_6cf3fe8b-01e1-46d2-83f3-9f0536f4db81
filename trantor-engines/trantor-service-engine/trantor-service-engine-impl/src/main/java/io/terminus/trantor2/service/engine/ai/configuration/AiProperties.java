package io.terminus.trantor2.service.engine.ai.configuration;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 * 项目中使用AI的相关配置
 *
 * <AUTHOR> Created on 2023/11/7 11:40
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ai")
public class AiProperties {
    public static String AGENT_PRE = "/api/trantor/agent/execute";
    public static String API_PRE = "/api/trantor/service/engine/execute";

    private static String EXEC_API = API_PRE + "/%s*";
    private static String EXEC_CHAT_API = API_PRE + "/**/%s*";
    private static String AGENT_API = AGENT_PRE + "/%s*";

    /**
     * AI功能是否开启
     */
    private boolean enable = false;

    /**
     * T-AI平台域名
     */
    private String tAiDomain = "https://t-ai-aigc.app.terminus.io";

    /**
     * AI平台域名
     */
    private String platformDomain = "https://t-ai-platform-runtime.app.terminus.io";

    /**
     * AI的项目ID
     */
    private Long projectId = 1L;

    /**
     * AI的项目Code
     */
    private String projectCode = "t-ai";

    /**
     * AI的项目门户Key
     */
    private String portalKey = "t_ai_platform";

    /**
     * AI的服务的accessKey
     */
    private String accessKey;

    /**
     * AI的服务的accessSecret
     */
    private String accessSecret;

    /**
     * AI语音文件存储路径（语音转录音频文件）
     */
    private String storagePath = "/data/ai/storage";

    /**
     * 多轮对话场景下是否对上下文进行清理
     */
    private boolean asyncContextClean = false;

    /**
     * SSE消息体结构化
     */
    private boolean sseMessageBodyStructured = false;

    /**
     * AI-Proxy的配置
     */
    private AIProxy aiProxy;

    /**
     * AI转发代理配置
     */
    private Proxy proxy;

    /**
     * agent配置
     */
    private Agent agent = new Agent();

    /**
     * i18n智能翻译
     */
    private I18nTranslation i18nTranslation;

    /**
     * 模型过滤
     */
    private ModelFilter modelFilter;

    /**
     * ai记忆配置
     */
    private MemoryConfig memory = new MemoryConfig();

    /**
     * 支持的推理框架
     */
    private List<ReasoningFramework> reasoningFrameworks;

    public String getExecuteUrl(String serviceKey) {
        return platformDomain + API_PRE + "/" + serviceKey;
    }

    public boolean isNotAiProjectId(Object projectIdObj) {
        return projectIdObj == null || !projectIdObj.toString().equals(this.projectId.toString());
    }

    public boolean isNotAiProjectCode(String projectCode) {
        return projectCode == null || !projectCode.equals(this.projectCode);
    }

    @Data
    public static class Agent {
        /**
         * 是否启用缓存，默认为true
         */
        private boolean loaderCacheEnabled = true;
    }

    @Data
    public static class MemoryConfig {
        /**
         * 聊天会话的存储方式，redis,db
         */
        private String chatStore = "db";

        /**
         * 存储时长，默认是1天
         * 当 chat_store是redis是，需要设置ttl
         */
        private Duration chatCacheTtl = Duration.ofDays(1);

        /**
         * 聊天会话表，默认是：sys_common$chat_history_record
         * 当 chat_store是db是，需要设置保存的模型
         */
        private String chatHistoryModel = "sys_common$chat_history_record";

        /**
         * 聊天会话消息明细表，默认是：sys_common$chat_history_message_record
         * 当 chat_store是db是，需要设置保存的模型
         */
        private String chatHistoryMessageModel = "sys_common$chat_history_message_record";

        /**
         * 会话总结配置
         */
        private ChatSummarySetting chatSummary = new ChatSummarySetting();
    }

    @Data
    public static class ChatSummarySetting {
        /**
         * 总结类型，round,iterative
         */
        private String type = "round";

        /**
         * 大模型提供商
         */
        private String modelProvider = "openai";

        /**
         * 大模型名称
         */
        private String modelName = "gpt-4.1";

        @JsonIgnore
        public boolean isIterative() {
            return "iterative".equalsIgnoreCase(type);
        }
    }

//    @Data
//    public static class MilvusSetting {
//
//        /**
//         * Milvus 的db
//         */
//        private String dbName = "trantor_ai_long_term_memory";
//
//        /**
//         * Milvus 的collectionName前缀
//         */
//        private String collectionNamePrefix;
//
//    }

    @Data
    public static class AIProxy {
        /**
         * AI-Proxy服务域名
         */
        private String domain = "https://ai-proxy.erda.cloud";
        /**
         * AI-Proxy服务AK
         */
        private String accessKeyId;
    }

    @Data
    public static class Proxy {
        /**
         * AI转发代理是否开启
         */
        private boolean enable = true;

        /**
         * 转发时是否保留原始Cookie
         */
        private boolean preserverCookies = false;

        /**
         * 转发时是否保留原始Referer
         */
        private boolean preserverReferer = false;

        /**
         * 转发时忽略的请求头
         */
        private List<String> ignoreHeaders;

        /**
         * 需要转发的模块Key
         */
        private List<String> moduleKeys;

        /**
         * 需要转发的URL
         */
        private List<String> urlMappings;

        public List<String> getUrlMappings() {
            List<String> allMappings = new ArrayList<>();
            if (urlMappings != null) {
                allMappings.addAll(urlMappings);
            }
            if (moduleKeys != null) {
                for (String module : moduleKeys) {
                    allMappings.add(String.format(EXEC_API, module));
                    allMappings.add(String.format(EXEC_CHAT_API, module));
                    allMappings.add(String.format(AGENT_API, module));
                }
            }
            return allMappings;
        }
    }

    @Data
    public static class ProxyHeader {
        private String key;
        private String fromKey;
    }

    @Data
    public static class I18nTranslation {
        /**
         * 智能翻译每批次容量
         */
        private Integer translationBatchSize;
        /**
         * 智能翻译编排服务
         */
        private String translationServiceKey;
        /**
         * 翻译校对编排服务
         */
        private String translationProofreadServiceKey;
        /**
         * 翻译校对渠道
         */
        private String translationProofreadChannel;
        /**
         * 翻译校对率
         */
        private Double translationProofreadRate;
    }

    @Data
    public static class ModelFilter {
        /**
         * 模型名称过滤内容
         */
        private String nameContent;
        /**
         * 模型名称过滤排除项
         */
        private String nameExclude;
    }

    @Data
    public static class ReasoningFramework {
        private String key;
        private String name;
    }
}
