package io.terminus.trantor2.service.engine.impl.expression.value.primitives;

import io.terminus.trantor2.service.engine.impl.expression.ParserException;
import io.terminus.trantor2.service.engine.impl.expression.ast.AbstractNode;
import io.terminus.trantor2.service.engine.impl.expression.value.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.List;

/**
 * IsNotNullFunc
 *
 * <AUTHOR> Created on 2022/08/29
 */
public class IsNotNullFunc implements PrimFunc {

    @Override
    public Value apply(List<Value> args, AbstractNode location) {

        if (args.size() != 1) {
            throw new ParserException("is not null function args error:{}", location);
        }

        Value value = args.get(0);
        if (value instanceof MapValue) {
            return new BoolValue(MapUtils.isNotEmpty(((MapValue) value).getValue()));
        } else if (value instanceof ListValue) {
            return new BoolValue(CollectionUtils.isNotEmpty(((ListValue) value).getValue()));
        } else {
            return new BoolValue(!(value instanceof NullValue));
        }
    }

}
