package io.terminus.trantor2.service.engine.impl.expression.value.primitives;

import io.terminus.trantor2.service.engine.impl.expression.OperatorType;
import io.terminus.trantor2.service.engine.impl.expression.ParserException;
import io.terminus.trantor2.service.engine.impl.expression.ast.AbstractNode;
import io.terminus.trantor2.service.engine.impl.expression.value.*;

import java.util.List;

/**
 * BetweenAndFunc
 *
 * <AUTHOR> Created on 2022/08/29
 */
public class BetweenAndFunc implements PrimFunc {

    @Override
    public Value apply(List<Value> args, AbstractNode location) {

        if (args.size() != OperatorType.BINARY.getArgNum()) {
            throw new ParserException("between and function args error:{}", location);
        }
        if (args.get(0) instanceof DateValue && args.get(1) instanceof ListValue) {
            DateValue dateValue = (DateValue) args.get(0);
            ListValue listValue = (ListValue) args.get(1);
            DateValue afterDate = DateValue.valOf(listValue.getValue().get(0));
            DateValue beforeDate = DateValue.valOf(listValue.getValue().get(1));
            return new BoolValue(dateValue.getValue().before(beforeDate.getValue()) && dateValue.getValue().after(afterDate.getValue()));
        } else if (args.get(0) instanceof StringValue && args.get(1) instanceof ListValue) {
            DateValue dateValue = DateValue.fromStr(((StringValue) args.get(0)).getValue());
            ListValue listValue = (ListValue) args.get(1);
            DateValue afterDate = DateValue.valOf(listValue.getValue().get(0));
            DateValue beforeDate = DateValue.valOf(listValue.getValue().get(1));
            return new BoolValue(dateValue.getValue().before(beforeDate.getValue()) && dateValue.getValue().after(afterDate.getValue()));
        } else if (args.get(0) instanceof NumberValue && args.get(1) instanceof ListValue) {
            NumberValue numberValue = (NumberValue) args.get(0);
            ListValue listValue = (ListValue) args.get(1);
            NumberValue fromNum = (NumberValue) listValue.getVal(0);
            NumberValue toNum = (NumberValue) listValue.getVal(1);
            return new BoolValue((numberValue.getValue().compareTo(fromNum.getValue()) >= 0) && (numberValue.getValue().compareTo(toNum.getValue()) < 0));
        } else if (args.get(0) instanceof NullValue || args.get(1) instanceof NullValue) {
            return new BoolValue(false);
        } else {
            throw new ParserException("between and function args type error:{}", location);
        }
    }

}
