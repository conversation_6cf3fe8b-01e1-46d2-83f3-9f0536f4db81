package io.terminus.trantor2.service.engine.ai.memory;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Repository;

import java.util.*;

@Slf4j
@Repository
public class AIAgentMemoryFragmentRepository extends AbstractAIAgentMemoryRepository {
    private final ObjectMapper objectMapper = ObjectJsonUtil.MAPPER;

    public AIAgentMemoryFragmentRepository(RedissonClient redissonClient) {
        super(redissonClient);
    }

    @Override
    protected String getType() {
        return "fragments";
    }

    public List<Map<String, Object>> listAllMemoryFragments(String agentKey) {
        Long userId = getCurrentUserId();
        String listKey = buildKey(agentKey, userId);
        RList<String> fragmentList = redissonClient.getList(listKey);
        List<Map<String, Object>> fragments = new ArrayList<>();
        for (String fragmentJson : fragmentList) {
            try {
                Map<String, Object> fragment = objectMapper.readValue(fragmentJson, new TypeReference<>() {});
                fragments.add(fragment);
            } catch (Exception e) {
                log.error("listAllMemoryFragments error", e);
            }
        }
        return fragments;
    }

    public String createMemoryFragment(@Nonnull String agentKey, @Nonnull String fragmentContent) {
        Long userId = getCurrentUserId();
        String listKey = buildKey(agentKey, userId);
        String fullUuid = UUID.randomUUID().toString().replace("-", "");
        String fragmentId = fullUuid.substring(0, 8);
        long now = System.currentTimeMillis();
        Map<String, Object> fragmentData = new HashMap<>();
        fragmentData.put("id", fragmentId);
        fragmentData.put("content", fragmentContent);
        fragmentData.put("timestamp", now);
        RList<String> fragmentList = redissonClient.getList(listKey);
        if (fragmentList.size() > 100) {
            throw new ValidationException("记忆片段已超过100条，请手动删除多余片段");
        }
        try {
            String json = objectMapper.writeValueAsString(fragmentData);
            fragmentList.add(json);
        } catch (Exception e) {
            log.error("createMemoryFragment error", e);
        }
        return fragmentId;
    }

    public void updateMemoryFragment(@Nonnull String agentKey, @Nonnull String fragmentId, @Nonnull String fragmentContent) {
        Long userId = getCurrentUserId();
        String listKey = buildKey(agentKey, userId);
        RList<String> fragmentList = redissonClient.getList(listKey);
        for (int i = 0; i < fragmentList.size(); i++) {
            String fragmentJson = fragmentList.get(i);
            try {
                Map<String, Object> fragment = objectMapper.readValue(fragmentJson, new TypeReference<>() {});
                if (fragmentId.equals(fragment.get("id"))) {
                    fragment.put("content", fragmentContent);
                    fragment.put("timestamp", System.currentTimeMillis());
                    String updatedJson = objectMapper.writeValueAsString(fragment);
                    fragmentList.set(i, updatedJson);
                    break;
                }
            } catch (Exception e) {
                log.error("updateMemoryFragment error", e);
            }
        }
    }

    public void deleteMemoryFragment(@Nonnull String agentKey, @Nonnull String fragmentId) {
        Long userId = getCurrentUserId();
        String listKey = buildKey(agentKey, userId);
        RList<String> fragmentList = redissonClient.getList(listKey);
        Iterator<String> iterator = fragmentList.iterator();
        while (iterator.hasNext()) {
            String fragmentJson = iterator.next();
            try {
                Map<String, Object> fragment = objectMapper.readValue(fragmentJson, new TypeReference<>() {});
                if (fragmentId.equals(fragment.get("id"))) {
                    iterator.remove();
                    break;
                }
            } catch (Exception e) {
                log.error("deleteMemoryFragment error", e);
            }
        }
    }
}