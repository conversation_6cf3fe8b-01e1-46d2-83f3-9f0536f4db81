package io.terminus.trantor2.service.engine.ai.core.conversation.analyzer;

import io.terminus.trantor2.service.engine.ai.core.message.UserMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 意图分析器
 *
 * <AUTHOR> Created on 2025/1/16
 */
@Slf4j
public class IntentAnalyzer {

    // 常见意图模式
    private static final Map<String, Pattern> INTENT_PATTERNS = new HashMap<>();

    static {
        // 询问类意图
        INTENT_PATTERNS.put("询问", Pattern.compile("(什么|怎么|如何|为什么|哪里|哪个|谁|什么时候|多少)"));
        INTENT_PATTERNS.put("请求", Pattern.compile("(请|帮我|能否|可以|麻烦)"));
        INTENT_PATTERNS.put("确认", Pattern.compile("(是吗|对吗|是不是|对不对|确认|核实)"));
        INTENT_PATTERNS.put("否定", Pattern.compile("(不是|不对|错误|错了|没有|不是的)"));
        INTENT_PATTERNS.put("肯定", Pattern.compile("(是的|对的|正确|没错|有|是的)"));
        INTENT_PATTERNS.put("感谢", Pattern.compile("(谢谢|感谢|多谢|谢了)"));
        INTENT_PATTERNS.put("道歉", Pattern.compile("(抱歉|对不起|不好意思|抱歉)"));
        INTENT_PATTERNS.put("问候", Pattern.compile("(你好|您好|早上好|下午好|晚上好|再见|拜拜)"));
    }

    // 槽位提取模式
    private static final Map<String, Pattern> SLOT_PATTERNS = new HashMap<>();

    static {
        // 时间相关槽位
        SLOT_PATTERNS.put("时间", Pattern.compile("(今天|明天|昨天|本周|下周|上周|这个月|下个月|上个月|\\d{4}年|\\d{1,2}月|\\d{1,2}日)"));
        // 数量相关槽位
        SLOT_PATTERNS.put("数量", Pattern.compile("(\\d+个|\\d+次|\\d+遍|\\d+回|\\d+下|\\d+遍|\\d+次)"));
        // 地点相关槽位
        SLOT_PATTERNS.put("地点", Pattern.compile("(在|到|去|从).*(里|中|上|下|前|后|左|右|东|西|南|北)"));
        // 人物相关槽位
        SLOT_PATTERNS.put("人物", Pattern.compile("(我|你|他|她|我们|你们|他们|大家|某人|某某)"));
    }

    /**
     * 提取意图
     *
     * @param message 用户消息
     * @return 识别的意图
     */
    public String extractIntent(UserMessage message) {
        if (message == null || StringUtils.isBlank(message.getUserContent())) {
            return "未知";
        }

        String content = message.getUserContent().trim();

        // 按优先级匹配意图
        for (Map.Entry<String, Pattern> entry : INTENT_PATTERNS.entrySet()) {
            if (entry.getValue().matcher(content).find()) {
                log.debug("Intent extracted: {} from content: {}", entry.getKey(), content);
                return entry.getKey();
            }
        }

        // 默认意图
        return "一般对话";
    }

    /**
     * 提取槽位信息
     *
     * @param message 用户消息
     * @return 槽位信息映射
     */
    public Map<String, Object> extractSlots(UserMessage message) {
        Map<String, Object> slots = new HashMap<>();

        if (message == null || StringUtils.isBlank(message.getUserContent())) {
            return slots;
        }

        String content = message.getUserContent().trim();

        // 提取各种槽位
        for (Map.Entry<String, Pattern> entry : SLOT_PATTERNS.entrySet()) {
            Matcher matcher = entry.getValue().matcher(content);
            if (matcher.find()) {
                slots.put(entry.getKey(), matcher.group());
                log.debug("Slot extracted: {} = {} from content: {}", entry.getKey(), matcher.group(), content);
            }
        }

        // 提取关键词（简单的名词提取）
        extractKeywords(content, slots);

        return slots;
    }

    /**
     * 获取意图识别置信度
     *
     * @param message 用户消息
     * @return 置信度 (0.0 - 1.0)
     */
    public double getConfidence(UserMessage message) {
        if (message == null || StringUtils.isBlank(message.getUserContent())) {
            return 0.0;
        }

        String content = message.getUserContent().trim();
        String intent = extractIntent(message);

        // 基于模式匹配的置信度计算
        Pattern pattern = INTENT_PATTERNS.get(intent);
        if (pattern != null) {
            Matcher matcher = pattern.matcher(content);
            if (matcher.find()) {
                // 如果匹配到明确的模式，给予较高置信度
                return 0.8;
            }
        }

        // 默认置信度
        return 0.5;
    }

    /**
     * 提取关键词
     *
     * @param content 消息内容
     * @param slots   槽位映射
     */
    private void extractKeywords(String content, Map<String, Object> slots) {
        // 简单的关键词提取：提取长度大于1的连续中文字符
        Pattern keywordPattern = Pattern.compile("[\\u4e00-\\u9fa5]{2,}");
        Matcher matcher = keywordPattern.matcher(content);

        StringBuilder keywords = new StringBuilder();
        while (matcher.find()) {
            if (keywords.length() > 0) {
                keywords.append(",");
            }
            keywords.append(matcher.group());
        }

        if (keywords.length() > 0) {
            slots.put("关键词", keywords.toString());
        }
    }
}
