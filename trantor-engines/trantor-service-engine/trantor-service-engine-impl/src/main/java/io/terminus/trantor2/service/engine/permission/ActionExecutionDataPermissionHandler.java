package io.terminus.trantor2.service.engine.permission;

import io.terminus.trantor2.common.dto.PermissionExecuteContext;

/**
 * action 数据权限处理器
 *
 * <AUTHOR>
 * 2024/8/6 14:34
 **/
public interface ActionExecutionDataPermissionHandler {

    /**
     * 植入数据权限查询条件
     *
     * @param context          数据权限执行上下文
     * @param dataConditionKey 数据规则标识
     * @param params           请求入参对象
     */
    void handle(PermissionExecuteContext context, String dataConditionKey, Object params);
}
