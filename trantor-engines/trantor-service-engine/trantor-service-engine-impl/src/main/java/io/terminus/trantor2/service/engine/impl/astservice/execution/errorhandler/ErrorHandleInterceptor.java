package io.terminus.trantor2.service.engine.impl.astservice.execution.errorhandler;

import com.google.auto.service.AutoService;
import io.terminus.trantor2.service.dsl.ServiceElement;
import io.terminus.trantor2.service.engine.impl.astservice.ExecutionContext;
import io.terminus.trantor2.service.engine.impl.astservice.execution.interceptor.NodeExecutionInterceptor;

/**
 * ErrorHandleInterceptor
 *
 * <AUTHOR> Created on 2024/5/21 12:05
 */
@SuppressWarnings("unused")
@AutoService(NodeExecutionInterceptor.class)
public class ErrorHandleInterceptor extends NodeExecutionInterceptor {

    @Override
    public void execute(ExecutionContext context) {
        try {
            next.execute(context);
        } catch (Throwable e) {
            ErrorHandlerFactory.handleCatch(getCurrentServiceElement(), context, e);
        }
    }

    @Override
    protected boolean supports(ServiceElement<?> serviceElement) {
        return serviceElement.hasErrorConfigs();
    }

    @Override
    protected int order() {
        return super.order() - 50;
    }

    @Override
    protected NodeExecutionInterceptor createNewInstance() {
        return new ErrorHandleInterceptor();
    }
}
