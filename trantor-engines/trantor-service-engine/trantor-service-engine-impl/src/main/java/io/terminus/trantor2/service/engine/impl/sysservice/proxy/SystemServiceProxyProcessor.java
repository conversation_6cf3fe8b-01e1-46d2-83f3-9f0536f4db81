package io.terminus.trantor2.service.engine.impl.sysservice.proxy;

import io.terminus.trantor2.service.engine.impl.sysservice.SystemService;
import org.springframework.beans.factory.config.BeanPostProcessor;

/**
 * SystemServiceProxyProcessor
 *
 * <AUTHOR> Created on 2023/10/22 23:25
 */
public class SystemServiceProxyProcessor implements BeanPostProcessor {

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) {
        if (bean instanceof SystemService) {
            return SystemServiceAdvice.createProxy((SystemService) bean);
        }
        return bean;
    }
}
