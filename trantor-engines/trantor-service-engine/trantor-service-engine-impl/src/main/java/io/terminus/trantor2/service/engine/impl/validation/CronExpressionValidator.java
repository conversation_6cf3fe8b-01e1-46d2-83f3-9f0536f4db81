package io.terminus.trantor2.service.engine.impl.validation;

import io.terminus.trantor2.common.exception.ValidationException;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * Cron 表达式验证器
 * 按照 Quartz CronExpression 规则进行验证 cron 表达式的周期时间是否小于1分钟
 *
 */
public class CronExpressionValidator {

    // Cron 字段的有效范围 (按照 Quartz 规范)
    private static final int[] SECONDS_RANGE = {0, 59};     // 秒：0-59
    private static final int[] MINUTES_RANGE = {0, 59};     // 分：0-59
    private static final int[] HOURS_RANGE = {0, 23};       // 时：0-23
    private static final int[] DAY_OF_MONTH_RANGE = {1, 31}; // 日：1-31
    private static final int[] MONTH_RANGE = {1, 12};       // 月：1-12
    private static final int[] DAY_OF_WEEK_RANGE = {1, 7};  // 周：1-7 (1=Sunday)
    private static final int[] YEAR_RANGE = {1970, 2099};   // 年：1970-2099

    // 月份名称映射 (Quartz 支持)
    private static final Set<String> MONTH_NAMES = new HashSet<>(Arrays.asList(
            "JAN", "FEB", "MAR", "APR", "MAY", "JUN",
            "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"
    ));

    // 星期名称映射 (Quartz 支持)
    private static final Set<String> DAY_NAMES = new HashSet<>(Arrays.asList(
            "SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"
    ));

    /**
     * 验证 cron 表达式，如果周期时间小于1分钟则抛出异常
     *
     * @param cronExpression cron 表达式
     * @throws ValidationException 当表达式格式错误或周期时间小于1分钟时抛出异常
     */
    public static void validateCronExpression(String cronExpression) {
        if (StringUtils.isBlank(cronExpression)) {
            return;
        }

        // 验证格式和字段值
        if (!isValidCronFormat(cronExpression)) {
            throw new ValidationException("Cron 表达式格式不正确: " + cronExpression);
        }

        // 检查周期时间是否小于1分钟
        if (hasIntervalLessThanOneMinute(cronExpression)) {
            throw new ValidationException("Cron 表达式的周期时间不能小于1分钟: " + cronExpression);
        }
    }

    /**
     * 检查 cron 表达式格式和字段值是否有效
     *
     * @param cronExpression cron 表达式
     * @return 是否有效
     */
    private static boolean isValidCronFormat(String cronExpression) {
        String[] parts = cronExpression.trim().split("\\s+");

        // 支持6字段（带秒）或7字段（带秒和年）的cron表达式
        if (parts.length < 6 || parts.length > 7) {
            return false;
        }

        // 验证每个字段
        if (parts.length == 6) {
            // 6字段格式：秒 分 时 日 月 周
            return isValidSecondsField(parts[0]) &&
                    isValidMinutesField(parts[1]) &&
                    isValidHoursField(parts[2]) &&
                    isValidDayOfMonthField(parts[3]) &&
                    isValidMonthField(parts[4]) &&
                    isValidDayOfWeekField(parts[5]);
        } else {
            // 7字段格式：秒 分 时 日 月 周 年
            return isValidSecondsField(parts[0]) &&
                    isValidMinutesField(parts[1]) &&
                    isValidHoursField(parts[2]) &&
                    isValidDayOfMonthField(parts[3]) &&
                    isValidMonthField(parts[4]) &&
                    isValidDayOfWeekField(parts[5]) &&
                    isValidYearField(parts[6]);
        }
    }

    /**
     * 验证秒字段
     */
    private static boolean isValidSecondsField(String field) {
        return isValidNumericField(field, SECONDS_RANGE, false);
    }

    /**
     * 验证分钟字段
     */
    private static boolean isValidMinutesField(String field) {
        return isValidNumericField(field, MINUTES_RANGE, false);
    }

    /**
     * 验证小时字段
     */
    private static boolean isValidHoursField(String field) {
        return isValidNumericField(field, HOURS_RANGE, false);
    }

    /**
     * 验证日期字段 (支持特殊字符 L, W)
     */
    private static boolean isValidDayOfMonthField(String field) {
        if (StringUtils.isBlank(field)) {
            return false;
        }

        // 处理特殊字符
        if ("*".equals(field) || "?".equals(field)) {
            return true;
        }

        // 处理 L (last day of month)
        if (field.contains("L")) {
            return isValidLastDayExpression(field);
        }

        // 处理 W (weekday)
        if (field.contains("W")) {
            return isValidWeekdayExpression(field);
        }

        return isValidNumericField(field, DAY_OF_MONTH_RANGE, false);
    }

    /**
     * 验证月份字段 (支持月份名称)
     */
    private static boolean isValidMonthField(String field) {
        return isValidNumericField(field, MONTH_RANGE, true);
    }

    /**
     * 验证星期字段 (支持星期名称和特殊字符 L, #)
     */
    private static boolean isValidDayOfWeekField(String field) {
        if (StringUtils.isBlank(field)) {
            return false;
        }

        // 处理特殊字符
        if ("*".equals(field) || "?".equals(field)) {
            return true;
        }

        // 处理 L (last occurrence)
        if (field.contains("L")) {
            return isValidLastOccurrenceExpression(field);
        }

        // 处理 # (nth occurrence)
        if (field.contains("#")) {
            return isValidNthOccurrenceExpression(field);
        }

        return isValidDayOfWeekNumericField(field);
    }

    /**
     * 验证年份字段
     */
    private static boolean isValidYearField(String yearField) {
        if (StringUtils.isBlank(yearField)) {
            return true; // 年份字段可以为空
        }

        return isValidNumericField(yearField, YEAR_RANGE, false);
    }

    /**
     * 验证数字字段 (通用方法)
     *
     * @param field 字段值
     * @param range 有效范围
     * @param allowNames 是否允许名称 (如月份名称、星期名称)
     * @return 是否有效
     */
    private static boolean isValidNumericField(String field, int[] range, boolean allowNames) {
        if (StringUtils.isBlank(field)) {
            return false;
        }

        // 处理特殊字符
        if ("*".equals(field) || "?".equals(field)) {
            return true;
        }

        // 处理逗号分隔的值列表
        String[] values = field.split(",");
        for (String value : values) {
            if (!isValidSingleValue(value.trim(), range, allowNames)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 验证星期字段的数字部分
     */
    private static boolean isValidDayOfWeekNumericField(String field) {
        if (StringUtils.isBlank(field)) {
            return false;
        }

        // 处理特殊字符
        if ("*".equals(field) || "?".equals(field)) {
            return true;
        }

        // 处理逗号分隔的值列表
        String[] values = field.split(",");
        for (String value : values) {
            if (!isValidDayOfWeekSingleValue(value.trim())) {
                return false;
            }
        }

        return true;
    }

    /**
     * 验证星期字段的单个值 (支持名称和数字)
     */
    private static boolean isValidDayOfWeekSingleValue(String value) {
        if (StringUtils.isBlank(value)) {
            return false;
        }

        // 处理特殊字符
        if ("*".equals(value) || "?".equals(value)) {
            return true;
        }

        // 处理步长表达式
        if (value.contains("/")) {
            String[] parts = value.split("/");
            if (parts.length != 2) {
                return false;
            }

            String baseValue = parts[0];
            String stepValue = parts[1];

            // 验证步长值
            try {
                int step = Integer.parseInt(stepValue);
                if (step <= 0) {
                    return false;
                }
                
                // 步长值不能超过星期字段的最大值 (7)
                if (step > DAY_OF_WEEK_RANGE[1]) {
                    return false;
                }

                // 验证基础值
                if ("*".equals(baseValue)) {
                    return step <= 7;
                } else if (baseValue.contains("-")) {
                    return isValidDayOfWeekRangeValue(baseValue);
                } else {
                    return isValidDayOfWeekValue(baseValue);
                }
            } catch (NumberFormatException e) {
                return false;
            }
        }

        // 处理范围表达式
        if (value.contains("-")) {
            return isValidDayOfWeekRangeValue(value);
        }

        // 处理单个值
        return isValidDayOfWeekValue(value);
    }

    /**
     * 验证星期字段的单个值 (数字或名称)
     */
    private static boolean isValidDayOfWeekValue(String value) {
        // 尝试作为数字解析
        try {
            int num = Integer.parseInt(value);
            return num >= DAY_OF_WEEK_RANGE[0] && num <= DAY_OF_WEEK_RANGE[1];
        } catch (NumberFormatException e) {
            // 尝试作为名称解析
            return DAY_NAMES.contains(value.toUpperCase());
        }
    }

    /**
     * 验证星期字段的范围值
     */
    private static boolean isValidDayOfWeekRangeValue(String rangeValue) {
        String[] parts = rangeValue.split("-");
        if (parts.length != 2) {
            return false;
        }

        return isValidDayOfWeekValue(parts[0]) && isValidDayOfWeekValue(parts[1]);
    }

    /**
     * 验证单个值是否有效
     *
     * @param value      单个值
     * @param range      有效范围
     * @param allowNames 是否允许名称
     * @return 是否有效
     */
    private static boolean isValidSingleValue(String value, int[] range, boolean allowNames) {
        if (StringUtils.isBlank(value)) {
            return false;
        }

        // 处理特殊字符
        if ("*".equals(value) || "?".equals(value)) {
            return true;
        }

        // 处理步长表达式 (例如: */30, 0/15, 1-10/2)
        if (value.contains("/")) {
            String[] parts = value.split("/");
            if (parts.length != 2) {
                return false;
            }

            String baseValue = parts[0];
            String stepValue = parts[1];

            // 验证步长值
            try {
                int step = Integer.parseInt(stepValue);
                if (step <= 0) {
                    return false;
                }
                
                // 步长值不能超过字段的最大值 (对于秒和分钟字段，步长不能超过59)
                if (step > range[1]) {
                    return false;
                }

                // 验证基础值
                if ("*".equals(baseValue)) {
                    return step <= (range[1] - range[0] + 1);
                } else if (baseValue.contains("-")) {
                    return isValidRangeValue(baseValue, range, allowNames);
                } else {
                    return isValidSimpleValue(baseValue, range, allowNames);
                }
            } catch (NumberFormatException e) {
                return false;
            }
        }

        // 处理范围表达式 (例如: 1-10)
        if (value.contains("-")) {
            return isValidRangeValue(value, range, allowNames);
        }

        // 处理单个数字或名称
        return isValidSimpleValue(value, range, allowNames);
    }

    /**
     * 验证简单值 (数字或名称)
     */
    private static boolean isValidSimpleValue(String value, int[] range, boolean allowNames) {
        // 尝试作为数字解析
        try {
            int num = Integer.parseInt(value);
            return num >= range[0] && num <= range[1];
        } catch (NumberFormatException e) {
            // 如果允许名称，尝试作为名称解析
            if (allowNames) {
                return MONTH_NAMES.contains(value.toUpperCase());
            }
            return false;
        }
    }

    /**
     * 验证范围值是否有效
     *
     * @param rangeValue 范围值 (例如: 1-10)
     * @param range      有效范围
     * @param allowNames 是否允许名称
     * @return 是否有效
     */
    private static boolean isValidRangeValue(String rangeValue, int[] range, boolean allowNames) {
        String[] parts = rangeValue.split("-");
        if (parts.length != 2) {
            return false;
        }

        return isValidSimpleValue(parts[0], range, allowNames) &&
                isValidSimpleValue(parts[1], range, allowNames);
    }

    /**
     * 验证 L 表达式 (last day of month)
     */
    private static boolean isValidLastDayExpression(String field) {
        if ("L".equals(field)) {
            return true;
        }

        // L-n 格式 (n days before last day)
        if (field.matches("L-\\d+")) {
            String numberPart = field.substring(2);
            try {
                int offset = Integer.parseInt(numberPart);
                return offset >= 1 && offset <= 30;
            } catch (NumberFormatException e) {
                return false;
            }
        }

        return false;
    }

    /**
     * 验证 W 表达式 (weekday)
     */
    private static boolean isValidWeekdayExpression(String field) {
        if ("LW".equals(field)) {
            return true;
        }

        // nW 格式 (weekday nearest to day n)
        if (field.matches("\\d+W")) {
            String numberPart = field.substring(0, field.length() - 1);
            try {
                int day = Integer.parseInt(numberPart);
                return day >= 1 && day <= 31;
            } catch (NumberFormatException e) {
                return false;
            }
        }

        return false;
    }

    /**
     * 验证 L 表达式在星期字段中的使用 (last occurrence)
     */
    private static boolean isValidLastOccurrenceExpression(String field) {
        if ("L".equals(field)) {
            return true; // 表示星期六
        }

        // nL 格式 (last occurrence of day n)
        if (field.matches("[1-7]L") || field.matches("(SUN|MON|TUE|WED|THU|FRI|SAT)L")) {
            return true;
        }

        return false;
    }

    /**
     * 验证 # 表达式 (nth occurrence)
     */
    private static boolean isValidNthOccurrenceExpression(String field) {
        // n#m 格式 (mth occurrence of day n)
        if (field.matches("[1-7]#[1-5]") ||
                field.matches("(SUN|MON|TUE|WED|THU|FRI|SAT)#[1-5]")) {
            return true;
        }

        return false;
    }

    /**
     * 检查 cron 表达式是否包含小于1分钟的周期
     *
     * @param cronExpression cron 表达式
     * @return 是否包含小于1分钟的周期
     */
    private static boolean hasIntervalLessThanOneMinute(String cronExpression) {
        String[] parts = cronExpression.trim().split("\\s+");

        // 检查秒字段 (第1个字段)
        String secondsField = parts[0];
        if (hasIntervalLessThanSixtySeconds(secondsField)) {
            return true;
        }

        // 检查分钟字段是否有小于1分钟的间隔
        String minutesField = parts[1];
        return hasIntervalLessThanOneMinuteInMinutesField(minutesField);
    }

    /**
     * 检查秒字段是否有小于60秒的间隔
     *
     * @param secondsField 秒字段
     * @return 是否有小于60秒的间隔
     */
    private static boolean hasIntervalLessThanSixtySeconds(String secondsField) {
        if (StringUtils.isBlank(secondsField)) {
            return false;
        }

        // 检查通配符 - 这会导致每秒执行，间隔为1秒
        if ("*".equals(secondsField)) {
            return true;
        }

        // 处理步长表达式
        if (secondsField.contains("/")) {
            String[] parts = secondsField.split("/");
            if (parts.length == 2) {
                try {
                    int step = Integer.parseInt(parts[1]);
                    return step < 60;
                } catch (NumberFormatException e) {
                    return false;
                }
            }
        }

        return false;
    }

    /**
     * 检查分钟字段是否有小于1分钟的间隔
     *
     * @param minutesField 分钟字段
     * @return 是否有小于1分钟的间隔
     */
    private static boolean hasIntervalLessThanOneMinuteInMinutesField(String minutesField) {
        if (StringUtils.isBlank(minutesField) || "*".equals(minutesField)) {
            return false;
        }

        // 处理步长表达式
        if (minutesField.contains("/")) {
            String[] parts = minutesField.split("/");
            if (parts.length == 2) {
                try {
                    int step = Integer.parseInt(parts[1]);
                    return step < 1;
                } catch (NumberFormatException e) {
                    return false;
                }
            }
        }

        return false;
    }
}
