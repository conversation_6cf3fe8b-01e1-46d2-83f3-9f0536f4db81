package io.terminus.trantor2.service.engine.impl.astservice.execution;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.service.dsl.ServiceElement;
import io.terminus.trantor2.service.dsl.enums.Propagation;
import io.terminus.trantor2.service.dsl.properties.ActionProperties;
import io.terminus.trantor2.service.dsl.properties.Field;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.exception.ServiceNotFoundException;
import io.terminus.trantor2.service.engine.executor.interceptor.Contexts;
import io.terminus.trantor2.service.engine.executor.interceptor.TransactionTemplate;
import io.terminus.trantor2.service.engine.impl.astservice.ExecutionContext;
import io.terminus.trantor2.service.engine.impl.astservice.spi.ExecutionDelegate;
import io.terminus.trantor2.service.engine.impl.astservice.spi.ExecutionDelegateFactory;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Objects;

/**
 * ActionExecution
 *
 * <AUTHOR> Created on 2023/2/24 18:24
 */
public class ActionExecution extends AbstractExecution {
    public ActionExecution(ServiceElement<?> currentNode) {
        super(currentNode);
    }

    @Override
    public void execute(ExecutionContext context) {
        final ActionProperties props = getProps(ActionProperties.class);
        final Object result = executeAction(props, context);
        // action结果为null不进行赋值，这样写是为了兼容事件那边的写法，原则上返回什么值就赋什么值
        if (result != null && CollectionUtils.isNotEmpty(props.getOutput())) {
            Field outputField = props.getOutput().get(0);
            setExecutionResult(context, MapUtil.of(outputField.getFieldKey(), result), props.getOutputAssign());
            Contexts.getServiceTrace().trace("序列化后Action节点出参", result);
        } else {
            Contexts.getServiceTrace().trace("无节点出参", "无节点出参结构");
        }
    }

    private Object executeAction(ActionProperties props, ExecutionContext context) {
        final ExecutionDelegate executionDelegate = getExecutionDelegate(props, context);
        if (Objects.isNull(executionDelegate)) {
            throw new ServiceNotFoundException(ErrorType.SERVICE_SPI_NOT_FOUND, new Object[]{props.getImplementation()});
        }

        final Object result;
        if (Propagation.nonTransaction(props.getTransactionPropagation())) {
            result = executionDelegate.execute(getCurrentServiceElement(), context);
        } else if (context.getServiceEngine().getTransactionTemplate() == null) {
            result = executionDelegate.execute(getCurrentServiceElement(), context);
        } else {
            // 事务执行
            TransactionTemplate.TransactionInvocation transactionInvocation = new TransactionTemplate.TransactionInvocation();
            transactionInvocation.setTransactionPropagation(props.getTransactionPropagation());
            transactionInvocation.setServiceKey(Key.of(context.getTeamId(), props.getImplementation()));
            transactionInvocation.setAction(() -> executionDelegate.execute(getCurrentServiceElement(), context));
            result = context.getServiceEngine().getTransactionTemplate().execute(transactionInvocation);
        }

        if (MapUtil.isMap(result)) {
            return MapUtil.toMap(result);
        }
        return result;
    }

    private ExecutionDelegate getExecutionDelegate(ActionProperties properties, ExecutionContext context) {
        final ExecutionDelegateFactory factory = getExecutionDelegateFactory(properties, context);
        return factory.create(Key.of(context.getTeamId(), properties.getImplementation()));
    }

    private ExecutionDelegateFactory getExecutionDelegateFactory(ActionProperties properties, ExecutionContext context) {
        if (properties.isNewAction()) {
            return context.getServiceEngine().getActionExecutionDelegateFactory();
        } else {
            return context.getServiceEngine().getSpiExecutionDelegateFactory();
        }
    }
}
