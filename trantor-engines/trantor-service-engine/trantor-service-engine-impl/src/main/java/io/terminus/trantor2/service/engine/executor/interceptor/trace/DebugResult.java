package io.terminus.trantor2.service.engine.executor.interceptor.trace;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.service.engine.impl.debug.DebugLocation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DebugResult
 *
 * <AUTHOR> Created on 2023/9/4 16:04
 */
@NoArgsConstructor
@Data
@Schema(description = "Debug结果")
public class DebugResult {

    @Schema(description = "服务运行结果")
    private Object serviceResult;

    @Schema(description = "服务Debug的位置")
    private DebugLocation debugLocation;

    @Schema(description = "服务运行日志")
    private List<TraceInfo> debugInfos;

    @Schema(description = "调试回调接口")
    private String callback;

    public DebugResult(Object serviceResult, List<TraceInfo> debugInfos) {
        this.serviceResult = serviceResult;
        this.debugInfos = debugInfos;
    }

    public DebugResult(Object serviceResult) {
        this.serviceResult = serviceResult;
    }

    public DebugResult(DebugLocation debugLocation) {
        this.debugLocation = debugLocation;
    }
}
