package io.terminus.trantor2.service.engine.executor.interceptor;

import io.terminus.common.api.exception.BusinessException;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorBizException;
import io.terminus.trantor2.service.common.consts.VariableKey;
import io.terminus.trantor2.service.common.exception.ServiceBizException;
import io.terminus.trantor2.service.engine.executor.ServiceInvocation;
import io.terminus.trantor2.service.engine.executor.interceptor.trace.DebugResult;
import io.terminus.trantor2.service.engine.executor.interceptor.trace.DebugRollbackException;
import io.terminus.trantor2.service.engine.executor.interceptor.trace.ServiceTrace;
import io.terminus.trantor2.service.engine.executor.interceptor.trace.TraceInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * TraceInterceptor
 *
 * <AUTHOR> Created on 2023/2/17 11:59
 */
public class TraceInterceptor extends ServiceInterceptor {

    @Override
    public Object execute(ServiceInvocation invocation) {
        ServiceTrace serviceTrace = Contexts.getServiceTraceOfNullable();

        boolean traceReused = false;

        if (serviceTrace == null) {
            serviceTrace = new ServiceTrace(Contexts.getTraceId(), isDebugEnabled(invocation));
            Contexts.setServiceTrace(serviceTrace);
        } else {
            traceReused = true;
        }

        try {
            if (!traceReused && serviceTrace.isDebugEnabled()) {
                return debugExecute(invocation);
            } else {
                return getNext().execute(invocation);
            }
        } finally {
            if (!traceReused) {
                try {
                    serviceTrace.close();
                } finally {
                    Contexts.removeServiceTrace();
                }
            }
        }
    }

    private boolean isDebugEnabled(ServiceInvocation invocation) {
        return Boolean.TRUE.equals(invocation.getArgs().getAttribute(VariableKey.DEBUG_ENABLED));
    }

    private Object debugExecute(ServiceInvocation invocation) {
        Throwable err = null;
        Object result = null;
        try {
            result = getNext().execute(invocation);
        } catch (Throwable e) {
            // 优先使用ServiceTrace内的异常
            err = Contexts.getServiceTrace().getException();
            if (e instanceof DebugRollbackException) {
                result = ((DebugRollbackException) e).getData();
            } else {
                if (err == null) {
                    err = e;
                }
            }
        }
        return handleResult(result, err);
    }

    private Object handleResult(Object result, Throwable err) {
        List<TraceInfo> debugInfos = new ArrayList<>(Contexts.getServiceTrace().getTraceInfos());
        if (err != null) {
            return new DebugResult(Response.error(getErrorCode(err), getErrorMsg(err)), debugInfos);
        } else {
            return new DebugResult(Response.ok(result), debugInfos);
        }
    }

    private String getErrorCode(Throwable exception) {
        if (exception instanceof TrantorBizException) {
            return ((TrantorBizException) exception).getErrorCode();
        } else if (exception instanceof ServiceBizException) {
            return ((ServiceBizException) exception).getErrorCode();
        } else if (exception instanceof BusinessException) {
            return ((BusinessException) exception).getErrorCode();
        }
        return ErrorType.SERVER_ERROR.getCode();
    }

    private String getErrorMsg(Throwable exception) {
        if (exception instanceof BusinessException) {
            return ((BusinessException) exception).getErrorMsg();
        }
        return exception.getMessage();
    }
}
