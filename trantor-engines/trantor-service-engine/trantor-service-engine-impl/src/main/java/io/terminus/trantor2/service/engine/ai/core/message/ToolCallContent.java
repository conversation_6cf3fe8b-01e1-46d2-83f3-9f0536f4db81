package io.terminus.trantor2.service.engine.ai.core.message;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * ToolCallContent
 *
 * <AUTHOR> Created on 2025/4/12 12:55
 */
@Setter
@Getter
@NoArgsConstructor
public class ToolCallContent implements Content {
    private static final long serialVersionUID = -8878753840376700537L;

    private ContentType type = ContentType.FUNCTION_CALL;
    private AIProgressStatus status = AIProgressStatus.DONE;
    private String callId;
    private String key;
    private String name;
    private String arguments;
    private boolean visible = true;
}
