package io.terminus.trantor2.service.engine.impl.astservice;

import io.terminus.trantor2.service.dsl.enums.AssignOperator;
import io.terminus.trantor2.service.engine.delegate.Arguments;

import java.util.List;
import java.util.Map;

/**
 * ExecutionContext
 *
 * <AUTHOR> Created on 2023/2/17 00:03
 */
public class SyncExecutionContext extends ExecutionContext {

    public SyncExecutionContext(ExecutionContext context) {
        super(Arguments.of(context.getTeamId(), context.getArgs()), context.getServiceEngine());
        super.setVariables(context.getVariables());
    }

    @Override
    public synchronized Map<String, Object> getVariables() {
        return super.getVariables();
    }

    @Override
    public synchronized void setVariable(String variableName, Object value) {
        super.setVariable(variableName, value);
    }

    @Override
    public synchronized void removeVariable(String variableName) {
        super.removeVariable(variableName);
    }

    @Override
    public synchronized Object getVariable(String variableName) {
        return super.getVariable(variableName);
    }

    @Override
    public synchronized Object getVariable(List<String> paths) {
        return super.getVariable(paths);
    }

    @Override
    public synchronized void setVariable(List<String> paths, Object value, AssignOperator operator) {
        super.setVariable(paths, value, operator);
    }

    @Override
    public synchronized void setVariable(List<String> paths, Object value) {
        super.setVariable(paths, value);
    }

    @Override
    public synchronized void removeVariable(List<String> paths) {
        super.removeVariable(paths);
    }
}
