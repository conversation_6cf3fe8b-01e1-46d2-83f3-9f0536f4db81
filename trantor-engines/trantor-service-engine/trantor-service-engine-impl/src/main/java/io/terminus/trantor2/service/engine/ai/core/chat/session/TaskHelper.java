package io.terminus.trantor2.service.engine.ai.core.chat.session;

import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.response.Response;
import io.terminus.common.scheduler.TScheduler;
import io.terminus.common.scheduler.client.JobDefinitionClient;
import io.terminus.common.scheduler.enums.JobTypeEnum;
import io.terminus.common.scheduler.model.JobDefinitionDTO;
import io.terminus.common.scheduler.request.JobDefinitionSaveRequest;
import io.terminus.common.scheduler.request.JobKeyRequest;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.service.engine.ai.core.message.AttachmentsContent;
import io.terminus.trantor2.service.engine.ai.core.message.Content;
import io.terminus.trantor2.service.engine.ai.core.message.TaskStartContent;
import io.terminus.trantor2.service.engine.ai.core.message.TextContent;
import io.terminus.trantor2.service.engine.ai.core.message.UserMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * JobCreator
 *
 * <AUTHOR> Created on 2025/6/25 19:02
 */
@Slf4j
@RequiredArgsConstructor
public class TaskHelper {

    private final JobDefinitionClient jobDefinitionClient;

    public void createAsyncTask(ChatSession session, UserMessage userMessage, TaskStartContent taskStartContent) {
        TrantorContext.Context trantorContext = session.getTrantorContext();
        String bizKey = session.getBizKey();
        String moduleKey = StringUtils.contains(bizKey, "$")
                ? KeyUtil.moduleKey(bizKey)
                : trantorContext.getModuleKey();
        String jobApp = trantorContext.getTeamCode();
        String jobKey = moduleKey + "$TAgentTask_JOB";

        JobKeyRequest request = new JobKeyRequest();
        request.setApp(jobApp);
        request.setJobKey(jobKey);
        Response<JobDefinitionDTO> res = jobDefinitionClient.findByJobKey(request);

        // 在Job定义不存在的情况下，创建一个新的Job定义
        if (!res.isSuccess() || res.getData() == null) {
            JobDefinitionSaveRequest saveRequest = new JobDefinitionSaveRequest();
            saveRequest.setApp(jobApp);
            saveRequest.setModule(moduleKey);
            saveRequest.setJobKey(jobKey);
            saveRequest.setJobName("TAgentTask");
            saveRequest.setJobType(JobTypeEnum.SecondDelay);
            saveRequest.setExecType("TAgentTask");
            saveRequest.setStatus("ENABLED");
            jobDefinitionClient.create(saveRequest);
        } else {
            // 如果Job定义存在，但状态不是ENABLED，则进行启用
            if (!"ENABLED".equalsIgnoreCase(res.getData().getStatus())) {
                jobDefinitionClient.enable(new IdRequest(res.getData().getId()));
            }
        }

        // 构建用于恢复Trantor上下文内容
        Map<String, Object> trantorContent = new HashMap<>();
        trantorContent.put("traceId", TrantorContext.getTraceId());
        trantorContent.put("teamId", trantorContext.getTeamId());
        trantorContent.put("teamCode", trantorContext.getTeamCode());
        trantorContent.put("appCode", moduleKey);
        trantorContent.put("moduleKey", moduleKey);
        trantorContent.put("portalCode", trantorContext.getPortalCode());
        trantorContent.put("userId", session.getCurrentUserId());

        // 恢复用户提问问题
        Map<String, Object> userContent = new HashMap<>(3);
        userContent.put("sessionId", session.getSessionId());
        userContent.put("resendMessageId", session.getCurrentUserMessageId());
        userContent.put("skipEvaluation", true);
        if (userMessage != null && userMessage.getContent() != null) {
            for (Content content : userMessage.getContent()) {
                if (content instanceof AttachmentsContent attachmentsContent) {
                    userContent.put("attachments", attachmentsContent.getAttachments());
                }

                if (content instanceof TextContent textContent) {
                    userContent.put("userContent", textContent.getText());
                }
            }
        }
        // 用newInput覆盖掉
        if (StringUtils.isNotBlank(taskStartContent.getNewInput())) {
            userContent.put("userContent", taskStartContent.getNewInput());
        }

        TaskParam taskParam = TaskParam.builder()
                .taskId(taskStartContent.getTaskId())
                .taskName(taskStartContent.getTaskName())
                .arguments(taskStartContent.getArguments())
                .currentUserMessageId(session.getCurrentUserMessageId())
                .currentUserId(session.getCurrentUserId())
                .sessionId(session.getSessionId())
                .userContent(userContent)
                .bizKey(bizKey)
                .trantorContext(trantorContent)
                .build();

        String jobId = TScheduler.newSecondDelayJob(jobKey, taskStartContent.getTaskId(), taskParam, 5L);

        log.info("[{}]异步任务创建成功,jobId:{},task:{}", TrantorContext.getTraceId(), jobId, JsonUtil.toJson(taskParam));
    }
}
