package io.terminus.trantor2.service.engine.impl.astservice.execution;

import com.google.common.collect.Lists;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.service.common.consts.ServiceConst;
import io.terminus.trantor2.service.dsl.ServiceElement;
import io.terminus.trantor2.service.dsl.properties.CascadeCreateDataProperties;
import io.terminus.trantor2.service.dsl.properties.CascadeDataProcessingProperties;
import io.terminus.trantor2.service.dsl.properties.CascadeDeleteDataProperties;
import io.terminus.trantor2.service.dsl.properties.CascadeFindDataProperties;
import io.terminus.trantor2.service.dsl.properties.CascadeUpdateDataProperties;
import io.terminus.trantor2.service.dsl.properties.CreateDataProperties;
import io.terminus.trantor2.service.dsl.properties.DataProcessingProperties;
import io.terminus.trantor2.service.dsl.properties.DeleteDataProperties;
import io.terminus.trantor2.service.dsl.properties.FindTreeDataProperties;
import io.terminus.trantor2.service.dsl.properties.MultiRetrieveDataProperties;
import io.terminus.trantor2.service.dsl.properties.PagingDataProperties;
import io.terminus.trantor2.service.dsl.properties.RetrieveDataProperties;
import io.terminus.trantor2.service.dsl.properties.UpdateDataProperties;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.exception.ServiceExecuteException;
import io.terminus.trantor2.service.engine.exception.ServiceNotFoundException;
import io.terminus.trantor2.service.engine.impl.astservice.ExecutionContext;
import io.terminus.trantor2.service.engine.impl.component.ModelDataRepository;
import io.terminus.trantor2.service.engine.impl.component.bean.QueryModel;
import io.terminus.trantor2.service.engine.impl.sysservice.bean.SystemServiceParameter;
import io.terminus.trantor2.service.engine.impl.value.ValueFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Map;
import java.util.Objects;

/**
 * DataProcessingExecution
 *
 * <AUTHOR> Created on 2023/2/17 11:27
 */
@Slf4j
public abstract class BaseDataProcessingExecution extends AbstractExecution {
    public BaseDataProcessingExecution(ServiceElement<?> currentNode) {
        super(currentNode);
    }

    @Override
    public void execute(ExecutionContext context) {
        if (Objects.isNull(getRepository(context))) {
            throw new ServiceNotFoundException(ErrorType.SERVICE_MODEL_REPO_NOT_FOUND);
        }

        final DataProcessingProperties props = getProps(DataProcessingProperties.class);

        // 校验组织开关
        validate(props.getModelKey(), context);

        Object modelData = null;
        // 新增数据
        if (props instanceof CreateDataProperties) {
            modelData = createData((CreateDataProperties) props, context);
        }
        // 更新数据
        else if (props instanceof UpdateDataProperties) {
            updateData((UpdateDataProperties) props, context);
        }
        // 查询多条数据
        else if (props instanceof MultiRetrieveDataProperties) {
            modelData = queryBatchData((MultiRetrieveDataProperties) props, context);
        }
        // 查询单条数据
        else if (props instanceof RetrieveDataProperties) {
            modelData = queryData((RetrieveDataProperties) props, context);
        }
        // 查询分页数据
        else if (props instanceof PagingDataProperties) {
            modelData = queryPagingData((PagingDataProperties) props, context);
        }
        // 删除数据
        else if (props instanceof DeleteDataProperties) {
            deleteData((DeleteDataProperties) props, context);
        }
        // 级联新增
        else if (props instanceof CascadeCreateDataProperties) {
            modelData = cascadeCreateData((CascadeCreateDataProperties) props, context);
        }
        // 级联删除
        else if (props instanceof CascadeDeleteDataProperties) {
            cascadeDeleteData((CascadeDeleteDataProperties) props, context);
        }
        // 级联更新
        else if (props instanceof CascadeUpdateDataProperties) {
            cascadeUpdateData((CascadeUpdateDataProperties) props, context);
        }
        // 级联查询
        else if (props instanceof CascadeFindDataProperties) {
            modelData = cascadeFindData((CascadeFindDataProperties) props, context);
        }
        // 查询树
        else if (props instanceof FindTreeDataProperties) {
            modelData = findTreeData((FindTreeDataProperties) props, context);
        }

        if (modelData != null) {
            setExecutionResult(context, modelData, props.getOutputAssign());
        }
    }

    protected abstract void cascadeUpdateData(CascadeUpdateDataProperties props, ExecutionContext context);

    protected abstract void cascadeDeleteData(CascadeDeleteDataProperties props, ExecutionContext context);

    protected abstract Object cascadeCreateData(CascadeCreateDataProperties props, ExecutionContext context);

    protected abstract Object queryData(RetrieveDataProperties props, ExecutionContext context);

    @Deprecated
    protected abstract Object findTreeData(FindTreeDataProperties props, ExecutionContext context);

    @Deprecated
    protected abstract Object cascadeFindData(CascadeFindDataProperties props, ExecutionContext context);

    @Deprecated
    protected abstract Object queryPagingData(PagingDataProperties props, ExecutionContext context);

    @Deprecated
    protected abstract Object queryBatchData(MultiRetrieveDataProperties props, ExecutionContext context);

    @Deprecated
    protected abstract Object createData(CreateDataProperties props, ExecutionContext context);

    @Deprecated
    protected abstract void updateData(UpdateDataProperties props, ExecutionContext context);

    @Deprecated
    protected abstract void deleteData(DeleteDataProperties props, ExecutionContext context);

    protected void validateQueryData(RetrieveDataProperties props, QueryModel queryModel, Object result) {
        if (ObjectUtils.isEmpty(result)) {
            if (props.isStopWhenDataEmpty()) {
                throw new ServiceExecuteException(ErrorType.SERVICE_EXECUTE_NOT_QUERY_MODEL_DATA, new Object[]{props.getModelKey()});
            } else {
                log.warn("service query data empty,node:{},model:{}. 参数:{}", getCurrentNodeKey(),
                        props.getModelKey(), JsonUtil.toJson(queryModel));
            }
        }
    }

    protected final ModelDataRepository getRepository(ExecutionContext context) {
        return context.getServiceEngine().getModelDataRepository();
    }

    protected Object callSystemService(String systemServiceKey, CascadeDataProcessingProperties props, ExecutionContext context) {
        final Object modelValue = ValueFactory.getValue(null, props.getModelValue(), context);
        final String newSysServiceKey = rewriteServiceKey(systemServiceKey, props.getModelKey());
        final SystemServiceParameter parameter = new SystemServiceParameter(props.getModelKey(), modelValue);
        final Map result = (Map) context.getServiceEngine().getServiceExecutor().execute(newSysServiceKey, context.newArguments(parameter));
        if (result != null) {
            return result.get(ServiceConst.DATA);
        }
        return null;
    }

    private String rewriteServiceKey(String serviceKey, String modelKey) {
        if (serviceKey.contains("$")) {
            return serviceKey;
        }
        return KeyUtil.moduleKey(modelKey) + "$" + serviceKey;
    }

    private void validate(String relatedModelAlias, ExecutionContext context) {
        Key modelKey = Key.of(context.getTeamId(), relatedModelAlias);
        context.getServiceEngine().getCommonFieldValidators().forEach(validator -> validator.validate(modelKey,
                (_fieldKey, _modelKey, _value) -> context.setVariable(Lists.newArrayList(_fieldKey, _modelKey), _value)));
    }
}
