package io.terminus.trantor2.service.engine.impl.expression;


import com.google.common.collect.Lists;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/27
 */
public class Constants {

    private Constants() {}

    public static final char SPACE = ' ';

    public static final char TAB = '\t';

    public static final char ENTER_R = '\r';

    public static final char ENTER_N = '\n';

    public static final String STRING_BEGIN = "\"";

    public static final String STRING_END = "\"";

    public static final String CHAR_BEGIN = "'";

    public static final String CHAR_END = "'";

    public static final Character PAREN_BEGIN = '(';

    public static final Character PAREN_END = ')';

    public static final Character LIST_BEGIN = '[';

    public static final Character LIST_END = ']';

    public static final String FIELD_ACCESS = ".";

    public static final Character COMMA = ',';

    /**
     * 标识符字符串列表
     */
    public static final List<Character> IDENT_CHARS = Lists.newArrayList('_');

    /**
     * 运算符字符串列表
     */
    public static final List<Character> SYMBOL_OPEATOR_CHARS = Lists.newArrayList('!', '%', '&', '|', '*', '/', '<', '=', '>',
            '~', '+', '-', '.');

    /**
     * 非符号运算符，例如 #EQ 需要保证和值之间用空格隔开
     */
    public static final Character NON_SYMBOL_OPERATOR_CHAR = '#';

    /**
     * 算符优先级表
     */
    public static final Map<String, Integer> BIN_OP_PRECEDENCE_MAP = new HashMap<>();

    static {
        BIN_OP_PRECEDENCE_MAP.put(".", 10);
        BIN_OP_PRECEDENCE_MAP.put("~", 9);
        BIN_OP_PRECEDENCE_MAP.put("!", 9);
        BIN_OP_PRECEDENCE_MAP.put("*", 8);
        BIN_OP_PRECEDENCE_MAP.put("/", 8);
        BIN_OP_PRECEDENCE_MAP.put("%", 8);
        BIN_OP_PRECEDENCE_MAP.put("+", 7);
        BIN_OP_PRECEDENCE_MAP.put("-", 7);
        BIN_OP_PRECEDENCE_MAP.put("==", 6);
        BIN_OP_PRECEDENCE_MAP.put("!=", 6);
        BIN_OP_PRECEDENCE_MAP.put(">", 6);
        BIN_OP_PRECEDENCE_MAP.put("<", 6);
        BIN_OP_PRECEDENCE_MAP.put(">=", 6);
        BIN_OP_PRECEDENCE_MAP.put("<=", 6);
        BIN_OP_PRECEDENCE_MAP.put("#EQ", 6);
        BIN_OP_PRECEDENCE_MAP.put("#NEQ", 6);
        BIN_OP_PRECEDENCE_MAP.put("#GT", 6);
        BIN_OP_PRECEDENCE_MAP.put("#LT", 6);
        BIN_OP_PRECEDENCE_MAP.put("#GTE", 6);
        BIN_OP_PRECEDENCE_MAP.put("#LTE", 6);
        BIN_OP_PRECEDENCE_MAP.put("&", 5);
        BIN_OP_PRECEDENCE_MAP.put("|", 5);
        BIN_OP_PRECEDENCE_MAP.put("&&", 5);
        BIN_OP_PRECEDENCE_MAP.put("||", 5);
        BIN_OP_PRECEDENCE_MAP.put("#AND", 5);
        BIN_OP_PRECEDENCE_MAP.put("#OR", 5);
        BIN_OP_PRECEDENCE_MAP.put("#IN", 5);
        BIN_OP_PRECEDENCE_MAP.put("#START_WITH", 5);
        BIN_OP_PRECEDENCE_MAP.put("#END_WITH", 5);
        BIN_OP_PRECEDENCE_MAP.put("#CONTAINS", 5);
        BIN_OP_PRECEDENCE_MAP.put("#NOT_CONTAINS", 5);
        BIN_OP_PRECEDENCE_MAP.put("#IS_NULL", 5);
        BIN_OP_PRECEDENCE_MAP.put("#IS_NOT_NULL", 5);
        BIN_OP_PRECEDENCE_MAP.put("#BETWEEN_AND", 5);
        BIN_OP_PRECEDENCE_MAP.put("#NOT_IN", 5);
    }

    /**
     * 一元运算符列表
     */
    public static final List<String> UNITARY_OPERATORS = Lists.newArrayList("~", "!",
            "#IS_NULL", "#IS_NOT_NULL");

    /**
     * 二元运算符列表
     */
    public static final List<String> BINARY_OPERATORS = Lists.newArrayList(".", "*", "/", "+", "-", "&", "|",
            "&&", "||", "<", ">", "==", "!=", "<=", ">=",
            "#AND", "#OR", "#LT", "#GT", "#EQ", "#NEQ", "#LTE", "#GTE",
            "#START_WITH", "#END_WITH", "#CONTAINS", "#NOT_CONTAINS", "#IN", "#NOT_IN", "#BETWEEN_AND");
}
