package io.terminus.trantor2.service.engine.ai.platform.service.impl;

import com.alibaba.fastjson.JSONValidator;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.model.management.meta.consts.SystemFieldGeneratorV2;
import io.terminus.trantor2.module.service.OSSService;
import io.terminus.trantor2.module.util.OSSConstant;
import io.terminus.trantor2.service.common.consts.AIConst;
import io.terminus.trantor2.service.common.utils.HttpClient;
import io.terminus.trantor2.service.dsl.enums.FieldType;
import io.terminus.trantor2.service.dsl.enums.Operator;
import io.terminus.trantor2.service.dsl.enums.SortType;
import io.terminus.trantor2.service.dsl.enums.ValueType;
import io.terminus.trantor2.service.dsl.properties.*;
import io.terminus.trantor2.service.engine.ai.platform.AIRepositorySupport;
import io.terminus.trantor2.service.engine.ai.platform.impl.TAIRestHandler;
import io.terminus.trantor2.service.engine.ai.platform.business.CommonBusinessHelper;
import io.terminus.trantor2.service.engine.ai.platform.model.request.KnowledgeDocRequest;
import io.terminus.trantor2.service.engine.ai.platform.model.request.KnowledgePagingRequest;
import io.terminus.trantor2.service.engine.ai.platform.service.AIKnowledgeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AIKnowledgeServiceImpl implements AIKnowledgeService {
    private final AIRepositorySupport aiRepositorySupport;
    private final OSSService ossService;
    private final TAIRestHandler taiRestHandler;

    private static final String ALIYUQUE_ENDPOINT = "https://aliyuque.antfin.com";

    @Override
    public List<KnowledgeDataBase> paging(KnowledgePagingRequest request) {
        List<SelectField> selectFields = CommonBusinessHelper.buildSelectFields(KnowledgeDataBase.class);
        Pageable pageable = buildPagingRequest(request);
        Paging<Map<String, Object>> paging = aiRepositorySupport.executePaging(request.getTeamId(),
                                                                               AIConst.AI_MANAGEMENT_KNOWLEDGE_DATABASE_MODEL_KEY,
                                                                               selectFields,
                                                                               pageable);
        if (Objects.nonNull(paging) && paging.getTotal() > 0) {
            return JsonUtil.fromJson(JsonUtil.toJson(paging.getData()), new TypeReference<List<KnowledgeDataBase>>() {
            });
        } else {
            return new ArrayList<>();
        }
    }

    public Pageable buildPagingRequest(KnowledgePagingRequest request) {
        Map<Field, Object> params = buildRequestFields(request.getKeyword());
        List<Condition> conditions = buildQueryConditions(params);
        return buildQueryPagingParams(request.getPageNumber(), request.getPageSize(), conditions);
    }

    public Map<Field, Object> buildRequestFields(String keyword) {
        Field name = new BaseField("name", "name", FieldType.Text);
        Map<Field, Object> params = new HashMap<>();
        params.put(name, keyword);
        return params;
    }

    public List<Condition> buildQueryConditions(Map<Field, Object> params) {
        List<Condition> conditions = new ArrayList<>();
        params.forEach((field, value) -> {
            ConditionLeaf condition = new ConditionLeaf();

            // 左值
            VarValue leftValue = new VarValue();
            leftValue.setValueType(ValueType.MODEL);
            leftValue.setFieldType(FieldType.Text);
            leftValue.setVarValue(Lists.newArrayList(new VarValue.VarStage(field.getFieldKey())));
            condition.setLeftValue(leftValue);

            // 操作符
            condition.setOperator(Operator.CONTAINS);

            // 右值
            ConstValue constValue = new ConstValue();

            switch (field.getFieldType()) {
                case Text:
                    if (StringUtils.isNotBlank(Objects.toString(value, ""))) {
                        constValue.setFieldType(FieldType.Text);
                        constValue.setConstValue(value);
                        condition.setRightValue(constValue);
                        conditions.add(condition);
                    }
                    break;
                case Number:
                    if (Objects.nonNull(value)) {
                        constValue.setFieldType(FieldType.Number);
                        constValue.setConstValue(value);
                        condition.setRightValue(constValue);
                        conditions.add(condition);
                    }
                    break;
                default:
                    break;
            }
        });
        return conditions;
    }

    public Pageable buildQueryPagingParams(Integer pageNum, Integer pageSize, List<Condition> conditions) {
        ConditionGroup conditionGroup = new ConditionGroup();
        conditionGroup.setConditions(Lists.newArrayList(conditions));

        List<SortOrder> sortOrders = Lists.newArrayList(new SortOrder(SystemFieldGeneratorV2.CREATED_AT.getFieldAlias(), SortType.DESC));

        Pageable pageable = new Pageable(Objects.isNull(pageNum) ? 1 : pageNum, Objects.isNull(pageSize) ? 15 : pageSize);
        pageable.setConditionGroup(conditionGroup);
        pageable.setSortOrders(sortOrders);
        return pageable;
    }

    @Override
    public void saveDoc(KnowledgeDocRequest knowledgeDoc) {
        aiRepositorySupport.executeSaveDataService(knowledgeDoc.getTeamId(), AIConst.AI_MANAGEMENT_KNOWLEDGE_DOCS_MODEL_KEY, knowledgeDoc);
    }

    @Override
    public List<?> searchKnowledge(String question, List<String> knowledgeCodes, Integer searchCount, Float similarity, String retrieverType) {
        List<?> result = taiRestHandler.similaritySearch(question, knowledgeCodes, searchCount, similarity, retrieverType);
        log.info("current Knowledge search result: {}", JsonUtil.toIndentJson(result));
        return result;
    }

    @Override
    public String uploadDocByAliYuqueSDK(String token, String url) {
        if (url.startsWith(ALIYUQUE_ENDPOINT)) {
            String[] pathVariables = url.substring((ALIYUQUE_ENDPOINT + "/").length()).split("/");
            if (pathVariables.length != 3) {
                throw new ValidationException("the url format may not match aliyuque.");
            }
            String groupLogin = pathVariables[0];
            String bookSlug   = pathVariables[1];
            String id         = pathVariables[2];
            String targetEndpoint = String.format("%s/api/v2/repos/%s/%s/docs/%s", ALIYUQUE_ENDPOINT, groupLogin, bookSlug, id);
            Map<String, String> headers = MapUtil.ofStr("X-Auth-Token", token);
            String httpResponse = HttpClient.INSTANCE.get(targetEndpoint, headers, new HashMap<>());
            if (JSONValidator.from(httpResponse).validate()) {
                YuqueApiDocResponse response = JsonUtil.fromJson(httpResponse, new TypeReference<YuqueApiDocResponse>() {});
                log.info("aliyuque doc body: \n{}", JsonUtil.toJson(response.getData()));
                if (response.getData().getTitle().endsWith(".md")) {
                    return uploadDocByContent(String.format("%s", response.getData().getTitle()), response.getData().getBody());
                } else {
                    return uploadDocByContent(String.format("%s.md", response.getData().getTitle()), response.getData().getBody());
                }
            } else {
                throw new ValidationException("response is not json, gateway errors maybe occurred");
            }
        } else {
            throw new ValidationException("current only support aliyuque doc url");
        }
    }

    @Override
    public String uploadDocByContent(String fileName, String content) {
        String prefix = OSSConstant.CONSOLE_FILE_PREFIX;
        if (StringUtils.isNotBlank(TrantorContext.getTeamCode())) {
            prefix = prefix + TrantorContext.getTeamCode() + "/";
        }
        return ossService.uploadFileAndGetUrl(prefix + "knowledge/aliyuque/",
                                              fileName,
                                              new ByteArrayInputStream(content.getBytes()),
                                              "text/markdown",
                                              null, null);
    }
}
