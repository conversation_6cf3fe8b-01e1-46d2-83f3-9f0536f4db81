package io.terminus.trantor2.service.engine;

import io.terminus.trantor2.service.common.utils.GraalvmScriptUtil;
import io.terminus.trantor2.service.common.utils.NashornScriptUtil;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * ServiceEngineInitialization
 *
 * <AUTHOR> Created on 2025/2/21 17:24
 */
@Component
public class ServiceEngineInitialization {

    @Async
    @EventListener(ApplicationStartedEvent.class)
    public void init() throws Exception {
        // JS引擎预热
        try {
            GraalvmScriptUtil.warmUp();
            NashornScriptUtil.warmUp();
        } finally {
            GraalvmScriptUtil.removeThreadLocalEngine();
            NashornScriptUtil.removeThreadLocalEngine();
        }
    }
}
