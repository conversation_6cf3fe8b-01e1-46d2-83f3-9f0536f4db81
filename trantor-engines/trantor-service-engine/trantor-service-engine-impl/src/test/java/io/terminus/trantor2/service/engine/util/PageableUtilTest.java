package io.terminus.trantor2.service.engine.util;

import com.fasterxml.jackson.core.type.TypeReference;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.dsl.util.PageableUtil;
import org.junit.jupiter.api.Test;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/9/13 3:22 PM
 */
public class PageableUtilTest {

    @Test
    public void test() {
        String condition = "{\n" +
            "        \"conditionGroup\":{\n" +
            "            \"type\":\"ConditionGroup\",\n" +
            "            \"logicOperator\":\"AND\",\n" +
            "            \"conditions\":[\n" +
            "                {\n" +
            "                    \"type\":\"ConditionGroup\",\n" +
            "                    \"logicOperator\":\"AND\",\n" +
            "                    \"conditions\":[\n" +
            "                        {\n" +
            "                            \"key\":\"win2S_HVz51ZR0M43CAT_\",\n" +
            "                            \"type\":\"ConditionLeaf\",\n" +
            "                            \"leftValue\":{\n" +
            "                                \"key\":\"0KRliMFWzGzKaC043yibR\",\n" +
            "                                \"type\":\"VarValue\",\n" +
            "                                \"fieldType\":\"Text\",\n" +
            "                                \"valueType\":\"VAR\",\n" +
            "                                \"varValue\":[\n" +
            "                                    {\n" +
            "                                        \"valueKey\":\"custCode\",\n" +
            "                                        \"valueName\":\"custCode\"\n" +
            "                                    }\n" +
            "                                ]\n" +
            "                            },\n" +
            "                            \"operator\":\"CONTAINS\",\n" +
            "                            \"rightValue\":{\n" +
            "                                \"key\":\"7U63EX7XomJQ_a4rm6B12\",\n" +
            "                                \"type\":\"VarValue\",\n" +
            "                                \"fieldType\":\"Text\",\n" +
            "                                \"valueType\":\"CONST\",\n" +
            "                                \"constValue\":\"0989\"\n" +
            "                            }\n" +
            "                        }\n" +
            "                    ]\n" +
            "                }\n" +
            "            ]\n" +
            "        },\n" +
            "        \"pageNo\":1,\n" +
            "        \"pageSize\":20\n" +
            "    }";
        String custCode = PageableUtil.getValue("custCode", JsonUtil.fromJson(condition, new TypeReference<Map<String, Object>>() {}));
        System.out.println(custCode);
    }

}
