package io.terminus.trantor2.service.engine.impl.function.impl;

import com.google.common.collect.Lists;
import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.function.Func;
import io.terminus.trantor2.service.engine.impl.value.expression.FuncExpression;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ToDateFuncTest
 *
 * <AUTHOR> Created on 2024/10/18 10:47
 */
class ToDateFuncTest extends AbstractFuncTest {

    @Test
    void apply() {
        Date date = new Date();
        VariableContext context = mockContext();
        context.setVariable(Lists.newArrayList("a", "b", "c"), date.getTime());
        FuncExpression expression = new FuncExpression("TO_DATE(${a.b.c})");
        Object result = expression.getValue(context);
        Assertions.assertEquals(date, result);
    }

    @Override
    protected List<Func> getFunc() {
        return Lists.newArrayList(new ToDateFunc());
    }
}
