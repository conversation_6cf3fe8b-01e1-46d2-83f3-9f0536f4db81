package io.terminus.trantor2.service.engine.ai.memory;

import io.terminus.trantor2.service.dsl.Agent;
import io.terminus.trantor2.service.dsl.properties.AgentLongTermMemoryConfig;
import io.terminus.trantor2.service.dsl.properties.AgentProperties;
import io.terminus.trantor2.service.engine.ai.agent.client.AgentClient;
import io.terminus.trantor2.service.engine.ai.agent.client.response.MemoryExtractAndUpdateResponse;
import io.terminus.trantor2.service.engine.loader.metadata.AgentMetadataQuery;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

class AIMemoryManagerTest {
    private AIGlobalMemoryService globalMemoryService;
    private AIAgentMemoryService agentMemoryService;
    private AgentClient agentClient;
    private AIMemoryManager aiMemoryManager;
    private AgentMetadataQuery agentMetadataQuery;

    @BeforeEach
    void setUp() {
        globalMemoryService = mock(AIGlobalMemoryService.class);
        agentMemoryService = mock(AIAgentMemoryService.class);
        agentMetadataQuery = mock(AgentMetadataQuery.class);
        agentClient = mock(AgentClient.class);
        aiMemoryManager = new AIMemoryManager(globalMemoryService, agentMemoryService, agentClient, agentMetadataQuery);
    }

    @Test
    void testGenerateMemoryPrompt() {
        Agent agent = new Agent();
        agent.setKey("agent1");
        AgentProperties agentProperties = new AgentProperties();
        agentProperties.getLongTermMemoryConfig().setEnabled(true);
        agent.setProps(agentProperties);

        // mock 全局记忆
        Map<String, Object> globalMemory = new HashMap<>();
        globalMemory.put("name", "张三");
        globalMemory.put("occupation", "程序员");
        when(globalMemoryService.getGlobalMemory()).thenReturn(globalMemory);

        // mock agent 变量
        AgentLongTermMemoryConfig.MemoryVariable var1 = new AgentLongTermMemoryConfig.MemoryVariable();
        var1.setName("兴趣");
        var1.setDescription("用户的兴趣爱好");
        var1.setValue("编程");
        AgentLongTermMemoryConfig.MemoryVariable var2 = new AgentLongTermMemoryConfig.MemoryVariable();
        var2.setName("最近任务");
        var2.setDescription("最近一次分配的任务");
        var2.setValue("写单元测试");
        when(agentMemoryService.getMemoryVariables(agent)).thenReturn(Arrays.asList(var1, var2));

        // mock agent 片段
        Map<String, Object> frag1 = new HashMap<>();
        frag1.put("content", "2024-06-01 完成了需求分析");
        Map<String, Object> frag2 = new HashMap<>();
        frag2.put("content", "2024-06-02 参加了项目会议");
        when(agentMemoryService.getMemoryFragments("agent1")).thenReturn(Arrays.asList(frag1, frag2));

        // mock config
        String prompt = aiMemoryManager.generateMemoryPrompt(agent);
        System.out.println(prompt);
        assertTrue(prompt.contains("请称呼我为【张三】"));
        assertTrue(prompt.contains("兴趣（用户的兴趣爱好）: 编程"));
        assertTrue(prompt.contains("2024-06-01 完成了需求分析"));
        assertTrue(prompt.contains("2024-06-02 参加了项目会议"));
    }

    @Test
    void testUpdateMemory() throws Exception {
        String agentKey = "agent1";
        String agentDesc = "desc";
        String userContent = "content";

        // 构造 response
        MemoryExtractAndUpdateResponse.UpdatedMemoryFragment del = new MemoryExtractAndUpdateResponse.UpdatedMemoryFragment();
        del.setAction("delete");
        del.setId("id1");
        del.setText(null);
        MemoryExtractAndUpdateResponse.UpdatedMemoryFragment upd = new MemoryExtractAndUpdateResponse.UpdatedMemoryFragment();
        upd.setAction("update");
        upd.setId("id2");
        upd.setText("newText");
        MemoryExtractAndUpdateResponse.UpdatedMemoryFragment add = new MemoryExtractAndUpdateResponse.UpdatedMemoryFragment();
        add.setAction("add");
        add.setId(null);
        add.setText("addText");

        MemoryExtractAndUpdateResponse.UpdatedMemoryVariable var = new MemoryExtractAndUpdateResponse.UpdatedMemoryVariable();
        var.setName("var1");
        var.setNewValue("val1");

        MemoryExtractAndUpdateResponse response = new MemoryExtractAndUpdateResponse();
        response.setUpdatedFragments(Arrays.asList(del, upd, add));
        response.setUpdatedVariables(Collections.singletonList(var));

        when(agentClient.extractAndUpdateMemory(any())).thenReturn(response);
        AgentLongTermMemoryConfig agentLongTermMemoryConfig = new AgentLongTermMemoryConfig();
        AgentLongTermMemoryConfig.MemoryVariable memoryVariable = new AgentLongTermMemoryConfig.MemoryVariable();
        memoryVariable.setName("var1");
        memoryVariable.setDescription("desc");
        memoryVariable.setModelInferenceEnabled(true);
        agentLongTermMemoryConfig.getMemoryVariables().add(memoryVariable);

        aiMemoryManager.updateMemory("Qwen-Turbo", userContent, agentKey, agentLongTermMemoryConfig, agentDesc);
        // 等待异步任务执行
        TimeUnit.SECONDS.sleep(1);

        verify(agentMemoryService).deleteMemoryFragment(agentKey, "id1");
        verify(agentMemoryService).updateMemoryFragment(agentKey, "id2", "newText");
        verify(agentMemoryService).createMemoryFragment(agentKey, "addText");
        verify(agentMemoryService).updateMemoryVariable(agentKey, "var1", "val1");
    }

    @Test
    void testAIMemoryTriggerDetector() {
        // 命中关键词
        assertTrue(AIMemoryTriggerDetector.isTrigger("请记住我的偏好"));
        assertTrue(AIMemoryTriggerDetector.isTrigger("以后所有的年假单都自动通过"));
        assertTrue(AIMemoryTriggerDetector.isTrigger("设置默认值"));
        assertTrue(AIMemoryTriggerDetector.isTrigger("记住我的名字叫张三"));
        assertTrue(AIMemoryTriggerDetector.isTrigger("以后总金额低于1w的审批流自动通过"));
        assertTrue(AIMemoryTriggerDetector.isTrigger("我叫李四，是一名工程师"));
        assertTrue(AIMemoryTriggerDetector.isTrigger("我的邮箱是****************"));
        assertTrue(AIMemoryTriggerDetector.isTrigger("我喜欢蓝色的东西"));
        assertTrue(AIMemoryTriggerDetector.isTrigger("以后请默认使用中文回答"));
        assertTrue(AIMemoryTriggerDetector.isTrigger("以后请使用英文回答"));
        assertTrue(AIMemoryTriggerDetector.isTrigger("你能记住我的生日是10月1日吗？"));
        assertTrue(AIMemoryTriggerDetector.isTrigger("别忘记我们下午3点有会议"));
        assertTrue(AIMemoryTriggerDetector.isTrigger("我有过敏性鼻炎，怎么办？"));
        assertTrue(AIMemoryTriggerDetector.isTrigger("我不喜欢甜的，可以记住吗?"));

        // 未命中关键词
        assertFalse(AIMemoryTriggerDetector.isTrigger("当天气下雨时，提醒我带伞"));
        assertFalse(AIMemoryTriggerDetector.isTrigger("你好，帮我查下天气"));
        assertFalse(AIMemoryTriggerDetector.isTrigger("今天吃什么"));
        assertFalse(AIMemoryTriggerDetector.isTrigger("你叫什么名字"));
        assertFalse(AIMemoryTriggerDetector.isTrigger("你的想法是什么"));
        assertFalse(AIMemoryTriggerDetector.isTrigger("推荐一部电影"));
        assertFalse(AIMemoryTriggerDetector.isTrigger("我以后再来找你"));
    }

    @Test
    void testAIMemoryTriggerDetector_en() {
        // 命中英文关键词
        assertTrue(AIMemoryTriggerDetector.detect("please remember my preference", java.util.Locale.ENGLISH).isTriggered());
        assertTrue(AIMemoryTriggerDetector.detect("from now on always auto approve", java.util.Locale.ENGLISH).isTriggered());
        assertTrue(AIMemoryTriggerDetector.detect("set default value", java.util.Locale.ENGLISH).isTriggered());
        assertTrue(AIMemoryTriggerDetector.detect("my name is John", java.util.Locale.ENGLISH).isTriggered());
        assertTrue(AIMemoryTriggerDetector.detect("I have an allergy", java.util.Locale.ENGLISH).isTriggered());
        assertTrue(AIMemoryTriggerDetector.detect("don't forget our meeting at 3pm", java.util.Locale.ENGLISH).isTriggered());
        assertTrue(AIMemoryTriggerDetector.detect("I dislike sweet food", java.util.Locale.ENGLISH).isTriggered());

        // 未命中英文关键词
        assertFalse(AIMemoryTriggerDetector.detect("what's the weather today", java.util.Locale.ENGLISH).isTriggered());
        assertFalse(AIMemoryTriggerDetector.detect("recommend a movie", java.util.Locale.ENGLISH).isTriggered());
        assertFalse(AIMemoryTriggerDetector.detect("your idea is interesting", java.util.Locale.ENGLISH).isTriggered());
    }
} 