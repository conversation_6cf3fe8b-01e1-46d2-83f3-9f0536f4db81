package io.terminus.trantor2.service.engine.impl.astservice;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.dsl.enums.SortType;
import io.terminus.trantor2.service.dsl.properties.Pageable;
import io.terminus.trantor2.service.dsl.properties.SelectField;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.delegate.Metadata;
import io.terminus.trantor2.service.engine.executor.AbstractExecutorTest;
import io.terminus.trantor2.service.engine.impl.component.bean.QueryModel;
import io.terminus.trantor2.service.engine.impl.component.bean.condition.GroupQueryCondition;
import io.terminus.trantor2.service.engine.impl.helper.PermissionHelper;
import io.terminus.trantor2.service.engine.util.DataLoader;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * RelatedModelOrderByFieldSettingTest
 *
 * <AUTHOR> Created on 2024/9/14 11:16
 */
public class RelatedModelConditionSettingTest extends AbstractExecutorTest {

    @Test
    public void execute() {

        Map<String, Object> args = new HashMap<>();
        args.put("pageable", new Pageable(1, 10));

        Key serviceKey = Key.of(1L, "ERP_SCM$test_related_model_condition_setting_service");

        when(serviceMetadataQuery.findMeta(serviceKey))
                .thenReturn(Metadata.of(serviceKey, DataLoader.loadObj("/service/dsl/test_related_model_condition_setting.json", ServiceDefinition.class)));

        mockedPermissionHelper.when(() -> PermissionHelper.fillQueryModelDataPermission(Mockito.any(), Mockito.any()))
                .thenReturn(true);

        Arguments arguments = Arguments.of(1L, args);
        serviceExecutor.execute(serviceKey.getKey(), arguments);

        // 验证查询模型数据时，是否设置了关联模型的排序规则（服务dsl设置了关联模型的排序规则）
        ArgumentCaptor<QueryModel> parameterCaptor = ArgumentCaptor.forClass(QueryModel.class);
        verify(modelDataRepository).findOne(parameterCaptor.capture());
        List<SelectField> selectFields = parameterCaptor.getValue().getSelectFields();

        SelectField selectField = findSelectField("matCode", selectFields);

        Object conditionGroup = selectField.getConditionGroup();

        Assertions.assertInstanceOf(GroupQueryCondition.class, conditionGroup);

        // 打印关联模型的过滤条件
        System.out.println(JsonUtil.toNonIndentJson(conditionGroup));
    }

    private SelectField findSelectField(String fieldKey, List<SelectField> selectFields) {
        if (CollectionUtils.isNotEmpty(selectFields)) {
            return selectFields.stream().filter(m -> fieldKey.equals(m.getField())).findFirst().orElse(null);
        }
        return null;
    }
}
