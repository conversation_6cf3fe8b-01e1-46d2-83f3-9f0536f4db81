package io.terminus.trantor2.service.engine.impl.astservice;

import io.terminus.trantor2.service.dsl.ActionNode;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.dsl.properties.ErrorConfig;
import io.terminus.trantor2.service.engine.util.DataLoader;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * ServiceDefinitionTest
 *
 * <AUTHOR> Created on 2024/5/17 15:48
 */
public class ServiceDefinitionTest {

    @Test
    public void convertToServiceDefinition() {
        ServiceDefinition serviceDefinition = DataLoader.loadObj("/json/def.json", ServiceDefinition.class);
        System.out.println(serviceDefinition);

        Assertions.assertInstanceOf(ActionNode.class, serviceDefinition.getChildren().get(1));
    }

    @Test
    public void convertToServiceDefinitionForErrorConfig() {
        ServiceDefinition serviceDefinition = DataLoader.loadObj("/service/dsl/test_error_config_service.json", ServiceDefinition.class);
        System.out.println(serviceDefinition);

        Assertions.assertInstanceOf(ErrorConfig.class, serviceDefinition.getProps().getErrorConfigs().get(0));
    }
}
