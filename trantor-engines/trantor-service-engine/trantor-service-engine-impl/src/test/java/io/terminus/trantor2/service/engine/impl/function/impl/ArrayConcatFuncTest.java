package io.terminus.trantor2.service.engine.impl.function.impl;

import com.google.common.collect.Lists;
import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.function.Func;
import io.terminus.trantor2.service.engine.impl.value.expression.FuncExpression;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

/**
 * ArrayConcatFuncTest
 *
 * <AUTHOR> Created on 2024/7/26 18:02
 */
public class ArrayConcatFuncTest extends AbstractFuncTest {

    @Test
    void apply() {
        VariableContext context = mockContext();
        context.setVariable(Lists.newArrayList("GLOBAL", "lst"), Lists.newArrayList("a", "b", "c"));
        context.setVariable(Lists.newArrayList("GLOBAL", "lst2"), Lists.newArrayList("a", "c"));
        context.setVariable(Lists.newArrayList("GLOBAL", "lst3"), Lists.newArrayList("a", "c", "d"));
        FuncExpression expression = new FuncExpression("ARRAY_CONCAT(${GLOBAL.lst},${GLOBAL.lst2},${GLOBAL.lst3})");
        Object result = expression.getValue(context);
        Assertions.assertEquals(Lists.newArrayList("a", "b", "c", "d"), result);
    }

    @Override
    protected List<Func> getFunc() {
        return Lists.newArrayList(new ArrayConcatFunc());
    }
}
