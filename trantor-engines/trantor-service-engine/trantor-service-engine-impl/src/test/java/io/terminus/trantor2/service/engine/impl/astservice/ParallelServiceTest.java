package io.terminus.trantor2.service.engine.impl.astservice;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.engine.ServiceEngine;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.delegate.Metadata;
import io.terminus.trantor2.service.engine.executor.AbstractExecutorTest;
import io.terminus.trantor2.service.engine.util.DataLoader;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;

import static org.mockito.Mockito.when;

/**
 * ParallelServiceTest
 *
 * <AUTHOR> Created on 2024/6/24 16:55
 */
public class ParallelServiceTest extends AbstractExecutorTest {

    @Override
    protected void initServiceEngine(ServiceEngine serviceEngine) {
        super.initServiceEngine(serviceEngine);
        serviceEngine.setAsyncExecutor(Executors.newCachedThreadPool());
    }

    @Test
    public void testParallel() {
        Key serviceKey = Key.of(1L, "TERP_MIGRATE$test_parallel_service");
        serviceFactory.load(serviceKey);
        when(serviceMetadataQuery.findMeta(serviceKey))
                .thenReturn(Metadata.of(serviceKey, DataLoader.loadObj("/service/dsl/test_parallel_service.json", ServiceDefinition.class)));

        for (int i = 0; i < 20; i++) {
            doParallel();
        }
    }

    public void doParallel() {
        Map<String, Object> args = new HashMap<>();
        args.put("expert_pool", MapUtil.of("name", "abc"));
        Key serviceKey = Key.of(1L, "TERP_MIGRATE$test_parallel_service");

        when(modelDataRepository.findOne(Mockito.any())).thenReturn(MapUtil.of("id", 100L));
        when(modelDataRepository.create(Mockito.any(), Mockito.any())).thenReturn(MapUtil.of("id", 900L));

        Object result = serviceExecutor.execute(serviceKey.getKey(), Arguments.of(1L, args));
        System.out.println(JsonUtil.toJson(result));

        Assertions.assertInstanceOf(Map.class, result);
        System.out.println("id1 =" + ((Map) result).get("id1"));
        System.out.println("id2 =" + ((Map) result).get("id2"));
        Assertions.assertEquals(((Map) result).get("id1").toString(), "100");
        Assertions.assertEquals(((Map) result).get("id2").toString(), "900");
    }
}
