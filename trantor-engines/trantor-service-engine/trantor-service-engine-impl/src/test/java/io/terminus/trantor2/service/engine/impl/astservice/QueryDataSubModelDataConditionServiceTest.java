package io.terminus.trantor2.service.engine.impl.astservice;

import com.fasterxml.jackson.core.type.TypeReference;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.dsl.properties.Pageable;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.delegate.Metadata;
import io.terminus.trantor2.service.engine.executor.AbstractExecutorTest;
import io.terminus.trantor2.service.engine.util.DataLoader;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.when;

/**
 * QueryDataSubModelDataConditionServiceTest
 *
 * <AUTHOR> Created on 2024/6/28 09:56
 */
public class QueryDataSubModelDataConditionServiceTest extends AbstractExecutorTest {

    @Test
    public void execute() {

        Map<String, Object> args = new HashMap<>();
        args.put("pageable", new Pageable(1, 10));

        Key serviceKey = Key.of(1L, "ERP_SCM$test_query_data_sub_model_data_condition_service");

        when(serviceMetadataQuery.findMeta(serviceKey))
            .thenReturn(Metadata.of(serviceKey, DataLoader.loadObj("/service/dsl/test_query_data_sub_model_data_condition_service.json", ServiceDefinition.class)));

        when(modelDataRepository.page(Mockito.any(), Mockito.any(), Mockito.any()))
            .thenReturn(DataLoader.loadByTypeReference("/service/dsl/test_query_data_sub_model_data_condition_service_page_result.json", new TypeReference<Paging<Map<String, Object>>>() {
            }));

        when(modelMetaQuery.getModelFields(Mockito.any(), Mockito.any(), Mockito.anyBoolean()))
            .thenReturn(new ArrayList<>());

        Arguments arguments = Arguments.of(1L, args);
        Object result = serviceExecutor.execute(serviceKey.getKey(), arguments);
        Assertions.assertNotNull(result);
        System.out.println(JsonUtil.toNonIndentJson(((Map) result).get("data")));
    }

}
