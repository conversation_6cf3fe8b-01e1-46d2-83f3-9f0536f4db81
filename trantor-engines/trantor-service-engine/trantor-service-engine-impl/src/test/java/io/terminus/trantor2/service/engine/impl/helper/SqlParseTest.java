package io.terminus.trantor2.service.engine.impl.helper;

import io.terminus.trantor2.service.common.utils.Placeholder;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.util.TablesNamesFinder;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.HashSet;
import java.util.Set;

/**
 * SqlParseTest
 *
 * <AUTHOR> Created on 2024/6/5 13:43
 */
public class SqlParseTest {

    @Test
    public void test() throws JSQLParserException {
        String sql = "WITH RECURSIVE cte AS (\n" +
            "    -- 基础查询，选择根节点（即给定的 id）\n" +
            "    SELECT id, parent_id, other_columns\n" +
            "    FROM user\n" +
            "    WHERE id = ?\n" +
            "\n" +
            "    UNION ALL\n" +
            "\n" +
            "    -- 递归部分，选择所有子节点\n" +
            "    SELECT t.id, t.parent_id, t.other_columns\n" +
            "    FROM user t\n" +
            "    INNER JOIN cte c ON t.parent_id = c.id\n" +
            ")\n" +
            "SELECT *\n" +
            "FROM cte;";

        Statement statement = CCJSqlParserUtil.parse(sql);

        Assertions.assertInstanceOf(Select.class, statement);
        TablesNamesFinder tablesNamesFinder = new TablesNamesFinder();
        Set<String> tables =  new HashSet<>(tablesNamesFinder.getTableList(statement));
        Assertions.assertTrue(tables.contains("user"));
        Assertions.assertFalse(tables.contains("cte"));
    }

    @Test
    public void testOfVariables() throws JSQLParserException {
        String sql = "select id, ${field} from user";
        sql = Placeholder.PLACE_DOLLAR_BRACES_HOLDER.replaceHolder(sql, key -> "1=1");
        Statement statement = CCJSqlParserUtil.parse(sql);
        Assertions.assertInstanceOf(Select.class, statement);
    }

    @Test
    public void testOfVariables2() throws JSQLParserException {
        String sql = "select a.`id`, ? from `trantor.user` a where a.id > #{id} and name =#{name} and ?";
        sql = Placeholder.PLACE_DOLLAR_BRACES_HOLDER.replaceHolder(sql, key -> "1=1");
        sql = Placeholder.PLACE_WELL_BRACES_HOLDER.replaceHolder(sql, key -> "?");
        Statement statement = CCJSqlParserUtil.parse(sql);

        Assertions.assertInstanceOf(Select.class, statement);

        TablesNamesFinder tablesNamesFinder = new TablesNamesFinder();
        Set<String> tables = new HashSet<>(tablesNamesFinder.getTableList(statement));
        Assertions.assertTrue(tables.contains("`trantor.user`"));
    }

    @Test
    public void testFunc() throws JSQLParserException {
        String sql = "SELECT concat(YEAR(#{createdAt}), '-', WEEK(#{createdAt}, 1)) as 'year-week' FROM dual where 1=1";

        sql = Placeholder.PLACE_WELL_BRACES_HOLDER.replaceHolder(sql, key -> "?");

        Statement statement = CCJSqlParserUtil.parse(sql);
        Assertions.assertInstanceOf(Select.class, statement);

        TablesNamesFinder tablesNamesFinder = new TablesNamesFinder();
        Set<String> tables = new HashSet<>(tablesNamesFinder.getTableList(statement));
        Assertions.assertTrue(tables.contains("dual"));
    }
}
