package io.terminus.trantor2.service.engine.impl.function.impl;

import com.google.common.collect.Lists;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.function.Func;
import io.terminus.trantor2.service.engine.impl.value.expression.FuncExpression;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * ArrayMapFuncTest
 *
 * <AUTHOR> Created on 2023/11/9 16:11
 */
class ArrayMapFuncTest extends AbstractFuncTest {

    @Test
    void apply() {
        VariableContext context = mockContext();

        List<Object> array = new ArrayList<>();
        array.add(MapUtil.of("id", 1L, "name", "abc"));
        array.add(MapUtil.of("id", 2L));
        array.add(MapUtil.of("id", null));
        context.setVariable("array", array);

        FuncExpression expression = new FuncExpression("ARRAY_MAP(${array},'item -> item.id', 'item -> item.id != null && item.name != null')");

        Object result = expression.getValue(context);

        System.out.println(result);

        Assertions.assertTrue(result instanceof List);
        Assertions.assertEquals(1, ((List) result).size());
    }

    @Override
    protected List<Func> getFunc() {
        return Lists.newArrayList(new ArrayMapFunc());
    }
}
