package io.terminus.trantor2.service.engine.impl.astservice;

import com.fasterxml.jackson.core.type.TypeReference;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.dsl.properties.QueryField;
import io.terminus.trantor2.service.dsl.properties.QueryModelFields;
import io.terminus.trantor2.service.dsl.properties.SelectField;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.executor.AbstractExecutorTest;
import io.terminus.trantor2.service.engine.impl.astservice.execution.DataProcessingExecution;
import io.terminus.trantor2.service.engine.util.DataLoader;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.when;

/**
 * DataProcessingExecutionTest
 *
 * <AUTHOR> Created on 2023/2/28 20:35
 */
public class DataProcessingExecutionTest extends AbstractExecutorTest {

    @Test
    public void queryData() {

        String queryJSON = "{\"type\":\"ServiceDefinition\",\"key\":\"FindOrderService\",\"name\":\"FindOrderService\",\"props\":{\"type\":\"ServiceProperties\",\"name\":\"FindOrderService\",\"desc\":\"FindOrderService\",\"status\":\"ENABLE\",\"transactionPropagation\":\"NOT_SUPPORTED\",\"appId\":1,\"teamId\":1},\"headNodeKeys\":[\"start0\"],\"children\":[{\"type\":\"StartNode\",\"key\":\"start0\",\"name\":\"开始节点\",\"props\":{\"type\":\"StartProperties\",\"name\":\"开始节点\",\"desc\":\"开始节点\",\"input\":[{\"fieldType\":\"OnlineModel\",\"fieldKey\":\"request\",\"fieldName\":\"request\",\"fieldType\":\"OnlineModel\",\"required\":true,\"relatedModel\":{\"modelKey\":\"Order\",\"modelName\":\"订单信息\",\"teamId\":1,\"appId\":1}}]},\"nextNodeKey\":\"findData0\"},{\"type\":\"RetrieveDataNode\",\"key\":\"findData0\",\"name\":\"查询订单信息数据\",\"props\":{\"type\":\"RetrieveDataProperties\",\"name\":\"查询订单信息数据\",\"desc\":\"查询订单信息数据\",\"relatedModel\":{\"modelKey\":\"Order\",\"modelName\":\"订单信息\",\"teamId\":1,\"appId\":1},\"dataType\":\"MODEL\",\"conditionGroup\":{\"type\":\"ConditionGroup\",\"conditions\":[{\"type\":\"ConditionGroup\",\"conditions\":[{\"type\":\"ConditionLeaf\",\"leftValue\":{\"type\":\"VarValue\",\"varValue\":[{\"valueKey\":\"id\",\"valueName\":\"id\"}],\"valueType\":\"MODEL\"},\"operator\":\"EQ\",\"rightValue\":{\"type\":\"VarValue\",\"varValue\":[{\"valueKey\":\"ServiceInnerModel_ServiceRequest\",\"valueName\":\"服务入参\"},{\"valueKey\":\"request\",\"valueName\":\"request\"},{\"valueKey\":\"id\",\"valueName\":\"id\"}],\"valueType\":\"VAR\",\"relatedNode\":{\"nodeKey\":\"global\",\"nodeTitle\":\"全局上下文\"}}}]}]},\"sortOrder\":{\"field\":{\"fieldType\":\"Text\",\"fieldKey\":\"id\",\"fieldName\":\"id\",\"fieldType\":\"Number\"},\"sortType\":\"DESC\",\"asc\":false},\"stopWhenDataEmpty\":false},\"nextNodeKey\":\"end0\"},{\"type\":\"EndNode\",\"key\":\"end0\",\"name\":\"结束节点\",\"props\":{\"type\":\"EndProperties\",\"outputMapping\":[{\"field\":{\"fieldType\":\"OnlineModel\",\"fieldKey\":\"data\",\"fieldName\":\"data\",\"fieldType\":\"OnlineModel\",\"relatedModel\":{\"modelKey\":\"Order\",\"modelName\":\"订单信息\",\"teamId\":1,\"appId\":1}},\"value\":{\"type\":\"VarValue\",\"varValue\":[{\"valueKey\":\"Order\",\"valueName\":\"订单信息\"}],\"valueType\":\"VAR\",\"relatedNode\":{\"nodeKey\":\"findData0\",\"nodeTitle\":\"查询订单信息数据\"}}}]}}]}";

        Map<String, Object> request = new HashMap<>();
        request.put("request", MapUtil.of("id", 1L));

        when(serviceMetadataQuery.find(Key.of(1L, "FindOrderService")))
                .thenReturn(JsonUtil.fromJson(queryJSON, ServiceDefinition.class));
        Object order = serviceExecutor.execute("FindOrderService", Arguments.of(1L, request));

        Assertions.assertTrue(order instanceof Map);
        Map test = (Map) ((Map) order).get("data");
        Assertions.assertEquals(test.get("name"), "订单1");
    }

    @Test
    public void queryBatchData() {
    }

    @Test
    public void createData() {
        String json = "{\"type\":\"ServiceDefinition\",\"key\":\"CreateOrderService\",\"name\":\"CreateOrderService\",\"props\":{\"type\":\"ServiceProperties\",\"name\":\"CreateOrderService\",\"desc\":\"CreateOrderService\",\"status\":\"ENABLE\",\"transactionPropagation\":\"REQUIRED\",\"appId\":1,\"teamId\":1},\"headNodeKeys\":[\"start0\"],\"children\":[{\"type\":\"StartNode\",\"key\":\"start0\",\"name\":\"开始节点\",\"props\":{\"type\":\"StartProperties\",\"name\":\"开始节点\",\"desc\":\"开始节点\",\"input\":[{\"fieldType\":\"OnlineModel\",\"fieldKey\":\"request\",\"fieldName\":\"request\",\"fieldType\":\"OnlineModel\",\"required\":true,\"relatedModel\":{\"modelKey\":\"Order\",\"modelName\":\"订单信息\",\"teamId\":1,\"appId\":1}}]},\"nextNodeKey\":\"createData0\"},{\"type\":\"CreateDataNode\",\"key\":\"createData0\",\"name\":\"新增订单信息数据\",\"props\":{\"type\":\"CreateDataProperties\",\"name\":\"新增订单信息数据\",\"desc\":\"新增订单信息数据\",\"relatedModel\":{\"modelKey\":\"Order\",\"modelName\":\"订单信息\",\"teamId\":1,\"appId\":1},\"inputMapping\":[{\"field\":{\"fieldType\":\"Text\",\"fieldKey\":\"address\",\"fieldName\":\"address\",\"fieldType\":\"Text\"},\"value\":{\"type\":\"VarValue\",\"varValue\":[{\"valueKey\":\"ServiceInnerModel_ServiceRequest\",\"valueName\":\"服务入参\"},{\"valueKey\":\"request\",\"valueName\":\"request\"},{\"valueKey\":\"address\",\"valueName\":\"address\"}],\"valueType\":\"VAR\",\"relatedNode\":{\"nodeKey\":\"global\",\"nodeTitle\":\"全局上下文\"}}},{\"field\":{\"fieldType\":\"Text\",\"fieldKey\":\"type\",\"fieldName\":\"类型\",\"fieldType\":\"Enum\"},\"value\":{\"type\":\"VarValue\",\"varValue\":[{\"valueKey\":\"ServiceInnerModel_ServiceRequest\",\"valueName\":\"服务入参\"},{\"valueKey\":\"request\",\"valueName\":\"request\"},{\"valueKey\":\"type\",\"valueName\":\"类型\"}],\"valueType\":\"VAR\",\"relatedNode\":{\"nodeKey\":\"global\",\"nodeTitle\":\"全局上下文\"}}}]},\"nextNodeKey\":\"end0\"},{\"type\":\"EndNode\",\"key\":\"end0\",\"name\":\"结束节点\",\"props\":{\"type\":\"EndProperties\",\"outputMapping\":[{\"field\":{\"fieldType\":\"OnlineModel\",\"fieldKey\":\"data\",\"fieldName\":\"data\",\"fieldType\":\"OnlineModel\",\"relatedModel\":{\"modelKey\":\"Order\",\"modelName\":\"订单信息\",\"teamId\":1,\"appId\":1}},\"value\":{\"type\":\"VarValue\",\"varValue\":[{\"valueKey\":\"Order\",\"valueName\":\"订单信息\"}],\"valueType\":\"VAR\",\"relatedNode\":{\"nodeKey\":\"createData0\",\"nodeTitle\":\"新增订单信息数据\"}}}]}}]}";
        Map<String, Object> request = new HashMap<>();
        request.put("request", MapUtil.of("name", "订单1", "enable", true));

        when(serviceMetadataQuery.find(Key.of(1L, "CreateOrderService")))
                .thenReturn(JsonUtil.fromJson(json, ServiceDefinition.class));
        Object order = serviceExecutor.execute("CreateOrderService", Arguments.of(1L, request));

        System.out.println(JsonUtil.toJson(order));
        Assertions.assertTrue(order instanceof Map);
        Map test = (Map) ((Map) order).get("data");
        Assertions.assertEquals((long) test.get("id"), 1);
    }

    @Test
    public void convertSelectFields() {

        DataProcessingExecution execution = new DataProcessingExecution(null);
        List<QueryField> queryFields = DataLoader.loadByTypeReference("/node/dsl/queryFields.json",
                new TypeReference<List<QueryField>>() {
                });
        QueryModelFields queryModelFields = new QueryModelFields();
        queryModelFields.setQueryFields(queryFields);
        List<SelectField> selectFields = execution.convertSelectFields(queryModelFields, null);

        System.out.println(queryFields.size());
        System.out.println(selectFields.size());
    }

    @Test
    public void updateData() {

        String updateJSON = "{\"type\":\"ServiceDefinition\",\"key\":\"UpdateOrderService\",\"name\":\"UpdateOrderService\",\"props\":{\"type\":\"ServiceProperties\",\"name\":\"UpdateOrderService\",\"desc\":\"UpdateOrderService\",\"status\":\"ENABLE\",\"transactionPropagation\":\"REQUIRED\",\"appId\":1,\"teamId\":1},\"headNodeKeys\":[\"start0\"],\"children\":[{\"type\":\"StartNode\",\"key\":\"start0\",\"name\":\"开始节点\",\"props\":{\"type\":\"StartProperties\",\"name\":\"开始节点\",\"desc\":\"开始节点\",\"input\":[{\"fieldType\":\"OnlineModel\",\"fieldKey\":\"request\",\"fieldName\":\"request\",\"fieldType\":\"OnlineModel\",\"required\":true,\"relatedModel\":{\"modelKey\":\"Order\",\"modelName\":\"订单信息\",\"teamId\":1,\"appId\":1}}]},\"nextNodeKey\":\"updateData0\"},{\"type\":\"UpdateDataNode\",\"key\":\"updateData0\",\"name\":\"更新订单信息数据\",\"props\":{\"type\":\"UpdateDataProperties\",\"name\":\"更新订单信息数据\",\"desc\":\"更新订单信息数据\",\"relatedModel\":{\"modelKey\":\"Order\",\"modelName\":\"订单信息\",\"teamId\":1,\"appId\":1},\"inputMapping\":[{\"field\":{\"fieldType\":\"Text\",\"fieldKey\":\"address\",\"fieldName\":\"address\",\"fieldType\":\"Text\"},\"value\":{\"type\":\"VarValue\",\"varValue\":[{\"valueKey\":\"ServiceInnerModel_ServiceRequest\",\"valueName\":\"服务入参\"},{\"valueKey\":\"request\",\"valueName\":\"request\"},{\"valueKey\":\"address\",\"valueName\":\"address\"}],\"valueType\":\"VAR\",\"relatedNode\":{\"nodeKey\":\"global\",\"nodeTitle\":\"全局上下文\"}}},{\"field\":{\"fieldType\":\"Text\",\"fieldKey\":\"type\",\"fieldName\":\"类型\",\"fieldType\":\"Enum\"},\"value\":{\"type\":\"VarValue\",\"varValue\":[{\"valueKey\":\"ServiceInnerModel_ServiceRequest\",\"valueName\":\"服务入参\"},{\"valueKey\":\"request\",\"valueName\":\"request\"},{\"valueKey\":\"type\",\"valueName\":\"类型\"}],\"valueType\":\"VAR\",\"relatedNode\":{\"nodeKey\":\"global\",\"nodeTitle\":\"全局上下文\"}}}],\"conditionGroup\":{\"type\":\"ConditionGroup\",\"conditions\":[{\"type\":\"ConditionGroup\",\"conditions\":[{\"type\":\"ConditionLeaf\",\"leftValue\":{\"type\":\"VarValue\",\"varValue\":[{\"valueKey\":\"id\",\"valueName\":\"id\"}],\"valueType\":\"MODEL\"},\"operator\":\"EQ\",\"rightValue\":{\"type\":\"VarValue\",\"varValue\":[{\"valueKey\":\"ServiceInnerModel_ServiceRequest\",\"valueName\":\"服务入参\"},{\"valueKey\":\"request\",\"valueName\":\"request\"},{\"valueKey\":\"id\",\"valueName\":\"id\"}],\"valueType\":\"VAR\",\"relatedNode\":{\"nodeKey\":\"global\",\"nodeTitle\":\"全局上下文\"}}}]}]}},\"nextNodeKey\":\"end0\"},{\"type\":\"EndNode\",\"key\":\"end0\",\"name\":\"结束节点\",\"props\":{\"type\":\"EndProperties\"}}]}";

        Map<String, Object> request = new HashMap<>();
        request.put("request", MapUtil.of("id", 1L, "name", "订单Update", "enable", true));

        when(serviceMetadataQuery.find(Key.of(1L, "UpdateOrderService")))
                .thenReturn(JsonUtil.fromJson(updateJSON, ServiceDefinition.class));
        Object order = serviceExecutor.execute("UpdateOrderService", Arguments.of(1L, request));

        Assertions.assertNull(order);
    }

    @Test
    public void deleteData() {

        String deleteJSON = "{\"type\":\"ServiceDefinition\",\"key\":\"DeleteOrderService\",\"name\":\"DeleteOrderService\",\"props\":{\"type\":\"ServiceProperties\",\"name\":\"DeleteOrderService\",\"desc\":\"DeleteOrderService\",\"status\":\"ENABLE\",\"transactionPropagation\":\"REQUIRED\",\"appId\":1,\"teamId\":1},\"headNodeKeys\":[\"start0\"],\"children\":[{\"type\":\"StartNode\",\"key\":\"start0\",\"name\":\"开始节点\",\"props\":{\"type\":\"StartProperties\",\"name\":\"开始节点\",\"desc\":\"开始节点\",\"input\":[{\"fieldType\":\"OnlineModel\",\"fieldKey\":\"request\",\"fieldName\":\"request\",\"fieldType\":\"OnlineModel\",\"required\":true,\"relatedModel\":{\"modelKey\":\"Order\",\"modelName\":\"订单信息\",\"teamId\":1,\"appId\":1}}]},\"nextNodeKey\":\"deleteData0\"},{\"type\":\"DeleteDataNode\",\"key\":\"deleteData0\",\"name\":\"删除订单信息数据\",\"props\":{\"type\":\"DeleteDataProperties\",\"name\":\"删除订单信息数据\",\"desc\":\"删除订单信息数据\",\"relatedModel\":{\"modelKey\":\"Order\",\"modelName\":\"订单信息\",\"teamId\":1,\"appId\":1},\"conditionGroup\":{\"type\":\"ConditionGroup\",\"conditions\":[{\"type\":\"ConditionGroup\",\"conditions\":[{\"type\":\"ConditionLeaf\",\"leftValue\":{\"type\":\"VarValue\",\"varValue\":[{\"valueKey\":\"id\",\"valueName\":\"id\"}],\"valueType\":\"MODEL\"},\"operator\":\"EQ\",\"rightValue\":{\"type\":\"VarValue\",\"varValue\":[{\"valueKey\":\"ServiceInnerModel_ServiceRequest\",\"valueName\":\"服务入参\"},{\"valueKey\":\"request\",\"valueName\":\"request\"},{\"valueKey\":\"id\",\"valueName\":\"id\"}],\"valueType\":\"VAR\",\"relatedNode\":{\"nodeKey\":\"global\",\"nodeTitle\":\"全局上下文\"}}}]}]}},\"nextNodeKey\":\"end0\"},{\"type\":\"EndNode\",\"key\":\"end0\",\"name\":\"结束节点\",\"props\":{\"type\":\"EndProperties\"}}]}";

        Map<String, Object> request = new HashMap<>();
        request.put("request", MapUtil.of("id", 1L));

        when(serviceMetadataQuery.find(Key.of(1L, "DeleteOrderService")))
                .thenReturn(JsonUtil.fromJson(deleteJSON, ServiceDefinition.class));
        Object order = serviceExecutor.execute("DeleteOrderService", Arguments.of(1L, request));

        Assertions.assertNull(order);
    }
}
