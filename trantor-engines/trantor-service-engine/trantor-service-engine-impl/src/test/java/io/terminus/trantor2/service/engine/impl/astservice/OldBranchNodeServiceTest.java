package io.terminus.trantor2.service.engine.impl.astservice;

import io.terminus.trantor2.service.common.exception.ServiceBizException;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.delegate.Metadata;
import io.terminus.trantor2.service.engine.executor.AbstractExecutorTest;
import io.terminus.trantor2.service.engine.util.DataLoader;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.when;

/**
 * OldBranchNodeServiceTest
 *
 * <AUTHOR> Created on 2024/3/11 10:04
 */
public class OldBranchNodeServiceTest extends AbstractExecutorTest {

    @Test
    public void execute_throw_3() {

        Map<String, Object> order = new HashMap<>();
        order.put("exchangeRate", 90);
        order.put("billNumber", "error");

        Map<String, Object> args = new HashMap<>();
        args.put("order", order);
        Key serviceKey = Key.of(1L, "TERP_MIGRATE$tes_9900");

        when(serviceMetadataQuery.findMeta(serviceKey))
            .thenReturn(Metadata.of(serviceKey, DataLoader.loadObj("/service/dsl/test_old_branch_node_service.json", ServiceDefinition.class)));

        try {
            Object result = serviceExecutor.execute(serviceKey.getKey(), Arguments.of(1L, args));
        } catch (Exception e) {
            Assertions.assertInstanceOf(ServiceBizException.class, e);
            Assertions.assertEquals(((ServiceBizException) e).getErrorMsg(), "已存在的计划行状态必须都为草稿");
        }

    }

    @Test
    public void execute_throw_1() {

        Map<String, Object> order = null;
//        order.put("exchangeRate", 90);
//        order.put("billNumber", "error");

        Map<String, Object> args = new HashMap<>();
        args.put("order", order);
        Key serviceKey = Key.of(1L, "TERP_MIGRATE$tes_9900");

        when(serviceMetadataQuery.findMeta(serviceKey))
            .thenReturn(Metadata.of(serviceKey, DataLoader.loadObj("/service/dsl/test_old_branch_node_service.json", ServiceDefinition.class)));

        try {
            Object result = serviceExecutor.execute(serviceKey.getKey(), Arguments.of(1L, args));
        } catch (Exception e) {
            Assertions.assertInstanceOf(ServiceBizException.class, e);
            Assertions.assertEquals(((ServiceBizException) e).getErrorMsg(), "销售订单行不能为空");
        }

    }

    @Test
    public void execute_throw_2() {

        Map<String, Object> order = new HashMap<>();
        order.put("exchangeRate", 101);
        order.put("billNumber", "error");

        Map<String, Object> args = new HashMap<>();
        args.put("order", order);
        Key serviceKey = Key.of(1L, "TERP_MIGRATE$tes_9900");

        when(modelDataRepository.findOne(Mockito.any())).thenReturn(new HashMap<>());
        when(serviceMetadataQuery.findMeta(serviceKey))
            .thenReturn(Metadata.of(serviceKey, DataLoader.loadObj("/service/dsl/test_old_branch_node_service.json", ServiceDefinition.class)));

        try {
            Object result = serviceExecutor.execute(serviceKey.getKey(), Arguments.of(1L, args));
        } catch (Exception e) {
            Assertions.assertInstanceOf(ServiceBizException.class, e);
            Assertions.assertEquals(((ServiceBizException) e).getErrorMsg(), "计划行确认数量累加不能超过订单行基本单位数量");
        }

    }
}
