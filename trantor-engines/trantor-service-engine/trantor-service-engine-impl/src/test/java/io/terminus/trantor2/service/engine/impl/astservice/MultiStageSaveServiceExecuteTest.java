package io.terminus.trantor2.service.engine.impl.astservice;

import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.executor.AbstractExecutorTest;
import io.terminus.trantor2.service.engine.util.DataLoader;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.when;

public class MultiStageSaveServiceExecuteTest extends AbstractExecutorTest {

    @Test
    public void pagingData() {
        Map<String, Object> request = new HashMap<>();
        request.put("pageNo", 1);
        request.put("pageSize", 10);

        Key serviceKey = Key.of(1L, "shopItem1_SEARCH");
        when(serviceMetadataQuery.find(serviceKey))
            .thenReturn(DataLoader.loadObj("/json/PagingItemServiceDsl.json", ServiceDefinition.class));
        Object order = serviceExecutor.execute(serviceKey.getKey(), Arguments.of(1L, request));

        Assertions.assertTrue(((Map) order).get("data") instanceof Paging);

        Assertions.assertFalse(((Paging) ((Map) order).get("data")).getData().isEmpty());
    }

}
