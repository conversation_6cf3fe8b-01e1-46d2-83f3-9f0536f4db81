package io.terminus.trantor2.service.engine.ai.memory;

import io.terminus.trantor2.common.exception.ValidationException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class AIAgentMemoryFragmentRepositoryTest {
    private RedissonClient redissonClient;
    private RList rList;
    private AIAgentMemoryFragmentRepository repository;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final String agentKey = "test-agent";

    @BeforeEach
    void setUp() throws Exception {
        redissonClient = mock(RedissonClient.class);
        rList = mock(RList.class);
        when(redissonClient.getList(anyString())).thenReturn(rList);
        repository = new AIAgentMemoryFragmentRepository(redissonClient) {
            @Override
            protected Long getCurrentUserId() {
                return 1L;
            }
        };
        // 反射注入 objectMapper
        java.lang.reflect.Field field = AIAgentMemoryFragmentRepository.class.getDeclaredField("objectMapper");
        field.setAccessible(true);
        field.set(repository, new ObjectMapper());
    }

    @Test
    void testCreateAndListMemoryFragment() throws Exception {
        when(rList.size()).thenReturn(0);
        ArgumentCaptor<String> captor = ArgumentCaptor.forClass(String.class);
        when(rList.add(captor.capture())).thenReturn(true);

        String fragmentId = repository.createMemoryFragment(agentKey, "hello world");
        assertNotNull(fragmentId);
        verify(rList, times(1)).add(anyString());

        // 模拟 listAllMemoryFragments
        Map<String, Object> fragment = new HashMap<>();
        fragment.put("id", fragmentId);
        fragment.put("content", "hello world");
        fragment.put("timestamp", System.currentTimeMillis());
        String json = objectMapper.writeValueAsString(fragment);
        when(rList.iterator()).thenReturn(Arrays.asList(json).iterator());
        when(rList.get(anyInt())).thenReturn(json);
        when(rList.size()).thenReturn(1);

        List<Map<String, Object>> list = repository.listAllMemoryFragments(agentKey);
        assertEquals(1, list.size());
        assertEquals(fragmentId, list.get(0).get("id"));
        assertEquals("hello world", list.get(0).get("content"));
    }

    @Test
    void testUpdateMemoryFragment() throws Exception {
        String fragmentId = UUID.randomUUID().toString();
        Map<String, Object> fragment = new HashMap<>();
        fragment.put("id", fragmentId);
        fragment.put("content", "old");
        fragment.put("timestamp", System.currentTimeMillis());
        String json = objectMapper.writeValueAsString(fragment);
        when(rList.size()).thenReturn(1);
        when(rList.get(0)).thenReturn(json);
        when(rList.set(eq(0), anyString())).thenReturn(null);

        repository.updateMemoryFragment(agentKey, fragmentId, "new content");
        verify(rList, times(1)).set(eq(0), anyString());
    }

    @Test
    void testDeleteMemoryFragment() throws Exception {
        String fragmentId = UUID.randomUUID().toString();
        Map<String, Object> fragment = new HashMap<>();
        fragment.put("id", fragmentId);
        fragment.put("content", "to delete");
        fragment.put("timestamp", System.currentTimeMillis());
        String json = objectMapper.writeValueAsString(fragment);
        Iterator<String> iterator = spy(Arrays.asList(json).iterator());
        when(rList.iterator()).thenReturn(iterator);
        doNothing().when(iterator).remove();

        repository.deleteMemoryFragment(agentKey, fragmentId);
        verify(iterator, times(1)).remove();
    }

    @Test
    void testCreateMemoryFragmentOverLimit() {
        when(rList.size()).thenReturn(101);
        assertThrows(ValidationException.class, () -> repository.createMemoryFragment(agentKey, "overflow"));
    }
} 