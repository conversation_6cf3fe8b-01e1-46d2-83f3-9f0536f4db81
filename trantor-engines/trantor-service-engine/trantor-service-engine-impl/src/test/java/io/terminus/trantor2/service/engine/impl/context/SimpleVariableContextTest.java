package io.terminus.trantor2.service.engine.impl.context;

import com.google.common.collect.Lists;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.service.dsl.WorkflowNode;
import io.terminus.trantor2.service.dsl.properties.WorkflowProperties;

/**
 * SimpleVariableContextTest
 *
 * <AUTHOR> Created on 2024/9/18 10:30
 */
class SimpleVariableContextTest {

    @org.junit.jupiter.api.Test
    void cloneVariables() {
        SimpleVariableContext context = new SimpleVariableContext();
        context.setVariable("a", 1);
        context.setVariable("b", MapUtil.of("c", null));
        context.setVariable("d", Lists.newArrayList("1", null, "2"));

        System.out.println(context.cloneVariables());

    }

    @org.junit.jupiter.api.Test
    void testWorkflowNode() {
        WorkflowNode node = new WorkflowNode();
        node.setProps(new WorkflowProperties());

        System.out.println(JsonUtil.toJson(node));
    }
}
