package io.terminus.trantor2.service.engine.impl.astservice.execution;

import io.terminus.trantor2.service.engine.executor.AbstractExecutorTest;
import io.terminus.trantor2.service.engine.executor.interceptor.trace.ServiceTrace;
import io.terminus.trantor2.service.engine.executor.interceptor.Contexts;
import io.terminus.trantor2.service.engine.executor.interceptor.InvocationContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;

/**
 * AbstractExecutionTest
 *
 * <AUTHOR> Created on 2024/1/24 10:27
 */
public class AbstractExecutionTest extends AbstractExecutorTest {

    @BeforeEach
    public void setUp() {
        super.setUp();
        Contexts.setInvocationContext(new InvocationContext(serviceEngine));
        Contexts.setServiceTrace(new ServiceTrace("unit-test"));
    }

    @AfterEach
    public void destroy() {
        Contexts.removeInvocationContext();
        Contexts.removeServiceTrace();
    }
}
