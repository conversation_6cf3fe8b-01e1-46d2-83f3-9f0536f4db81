package io.terminus.trantor2.service.engine.impl.astservice;

import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.common.api.response.Response;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.doc.api.dto.ActionDTO;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.service.common.spi.annotation.ServiceSPI;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.engine.ServiceEngine;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.delegate.Metadata;
import io.terminus.trantor2.service.engine.executor.AbstractExecutorTest;
import io.terminus.trantor2.service.engine.impl.astservice.spi.ActionExecutionDelegateFactory;
import io.terminus.trantor2.service.engine.impl.spi.SPILoader;
import io.terminus.trantor2.service.engine.impl.spi.scanner.ServiceSPIProxy;
import io.terminus.trantor2.service.engine.util.DataLoader;
import lombok.Data;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.mockito.Mockito.when;

/**
 * NewActionServiceTest
 *
 * <AUTHOR> Created on 2024/6/11 16:17
 */
public class NewActionServiceTest extends AbstractExecutorTest {

    @Mock
    protected SPILoader spiLoader;

    @Override
    protected void initServiceEngine(ServiceEngine serviceEngine) {
        super.initServiceEngine(serviceEngine);
        serviceEngine.setActionExecutionDelegateFactory(new ActionExecutionDelegateFactory(spiLoader, null));
    }

    @SneakyThrows
    @Test
    public void execute() {

//        a.(ERP_GEN$order)order.(ERP_GEN$user)createdBy
//        "a":{
//            "order":{
//                "createdBy":{
//                    "username":"abc",
//                    "userId":"1"
//                },
//                "orderId":1
//            }
//        }

        Map<String, Object> user = new HashMap<>();
        user.put("username", "abc");
        user.put("userId", "1");

        Map<String, Object> order = new HashMap<>();
        order.put("order", MapUtil.of("createdBy", user, "orderId", 1L));

        Map<String, Object> args = new HashMap<>();
        args.put("a", order);
        Key serviceKey = Key.of(1L, "ERP_GEN$test_new_action");

        when(serviceMetadataQuery.findMeta(serviceKey))
            .thenReturn(Metadata.of(serviceKey, DataLoader.loadObj("/service/dsl/test_new_action_service.json", ServiceDefinition.class)));

        when(metaQueryService.queryInTeam(Mockito.any())).thenReturn(queryOp);

        ActionDTO actionDTO = DataLoader.loadObj("/service/dsl/action_meta.json", ActionDTO.class);

        MetaTreeNodeExt metaTreeNodeExt = new MetaTreeNodeExt();
        metaTreeNodeExt.setKey(actionDTO.getCode());
        metaTreeNodeExt.setName(actionDTO.getName());
        metaTreeNodeExt.setProps(JsonUtil.convert(actionDTO, ObjectNode.class));
        metaTreeNodeExt.setParentKey(actionDTO.getModule());
        when(queryOp.find(MetaType.Action, "ERP_GEN$GEN_WORK_DAY_QUERY_ACTION"))
            .thenReturn(Optional.of(metaTreeNodeExt));

        when(queryOp.find(MetaType.Model, "ERP_GEN$user")).thenReturn(Optional.empty());

        ActionMock actionMock = new ActionMock();

        Method method = actionMock.getClass().getDeclaredMethod("test", UserTestRequest.class);
        ServiceSPI spi = method.getAnnotation(ServiceSPI.class);

        ServiceSPIProxy serviceSPIProxy = new ServiceSPIProxy(spi, method, actionMock);
        when(spiLoader.load("GEN_WORK_DAY_QUERY_ACTION")).thenReturn(serviceSPIProxy);

        Object result = serviceExecutor.execute(serviceKey.getKey(), Arguments.of(1L, args));
        System.out.println(JsonUtil.toJson(result));

        Assertions.assertInstanceOf(Map.class, result);

        Map<String, Object> actionRes = (Map<String, Object>) ((Map<String, Object>) result).get("data");

        Map<String, Object> userRes = (Map<String, Object>) actionRes.get("data");

        Map<String, Object> info = (Map<String, Object>) actionRes.get("info");

        Assertions.assertEquals(info.get("msg"), "test");

        Assertions.assertEquals(userRes.get("username"), "abc");
        Assertions.assertEquals(userRes.get("userId"), "1[modified]");

        // 入参回写
        Assertions.assertEquals(user.get("username"), "abc[modified]");
        Assertions.assertEquals(user.get("userId"), "1[modified]");
    }

    public static class ActionMock {

        @ServiceSPI(value = "GEN_WORK_DAY_QUERY_ACTION", key = "GEN_WORK_DAY_QUERY_ACTION", name = "查询指定日期之后之前的工作日")
        public Response<UserTestResponse> test(UserTestRequest args) {

            UserTestResponse response = new UserTestResponse();
            response.setUsername(args.getUsername());
            response.setUserId(args.getUserId() + "[modified]");

            args.setUserId(args.getUserId() + "[modified]");
            args.setUsername(args.getUsername() + "[modified]");
            Response<UserTestResponse> result = Response.ok(response);
            result.setInfo(new Response.Info("test", "test", Response.InfoLevel.INFO));
            return result;
        }
    }

    @Data
    public static class UserTestResponse {
        private String username;
        private String userId;
    }

    @Data
    public static class UserTestRequest {
        private String username;
        private String userId;
    }
}
