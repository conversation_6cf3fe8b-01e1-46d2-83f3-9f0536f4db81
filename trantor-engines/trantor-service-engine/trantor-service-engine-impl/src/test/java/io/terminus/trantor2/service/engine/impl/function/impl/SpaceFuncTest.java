package io.terminus.trantor2.service.engine.impl.function.impl;

import com.google.common.collect.Lists;
import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.function.Func;
import io.terminus.trantor2.service.engine.impl.value.expression.FuncExpression;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

/**
 * SpaceFuncTest
 *
 * <AUTHOR> Created on 2024/7/12 21:11
 */
public class SpaceFuncTest extends AbstractFuncTest {

    @Test
    void apply() {
        VariableContext context = mockContext();
        FuncExpression expression = new FuncExpression("SPACE()");
        Object result = expression.getValue(context);
        Assertions.assertEquals(StringUtils.SPACE, result);
    }

    @Test
    void apply2() {
        VariableContext context = mockContext();
        FuncExpression expression = new FuncExpression("SPACE(2)");
        Object result = expression.getValue(context);
        Assertions.assertEquals(StringUtils.repeat(StringUtils.SPACE, 2), result);
    }

    @Override
    protected List<Func> getFunc() {
        return Lists.newArrayList(new SpaceFunc());
    }
}


