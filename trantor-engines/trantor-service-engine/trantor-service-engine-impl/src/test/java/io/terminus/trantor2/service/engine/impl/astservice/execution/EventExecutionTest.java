package io.terminus.trantor2.service.engine.impl.astservice.execution;

import com.google.common.collect.Lists;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.service.dsl.EventNode;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.impl.astservice.ExecutionContext;
import io.terminus.trantor2.service.engine.util.DataLoader;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.HashMap;
import java.util.Map;

/**
 * EventExecutionTest
 *
 * <AUTHOR> Created on 2024/2/19 13:50
 */
public class EventExecutionTest extends AbstractExecutionTest {

    @Test
    void execute() {

        Map<String, Object> user = new HashMap<>();
        user.put("id", 1L);
        user.put("name", "占三");

        ExecutionContext context = new ExecutionContext(Arguments.of(1L, new HashMap<>()), serviceEngine);
        context.setVariable(Lists.newArrayList("REQUEST", "user"), user);

        Mockito.when(teamService.getTeamCode(1L)).thenReturn("terp");

        EventNode node = DataLoader.loadObj("/node/dsl/EventNode_send.json", EventNode.class);
        EventExecution execution = new EventExecution(node);
        execution.execute(context);

        Mockito.verify(eventDefinitionPublisher).publisher("terp", "test", MapUtil.of("request", user));

    }
}
