package io.terminus.trantor2.service.engine.impl.astservice;

import com.aliyun.openservices.shade.com.google.common.collect.Lists;
import io.terminus.common.scheduler.client.JobDefinitionClient;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.common.enums.ServiceType;
import io.terminus.trantor2.service.dsl.Agent;
import io.terminus.trantor2.service.engine.ai.agent.AgentService;
import io.terminus.trantor2.service.engine.ai.agent.client.AgentClient;
import io.terminus.trantor2.service.engine.ai.agent.client.impl.Agent4PClient;
import io.terminus.trantor2.service.engine.ai.client.httpclient.AiHttpClient;
import io.terminus.trantor2.service.engine.ai.client.httpclient.AiHttpInterceptor;
import io.terminus.trantor2.service.engine.ai.core.chat.session.ChatSessionManager;
import io.terminus.trantor2.service.engine.ai.core.chat.session.TaskHelper;
import io.terminus.trantor2.service.engine.ai.core.memory.ChatMemoryStore;
import io.terminus.trantor2.service.engine.ai.core.memory.store.ChatMemoryInMemoryStore;
import io.terminus.trantor2.service.engine.ai.core.session.SessionDispatcher;
import io.terminus.trantor2.service.engine.ai.memory.AIMemoryManager;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.delegate.Metadata;
import io.terminus.trantor2.service.engine.executor.AbstractExecutorTest;
import io.terminus.trantor2.service.engine.loader.DefinedServiceLoader;
import io.terminus.trantor2.service.engine.loader.ServiceLoader;
import io.terminus.trantor2.service.engine.util.DataLoader;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.mockito.Mockito.when;

/**
 * SingleAgentServiceTest
 *
 * <AUTHOR> Created on 2025/3/19 23:25
 */
@Slf4j
public class SingleAgentServiceTest extends AbstractExecutorTest {

    @Mock
    private SessionDispatcher sessionDispatcher;
    @Mock
    private JobDefinitionClient jobDefinitionClient;
    @Mock
    private AIMemoryManager aiMemoryManager;

    protected List<ServiceLoader> initServiceLoaders() {
        OkHttpClient.Builder okHttpClientBuilder = new OkHttpClient.Builder()
                .callTimeout(Duration.ofSeconds(100))
                .connectTimeout(Duration.ofSeconds(100))
                .readTimeout(Duration.ofSeconds(100))
                .writeTimeout(Duration.ofSeconds(100));

        AiHttpInterceptor interceptor = builder -> {
            builder.header("T-AI-CALLBACK", "https://t-erp-portal-staging.app.terminus.io");
            builder.header("T-AI-SOURCE-COOKIE", "t_iam_staging=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbklkIjoiYTc2M2JlMTU2Y2E3NDM1Yjg2ZWUxN2QwZTUzOTk3ZmYiLCJleHBpcmUiOjI1OTIwMCwicGF0aCI6Ii8iLCJkb21haW4iOiJ0ZXJtaW51cy5pbyIsImh0dHBPbmx5Ijp0cnVlLCJzZWN1cmUiOmZhbHNlLCJpc3MiOiJpYW0oMi41LjI0LjExMzAuMC1TTkFQU0hPVCkiLCJzdWIiOiJpYW0gdXNlciIsImV4cCI6MTc0MzE2ODMwNCwibmJmIjoxNzQyOTA5MTA0LCJpYXQiOjE3NDI5MDkxMDQsImp0aSI6IjNlM2RlZjZlYzA4OTQ4NmM4OGFkNGNlYWZkZWE1MTNiIn0.mEQ-YXCfmOhg2Y3Jpo0w40IsmgcNedrjCo2RDX6WjGA; Path=/; Domain=terminus.io; Max-Age=259200; Expires=Fri, 28 Mar 2025 13:25:04 GMT; HttpOnly");
            builder.header("T-AI-SOURCE-REFERER", "https://t-erp-portal-staging.app.terminus.io/TERP_PORTAL-TERP/TERP_PORTAL/TERP_PORTAL$Y6OAt5/page?_tab_id=Ii29W3KQeiER&sceneKey=AI%24ai_price_elasticity_md_scene&viewKey=AI%24ai_price_elasticity_md_scene%3Alist");
        };
        AiHttpClient aiHttpClient = new AiHttpClient(okHttpClientBuilder.build(), Lists.newArrayList(interceptor));
        AgentClient agentClient = new Agent4PClient("http://127.0.0.1:8000", aiHttpClient);

        ChatMemoryStore chatMemoryStore = new ChatMemoryInMemoryStore();
        ChatSessionManager chatSessionManager = new ChatSessionManager(sessionDispatcher, new TaskHelper(jobDefinitionClient), null, redissonClient, aiMemoryManager, null);

        List<ServiceLoader> loaders = super.initServiceLoaders();
        loaders.add(new DefinedServiceLoader(agentMetadataQuery,
                AgentService::new,
                false));
        return loaders;
    }

    @Test
    public void execute() {

        Map<String, Object> args = new HashMap<>();
        args.put("userContent", "帮我获取一下今日天气，可以吗？");
        args.put("sessionId", "1");

        Key serviceKey = Key.of(1L, "AI$test_multi_session");

        when(agentMetadataQuery.findMeta(serviceKey)).thenReturn(Metadata.of(
                serviceKey,
                DataLoader.loadObj("/agent/dsl/single_agent.json", Agent.class),
                ServiceType.AGENT));

        Arguments arguments = Arguments.of(1L, args);
        Object result = serviceExecutor.execute(serviceKey.getKey(), arguments);
        Assertions.assertNotNull(result);
        Assertions.assertInstanceOf(SseEmitter.class, result);

        SseEmitter emitter = (SseEmitter) result;

        AtomicInteger loop = new AtomicInteger(1);
        while (loop.get() < 5) {
            try {
                TimeUnit.SECONDS.sleep(2);
                System.out.println("============sleep============");
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            emitter.onCompletion(() -> {
                log.info("onCompletion()");
                loop.set(101);
            });
            emitter.onError(err -> {
                log.error("onError()", err);
                loop.set(101);
            });

            loop.incrementAndGet();
        }
    }

    @Test
    public void testJson() {
        SseEmitter emitter = new SseEmitter();
        System.out.println(JsonUtil.toNonIndentJson(emitter));
    }
}
