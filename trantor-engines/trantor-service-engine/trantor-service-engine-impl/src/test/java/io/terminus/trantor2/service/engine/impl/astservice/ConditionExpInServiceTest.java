package io.terminus.trantor2.service.engine.impl.astservice;

import com.google.common.collect.Lists;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.dsl.enums.VariableType;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.delegate.Metadata;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.executor.AbstractExecutorTest;
import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.value.expression.Expression;
import io.terminus.trantor2.service.engine.util.DataLoader;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.when;

/**
 * ConditionExpInServiceTest
 *
 * <AUTHOR> Created on 2024/1/23 14:11
 */
public class ConditionExpInServiceTest extends AbstractExecutorTest {

    @Test
    public void execute() {
        Map<String, Object> args = new HashMap<>();
        args.put("id", 1L);
        args.put("ids", Lists.newArrayList(1L, 2L, 3L));
        Key serviceKey = Key.of(1L, "demo$condition_exp_in_service");

        when(serviceEngine.getRuleEngineFactorApi().queryFactorBySceneKey(Mockito.any())).thenReturn(Response.ok(null));
        when(serviceMetadataQuery.findMeta(serviceKey))
            .thenReturn(Metadata.of(serviceKey, DataLoader.loadObj("/service/dsl/condition_exp_in_service.json", ServiceDefinition.class)));

        Object result = serviceExecutor.execute(serviceKey.getKey(), Arguments.of(1L, args));
        System.out.println(JsonUtil.toJson(result));
    }

    @Test
    public void executeDataTimeLTExpression() {
        Map<String, Object> args = new HashMap<>();
        args.put("time", 1707840000000L);
        Key serviceKey = Key.of(1L, "TERP_MIGRATE$asdfasdfasfasf");

        when(serviceMetadataQuery.findMeta(serviceKey))
            .thenReturn(Metadata.of(serviceKey, DataLoader.loadObj("/service/dsl/executeDataTimeLTExpression.json", ServiceDefinition.class)));

        Arguments arguments = Arguments.of(1L, args);

        Expression Today = new Expression() {
            @Override
            public Object getValue(VariableContext context) {
                return (new Date()).getTime();
            }

            @Override
            public String getExpressionText() {
                return "Today";
            }
        };

        arguments.addAttribute(VariableType.SYS.getKey(), MapUtil.of("Today", Today));

        Object result = serviceExecutor.execute(serviceKey.getKey(), arguments);

        System.out.println(JsonUtil.toJson(result));
    }


}
