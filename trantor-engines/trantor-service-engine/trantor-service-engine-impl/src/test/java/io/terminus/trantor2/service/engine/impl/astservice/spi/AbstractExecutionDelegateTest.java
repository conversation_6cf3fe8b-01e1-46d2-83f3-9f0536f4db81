package io.terminus.trantor2.service.engine.impl.astservice.spi;

import com.alibaba.fastjson.JSONObject;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.service.dsl.ServiceElement;
import io.terminus.trantor2.service.engine.impl.astservice.ExecutionContext;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AbstractExecutionDelegateTest
 *
 * <AUTHOR> Created on 2024/10/29 18:19
 */
class AbstractExecutionDelegateTest {

    @Test
    void convertParamForConvert() {

        String groovyScript = "import com.alibaba.fastjson.JSONObject\n" +
                "import io.terminus.erp.fin.spi.model.arm.dto.FinArmArHeadTrDTO\n" +
                "\n" +
                "def convert (param) {\n" +
                "    JSONObject jsonObject = param\n" +
                "    FinArmArHeadTrDTO finArmArHeadTrDTO = new FinArmArHeadTrDTO()\n" +
                "    finArmArHeadTrDTO.setArHeadCode(jsonObject.getString(\"tradingDocCode\"))\n" +
                "\n" +
                "    println(\"==============================结束arconvert groovy====================================\")\n" +
                "    \n" +
                "    JSONObject result = JSONObject.toJSON(finArmArHeadTrDTO)\n" +
                "    println(result)\n" +
                "    return result\n" +
                "}";

        AbstractExecutionDelegate delegate = new AbstractExecutionDelegate() {
            @Override
            public Object execute(ServiceElement<?> currentNode, ExecutionContext context) {
                return null;
            }

            @Override
            protected void execute(Object request, DelegateContext context) {

            }
        };

        Object newRequest = delegate.convertParamForConvert(groovyScript, MapUtil.of("tradingDocCode", "123"));

        assertInstanceOf(JSONObject.class, newRequest);
    }
}
