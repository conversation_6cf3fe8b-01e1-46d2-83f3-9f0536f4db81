package io.terminus.trantor2.service.engine.impl.sysservice;

import com.google.common.collect.Lists;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.executor.AbstractExecutorTest;
import io.terminus.trantor2.service.engine.impl.sysservice.bean.SystemServiceParameter;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.when;

/**
 * SumDataServiceTest
 *
 * <AUTHOR> Created on 2023/11/27 12:05
 */
class SumDataServiceTest extends AbstractExecutorTest {

    @Test
    void execute() {
        SystemServiceParameter args = new SystemServiceParameter();
        args.setModelKey("erp$order");
        args.setRequest(null);
        args.setFields(Lists.newArrayList("price"));

        Map<String, BigDecimal> result = new HashMap<>();
        result.put("price", new BigDecimal("100"));

        when(modelDataRepository.sum(Mockito.any(), Mockito.any())).thenReturn(result);
        when(systemServiceLoader.load(Key.of(1L, SumDataService.KEY))).thenReturn(new SumDataService());

        Object data = serviceExecutor.execute(SumDataService.KEY, Arguments.of(1L, args));

        Assertions.assertNotNull(data);

        Assertions.assertEquals(((Map) data).get("data"), result);
    }
}
