package io.terminus.trantor2.service.engine.util;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.introspect.VisibilityChecker;
import com.google.common.base.Throwables;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.service.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class DataLoader {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        OBJECT_MAPPER.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        OBJECT_MAPPER.setVisibility(VisibilityChecker.Std.defaultInstance().withFieldVisibility(JsonAutoDetect.Visibility.ANY));
    }

    public static <K, V> Map<K, V> loadMap(String jsonPath) {
        Resource resource = new ClassPathResource(jsonPath);
        JavaType javaType = OBJECT_MAPPER.getTypeFactory().constructMapType(Map.class, String.class, Object.class);
        try {
            return OBJECT_MAPPER.readValue(resource.getInputStream(), javaType);
        } catch (IOException e) {
            log.error(Throwables.getStackTraceAsString(e));
            throw new ServiceException(ErrorType.SERVER_ERROR, e.getMessage());
        }
    }

    public static <T> T loadObj(String jsonPath, Class<T> clazz) {
        Resource resource = new ClassPathResource(jsonPath);
        try {
            return OBJECT_MAPPER.readValue(resource.getInputStream(), clazz);
        } catch (IOException e) {
            log.error(Throwables.getStackTraceAsString(e));
            throw new ServiceException(ErrorType.SERVER_ERROR, e.getMessage());
        }
    }


    public static <T> T loadByTypeReference(String jsonPath, TypeReference<T> valueTypeRef) {
        Resource resource = new ClassPathResource(jsonPath);
        try {
            return OBJECT_MAPPER.readValue(resource.getInputStream(), valueTypeRef);
        } catch (IOException e) {
            log.error(Throwables.getStackTraceAsString(e));
            throw new ServiceException(ErrorType.SERVER_ERROR, e.getMessage());
        }
    }

    public static <K, V> Map<K, V> loadMapFromJson(String json) {
        JavaType javaType = OBJECT_MAPPER.getTypeFactory().constructMapType(Map.class, String.class, Object.class);
        try {
            return OBJECT_MAPPER.readValue(json, javaType);
        } catch (IOException e) {
            log.error(Throwables.getStackTraceAsString(e));
            throw new ServiceException(ErrorType.SERVER_ERROR, e.getMessage());
        }
    }

}
