package io.terminus.trantor2.service.engine.impl.function.impl;

import io.terminus.trantor2.service.engine.ServiceEngine;
import io.terminus.trantor2.service.engine.executor.AbstractExecutorTest;
import io.terminus.trantor2.service.engine.impl.context.SimpleVariableContext;
import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.function.Func;
import io.terminus.trantor2.service.engine.impl.function.FuncManager;
import io.terminus.trantor2.service.engine.impl.function.FuncManagerImpl;
import io.terminus.trantor2.service.engine.impl.function.ServiceFunction;

import java.util.List;

/**
 * AbstractFuncTest
 *
 * <AUTHOR> Created on 2023/11/9 16:14
 */
public abstract class AbstractFuncTest extends AbstractExecutorTest {

    @Override
    protected void initServiceEngine(ServiceEngine serviceEngine) {
        super.initServiceEngine(serviceEngine);
        serviceEngine.setFuncManager(mockFuncManager());
    }

    private FuncManager mockFuncManager() {
        FuncManagerImpl funcManager = new FuncManagerImpl();
        List<Func> func = getFunc();
        for (Func f : func) {
            ServiceFunction anno = f.getClass().getAnnotation(ServiceFunction.class);
            funcManager.register(anno.key(), f);
        }

        return funcManager;
    }

    protected VariableContext mockContext() {
        return new MockContext(serviceEngine);
    }

    protected abstract List<Func> getFunc();

    public static class MockContext extends SimpleVariableContext {

        public MockContext(ServiceEngine serviceEngine) {
            super(serviceEngine);
        }

        @Override
        public Long getTeamId() {
            return 1L;
        }
    }
}
