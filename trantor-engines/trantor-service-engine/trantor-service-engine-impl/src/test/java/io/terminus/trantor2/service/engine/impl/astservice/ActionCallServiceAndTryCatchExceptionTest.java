package io.terminus.trantor2.service.engine.impl.astservice;

import io.terminus.common.api.response.Response;
import io.terminus.common.runtime.helper.SpringContextHelper;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.doc.api.ActionMeta;
import io.terminus.trantor2.doc.api.dto.ActionDTO;
import io.terminus.trantor2.doc.api.props.ActionProps;
import io.terminus.trantor2.doc.repo.ActionRuntimeRepo;
import io.terminus.trantor2.service.common.spi.annotation.ServiceSPI;
import io.terminus.trantor2.service.dsl.ServiceDefinition;
import io.terminus.trantor2.service.engine.ServiceEngine;
import io.terminus.trantor2.service.engine.delegate.Arguments;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.delegate.Metadata;
import io.terminus.trantor2.service.engine.executor.AbstractExecutorTest;
import io.terminus.trantor2.service.engine.executor.ServiceExecutor;
import io.terminus.trantor2.service.engine.impl.astservice.spi.ActionExecutionDelegateFactory;
import io.terminus.trantor2.service.engine.impl.spi.SPILoader;
import io.terminus.trantor2.service.engine.impl.spi.scanner.ServiceSPIProxy;
import io.terminus.trantor2.service.engine.permission.ActionExecutionDataPermissionHandler;
import io.terminus.trantor2.service.engine.util.DataLoader;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.when;

/**
 * NewActionServiceTest
 *
 * <AUTHOR> Created on 2024/6/11 16:17
 */
@Slf4j
public class ActionCallServiceAndTryCatchExceptionTest extends AbstractExecutorTest {

    @Mock
    protected SPILoader spiLoader;
    @Mock
    protected ActionRuntimeRepo actionRuntimeRepo;
    @Mock
    private ActionExecutionDataPermissionHandler actionExecutionDataPermissionHandler;

    @Override
    protected void initServiceEngine(ServiceEngine serviceEngine) {
        super.initServiceEngine(serviceEngine);
        serviceEngine.setActionExecutionDelegateFactory(new ActionExecutionDelegateFactory(spiLoader, actionRuntimeRepo));
    }

    @SneakyThrows
    @Test
    public void execute() {

        Map<String, Object> user = new HashMap<>();
        user.put("username", "abc");
        user.put("userId", "1");

        Map<String, Object> order = new HashMap<>();
        order.put("order", MapUtil.of("createdBy", user, "orderId", 1L));

        Map<String, Object> args = new HashMap<>();
        args.put("a", order);

        Key serviceKey = Key.of(1L, "ERP_GEN$test_new_action");

        when(serviceMetadataQuery.findMeta(serviceKey))
                .thenReturn(Metadata.of(serviceKey, DataLoader.loadObj("/service/dsl/test_new_action_service.json", ServiceDefinition.class)));

        when(serviceMetadataQuery.findMeta(Key.of(1L, "ERP_GEN$test_throw_err")))
                .thenReturn(Metadata.of(Key.of(1L, "ERP_GEN$test_throw_err"),
                        DataLoader.loadObj("/service/dsl/test_throw_err_service.json", ServiceDefinition.class)));

        ActionDTO actionDTO = DataLoader.loadObj("/service/dsl/action_meta.json", ActionDTO.class);

        ActionMeta actionMeta = new ActionMeta();
        actionMeta.setKey(actionDTO.getCode());
        actionMeta.setName(actionDTO.getName());
        actionMeta.setResourceProps(JsonUtil.convert(actionDTO, ActionProps.class));
        actionMeta.setParentKey(actionDTO.getModule());
        when(actionRuntimeRepo.findOneByKey("ERP_GEN$GEN_WORK_DAY_QUERY_ACTION")).thenReturn(actionMeta);

        mockedSpringContextHelper.when(() -> SpringContextHelper.getBean(ActionExecutionDataPermissionHandler.class))
                .thenReturn(actionExecutionDataPermissionHandler);

        ActionTryCatchMock actionMock = new ActionTryCatchMock(serviceExecutor);

        Method method = actionMock.getClass().getDeclaredMethod("test", NewActionServiceTest.UserTestRequest.class);
        ServiceSPI spi = method.getAnnotation(ServiceSPI.class);

        // 在Action里catch异常了，那么外面这个服务应该是正常返回的，不应该在抛出异常
        ServiceSPIProxy serviceSPIProxy = new ServiceSPIProxy(spi, method, actionMock);
        when(spiLoader.load("GEN_WORK_DAY_QUERY_ACTION")).thenReturn(serviceSPIProxy);

        Assertions.assertDoesNotThrow(() -> {
            serviceExecutor.execute(serviceKey.getKey(), Arguments.of(1L, args));
        });
    }

    @AllArgsConstructor
    public static class ActionTryCatchMock {

        private ServiceExecutor serviceExecutor;

        @ServiceSPI(value = "GEN_WORK_DAY_QUERY_ACTION", key = "GEN_WORK_DAY_QUERY_ACTION", name = "查询指定日期之后之前的工作日")
        public Response<NewActionServiceTest.UserTestResponse> test(NewActionServiceTest.UserTestRequest args) {
            try {
                serviceExecutor.execute(Key.of(1L, "ERP_GEN$test_throw_err").getKey(), Arguments.of(1L, args));
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            return Response.ok();
        }
    }
}
