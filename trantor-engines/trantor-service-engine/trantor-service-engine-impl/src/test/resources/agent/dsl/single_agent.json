{"agent": {"type": "Agent", "id": null, "key": "ERP_SCM$test_main_helper", "name": "测试版主控助手", "desc": null, "props": {"type": "AgentProperties", "transactionPropagation": null, "aiService": null, "aiChatMode": null, "aiRoundsStrategy": null, "schedulerJob": null, "stateMachine": null, "permissionKey": null, "inputFieldRules": null, "outputFieldRules": null, "serviceFinally": null, "avatar": null, "model": {"providerType": "Azure", "name": "gpt-4.1", "type": "text_to_text", "setting": {"frequencyPenalty": 0.0, "presencePenalty": 0.0, "temperature": 0.0, "topP": 1, "chatRounds": 10, "maxTokens": 4096}}, "greetings": "你好，我是你的万能助手，请问我有什么可以帮助你的吗？", "userQuestions": ["销售价格是什么", "什么是ERP系统"], "systemPrompt": "你是一个主控租售，有很强的沟通引导能力，你的工作是深入了解用户需求，并依据需求将他们引导至合适的专业助手，为用户提供精准、高效的服务\n 比如：统计分析下大米01、大米02的销售价格合理性,毛利回报率趋势等，引导至‘数据统计分析助手’处。", "skillTools": null}, "handoffs": [{"id": null, "agentKey": "test_multi_session", "handoffDescription": "适用于从网络上查询各种信息，帮助用户解答各种问题"}, {"id": null, "agentKey": "data_analysis_hepler", "handoffDescription": "适用于据数据统计分析，帮助用户进行数据统计分析"}], "children": [{"type": "ReferenceAgent", "id": null, "key": "test_multi_session", "name": "测试版万能助手", "desc": null, "props": {"type": "ReferenceAgentProperties", "refAgentKey": "ERP_SCM$test_multi_session", "refAgentName": "测试版万能助手"}, "handoffs": null, "targetAgent": null}, {"type": "Agent", "id": null, "key": "data_analysis_hepler", "name": "数据统计分析助手", "desc": null, "props": {"type": "AgentProperties", "model": {"providerType": "Azure", "name": "gpt-4.1", "type": "text_to_text", "setting": {"frequencyPenalty": 0.0, "presencePenalty": 0.0, "temperature": 0.0, "topP": 1, "chatRounds": 10, "maxTokens": 4096}}, "systemPrompt": "你是一个数据统计分析助手，能够精准分析用户需求，能够从工具中查询出统计数据，并且严格按照统一的格式进行输出。\n统一的输出格式是：<TRANTOR_Chart>JSON_DATA</TRANTOR_Chart>\n示列如下：\n<TRANTOR_Chart>\n{\n  xAxis: {\n    type: 'category',\n    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']\n  },\n  yAxis: {\n    type: 'value'\n  },\n  series: [\n    {\n      data: [150, 230, 224, 218, 135, 147, 260],\n      type: 'line'\n    }\n  ]\n}\n</TRANTOR_Chart>\n目前支持的图表有：\n- bar: 柱状图\n- line: 折线图\n- pie: 饼图\n- scatter: 散点图\n- funnel: 漏斗图\n- radar: 雷达图\n", "skillTools": [{"type": "service", "key": "ERP_SCM$material_price_bi", "name": "查询各种物料价格合理性统计分析数据", "desc": "查询各种物料价格合理性统计分析数据"}, {"type": "service", "key": "ERP_SCM$material_gross_profit_return_trend", "name": "查询各种物料毛利回报率趋势统计分析数据", "desc": "查询各种物料毛利回报率趋势统计分析数据"}]}}]}, "isEnabled": true}