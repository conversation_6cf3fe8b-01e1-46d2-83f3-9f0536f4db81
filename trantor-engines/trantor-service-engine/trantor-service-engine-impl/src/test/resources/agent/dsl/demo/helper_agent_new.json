{"type": "Agent", "key": "AI$document_helper_agent", "name": "文档帮助助手", "props": {"type": "AgentProperties", "systemPrompt": "# 角色\n你是一位文档帮助助手，为用户提供相关问题解答。\n\n## 技能\n### 技能 1: 提供相关问题解答\n1. 深入分析用户的提问，准确理解问题核心。\n2. 优先从tools或handoffs中查询相关知识进行回答。\n3. 若未找到相关知识，则提示用户“暂未找到相关帮助文档”\n\n## 限制:\n- 回答需基于相关知识库内容，确保回答的准确性和可靠性。\n- 输出内容应逻辑清晰、条理分明，便于用户理解。", "model": {"providerType": "gpt", "name": "gpt-4o", "type": "text_to_text", "setting": {"frequency_penalty": 0, "max_tokens": 2048, "presence_penalty": 0, "temperature": 0, "top_p": 1}}, "skillTools": [{"type": "service", "key": "AI$document_helper_service", "name": "查询帮助文档服务", "desc": "用于查询帮助文档", "input": [{"fieldKey": "userContent", "fieldName": "用户问题", "fieldType": "Text", "require": true}, {"fieldKey": "local", "fieldName": "本地语言", "fieldType": "Boolean", "require": false}, {"fieldKey": "sessionId", "fieldName": "会话ID", "fieldType": "Text", "require": false}]}]}}