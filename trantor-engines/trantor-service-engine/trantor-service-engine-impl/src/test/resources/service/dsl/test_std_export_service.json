{"id": null, "key": "ERP_SCM$test_std_export_service", "name": "test_std_export_service", "props": {"type": "ServiceProperties", "transactionPropagation": "REQUIRED"}, "children": [{"type": "StartNode", "id": null, "key": "node_1hvm9kn791", "name": "开始", "props": {"type": "StartProperties", "input": [{"fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text"}, {"fieldKey": "request", "fieldName": "request", "fieldType": "Object"}, {"fieldKey": "selectFields", "fieldName": "selectFields", "fieldType": "Array"}], "output": [{"fieldKey": "data", "fieldName": "data", "fieldType": "Paging"}], "globalVariable": null}}, {"type": "GenericNode", "id": null, "key": "node_1hvm9kqja3", "name": "标准导出", "props": {"type": "GenericProperties", "implementationType": "spring", "implementation": "standardExportService", "implementationMethod": "exportData", "input": [{"fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text"}, {"fieldKey": "request", "fieldName": "request", "fieldType": "Object"}, {"fieldKey": "selectFields", "fieldName": "selectFields", "fieldType": "Array"}], "output": [{"fieldKey": "data", "fieldName": "data", "fieldType": "Paging"}], "inputMapping": [{"id": null, "field": {"fieldType": "Text", "fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>"}, "value": {"type": "VarValue", "varValue": [{"valueKey": "REQUEST", "valueName": "服务入参"}, {"valueKey": "<PERSON><PERSON><PERSON>", "valueName": "<PERSON><PERSON><PERSON>"}], "valueType": "VAR", "fieldType": "Text"}}, {"id": null, "field": {"fieldType": "Object", "fieldKey": "request", "fieldName": "request"}, "value": {"type": "VarValue", "varValue": [{"valueKey": "REQUEST", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}], "valueType": "VAR", "fieldType": "Object"}}, {"id": null, "field": {"fieldType": "Array", "fieldKey": "selectFields", "fieldName": "selectFields"}, "value": {"type": "VarValue", "varValue": [{"valueKey": "REQUEST", "valueName": "服务入参"}, {"valueKey": "selectFields", "valueName": "selectFields"}], "valueType": "VAR", "fieldType": "Array"}}], "outputAssign": {"outputAssignType": "CUSTOM", "customAssignments": [{"id": "1i0561eie26", "field": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "OUTPUT", "valueName": "服务出参"}, {"valueKey": "data", "valueName": "data"}], "valueType": "VAR", "fieldType": "Paging"}, "operator": "EQ", "value": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "NODE_OUTPUT_node_1hvm9kqja3", "valueName": "节点出参"}, {"valueKey": "data", "valueName": "data"}], "valueType": "VAR", "fieldType": "Paging"}}]}}}, {"type": "EndNode", "id": null, "key": "node_1hvm9kn792", "name": "结束", "props": {"type": "EndProperties"}}]}