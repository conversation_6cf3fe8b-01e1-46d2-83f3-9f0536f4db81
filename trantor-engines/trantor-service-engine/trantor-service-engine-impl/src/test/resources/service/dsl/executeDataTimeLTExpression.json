{"type": "ServiceDefinition", "id": null, "key": "TERP_MIGRATE$asdfasdfasfasf", "name": "asdfasdfa", "props": {"type": "ServiceProperties", "name": null, "desc": null, "transactionPropagation": null, "aiService": null, "aiChatMode": null, "aiRoundsStrategy": null}, "headNodeKeys": ["node_1gumfja06197"], "children": [{"type": "StartNode", "id": null, "key": "node_1gumfja06197", "name": "开始", "props": {"type": "StartProperties", "name": null, "desc": null, "input": [{"id": null, "fieldKey": "time", "fieldAlias": "time", "fieldName": "time", "fieldType": "DateTime", "description": null, "required": null, "defaultValue": null, "format": null}], "output": null, "globalVariable": null}, "headNodeKeys": null, "children": null, "renderType": null, "preNodeKey": null, "nextNodeKey": "node_1hn5f7ugm1"}, {"type": "ExclusiveBranchNode", "id": null, "key": "node_1hn5f7ugm1", "name": "排他分支", "props": {"type": "ExclusiveBranchProperties", "name": null, "desc": null}, "headNodeKeys": ["node_1hn5f7ugm2", "node_1hn5f7ugm3"], "children": [{"type": "ConditionNode", "id": null, "key": "node_1hn5f7ugm2", "name": "条件", "props": {"type": "ConditionProperties", "name": null, "desc": null, "conditionGroup": {"type": "ConditionGroup", "id": "pBelN-TKd3yO0LSOIwPHa", "conditions": [{"type": "ConditionGroup", "id": "eYSuSZTt0Bum8wEnfrVmj", "conditions": [{"type": "ConditionLeaf", "id": "MzB5WhO7iIjivF2hYNFwl", "key": null, "leftValue": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "REQUEST", "valueName": "服务入参", "modelAlias": null, "relatedModel": null}, {"valueKey": "time", "valueName": "time", "modelAlias": null, "relatedModel": null}], "constValue": null, "valueType": "VAR", "fieldType": "DateTime"}, "operator": "LT", "rightValue": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "SYS", "valueName": "系统变量", "modelAlias": null, "relatedModel": null}, {"valueKey": "Today", "valueName": "今天", "modelAlias": null, "relatedModel": null}], "constValue": null, "valueType": "VAR", "fieldType": "DateTime"}, "rightValues": null}], "logicOperator": "AND"}], "logicOperator": "OR"}}, "headNodeKeys": null, "children": null, "renderType": null, "preNodeKey": null, "nextNodeKey": null}, {"type": "ConditionElseNode", "id": null, "key": "node_1hn5f7ugm3", "name": "else", "props": {"type": "ConditionElseProperties", "name": null, "desc": null}, "headNodeKeys": null, "children": null, "renderType": null, "preNodeKey": null, "nextNodeKey": "node_1hn5f9oaq4"}, {"type": "ErrorNode", "id": null, "key": "node_1hn5f9oaq4", "name": "异常错误", "props": {"type": "ErrorProperties", "name": null, "desc": null, "errorCode": "TERP_MIGRATE$common_error", "errorMsg": "${errorMessage}", "placeholderMapping": [{"id": null, "key": "errorMessage", "value": {"type": "ConstValue", "id": null, "fieldType": "Text", "constValue": "111"}}], "link": "MetaLink$ErrorCode$TERP_MIGRATE$common_error"}, "headNodeKeys": null, "children": null, "renderType": null, "preNodeKey": null, "nextNodeKey": null}], "renderType": null, "preNodeKey": null, "nextNodeKey": "node_1gumfja06198"}, {"type": "EndNode", "id": null, "key": "node_1gumfja06198", "name": "结束", "props": {"type": "EndProperties", "name": null, "desc": null, "outputMapping": null}, "headNodeKeys": null, "children": null, "renderType": null, "preNodeKey": null, "nextNodeKey": null}], "input": [{"id": null, "fieldKey": "time", "fieldAlias": "time", "fieldName": "time", "fieldType": "DateTime", "description": null, "required": null, "defaultValue": null, "format": null}], "output": null}