{"type": "ServiceDefinition", "key": "CreateUserTestService", "name": "创建用户账号", "props": {"type": "ServiceProperties", "appId": 1, "teamId": 1}, "headNodeKeys": ["start0"], "children": [{"type": "StartNode", "key": "start0", "name": "开始", "props": {"type": "StartProperties", "input": [{"fieldKey": "email", "fieldName": "email", "fieldType": "Text"}, {"fieldKey": "employeeId", "fieldName": "employeeId", "fieldType": "Number"}, {"fieldKey": "mobile", "fieldName": "mobile", "fieldType": "Text"}, {"fieldKey": "password", "fieldName": "password", "fieldType": "Text"}, {"fieldKey": "username", "fieldName": "username", "fieldType": "Text"}]}, "nextNodeKey": "httpService0"}, {"type": "HttpServiceNode", "key": "httpService0", "name": "调用HTTP请求", "props": {"type": "HttpServiceProperties", "serviceName": "创建用户账号", "url": "https://organization-dev.app.terminus.io/api/trantor/org/user/create", "method": "POST", "inputMapping": [{"field": {"fieldKey": "email", "fieldName": "email", "fieldType": "Text"}, "value": {"type": "VarValue", "valueType": "VAR", "varValue": [{"valueKey": "global", "valueName": "全局上下文"}, {"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "email", "valueName": "email"}]}}, {"field": {"fieldKey": "employeeId", "fieldName": "employeeId", "fieldType": "Number"}, "value": {"type": "VarValue", "valueType": "VAR", "varValue": [{"valueKey": "global", "valueName": "全局上下文"}, {"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "employeeId", "valueName": "employeeId"}]}}, {"field": {"fieldKey": "mobile", "fieldName": "mobile", "fieldType": "Text"}, "value": {"type": "VarValue", "valueType": "VAR", "varValue": [{"valueKey": "global", "valueName": "全局上下文"}, {"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "mobile", "valueName": "mobile"}]}}, {"field": {"fieldKey": "password", "fieldName": "password", "fieldType": "Text"}, "value": {"type": "VarValue", "valueType": "VAR", "varValue": [{"valueKey": "global", "valueName": "全局上下文"}, {"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "password", "valueName": "password"}]}}, {"field": {"fieldKey": "username", "fieldName": "username", "fieldType": "Text"}, "value": {"type": "VarValue", "valueType": "VAR", "varValue": [{"valueKey": "global", "valueName": "全局上下文"}, {"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "username", "valueName": "username"}]}}], "output": [{"fieldKey": "data", "fieldName": "data", "fieldType": "Object"}]}, "nextNodeKey": "end0"}, {"type": "EndNode", "key": "end0", "name": "结束", "props": {"type": "EndProperties", "outputMapping": [{"field": {"fieldKey": "email", "fieldName": "email", "fieldType": "Text"}, "value": {"type": "VarValue", "valueType": "VAR", "varValue": [{"valueKey": "httpService0", "valueName": "调用HTTP请求"}, {"valueKey": "ServiceInnerModel_NodeOutput", "valueName": "节点出参"}, {"valueKey": "data", "valueName": "data"}, {"valueKey": "data", "valueName": "data"}, {"valueKey": "email", "valueName": "email"}]}}, {"field": {"fieldKey": "employeeId", "fieldName": "employeeId", "fieldType": "Number"}, "value": {"type": "VarValue", "valueType": "VAR", "varValue": [{"valueKey": "httpService0", "valueName": "调用HTTP请求"}, {"valueKey": "ServiceInnerModel_NodeOutput", "valueName": "节点出参"}, {"valueKey": "data", "valueName": "data"}, {"valueKey": "data", "valueName": "data"}, {"valueKey": "employeeId", "valueName": "employeeId"}]}}, {"field": {"fieldKey": "mobile", "fieldName": "mobile", "fieldType": "Text"}, "value": {"type": "VarValue", "valueType": "VAR", "varValue": [{"valueKey": "httpService0", "valueName": "调用HTTP请求"}, {"valueKey": "ServiceInnerModel_NodeOutput", "valueName": "节点出参"}, {"valueKey": "data", "valueName": "data"}, {"valueKey": "data", "valueName": "data"}, {"valueKey": "mobile", "valueName": "mobile"}]}}, {"field": {"fieldKey": "status", "fieldName": "status", "fieldType": "Boolean"}, "value": {"type": "VarValue", "valueType": "VAR", "varValue": [{"valueKey": "httpService0", "valueName": "调用HTTP请求"}, {"valueKey": "ServiceInnerModel_NodeOutput", "valueName": "节点出参"}, {"valueKey": "data", "valueName": "data"}, {"valueKey": "data", "valueName": "data"}, {"valueKey": "status", "valueName": "status"}]}}, {"field": {"fieldKey": "userId", "fieldName": "userId", "fieldType": "Number"}, "value": {"type": "VarValue", "valueType": "VAR", "varValue": [{"valueKey": "httpService0", "valueName": "调用HTTP请求"}, {"valueKey": "ServiceInnerModel_NodeOutput", "valueName": "节点出参"}, {"valueKey": "data", "valueName": "data"}, {"valueKey": "data", "valueName": "data"}, {"valueKey": "userId", "valueName": "userId"}]}}, {"field": {"fieldKey": "username", "fieldName": "username", "fieldType": "Text"}, "value": {"type": "VarValue", "valueType": "VAR", "varValue": [{"valueKey": "httpService0", "valueName": "调用HTTP请求"}, {"valueKey": "ServiceInnerModel_NodeOutput", "valueName": "节点出参"}, {"valueKey": "data", "valueName": "data"}, {"valueKey": "data", "valueName": "data"}, {"valueKey": "username", "valueName": "username"}]}}]}}]}