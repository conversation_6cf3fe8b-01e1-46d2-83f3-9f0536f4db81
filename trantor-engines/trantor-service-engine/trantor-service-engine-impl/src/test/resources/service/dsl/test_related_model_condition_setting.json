{"id": null, "key": "ERP_SCM$test_related_model_condition_setting_service", "name": "test", "desc": null, "children": [{"type": "StartNode", "id": null, "key": "node_1ian5bphk1", "name": "开始", "desc": null, "nextNodeKey": "node_1ian5bu7o3", "props": {"type": "StartProperties", "input": [], "output": null, "globalVariable": null}}, {"type": "RetrieveDataNode", "id": null, "key": "node_1ian5bu7o3", "name": "查询数据", "desc": null, "nextNodeKey": "node_1ian5bphk2", "props": {"type": "RetrieveDataProperties", "relatedModel": {"modelKey": "ERP_SCM$pur_quota_head_md", "modelAlias": "ERP_SCM$pur_quota_head_md", "modelName": "配额协议表"}, "dataConditionPermissionKey": null, "dataType": "MODEL", "pageable": null, "dynamicCondition": null, "conditionGroup": {"type": "ConditionGroup", "id": "P8OlJJHUJyKnWSnKyFA49", "conditions": [{"type": "ConditionGroup", "id": "b48S3-TuRwdxHUpbjpSRn", "conditions": [{"type": "ConditionLeaf", "id": "pQ35VhBX_456L0qr4tWVO", "key": null, "leftValue": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "id", "valueName": "ID", "fieldType": null, "modelAlias": "ERP_SCM$pur_quota_head_md"}], "scope": null, "valueType": "MODEL", "fieldType": "Number"}, "operator": "EQ", "rightValue": {"type": "ConstValue", "id": null, "fieldType": "Number", "constValue": "1"}, "rightValues": null}], "logicOperator": "AND"}], "logicOperator": "OR"}, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": [{"fieldKey": "matCode", "modelKey": "ERP_GEN$gen_mat_md", "conditionGroup": {"type": "ConditionGroup", "id": "GSR7bq5eLMuhJnfQ1kaSH", "conditions": [{"type": "ConditionGroup", "id": "qnPF41faPzI046TtSxuoQ", "conditions": [{"type": "ConditionLeaf", "id": "I_8VO5ZlHZ0Cm1gUAk5Lh", "key": null, "leftValue": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "matCode", "valueName": "物料编码", "fieldType": null, "modelAlias": "ERP_GEN$gen_mat_md"}], "scope": null, "valueType": "MODEL", "fieldType": "Text"}, "operator": "EQ", "rightValue": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "<PERSON><PERSON><PERSON>", "valueName": "物料名称", "fieldType": null, "modelAlias": "ERP_GEN$gen_mat_md"}], "scope": null, "valueType": "MODEL", "fieldType": "Text"}, "rightValues": null}], "logicOperator": "AND"}], "logicOperator": "OR"}, "sortOrders": [{"fieldAlias": "matCode", "sortType": "ASC"}], "subQueryRelatedModels": []}, {"fieldKey": "purOrgCode", "modelKey": "sys_common$org_struct_md", "conditionGroup": null, "sortOrders": null, "subQueryRelatedModels": []}], "queryModelFields": {"modelKey": "ERP_SCM$pur_quota_head_md", "allFields": false, "queryFields": [{"fieldKey": "purQuotaHeadCode"}, {"fieldKey": "quotaDateFrom"}, {"fieldKey": "matCode", "queryModelFields": {"modelKey": "ERP_GEN$gen_mat_md", "allFields": false, "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "purOrgCode", "queryModelFields": {"modelKey": "sys_common$org_struct_md", "allFields": false, "queryFields": [{"fieldKey": "id"}]}}]}, "maximum": null, "desensitized": true, "outputAssign": {"outputAssignType": "SYSTEM", "customAssignments": null}}}, {"type": "EndNode", "id": null, "key": "node_1ian5bphk2", "name": "结束", "desc": null, "props": {"type": "EndProperties"}}], "input": [], "output": null, "props": {"type": "ServiceProperties", "transactionPropagation": "NOT_SUPPORTED"}}