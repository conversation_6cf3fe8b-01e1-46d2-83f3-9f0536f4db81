{"id": null, "key": "ERP_SCM$test_query", "name": "test_query", "desc": null, "props": {"type": "ServiceProperties", "transactionPropagation": "NOT_SUPPORTED", "aiService": null, "aiChatMode": null, "aiRoundsStrategy": null, "schedulerJob": null, "stateMachine": null, "permissionKey": "ERP_SCM$test_query_service_perm_ac_89dc3a", "inputFieldRules": null, "outputFieldRules": null}, "children": [{"key": "node_1i8katk9f1", "type": "StartNode", "name": "开始", "nextNodeKey": "node_1id6vs63v20", "props": {"type": "StartProperties", "input": [{"id": null, "fieldKey": "pageable", "fieldAlias": "pageable", "fieldName": "分页设置", "fieldType": "Pageable", "elements": [{"id": null, "fieldKey": "conditionGroup", "fieldAlias": "conditionGroup", "fieldName": "条件组", "fieldType": "ConditionGroup", "elements": null}, {"id": null, "fieldKey": "conditionItems", "fieldAlias": "conditionItems", "fieldName": "简化版条件组", "fieldType": "ConditionItems", "elements": [{"id": null, "fieldKey": null, "fieldName": null, "fieldType": "Model", "relatedModel": null}, {"id": null, "fieldKey": null, "fieldName": null, "fieldType": "Text"}]}, {"id": null, "fieldKey": "sortOrders", "fieldAlias": "sortOrders", "fieldName": "字段排序", "fieldType": "Array", "element": null}, {"id": null, "fieldKey": "pageNo", "fieldAlias": "pageNo", "fieldName": "页码", "fieldType": "Number"}, {"id": null, "fieldKey": "pageSize", "fieldAlias": "pageSize", "fieldName": "每页数量", "fieldType": "Number"}, {"id": null, "fieldKey": "keyword", "fieldAlias": "keyword", "fieldName": "模糊搜索", "fieldType": "Text"}]}, {"id": null, "fieldKey": "arr", "fieldAlias": "arr", "fieldName": "arr", "fieldType": "Array", "element": {"id": null, "fieldKey": "element", "fieldAlias": "element", "fieldName": "element", "fieldType": "Text"}}, {"id": null, "fieldKey": "one", "fieldAlias": "one", "fieldName": "one", "fieldType": "Text"}], "globalVariable": [{"fieldKey": "fisrt", "fieldName": "fisrt", "fieldType": "Text"}], "nodeTitle": "开始", "nodeKey": "node_1i8katk9f1"}}, {"key": "node_1id6vs63v20", "type": "ParallelBranchNode", "name": "并行分支", "nextNodeKey": "node_1id6vt2uh25", "props": {"nodeTitle": "并行分支", "type": "ParallelBranchProperties"}, "children": [{"key": "node_1id6vs63v21", "type": "ParallelConditionNode", "name": "并行分支", "nextNodeKey": "node_1id6vsbb823", "props": {"nodeTitle": "并行分支", "type": "ParallelConditionProperties", "nodeKey": "node_1id6vs63v21"}, "children": [{"key": "node_1id6vsbb823", "type": "AssignNode", "name": "赋值", "props": {"nodeTitle": "赋值", "type": "AssignProperties", "assignments": [{"id": "1id77vp0f33", "field": {"type": "VarValue", "valueType": "VAR", "fieldType": "Text", "varValue": [{"valueKey": "GLOBAL", "valueName": "全局变量"}, {"valueKey": "fisrt", "valueName": "fisrt"}]}, "value": {"type": "ConstValue", "valueType": "CONST", "constValue": "111111", "fieldType": "Text"}}], "nodeKey": "node_1id6vsbb823"}}]}, {"key": "node_1id6vs63v22", "type": "ParallelConditionNode", "name": "并行分支", "props": {"nodeTitle": "并行分支", "type": "ParallelConditionProperties"}, "children": []}]}, {"key": "node_1id6vt2uh25", "type": "CallServiceNode", "name": "调用编排服务", "nextNodeKey": "node_1i8katk9f2", "props": {"nodeTitle": "调用编排服务", "type": "CallServiceProperties", "serviceKey": "ERP_SCM$test_query2", "transactionPropagation": "NOT_SUPPORTED", "async": true, "outputAssign": {"outputAssignType": "SYSTEM"}, "nodeKey": "node_1id6vt2uh25"}}, {"key": "node_1i8katk9f2", "type": "EndNode", "name": "结束", "props": {"type": "EndProperties", "nodeTitle": "结束"}}], "input": [{"id": null, "fieldKey": "pageable", "fieldAlias": "pageable", "fieldName": "分页设置", "fieldType": "Pageable", "elements": [{"id": null, "fieldKey": "conditionGroup", "fieldAlias": "conditionGroup", "fieldName": "条件组", "fieldType": "ConditionGroup", "elements": null}, {"id": null, "fieldKey": "conditionItems", "fieldAlias": "conditionItems", "fieldName": "简化版条件组", "fieldType": "ConditionItems", "elements": [{"id": null, "fieldKey": null, "fieldName": null, "fieldType": "Model", "relatedModel": null}, {"id": null, "fieldKey": null, "fieldName": null, "fieldType": "Text"}]}, {"id": null, "fieldKey": "sortOrders", "fieldAlias": "sortOrders", "fieldName": "字段排序", "fieldType": "Array", "element": null}, {"id": null, "fieldKey": "pageNo", "fieldAlias": "pageNo", "fieldName": "页码", "fieldType": "Number"}, {"id": null, "fieldKey": "pageSize", "fieldAlias": "pageSize", "fieldName": "每页数量", "fieldType": "Number"}, {"id": null, "fieldKey": "keyword", "fieldAlias": "keyword", "fieldName": "模糊搜索", "fieldType": "Text"}]}, {"id": null, "fieldKey": "arr", "fieldAlias": "arr", "fieldName": "arr", "fieldType": "Array", "element": {"id": null, "fieldKey": "element", "fieldAlias": "element", "fieldName": "element", "fieldType": "Text"}}, {"id": null, "fieldKey": "one", "fieldAlias": "one", "fieldName": "one", "fieldType": "Text"}], "output": null}