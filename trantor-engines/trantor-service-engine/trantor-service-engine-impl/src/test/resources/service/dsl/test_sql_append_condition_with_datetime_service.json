{"id": null, "key": "QA$test_sql_append_condition_with_datetime_service", "name": "测试服务流程10144", "desc": null, "props": {"type": "ServiceProperties", "transactionPropagation": "NOT_SUPPORTED"}, "children": [{"type": "StartNode", "id": null, "key": "node_1ia540luh1", "name": "开始", "desc": null, "props": {"type": "StartProperties", "input": [{"id": null, "fieldKey": "conditionItems", "fieldAlias": "conditionItems", "fieldName": "简化版条件组", "fieldType": "ConditionItems", "elements": [{"id": null, "fieldKey": "conditions", "fieldAlias": "conditions", "fieldName": "条件对象", "fieldType": "Model", "relatedModel": {"modelKey": "QA$gen_test_process_md", "modelAlias": "QA$gen_test_process_md", "modelName": "QA$gen_test_process_md"}, "modelKey": "QA$gen_test_process_md"}, {"id": null, "fieldKey": "logicOperator", "fieldAlias": "logicOperator", "fieldName": "逻辑运算符", "fieldType": "Text"}]}, {"id": null, "fieldKey": "name", "fieldAlias": "name", "fieldName": "name", "fieldType": "Text"}], "output": [{"id": null, "fieldKey": "data", "fieldAlias": "data", "fieldName": "data", "fieldType": "Paging", "elements": [{"id": null, "fieldKey": "total", "fieldAlias": "total", "fieldName": "total", "fieldType": "Number"}, {"id": null, "fieldKey": "data", "fieldAlias": "data", "fieldName": "data", "fieldType": "Array", "element": {"id": null, "fieldKey": "element", "fieldAlias": "element", "fieldName": "element", "fieldType": "Model", "relatedModel": {"modelKey": "QA$gen_test_process_md", "modelAlias": "QA$gen_test_process_md", "modelName": "QA$gen_test_process_md"}, "modelKey": "QA$gen_test_process_md"}}]}], "globalVariable": [{"id": null, "fieldKey": "test_process", "fieldAlias": "test_process", "fieldName": "test_process", "fieldType": "Model", "relatedModel": {"modelKey": "QA$gen_test_process_md", "modelAlias": "QA$gen_test_process_md", "modelName": "QA$gen_test_process_md"}, "modelKey": "QA$gen_test_process_md"}]}, "nextNodeKey": "node_1iaab5os26"}, {"type": "SqlNode", "id": null, "key": "node_1iaab5os26", "name": "在线SQL脚本", "desc": null, "props": {"type": "SqlProperties", "sqlScript": "SELECT * FROM gen_test_process_md WHERE ${REQUEST.conditionItems} ", "output": [{"id": null, "fieldKey": "data", "fieldAlias": "data", "fieldName": "data", "fieldType": "Array", "element": {"id": null, "fieldKey": "element", "fieldAlias": "element", "fieldName": "element", "fieldType": "Object", "elements": null}}], "outputAssign": {"outputAssignType": "CUSTOM", "customAssignments": [{"id": "1iaab9j6i7", "field": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "OUTPUT", "valueName": "服务出参", "fieldType": null}, {"valueKey": "data", "valueName": "data", "fieldType": null}, {"valueKey": "data", "valueName": "data", "fieldType": null, "relatedModel": {"modelKey": "QA$gen_test_process_md", "modelAlias": "QA$gen_test_process_md", "modelName": "QA$gen_test_process_md"}}], "scope": null, "valueType": "VAR", "fieldType": "Array"}, "operator": "EQ", "value": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "NODE_OUTPUT_node_1iaab5os26", "valueName": "出参结构体", "fieldType": null}, {"valueKey": "data", "valueName": "data", "fieldType": null}], "scope": null, "valueType": "VAR", "fieldType": "Array"}}]}}, "nextNodeKey": "node_1ia540luh2"}, {"type": "EndNode", "id": null, "key": "node_1ia540luh2", "name": "结束", "desc": null, "props": {"type": "EndProperties"}}]}