{"id": null, "key": "QA$exception_node_test2_1106", "name": "异常节点测试2_1106", "desc": null, "props": {"type": "ServiceProperties", "transactionPropagation": "NOT_SUPPORTED"}, "children": [{"key": "node_1ibtnoila4", "type": "StartNode", "name": "开始", "nextNodeKey": "node_1ibvnohi227", "props": {"type": "StartProperties", "input": [{"id": null, "fieldKey": "test", "fieldAlias": "test", "fieldName": "test", "fieldType": "Model", "relatedModel": {"modelKey": "QA$gen_svc_enum_cf", "modelAlias": "QA$gen_svc_enum_cf", "modelName": "QA$gen_svc_enum_cf"}}, {"id": null, "fieldKey": "test2", "fieldAlias": "test2", "fieldName": "test2", "fieldType": "Object", "elements": null}], "output": [{"id": null, "fieldKey": "data", "fieldAlias": "data", "fieldName": "data", "fieldType": "Object", "elements": null}], "globalVariable": null, "nodeTitle": "开始"}}, {"key": "node_1ibvnohi227", "type": "CallServiceNode", "name": "调用编排服务", "nextNodeKey": "node_1ibtnoila5", "props": {"type": "CallServiceProperties", "errorConfigs": [{"type": "CallServiceProperties", "serviceKey": "QA$add_data_service_115", "serviceName": "新增数据服务115", "transactionPropagation": "NOT_SUPPORTED", "async": false, "inputMapping": [{"id": "1ibvnpm4g30", "field": {"id": null, "fieldKey": "test", "fieldAlias": "test", "fieldName": "test", "fieldType": "Model", "relatedModel": {"modelKey": "QA$gen_svc_enum_cf", "modelAlias": "QA$gen_svc_enum_cf", "modelName": "QA$gen_svc_enum_cf"}}, "value": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "REQUEST", "valueName": "服务入参", "fieldType": null}, {"valueKey": "test2", "valueName": "test2", "fieldType": null}], "scope": null, "valueType": "VAR", "fieldType": "Model"}}], "output": [{"id": null, "fieldKey": "data", "fieldAlias": "data", "fieldName": "data", "fieldType": "Object", "elements": null}], "outputAssign": null}], "serviceKey": "QA$add_data_service_115", "serviceName": null, "transactionPropagation": "NOT_SUPPORTED", "async": false, "inputMapping": [{"id": "1ibvnp7m628", "field": {"id": null, "fieldKey": "test", "fieldAlias": "test", "fieldName": "test", "fieldType": "Model", "relatedModel": {"modelKey": "QA$gen_svc_enum_cf", "modelAlias": "QA$gen_svc_enum_cf", "modelName": "QA$gen_svc_enum_cf"}}, "value": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "REQUEST", "valueName": "服务入参", "fieldType": null}, {"valueKey": "test", "valueName": "test", "fieldType": null, "relatedModel": {"modelKey": "QA$gen_svc_enum_cf", "modelAlias": "QA$gen_svc_enum_cf", "modelName": "QA$gen_svc_enum_cf"}}], "scope": null, "valueType": "VAR", "fieldType": "Model"}}], "output": [{"id": null, "fieldKey": "data", "fieldAlias": "data", "fieldName": "data", "fieldType": "Object", "elements": null}], "outputAssign": {"outputAssignType": "CUSTOM", "customAssignments": [{"id": "1ibvnptm831", "field": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "OUTPUT", "valueName": "服务出参", "fieldType": null}, {"valueKey": "data", "valueName": "data", "fieldType": null}], "scope": null, "valueType": "VAR", "fieldType": "Object"}, "operator": "EQ", "value": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "NODE_OUTPUT_node_1ibvnohi227", "valueName": "出参结构体", "fieldType": null}, {"valueKey": "data", "valueName": "data", "fieldType": null}], "scope": null, "valueType": "VAR", "fieldType": "Object"}}]}, "nodeTitle": "调用编排服务", "nodeKey": "node_1ibvnohi227"}}, {"key": "node_1ibtnoila5", "type": "EndNode", "name": "结束", "props": {"type": "EndProperties", "nodeTitle": "结束"}}], "input": [{"id": null, "fieldKey": "test", "fieldAlias": "test", "fieldName": "test", "fieldType": "Model", "relatedModel": {"modelKey": "QA$gen_svc_enum_cf", "modelAlias": "QA$gen_svc_enum_cf", "modelName": "QA$gen_svc_enum_cf"}}, {"id": null, "fieldKey": "test2", "fieldAlias": "test2", "fieldName": "test2", "fieldType": "Object", "elements": null}], "output": [{"id": null, "fieldKey": "data", "fieldAlias": "data", "fieldName": "data", "fieldType": "Object", "elements": null}]}