{"key": "ERP_GEN$test_throw_err", "name": "服务", "type": "ServiceDefinition", "props": {"type": "ServiceProperties", "transactionPropagation": "NOT_SUPPORTED"}, "children": [{"key": "node_1ieb8d01b1", "type": "StartNode", "name": "开始", "nextNodeKey": "node_1ieb8d62s3", "props": {"nodeTitle": "开始", "type": "StartProperties"}}, {"key": "node_1ieb8d62s3", "type": "ErrorNode", "name": "异常错误", "nextNodeKey": "node_1ieb8d01b2", "props": {"nodeTitle": "异常错误", "type": "ErrorProperties", "errorCode": "TSRM$TSRM_PR_LIST_HAVE_DIFF_MAT", "placeholderMapping": [], "errorMsg": "不同物料的采购申请不能合并寻源", "nodeKey": "node_1ieb8d62s3"}}, {"key": "node_1ieb8d01b2", "type": "EndNode", "name": "结束", "props": {"nodeTitle": "结束", "type": "EndProperties"}}]}