{"id": null, "key": "ERP_SCM$test_related_model_order_by_field_setting_service", "name": "test_query", "desc": null, "props": {"type": "ServiceProperties", "transactionPropagation": "NOT_SUPPORTED"}, "children": [{"key": "node_1i8katk9f1", "type": "StartNode", "name": "开始", "nextNodeKey": "node_1i8katnul3", "props": {"type": "StartProperties", "input": null, "output": null, "globalVariable": null, "nodeTitle": "开始"}}, {"key": "node_1i8katnul3", "type": "RetrieveDataNode", "name": "查询数据", "nextNodeKey": "node_1i8katk9f2", "props": {"type": "RetrieveDataProperties", "relatedModel": {"modelKey": "ERP_SCM$scm_order_rule_head_tr", "modelAlias": "ERP_SCM$scm_order_rule_head_tr", "modelName": "单据标准收付款规则头表"}, "dataConditionPermissionKey": null, "dataType": "MODEL", "pageable": null, "dynamicCondition": null, "conditionGroup": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": [{"fieldKey": "genBrmRuleTypeId", "modelKey": "ERP_GEN$gen_brm_rule_type_cf", "conditionGroup": null, "sortOrders": [{"_row_id_": "ZKfNaoM", "fieldAlias": "id", "sortType": "DESC"}, {"_row_id_": "OUX91ie", "fieldAlias": "updatedBy", "sortType": "ASC"}], "subQueryRelatedModels": []}, {"fieldKey": "iesOrderRuleItemList", "modelKey": "ERP_SCM$scm_order_rule_item_tr", "conditionGroup": null, "sortOrders": [{"_row_id_": "ECALQ3D", "fieldAlias": "delayDays", "sortType": "ASC"}], "subQueryRelatedModels": [{"fieldKey": "iesOrderRuleHeadTrId", "modelKey": "ERP_SCM$scm_order_rule_head_tr", "sortOrders": [{"_row_id_": "EVZKU8S", "fieldAlias": "code", "sortType": "DESC"}], "subQueryRelatedModels": []}]}, {"fieldKey": "purPoHeadTrId", "modelKey": "ERP_SCM$pur_po_head_tr", "conditionGroup": null, "subQueryRelatedModels": []}, {"fieldKey": "slsSoHeadTrId", "modelKey": "ERP_SCM$sls_so_head_tr", "conditionGroup": null, "sortOrders": [{"_row_id_": "W6KqOUd", "fieldAlias": "exchRate", "sortType": "DESC"}], "subQueryRelatedModels": []}, {"fieldKey": "created<PERSON>y", "modelKey": "ERP_SCM$user", "conditionGroup": null, "subQueryRelatedModels": []}, {"fieldKey": "updatedBy", "modelKey": "ERP_SCM$user", "conditionGroup": null, "subQueryRelatedModels": []}], "queryModelFields": {"modelKey": "ERP_SCM$scm_order_rule_head_tr", "queryFields": [{"fieldKey": "genBrmRuleTypeId", "queryModelFields": {"modelKey": "ERP_GEN$gen_brm_rule_type_cf", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "iesOrderRuleItemList", "queryModelFields": {"modelKey": "ERP_SCM$scm_order_rule_item_tr", "queryFields": [{"fieldKey": "id"}, {"fieldKey": "iesOrderRuleHeadTrId", "queryModelFields": {"modelKey": "ERP_SCM$scm_order_rule_head_tr", "queryFields": [{"fieldKey": "docId"}]}}]}}, {"fieldKey": "purPoHeadTrId", "queryModelFields": {"modelKey": "ERP_SCM$pur_po_head_tr", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "slsSoHeadTrId", "queryModelFields": {"modelKey": "ERP_SCM$sls_so_head_tr", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "created<PERSON>y", "queryModelFields": {"modelKey": "ERP_SCM$user", "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "updatedBy", "queryModelFields": {"modelKey": "ERP_SCM$user", "queryFields": [{"fieldKey": "id"}]}}]}, "maximum": null, "desensitized": true, "outputAssign": {"outputAssignType": "SYSTEM", "customAssignments": null}, "nodeTitle": "查询数据", "ERP_SCM$sls_so_head_tr": {"sortOrders": [{"_row_id_": "W6KqOUd", "fieldAlias": "exchRate", "sortType": "DESC"}]}, "nodeKey": "node_1i8katnul3"}}, {"key": "node_1i8katk9f2", "type": "EndNode", "name": "结束", "props": {"type": "EndProperties", "nodeTitle": "结束"}}]}