{"id": null, "key": "TERP_MIGRATE$test_parallel_service", "name": "JoseParallelNode624", "desc": null, "props": {"type": "ServiceProperties", "errorConfigs": null, "transactionPropagation": "NOT_SUPPORTED", "aiService": false, "aiChatMode": null, "aiRoundsStrategy": null, "schedulerJob": {"enable": false, "jobKey": null, "jobName": null, "jobType": null, "desc": null, "expression": null, "params": null, "execType": "TService"}, "stateMachine": null, "fieldRules": null}, "children": [{"type": "StartNode", "id": null, "key": "node_1i0kih1bg1", "name": "开始", "desc": null, "props": {"type": "StartProperties", "errorConfigs": null, "input": [{"id": null, "fieldKey": "request", "fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "description": null, "required": null, "defaultValue": null, "elements": [{"id": null, "fieldKey": "entity", "fieldAlias": "entity", "fieldName": "entity", "fieldType": "Model", "description": null, "required": null, "defaultValue": null, "relatedModel": {"modelKey": "bid_management_module$bid_specialists_md", "modelName": "bid_management_module$bid_specialists_md"}, "relation": null, "modelKey": "bid_management_module$bid_specialists_md"}, {"id": null, "fieldKey": "name", "fieldAlias": "name", "fieldName": "name", "fieldType": "Text", "description": null, "required": null, "defaultValue": null}, {"id": null, "fieldKey": "test_vo", "fieldAlias": "test_vo", "fieldName": "testVO", "fieldType": "Object", "description": null, "required": null, "defaultValue": null, "elements": [{"id": null, "fieldKey": "supplier_master_data", "fieldAlias": "supplier_master_data", "fieldName": "供应商主数据", "fieldType": "Model", "description": null, "required": null, "defaultValue": null, "relatedModel": {"modelKey": "GEN$gen_vend_info_md", "modelName": "GEN$gen_vend_info_md"}, "relation": null, "modelKey": "GEN$gen_vend_info_md"}]}]}, {"id": null, "fieldKey": "applyArray", "fieldAlias": "applyArray", "fieldName": "applyArray", "fieldType": "Array", "description": null, "required": null, "defaultValue": null, "element": {"id": null, "fieldKey": "element", "fieldAlias": "element", "fieldName": "element", "fieldType": "Model", "description": null, "required": null, "defaultValue": null, "relatedModel": {"modelKey": "SLS$sls_so_type_cf", "modelName": "SLS$sls_so_type_cf"}, "relation": null, "modelKey": "SLS$sls_so_type_cf"}}, {"id": null, "fieldKey": "text_array", "fieldAlias": "text_array", "fieldName": "texrArray", "fieldType": "Array", "description": null, "required": null, "defaultValue": null, "element": {"id": null, "fieldKey": "element", "fieldAlias": "element", "fieldName": "element", "fieldType": "Text", "description": null, "required": null, "defaultValue": null}}, {"id": null, "fieldKey": "expert_pool", "fieldAlias": "expert_pool", "fieldName": "专家库", "fieldType": "Model", "description": null, "required": null, "defaultValue": null, "relatedModel": {"modelKey": "bid_management_module$bid_specialists_md", "modelName": "bid_management_module$bid_specialists_md"}, "relation": null, "modelKey": "bid_management_module$bid_specialists_md"}], "output": [{"fieldKey": "id1", "fieldName": "id1", "fieldType": "Number"}, {"fieldKey": "id2", "fieldName": "id2", "fieldType": "Number"}], "globalVariable": [{"id": null, "fieldKey": "supplier", "fieldAlias": "supplier", "fieldName": "供应商", "fieldType": "Model", "description": null, "required": null, "defaultValue": null, "relatedModel": {"modelKey": "TERP_MIGRATE$sup_invitation", "modelName": "TERP_MIGRATE$sup_invitation"}, "relation": null, "modelKey": "TERP_MIGRATE$sup_invitation"}, {"id": null, "fieldKey": "id1", "fieldAlias": "id1", "fieldName": "id1", "fieldType": "Number", "description": null, "required": null, "defaultValue": null}, {"id": null, "fieldKey": "id2", "fieldAlias": "id2", "fieldName": "id2", "fieldType": "Number", "description": null, "required": null, "defaultValue": null}]}, "nextNodeKey": "node_1i14m5gkf7"}, {"type": "ParallelBranchNode", "id": null, "key": "node_1i14m5gkf7", "name": "并行分支", "desc": null, "props": {"type": "ParallelBranchProperties", "errorConfigs": null}, "nextNodeKey": "node_1i0kih1bg2", "children": [{"type": "ParallelConditionNode", "id": null, "key": "node_1i14m5gkg8", "name": "并行分支1", "desc": null, "props": {"type": "ParallelConditionProperties", "errorConfigs": null, "conditionGroup": null}, "nextNodeKey": "node_1i14m7sqf11", "children": [{"type": "RetrieveDataNode", "id": null, "key": "node_1i14m7sqf11", "name": "查询数据", "desc": null, "props": {"type": "RetrieveDataProperties", "errorConfigs": null, "relatedModel": {"modelKey": "bid_management_module$bid_specialists_md", "modelName": "专家库主数据"}, "dataType": "MODEL", "pageable": null, "conditionGroup": null, "sortOrders": null, "stopWhenDataEmpty": false, "subQueryRelatedModels": [{"fieldKey": "created<PERSON>y", "modelKey": "bid_management_module$user", "conditionGroup": null, "subQueryRelatedModels": []}, {"fieldKey": "updatedBy", "modelKey": "bid_management_module$user", "conditionGroup": null, "subQueryRelatedModels": []}, {"fieldKey": "specialistsRelatedPersonId", "modelKey": "GEN$gen_employee_md", "conditionGroup": {"type": "ConditionGroup", "id": "ugqHFHfLUJXPzO80WElRo", "conditions": [{"type": "ConditionGroup", "id": "emaOcU4CgbeBfPRg6Dme8", "conditions": [{"type": "ConditionLeaf", "id": "5ii7GYw9uQa6LyF_ULVAO", "key": null, "leftValue": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "id", "valueName": "ID", "fieldType": null, "modelAlias": "GEN$gen_employee_md", "relatedModel": null}], "valueType": "MODEL", "fieldType": "Number"}, "operator": "EQ", "rightValue": {"type": "ConstValue", "id": null, "fieldType": "Number", "constValue": "1"}, "rightValues": null}], "logicOperator": "AND"}], "logicOperator": "OR"}, "subQueryRelatedModels": []}, {"fieldKey": "specialistsCountry", "modelKey": "GEN$gen_coun_type_cf", "conditionGroup": null, "subQueryRelatedModels": []}, {"fieldKey": "specialistsAdress", "modelKey": "GEN$gen_addr_acc_md", "conditionGroup": null, "subQueryRelatedModels": []}, {"fieldKey": "specialistsSeverItemId", "modelKey": "GEN$gen_mat_cate_md", "conditionGroup": null, "subQueryRelatedModels": []}, {"fieldKey": "bidSpecialistsCompanyId", "modelKey": "bid_management_module$bid_specialists_company", "conditionGroup": null, "subQueryRelatedModels": []}], "queryModelFields": {"modelKey": "bid_management_module$bid_specialists_md", "allFields": true, "queryFields": [{"fieldKey": "created<PERSON>y", "queryModelFields": {"modelKey": "bid_management_module$user", "allFields": false, "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "updatedBy", "queryModelFields": {"modelKey": "bid_management_module$user", "allFields": false, "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "specialistsRelatedPersonId", "queryModelFields": {"modelKey": "GEN$gen_employee_md", "allFields": false, "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "specialistsCountry", "queryModelFields": {"modelKey": "GEN$gen_coun_type_cf", "allFields": false, "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "specialistsAdress", "queryModelFields": {"modelKey": "GEN$gen_addr_acc_md", "allFields": false, "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "specialistsSeverItemId", "queryModelFields": {"modelKey": "GEN$gen_mat_cate_md", "allFields": false, "queryFields": [{"fieldKey": "id"}]}}, {"fieldKey": "bidSpecialistsCompanyId", "queryModelFields": {"modelKey": "bid_management_module$bid_specialists_company", "allFields": false, "queryFields": [{"fieldKey": "id"}]}}]}, "maximum": null, "desensitized": true, "outputAssign": {"outputAssignType": "SYSTEM", "customAssignments": null}}, "nextNodeKey": "node_1i14oe2e21"}, {"type": "AssignNode", "id": null, "key": "node_1i14oe2e21", "name": "赋值", "desc": null, "props": {"type": "AssignProperties", "errorConfigs": null, "assignments": [{"id": "1i14oesnl2", "field": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "GLOBAL", "valueName": "全局变量", "fieldType": null, "modelAlias": null, "relatedModel": null}, {"valueKey": "id1", "valueName": "id1", "fieldType": null, "modelAlias": null, "relatedModel": null}], "valueType": "VAR", "fieldType": "Number"}, "operator": "EQ", "value": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "GLOBAL", "valueName": "全局变量", "fieldType": null, "modelAlias": null, "relatedModel": null}, {"valueKey": "NODE_OUTPUT_node_1i14m7sqf11", "valueName": "[查询数据]节点出参", "fieldType": null, "modelAlias": null, "relatedModel": {"modelKey": "bid_management_module$bid_specialists_md", "modelName": "专家库主数据"}}, {"valueKey": "id", "valueName": "ID", "fieldType": null, "modelAlias": "bid_management_module$bid_specialists_md", "relatedModel": {"modelKey": null, "modelName": null}}], "valueType": "VAR", "fieldType": "Number"}}]}}]}, {"type": "ParallelConditionNode", "id": null, "key": "node_1i14m5gkg9", "name": "并行分支2", "desc": null, "props": {"type": "ParallelConditionProperties", "errorConfigs": null, "conditionGroup": null}, "nextNodeKey": "node_1i14o2a1g1", "children": [{"type": "CascadeCreateDataNode", "id": null, "key": "node_1i14o2a1g1", "name": "新增数据", "desc": null, "props": {"type": "CascadeCreateDataProperties", "errorConfigs": null, "relatedModel": {"modelKey": "bid_management_module$bid_specialists_md", "modelName": "专家库主数据"}, "modelValue": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "REQUEST", "valueName": "服务入参", "fieldType": null, "modelAlias": null, "relatedModel": null}, {"valueKey": "expert_pool", "valueName": "专家库", "fieldType": null, "modelAlias": null, "relatedModel": {"modelKey": "bid_management_module$bid_specialists_md", "modelName": "bid_management_module$bid_specialists_md"}}], "valueType": "VAR", "fieldType": "Model"}, "outputAssign": {"outputAssignType": "SYSTEM", "customAssignments": null}}, "nextNodeKey": "node_1i14ofklo3"}, {"type": "AssignNode", "id": null, "key": "node_1i14ofklo3", "name": "赋值", "desc": null, "props": {"type": "AssignProperties", "errorConfigs": null, "assignments": [{"id": "1i14oflkn4", "field": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "GLOBAL", "valueName": "全局变量", "fieldType": null, "modelAlias": null, "relatedModel": null}, {"valueKey": "id2", "valueName": "id2", "fieldType": null, "modelAlias": null, "relatedModel": null}], "valueType": "VAR", "fieldType": "Number"}, "operator": "EQ", "value": {"type": "VarValue", "id": null, "varValue": [{"valueKey": "GLOBAL", "valueName": "全局变量", "fieldType": null, "modelAlias": null, "relatedModel": null}, {"valueKey": "NODE_OUTPUT_node_1i14o2a1g1", "valueName": "[新增数据]节点出参", "fieldType": null, "modelAlias": null, "relatedModel": {"modelKey": "bid_management_module$bid_specialists_md", "modelName": "专家库主数据"}}, {"valueKey": "id", "valueName": "ID", "fieldType": null, "modelAlias": "bid_management_module$bid_specialists_md", "relatedModel": {"modelKey": null, "modelName": null}}], "valueType": "VAR", "fieldType": "Number"}}]}}]}]}, {"id": null, "key": "node_1i14om8h37", "type": "AssignNode", "name": "赋值", "nextNodeKey": "node_1i141vl822", "props": {"nodeTitle": "赋值", "type": "AssignProperties", "assignments": [{"id": "1i14om9iu8", "field": {"type": "VarValue", "valueType": "VAR", "fieldType": "Number", "varValue": [{"valueKey": "OUTPUT", "valueName": "服务出参"}, {"valueKey": "id1", "valueName": "id1"}]}, "value": {"type": "VarValue", "valueType": "VAR", "fieldType": "Number", "varValue": [{"valueKey": "GLOBAL", "valueName": "全局变量"}, {"valueKey": "id1", "valueName": "id1"}]}}, {"id": "1i14onc5v9", "field": {"type": "VarValue", "valueType": "VAR", "fieldType": "Number", "varValue": [{"valueKey": "OUTPUT", "valueName": "服务出参"}, {"valueKey": "id2", "valueName": "id2"}]}, "value": {"type": "VarValue", "valueType": "VAR", "fieldType": "Number", "varValue": [{"valueKey": "GLOBAL", "valueName": "全局变量"}, {"valueKey": "id2", "valueName": "id2"}]}}], "nodeKey": "node_1i14om8h37"}}, {"type": "EndNode", "id": null, "key": "node_1i0kih1bg2", "name": "结束", "desc": null, "props": {"type": "EndProperties", "errorConfigs": null}}]}