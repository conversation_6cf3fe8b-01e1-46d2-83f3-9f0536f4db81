{"serviceName": "asdfasfasf", "serviceKey": "asfdasfasfd", "id": "836610485804699648", "serviceDsl": {"key": "asfdasfasfd", "name": "asdfasfasf", "type": "ServiceDefinition", "headNodeKeys": ["lfm0az2h1"], "props": {"type": "ServiceProperties"}, "children": [{"key": "lfm0az2h1", "type": "StartNode", "name": "开始", "nextNodeKey": "lfm0cmr49", "props": {"nodeTitle": "开始", "type": "StartProperties", "globalVariable": [{"fieldKey": "a", "fieldName": "a", "fieldType": "Text"}], "nodeKey": "lfm0az2h1"}}, {"key": "lfm0cmr49", "type": "UpdateDataNode", "name": "更新数据", "nextNodeKey": "lfm0fru524", "props": {"nodeTitle": "更新数据", "type": "UpdateDataProperties", "relatedModel": {"type": "BUSINESS_MODEL", "modelKey": "test_state_model", "modelName": "状态机测试模型", "teamId": 1, "appId": 1}, "conditionGroups": [[{"key": "lfm0cqs81", "type": "ConditionLeaf", "leftValue": {"key": "lfm0csih12", "type": "VarValue", "varValue": [{"valueKey": "id", "valueName": "ID"}], "valueType": "VAR", "fieldType": "Number"}, "operator": "EQ", "rightValue": {"key": "lfm0d1ek32", "type": "VarValue", "valueType": "CONST", "fieldType": "Number", "constValue": 11}}, {"key": "lfm0de70102", "type": "ConditionLeaf", "leftValue": {"key": "lfm0dee7118", "type": "VarValue", "varValue": [{"valueKey": "version", "valueName": "版本号"}], "valueType": "VAR", "fieldType": "Number"}, "operator": "EQ"}], [{"key": "lfm0d4le44", "type": "ConditionLeaf", "leftValue": {"key": "lfm0d4pk60", "type": "VarValue", "varValue": [{"valueKey": "version", "valueName": "版本号"}], "valueType": "VAR", "fieldType": "Number"}, "operator": "EQ", "rightValue": {"key": "lfm0da1g81", "type": "VarValue", "valueType": "CONST", "fieldType": "Number", "constValue": 11}}]], "inputMapping": [{"id": "lfm0dyej184"}], "nodeKey": "lfm0cmr49"}}, {"key": "lfm0fru524", "type": "AssignNode", "name": "赋值", "nextNodeKey": "lfm0az2h2", "props": {"nodeTitle": "赋值", "type": "AssignProperties", "assignments": [{"id": "lfm0ft8a214", "field": {"key": "lfm0fvtb215", "type": "VarValue", "varValue": [{"valueKey": "global", "valueName": "全局上下文"}, {"valueKey": "ServiceInnerModel_GlobalVariable", "valueName": "全局变量"}, {"valueKey": "a", "valueName": "a"}], "valueType": "VAR", "fieldType": "Text"}, "value": {"key": "lfm0fwhw216", "type": "VarValue", "valueType": "CONST", "fieldType": "Text", "constValue": "111"}}], "nodeKey": "lfm0fru524"}}, {"key": "lfm0az2h2", "type": "EndNode", "name": "结束", "props": {"nodeTitle": "结束", "type": "EndProperties"}}]}}