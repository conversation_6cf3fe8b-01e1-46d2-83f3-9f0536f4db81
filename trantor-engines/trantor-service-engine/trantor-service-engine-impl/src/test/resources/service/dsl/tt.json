{"type": "ServiceDefinition", "key": "CascadeDeleteDataNode", "name": "CascadeDeleteDataNode", "props": {"type": "ServiceProperties", "name": "CascadeDeleteDataNode", "appId": 1, "teamId": 1}, "headNodeKeys": ["start0"], "children": [{"type": "StartNode", "key": "start0", "name": "开始节点", "props": {"type": "StartProperties", "name": "开始节点", "desc": "开始节点", "input": [{"fieldType": "Model", "fieldKey": "request", "fieldName": "request", "required": true, "relatedModel": {"modelKey": "order", "modelName": "order"}}]}, "nextNodeKey": "SPINodeKey"}, {"type": "CascadeDeleteDataNode", "key": "CascadeDeleteDataNodeKey", "name": "级联删除数据", "props": {"type": "CascadeDeleteDataProperties", "name": "级联删除数据", "relatedModel": {"modelKey": "order", "modelName": "订单"}, "modelValue": {"type": "VarValue", "valueType": "VAR", "varValue": [{"valueKey": "global", "valueName": "全局上下文"}, {"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}]}}, "nextNodeKey": "end0"}, {"type": "EndNode", "key": "end0", "name": "结束节点", "props": {"type": "EndProperties"}}]}