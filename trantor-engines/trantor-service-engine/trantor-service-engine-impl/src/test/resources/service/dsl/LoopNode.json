{"type": "LoopNode", "key": "LoopNodeKey", "name": "循环创建子行", "props": {"type": "LoopProperties", "desc": "循环创建子行", "loopData": {"type": "VarValue", "fieldType": "Array", "varValue": [{"valueKey": "ServiceInnerModel_ServiceRequest", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "((this.key))", "valueName": "((this.name))"}], "relatedNode": {"nodeKey": "global", "nodeTitle": "全局上下文"}, "constValue": null, "valueType": "VAR"}, "loopElement": {"fieldKey": "item", "fieldType": "Model"}}, "headNodeKeys": null, "children": [], "renderType": null, "nextNodeKey": null}