<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>io.terminus.trantor2</groupId>
        <artifactId>trantor-service-engine</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>trantor-service-engine-impl</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <!--##################### trantor runtime api (这里只允许依赖 runtime-api) #####################-->
        <dependency>
            <artifactId>trantor-model-runtime-api</artifactId>
            <groupId>io.terminus.trantor2</groupId>
        </dependency>

        <dependency>
            <artifactId>trantor-trigger-runtime-api</artifactId>
            <groupId>io.terminus.trantor2</groupId>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-rule-engine-runtime-api</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-code-engine-runtime-api</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-connector-runtime-api</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-doc-engine-runtime-api</artifactId>
        </dependency>

        <!-- 连接器调试用 -->
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-connector-management-api</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-service-runtime-api</artifactId>
        </dependency>

        <!--##################### trantor common ######################-->
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-datasource-engine-api-common</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-service-impl-common</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-model-impl-common</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-doc-engine-api-common</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-globalreq-engine</artifactId>
        </dependency>

        <!--##################### trantor middleware #####################-->
        <dependency>
            <groupId>io.terminus.trantor.workflow</groupId>
            <artifactId>workflow-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus.erp</groupId>
            <artifactId>terminus-notice-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.terminus.erp</groupId>
                    <artifactId>erp-framework-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.terminus.trantor2</groupId>
                    <artifactId>trantor-meta-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.terminus.common</groupId>
            <artifactId>terminus-spring-boot-starter-rocketmq</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus.common</groupId>
            <artifactId>terminus-spring-boot-starter-scheduler</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus</groupId>
            <artifactId>operation-log-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus.common</groupId>
            <artifactId>terminus-spring-boot-starter-sequence</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.terminus.trantor2</groupId>
                    <artifactId>trantor-meta-runtime-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.terminus.trantor2</groupId>
                    <artifactId>trantor-rule-engine-runtime-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--##################### other #####################-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>

        <dependency>
            <groupId>com.openai</groupId>
            <artifactId>openai-java</artifactId>
        </dependency>
        <dependency>
            <groupId>io.modelcontextprotocol.sdk</groupId>
            <artifactId>mcp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.theokanning.openai-gpt3-java</groupId>
            <artifactId>service</artifactId>
        </dependency>
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-open-ai</artifactId>
        </dependency>

        <dependency>
            <groupId>com.knuddels</groupId>
            <artifactId>jtokkit</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-http</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>QLExpress</artifactId>
            <version>3.3.1</version>
        </dependency>

        <dependency>
            <groupId>io.opentelemetry</groupId>
            <artifactId>opentelemetry-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.opentelemetry</groupId>
            <artifactId>opentelemetry-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>io.opentelemetry</groupId>
            <artifactId>opentelemetry-sdk-extension-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>io.opentelemetry</groupId>
            <artifactId>opentelemetry-exporter-otlp</artifactId>
        </dependency>
        <dependency>
            <!-- Not managed by opentelemetry-bom -->
            <groupId>io.opentelemetry.semconv</groupId>
            <artifactId>opentelemetry-semconv</artifactId>
            <version>1.25.0-alpha</version>
        </dependency>

        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
        </dependency>

        <dependency>
            <groupId>io.milvus</groupId>
            <artifactId>milvus-sdk-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>fr.opensagres.xdocreport.itext-gae</groupId>
            <artifactId>itext-gae</artifactId>
            <version>4.2.0-1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-ide-impl</artifactId>
        </dependency>
    </dependencies>

</project>
