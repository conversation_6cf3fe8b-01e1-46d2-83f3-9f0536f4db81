package io.terminus.trantor2.print.task;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.common.api.model.Paging;
import io.terminus.print.api.facade.PrintSceneConsoleFacade;
import io.terminus.print.api.model.dto.PrintSceneDTO;
import io.terminus.print.api.model.request.PrintScenePageReq;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.ide.repository.PrintSceneRepo;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.model.MetaNodeAccessLevel;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskService;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.module.service.TeamService;
import io.terminus.trantor2.print.PrintDataSourceType;
import io.terminus.trantor2.print.PrintSceneMeta;
import io.terminus.trantor2.task.TaskOutput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.Optional;

/**
 * <AUTHOR>
 **/
@Slf4j
@TaskService
public class SyncPrintSceneMetaTask extends BaseTask<SyncPrintSceneMetaTask.Options> {
    @Autowired
    private PrintSceneConsoleFacade printSceneConsoleFacade;
    @Autowired
    private PrintSceneRepo printSceneRepo;
    @Autowired
    private TeamService teamService;
    @Autowired
    private MetaQueryService metaQueryService;

    private static final String DEFAULT_MODULE = "ERP_GEN";

    @Override
    public void exec(Options opts, TaskOutput output, TaskContext ctx) {
        PrintScenePageReq pagingReq = new PrintScenePageReq();
        String teamCode = ctx.getTeamCode();
        String targetModule = getTargetModule(opts);
        MetaEditAndQueryContext metaEditAndQueryContext = EditUtil.newCtx(ctx.getTeamId(), ctx.getUserId());
        if (!metaQueryService.findByKey(metaEditAndQueryContext, targetModule).isPresent()) {
            return;
        }

        int pageNo = 1;
        while (true) {
            pagingReq.setPageNo(pageNo);
            pagingReq.setPageSize(100);
            pagingReq.setSearchCount(Boolean.FALSE);

            Paging<PrintSceneDTO> paging = printSceneConsoleFacade.paging(pagingReq).getData();
            if (paging == null || CollectionUtils.isEmpty(paging.getData())) {
                break;
            }

            paging.getData().forEach(print -> {
                        try {

                            ResourceContext resourceContext = ResourceContext.newResourceCtx(teamCode, TrantorContext.getCurrentUserId());
                            Optional<PrintSceneMeta> opt = printSceneRepo.findOneByKey(KeyUtil.newKeyUnderModule(targetModule, print.getCode()), resourceContext);
                            if (!opt.isPresent()) {
                                printSceneRepo.create(initPrintScene(print, teamCode, targetModule), resourceContext);
                            }
                        } catch (Exception e) {
                            log.error("create print scene ({}) meta fail", print, e);
                        }
                    });

            pageNo++;
        }
    }

    private PrintSceneMeta initPrintScene(PrintSceneDTO print, String teamCode, String targetModule) {
        PrintSceneMeta meta = new PrintSceneMeta();
        meta.setKey(KeyUtil.newKeyUnderModule(targetModule, print.getCode()));
        meta.setName(print.getName());
        meta.setParentKey(KeyUtil.newKeyUnderModule(targetModule, "ungroup"));

        meta.setTeamId(print.getTenantId());
        meta.setTeamCode(teamCode);
        meta.setAccess(MetaNodeAccessLevel.Public);
        meta.setDescription(print.getDesc());

        PrintSceneMeta.Props props = new PrintSceneMeta.Props();
        props.setOldId(print.getId());
        props.setModelKey(print.getModelKey());
        if (StringUtils.isBlank(print.getServiceInfo())) {
            props.setDataSourceType(PrintDataSourceType.View);
        } else {
            props.setDataSourceType(PrintDataSourceType.Service);
            ObjectNode jsonNodes = ObjectJsonUtil.deserialize(print.getServiceInfo());
            props.setServiceKey(Optional.ofNullable(jsonNodes.get("serviceKey")).map(JsonNode::asText).orElse(null));
        }
        meta.setResourceProps(props);
        return meta;
    }

    private String getTargetModule(Options opts) {
        return StringUtils.defaultIfBlank(opts.getTargetModule(), DEFAULT_MODULE);
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
        private String targetModule = "ERP_GEN";
    }
}
