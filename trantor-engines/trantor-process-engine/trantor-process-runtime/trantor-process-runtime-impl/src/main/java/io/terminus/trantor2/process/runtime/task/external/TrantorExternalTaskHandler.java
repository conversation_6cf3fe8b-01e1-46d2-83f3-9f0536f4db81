package io.terminus.trantor2.process.runtime.task.external;

import io.terminus.trantor2.service.common.meta.ServiceMeta;
import io.terminus.trantor2.meta.runtime.repo.ServiceMetaRepo;
import io.terminus.trantor2.service.runtime.api.ServiceExecuteApi;
import io.terminus.trantor2.service.runtime.api.model.request.ServiceExecuteRequest;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.camunda.bpm.client.task.ExternalTask;
import org.camunda.bpm.client.task.ExternalTaskHandler;
import org.camunda.bpm.client.task.ExternalTaskService;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 基于Trantor机制实现的External Task Handler
 *
 * <AUTHOR>
 *
 */
@Component
@ExternalTaskSubscription
public class TrantorExternalTaskHandler implements ExternalTaskHandler {

    @Resource
    ServiceExecuteApi serviceExecuteApi;
    @Resource
    ServiceMetaRepo serviceMetaRepo;

    @Override
    public void execute(ExternalTask externalTask, ExternalTaskService externalTaskService) {
        //1、获取Task信息中关联的服务信息
        String serviceKey = getServiceLinkOfTask(externalTask);
        ServiceExecuteRequest serviceExecuteRequest = generateServiceExecuteRequest(serviceKey, externalTask);
        //2、执行业务逻辑，调用服务、操作事件
        //TIPS：注意这里调用的具体服务执行方法，需要和ServiceExecuteAspect切边拦截的方法保持一致；
        serviceExecuteApi.execute(serviceKey, serviceExecuteRequest);
        //TIPS：执行方法的返回参数不进行处理，统一走AOP后置拦截，发送消息到引擎侧，进行后续处理；
    }

    private String getServiceLinkOfTask(ExternalTask externalTask){
        // TIPS：如果用topicName，在SpringTopicSubscriptionImpl实例化的时候需要注意订阅多个topic（每个External Task都有一个Topic）
        return externalTask.getTopicName();
    }

    private ServiceExecuteRequest generateServiceExecuteRequest(String serviceKey, ExternalTask externalTask) {
        ServiceExecuteRequest request = new ServiceExecuteRequest();
        // bpmn文件里, 外部任务节点, 要设置入参request进行传参, teamId, appId可以考虑在流程启动时, 设为全局变量
        ServiceMeta serviceMeta = serviceMetaRepo.findOneByKey(serviceKey);
        request.setParams(externalTask.getVariable("request"));
        request.setTeamId(serviceMeta.getTeamId());
        request.setAppId(serviceMeta.getAppId());
        return request;
    }
}
