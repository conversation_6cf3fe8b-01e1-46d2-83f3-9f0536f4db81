package io.terminus.trantor2.process.console.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OverviewMetaLinkDTO implements Serializable {

    private static final long serialVersionUID = -9201113750800012114L;

    /**
     * 其他元数据Key
     */
    private String targetKey;

    /**
     * 其他元数据类型
     */
    private String targetType;

    /**
     * 概念Key
     */
    private String overviewKey;

    /**
     * 概念名称
     */
    private String overviewName;

    private List<OverviewFieldLinkDTO> fields;

}
