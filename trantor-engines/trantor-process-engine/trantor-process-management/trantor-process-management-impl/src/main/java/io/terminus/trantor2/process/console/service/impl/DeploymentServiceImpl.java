package io.terminus.trantor2.process.console.service.impl;

import io.terminus.process.engine.client.ProcessEngineClient;
import io.terminus.process.engine.dto.ProcessDeploymentDto;
import io.terminus.process.engine.dto.ServiceLinkDto;
import io.terminus.trantor2.process.console.service.ProcessAnalyzeService;
import io.terminus.trantor2.process.dto.ProcessLinkDto;
import io.terminus.trantor2.process.meta.ProcessMeta;
import io.terminus.trantor2.process.console.service.DeploymentService;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 业务流发布服务
 *
 * <AUTHOR>
 */
@Service
public class DeploymentServiceImpl implements DeploymentService {

    @Resource
    ProcessEngineClient processEngineClient;
    @Resource
    ProcessAnalyzeService analyzeService;

    @Override
    public Boolean syncProcess(ProcessMeta processMeta) {
        if (processMeta.getResourceProps().getBpmnXml() == null) {
            return Boolean.TRUE;
        }
        try {
            List<ProcessLinkDto> linkDtos = analyzeService.analyze(processMeta);

            List<ServiceLinkDto> serviceLinkDtos = new ArrayList<>();
            for (ProcessLinkDto dto : linkDtos) {
                ServiceLinkDto serviceLinkDto = new ServiceLinkDto();
                serviceLinkDto.setProcessKey(dto.getProcessKey());
                serviceLinkDto.setServiceKey(dto.getServiceKey());
                serviceLinkDto.setModelKey(dto.getModelKey());
                serviceLinkDto.setTenantId(processMeta.getTeamCode());
                serviceLinkDtos.add(serviceLinkDto);
            }

            ProcessDeploymentDto deploymentDto = new ProcessDeploymentDto();
            deploymentDto.setProcessKey(processMeta.getKey());
            deploymentDto.setBpmnXMl(processMeta.getResourceProps().getBpmnXml());
            deploymentDto.setDeploymentName(processMeta.getName());
            deploymentDto.setTenantId(processMeta.getTeamCode());
            deploymentDto.setLinkDtos(serviceLinkDtos);
            return processEngineClient.deploy(deploymentDto);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Boolean syncMultipleProcess(List<ProcessMeta> processMetas) {
        if (ObjectUtils.isEmpty(processMetas)) {
            return Boolean.TRUE;
        }
        for (ProcessMeta meta : processMetas) {
            if (!syncProcess(meta)) {
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

}
