package io.terminus.trantor2.permission.management.impl.task.snapshot;

import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskDefine;
import io.terminus.trantor2.meta.management.task.TaskService;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.repository.ModuleRepo;
import io.terminus.trantor2.task.TaskOutput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * 2024/12/25 10:32
 **/
@RequiredArgsConstructor
@TaskService(displayName = "构建角色授权快照", visible = true)
public class PermissionSnapshotRebuildTask extends BaseTask<PermissionSnapshotRebuildTask.Options> {

    private final ModuleRepo moduleRepo;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
    }

    @Override
    public void exec(Options opts, TaskOutput output, TaskContext ctx) {
        ResourceContext resourceContext = ResourceContext.newResourceCtx(ctx.getTeamCode(), ctx.getUserId());
        List<ModuleMeta> portals = moduleRepo.findAllPortalByTeam(resourceContext);
        if (CollectionUtils.isNotEmpty(portals)) {
            for (ModuleMeta portal : portals) {
                PermissionSnapshotForSinglePortalRebuildTask.Options subOpts = new PermissionSnapshotForSinglePortalRebuildTask.Options();
                subOpts.setPortalCode(portal.getKey());
                ctx.getSubTasks().add(new TaskDefine(
                        PermissionSnapshotForSinglePortalRebuildTask.class,
                        subOpts,
                        "",
                        Boolean.FALSE
                ));
            }
        }
    }
}
