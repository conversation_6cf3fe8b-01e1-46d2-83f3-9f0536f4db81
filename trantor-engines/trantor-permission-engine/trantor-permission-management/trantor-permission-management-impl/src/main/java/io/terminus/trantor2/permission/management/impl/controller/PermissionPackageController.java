package io.terminus.trantor2.permission.management.impl.controller;

import com.aliyun.openservices.shade.com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.iam.api.response.ei.ExportImportTask;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.service.ConfigurationService;
import io.terminus.trantor2.module.service.ModuleManagerQueryService;
import io.terminus.trantor2.permission.api.common.dto.PermissionPackageBeforeResponse;
import io.terminus.trantor2.permission.api.common.dto.PermissionPackageInteractionRequest;
import io.terminus.trantor2.permission.api.common.dto.PermissionPackagePagingRequest;
import io.terminus.trantor2.permission.api.common.dto.PermissionPackageTaskCreateRequest;
import io.terminus.trantor2.permission.management.api.cache.PermissionPackageCache;
import io.terminus.trantor2.permission.management.api.service.TrantorPermissionPackageService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;


@Tag(name = "权限包管理")
@RestController
@RequestMapping(path = "/api/trantor/console/permission/package")
@RequiredArgsConstructor
public class PermissionPackageController {

    private final ModuleManagerQueryService moduleService;
    private final ConfigurationService configurationService;
    private final TrantorPermissionPackageService permissionPackageService;
    private final PermissionPackageCache permissionPackageCache;

    @PostMapping("/create")
    @Operation(summary = "权限包任务创建")
    public Response<ExportImportTask> create(@RequestBody PermissionPackageTaskCreateRequest request) {
        return permissionPackageService.checkInProcessAndCreate(request);
    }

    @PostMapping("/invalidate")
    @Operation(summary = "权限包缓存清除")
    public Response<Boolean> invalidate(@RequestBody PermissionPackageTaskCreateRequest request) {
        permissionPackageCache.invalidate(request);
        return Response.ok(Boolean.TRUE);
    }

    @GetMapping("/paging")
    @Operation(summary = "权限包任务分页")
    public Response<Paging<ExportImportTask>> paging(PermissionPackagePagingRequest request) {

        if (Objects.isNull(request.getAppId())){
            ModuleMeta moduleMeta = moduleService.findByKey(request.getModuleKey());
            Long iamEndpointId = configurationService.getIamEndpointId(TrantorContext.getTeamId(), moduleMeta.getKey());
            request.setAppId(iamEndpointId.toString());
        }
        request.setProcessorCodes(Lists.newArrayList("permissionPackageDTOExportProcessor", "permissionPackageDTOImportProcessor"));
        return Response.ok(permissionPackageService.paging(request));
    }

}
