package io.terminus.trantor2.permission.management.impl.task.permission.view;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.terminus.trantor2.ide.repository.PermissionRepo;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.ViewPermissionDTO;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.exception.MetaTaskException;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskService;
import io.terminus.trantor2.meta.resource.BaseMeta;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.service.MenuConsoleQueryService;
import io.terminus.trantor2.module.util.MenuUtils;
import io.terminus.trantor2.permission.PermissionMeta;
import io.terminus.trantor2.permission.api.common.dto.ACLResourceType;
import io.terminus.trantor2.scene.config.SceneType;
import io.terminus.trantor2.scene.config.datamanager.DataManagerSceneConfig;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.constants.SceneConsts;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.scene.repo.SceneRepo;
import io.terminus.trantor2.task.TaskOutput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2025/4/15 15:21
 **/
@RequiredArgsConstructor
@TaskService(displayName = "权限授权视图(单一门户)", visible = true)
public class PermissionAuthorizationViewForSinglePortalGenerateTask extends BaseTask<PermissionAuthorizationViewForSinglePortalGenerateTask.Options> {

    private final MenuConsoleQueryService menuConsoleQueryService;
    private final SceneRepo sceneRepo;
    private final MetaQueryService metaQueryService;
    private final PermissionRepo permissionRepo;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
        private String portalCode;
    }

    @Override
    public void preCheck(Options opts, TaskContext ctx) {
        if (StringUtils.isBlank(opts.getPortalCode())) {
            throw new MetaTaskException("portalCode is required");
        }
    }

    @Override
    public String showOpts(Options opts) {
        return " (门户: " + opts.getPortalCode() + ")";
    }

    @Override
    public void exec(Options opts, TaskOutput output, TaskContext ctx) {
        output.log("portalCode: " + opts.getPortalCode());
        List<MenuMeta> menuMetaList = menuConsoleQueryService.getMenuTree(opts.getPortalCode());
        if (CollectionUtils.isNotEmpty(menuMetaList)) {
            Collection<PermTreeNode> permTreeNodeList = convertMenus(output, menuMetaList);
            output.resultData(permTreeNodeList);
        }
    }

    private Collection<PermTreeNode> convertMenus(@NotNull TaskOutput output,
                                                  @NotEmpty Collection<MenuMeta> menuMetas) {
        Map<String, SceneMeta> sceneMap = MenuUtils.recursiveGetSceneWithSingleView(metaQueryService, sceneRepo, menuMetas);
        List<PermTreeNode> treeNodeList = new ArrayList<>();
        for (MenuMeta menuMeta : menuMetas) {
            PermTreeNode treeNode = new PermTreeNode();
            treeNode.setKey(menuMeta.getKey());
            treeNode.setName(menuMeta.getLabel());
            treeNode.setDescription(ACLResourceType.Menu.name());
            if (MenuMeta.RouteType.None.equals(menuMeta.getRouteType())) {
                treeNode.setType(PermTreeNode.Type.FOLDER);
                if (CollectionUtils.isNotEmpty(menuMeta.getChildren())) {
                    treeNode.setChildren(convertMenus(output, menuMeta.getChildren()));
                }
            } else if (MenuMeta.RouteType.Scene.equals(menuMeta.getRouteType())) {
                treeNode.setType(PermTreeNode.Type.PERMISSION);
                treeNode.addPermKey(menuMeta.getPermissionKey());
                String sceneKey = menuMeta.getRouteConfig(SceneConsts.SCENE_KEY);
                if (StringUtils.isNotBlank(sceneKey)) {
                    SceneMeta sceneMeta = sceneMap.get(sceneKey);
                    if (Objects.nonNull(sceneMeta)) {
                        treeNode.setChildren(convertScene(output, sceneMeta));
                    }
                }
            } else {
                treeNode.setType(PermTreeNode.Type.PERMISSION);
                treeNode.addPermKey(menuMeta.getPermissionKey());
            }
            treeNodeList.add(treeNode);
        }
        return treeNodeList;
    }

    private Collection<PermTreeNode> convertScene(@NotNull TaskOutput output,
                                                  @NotNull SceneMeta sceneMeta) {
        if (SceneType.DATA.equals(sceneMeta.getType())) {
            DataManagerSceneConfig sceneConfig = (DataManagerSceneConfig) sceneMeta.getSceneConfig();
            if (CollectionUtils.isNotEmpty(sceneConfig.getViews())) {
                return convertViews(output, sceneConfig.getViews());
            }
        }
        return Collections.emptyList();
    }

    private Collection<PermTreeNode> convertViews(@NotNull TaskOutput output,
                                                  @NotEmpty Collection<DataManagerView> dataManagerViews) {
        List<String> viewKeys = dataManagerViews.stream().map(DataManagerView::getKey).distinct().collect(Collectors.toList());
        List<ViewPermissionDTO> viewPermissions = metaQueryService.findViewPermissions(EditUtil.ctxFromThreadLocal(), null, viewKeys);
        return convertViewPermissions(output, getAllViewPermissions(viewPermissions));
//        return convertViewPermissions(dataManagerViews, viewPermissions);
    }

    /**
     * 获取视图权限映射对象
     * <p>
     * 1. 视图权限 to 容器（非按钮）和绑定服务权限
     * 2. 按钮权限 to 绑定服务权限
     *
     * @param viewPermissions 视图权限对象
     * @return Map
     */
    private Map<String, Collection<String>> getAllViewPermissions(@NotEmpty Collection<ViewPermissionDTO> viewPermissions) {
        Map<String, Collection<String>> permKeyMap = new HashMap<>();
        for (ViewPermissionDTO viewPermission : viewPermissions) {
            permKeyMap.putIfAbsent(viewPermission.getPermissionKey(), new HashSet<>());
            if (CollectionUtils.isNotEmpty(viewPermission.getComponents())) {
                // 获取视图下容器和绑定服务（非按钮）权限
                Collection<String> containerPermKeys = getContainerPermKeys(viewPermission.getComponents());
                permKeyMap.get(viewPermission.getPermissionKey()).addAll(containerPermKeys);
                permKeyMap.get(viewPermission.getPermissionKey()).remove(viewPermission.getPermissionKey());
                // 获取按钮和绑定服务权限映射
                permKeyMap.putAll(getButtonPermMap(viewPermission.getComponents()));
            }
        }
        return permKeyMap;
    }

    private Collection<String> getContainerPermKeys(@NotEmpty Collection<ViewPermissionDTO.ComponentPermissionDTO> components) {
        Set<String> permKeys = new HashSet<>();
        for (ViewPermissionDTO.ComponentPermissionDTO component : components) {
            if (Objects.nonNull(component)) {
                if (!ACLResourceType.Button.name().equals(component.getResourceType())
                        && CollectionUtils.isNotEmpty(component.getAllPermissionKeys())) {
                    permKeys.addAll(component.getAllPermissionKeys());
                }
                if (CollectionUtils.isNotEmpty(component.getVirtualComponents())) {
                    // virtual components
                    for (ViewPermissionDTO.VirtualComponentPermissionDTO virtualComponent : component.getVirtualComponents()) {
                        if (Objects.nonNull(virtualComponent)
                                && !ACLResourceType.Button.name().equals(virtualComponent.getResourceType())
                                && CollectionUtils.isNotEmpty(virtualComponent.getAllPermissionKeys())) {
                            permKeys.addAll(virtualComponent.getAllPermissionKeys());
                        }
                    }
                }
            }
        }
        return permKeys;
    }

    private Map<String, Collection<String>> getButtonPermMap(@NotEmpty Collection<ViewPermissionDTO.ComponentPermissionDTO> components) {
        Map<String, Collection<String>> permKeyMap = new HashMap<>();
        for (ViewPermissionDTO.ComponentPermissionDTO component : components) {
            if (Objects.nonNull(component)) {
                if (ACLResourceType.Button.name().equals(component.getResourceType())
                        && StringUtils.isNotBlank(component.getCompPermissionKey())) {
                    permKeyMap.putIfAbsent(component.getCompPermissionKey(), new HashSet<>());
                    permKeyMap.get(component.getCompPermissionKey()).addAll(component.getResolvedPermissionKeys());
                    permKeyMap.get(component.getCompPermissionKey()).remove(component.getCompPermissionKey());
                }
                if (CollectionUtils.isNotEmpty(component.getVirtualComponents())) {
                    for (ViewPermissionDTO.VirtualComponentPermissionDTO virtualComponent : component.getVirtualComponents()) {
                        if (Objects.nonNull(virtualComponent)
                                && ACLResourceType.Button.name().equals(virtualComponent.getResourceType())
                                && StringUtils.isNotBlank(virtualComponent.getCompPermissionKey())) {
                            permKeyMap.putIfAbsent(virtualComponent.getCompPermissionKey(), new HashSet<>());
                            permKeyMap.get(virtualComponent.getCompPermissionKey()).addAll(virtualComponent.getResolvedPermissionKeys());
                            permKeyMap.get(virtualComponent.getCompPermissionKey()).remove(virtualComponent.getCompPermissionKey());
                        }
                    }
                }
            }
        }
        return permKeyMap;
    }

    private Collection<PermTreeNode> convertViewPermissions(@NotNull TaskOutput output,
                                                            @NotEmpty Map<String, Collection<String>> permKeyMap) {
        Set<String> permKeys = new HashSet<>(permKeyMap.keySet());
        permKeys.addAll(permKeyMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
        List<PermissionMeta> permissionMetaList = permissionRepo.findAllByKeys(permKeys, ResourceContext.ctxFromThreadLocal());
        Map<String, PermissionMeta> keyToPermissionMeta = permissionMetaList.stream().collect(Collectors.toMap(BaseMeta::getKey, Function.identity()));
        List<PermTreeNode> treeNodeList = new ArrayList<>();
        permKeyMap.forEach((mainPermKey, followPermKeys) -> {
            PermissionMeta permissionMeta = keyToPermissionMeta.get(mainPermKey);
            if (Objects.isNull(permissionMeta)) {
                output.log("PermissionMeta not found: " + mainPermKey);
            } else {
                PermTreeNode treeNode = new PermTreeNode();
                treeNode.setType(PermTreeNode.Type.PERMISSION);
                treeNode.setKey(permissionMeta.getKey());
                treeNode.setName(permissionMeta.getName());
                treeNode.setDescription(permissionMeta.getDescription());
                treeNode.addPermKey(mainPermKey);
                treeNode.addPermKeys(followPermKeys);
                treeNodeList.add(treeNode);
            }
        });
        return treeNodeList;
    }

    private Collection<PermTreeNode> convertViewPermissions(@NotEmpty Collection<DataManagerView> dataManagerViews,
                                                            @NotEmpty Collection<ViewPermissionDTO> viewPermissions) {
        List<PermTreeNode> treeNodeList = new ArrayList<>();
        Map<String, ViewPermissionDTO> viewKeyToViewPermission = viewPermissions.stream()
                .collect(Collectors.toMap(ViewPermissionDTO::getViewKey, Function.identity()));
        for (DataManagerView dataManagerView : dataManagerViews) {
            PermTreeNode treeNode = new PermTreeNode();
            treeNode.setType(PermTreeNode.Type.PERMISSION);
            treeNode.setKey(dataManagerView.getKey());
            treeNode.setName(dataManagerView.getTitle());
            treeNode.setDescription(ACLResourceType.View.name());
            ViewPermissionDTO viewPermission = viewKeyToViewPermission.get(dataManagerView.getKey());
            if (Objects.nonNull(viewPermission)) {
                treeNode.addPermKey(viewPermission.getPermissionKey());    // 视图自身权限项
                if (CollectionUtils.isNotEmpty(viewPermission.getComponents())) {
                    Collection<String> containerPermKeys = getContainerPermKeys(viewPermission.getComponents());
                    treeNode.addPermKeys(containerPermKeys); // 视图下容器（非按钮）组件权限项
                    treeNode.setChildren(convertButtons(viewPermission.getComponents()));   // 按钮权限项
                }
            }
            treeNodeList.add(treeNode);
        }
        return treeNodeList;
    }

    private Collection<PermTreeNode> convertButtons(@NotEmpty Collection<ViewPermissionDTO.ComponentPermissionDTO> components) {
        List<PermTreeNode> treeNodeList = new ArrayList<>();
        for (ViewPermissionDTO.ComponentPermissionDTO component : components) {
            if (Objects.nonNull(component)) {
                if (ACLResourceType.Button.name().equals(component.getResourceType())) {
                    PermTreeNode treeNode = new PermTreeNode();
                    treeNode.setType(PermTreeNode.Type.PERMISSION);
                    treeNode.setKey(component.getCompKey());
                    treeNode.setName(component.getCompTitle());
                    treeNode.setDescription(component.getCompName());
                    treeNode.addPermKeys(component.getAllPermissionKeys());
                    treeNodeList.add(treeNode);
                }
                if (CollectionUtils.isNotEmpty(component.getVirtualComponents())) {
                    for (ViewPermissionDTO.VirtualComponentPermissionDTO virtualComponent : component.getVirtualComponents()) {
                        if (Objects.nonNull(virtualComponent)
                                && ACLResourceType.Button.name().equals(virtualComponent.getResourceType())) {
                            PermTreeNode treeNode = new PermTreeNode();
                            treeNode.setType(PermTreeNode.Type.PERMISSION);
                            treeNode.setKey(virtualComponent.getVirtualCompFullKey());
                            treeNode.setName(virtualComponent.getVirtualCompTitle());
                            treeNode.setDescription(ACLResourceType.Button.name());
                            treeNode.addPermKeys(virtualComponent.getAllPermissionKeys());
                            treeNodeList.add(treeNode);
                        }
                    }
                }
            }
        }
        return treeNodeList;
    }

    @Data
    private static class PermTreeNode {
        private Type type;
        private String key;
        private String name;
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String description;
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private Collection<String> permissionKeys;
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private Collection<PermTreeNode> children;

        private void addPermKey(String newPermKey) {
            if (StringUtils.isNotBlank(newPermKey)) {
                if (Objects.isNull(this.permissionKeys)) {
                    this.permissionKeys = new TreeSet<>(String.CASE_INSENSITIVE_ORDER);
                }
                this.permissionKeys.add(newPermKey);
            }
        }

        private void addPermKeys(Collection<String> newPermKeys) {
            if (CollectionUtils.isNotEmpty(newPermKeys)) {
                for (String newPermKey : newPermKeys) {
                    addPermKey(newPermKey);
                }
            }
        }

        private enum Type {
            FOLDER,
            PERMISSION
        }
    }
}
