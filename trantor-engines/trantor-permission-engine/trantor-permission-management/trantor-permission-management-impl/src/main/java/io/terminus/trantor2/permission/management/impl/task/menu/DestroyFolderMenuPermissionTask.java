package io.terminus.trantor2.permission.management.impl.task.menu;

import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskDefine;
import io.terminus.trantor2.meta.resource.BaseMeta;
import io.terminus.trantor2.task.TaskOutput;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.repository.ModuleRepo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 销毁文件夹类型菜单的权限，包括元数据中的权限项、引用，和IAM的权限信息
 *
 * <AUTHOR>
 * 2024/11/28 18:47
 **/
@Component
@RequiredArgsConstructor
public class DestroyFolderMenuPermissionTask extends BaseTask<DestroyFolderMenuPermissionTask.Options> {

    private final ModuleRepo moduleRepo;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
        /**
         * 门户编号，不指定则表示所有门户
         */
        private Collection<String> portalCodes;
        /**
         * 是否需要删除IAM存储的菜单资源数据，true：删除，false：不删除，默认值：false
         */
        private boolean deleteIamMenu = false;
    }

    @Override
    public void exec(Options opts, TaskOutput output, TaskContext ctx) {
        if (CollectionUtils.isNotEmpty(opts.getPortalCodes())) {
            execForSomePortals(opts, output, ctx, opts.getPortalCodes());
        } else {
            execForAllPortals(opts, output, ctx);
        }
    }

    private void execForSomePortals(Options opts, TaskOutput output, TaskContext ctx, Collection<String> portalCodes) {
        for (String portalCode : portalCodes) {
            DestroyFolderMenuPermissionForSinglePortalTask.Options subOpts = new DestroyFolderMenuPermissionForSinglePortalTask.Options();
            subOpts.setPortalCode(portalCode);
            TaskDefine taskDefine = new TaskDefine(DestroyFolderMenuPermissionForSinglePortalTask.class, subOpts, "", false);
            ctx.getSubTasks().add(taskDefine);
        }
    }

    private void execForAllPortals(Options opts, TaskOutput output, TaskContext ctx) {
        ResourceContext resourceContext = ResourceContext.newResourceCtx(ctx.getTeamCode(), ctx.getUserId());
        List<ModuleMeta> portals = moduleRepo.findAllPortalByTeam(resourceContext);
        if (CollectionUtils.isNotEmpty(portals)) {
            Set<String> portalCodes = portals.stream().map(BaseMeta::getKey).filter(Objects::nonNull).collect(Collectors.toSet());
            execForSomePortals(opts, output, ctx, portalCodes);
        }
    }
}
