package io.terminus.trantor2.permission.management.impl.service.impl;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.ide.repository.PermissionAuthorizeViewRepo;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.service.MenuConsoleQueryService;
import io.terminus.trantor2.module.service.ModuleManagerQueryService;
import io.terminus.trantor2.permission.PermissionAuthorizeViewMeta;
import io.terminus.trantor2.permission.api.common.dto.PermissionResourceDTO;
import io.terminus.trantor2.permission.impl.common.util.PermAuthViewGenerator;
import io.terminus.trantor2.permission.props.PermissionAuthorizeViewProps;
import io.terminus.trantor2.scene.repo.SceneRepo;
import io.terminus.trantor2.scene.service.SceneConsoleQueryService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import jakarta.validation.constraints.NotNull;
import java.util.Collection;
import java.util.Objects;

/**
 * <AUTHOR>
 * 2025/3/26 20:01
 **/
@Service
@RequiredArgsConstructor
public class PermissionAuthorizationViewService {

    private final PermissionAuthorizeViewRepo permissionAuthorizeViewRepo;
    private final ModuleManagerQueryService moduleManagerQueryService;
    private final MetaQueryService metaQueryService;
    private final MenuConsoleQueryService menuConsoleQueryService;
    private final SceneConsoleQueryService sceneConsoleQueryService;
    private final SceneRepo sceneRepo;

    public PermissionAuthorizeViewMeta generateAndSaveForFuncPerm(@NotNull String portalCode) {
        ResourceContext ctx = ResourceContext.ctxFromThreadLocal();
        String key = KeyUtil.newKeyUnderModule(portalCode, "PermissionAuthorizationView");
        PermissionAuthorizeViewMeta meta = permissionAuthorizeViewRepo.findOneByKey(key, ctx).orElse(null);
        if (Objects.nonNull(meta)) {
            Collection<PermissionResourceDTO> permAuthView = PermAuthViewGenerator.generateForFuncPerm(
                    portalCode,
                    moduleManagerQueryService,
                    menuConsoleQueryService,
                    sceneConsoleQueryService,
                    metaQueryService,
                    sceneRepo);
            meta.getResourceProps().setData(JsonUtil.NON_INDENT_NON_EMPTY.toJson(permAuthView));
            permissionAuthorizeViewRepo.update(meta, ctx);
            return meta;
        } else {
            PermissionAuthorizeViewMeta permissionAuthorizeViewMeta = new PermissionAuthorizeViewMeta();
            permissionAuthorizeViewMeta.setKey(key);
            permissionAuthorizeViewMeta.setName("权限授权视图（门户：" + portalCode + "）");
            permissionAuthorizeViewMeta.setParentKey(portalCode);
            PermissionAuthorizeViewProps props = new PermissionAuthorizeViewProps();
            Collection<PermissionResourceDTO> permAuthView = PermAuthViewGenerator.generateForFuncPerm(
                    portalCode,
                    moduleManagerQueryService,
                    menuConsoleQueryService,
                    sceneConsoleQueryService,
                    metaQueryService,
                    sceneRepo);
            props.setData(JsonUtil.NON_INDENT_NON_EMPTY.toJson(permAuthView));
            permissionAuthorizeViewMeta.setResourceProps(props);
            permissionAuthorizeViewRepo.create(permissionAuthorizeViewMeta, ctx);
            return permissionAuthorizeViewMeta;
        }
    }
}
