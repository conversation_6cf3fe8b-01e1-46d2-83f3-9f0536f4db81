package io.terminus.trantor2.permission.management.impl.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.ide.dto.ResourceNodeDeleteRequest;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.page.Order;
import io.terminus.trantor2.meta.api.dto.page.PageReq;
import io.terminus.trantor2.meta.resource.ResourceBaseMeta;
import io.terminus.trantor2.permission.DataConditionMeta;
import io.terminus.trantor2.permission.PermissionPropsType;
import io.terminus.trantor2.permission.management.impl.service.PermissionMetaService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 权限元数据管理接口
 *
 * <AUTHOR>
 * 2024/5/22 11:12
 **/
@Tag(name = "权限元数据管理接口")
@RestController
@RequestMapping("/api/trantor/console/permission-meta")
@RequiredArgsConstructor
public class PermissionMetaController {

    private final PermissionMetaService permissionMetaService;

    @PostMapping("/create")
    @Operation(summary = "创建权限元数据")
    public Response<MetaTreeNodeExt> create(@RequestBody MetaTreeNodeExt permissionMeta) {
        return Response.ok(permissionMetaService.create(permissionMeta));
    }

    @PostMapping("/update")
    @Operation(summary = "更新权限元数据")
    public Response<MetaTreeNodeExt> update(@RequestBody MetaTreeNodeExt permissionMeta) {
        return Response.ok(permissionMetaService.update(permissionMeta));
    }

    @PostMapping("/delete")
    @Operation(summary = "删除权限元数据")
    public Response<Void> delete(@RequestBody ResourceNodeDeleteRequest req) {
        permissionMetaService.delete(req);
        return Response.ok();
    }

    @GetMapping("/find")
    @Deprecated
    @Operation(summary = "查询权限元数据详情列表")
    public Response<List<? extends ResourceBaseMeta<?>>> findAll(@RequestParam(required = false) String moduleKey,
                                                                 @RequestParam PermissionPropsType propsType) {
        return Response.ok(permissionMetaService.findAll(moduleKey, propsType));
    }

    @PostMapping("/find-by-keys")
    @Operation(summary = "根据 key 批量查询权限元数据详情列表")
    public Response<List<? extends ResourceBaseMeta<?>>> findByKeys(@RequestBody @Validated @NotEmpty List<String> keys,
                                                                    @RequestParam(required = false, defaultValue = "FUNCTION_ITEM") PermissionPropsType propsType) {
        return Response.ok(permissionMetaService.findByKeys(keys, propsType));
    }

    @GetMapping("/paging")
    @Operation(summary = "分页查询权限元数据详情列表")
    public Response<Paging<? extends ResourceBaseMeta<?>>> paging(@RequestParam(required = false, defaultValue = "1") Integer pageNumber,
                                                    @RequestParam(required = false, defaultValue = "100") Integer pageSize,
                                                    @RequestParam(required = false) String moduleKey,
                                                    @RequestParam PermissionPropsType propsType,
                                                    @RequestParam(required = false) String fuzzyValue) {
        PageReq pageReq = PageReq.of(pageNumber - 1, pageSize, Order.byModifiedAt().desc());
        return Response.ok(permissionMetaService.paging(pageReq, moduleKey, propsType, fuzzyValue));
    }

    @GetMapping("/data-condition/find-model-usage")
    @Operation(summary = "数据规则权限元数据-查看引用模型列表")
    public Response<List<MetaTreeNodeExt>> findModelUsageForDataCondition(@RequestParam(required = false) String moduleKey,
                                                                          @RequestParam(required = false) String parentKey) {
        return Response.ok(permissionMetaService.findModelUsageForDataCondition(moduleKey, parentKey));
    }

    @GetMapping("/data-condition/find-by-model")
    @Operation(summary = "数据规则权限元数据-根据模型查询关联数据规则列表")
    public Response<List<DataConditionMeta>> findDataConditionByModel(@RequestParam(required = false) String moduleKey,
                                                                      @RequestParam(required = false) String parentKey,
                                                                      @RequestParam String modelKey) {
        return Response.ok(permissionMetaService.findDataConditionByModel(moduleKey, parentKey, modelKey));
    }
}
