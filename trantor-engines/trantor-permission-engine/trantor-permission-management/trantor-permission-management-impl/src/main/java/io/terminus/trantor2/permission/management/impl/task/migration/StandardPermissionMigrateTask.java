package io.terminus.trantor2.permission.management.impl.task.migration;

import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.index.MetaIndexAssetRepo;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskDefine;
import io.terminus.trantor2.meta.management.task.TaskService;
import io.terminus.trantor2.meta.resource.BaseMeta;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.repository.ModuleRepo;
import io.terminus.trantor2.module.service.MenuConsoleQueryService;
import io.terminus.trantor2.permission.management.api.service.PermissionKeyInitializer;
import io.terminus.trantor2.permission.management.impl.task.authorization.PermissionAuthorizationRecoverForAllPortalTask;
import io.terminus.trantor2.permission.management.impl.task.authorization.PermissionAuthorizationRecoverTask;
import io.terminus.trantor2.permission.management.impl.task.snapshot.PermissionSnapshotRebuildTask;
import io.terminus.trantor2.scene.repo.ViewRepo;
import io.terminus.trantor2.service.management.repo.ServiceRepo;
import io.terminus.trantor2.task.TaskOutput;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2025/3/20 17:02
 **/
@TaskService(displayName = "标准化操作权限迁移任务")
public class StandardPermissionMigrateTask extends AbstractStandardPermissionMigrateTask<StandardPermissionMigrateTask.Options> {

    private final ModuleRepo moduleRepo;

    public StandardPermissionMigrateTask(MetaIndexAssetRepo metaIndexAssetRepo,
                                         ViewRepo viewRepo,
                                         ServiceRepo serviceRepo,
                                         MenuConsoleQueryService menuConsoleQueryService,
                                         ModuleRepo moduleRepo,
                                         PermissionKeyInitializer permissionKeyInitializer) {
        super(metaIndexAssetRepo, viewRepo, serviceRepo, menuConsoleQueryService, permissionKeyInitializer);
        this.moduleRepo = moduleRepo;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
        /**
         * 执行模块子任务，每个模块启动一个子任务，true: 是，false: 否
         */
        private Boolean execModuleSubTask = Boolean.TRUE;
        /**
         * 是否执行迁移，true：执行，false：不执行
         */
        private Boolean migrateOrNot = Boolean.FALSE;
    }

    @Override
    public void exec(Options opts, TaskOutput output, TaskContext ctx) {
        migrateOrNot = opts.getMigrateOrNot();

        // step1：生成角色在所有门户的权限授权数据备份
        if (migrateOrNot) {
            PermissionSnapshotRebuildTask.Options subOpts = new PermissionSnapshotRebuildTask.Options();
            ctx.getSubTasks().add(new TaskDefine(PermissionSnapshotRebuildTask.class, subOpts, "", true));
        }

        // step2：执行权限项标准化和语义化迁移
        if (Boolean.TRUE.equals(opts.execModuleSubTask)) {
            execSubTaskForAllModules(ctx);
        } else {
            execForAllModules(output, ctx);
        }

        // 基于角色权限授权数据备份恢复迁移后权限项的授权状态
        if (migrateOrNot) {
            PermissionAuthorizationRecoverForAllPortalTask.Options subOpts = new PermissionAuthorizationRecoverForAllPortalTask.Options();
            ctx.getSubTasks().add(new TaskDefine(PermissionAuthorizationRecoverTask.class, subOpts, "", false));
        }
    }

    private void execSubTaskForAllModules(TaskContext ctx) {
        ResourceContext resourceContext = ResourceContext.newResourceCtx(ctx.getTeamCode(), ctx.getUserId());
        List<ModuleMeta> moduleMetas = moduleRepo.findAll(Field.type().equal(MetaType.Module.name()), resourceContext);
        Set<String> nativeModuleKeys = moduleMetas.stream().filter(ModuleMeta::isNativeModule).map(BaseMeta::getKey).collect(Collectors.toSet());
        for (String moduleKey : nativeModuleKeys) {
            StandardPermissionMigrateForSingleModuleTask.Options subOpts = new StandardPermissionMigrateForSingleModuleTask.Options();
            subOpts.setModuleKey(moduleKey);
            subOpts.setMigrateOrNot(migrateOrNot);
            ctx.getSubTasks().add(new TaskDefine(StandardPermissionMigrateForSingleModuleTask.class, subOpts, "", false));
        }
    }

    private void execForAllModules(TaskOutput output, TaskContext ctx) {
        Collection<Result> results = new ArrayList<>();
        ResourceContext resourceContext = ResourceContext.newResourceCtx(ctx.getTeamCode(), ctx.getUserId());
        List<ModuleMeta> moduleMetas = moduleRepo.findAll(Field.type().equal(MetaType.Module.name()), resourceContext);
        for (ModuleMeta moduleMeta : moduleMetas) {
            if (moduleMeta.isPortal()) {
                results.addAll(handleMenu(output, moduleMeta.getKey()));
            } else {
                results.addAll(handleService(output, ctx, moduleMeta.getKey()));
                results.addAll(handleView(output, ctx, moduleMeta.getKey()));
            }
        }
        StandardPermissionMigrateForSingleModuleTask.handleResult(output, results);
    }
}
