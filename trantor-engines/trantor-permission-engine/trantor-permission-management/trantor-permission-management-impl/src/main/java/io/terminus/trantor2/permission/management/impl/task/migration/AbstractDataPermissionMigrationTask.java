package io.terminus.trantor2.permission.management.impl.task.migration;

import cn.hutool.core.util.IdUtil;
import io.terminus.iam.api.dto.condition.SingleCondition;
import io.terminus.iam.api.request.permission.DataPermissionFindParams;
import io.terminus.iam.api.response.permission.DataPermission;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.resource.BaseMeta;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.adapter.IamManagerAdapter;
import io.terminus.trantor2.module.meta.ModuleIamConfig;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.repository.ModuleRepo;
import io.terminus.trantor2.module.service.ConfigurationService;
import org.apache.commons.collections4.CollectionUtils;

import jakarta.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024/7/5 10:51
 **/
public abstract class AbstractDataPermissionMigrationTask<P extends BaseTask.Options> extends BaseTask<P> {

    protected final ConfigurationService configurationService;
    protected final IamManagerAdapter iamManagerAdapter;
    protected final ModuleRepo moduleRepo;

    protected AbstractDataPermissionMigrationTask(ConfigurationService configurationService,
                                                  IamManagerAdapter iamManagerAdapter, ModuleRepo moduleRepo) {
        this.configurationService = configurationService;
        this.iamManagerAdapter = iamManagerAdapter;
        this.moduleRepo = moduleRepo;
    }

    protected Collection<DataPermission> findOldDataPermissions(TaskContext ctx) {
        // 查询在指定项目下的数据权限
        Collection<DataPermission> dataPermissions = findDataPermissionsWithInTeam(ctx);
        // 补全数据权限条件对象key
        dataPermissions.forEach(dataPermission -> {
            if (CollectionUtils.isNotEmpty(dataPermission.getConditionGroups())) {
                dataPermission.getConditionGroups().forEach(this::fillConditionKeyIfNull);
            }
        });
        return dataPermissions;
    }

    private Collection<DataPermission> findDataPermissionsWithInTeam(@NotNull TaskContext ctx) {
        ResourceContext rscCtx = ResourceContext.newResourceCtx(ctx.getTeamCode(), ctx.getUserId());
        List<ModuleMeta> moduleMetas = moduleRepo.findAllPortalByTeam(rscCtx);
        Map<String, ModuleIamConfig> moduleIamConfigMap = configurationService.queryAllModuleIamConfig(ctx.getTeamId());
        Set<Long> allIamAppIdByTeamId = moduleMetas.stream().map(moduleMeta -> {
            ModuleIamConfig moduleIamConfig = moduleIamConfigMap.get(moduleMeta.getKey());
            return moduleIamConfig.getIamEndPointId();
        }).collect(Collectors.toSet());

        List<DataPermission> dataPermissions = iamManagerAdapter.findDataPermission(new DataPermissionFindParams());
        return dataPermissions.stream()
                .filter(dataPermission -> allIamAppIdByTeamId.contains(dataPermission.getEndpointId()))
                .collect(Collectors.toList());
    }

    private void fillConditionKeyIfNull(io.terminus.iam.api.dto.condition.Condition condition) {
        if (condition instanceof io.terminus.iam.api.dto.condition.ConditionGroup) {
            io.terminus.iam.api.dto.condition.ConditionGroup conditionGroup = (io.terminus.iam.api.dto.condition.ConditionGroup) condition;
            if (Objects.isNull(conditionGroup.getKey())) {
                conditionGroup.setKey(IdUtil.fastSimpleUUID());
            }
            if (CollectionUtils.isNotEmpty(conditionGroup.getConditions())) {
                conditionGroup.getConditions().forEach(this::fillConditionKeyIfNull);
            }
        } else if (condition instanceof SingleCondition) {
            SingleCondition conditionLeaf = (SingleCondition) condition;
            if (Objects.isNull(conditionLeaf.getKey())) {
                conditionLeaf.setKey(IdUtil.fastSimpleUUID());
            }
        }
    }

    /**
     * 基于旧权限生成新权限key，新权限key = 旧权限模型的模块key前缀 + $ + 旧权限key
     */
    protected String generatePermissionKey(DataPermission dataPermission) {
        String[] split = dataPermission.getResource().split("\\$");
        String moduleKey = split[0];
        return moduleKey + "$" + dataPermission.getKey();
    }

    /**
     * 移除模型不属于本地模块或者模型不存在的数据权限规则，非本地模块不允许修改，所以是无法插入成功的
     */
    protected void removeDataPermissionIfModelNotInNativeModuleOrNotFound(ResourceContext rscCtx, Collection<DataPermission> dataPermissions) {
        Set<String> modelKeys = dataPermissions.stream().map(DataPermission::getResource).collect(Collectors.toSet());
        Set<String> allModuleKeys = modelKeys.stream().map(KeyUtil::moduleKey).collect(Collectors.toSet());
        Set<String> allowedModuleKeys = moduleRepo.findAll(Field.key().in(allModuleKeys), rscCtx)
                .stream().filter(ModuleMeta::isNativeModule).map(BaseMeta::getKey).collect(Collectors.toSet());
        Map<String, Boolean> modelExistMap = metaQueryService.existKeys(EditUtil.newFrom(rscCtx), modelKeys);
        dataPermissions.removeIf(dataPermission -> {
            String modelKey = dataPermission.getResource();
            String moduleKey = KeyUtil.moduleKey(modelKey);
            // 模型不属于本地模块或者模型不存在
            return !allowedModuleKeys.contains(moduleKey)
                    || !modelExistMap.containsKey(modelKey)
                    || !Boolean.TRUE.equals(modelExistMap.get(modelKey));
        });
    }
}
