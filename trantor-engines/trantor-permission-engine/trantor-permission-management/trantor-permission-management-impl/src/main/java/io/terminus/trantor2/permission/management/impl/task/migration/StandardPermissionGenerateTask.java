package io.terminus.trantor2.permission.management.impl.task.migration;

import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskDefine;
import io.terminus.trantor2.meta.management.task.TaskService;
import io.terminus.trantor2.module.repository.ModuleRepo;
import io.terminus.trantor2.task.TaskOutput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;

import java.util.Collection;

/**
 * <AUTHOR>
 * 2025/5/12 10:22
 **/
@RequiredArgsConstructor
@TaskService(displayName = "标准化操作权限生成任务", visible = true)
public class StandardPermissionGenerateTask extends BaseTask<StandardPermissionGenerateTask.Options> {

    private final ModuleRepo moduleRepo;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
        /**
         * 需要初始化标准权限的模型范围：
         * 1. ALL_PERSISTENT：所有持久化模型
         * 2. IN_USE：使用中的模型，默认值
         * 3. SPECIFIED：指定模型
         */
        private StandardPermissionGenerateForSingleModuleTask.Scope scope = StandardPermissionGenerateForSingleModuleTask.Scope.IN_USE;
        /**
         * 重复执行任务时，针对已经存在的权限项是否允许重置操作权限信息
         */
        private Boolean allowReset = Boolean.FALSE;
    }

    @Override
    public void exec(Options opts, TaskOutput output, TaskContext ctx) {
        Collection<String> nativeModuleKeys = moduleRepo.findAllNativeModuleKeys();
        for (String nativeModuleKey : nativeModuleKeys) {
            StandardPermissionGenerateForSingleModuleTask.Options subOpts = new StandardPermissionGenerateForSingleModuleTask.Options();
            subOpts.setModuleKey(nativeModuleKey);
            subOpts.setScope(opts.getScope());
            subOpts.setAllowReset(opts.getAllowReset());
            ctx.getSubTasks().add(
                    new TaskDefine(StandardPermissionGenerateForSingleModuleTask.class, subOpts, "", false)
            );
        }
    }
}
