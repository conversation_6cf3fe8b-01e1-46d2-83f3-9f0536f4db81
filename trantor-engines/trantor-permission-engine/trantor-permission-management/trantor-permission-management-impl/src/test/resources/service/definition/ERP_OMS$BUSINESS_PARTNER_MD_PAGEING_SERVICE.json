{"type": "ServiceDefinition", "key": "ERP_OMS$BUSINESS_PARTNER_MD_PAGEING_SERVICE", "name": "渠道合作伙伴-分页查询", "props": {"type": "ServiceProperties", "transactionPropagation": "NOT_SUPPORTED", "aiService": false, "schedulerJob": {"enable": false, "execType": "TService"}, "permissionKey": "ERP_OMS$BUSINESS_PARTNER_MD_PAGEING_SERVICE_perm_ac"}, "children": [{"type": "StartNode", "key": "node_1h9a9hm2n23", "name": "开始", "props": {"type": "StartProperties", "input": [{"fieldKey": "request", "fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "elements": [{"fieldKey": "pageable", "fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable", "elements": [{"fieldKey": "conditionGroup", "fieldAlias": "conditionGroup", "fieldName": "条件组", "fieldType": "Object"}, {"fieldKey": "conditionItems", "fieldAlias": "conditionItems", "fieldName": "简化版条件组", "fieldType": "Object"}, {"fieldKey": "sortOrders", "fieldAlias": "sortOrders", "fieldName": "字段排序", "fieldType": "Array"}, {"fieldKey": "pageNo", "fieldAlias": "pageNo", "fieldName": "页码", "fieldType": "Number"}, {"fieldKey": "pageSize", "fieldAlias": "pageSize", "fieldName": "每页数量", "fieldType": "Number"}]}, {"fieldKey": "shopId", "fieldAlias": "shopId", "fieldName": "shopId", "fieldType": "Number"}]}], "output": [{"fieldKey": "data", "fieldAlias": "data", "fieldName": "data", "fieldType": "Paging", "elements": [{"fieldKey": "total", "fieldAlias": "total", "fieldName": "total", "fieldType": "Number"}, {"fieldKey": "data", "fieldAlias": "data", "fieldName": "data", "fieldType": "Array", "element": {"fieldKey": "element", "fieldAlias": "element", "fieldName": "element", "fieldType": "Model", "relatedModel": {"modelKey": "ERP_OMS$oms_business_partner_md", "modelAlias": "ERP_OMS$oms_business_partner_md", "modelName": "渠道合作伙伴"}}}]}]}, "nextNodeKey": "node_1h9aa6nhd27"}, {"type": "RetrieveDataNode", "key": "node_1h9aa6nhd27", "name": "查询数据", "props": {"type": "RetrieveDataProperties", "relatedModel": {"modelKey": "ERP_OMS$oms_business_partner_md", "modelAlias": "ERP_OMS$oms_business_partner_md", "modelName": "渠道合作伙伴"}, "dataType": "PAGING", "pageable": {"type": "VarValue", "varValue": [{"valueKey": "REQUEST", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "pageable", "valueName": "pageable"}], "valueType": "VAR", "fieldType": "Pageable"}, "conditionGroup": {"type": "ConditionGroup", "id": "h3P0iAVewaXZZyr7o9QUu", "conditions": [{"type": "ConditionGroup", "id": "b0KqdhB_LK0qYfzFX73Zs", "conditions": [{"type": "ConditionLeaf", "id": "STxOHj3subEozSXtXjrvF", "leftValue": {"type": "VarValue", "varValue": [{"valueKey": "shopId", "valueName": "店铺", "modelAlias": "ERP_OMS$oms_business_partner_md", "relatedModel": {"modelKey": "ERP_OMS$oms_shop_md", "modelAlias": "ERP_OMS$oms_shop_md", "modelName": "ERP_OMS$oms_shop_md"}}, {"valueKey": "id", "valueName": "ID", "modelAlias": "ERP_OMS$oms_shop_md"}], "valueType": "MODEL", "fieldType": "Number"}, "operator": "EQ", "rightValue": {"type": "VarValue", "varValue": [{"valueKey": "REQUEST", "valueName": "服务入参"}, {"valueKey": "request", "valueName": "request"}, {"valueKey": "shopId", "valueName": "shopId"}], "valueType": "VAR", "fieldType": "Number"}}], "logicOperator": "AND"}], "logicOperator": "OR"}, "sortOrders": [{"fieldAlias": "id", "sortType": "DESC"}], "stopWhenDataEmpty": false, "subQueryRelatedModels": [], "queryModelFields": {"modelKey": "ERP_OMS$oms_business_partner_md", "allFields": true}, "desensitized": true, "dataI18n": false, "outputAssign": {"outputAssignType": "CUSTOM", "customAssignments": [{"id": "1h9pl7l121", "field": {"type": "VarValue", "varValue": [{"valueKey": "OUTPUT", "valueName": "服务出参"}, {"valueKey": "data", "valueName": "data"}], "valueType": "VAR", "fieldType": "Paging"}, "operator": "EQ", "value": {"type": "VarValue", "varValue": [{"valueKey": "NODE_OUTPUT_node_1h9aa6nhd27", "valueName": "出参结构体"}], "valueType": "MODEL", "fieldType": "Paging"}}]}}, "nextNodeKey": "node_1h9a9hm2n24"}, {"type": "EndNode", "key": "node_1h9a9hm2n24", "name": "结束", "props": {"type": "EndProperties"}}], "input": [{"fieldKey": "request", "fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "elements": [{"fieldKey": "pageable", "fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable", "elements": [{"fieldKey": "conditionGroup", "fieldAlias": "conditionGroup", "fieldName": "条件组", "fieldType": "Object"}, {"fieldKey": "conditionItems", "fieldAlias": "conditionItems", "fieldName": "简化版条件组", "fieldType": "Object"}, {"fieldKey": "sortOrders", "fieldAlias": "sortOrders", "fieldName": "字段排序", "fieldType": "Array"}, {"fieldKey": "pageNo", "fieldAlias": "pageNo", "fieldName": "页码", "fieldType": "Number"}, {"fieldKey": "pageSize", "fieldAlias": "pageSize", "fieldName": "每页数量", "fieldType": "Number"}]}, {"fieldKey": "shopId", "fieldAlias": "shopId", "fieldName": "shopId", "fieldType": "Number"}]}], "output": [{"fieldKey": "data", "fieldAlias": "data", "fieldName": "data", "fieldType": "Paging", "elements": [{"fieldKey": "total", "fieldAlias": "total", "fieldName": "total", "fieldType": "Number"}, {"fieldKey": "data", "fieldAlias": "data", "fieldName": "data", "fieldType": "Array", "element": {"fieldKey": "element", "fieldAlias": "element", "fieldName": "element", "fieldType": "Model", "relatedModel": {"modelKey": "ERP_OMS$oms_business_partner_md", "modelAlias": "ERP_OMS$oms_business_partner_md", "modelName": "渠道合作伙伴"}}}]}]}