{"behaviors": [{"key": "AiChat", "name": "AiChat", "type": "Widget", "title": "智能助手", "authors": [{"name": "曹琛尧", "email": "<EMAIL>"}], "group": "AI助手", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "defaultProps": {"querySceneFlow": {"type": "InvokeService", "serviceKey": "t_ai_management$query_application_scene_infos"}, "newSessionFlow": {"type": "InvokeService", "serviceKey": "ai_app_build$new_session"}, "chooseHistoryFlow": {"type": "InvokeService", "serviceKey": "t_ai_management$FindSessionHistoryRecordData", "modelAlias": "t_ai_management$t_ai_session_history"}, "getHistoryFlow": {"type": "InvokeSystemService", "serviceKey": "t_ai_management$SYS_PagingDataService", "modelAlias": "t_ai_management$t_ai_session_history"}, "saveHistoryFlow": {"type": "InvokeSystemService", "serviceKey": "t_ai_management$SYS_SaveDataService", "modelAlias": "t_ai_management$t_ai_session_history"}, "deleteHistoryFlow": {"type": "InvokeSystemService", "serviceKey": "t_ai_management$SYS_DeleteDataByIdService", "modelAlias": "t_ai_management$t_ai_session_history"}, "historyModelAlias": "t_ai_management$t_ai_session_history", "getPromptRecommendFlow": {"type": "InvokeSystemService", "serviceKey": "t_ai_management$SYS_PagingDataService", "modelAlias": "t_ai_management$t_ai_user_prompt"}, "savePromptRecommendFlow": {"type": "InvokeSystemService", "serviceKey": "t_ai_management$SYS_SaveDataService", "modelAlias": "t_ai_management$t_ai_user_prompt"}, "deletePromptRecommendFlow": {"type": "InvokeSystemService", "serviceKey": "t_ai_management$SYS_DeleteDataByIdService", "modelAlias": "t_ai_management$t_ai_user_prompt"}, "promptModelAlias": "t_ai_management$t_ai_user_prompt"}, "designerProps": {"type": "formily-react", "title": "智能助手配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "i18n": {"fields": ["welcome"]}, "editable": true, "icon": "zidingyiyemian"}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "Layout", "title": "锚点目标区域组件", "authors": [{"name": "周羿风", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": true, "draggable": true, "selectable": true, "cloneable": true, "deletable": false, "displayNamePath": "label", "designerProps": {"type": "formily-react", "title": "锚点目标区域配置"}, "i18n": {"fields": []}, "editable": true}, {"key": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "type": "Layout", "title": "锚点", "authors": [{"name": "周羿风", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "锚点配置"}, "i18n": {"fields": []}, "editable": true}, {"key": "Barcode", "name": "Barcode", "type": "Widget", "title": "条码二维码", "group": "移动端组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "defaultProps": {}, "designerProps": {"type": "formily-react", "title": "条码二维码配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "i18n": {"fields": []}, "editable": true}, {"key": "BatchActions", "name": "BatchActions", "type": "Layout", "title": "按钮组", "authors": [{"name": "曹琛尧", "email": "<EMAIL>"}], "droppable": true, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "i18n": {"fields": []}, "editable": true}, {"key": "Box", "name": "Box", "type": "Layout", "title": "区块", "authors": [{"name": "周羿风", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": true, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "i18n": {"fields": []}, "defaultProps": {"paddingHorizontal": 0}, "designerProps": {"type": "schema", "title": "区块配置", "schema": {"type": "object", "properties": {"style": {"type": "object", "properties": {"paddingHorizontal": {"type": "number", "title": "水平内边距", "x-decorator": "FormItem", "x-component": "InputNumber", "x-decorator-props": {"compact": true}, "x-component-props": {"suffix": "px", "min": 0, "style": {"width": 200}}}}}}}}, "editable": true}, {"key": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "type": "Widget", "title": "按钮", "authors": [{"name": "郑文宽", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "displayNamePath": "label", "defaultProps": {}, "designerProps": {"type": "formily-react", "title": "按钮配置"}, "permissions": [{"type": "Self", "resourceType": "<PERSON><PERSON>"}], "i18n": {"fields": []}, "editable": true, "takeOverClickLayout": true}, {"key": "CardListSearchBarAction", "name": "CardListSearchBarAction", "type": "Layout", "title": "卡片列表筛选框按钮", "authors": [{"name": "杨帆", "email": "<EMAIL>"}], "droppable": true, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "i18n": {"fields": []}, "editable": true}, {"key": "CardList", "name": "CardList", "type": "Container", "title": "卡片列表", "authors": [{"name": "魏斌", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": true, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "卡片配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "i18n": {"fields": []}, "actions": [{"key": "setData", "label": "回填", "category": "fillData"}, {"key": "getData", "label": "获取数据", "category": "getData", "dataType": "Array"}, {"key": "reload", "label": "刷新", "category": "reload"}], "contextForChildren": [{"label": "选中数据", "desc": "取到表格当前选中数据集合，类型为：[id1, id2]", "value": "<PERSON><PERSON><PERSON><PERSON>", "children": ["BatchActions", "ToolbarActions"], "dataType": "Array"}, {"label": "行数据", "desc": "当前行数据", "value": "record", "children": ["RecordActions", "Fields.Field"], "dataType": "Model"}, {"label": "父行数据", "desc": "当前父行数据", "value": "parentRecord", "children": ["SubTable.*"], "dataType": "Model"}], "contextForLookup": [{"scope": "row", "relation": "children", "children": ["RecordActions", "Fields.Field", "SubTable.*"]}], "editable": true, "icon": "zidingyiyemian"}, {"key": "Card", "name": "Card", "type": "Layout", "title": "卡片", "authors": [{"name": "曹琛尧", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": true, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "defaultProps": {"showFooter": false, "padding": {"top": 12, "bottom": 12, "left": 12, "right": 12}}, "designerProps": {"type": "formily-react", "title": "卡片配置"}, "i18n": {"fields": []}, "editable": true}, {"key": "ChildViewBody", "name": "ChildViewBody", "type": "Layout", "title": "弹窗内容", "authors": [{"name": "蒋毅强", "email": "<EMAIL>"}], "droppable": true, "draggable": false, "selectable": false, "cloneable": false, "deletable": false, "interactive": false, "i18n": {"fields": []}, "editable": false}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "Container", "title": "弹窗", "authors": [{"name": "蒋毅强", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": true, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "parentAccept": ["Page"], "defaultProps": {"title": "弹窗标题", "designOpen": true, "maskClosable": false, "style": {"width": 90, "maxHeight": 80, "gap": 12}}, "designerProps": {"type": "schema", "title": "弹窗配置", "schema": {"type": "object", "properties": {"title": {"type": "string", "title": "弹窗标题", "x-decorator": "FormItem", "x-decorator-props": {"compact": true}, "x-component": "Input", "x-component-props": {"placeholder": "请输入弹窗名称", "maxLength": 50}}, "maskClosable": {"type": "boolean", "title": "点击蒙层关闭", "x-component": "Switch", "x-decorator": "FormItem", "x-decorator-props": {"compact": true}, "default": false}, "layout": {"type": "string", "title": "弹窗形式", "x-decorator-props": {"compact": true}, "x-decorator": "FormItem", "x-component": "RadioGroup", "enum": ["modal", "drawer"], "default": "drawer", "x-data-source": [{"label": "对话框", "value": "modal"}, {"label": "抽屉", "value": "drawer"}]}, "style": {"type": "object", "properties": {"width": {"type": "number", "title": "内容宽度", "x-decorator-props": {"compact": true}, "x-decorator": "FormItem", "x-component": "InputNumber", "x-component-props": {"suffix": "%", "min": 0, "max": 100, "style": {"width": 200, "placeholder": "宽度设置，为空则自适应"}}}, "maxHeight": {"type": "number", "title": "最大高度", "x-decorator": "FormItem", "x-component": "InputNumber", "x-decorator-props": {"compact": true}, "x-component-props": {"suffix": "%", "min": 30, "max": 100, "style": {"width": 200, "placeholder": "最大高度设置"}}}, "gap": {"type": "number", "title": "子元素间距", "x-decorator": "FormItem", "x-component": "InputNumber", "x-decorator-props": {"compact": true}, "x-component-props": {"suffix": "px", "min": 0, "style": {"width": 200}}}}}, "buttons": {"title": "操作按钮", "x-component": "SortableRecordActions", "x-component-props": {"name": "RecordActions", "label": "操作按钮"}}, "params": {"type": "array", "title": "参数配置", "x-decorator": "FormItem", "x-component": "InParamsConfig", "x-decorator-props": {"compact": false}}}}}, "i18n": {"fields": ["title"]}, "editable": true}, {"key": "DetailCard", "name": "DetailCard", "type": "Container", "title": "详情卡片", "authors": [{"name": "张培峰", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": true, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "defaultProps": {"items": []}, "designerProps": {"type": "formily-react", "title": "详情卡片配置"}, "i18n": {"fields": []}, "editable": true, "icon": "zidingyiyemian"}, {"key": "DetailField", "name": "DetailField", "type": "Widget", "title": "详情字段", "authors": [{"name": "曹琛尧", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "displayNamePath": "label", "defaultProps": {"label": "详情字段", "name": ""}, "designerProps": {"type": "formily-react", "title": "详情字段配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "i18n": {"fields": []}, "editable": true, "icon": "zidingyiyemian"}, {"key": "Detail", "name": "Detail", "type": "Container", "title": "详情", "authors": [{"name": "曹琛尧", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": true, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "defaultProps": {"padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}}, "designerProps": {"type": "formily-react", "title": "详情配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "i18n": {"fields": []}, "actions": [{"key": "getData", "label": "获取数据", "category": "getData", "dataType": "Model"}, {"key": "reload", "label": "刷新", "category": "reload", "dataType": "Model"}], "editable": true, "icon": "zidingyiyemian"}, {"key": "FlexDetailGroupItem", "name": "FlexDetailGroupItem", "type": "Layout", "title": "详情组元素", "authors": [{"name": "沈泽棋", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": true, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "displayNamePath": "title", "defaultProps": {"title": "详情组标题"}, "designerProps": {"type": "formily-react", "title": "详情组元素配置"}, "i18n": {"fields": []}, "editable": true}, {"key": "FlexFormGroupItem", "name": "FlexFormGroupItem", "type": "Layout", "title": "表单组元素", "authors": [{"name": "沈泽棋", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": true, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "displayNamePath": "title", "defaultProps": {"title": "表单组标题"}, "designerProps": {"type": "formily-react", "title": "表单组元素配置"}, "i18n": {"fields": []}, "editable": true}, {"key": "FooterActions", "name": "FooterActions", "type": "Layout", "title": "底部操作栏", "authors": [{"name": "曹琛尧", "email": "<EMAIL>"}], "droppable": true, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "i18n": {"fields": []}, "editable": true}, {"key": "FormCardList", "name": "FormCardList", "type": "Container", "title": "表单卡片列表", "authors": [{"name": "曹琛尧", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": true, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "表单卡片列表配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "i18n": {"fields": []}, "actions": [{"key": "fillData", "label": "回填", "category": "fillData"}, {"key": "getData", "label": "获取数据", "category": "getData", "dataType": "Array"}], "contextForChildren": [{"label": "父行数据", "desc": "当前父行数据", "value": "parentRecord", "children": ["SubTable.*"], "dataType": "Model"}], "contextForLookup": [{"scope": "row", "relation": "children", "children": ["RecordActions", "Fields.Field", "SubTable.*"]}], "editable": true, "icon": "zidingyiyemian"}, {"key": "FormField", "name": "FormField", "type": "Widget", "title": "表单字段", "authors": [{"name": "曹琛尧", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "displayNamePath": "label", "defaultProps": {"label": "表单字段", "name": ""}, "designerProps": {"type": "formily-react", "title": "表单字段配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "i18n": {"fields": []}, "editable": true, "icon": "zidingyiyemian", "takeOverClickLayout": true}, {"key": "FormGroup", "name": "FormGroup", "type": "Container", "title": "表单", "authors": [{"name": "曹琛尧", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": true, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "defaultProps": {"padding": {"top": 0, "bottom": 0, "left": 0, "right": 0}}, "designerProps": {"type": "formily-react", "title": "表单配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "i18n": {"fields": []}, "actions": [{"key": "fillData", "label": "回填", "category": "fillData"}, {"key": "getData", "label": "获取数据", "category": "getData", "dataType": "Model"}, {"key": "reload", "label": "刷新", "category": "reload"}], "contextForLookup": [{"scope": "form", "relation": "sibling"}, {"scope": "form", "relation": "children", "children": ["*"]}], "editable": true, "icon": "zidingyiyemian"}, {"key": "InfoCard", "name": "InfoCard", "type": "Widget", "title": "信息卡片", "authors": [{"name": "蒋毅强", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "defaultProps": {"title": "信息卡片", "displayMode": "list", "resizeMode": "cover", "imageWidth": 280, "imageHeight": 160}, "designerProps": {"type": "formily-react", "title": "信息卡片配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "i18n": {"fields": []}, "editable": true, "icon": "zidingyiyemian", "takeOverClickLayout": true}, {"key": "<PERSON>Footer", "name": "<PERSON>Footer", "type": "Layout", "title": "页脚", "authors": [{"name": "曹琛尧", "email": "<EMAIL>"}], "droppable": true, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "i18n": {"fields": []}, "editable": true}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "Widget", "title": "页头", "authors": [{"name": "杨帆", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": false, "defaultProps": {"showBack": true, "mode": "normal"}, "designerProps": {"type": "formily-react", "title": "页头配置"}, "i18n": {"fields": []}, "editable": true}, {"key": "Page", "name": "Page", "type": "Container", "title": "页面", "authors": [{"name": "曹琛尧", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": true, "draggable": true, "selectable": true, "cloneable": true, "deletable": false, "defaultProps": {"showHeader": true, "showFooter": true, "contentScrollable": false, "gap": 12}, "designerProps": {"type": "formily-react", "title": "页面配置"}, "i18n": {"fields": []}, "actions": [{"key": "reload", "label": "刷新", "category": "reload"}], "editable": true}, {"key": "Popover", "name": "Popover", "type": "Widget", "title": "按钮卡", "authors": [{"name": "郑文宽", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "displayNamePath": "text", "designerProps": {"type": "formily-react", "title": "按钮卡配置"}, "permissions": [{"type": "Property", "resourceType": "<PERSON><PERSON>", "target": "items", "key": "key", "label": "text"}], "i18n": {"fields": []}, "editable": true}, {"key": "RecordActions", "name": "RecordActions", "type": "Layout", "title": "按钮组", "authors": [{"name": "曹琛尧", "email": "<EMAIL>"}], "droppable": true, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "i18n": {"fields": []}, "editable": true}, {"key": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "type": "Widget", "title": "扫码", "authors": [{"name": "蒋毅强", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "扫码配置"}, "permissions": [{"type": "Self", "resourceType": "<PERSON><PERSON>"}], "i18n": {"fields": []}, "editable": true}, {"key": "Switch", "name": "Switch", "type": "Widget", "title": "开关", "authors": [{"name": "郑文宽", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "defaultProps": {}, "designerProps": {"type": "formily-react", "title": "开关配置"}, "i18n": {"fields": []}, "editable": true}, {"key": "TabItem", "name": "TabItem", "type": "Layout", "title": "页签项", "authors": [{"name": "周羿风", "email": "<EMAIL>"}], "droppable": true, "draggable": false, "selectable": false, "cloneable": true, "deletable": false, "i18n": {"fields": []}, "editable": true}, {"key": "Tabs", "name": "Tabs", "type": "Layout", "title": "页签", "authors": [{"name": "周羿风", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "页签配置"}, "i18n": {"fields": []}, "editable": true}, {"key": "TodoList", "name": "TodoList", "type": "Widget", "title": "我的待办", "authors": [{"name": "蒋毅强", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "defaultProps": {"title": "我的待办"}, "designerProps": {"type": "formily-react", "title": "我的待办配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "i18n": {"fields": []}, "editable": true, "icon": "zidingyiyemian", "takeOverClickLayout": true}, {"key": "ApprovalCenter", "name": "ApprovalCenter", "type": "Widget", "title": "审批中心", "authors": [{"name": "蒋毅强", "email": "<EMAIL>"}], "group": "移动端组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "defaultProps": {"title": "审批中心", "entries": [{"key": "waitingApproveCount", "label": "待我处理的", "isChosen": true}, {"key": "approvedCount", "label": "已处理的", "isChosen": true}, {"key": "initiatedCount", "label": "我发起的", "isChosen": true}]}, "designerProps": {"type": "formily-react", "title": "审批中心配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "i18n": {"fields": []}, "editable": true, "icon": "zidingyiyemian", "takeOverClickLayout": true}], "namespace": "base-mobile", "endpointType": "APP", "metadata": {"buildAt": "2025-02-12 13:50:47", "commitSha": "9104ff2", "branch": "feature/develop"}, "updateAt": "2025-02-12T05:51:40.441Z"}