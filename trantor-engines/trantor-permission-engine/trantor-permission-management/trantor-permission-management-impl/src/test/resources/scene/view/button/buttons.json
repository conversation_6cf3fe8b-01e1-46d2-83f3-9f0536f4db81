[{"children": [], "key": "TERP_MIGRATE$TCP72601-NsHs3tiULpD4Ndr5hOge_", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "systemActions.remove($context)()"}, "buttonKey": "button_Q1a2kY42dg4eQBM9KWxu", "confirmOn": "off", "label": "删除", "type": "default"}, "type": "Widget"}, {"children": [], "key": "AI$ai_price_elasticity_md_scene-detail-page-header-copy", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"executeLogic": "ExecuteScript", "executeScriptConfig": "navigate({ action: 'new', query: { copyId: route.recordId } })"}, "label": "复制", "permissionEnabled": false, "type": "default", "eventActions": [], "showCondition": {}}, "type": "Widget"}, {"children": [], "key": "AI$ai_price_elasticity_md_scene-detail-page-header-edit", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "Message", "message": "开始编辑"}, {"action": "PageJump", "target": "edit"}]}, "label": "编辑", "permissionEnabled": false, "type": "primary", "eventActions": [], "showCondition": {}}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$sls_so_copy-TERP_MIGRATE$sls_so-batch-actions-1-button-1", "name": "<PERSON><PERSON>", "props": {"confirmOn": "off", "eventActions": [{"actions": [{"actionId": "vmP7MMHM", "config": {"target": "new"}, "type": "PageJump"}]}], "label": "新建", "permissionEnabled": false, "showCondition": {}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "TERP_MIGRATE$FIN_CM_IB_230817-editView-footer-save", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"bindFlowConfig": {"params": [{"action": {"target": "TERP_MIGRATE$FIN_CM_IB_230817-total-config-ERP_FIN$fin_cm_ib_head_tr"}, "name": "request", "type": "action"}], "service": "ERP_FIN$IB_SUBMIT_EVENT_SERVICE"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "PageJump", "target": "list"}, {"action": "Refresh", "target": "TERP_MIGRATE$FIN_CM_IB_230817-editView"}], "executeLogic": "BindFlow"}, "label": "保存", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "AI$ai_price_elasticity_md_scene-batch-actions-1-button-1", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"endLogic": "Other", "endLogicOtherConfig": [{"action": "OpenView", "openViewConfig": {"page": {"key": "AI$ai_price_elasticity_md_scene:edit", "name": "edit", "type": "View"}, "refresh": true, "type": "Drawer"}}]}, "label": "新建", "permissionEnabled": false, "type": "primary", "eventActions": [], "showCondition": {}}, "type": "Widget"}, {"children": [], "key": "AI$ai_price_elasticity_md_scene-SmP7Q7RpHdEiPqAg3AKdk", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认统一停用吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "AI$ai_price_elasticity_md"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "AI$SYS_MasterData_MultiDisableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ["AI$ai_price_elasticity_md_scene-table-container-AI$ai_price_elasticity_md"]}, {"action": "Message", "message": "停用成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "label": "停用", "permissionEnabled": false}, "type": "Widget"}, {"children": [], "key": "AI$ai_price_elasticity_md_scene-2YHQzGwz3obsIXD-tse79", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "确认统一启用吗？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "AI$ai_price_elasticity_md"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "AI$SYS_MasterData_MultiEnableDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": ["AI$ai_price_elasticity_md_scene-table-container-AI$ai_price_elasticity_md"]}, {"action": "Message", "message": "启用成功"}], "executeLogic": "BindService"}, "disabled$": "$context.selectedKeys?.length === 0", "label": "启用", "permissionEnabled": false}, "type": "Widget"}, {"children": [], "key": "AI$ai_price_elasticity_md_scene-batch-actions-1-button-2", "name": "<PERSON><PERSON>", "props": {"actionConfig": {"beforeLogicConfig": [{"action": "Confirm", "text": "是否删除选中单据？", "type": "Modal"}], "bindServiceConfig": {"params": [{"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "AI$ai_price_elasticity_md"}, {"expression": "{ ids: selectedKeys }", "name": "request", "type": "expression"}], "service": "AI$SYS_BatchDeleteDataService"}, "endLogic": "Other", "endLogicOtherConfig": [{"action": "Refresh", "target": "AI$ai_price_elasticity_md_scene-table-container-AI$ai_price_elasticity_md"}, {"action": "Message", "level": "success", "message": "删除成功"}], "executeLogic": "BindService"}, "confirmOn": "off", "disabled$": "mode === \"design\" ? false : $context.selectedKeys?.length === 0", "isMultiple": true, "label": "删除", "permissionEnabled": false}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$sls_so_copy-TERP_MIGRATE$sls_so-editView-footer-cancel", "name": "<PERSON><PERSON>", "props": {"confirmOn": "off", "eventActions": [{"actions": [{"actionId": "ajrmLaEv", "config": {"target": "previous"}, "type": "PageJump"}]}], "label": "取消", "permissionKey": "ERP_SCM$SLS_SO_HEAD_TR:MODIFY_PERMISSION", "type": "default", "showCondition": {}}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$sls_so_copy-m10emot2Z_AbTGJtQxFQK", "name": "<PERSON><PERSON>", "props": {"confirmOn": "off", "eventActions": [{"actions": [{"actionId": "TtXN8Cg7", "config": {"text": "重新获取定价将刷新定价记录，是否确认？", "type": "Popconfirm"}, "type": "Confirm"}, {"actionId": "3R7iiIoi", "config": {"outputParams": {"expression": "data", "serviceKey": "ERP_SCM$SO_REPRICE_EVENT_SERVICE", "type": "expression"}, "params": [{"elements": [{"fieldAlias": "soCode", "fieldName": "单据编号", "fieldType": "TEXT"}, {"fieldAlias": "soDocDate", "fieldName": "单据日期", "fieldType": "DATE"}, {"fieldAlias": "soExtCode", "fieldName": "外部单据号", "fieldType": "TEXT"}, {"fieldAlias": "baseCurrId", "fieldName": "本位币种", "fieldType": "OBJECT"}, {"fieldAlias": "slsCurrId", "fieldName": "单据币种", "fieldType": "OBJECT"}, {"fieldAlias": "exchRate", "fieldName": "汇率", "fieldType": "NUMBER"}, {"fieldAlias": "grossBaseAmt", "fieldName": "单据币含税金额", "fieldType": "NUMBER"}, {"fieldAlias": "netBaseAmt", "fieldName": "单据币不含税金额", "fieldType": "NUMBER"}, {"fieldAlias": "taxAmt", "fieldName": "税额", "fieldType": "NUMBER"}, {"fieldAlias": "soCreateSource", "fieldName": "创建来源", "fieldType": "TEXT"}, {"fieldAlias": "slsOrgId", "fieldName": "销售组织", "fieldType": "OBJECT"}, {"fieldAlias": "slsDcId", "fieldName": "销售渠道", "fieldType": "OBJECT"}, {"fieldAlias": "slsTmId", "fieldName": "销售组", "fieldType": "OBJECT"}, {"fieldAlias": "soTypeId", "fieldName": "单据类型", "fieldType": "OBJECT"}, {"fieldAlias": "custId", "fieldName": "客户", "fieldType": "OBJECT"}, {"fieldAlias": "cust<PERSON><PERSON><PERSON><PERSON>", "fieldName": "客户联系人", "fieldType": "TEXT"}, {"fieldAlias": "custPhone", "fieldName": "客户联系电话", "fieldType": "TEXT"}, {"fieldAlias": "slsPersonName", "fieldName": "销售方联系人", "fieldType": "TEXT"}, {"fieldAlias": "slsPhone", "fieldName": "销售方联系电话", "fieldType": "TEXT"}, {"fieldAlias": "soItems", "fieldName": "订单项目行", "fieldType": "Array"}, {"fieldAlias": "slsPartnerLinks", "fieldName": "相关方", "fieldType": "Array"}, {"fieldAlias": "slsTextLinks", "fieldName": "文本", "fieldType": "Array"}, {"fieldAlias": "slsAttachmentLinks", "fieldName": "附件", "fieldType": "Array"}, {"fieldAlias": "soStatus", "fieldName": "单据状态", "fieldType": "ENUM"}, {"fieldAlias": "approveStatus", "fieldName": "审批状态", "fieldType": "ENUM"}, {"fieldAlias": "soTitle", "fieldName": "单据标题", "fieldType": "TEXT"}, {"fieldAlias": "soDesc", "fieldName": "单据描述", "fieldType": "TEXT"}, {"fieldAlias": "slsInvId", "fieldName": "库存组织", "fieldType": "OBJECT"}, {"fieldAlias": "isFixedExchRate", "fieldName": "是否固定汇率", "fieldType": "BOOL"}, {"fieldAlias": "slsComId", "fieldName": "公司组织", "fieldType": "OBJECT"}, {"fieldAlias": "addrId", "fieldName": "客户地址", "fieldType": "OBJECT"}, {"fieldAlias": "priceIdempotent", "fieldName": "定价幂等键", "fieldType": "TEXT"}, {"fieldAlias": "id", "fieldName": "ID", "fieldType": "NUMBER"}, {"fieldAlias": "created<PERSON>y", "fieldName": "创建人", "fieldType": "OBJECT"}, {"fieldAlias": "updatedBy", "fieldName": "更新人", "fieldType": "OBJECT"}, {"fieldAlias": "createdAt", "fieldName": "创建时间", "fieldType": "DATE"}, {"fieldAlias": "updatedAt", "fieldName": "更新时间", "fieldType": "DATE"}, {"fieldAlias": "version", "fieldName": "版本号", "fieldType": "NUMBER"}, {"fieldAlias": "deleted", "fieldName": "逻辑删除标识", "fieldType": "NUMBER"}, {"fieldAlias": "originOrgId", "fieldName": "所属组织", "fieldType": "NUMBER"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "ERP_SCM$sls_so_head_tr", "required": true, "valueConfig": {"action": {"selector": "", "target": "ERP_SCM$sls_so_copy-TERP_MIGRATE$sls_so-total-config-TERP_MIGRATE$sls_so_head_tr"}, "type": "action"}}], "service": "ERP_SCM$SO_REPRICE_EVENT_SERVICE"}, "type": "BindService"}, {"actionId": "MkNtn4D8", "config": {"target": "ERP_SCM$sls_so_copy-TERP_MIGRATE$sls_so-total-config-TERP_MIGRATE$sls_so_head_tr"}, "type": "FillData"}]}], "label": "重新定价", "permissionKey": "ERP_SCM$sls_so_copy-edit_perm_ac_z_3_1", "showCondition": {}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$sls_so_copy-TERP_MIGRATE$sls_so-editView-footer-save", "name": "<PERSON><PERSON>", "props": {"confirmOn": "off", "eventActions": [{"actions": [{"actionId": "354ypUdH", "config": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "ERP_SCM$sls_so_head_tr", "valueConfig": {"action": {"name": "getData", "target": "ERP_SCM$sls_so_copy-TERP_MIGRATE$sls_so-total-config-TERP_MIGRATE$sls_so_head_tr"}, "type": "action"}}], "service": "ERP_SCM$SO_CREATE_SAVE_EVENT_NEW_SERVICE"}, "type": "BindService"}, {"actionId": "YagtXWqI", "config": {"message": "订单保存成功"}, "type": "Message"}, {"actionId": "welV0_nf", "config": {"openViewConfig": {"page": {"key": "ERP_SCM$sls_so_copy-list", "name": "list", "type": "View"}, "type": "NewPage"}}, "type": "OpenView"}, {"actionId": "wBUua9yZ", "config": {"target": ["ERP_SCM$sls_so_copy-TERP_MIGRATE$sls_so-editView"]}, "type": "Refresh"}]}], "label": "保存", "permissionKey": "ERP_SCM$SLS_SO_HEAD_TR:MODIFY_PERMISSION", "showCondition": {}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$sls_so_copy-mdjGG36iWfx9NwcXdXdT_", "name": "<PERSON><PERSON>", "props": {"confirmOn": "off", "eventActions": [{"actions": [{"actionId": "koAsjopk", "config": {"text": "是否确认？", "type": "Popconfirm"}, "type": "Confirm"}, {"actionId": "sKC0tXdv", "config": {"params": [{"elements": [{"fieldAlias": "soCode", "fieldName": "单据编号", "fieldType": "TEXT"}, {"fieldAlias": "soDocDate", "fieldName": "单据日期", "fieldType": "DATE"}, {"fieldAlias": "soExtCode", "fieldName": "外部单据号", "fieldType": "TEXT"}, {"fieldAlias": "baseCurrId", "fieldName": "本位币种", "fieldType": "OBJECT"}, {"fieldAlias": "slsCurrId", "fieldName": "单据币种", "fieldType": "OBJECT"}, {"fieldAlias": "exchRate", "fieldName": "汇率", "fieldType": "NUMBER"}, {"fieldAlias": "grossBaseAmt", "fieldName": "单据币含税金额", "fieldType": "NUMBER"}, {"fieldAlias": "netBaseAmt", "fieldName": "单据币不含税金额", "fieldType": "NUMBER"}, {"fieldAlias": "taxAmt", "fieldName": "税额", "fieldType": "NUMBER"}, {"fieldAlias": "soCreateSource", "fieldName": "创建来源", "fieldType": "TEXT"}, {"fieldAlias": "slsOrgId", "fieldName": "销售组织", "fieldType": "OBJECT"}, {"fieldAlias": "slsDcId", "fieldName": "销售渠道", "fieldType": "OBJECT"}, {"fieldAlias": "slsTmId", "fieldName": "销售组", "fieldType": "OBJECT"}, {"fieldAlias": "soTypeId", "fieldName": "单据类型", "fieldType": "OBJECT"}, {"fieldAlias": "custId", "fieldName": "客户", "fieldType": "OBJECT"}, {"fieldAlias": "cust<PERSON><PERSON><PERSON><PERSON>", "fieldName": "客户联系人", "fieldType": "TEXT"}, {"fieldAlias": "custPhone", "fieldName": "客户联系电话", "fieldType": "TEXT"}, {"fieldAlias": "slsPersonName", "fieldName": "销售方联系人", "fieldType": "TEXT"}, {"fieldAlias": "slsPhone", "fieldName": "销售方联系电话", "fieldType": "TEXT"}, {"fieldAlias": "soItems", "fieldName": "订单项目行", "fieldType": "Array"}, {"fieldAlias": "slsPartnerLinks", "fieldName": "相关方", "fieldType": "Array"}, {"fieldAlias": "slsTextLinks", "fieldName": "文本", "fieldType": "Array"}, {"fieldAlias": "slsAttachmentLinks", "fieldName": "附件", "fieldType": "Array"}, {"fieldAlias": "soStatus", "fieldName": "单据状态", "fieldType": "ENUM"}, {"fieldAlias": "approveStatus", "fieldName": "审批状态", "fieldType": "ENUM"}, {"fieldAlias": "soTitle", "fieldName": "单据标题", "fieldType": "TEXT"}, {"fieldAlias": "soDesc", "fieldName": "单据描述", "fieldType": "TEXT"}, {"fieldAlias": "soNotes", "fieldName": "退款原因", "fieldType": "TEXT"}, {"fieldAlias": "slsInvId", "fieldName": "库存组织", "fieldType": "OBJECT"}, {"fieldAlias": "isFixedExchRate", "fieldName": "是否固定汇率", "fieldType": "BOOL"}, {"fieldAlias": "slsComId", "fieldName": "公司组织", "fieldType": "OBJECT"}, {"fieldAlias": "addrId", "fieldName": "客户地址", "fieldType": "OBJECT"}, {"fieldAlias": "priceIdempotent", "fieldName": "定价幂等键", "fieldType": "TEXT"}, {"fieldAlias": "freezeType", "fieldName": "是否交货冻结", "fieldType": "ENUM"}, {"fieldAlias": "soAssociationCode", "fieldName": "关联单号", "fieldType": "TEXT"}, {"fieldAlias": "isRev", "fieldName": "是否逆向单", "fieldType": "BOOL"}, {"fieldAlias": "soBusinessStatus", "fieldName": "业务状态", "fieldType": "ENUM"}, {"fieldAlias": "id", "fieldName": "ID", "fieldType": "NUMBER"}, {"fieldAlias": "created<PERSON>y", "fieldName": "创建人", "fieldType": "OBJECT"}, {"fieldAlias": "updatedBy", "fieldName": "更新人", "fieldType": "OBJECT"}, {"fieldAlias": "createdAt", "fieldName": "创建时间", "fieldType": "DATE"}, {"fieldAlias": "updatedAt", "fieldName": "更新时间", "fieldType": "DATE"}, {"fieldAlias": "version", "fieldName": "版本号", "fieldType": "NUMBER"}, {"fieldAlias": "deleted", "fieldName": "逻辑删除标识", "fieldType": "NUMBER"}, {"fieldAlias": "originOrgId", "fieldName": "所属组织", "fieldType": "NUMBER"}], "fieldAlias": "so_head", "fieldName": "销售订单抬头", "fieldType": "Model", "modelAlias": "ERP_SCM$sls_so_head_tr", "required": null, "valueConfig": {"action": {"selector": "", "target": "ERP_SCM$sls_so_copy-TERP_MIGRATE$sls_so-total-config-TERP_MIGRATE$sls_so_head_tr"}, "type": "action"}}], "service": "ERP_SCM$SO_CREATE_SUBMIT_SERVICE"}, "type": "BindService"}, {"actionId": "I_YWO8w2", "config": {"message": "订单提交成功"}, "type": "Message"}, {"actionId": "gDqj7QzL", "config": {"target": ["ROOT"]}, "type": "Close"}, {"actionId": "MEohIwcj", "config": {"openViewConfig": {"page": {"key": "ERP_SCM$sls_so_copy-list", "name": "list", "type": "View"}, "type": "NewPage"}}, "type": "OpenView"}, {"actionId": "zUWHDHE4", "config": {"target": ["ERP_SCM$sls_so_copy-TERP_MIGRATE$sls_so-editView"]}, "type": "Refresh"}]}], "label": "提交", "permissionKey": "ERP_SCM$sls_so_copy-edit_perm_ac_z_3_3", "showCondition": {}, "type": "primary"}, "type": "Widget"}, {"children": [], "key": "ERP_GEN$GEN_CATE_VIEW-ERP_GEN$GEN_CATE-tree-deleteBtn", "name": "<PERSON><PERSON>", "props": {"confirmOn": "off", "eventActions": [{"actions": [{"actionId": "1edIz1Jq", "config": {"text": "确认删除吗？", "type": "Modal"}, "type": "Confirm"}, {"actionId": "PPYbaD7U", "config": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_GEN$gen_mat_cate_md"}}, {"fieldAlias": "request", "fieldName": "request", "fieldType": "Text", "valueConfig": {"expression": "{ id: primaryId }", "name": "request", "type": "expression"}}], "service": "ERP_GEN$SYS_DeleteDataByIdService"}, "type": "BindService"}, {"actionId": "NVteQOxa", "config": {"target": "ERP_GEN$GEN_CATE_VIEW-ERP_GEN$GEN_CATE-tree"}, "type": "Refresh"}, {"actionId": "LOukdfzG", "config": {"target": "list"}, "type": "PageJump"}, {"actionId": "jJnhWJJe", "config": {"message": "删除成功"}, "type": "Message"}]}], "label": "删除", "permissionKey": "ERP_GEN$GEN_MAT_CATE_MD:DELETE_PERMISSION", "type": "default", "showCondition": {}}, "type": "Widget"}, {"children": [], "key": "ERP_SCM$sls_so_copy-TERP_MIGRATE$sls_so-record-actions-1-button-1", "name": "<PERSON><PERSON>", "props": {"confirmOn": "off", "eventActions": [{"actions": [{"actionId": "hWLhCHIV", "config": {"target": "show"}, "type": "PageJump"}]}], "label": "查看", "permissionKey": "ERP_SCM$SLS_SO_HEAD_TR:VIEW_PERMISSION", "showCondition": {}, "type": "default"}}, {"children": [], "key": "test111$test_scene_05-detail-page-header-detail-switch-ids-btn", "name": "SwitchIdsButton", "props": {"buttonType": "default", "downLabel": "下一", "label": "翻页按钮", "permissionKey": "ERP_SCM$PUR_PR_HEAD_TR:VIEW_PERMISSION", "type": "default", "upLabel": "上一", "showCondition": {}}, "type": "Widget"}, {"children": [], "key": "test111$test_scene_05-detail-page-header-back-list", "name": "<PERSON><PERSON>", "props": {"buttonType": "default", "confirmOn": "off", "eventActions": [{"actions": [{"actionId": "23ads", "config": {"openViewConfig": {"page": {"key": "test111$test_scene_05:list", "name": "list", "type": "View"}, "params": [], "refresh": true, "type": "NewPage"}}, "type": "OpenView"}]}], "label": "回列表", "permissionKey": "ERP_SCM$PUR_PR_HEAD_TR:VIEW_PERMISSION", "showCondition": {}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "test111$test_scene_05-detail-page-header-enable", "name": "<PERSON><PERSON>", "props": {"eventActions": [{"actions": [{"actionId": "Confirm1", "config": {"text": "确认启用吗？", "type": "Popconfirm"}, "type": "Confirm"}, {"actionId": "service1", "actionName": "启用服务", "config": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_SCM$pur_pr_head_tr"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Text", "valueConfig": {"expression": "route.recordId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "ERP_SCM$SYS_MasterData_EnableDataService", "serviceName": "ERP_SCM$SYS_MasterData_EnableDataService"}, "type": "BindService"}, {"actionId": "Refresh223", "config": {"target": ["test111$test_scene_05-detail"]}, "type": "Refresh"}, {"actionId": "Message23", "config": {"level": "success", "message": "启用成功"}, "type": "Message"}]}], "label": "启用", "permissionKey": "ERP_SCM$PUR_PR_HEAD_TR:ENABLE_PERMISSION", "showCondition": {"conditions": [{"conditions": [{"leftValue": {"fieldType": "Enum", "scope": "form", "title": "status", "type": "VarValue", "val": "status", "value": "ERP_SCM$pur_pr_head_tr.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "NEQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "VarValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "logicOperator": "AND", "type": "ConditionGroup"}], "logicOperator": "OR", "type": "ConditionGroup"}}, "type": "Widget"}, {"children": [], "key": "test111$test_scene_05-detail-page-header-copy", "name": "<PERSON><PERSON>", "props": {"eventActions": [{"actions": [{"actionId": "odasdsa", "config": {"openViewConfig": {"page": {"key": "test111$test_scene_05:edit", "name": "edit", "type": "View"}, "params": [{"expression": "route.recordId", "name": "copyId", "type": "expression"}], "refresh": true, "type": "NewPage"}}, "type": "OpenView"}]}], "label": "复制", "permissionKey": "ERP_SCM$PUR_PR_HEAD_TR:CREATE_PERMISSION"}, "type": "Widget"}, {"children": [], "key": "test111$test_scene_05-detail-page-header-edit", "name": "<PERSON><PERSON>", "props": {"eventActions": [{"actions": [{"actionId": "odasds23a", "config": {"openViewConfig": {"page": {"key": "test111$test_scene_05:edit", "name": "edit", "type": "View"}, "params": [{"expression": "route.recordId", "name": "recordId", "type": "expression"}], "refresh": true, "type": "NewPage"}}, "type": "OpenView"}]}], "label": "编辑", "permissionKey": "ERP_SCM$PUR_PR_HEAD_TR:MODIFY_PERMISSION", "type": "primary"}, "type": "Widget"}, {"children": [], "key": "test111$test_scene_05-detail-ERP_SCM$pur_pr_head_tr-approval-record", "name": "ApprovalInstanceBtn", "props": {"ApprovalInstanceBtnCancelProps": {"cancelFlow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_CANCEL_POST", "type": "InvokeService"}}, "ApprovalInstanceBtnContainerProps": {"customCandidateFlow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_START_WITH_CUSTOMIZED_CANDIDATE_POST", "type": "InvokeService"}, "getApprovalListByBizIdFlow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_INSTANCE_LIST_GET", "type": "InvokeService"}, "getApprovalStepsFlow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_INSTANCE_GET", "type": "InvokeService"}, "remarkFlow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_INSTANCE_ADD_COMMENT_POST", "type": "InvokeService"}}, "ApprovalInstanceBtnUrgeProps": {"urgeFlow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_TASK_REMIND_POST", "type": "InvokeService"}}, "ApprovalRecordBtnCancelServiceProps": {"permissionKey": "ERP_SCM$PUR_PR_HEAD_TR:APPROVAL_PERMISSION"}, "ApprovalRecordBtnUrgeServiceProps": {"permissionKey": "ERP_SCM$PUR_PR_HEAD_TR:APPROVAL_PERMISSION"}, "label": "审批记录"}, "type": "Widget"}, {"children": [], "key": "test111$test_scene_05-detail-ERP_SCM$pur_pr_head_tr-print", "name": "Print", "props": {"getPrintCertificateFlow": {"serviceKey": "sys_common$API_PRINT_PRINT_SECRET_KEY_GET_CERTIFICATE_POST", "type": "InvokeService"}, "getPrintSceneInstanceDataFlow": {"serviceKey": "sys_common$API_PRINT_FIND_ALL_MATCHED_PRINT_SCENE_INSTANCE_POST", "type": "InvokeService"}, "getPrintScenesMetaDataFlow": {"serviceKey": "sys_common$API_TRANTOR_PORTAL_META_LIST_PRINT_SCENE_GET", "type": "InvokeService"}, "getPrintSignatureFlow": {"serviceKey": "sys_common$API_PRINT_PRINT_SECRET_KEY_GET_SIGN_POST", "type": "InvokeService"}, "label": "打印", "modelAlias": "ERP_SCM$pur_pr_head_tr", "permissionKey": "ERP_SCM$PUR_PR_HEAD_TR:PRINT_PERMISSION", "printServiceProps": {"getAliasFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor/struct-node/find-by-alias"}, "getApprovalRecordsFlow": {"serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_PRINT_WORKFLOW_RECORD_POST", "type": "InvokeService"}, "getWaterMarkVariableKeysFlow": {"serviceKey": "sys_common$API_PRINT_LIST_WATER_MARK_ALLOW_VARIABLE_KEY_POST", "type": "InvokeService"}, "matchPrintSceneInstanceFlow": {"serviceKey": "sys_common$API_PRINT_MATCH_PRINT_SCENE_INSTANCE_POST", "type": "InvokeService"}, "previewHtmlFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/print/html-preview"}, "previewOfficeFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/print/officePreview"}, "previewPrintFlow": {"serviceKey": "sys_common$API_PRINT_PREVIEW_POST", "type": "InvokeService"}, "previewWaterMarkFlow": {"serviceKey": "sys_common$API_PRINT_WATER_MARK_PREVIEW_POST", "type": "InvokeService"}}}, "type": "Widget"}]