{"children": [{"children": [{"children": [{"children": [], "key": "ERP_OMS$oms_category_view-1Cpc9_AoxmZHajMgAqf22", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_OMS$oms_category_view-4Jb-8hxDXFWU6pVp9DY8d", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_OMS$oms_category_view-L2ATHI-2cFvrX3zhFyPIv", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "ERP_OMS$oms_category_view-sY5mOR2Bxv_hee8xEu4CG", "name": "ActionsGroup", "props": {"params": []}, "type": "Layout"}], "key": "ERP_OMS$oms_category_view-list-header", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_OMS$oms_category_view-list-title", "name": "Page<PERSON><PERSON>le", "props": {"title": "渠道类目"}, "type": "Meta"}, {"children": [{"children": [{"children": [], "key": "ERP_OMS$oms_category_view-d4HGcMImKhFJG0YrpwzWh", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_OMS$oms_category_view-asKAzoB3Y2ipszAAHhPGr", "name": "<PERSON><PERSON>", "props": {"buttonType": "default", "confirmOn": "off", "eventActions": [{"actions": [{"actionId": "actionjwoKibjU", "config": {"openViewConfig": {"page": {"key": "ERP_OMS$oms_category_view-neHuZNLDKv_1TQqkqFl_l", "name": "类目拉取 (窗口)", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "params": [], "refresh": true, "type": "NewPage"}}, "type": "OpenView"}]}], "label": "拉取类目", "permissionKey": "ERP_OMS$oms_category_view:list_perm_ac_z_2_0_1_0", "showCondition": {}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-27gTDV6Sf-MNYYn39OXHt", "name": "<PERSON><PERSON>", "props": {"buttonType": "default", "confirmOn": "off", "eventActions": [{"actions": [{"actionId": "actionEevgNGVa", "config": {"openViewConfig": {"page": {"key": "ERP_OMS$oms_category_view-v2U5KesF83WSCqBHHMSCC", "name": "类目同步 (窗口)", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "params": [], "refresh": true, "type": "NewPage"}}, "type": "OpenView"}]}], "label": "同步类目", "permissionKey": "ERP_OMS$oms_category_view:list_perm_ac_z_2_0_1_1", "showCondition": {}, "type": "default"}, "type": "Widget"}], "key": "ERP_OMS$oms_category_view-UQvOejxWcUR2ClS3tHy3r", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}, {"children": [], "key": "ERP_OMS$oms_category_view-FwsJ02JpP9jMPkiMqM9Mv", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "ERP_OMS$oms_category_view-batch-actions-1", "name": "BatchActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_OMS$oms_category_view-record-actions-1-button-edit", "name": "<PERSON><PERSON>", "props": {"eventActions": [{"actions": [{"actionId": "actionlPiSI0ye", "config": {"openViewConfig": {"mask": true, "maskClosable": true, "page": {"key": "ERP_OMS$oms_category_view:edit", "name": "detail", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "type": "expression"}], "placement": "right", "refresh": true, "size": {"width": 720}, "type": "Drawer"}}, "type": "OpenView"}]}], "label": "编辑", "permissionKey": "ERP_OMS$OMS_CATEGORY_MD:MODIFY_PERMISSION", "showCondition": {}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-record-actions-1-button-delete", "name": "<PERSON><PERSON>", "props": {"eventActions": [{"actions": [{"actionId": "actionFU7Yl3Ye", "config": {"text": "确认删除吗？", "type": "Popconfirm"}, "type": "Confirm"}, {"actionId": "actionmPXfMOAm", "config": {"params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_OMS$oms_category_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "record?.id", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "service": "ERP_OMS$SYS_DeleteDataByIdService"}, "type": "BindService"}, {"actionId": "actionkU9BAtbF", "config": {"target": ["ERP_OMS$oms_category_view-table-container-ERP_OMS$oms_category_md"]}, "type": "Refresh"}, {"actionId": "actionu_qrVSLA", "config": {"level": "success", "message": "删除成功!"}, "type": "Message"}]}], "label": "删除", "permissionKey": "ERP_OMS$OMS_CATEGORY_MD:DELETE_PERMISSION", "showCondition": {}, "type": "default"}, "type": "Widget"}], "key": "ERP_OMS$oms_category_view-record-actions-1", "name": "RecordActions", "props": {}, "type": "Layout"}, {"children": [{"children": [{"children": [], "key": "ERP_OMS$oms_category_view-list-ERP_OMS$oms_category_md-logs", "name": "Logs", "props": {"label": "日志", "logsServiceProps": {"getLogsFlow": {"serviceKey": "sys_common$API_OPLOG_ADMIN_PAGING_LOG_POST", "type": "InvokeService"}}}, "type": "Widget"}], "key": "ERP_OMS$oms_category_view-uzdiBsTjw0GMRCgDcnOVc", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "ERP_OMS$oms_category_view-toolbar-actions-1", "name": "ToolbarActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_OMS$oms_category_view-uIWR7nE_yXS1304piNW6q", "name": "ActionsGroupItem", "props": {}, "type": "Layout"}], "key": "ERP_OMS$oms_category_view-EihRwt7WT2Dhtc2Xwu234", "name": "TableTitleActions", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_OMS$oms_category_view-_N1_tfAtdTGZaluGTlDC-", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "platformCategoryCode", "parentModelAlias": "ERP_OMS$oms_category_md", "placeholder": "请输入"}, "displayComponentProps": {"alinkConfig": {"enable": true, "eventActions": [{"actions": [{"actionId": "9ca19ce8", "config": {"openViewConfig": {"mask": true, "maskClosable": true, "page": {"key": "ERP_OMS$oms_category_view:detail", "name": "detail", "type": "View"}, "params": [{"expression": "record?.id", "name": "recordId", "type": "expression"}], "placement": "right", "refresh": true, "size": {"width": 720}, "type": "Drawer"}}, "type": "OpenView"}]}]}, "copyable": false}, "displayComponentType": "Text", "editComponentProps": {}, "hidden": false, "label": "渠道类目编码", "name": "platformCategoryCode", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-lXm5XVb5xnS0z_g4zBhvO", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "shopCode", "parentModelAlias": "ERP_OMS$oms_category_md", "placeholder": "请输入"}, "displayComponentProps": {"copyable": false}, "displayComponentType": "Text", "editComponentProps": {}, "hidden": false, "label": "店铺编码", "name": "shopCode", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-RnGUjY5exY8Q7VRhQ1Jrr", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "channelCode", "parentModelAlias": "ERP_OMS$oms_category_md", "placeholder": "请输入"}, "displayComponentProps": {"copyable": false}, "displayComponentType": "Text", "editComponentProps": {}, "hidden": false, "label": "渠道编码", "name": "channelCode", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-myCXJtlz5wId0SFjgw2vi", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "platformParentCategoryCode", "parentModelAlias": "ERP_OMS$oms_category_md", "placeholder": "请输入"}, "displayComponentProps": {"copyable": false}, "displayComponentType": "Text", "editComponentProps": {}, "hidden": false, "label": "渠道上级类目编码", "name": "platformParentCategoryCode", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-yvYTW47KKvvccUnGIcvfx", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "platformCategoryName", "parentModelAlias": "ERP_OMS$oms_category_md", "placeholder": "请输入"}, "displayComponentProps": {"copyable": false}, "displayComponentType": "Text", "editComponentProps": {}, "hidden": false, "label": "渠道类目名称", "name": "platformCategoryName", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-z6V7fmDjLPeuZcKWiZZuY", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "platformCategoryType", "parentModelAlias": "ERP_OMS$oms_category_md", "placeholder": "请输入"}, "displayComponentProps": {"copyable": false}, "displayComponentType": "Text", "editComponentProps": {}, "hidden": false, "label": "渠道类目类型", "name": "platformCategoryType", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-KDFxfzAsb_BHG0ThaQwlE", "name": "Field", "props": {"align": "left", "componentProps": {"fieldAlias": "level", "parentModelAlias": "ERP_OMS$oms_category_md", "placeholder": "请输入"}, "displayComponentProps": {"precisionDisplayType": "origin-round"}, "displayComponentType": "Number", "editComponentProps": {}, "hidden": false, "label": "类目层级", "name": "level", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-ueFKyXXtR5EMxVen348Sn", "name": "Field", "props": {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "parentModelAlias": "ERP_OMS$oms_category_md", "placeholder": "请选择"}, "displayComponentProps": {}, "editComponentProps": {}, "hidden": false, "label": "是否末级", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "BOOL", "width": 116}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-h14VbKQItzBm5zPbtEK_A", "name": "Field", "props": {"componentProps": {"fieldAlias": "createdAt", "parentModelAlias": "ERP_OMS$oms_category_md", "placeholder": "请选择"}, "displayComponentProps": {}, "editComponentProps": {}, "hidden": true, "label": "创建时间", "name": "createdAt", "type": "DATE", "width": 134}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-5FxCUvksCJTArpeK3m29k", "name": "Field", "props": {"componentProps": {"fieldAlias": "updatedAt", "parentModelAlias": "ERP_OMS$oms_category_md", "placeholder": "请选择"}, "displayComponentProps": {}, "editComponentProps": {}, "hidden": true, "label": "更新时间", "name": "updatedAt", "type": "DATE", "width": 134}, "type": "Widget"}], "key": "ERP_OMS$oms_category_view-tVn-V4UheCp-HrznfgqfS", "name": "Fields", "props": {}, "type": "Meta"}, {"children": [{"children": [], "key": "ERP_OMS$oms_category_view-OS04ZaCzqWNgaA20KLqYP", "name": "FilterField", "props": {"componentProps": {"fieldAlias": "shopCode", "parentModelAlias": "ERP_OMS$oms_category_md", "placeholder": "请输入"}, "hidden": false, "label": "店铺编码", "name": "shopCode", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-3TJlVW0929O5Kyaji0LJX", "name": "FilterField", "props": {"componentProps": {"fieldAlias": "channelCode", "parentModelAlias": "ERP_OMS$oms_category_md", "placeholder": "请输入"}, "editComponentProps": {"filterFields": [], "labelField": [], "modalProps": {"size": "middle", "width": 720}, "options": [{"enabled": true, "id": "fER6CgidG6ws8F1s3dYVC", "isDefault": false, "label": "1688", "value": "ONE688"}], "pagination": {"defaultPageSize": 20, "pageSizeOptions": [20, 50, 100, 200]}, "searchable": false, "showScope": "all", "tableCondition": null, "tableConditionContext$": null}, "editComponentType": "Select", "hidden": false, "label": "渠道编码", "name": "channelCode", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-wjtfTal240gbphiKa-360", "name": "FilterField", "props": {"componentProps": {"fieldAlias": "platformCategoryCode", "parentModelAlias": "ERP_OMS$oms_category_md", "placeholder": "请输入"}, "hidden": false, "label": "渠道类目编码", "name": "platformCategoryCode", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-pbH_0K8d4770mYkcCbb5X", "name": "FilterField", "props": {"componentProps": {"fieldAlias": "platformParentCategoryCode", "parentModelAlias": "ERP_OMS$oms_category_md", "placeholder": "请输入"}, "hidden": false, "label": "渠道上级类目编码", "name": "platformParentCategoryCode", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-ZsOOmEHqQsjLB5rAtpzx6", "name": "FilterField", "props": {"componentProps": {"fieldAlias": "platformCategoryName", "parentModelAlias": "ERP_OMS$oms_category_md", "placeholder": "请输入"}, "hidden": false, "label": "渠道类目名称", "name": "platformCategoryName", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-4ZlNQlE8ZAI1j_wJHVYkJ", "name": "FilterField", "props": {"componentProps": {"fieldAlias": "platformCategoryType", "parentModelAlias": "ERP_OMS$oms_category_md", "placeholder": "请输入"}, "hidden": false, "label": "渠道类目类型", "name": "platformCategoryType", "type": "TEXT", "width": 146}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-Y_ZmDYiXgJqBFpvvPbY5Z", "name": "FilterField", "props": {"componentProps": {"fieldAlias": "level", "parentModelAlias": "ERP_OMS$oms_category_md", "placeholder": "请输入"}, "editComponentProps": {"precisionDisplayType": "origin-round", "searchable": false, "shape": "line"}, "editComponentType": "InputNumber", "hidden": false, "label": "类目层级", "name": "level", "type": "NUMBER", "width": 144}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-mAg4vI2u8ItWNyb7aVLpg", "name": "FilterField", "props": {"componentProps": {"fieldAlias": "<PERSON><PERSON><PERSON><PERSON>", "parentModelAlias": "ERP_OMS$oms_category_md", "placeholder": "请选择"}, "hidden": false, "label": "是否末级", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "BOOL", "width": 116}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-3oYIU73vqwTiQDd-bUIfD", "name": "FilterField", "props": {"componentProps": {"fieldAlias": "createdAt", "parentModelAlias": "ERP_OMS$oms_category_md", "placeholder": "请选择"}, "hidden": true, "label": "创建时间", "name": "createdAt", "type": "DATE", "width": 134}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-VTxrtBx66MvLSTjoUDv3A", "name": "FilterField", "props": {"componentProps": {"fieldAlias": "updatedAt", "parentModelAlias": "ERP_OMS$oms_category_md", "placeholder": "请选择"}, "hidden": true, "label": "更新时间", "name": "updatedAt", "type": "DATE", "width": 134}, "type": "Widget"}], "key": "ERP_OMS$oms_category_view-BCceuvcbRf-2dHVFjO7vF", "name": "Filter<PERSON>ields", "props": {}, "type": "Meta"}], "key": "ERP_OMS$oms_category_view-table-container-ERP_OMS$oms_category_md", "name": "Table", "props": {"allowClickRowSelect": true, "allowRowSelect": true, "enableSolution": true, "flow": {"containerKey": "ERP_OMS$oms_category_view-table-container-ERP_OMS$oms_category_md", "context$": "$context", "modelAlias": "ERP_OMS$oms_category_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_OMS$oms_category_md"}}, {"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_OMS$SYS_PagingDataService", "type": "InvokeSystemService"}, "label": "", "mode": "normal", "modelAlias": "ERP_OMS$oms_category_md", "onRowActionConfig": {"enable": false}, "pagination": {"defaultPageSize": 20, "pageSizeOptions": [20, 50, 100, 200], "needTotal": true}, "selectType": "multiple", "serviceKey": "ERP_OMS$SYS_PagingDataService", "showFilterFields": true, "showScope": "all", "showType": "normal", "subTableConfig": {}, "tableCondition": null, "tableConditionContext$": null, "toolbar": {"search": false}, "useNewFilter": true}, "type": "Container"}, {"children": [{"children": [{"children": [{"children": [], "key": "ERP_OMS$oms_category_view-oIul0ZW2FvjjjKIJPCmFw", "name": "FormField", "props": {"componentProps": {"fieldAlias": "channelCode", "parentModelAlias": "ERP_OMS$oms_category_sync_request", "placeholder": "请输入"}, "displayComponentProps": {}, "displayComponentType": "Text", "editComponentProps": {"options": [{"id": "cqPacAtowi99o7_B48WCr", "label": "1688", "value": "ONE688"}], "searchable": false}, "editComponentType": "Select", "initialValue": "ONE688", "label": "渠道编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "UAUnQv96EySfyNDBjtlvU", "trigger": "auto", "valueRules": null}], "name": "channelCode", "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-omQqjaou3jwk_03bJ8cvb", "name": "FormField", "props": {"componentProps": {"fieldAlias": "omsShopMd", "labelField": "shopName", "modelAlias": "ERP_OMS$oms_shop_md", "parentModelAlias": "ERP_OMS$oms_category_sync_request", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"containerKey": "ERP_OMS$oms_category_view-omQqjaou3jwk_03bJ8cvb", "context$": "$context", "modelAlias": "ERP_OMS$oms_shop_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_OMS$oms_shop_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_OMS$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_OMS$oms_shop_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "shopName", "parentModelAlias": "ERP_OMS$oms_shop_md", "placeholder": "请输入"}, "isRelationColumn": true, "label": "店铺名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "YZxEJqiGk8DQDxA9MQqKB", "trigger": "auto", "valueRules": null}], "name": "shopName", "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "shopName", "parentModelAlias": "ERP_OMS$oms_shop_md", "placeholder": "请输入"}, "label": "店铺名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "YZxEJqiGk8DQDxA9MQqKB", "trigger": "auto", "valueRules": null}], "name": "shopName", "type": "TEXT", "width": 120}], "findFlow": {"containerKey": "ERP_OMS$oms_category_view-omQqjaou3jwk_03bJ8cvb", "context$": "$context", "modelAlias": "ERP_OMS$oms_shop_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_OMS$oms_shop_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_OMS$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_OMS$oms_shop_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_OMS$oms_shop_md"}}, {"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_OMS$SYS_PagingDataService", "type": "InvokeSystemService"}, "label": "选择渠道店铺", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_OMS$oms_shop_md", "pagination": {"defaultPageSize": 20, "pageSizeOptions": [20, 50, 100, 200]}, "searchable": false, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "JWBQCXo0erW6pE67z7A6j", "leftValue": {"fieldType": "Text", "title": "渠道编码(channelCode)", "type": "VarValue", "val": "channelCode", "value": "ERP_OMS$oms_shop_md.channelCode", "valueType": "VAR", "varVal": "channelCode", "varValue": [{"valueKey": "channelCode", "valueName": "channelCode"}]}, "operator": "EQ", "rightValue": {"fieldType": "Text", "scope": "form", "target": "ERP_OMS$oms_category_view-7M1Ltewnd47LClEAM0N-8", "title": "channelCode", "type": "VarValue", "val": "channelCode", "value": "channelCode", "valueType": "VAR", "varVal": "channelCode", "varValue": [{"valueKey": "channelCode", "valueName": "channelCode"}]}, "type": "ConditionLeaf"}, {"id": "hKl_nLRqXaH04G_QaowMn", "leftValue": {"fieldType": "Enum", "title": "状态(status)", "type": "VarValue", "val": "status", "value": "ERP_OMS$oms_shop_md.status", "valueType": "VAR", "varVal": "status", "varValue": [{"valueKey": "status", "valueName": "status"}]}, "operator": "EQ", "rightValue": {"constValue": "ENABLED", "fieldType": "Enum", "type": "ConstValue", "valueType": "CONST"}, "type": "ConditionLeaf"}], "id": "XbIfsI6dqxR6onkg__DGM", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "5P-kos_20cUnvLvKspQr3", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": "$context"}, "editComponentType": "RelationSelect", "label": "渠道店铺", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "H5wqfoha_zfZ6xKw7Rour", "trigger": "auto", "valueRules": null}], "name": "omsShopMd", "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-ddskyYOeqUsAG3aiVhdsZ", "name": "FormField", "props": {"componentProps": {"fieldAlias": "ptCategoryCode", "parentModelAlias": "ERP_OMS$oms_category_sync_request", "placeholder": "请输入"}, "displayComponentProps": {}, "displayComponentType": "Text", "editComponentProps": {"searchable": false}, "editComponentType": "InputText", "label": "平台类目编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "Zh6PyrjXXYxTyqFEBeGrT", "trigger": "auto", "valueRules": null}], "name": "ptCategoryCode", "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-MZQvfa4q1h-i8Gj3ghJSp", "name": "FormField", "props": {"componentProps": {"fieldAlias": "ptCategoryName", "parentModelAlias": "ERP_OMS$oms_category_sync_request", "placeholder": "请输入"}, "displayComponentProps": {}, "displayComponentType": "Text", "editComponentProps": {"searchable": false}, "editComponentType": "InputText", "label": "平台类目名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "ov63E3R6hA866ZcHlW6Ts", "trigger": "auto", "valueRules": null}], "name": "ptCategoryName", "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-14tKkjaizl95viVzLhe3h", "name": "FormField", "props": {"componentProps": {"fieldAlias": "platformCateType", "parentModelAlias": "ERP_OMS$oms_category_sync_request", "placeholder": "请输入"}, "displayComponentProps": {}, "displayComponentType": "Text", "editComponentProps": {"searchable": false}, "editComponentType": "InputText", "label": "渠道类目类型", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "11T_zV5gXYLBDVBT57xtR", "trigger": "auto", "valueRules": null}], "name": "platformCateType", "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-SmqJkxG7r1HXaEoefDN9Z", "name": "FormField", "props": {"componentProps": {"fieldAlias": "level", "parentModelAlias": "ERP_OMS$oms_category_sync_request", "placeholder": "请输入"}, "displayComponentProps": {"precisionDisplayType": "fill-round"}, "displayComponentType": "Number", "editComponentProps": {"precisionDisplayType": "fill-round", "searchable": false, "shape": "line"}, "editComponentType": "InputNumber", "label": "类目层级", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "SAJqdAwqgLlj2iCdLxEMs", "trigger": "auto", "valueRules": null}], "name": "level", "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-RlFpwdm5lipuF9hbtXVLv", "name": "FormField", "props": {"componentProps": {"fieldAlias": "isSyncSubcategory", "parentModelAlias": "ERP_OMS$oms_category_sync_request", "placeholder": "请选择"}, "displayComponentProps": {}, "displayComponentType": "BoolShow", "editComponentProps": {"options": [{"id": "X2oH0ndQe_aTHtdtwGjv-", "label": "是", "value": true}, {"id": "pJb5bY0uuJVj6llacyom_", "label": "否", "value": false}], "searchable": false}, "editComponentType": "Switch", "label": "是否拉取子类目", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "p6_kldOOCQ6ySKMe8I-Kh", "trigger": "auto", "valueRules": null}], "name": "isSyncSubcategory", "type": "BOOL", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-S1VgcBaYAAnaYAlZ3aCDw", "name": "FormField", "props": {"componentProps": {"fieldAlias": "syncType", "modelAlias": "ERP_OMS$oms_category_sync_request", "parentModelAlias": "ERP_OMS$oms_category_sync_request", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal", "modelAlias": "ERP_OMS$oms_category_sync_request"}, "displayComponentType": "Enum", "editComponentProps": {"modelAlias": "ERP_OMS$oms_category_sync_request", "options": [{"_row_id_": "FULL", "enabled": true, "isDefault": false, "label": "全量同步", "value": "1"}, {"_row_id_": "INCREMENT", "enabled": true, "isDefault": false, "label": "增量同步", "value": "2"}], "searchable": false}, "editComponentType": "Select", "initialValue": "2", "label": "同步类型", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "x39SND9iE_jWH6lBdUVNe", "trigger": "auto", "valueRules": null}], "name": "syncType", "type": "SELECT", "width": 120}, "type": "Widget"}], "key": "ERP_OMS$oms_category_view-7M1Ltewnd47LClEAM0N-8", "name": "FormGroup", "props": {"layout": "vertical", "modelAlias": "ERP_OMS$oms_category_sync_request", "labelPosition": "left"}, "type": "Container"}], "key": "ERP_OMS$oms_category_view-4udeJjj4lMpVC2SBblO3S", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_OMS$oms_category_view-dZKRXAn2VFzRBqKnEJITu", "name": "<PERSON><PERSON>", "props": {"buttonType": "default", "confirmOn": "off", "eventActions": [{"actions": [{"actionId": "action11bJVJVj", "config": {"target": ["ROOT"]}, "type": "Close"}]}], "label": "取消", "permissionKey": "ERP_OMS$oms_category_view:list_perm_ac_z_3_1_0", "showCondition": {}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-nt6m0l3EqRKtUtogu3P2L", "name": "<PERSON><PERSON>", "props": {"buttonType": "default", "confirmOn": "off", "eventActions": [{"actions": [{"actionId": "action8ATI47so", "config": {"params": [{"fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "modelAlias": "ERP_OMS$oms_category_sync_request", "valueConfig": {"action": {"name": "getData", "target": "ERP_OMS$oms_category_view-7M1Ltewnd47LClEAM0N-8"}, "type": "action"}}], "service": "ERP_OMS$OMS_CATEGORY_PULL_EXECUTE_EVENT_SERVICE"}, "type": "BindService"}, {"actionId": "actionBa2Nrphk", "config": {"target": ["ERP_OMS$oms_category_view-neHuZNLDKv_1TQqkqFl_l"]}, "type": "Close"}, {"actionId": "actionFyhDCj75", "config": {"level": "success", "message": "提交成功"}, "type": "Message"}, {"actionId": "actionPyF1it_O", "config": {"target": ["ERP_OMS$oms_category_view-table-container-ERP_OMS$oms_category_md"]}, "type": "Refresh"}]}], "label": "提交", "permissionKey": "ERP_OMS$oms_category_view:list_perm_ac_z_3_1_1", "showCondition": {}, "type": "default"}, "type": "Widget"}], "key": "ERP_OMS$oms_category_view-YUeGeMhWEnoEsyzeMEIh1", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "key": "ERP_OMS$oms_category_view-neHuZNLDKv_1TQqkqFl_l", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": false, "layout": "modal", "layoutProps": {"bodyStyle": {}, "dragMode": [], "width": 720, "widthType": "custom", "placement": "right", "height": 360}, "params": [], "title": "类目拉取"}, "type": "Container"}, {"children": [{"children": [{"children": [{"children": [], "key": "ERP_OMS$oms_category_view-5IDpshIPxA0shq109IdxI", "name": "FormField", "props": {"componentProps": {"fieldAlias": "channelCode", "parentModelAlias": "ERP_OMS$oms_category_sync_request", "placeholder": "请输入"}, "displayComponentProps": {}, "displayComponentType": "Text", "editComponentProps": {"options": [{"id": "cqPacAtowi99o7_B48WCr", "label": "1688", "value": "ONE688"}], "searchable": false}, "editComponentType": "Select", "initialValue": "ONE688", "label": "渠道编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "UAUnQv96EySfyNDBjtlvU", "trigger": "auto", "valueRules": null}], "name": "channelCode", "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-tDaBj3AoTQGwKJeJUP5-g", "name": "FormField", "props": {"componentProps": {"fieldAlias": "omsShopMd", "labelField": "shopName", "modelAlias": "ERP_OMS$oms_shop_md", "parentModelAlias": "ERP_OMS$oms_category_sync_request", "placeholder": "请选择"}, "displayComponentProps": {"findFlow": {"containerKey": "ERP_OMS$oms_category_view-tDaBj3AoTQGwKJeJUP5-g", "context$": "$context", "modelAlias": "ERP_OMS$oms_shop_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_OMS$oms_shop_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_OMS$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "modelAlias": "ERP_OMS$oms_shop_md"}, "displayComponentType": "RelationShow", "editComponentProps": {"fields": [{"componentProps": {"fieldAlias": "shopName", "parentModelAlias": "ERP_OMS$oms_shop_md", "placeholder": "请输入"}, "isRelationColumn": true, "label": "店铺名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "YZxEJqiGk8DQDxA9MQqKB", "trigger": "auto", "valueRules": null}], "name": "shopName", "type": "TEXT", "width": 120}], "filterFields": [{"componentProps": {"fieldAlias": "shopName", "parentModelAlias": "ERP_OMS$oms_shop_md", "placeholder": "请输入"}, "label": "店铺名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "YZxEJqiGk8DQDxA9MQqKB", "trigger": "auto", "valueRules": null}], "name": "shopName", "type": "TEXT", "width": 120}], "findFlow": {"containerKey": "ERP_OMS$oms_category_view-tDaBj3AoTQGwKJeJUP5-g", "context$": "$context", "modelAlias": "ERP_OMS$oms_shop_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_OMS$oms_shop_md"}}, {"elements": [{"fieldAlias": "id", "fieldName": "id", "fieldType": "Number", "valueConfig": {"expression": "primaryId", "name": "id", "type": "expression"}}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_OMS$SYS_FindDataByIdService", "type": "InvokeSystemService"}, "flow": {"containerKey": "", "context$": "$context", "modelAlias": "ERP_OMS$oms_shop_md", "params": [{"fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "valueConfig": {"name": "<PERSON><PERSON><PERSON>", "type": "const", "value": "ERP_OMS$oms_shop_md"}}, {"elements": [{"fieldAlias": "pageable", "fieldName": "pageable", "fieldType": "Pageable"}], "fieldAlias": "request", "fieldName": "request", "fieldType": "Object"}], "serviceKey": "ERP_OMS$SYS_PagingDataService", "type": "InvokeSystemService"}, "label": "选择渠道店铺", "modalProps": {"size": "middle", "width": 720}, "modelAlias": "ERP_OMS$oms_shop_md", "pagination": {"defaultPageSize": 20, "pageSizeOptions": [20, 50, 100, 200]}, "searchable": false, "showScope": "filter", "tableCondition": {"conditions": [{"conditions": [{"id": "JWBQCXo0erW6pE67z7A6j", "leftValue": {"fieldType": "Text", "title": "渠道编码(channelCode)", "type": "VarValue", "val": "channelCode", "value": "ERP_OMS$oms_shop_md.channelCode", "valueType": "VAR", "varVal": "channelCode", "varValue": [{"valueKey": "channelCode", "valueName": "channelCode"}]}, "operator": "EQ", "rightValue": {"fieldType": "Text", "scope": "form", "target": "ERP_OMS$oms_category_view-8rkfi5eccaw6l9klj2Xll", "title": "channelCode", "type": "VarValue", "val": "channelCode", "value": "channelCode", "valueType": "VAR", "varVal": "channelCode", "varValue": [{"valueKey": "channelCode", "valueName": "channelCode"}]}, "type": "ConditionLeaf"}], "id": "XbIfsI6dqxR6onkg__DGM", "logicOperator": "AND", "type": "ConditionGroup"}], "id": "5P-kos_20cUnvLvKspQr3", "logicOperator": "OR", "type": "ConditionGroup"}, "tableConditionContext$": "$context"}, "editComponentType": "RelationSelect", "label": "渠道店铺", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": true}, "key": "H5wqfoha_zfZ6xKw7Rour", "trigger": "auto", "valueRules": null}], "name": "omsShopMd", "type": "OBJECT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-Ub34iNBcOemBYGiSpO3Uk", "name": "FormField", "props": {"componentProps": {"fieldAlias": "ptCategoryCode", "parentModelAlias": "ERP_OMS$oms_category_sync_request", "placeholder": "请输入"}, "displayComponentProps": {}, "displayComponentType": "Text", "editComponentProps": {"searchable": false}, "editComponentType": "InputText", "label": "平台类目编码", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "Zh6PyrjXXYxTyqFEBeGrT", "trigger": "auto", "valueRules": null}], "name": "ptCategoryCode", "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-AX8zyiYoRNsz9mrEmAhWx", "name": "FormField", "props": {"componentProps": {"fieldAlias": "ptCategoryName", "parentModelAlias": "ERP_OMS$oms_category_sync_request", "placeholder": "请输入"}, "displayComponentProps": {}, "displayComponentType": "Text", "editComponentProps": {"searchable": false}, "editComponentType": "InputText", "label": "平台类目名称", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "ov63E3R6hA866ZcHlW6Ts", "trigger": "auto", "valueRules": null}], "name": "ptCategoryName", "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-LBHOoyWogaLg2s539z6E_", "name": "FormField", "props": {"componentProps": {"fieldAlias": "platformCateType", "parentModelAlias": "ERP_OMS$oms_category_sync_request", "placeholder": "请输入"}, "displayComponentProps": {}, "displayComponentType": "Text", "editComponentProps": {"searchable": false}, "editComponentType": "InputText", "label": "渠道类目类型", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "11T_zV5gXYLBDVBT57xtR", "trigger": "auto", "valueRules": null}], "name": "platformCateType", "type": "TEXT", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-QkvLJc9XcFp7be8RaAyvx", "name": "FormField", "props": {"componentProps": {"fieldAlias": "level", "parentModelAlias": "ERP_OMS$oms_category_sync_request", "placeholder": "请输入"}, "displayComponentProps": {"precisionDisplayType": "fill-round"}, "displayComponentType": "Number", "editComponentProps": {"precisionDisplayType": "fill-round", "searchable": false, "shape": "line"}, "editComponentType": "InputNumber", "label": "类目层级", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "SAJqdAwqgLlj2iCdLxEMs", "trigger": "auto", "valueRules": null}], "name": "level", "type": "NUMBER", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-N4bUMkMcFfSENjnC3RYHJ", "name": "FormField", "props": {"componentProps": {"fieldAlias": "isSyncSubcategory", "parentModelAlias": "ERP_OMS$oms_category_sync_request", "placeholder": "请选择"}, "displayComponentProps": {"showSwitchText": false}, "displayComponentType": "BoolShow", "editComponentProps": {"options": [{"id": "zkAiJiHTiFpZm3ZG4aJrI", "label": "是", "value": true}, {"id": "E0hCAzN-8xhlPqFLx46hR", "label": "否", "value": false}], "searchable": false, "showSwitchText": false}, "editComponentType": "Switch", "initialValue": true, "label": "是否同步子类目", "lookup": [{"fieldRules": {"hidden": false, "readOnly": true, "required": false}, "key": "97Z-Fdml2bdMXcA10CtXe", "trigger": "auto", "valueRules": null}], "name": "isSyncSubcategory", "type": "BOOL", "width": 120}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-i8RIFSTb6e0EUITpR7aoS", "name": "FormField", "props": {"componentProps": {"fieldAlias": "syncType", "modelAlias": "ERP_OMS$oms_category_sync_request", "parentModelAlias": "ERP_OMS$oms_category_sync_request", "placeholder": "请选择"}, "displayComponentProps": {"enumStyle": "normal", "modelAlias": "ERP_OMS$oms_category_sync_request"}, "displayComponentType": "Enum", "editComponentProps": {"modelAlias": "ERP_OMS$oms_category_sync_request", "options": [{"_row_id_": "FULL", "enabled": true, "isDefault": false, "label": "全量同步", "value": "1"}, {"_row_id_": "INCREMENT", "enabled": true, "isDefault": false, "label": "增量同步", "value": "2"}], "searchable": false}, "editComponentType": "Select", "initialValue": "2", "label": "同步类型", "lookup": [{"fieldRules": {"hidden": false, "readOnly": false, "required": false}, "key": "x39SND9iE_jWH6lBdUVNe", "trigger": "auto", "valueRules": null}], "name": "syncType", "type": "SELECT", "width": 120}, "type": "Widget"}], "key": "ERP_OMS$oms_category_view-8rkfi5eccaw6l9klj2Xll", "name": "FormGroup", "props": {"layout": "vertical", "modelAlias": "ERP_OMS$oms_category_sync_request"}, "type": "Container"}], "key": "ERP_OMS$oms_category_view-3eH4_uvDSvCDPkUYvqsxu", "name": "ChildViewBody", "props": {}, "type": "Layout"}, {"children": [{"children": [], "key": "ERP_OMS$oms_category_view-46MdliUtfj46r28dHKBL5", "name": "<PERSON><PERSON>", "props": {"buttonType": "default", "confirmOn": "off", "eventActions": [{"actions": [{"actionId": "actioncRAeeJhm", "config": {"target": ["ROOT"]}, "type": "Close"}]}], "label": "取消", "permissionKey": "ERP_OMS$oms_category_view:list_perm_ac_z_4_1_0", "showCondition": {}, "type": "default"}, "type": "Widget"}, {"children": [], "key": "ERP_OMS$oms_category_view-QzLAWarTSb901dxJMzrx8", "name": "<PERSON><PERSON>", "props": {"buttonType": "default", "confirmOn": "off", "eventActions": [{"actions": [{"actionId": "actionjbfZT_lp", "config": {"params": [], "service": "ERP_OMS$OMS_CATEGORY_SYNC_EXECUTE_EVENT_SERVICE"}, "type": "BindService"}, {"actionId": "actionFSoZqYUq", "config": {"target": ["ERP_OMS$oms_category_view-v2U5KesF83WSCqBHHMSCC"]}, "type": "Close"}, {"actionId": "actionWNhdduNc", "config": {"level": "success", "message": "提交成功"}, "type": "Message"}, {"actionId": "action8mTIjauF", "config": {"target": ["ERP_OMS$oms_category_view-table-container-ERP_OMS$oms_category_md"]}, "type": "Refresh"}]}], "label": "提交", "permissionKey": "ERP_OMS$oms_category_view:list_perm_ac_z_4_1_1", "showCondition": {}, "type": "default"}, "type": "Widget"}], "key": "ERP_OMS$oms_category_view-jPYk8daU_7eHE9xl_ovNZ", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {}, "type": "Layout"}], "key": "ERP_OMS$oms_category_view-v2U5KesF83WSCqBHHMSCC", "name": "<PERSON><PERSON><PERSON><PERSON>", "props": {"designOpen": false, "layout": "modal", "layoutProps": {"bodyStyle": {}, "dragMode": [], "width": 720, "widthType": "custom", "placement": "right", "height": 360}, "params": [], "title": "类目同步"}, "type": "Container"}, {"children": [], "key": "ERP_OMS$oms_category_view-yi9zdN82ERLP2Y7HkifQY", "name": "PageHeaderAddition", "props": {}, "type": "Layout"}], "key": "ERP_OMS$oms_category_view-list", "name": "Page", "props": {"actionConfig": {}, "backResourceTabProcessConfig": "retain", "backgroundTransparent": true, "collectionService": {"createBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK/save"}, "deleteBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK/delete"}, "getCurrentSceneBookMarkFlow": {"method": "POST", "type": "InvokeApi", "url": "/api/trantor2default/preference/BOOKMARK"}}, "params": [], "showBack": false, "showFooter": false, "showHeader": false, "headerUnderline": true, "eventActions": []}, "type": "Container"}