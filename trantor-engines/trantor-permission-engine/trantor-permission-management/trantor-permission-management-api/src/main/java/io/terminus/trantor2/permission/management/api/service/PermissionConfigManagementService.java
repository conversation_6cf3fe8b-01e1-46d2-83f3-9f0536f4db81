package io.terminus.trantor2.permission.management.api.service;

import io.terminus.trantor2.permission.api.common.dto.PermissionConfig;

/**
 * 权限配置逻辑处理服务
 *
 * <AUTHOR>
 * 2023/12/13 10:19 AM
 **/
public interface PermissionConfigManagementService {

    /**
     * 查询指定门户的权限配置
     *
     * @param portalCode 门户编码
     * @return PermissionConfig 权限配置
     */
    PermissionConfig findPermissionConfig(String portalCode);

    /**
     * 更新权限配置
     *
     * @param permissionConfig 更新对象
     * @return 更新后权限配置信息对象
     */
    PermissionConfig updatePermissionConfig(PermissionConfig permissionConfig);
}
