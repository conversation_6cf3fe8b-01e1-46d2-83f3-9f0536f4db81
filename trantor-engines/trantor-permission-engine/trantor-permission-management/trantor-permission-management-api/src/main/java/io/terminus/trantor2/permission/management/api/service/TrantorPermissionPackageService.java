package io.terminus.trantor2.permission.management.api.service;

import io.terminus.iam.api.response.ei.ExportImportTask;
import io.terminus.iam.api.request.ei.ExportImportTaskPagingParams;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.permission.api.common.dto.PermissionPackageBeforeResponse;
import io.terminus.trantor2.permission.api.common.dto.PermissionPackageInteractionRequest;
import io.terminus.trantor2.permission.api.common.dto.PermissionPackageTaskCreateRequest;

public interface TrantorPermissionPackageService {

    /**
     * 任务列表分页
     *
     * @return true 有 false 没有
     */
    Paging<ExportImportTask> paging(ExportImportTaskPagingParams params);

    /**
     * 是否有进行中的任务并且创建权限包导入导出任务
     *
     * @return 权限包信息
     */
    Response<ExportImportTask> checkInProcessAndCreate(PermissionPackageTaskCreateRequest request);
}
