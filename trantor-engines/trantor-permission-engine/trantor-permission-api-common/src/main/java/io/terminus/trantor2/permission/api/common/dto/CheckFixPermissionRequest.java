package io.terminus.trantor2.permission.api.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.model.management.meta.model.AbstractRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "权限检测工具入参")
public class CheckFixPermissionRequest extends AbstractRequest {

    @Schema(description = "门户code", required = true)
    @NotNull(message = "portalCode can not be null")
    private String portalCode;

    @Schema(description = "快照Id，检测不传入修复传入，根据快照Id获取快照信息，如果快照不存在重新加载")
    private String snapshotId;
}
