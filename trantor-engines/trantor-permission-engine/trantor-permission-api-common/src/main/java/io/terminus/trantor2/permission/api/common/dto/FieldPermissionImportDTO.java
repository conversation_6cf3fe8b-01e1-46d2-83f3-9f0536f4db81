package io.terminus.trantor2.permission.api.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.iam.api.response.permission.FieldPermission;
import io.terminus.trantor2.permission.api.common.consts.PermissionPackageConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class FieldPermissionImportDTO extends FieldPermission {

    /**
     * @see PermissionPackageConstants.ChangeType
     * 导入变更类型
     */
    @Schema(description = "导入变更类型")
    private String importChangeType;
}
