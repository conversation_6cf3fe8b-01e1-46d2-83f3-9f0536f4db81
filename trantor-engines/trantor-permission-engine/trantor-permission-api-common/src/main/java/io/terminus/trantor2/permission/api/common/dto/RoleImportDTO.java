package io.terminus.trantor2.permission.api.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.permission.api.common.consts.PermissionPackageConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.iam.api.response.role.Role;


@Data
@EqualsAndHashCode(callSuper = true)
public class RoleImportDTO extends Role {

    /**
     * @see PermissionPackageConstants.ChangeType
     * 导入变更类型
     */
    @Schema(description = "导入变更类型")
    private String importChangeType;
}
