package io.terminus.trantor2.permission.api.common.exception;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorBizException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PermissionHandleException extends TrantorBizException {

    public PermissionHandleException(String message) {
        super(ErrorType.PERMISSION_HANDLE_ERROR, message, new Object[]{message});
    }

    public PermissionHandleException(ErrorType errorType) {
        super(errorType);
    }

    public PermissionHandleException(ErrorType errorType, Throwable e) {
        super(errorType, e);
    }
}
