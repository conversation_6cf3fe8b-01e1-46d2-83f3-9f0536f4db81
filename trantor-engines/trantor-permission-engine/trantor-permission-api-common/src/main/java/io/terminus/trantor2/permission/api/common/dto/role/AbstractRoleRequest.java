package io.terminus.trantor2.permission.api.common.dto.role;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.iam.api.request.CreateOps;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/5/13 15:00
 **/
@Data
public abstract class AbstractRoleRequest implements Serializable {

    @Schema(description = "角色标识")
    @NotNull(message = "key can not be null", groups = CreateOps.class)
    @Size(max = 20)
    private String key;

    @Schema(description = "角色名称")
    @NotNull(message = "name can not be null", groups = CreateOps.class)
    @Size(max = 20)
    private String name;

    @Schema(description = "描述")
    @Size(max = 50)
    private String desc;

    @Schema(description = "门户标识")
    private String portalCode;
}
