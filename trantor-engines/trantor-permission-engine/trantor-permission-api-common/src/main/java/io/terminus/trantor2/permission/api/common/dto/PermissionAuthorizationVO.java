package io.terminus.trantor2.permission.api.common.dto;

import io.terminus.iam.api.enums.permission.AuthorizationEffect;
import io.terminus.trantor2.meta.api.dto.ViewPermissionDTO;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.service.common.meta.AIAgentMeta;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2025/6/11 15:54
 **/
public class PermissionAuthorizationVO extends PermissionResourceDTO {

    public static PermissionAuthorizationVO of(AIAgentMeta agentMeta, String permissionKey, AuthorizationEffect effect) {
        PermissionAuthorizationVO permissionAuthorizationVO = new PermissionAuthorizationVO();
        permissionAuthorizationVO.setResourceId(agentMeta.getId());
        permissionAuthorizationVO.setResourceType(ACLResourceType.AIAgent);
        permissionAuthorizationVO.setResourceKey(agentMeta.getKey());
        permissionAuthorizationVO.setResourceName(agentMeta.getName());
        FunctionPermissionResourceProps props = new FunctionPermissionResourceProps();
        props.setOwnPermissionKey(permissionKey);
        props.setEffect(effect);
        permissionAuthorizationVO.setProps(props);
        return permissionAuthorizationVO;
    }

    public static PermissionAuthorizationVO of(@NotNull AIAgentMeta agentMeta,
                                               @NotNull ViewPermissionDTO.BindAIAgentPermissionDTO agentPermission,
                                               @NotNull Map<String, AuthorizationEffect> permissionKeyToEffect) {
        PermissionAuthorizationVO agentPermAuthVO = of(agentMeta, agentPermission.getPermissionKey(), permissionKeyToEffect.getOrDefault(agentPermission.getPermissionKey(), AuthorizationEffect.NONE));
        agentPermAuthVO.setChildren(new ArrayList<>());
        if (CollectionUtils.isNotEmpty(agentPermission.getBindServices())) {
            List<PermissionAuthorizationVO> servicePermAuthVO = agentPermission.getBindServices().stream()
                    .map(bindServicePermissionDTO -> of(bindServicePermissionDTO, permissionKeyToEffect))
                    .collect(Collectors.toList());
            agentPermAuthVO.getChildren().addAll(servicePermAuthVO);
        }
        return agentPermAuthVO;
    }

    public static PermissionAuthorizationVO of(@NotNull ViewPermissionDTO.BindServicePermissionDTO bindServicePermissionDTO,
                                               @NotNull Map<String, AuthorizationEffect> permissionKeyToEffect) {
        PermissionAuthorizationVO permissionAuthorizationVO = new PermissionAuthorizationVO();
        permissionAuthorizationVO.setResourceType(ACLResourceType.Service);
        permissionAuthorizationVO.setResourceKey(bindServicePermissionDTO.getServiceKey());
        permissionAuthorizationVO.setResourceName(bindServicePermissionDTO.getServiceKey());
        FunctionPermissionResourceProps props = new FunctionPermissionResourceProps();
        props.setOwnPermissionKey(bindServicePermissionDTO.getPermissionKey());
        props.setEffect(permissionKeyToEffect.getOrDefault(bindServicePermissionDTO.getPermissionKey(), AuthorizationEffect.NONE));
        permissionAuthorizationVO.setProps(props);
        return permissionAuthorizationVO;
    }

    public static PermissionAuthorizationVO of(@NotNull ViewPermissionDTO.BindApiPermissionDTO bindApiPermissionDTO,
                                               @NotNull Map<String, AuthorizationEffect> permissionKeyToEffect) {
        PermissionAuthorizationVO permissionAuthorizationVO = new PermissionAuthorizationVO();
        permissionAuthorizationVO.setResourceType(ACLResourceType.Service);
        permissionAuthorizationVO.setResourceKey(KeyUtil.assembleApi(bindApiPermissionDTO.getApiMethod(), bindApiPermissionDTO.getApiPath()));
        permissionAuthorizationVO.setResourceName(KeyUtil.assembleApi(bindApiPermissionDTO.getApiMethod(), bindApiPermissionDTO.getApiPath()));
        FunctionPermissionResourceProps props = new FunctionPermissionResourceProps();
        props.setOwnPermissionKey(bindApiPermissionDTO.getPermissionKey());
        props.setEffect(permissionKeyToEffect.getOrDefault(bindApiPermissionDTO.getPermissionKey(), AuthorizationEffect.NONE));
        permissionAuthorizationVO.setProps(props);
        return permissionAuthorizationVO;
    }
}
