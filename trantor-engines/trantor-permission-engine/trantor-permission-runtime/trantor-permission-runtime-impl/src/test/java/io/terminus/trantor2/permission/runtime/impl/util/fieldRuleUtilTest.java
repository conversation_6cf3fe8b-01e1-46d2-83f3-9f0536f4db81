package io.terminus.trantor2.permission.runtime.impl.util;

import com.fasterxml.jackson.core.type.TypeReference;
import io.terminus.iam.api.enums.permission.FieldAccessType;
import io.terminus.iam.api.response.permission.FieldRule;
import io.terminus.trantor2.service.common.utils.DataLoader;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;

import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 2024/7/24 11:17
 **/
public class fieldRuleUtilTest {

    @Test
    public void testMergeFieldRules() {
        List<FieldRule> originalFieldRules = DataLoader.loadByTypeReference("field_rules.json", new TypeReference<List<FieldRule>>() {
        });
        Assert.assertTrue(CollectionUtils.isNotEmpty(originalFieldRules));

        Collection<FieldRule> fieldRules = FieldRuleUtil.mergeFieldRules(originalFieldRules);

        Map<String, FieldAccessType> fieldPathToFieldAccessTypeMap = getParamKeyPathToFieldAccessTypeMap(fieldRules);

        fieldPathToFieldAccessTypeMap.forEach((fieldPath, fieldAccessType) -> {
            String[] split = fieldPath.split("\\.");
            String fieldKey = split[split.length - 1];
            if (fieldKey.contains("writable")) {
                Assert.assertEquals(FieldAccessType.WRITABLE, fieldAccessType);
            } else if (fieldKey.contains("readonly")) {
                Assert.assertEquals(FieldAccessType.READONLY, fieldAccessType);
            } else if (fieldKey.contains("invisible")) {
                Assert.assertEquals(FieldAccessType.INVISIBLE, fieldAccessType);
            }
        });
    }

    private Map<String, FieldAccessType> getParamKeyPathToFieldAccessTypeMap(Collection<FieldRule> fieldRules) {
        Map<String, FieldAccessType> fieldPathToFieldAccessTypeMap = new LinkedHashMap<>();
        fillFieldAccessType(fieldRules, "", fieldPathToFieldAccessTypeMap);
        return fieldPathToFieldAccessTypeMap;
    }

    private void fillFieldAccessType(@NotNull Collection<FieldRule> fieldRules, String parentPath, @NotNull Map<String, FieldAccessType> fieldAccessTypeMap) {
        for (FieldRule fieldRule : fieldRules) {
            String fieldPath = parentPath + fieldRule.getEntityParamKey();
            Assert.assertFalse(fieldAccessTypeMap.containsKey(fieldPath));
            fieldAccessTypeMap.put(fieldPath, fieldRule.getAccessType());
            if (CollectionUtils.isNotEmpty(fieldRule.getChildren())) {
                fillFieldAccessType(fieldRule.getChildren(), fieldRule.getEntityParamKey() + ".", fieldAccessTypeMap);
            }
        }
    }

    @Test
    public void testDeterminePrimaryAccessTypes() {
        List<FieldAccessType> elements = Arrays.asList(
                FieldAccessType.WRITABLE,
                FieldAccessType.READONLY,
                FieldAccessType.INVISIBLE,
                null
        );

        List<List<FieldAccessType>> allCombinations = new ArrayList<>();

        // 生成所有非空组合
        generateCombinations(elements, 0, new ArrayList<>(), allCombinations);

        // 再对外层集合按每个内层集合的元素个数排序
        allCombinations.sort(Comparator.comparingInt(List::size));

        // 对每个组合生成所有排列并按顺序输出
        for (List<FieldAccessType> combination : allCombinations) {
            List<List<FieldAccessType>> allPermutations = new ArrayList<>();
            generatePermutations(combination, 0, allPermutations);

            // 排序并输出
            allPermutations.sort(Comparator.comparing(Object::toString));
            for (List<FieldAccessType> permutation : allPermutations) {
                FieldAccessType actualFieldAccessType = FieldRuleUtil.determinePrimaryAccessTypes(permutation);
                if (permutation.contains(FieldAccessType.WRITABLE)) {
                    Assert.assertEquals(FieldAccessType.WRITABLE, actualFieldAccessType);
                } else if (permutation.contains(FieldAccessType.READONLY)) {
                    Assert.assertEquals(FieldAccessType.READONLY, actualFieldAccessType);
                } else if (permutation.contains(FieldAccessType.INVISIBLE)) {
                    Assert.assertEquals(FieldAccessType.INVISIBLE, actualFieldAccessType);
                } else {
                    Assert.assertNull(actualFieldAccessType);
                }
            }
        }
    }

    // 递归方法：生成所有非空子集组合
    private void generateCombinations(List<FieldAccessType> elements, int start, List<FieldAccessType> currentCombination, List<List<FieldAccessType>> allCombinations) {
        if (!currentCombination.isEmpty()) {
            allCombinations.add(new ArrayList<>(currentCombination));
        }
        for (int i = start; i < elements.size(); i++) {
            currentCombination.add(elements.get(i));
            generateCombinations(elements, i + 1, currentCombination, allCombinations);
            currentCombination.remove(currentCombination.size() - 1);
        }
    }

    // 递归方法：生成一个组合的所有排列
    private void generatePermutations(List<FieldAccessType> elements, int index, List<List<FieldAccessType>> allPermutations) {
        if (index == elements.size() - 1) {
            allPermutations.add(new ArrayList<>(elements)); // 添加当前排列
            return;
        }
        for (int i = index; i < elements.size(); i++) {
            swap(elements, index, i); // 交换元素
            generatePermutations(elements, index + 1, allPermutations); // 递归生成剩余元素的排列
            swap(elements, index, i); // 还原交换
        }
    }

    // 辅助方法：交换列表中的两个元素
    private void swap(List<FieldAccessType> list, int i, int j) {
        FieldAccessType temp = list.get(i);
        list.set(i, list.get(j));
        list.set(j, temp);
    }
}
