package io.terminus.trantor2.permission.runtime.impl.service.evaluate;

import io.terminus.iam.api.enums.permission.AuthorizationEffect;
import io.terminus.iam.api.request.permission.v2.PermissionAssignFindParams;
import io.terminus.iam.api.response.permission.v2.PermissionAssign;
import io.terminus.trantor2.iam.cache.IAMCache;
import io.terminus.trantor2.iam.cache.PermissionCacheManager;
import io.terminus.trantor2.iam.cache.TrantorPermissionAssignCache;
import io.terminus.trantor2.iam.service.TrantorIAMPermissionAssignService;
import io.terminus.trantor2.permission.api.common.cache.PortalToIamAppConverter;
import io.terminus.trantor2.permission.api.common.consts.PermissionConstants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

/**
 * {@link RolePermissionEvaluateServiceImpl} 单元测试
 *
 * <AUTHOR>
 * 2025/8/11 09:54
 **/
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class RolePermissionEvaluateServiceImplTest {

    @InjectMocks
    private RolePermissionEvaluateServiceImpl rolePermissionEvaluateService;

    @Mock
    private PermissionCacheManager permissionCacheManager;

    @Mock
    private TrantorIAMPermissionAssignService iamPermissionAssignService;

    @Mock
    private PortalToIamAppConverter portalToIamAppConverter;

    @Mock
    private TrantorPermissionAssignCache permissionAssignCache;

    private static final String TEST_PERMISSION_KEY = "test:permission:key";
    private static final String TEST_PORTAL_KEY = "test-portal";
    private static final Long TEST_IAM_APP_ID = 123L;
    private static final List<Long> TEST_ROLE_IDS = Arrays.asList(1L, 2L, 3L);
    private static final Long TEST_ROLE_ID = 1L;
    private static final List<String> TEST_PERMISSION_KEYS = Arrays.asList("perm1", "perm2", "perm3");

    @BeforeEach
    void setUp() {
        when(permissionCacheManager.getCache(IAMCache.Type.PERMISSION_ASSIGN)).thenReturn(permissionAssignCache);
        lenient().when(portalToIamAppConverter.getIamAppIdByPortalCode(anyString())).thenReturn(TEST_IAM_APP_ID);
    }

    @Test
    void evaluate_WithRoleIds_ShouldReturnCorrectPermissionMap() {
        // 准备测试数据
        List<PermissionAssign> permissionAssigns = Arrays.asList(
                createPermissionAssign("1", AuthorizationEffect.ALLOW),
                createPermissionAssign("2", AuthorizationEffect.DENIED),
                createPermissionAssign("3", AuthorizationEffect.ALLOW)
        );

        // 模拟缓存行为
        when(permissionAssignCache.getValue(any(PermissionAssignFindParams.class), any()))
                .thenReturn(permissionAssigns);

        // 执行测试
        Map<Long, Boolean> result = rolePermissionEvaluateService.evaluate(TEST_PERMISSION_KEY, TEST_PORTAL_KEY, TEST_ROLE_IDS);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.get(1L));
        assertFalse(result.get(2L));
        assertTrue(result.get(3L));

        // 验证调用
        verify(portalToIamAppConverter).getIamAppIdByPortalCode(TEST_PORTAL_KEY);
        verify(permissionAssignCache, times(1)).getValue(any(PermissionAssignFindParams.class), any());
    }

    @Test
    void evaluate_WithRoleIds_ShouldHandleEmptyRoleIds() {
        // 准备测试数据
        List<Long> emptyRoleIds = Collections.emptyList();

        // 执行测试
        Map<Long, Boolean> result = rolePermissionEvaluateService.evaluate(TEST_PERMISSION_KEY, TEST_PORTAL_KEY, emptyRoleIds);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证调用 - 即使roleIds为空，portalToIamAppConverter仍然会被调用
        verify(portalToIamAppConverter).getIamAppIdByPortalCode(TEST_PORTAL_KEY);
        // 由于roleIds为空，不会调用缓存
        verifyNoInteractions(permissionAssignCache);
    }

    @Test
    void evaluate_WithRoleIds_ShouldHandleDuplicateRoleIds() {
        // 准备测试数据 - 包含重复的角色ID
        List<Long> duplicateRoleIds = Arrays.asList(1L, 2L, 1L, 3L, 2L);

        List<PermissionAssign> permissionAssigns = Arrays.asList(
                createPermissionAssign("1", AuthorizationEffect.ALLOW),
                createPermissionAssign("2", AuthorizationEffect.DENIED),
                createPermissionAssign("3", AuthorizationEffect.ALLOW)
        );

        when(permissionAssignCache.getValue(any(PermissionAssignFindParams.class), any()))
                .thenReturn(permissionAssigns);

        // 执行测试
        Map<Long, Boolean> result = rolePermissionEvaluateService.evaluate(TEST_PERMISSION_KEY, TEST_PORTAL_KEY, duplicateRoleIds);

        // 验证结果 - 应该去重
        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.get(1L));
        assertFalse(result.get(2L));
        assertTrue(result.get(3L));
    }

    @Test
    void evaluate_WithRoleIds_ShouldHandleLargeRoleIdsList() {
        // 准备测试数据 - 超过分片大小的角色ID列表
        List<Long> largeRoleIds = new ArrayList<>();
        for (int i = 1; i <= PermissionConstants.PARTITION_SIZE + 10; i++) {
            largeRoleIds.add((long) i);
        }

        List<PermissionAssign> permissionAssigns = Arrays.asList(
                createPermissionAssign("1", AuthorizationEffect.ALLOW),
                createPermissionAssign("500", AuthorizationEffect.DENIED),
                createPermissionAssign("600", AuthorizationEffect.ALLOW)
        );

        when(permissionAssignCache.getValue(any(PermissionAssignFindParams.class), any()))
                .thenReturn(permissionAssigns);

        // 执行测试
        Map<Long, Boolean> result = rolePermissionEvaluateService.evaluate(TEST_PERMISSION_KEY, TEST_PORTAL_KEY, largeRoleIds);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.size() > 0);

        // 验证调用 - 应该被分片处理
        verify(portalToIamAppConverter).getIamAppIdByPortalCode(TEST_PORTAL_KEY);
        verify(permissionAssignCache, atLeastOnce()).getValue(any(PermissionAssignFindParams.class), any());
    }

    @Test
    void evaluate_WithRoleIds_ShouldHandleCacheMiss() {
        // 准备测试数据
        List<PermissionAssign> permissionAssigns = Arrays.asList(
                createPermissionAssign("1", AuthorizationEffect.ALLOW)
        );

        // 模拟缓存未命中，需要调用服务
        when(permissionAssignCache.getValue(any(PermissionAssignFindParams.class), any()))
                .thenAnswer(invocation -> {
                    // 获取第二个参数（Supplier）
                    java.util.function.Supplier<List<PermissionAssign>> supplier = invocation.getArgument(1);
                    return supplier.get();
                });

        when(iamPermissionAssignService.findPermissionAssign(any(PermissionAssignFindParams.class)))
                .thenReturn(permissionAssigns);

        // 执行测试
        Map<Long, Boolean> result = rolePermissionEvaluateService.evaluate(TEST_PERMISSION_KEY, TEST_PORTAL_KEY, TEST_ROLE_IDS);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.get(1L));

        // 验证调用
        verify(portalToIamAppConverter).getIamAppIdByPortalCode(TEST_PORTAL_KEY);
        verify(permissionAssignCache).getValue(any(PermissionAssignFindParams.class), any());
        verify(iamPermissionAssignService).findPermissionAssign(any(PermissionAssignFindParams.class));
    }

    @Test
    void evaluate_WithRoleIds_ShouldHandleException() {
        // 模拟服务抛出异常
        when(permissionAssignCache.getValue(any(PermissionAssignFindParams.class), any()))
                .thenThrow(new RuntimeException("测试异常"));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            rolePermissionEvaluateService.evaluate(TEST_PERMISSION_KEY, TEST_PORTAL_KEY, TEST_ROLE_IDS);
        });

        // 验证调用
        verify(portalToIamAppConverter).getIamAppIdByPortalCode(TEST_PORTAL_KEY);
        verify(permissionAssignCache).getValue(any(PermissionAssignFindParams.class), any());
    }

    @Test
    void evaluate_WithPermissionKeys_ShouldReturnCorrectPermissionMap() {
        // 准备测试数据
        List<PermissionAssign> permissionAssigns = Arrays.asList(
                createPermissionAssignWithKey("perm1", AuthorizationEffect.ALLOW),
                createPermissionAssignWithKey("perm2", AuthorizationEffect.DENIED),
                createPermissionAssignWithKey("perm3", AuthorizationEffect.ALLOW)
        );

        when(permissionAssignCache.getValue(any(PermissionAssignFindParams.class), any()))
                .thenReturn(permissionAssigns);

        // 执行测试
        Map<String, Boolean> result = rolePermissionEvaluateService.evaluate(TEST_PERMISSION_KEYS, TEST_PORTAL_KEY, TEST_ROLE_ID);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.get("perm1"));
        assertFalse(result.get("perm2"));
        assertTrue(result.get("perm3"));

        // 验证调用
        verify(portalToIamAppConverter).getIamAppIdByPortalCode(TEST_PORTAL_KEY);
        verify(permissionAssignCache, times(1)).getValue(any(PermissionAssignFindParams.class), any());
    }

    @Test
    void evaluate_WithPermissionKeys_ShouldHandleEmptyPermissionKeys() {
        // 准备测试数据
        List<String> emptyPermissionKeys = Collections.emptyList();

        // 执行测试
        Map<String, Boolean> result = rolePermissionEvaluateService.evaluate(emptyPermissionKeys, TEST_PORTAL_KEY, TEST_ROLE_ID);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证调用 - 即使permissionKeys为空，portalToIamAppConverter仍然会被调用
        verify(portalToIamAppConverter).getIamAppIdByPortalCode(TEST_PORTAL_KEY);
        // 由于permissionKeys为空，不会调用缓存
        verifyNoInteractions(permissionAssignCache);
    }

    @Test
    void evaluate_WithPermissionKeys_ShouldHandleDuplicatePermissionKeys() {
        // 准备测试数据 - 包含重复的权限key
        List<String> duplicatePermissionKeys = Arrays.asList("perm1", "perm2", "perm1", "perm3", "perm2");

        List<PermissionAssign> permissionAssigns = Arrays.asList(
                createPermissionAssignWithKey("perm1", AuthorizationEffect.ALLOW),
                createPermissionAssignWithKey("perm2", AuthorizationEffect.DENIED),
                createPermissionAssignWithKey("perm3", AuthorizationEffect.ALLOW)
        );

        when(permissionAssignCache.getValue(any(PermissionAssignFindParams.class), any()))
                .thenReturn(permissionAssigns);

        // 执行测试
        Map<String, Boolean> result = rolePermissionEvaluateService.evaluate(duplicatePermissionKeys, TEST_PORTAL_KEY, TEST_ROLE_ID);

        // 验证结果 - 应该去重
        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.get("perm1"));
        assertFalse(result.get("perm2"));
        assertTrue(result.get("perm3"));
    }

    @Test
    void evaluate_WithPermissionKeys_ShouldHandleLargePermissionKeysList() {
        // 准备测试数据 - 超过分片大小的权限key列表
        List<String> largePermissionKeys = new ArrayList<>();
        for (int i = 1; i <= PermissionConstants.PARTITION_SIZE + 10; i++) {
            largePermissionKeys.add("perm" + i);
        }

        List<PermissionAssign> permissionAssigns = Arrays.asList(
                createPermissionAssignWithKey("perm1", AuthorizationEffect.ALLOW),
                createPermissionAssignWithKey("perm500", AuthorizationEffect.DENIED),
                createPermissionAssignWithKey("perm600", AuthorizationEffect.ALLOW)
        );

        when(permissionAssignCache.getValue(any(PermissionAssignFindParams.class), any()))
                .thenReturn(permissionAssigns);

        // 执行测试
        Map<String, Boolean> result = rolePermissionEvaluateService.evaluate(largePermissionKeys, TEST_PORTAL_KEY, TEST_ROLE_ID);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.size() > 0);

        // 验证调用 - 应该被分片处理
        verify(portalToIamAppConverter).getIamAppIdByPortalCode(TEST_PORTAL_KEY);
        verify(permissionAssignCache, atLeastOnce()).getValue(any(PermissionAssignFindParams.class), any());
    }

    @Test
    void evaluate_WithPermissionKeys_ShouldHandleCacheMiss() {
        // 准备测试数据
        List<PermissionAssign> permissionAssigns = Arrays.asList(
                createPermissionAssignWithKey("perm1", AuthorizationEffect.ALLOW)
        );

        // 模拟缓存未命中，需要调用服务
        when(permissionAssignCache.getValue(any(PermissionAssignFindParams.class), any()))
                .thenAnswer(invocation -> {
                    java.util.function.Supplier<List<PermissionAssign>> supplier = invocation.getArgument(1);
                    return supplier.get();
                });

        when(iamPermissionAssignService.findPermissionAssign(any(PermissionAssignFindParams.class)))
                .thenReturn(permissionAssigns);

        // 执行测试
        Map<String, Boolean> result = rolePermissionEvaluateService.evaluate(TEST_PERMISSION_KEYS, TEST_PORTAL_KEY, TEST_ROLE_ID);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.get("perm1"));

        // 验证调用
        verify(portalToIamAppConverter).getIamAppIdByPortalCode(TEST_PORTAL_KEY);
        verify(permissionAssignCache).getValue(any(PermissionAssignFindParams.class), any());
        verify(iamPermissionAssignService).findPermissionAssign(any(PermissionAssignFindParams.class));
    }

    @Test
    void evaluate_WithPermissionKeys_ShouldHandleException() {
        // 模拟服务抛出异常
        when(permissionAssignCache.getValue(any(PermissionAssignFindParams.class), any()))
                .thenThrow(new RuntimeException("测试异常"));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            rolePermissionEvaluateService.evaluate(TEST_PERMISSION_KEYS, TEST_PORTAL_KEY, TEST_ROLE_ID);
        });

        // 验证调用
        verify(portalToIamAppConverter).getIamAppIdByPortalCode(TEST_PORTAL_KEY);
        verify(permissionAssignCache).getValue(any(PermissionAssignFindParams.class), any());
    }

    @Test
    void evaluate_WithSingleRoleId_ShouldReturnCorrectBoolean() {
        // 准备测试数据
        List<PermissionAssign> permissionAssigns = Arrays.asList(
                createPermissionAssign("1", AuthorizationEffect.ALLOW)
        );

        when(permissionAssignCache.getValue(any(PermissionAssignFindParams.class), any()))
                .thenReturn(permissionAssigns);

        // 执行测试
        Boolean result = rolePermissionEvaluateService.evaluate(TEST_PERMISSION_KEY, TEST_PORTAL_KEY, TEST_ROLE_ID);

        // 验证结果
        assertTrue(result);

        // 验证调用
        verify(portalToIamAppConverter).getIamAppIdByPortalCode(TEST_PORTAL_KEY);
        verify(permissionAssignCache).getValue(any(PermissionAssignFindParams.class), any());
    }

    @Test
    void evaluate_WithSingleRoleId_ShouldReturnFalse_WhenNoPermission() {
        // 准备测试数据
        List<PermissionAssign> permissionAssigns = Arrays.asList(
                createPermissionAssign("1", AuthorizationEffect.DENIED)
        );

        when(permissionAssignCache.getValue(any(PermissionAssignFindParams.class), any()))
                .thenReturn(permissionAssigns);

        // 执行测试
        Boolean result = rolePermissionEvaluateService.evaluate(TEST_PERMISSION_KEY, TEST_PORTAL_KEY, TEST_ROLE_ID);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void evaluate_WithSingleRoleId_ShouldReturnFalse_WhenRoleNotFound() {
        // 准备测试数据 - 角色不存在
        List<PermissionAssign> permissionAssigns = Collections.emptyList();

        when(permissionAssignCache.getValue(any(PermissionAssignFindParams.class), any()))
                .thenReturn(permissionAssigns);

        // 执行测试
        Boolean result = rolePermissionEvaluateService.evaluate(TEST_PERMISSION_KEY, TEST_PORTAL_KEY, TEST_ROLE_ID);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void evaluate_WithSingleRoleId_ShouldReturnFalse_WhenRoleIdMismatch() {
        // 准备测试数据 - 角色ID不匹配
        List<PermissionAssign> permissionAssigns = Arrays.asList(
                createPermissionAssign("999", AuthorizationEffect.ALLOW) // 不同的角色ID
        );

        when(permissionAssignCache.getValue(any(PermissionAssignFindParams.class), any()))
                .thenReturn(permissionAssigns);

        // 执行测试
        Boolean result = rolePermissionEvaluateService.evaluate(TEST_PERMISSION_KEY, TEST_PORTAL_KEY, TEST_ROLE_ID);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void evaluate_WithSingleRoleId_ShouldHandleNullEffect() {
        // 准备测试数据 - 权限效果为null
        List<PermissionAssign> permissionAssigns = Arrays.asList(
                createPermissionAssign("1", null)
        );

        when(permissionAssignCache.getValue(any(PermissionAssignFindParams.class), any()))
                .thenReturn(permissionAssigns);

        // 执行测试
        Boolean result = rolePermissionEvaluateService.evaluate(TEST_PERMISSION_KEY, TEST_PORTAL_KEY, TEST_ROLE_ID);

        // 验证结果 - null效果应该被当作DENY
        assertFalse(result);
    }

    // 辅助方法：创建权限分配对象（用于角色ID测试）
    private PermissionAssign createPermissionAssign(String targetId, AuthorizationEffect effect) {
        PermissionAssign permissionAssign = new PermissionAssign();
        permissionAssign.setTargetId(targetId);
        permissionAssign.setEffect(effect);
        permissionAssign.setPermissionKey(TEST_PERMISSION_KEY);
        return permissionAssign;
    }

    // 辅助方法：创建权限分配对象（用于权限key测试）
    private PermissionAssign createPermissionAssignWithKey(String permissionKey, AuthorizationEffect effect) {
        PermissionAssign permissionAssign = new PermissionAssign();
        permissionAssign.setTargetId(String.valueOf(TEST_ROLE_ID));
        permissionAssign.setEffect(effect);
        permissionAssign.setPermissionKey(permissionKey);
        return permissionAssign;
    }
}
