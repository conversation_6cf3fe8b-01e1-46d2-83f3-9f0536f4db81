[{"id": 617030530525125, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 584138222566917, "permissionKey": "ERP_SCM$bjdwfcbeqbyhcd", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "VALUE_RANGE", "valueRange": [{"valueType": "SAMPLE_VALUE", "values": ["61"]}, {"valueType": "SAMPLE_VALUE", "values": ["62"]}, {"valueType": "SAMPLE_VALUE", "values": ["59"]}, {"valueType": "SAMPLE_VALUE", "values": ["60"]}]}}, {"id": 608884892815429, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 584141789507077, "permissionKey": "Jose_dev_mod$test_data_role", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "VALUE_RANGE", "valueRange": [{"valueType": "SAMPLE_VALUE", "values": ["2002001"]}, {"valueType": "SAMPLE_VALUE", "values": ["2001005"]}, {"valueType": "SAMPLE_VALUE", "values": ["2001004"]}, {"valueType": "SAMPLE_VALUE", "values": ["2001003"]}, {"valueType": "SAMPLE_VALUE", "values": ["2001002"]}, {"valueType": "SAMPLE_VALUE", "values": ["2001001"]}, {"valueType": "SAMPLE_VALUE", "values": ["2000002"]}, {"valueType": "SAMPLE_VALUE", "values": ["2000001"]}]}}, {"id": 598978279356106, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 587664403951877, "permissionKey": "zjt_test$de", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "VALUE_RANGE", "valueRange": [{"valueType": "SPECIFY_VALUE", "values": ["1"]}]}}, {"id": 604730516228549, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 587664769876229, "permissionKey": "zjt_test$ces", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "VALUE_RANGE", "valueRange": [{"valueType": "SPECIFY_VALUE", "values": ["1"]}, {"valueType": "SPECIFY_VALUE", "values": ["2"]}, {"valueType": "SPECIFY_VALUE", "values": ["3"]}]}}, {"id": 598978279356101, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 588654127194373, "permissionKey": "ERP_GEN$zdwd3", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "VALUE_RANGE", "valueRange": [{"valueType": "SPECIFY_VALUE", "values": ["1"]}]}}, {"id": 602146163448965, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 588727187812613, "permissionKey": "Jose_dev_mod$gen_data_auth_cf:zdgz2", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "CONDITION_RULE", "conditions": [{"key": "6agRvdxby0SLUrvk21Yh5", "conditions": [{"key": "pnsWrIhtd-paAngVJQSaN", "nextLogicalOperator": "OR", "param": "{\"modelKey\":\"Jose_dev_mod$user\",\"modelName\":\"Jose_dev_mod$user\",\"columnKey\":\"createdBy\",\"columnName\":\"创建人\",\"columnType\":\"Model\",\"leftValue\":{\"type\":\"VarValue\",\"varValue\":[{\"valueKey\":\"createdBy\",\"valueName\":\"创建人\",\"modelAlias\":\"Jose_dev_mod$gen_data_auth_cf\",\"relatedModel\":{\"modelKey\":\"Jose_dev_mod$user\",\"modelAlias\":\"Jose_dev_mod$user\",\"modelName\":\"Jose_dev_mod$user\"}}],\"valueType\":\"MODEL\",\"fieldType\":\"Model\"}}", "operator": "EQ", "conditionValues": [{"valueType": "SPECIFY_VALUE", "values": ["601818673028229"]}]}, {"key": "o2eEPLoACORFP_sNOOCsJ", "param": "{\"modelKey\":\"Jose_dev_mod$gen_data_auth_cf\",\"columnKey\":\"name2\",\"columnName\":\"name2\",\"columnType\":\"Text\",\"leftValue\":{\"type\":\"VarValue\",\"varValue\":[{\"valueKey\":\"name2\",\"valueName\":\"name2\",\"modelAlias\":\"Jose_dev_mod$gen_data_auth_cf\"}],\"valueType\":\"MODEL\",\"fieldType\":\"Text\"}}", "operator": "IN", "conditionValues": [{"valueType": "SPECIFY_VALUE", "values": ["rrrrr"]}]}]}]}}, {"id": 598978279356112, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 588727491174661, "permissionKey": "Jose_dev_mod$zdwd2", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "VALUE_RANGE", "valueRange": [{"valueType": "SPECIFY_VALUE", "values": ["1"]}]}}, {"id": 607500560300485, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 589360785662213, "permissionKey": "ERP_SCM$pur_po_head_tr:pur_po_head_tr:view", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "CONDITION_RULE"}}, {"id": 606840503792773, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 590138017423621, "permissionKey": "Jose_dev_mod$zdwd4", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "VALUE_RANGE", "valueRange": [{"valueType": "SAMPLE_VALUE", "values": ["2001003"]}, {"valueType": "SAMPLE_VALUE", "values": ["2001004"]}, {"valueType": "SAMPLE_VALUE", "values": ["2001005"]}, {"valueType": "SAMPLE_VALUE", "values": ["2002001"]}]}}, {"id": 598978279356111, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 590138017423622, "permissionKey": "Jose_dev_mod$zdwd5", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "VALUE_RANGE", "valueRange": [{"valueType": "SPECIFY_VALUE", "values": ["1"]}]}}, {"id": 598980568970950, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 590138017423623, "permissionKey": "Jose_dev_mod$zdwd7", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "VALUE_RANGE", "valueRange": [{"valueType": "SPECIFY_VALUE", "values": ["13"]}]}}, {"id": 598980568970949, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 590138017423624, "permissionKey": "Jose_dev_mod$zdwd8", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "VALUE_RANGE", "valueRange": [{"valueType": "SPECIFY_VALUE", "values": ["13"]}]}}, {"id": 598988146013893, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 590138017423625, "permissionKey": "Jose_dev_mod$zdwd9", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "VALUE_RANGE", "valueRange": [{"valueType": "SAMPLE_VALUE", "values": ["2002001"]}, {"valueType": "SAMPLE_VALUE", "values": ["2001005"]}]}}, {"id": 608470558468549, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 596098118424261, "permissionKey": "ERP_SCM$pakho_test1", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "VALUE_RANGE", "valueRange": [{"valueType": "SAMPLE_VALUE", "values": ["2044001"]}, {"valueType": "SAMPLE_VALUE", "values": ["2045001"]}, {"valueType": "SAMPLE_VALUE", "values": ["2030010"]}, {"valueType": "SAMPLE_VALUE", "values": ["2030011"]}, {"valueType": "SAMPLE_VALUE", "values": ["2030012"]}, {"valueType": "SAMPLE_VALUE", "values": ["2035002"]}, {"valueType": "SAMPLE_VALUE", "values": ["2030014"]}, {"valueType": "SAMPLE_VALUE", "values": ["2036005"]}, {"valueType": "SAMPLE_VALUE", "values": ["2038001"]}, {"valueType": "SAMPLE_VALUE", "values": ["2038002"]}, {"valueType": "SAMPLE_VALUE", "values": ["2030013"]}, {"valueType": "SAMPLE_VALUE", "values": ["2036003"]}]}}, {"id": 6018514697493811, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 601789204667525, "permissionKey": "TERP_MIGRATE$misc_article:zdgz", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "CONDITION_RULE", "conditions": [{"key": "6agRvdxby0SLUrvk21Yh5", "conditions": [{"key": "pnsWrIhtd-paAngVJQSaN", "nextLogicalOperator": "OR", "param": "{\"modelKey\":\"Jose_dev_mod$user\",\"modelName\":\"Jose_dev_mod$user\",\"columnKey\":\"createdBy\",\"columnName\":\"创建人\",\"columnType\":\"Model\",\"leftValue\":{\"type\":\"VarValue\",\"varValue\":[{\"valueKey\":\"createdBy\",\"valueName\":\"创建人\",\"modelAlias\":\"Jose_dev_mod$gen_data_auth_cf\",\"relatedModel\":{\"modelKey\":\"Jose_dev_mod$user\",\"modelAlias\":\"Jose_dev_mod$user\",\"modelName\":\"Jose_dev_mod$user\"}}],\"valueType\":\"MODEL\",\"fieldType\":\"Model\"}}", "operator": "EQ", "conditionValues": [{"valueType": "SPECIFY_VALUE", "values": ["601818673028229"]}]}, {"key": "o2eEPLoACORFP_sNOOCsJ", "param": "{\"modelKey\":\"Jose_dev_mod$gen_data_auth_cf\",\"columnKey\":\"name2\",\"columnName\":\"name2\",\"columnType\":\"Text\",\"leftValue\":{\"type\":\"VarValue\",\"varValue\":[{\"valueKey\":\"name2\",\"valueName\":\"name2\",\"modelAlias\":\"Jose_dev_mod$gen_data_auth_cf\"}],\"valueType\":\"MODEL\",\"fieldType\":\"Text\"}}", "operator": "IN", "conditionValues": [{"valueType": "VALUE_RANGE", "variableValue": {"name": "null", "value": "Jose_dev_mod$test_data_role"}}]}]}]}}, {"id": 604677074422213, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 602143253888133, "permissionKey": "zjt_test$gen_field_perm_test_md:aaa", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "CONDITION_RULE", "conditions": [{"key": "fr5xSBiJdQ20ZsOkDzrO9", "conditions": [{"key": "hwqZZoU3lLhvaZHRwAFJ4", "param": "{\"modelKey\":\"zjt_test$gen_field_perm_test_md\",\"columnKey\":\"aaa\",\"columnName\":\"aaa\",\"columnType\":\"Text\",\"leftValue\":{\"type\":\"VarValue\",\"varValue\":[{\"valueKey\":\"aaa\",\"valueName\":\"aaa\",\"modelAlias\":\"zjt_test$gen_field_perm_test_md\"}],\"valueType\":\"MODEL\",\"fieldType\":\"Text\"}}", "operator": "EQ", "conditionValues": [{"valueType": "SPECIFY_VALUE", "values": ["2233"]}]}]}]}}, {"id": 604640237533637, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 604640236665285, "permissionKey": "Jose_dev_mod$zdgzwd1", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "VALUE_RANGE", "valueRange": [{"valueType": "SPECIFY_VALUE", "values": ["1"]}]}}, {"id": 606840503792774, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 606840503596165, "permissionKey": "sys_common$employee", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "VALUE_RANGE", "valueRange": [{"valueType": "SPECIFY_VALUE", "values": ["1"]}]}}, {"id": 607481591581126, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 607481591056837, "permissionKey": "sys_common$ss", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "VALUE_RANGE", "valueRange": [{"valueType": "SPECIFY_VALUE", "values": ["3"]}]}}, {"id": 607481591581125, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 607481591056838, "permissionKey": "sys_common$3123123", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "VALUE_RANGE", "valueRange": [{"valueType": "SPECIFY_VALUE", "values": ["3"]}]}}, {"id": 608889163436101, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 608873383551045, "permissionKey": "Jose_dev_mod$gen_data_auth_cf:aaa", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "CONDITION_RULE", "conditions": [{"key": "XcXMPpXjo5GFp0Iaw9Y0g", "conditions": [{"key": "joDF9WgMaN2emLlAoZbNT", "param": "{\"modelKey\":\"Jose_dev_mod$gen_data_auth_cf\",\"columnKey\":\"id\",\"columnName\":\"ID\",\"columnType\":\"Number\",\"leftValue\":{\"type\":\"VarValue\",\"varValue\":[{\"valueKey\":\"id\",\"valueName\":\"ID\",\"modelAlias\":\"Jose_dev_mod$gen_data_auth_cf\"}],\"valueType\":\"MODEL\",\"fieldType\":\"Number\"}}", "operator": "IN", "conditionValues": [{"valueType": "VALUE_RANGE", "variableValue": {"name": "null", "value": "Jose_dev_mod$24maoy"}}]}]}]}}, {"id": 608888049832005, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 608888049737797, "permissionKey": "Jose_dev_mod$24maoy", "effect": "ALLOW", "props": {"permissionType": "DATA_PERMISSION", "propsType": "VALUE_RANGE", "valueRange": [{"valueType": "SAMPLE_VALUE", "values": ["2000001"]}, {"valueType": "SAMPLE_VALUE", "values": ["2000002"]}]}}, {"id": 617307199296965, "sourceApplicationId": 517598287198469, "effectiveApplicationId": 517598287198469, "targetType": "ROLE", "targetId": "589790338732293", "permissionType": "DATA_PERMISSION", "permissionId": 616726918532869, "permissionKey": "Jose_dev_mod$gen_data_auth_cf:252525", "effect": "ALLOW", "props": {"propsType": "CONDITION_RULE", "conditions": [{"key": "enOUMXyD0nWCDoaL8ZkD6", "conditions": [{"key": "D_afi9Hc41cwVuGgyEMNU", "param": "{\"modelKey\":\"Jose_dev_mod$gen_data_auth_cf\",\"columnKey\":\"id\",\"columnName\":\"ID\",\"columnType\":\"Number\",\"leftValue\":{\"type\":\"VarValue\",\"varValue\":[{\"valueKey\":\"id\",\"valueName\":\"ID\",\"modelAlias\":\"Jose_dev_mod$gen_data_auth_cf\"}],\"valueType\":\"MODEL\",\"fieldType\":\"Number\"}}", "operator": "IN", "conditionValues": [{"valueType": "VALUE_RANGE", "variableValue": {"name": "赵迪模型固定值维度", "value": "Jose_dev_mod$zddatatest"}}]}]}], "permissionType": "DATA_PERMISSION"}}]