{"oid": "0f114b7f229c408ed026e987fecb4c2d810f7a40cadd3303bfc8e3f9180afd6e", "key": "agent1", "name": "招投标 AI 专家_bid", "description": "", "access": "Private", "id": 34143799, "createdBy": 435399008359109, "createdAt": 1748576838000, "updatedBy": 435399008359109, "updatedAt": 1748576838000, "parentKey": "AI$ungroup", "path": "", "teamId": 123, "teamCode": "PJ0724", "appId": 33926599, "metaType": "AIAgent", "props": {"agent": {"type": "Agent", "key": "agent1", "name": "招投标 AI 专家_bid", "props": {"permissionKey": "agent1_permission", "type": "AgentProperties", "model": {"providerType": "Azure", "name": "gpt-4.1", "type": "text_to_text", "setting": {"frequencyPenalty": 0.0, "presencePenalty": 0.0, "temperature": 0.0, "topP": 1, "chatRounds": 10, "maxTokens": 4096}}, "greetings": "你好，我是智能体，请问我有什么可以帮助你的吗？", "userQuestions": [], "userQuestionsSuggest": false, "userQuestionsCustom": false, "systemPrompt": "你是一个招投标专家，能够处理招投标领域所有问题。", "skillTools": [{"key": "AI$PURCHASE_REQ_PAGING_DATA_SERVICE", "name": "采购需求单-分页数据服务", "desc": "需要查询采购需求单时使用该服务，pageNo默认 为 1，pageSize 默认为 20", "type": "service", "visible": true, "input": [{"fieldKey": "pageable", "fieldAlias": "pageable", "fieldName": "分页设置", "fieldType": "Pageable", "elements": [{"fieldKey": "conditionGroup", "fieldAlias": "conditionGroup", "fieldName": "条件组", "fieldType": "ConditionGroup"}, {"fieldKey": "conditionItems", "fieldAlias": "conditionItems", "fieldName": "简化版条件组", "fieldType": "ConditionItems", "elements": [{"fieldKey": "conditions", "fieldAlias": "conditions", "fieldName": "条件对象", "fieldType": "Model", "relatedModel": {"modelKey": "AI$purchase_request", "modelAlias": "AI$purchase_request", "modelName": "AI$purchase_request"}}, {"fieldKey": "logicOperator", "fieldAlias": "logicOperator", "fieldName": "逻辑运算符", "fieldType": "Text"}]}, {"fieldKey": "sortOrders", "fieldAlias": "sortOrders", "fieldName": "字段排序", "fieldType": "Array"}, {"fieldKey": "pageNo", "fieldAlias": "pageNo", "fieldName": "页码", "fieldType": "Number"}, {"fieldKey": "pageSize", "fieldAlias": "pageSize", "fieldName": "每页数量", "fieldType": "Number"}, {"fieldKey": "keyword", "fieldAlias": "keyword", "fieldName": "模糊搜索", "fieldType": "Text"}, {"fieldKey": "needTotal", "fieldAlias": "needTotal", "fieldName": "查询总数", "fieldType": "Boolean"}]}]}, {"key": "AI$procurement_requirement_save_data_service", "name": "采购需求单-保存数据服务", "desc": "", "type": "service", "visible": true, "input": [{"fieldKey": "request", "fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "relatedModel": {"modelKey": "AI$purchase_request", "modelAlias": "AI$purchase_request", "modelName": "AI$purchase_request"}}]}, {"key": "AI$tender_save_data_service", "name": "招标单-保存数据服务", "desc": "", "type": "service", "visible": true, "input": [{"fieldKey": "request", "fieldAlias": "request", "fieldName": "request", "fieldType": "Model", "relatedModel": {"modelKey": "AI$tender_document", "modelAlias": "AI$tender_document", "modelName": "AI$tender_document"}}]}, {"key": "AI$bid_page_data_service", "name": "招标单-分页数据服务", "desc": "", "type": "service", "visible": true, "input": [{"fieldKey": "pageable", "fieldAlias": "pageable", "fieldName": "分页设置", "fieldType": "Pageable", "elements": [{"fieldKey": "conditionGroup", "fieldAlias": "conditionGroup", "fieldName": "条件组", "fieldType": "ConditionGroup"}, {"fieldKey": "conditionItems", "fieldAlias": "conditionItems", "fieldName": "简化版条件组", "fieldType": "ConditionItems", "elements": [{"fieldKey": "conditions", "fieldAlias": "conditions", "fieldName": "条件对象", "fieldType": "Model", "relatedModel": {"modelKey": "AI$tender_document", "modelAlias": "AI$tender_document", "modelName": "AI$tender_document"}}, {"fieldKey": "logicOperator", "fieldAlias": "logicOperator", "fieldName": "逻辑运算符", "fieldType": "Text"}]}, {"fieldKey": "sortOrders", "fieldAlias": "sortOrders", "fieldName": "字段排序", "fieldType": "Array"}, {"fieldKey": "pageNo", "fieldAlias": "pageNo", "fieldName": "页码", "fieldType": "Number"}, {"fieldKey": "pageSize", "fieldAlias": "pageSize", "fieldName": "每页数量", "fieldType": "Number"}, {"fieldKey": "keyword", "fieldAlias": "keyword", "fieldName": "模糊搜索", "fieldType": "Text"}, {"fieldKey": "needTotal", "fieldAlias": "needTotal", "fieldName": "查询总数", "fieldType": "Boolean"}]}]}, {"key": "AI$PR_BATCH_DELETE_DATA_SERVICE", "name": "采购需求单-批量删除数据服务", "desc": "删除单条和多条都可以使用该工具，只需要传入 id", "type": "service", "visible": true, "input": [{"fieldKey": "request", "fieldAlias": "request", "fieldName": "request", "fieldType": "Object", "elements": [{"fieldKey": "ids", "fieldAlias": "ids", "fieldName": "ids", "fieldType": "Array", "element": {"fieldKey": "element", "fieldAlias": "element", "fieldName": "element", "fieldType": "Number"}}]}]}], "triggers": []}}, "isEnabled": true}}