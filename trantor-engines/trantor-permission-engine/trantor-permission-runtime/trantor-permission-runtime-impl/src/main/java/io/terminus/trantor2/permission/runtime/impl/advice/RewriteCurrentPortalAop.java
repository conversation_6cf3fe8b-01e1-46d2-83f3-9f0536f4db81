package io.terminus.trantor2.permission.runtime.impl.advice;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.module.meta.ConfigType;
import io.terminus.trantor2.module.meta.ModuleIamConfig;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.service.ConfigurationService;
import io.terminus.trantor2.module.service.ModuleQueryService;
import io.terminus.trantor2.module.service.ModuleRuntimeQueryService;
import io.terminus.trantor2.permission.impl.common.advice.RewriteCurrentPortal;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Objects;


/**
 * 重写{@link TrantorContext#getCurrentPortal()}信息
 *
 * <AUTHOR>
 * 2023/8/24 7:00 下午
 **/
@Aspect
@Component
@RequiredArgsConstructor
public class RewriteCurrentPortalAop {

    private final ModuleRuntimeQueryService moduleQueryService;
    private final ConfigurationService configurationService;
    private static final SpelExpressionParser parser = new SpelExpressionParser();
    private static final DefaultParameterNameDiscoverer nameDiscoverer = new DefaultParameterNameDiscoverer();

    @Around("@annotation(rewriteCurrentPortal)")
    public Object aound(ProceedingJoinPoint joinPoint, RewriteCurrentPortal rewriteCurrentPortal) throws Throwable {
        // 解析目标方法接收的portalCode
        String portalCode = this.parseValueBySpEL(rewriteCurrentPortal.portalCode(), joinPoint);
        if (Objects.isNull(portalCode)) {
            throw new TrantorRuntimeException("Parse annotation `@RewriteCurrentPortal` property `portalCode` failed, the parse result of portalCode is null");
        }
        String currentPortalCode = TrantorContext.getCurrentPortalOptional().map(Portal::getCode).orElse(null);
        if (portalCode.equals(currentPortalCode)) {
            // 如果目标方法接收的portalCode就是当前Portal，则不需要做后续处理
            return joinPoint.proceed();
        }
        // 记录原始的Portal信息
        Portal originalPortal = TrantorContext.getCurrentPortal();
        try {
            Portal curPortal = findPortalByCode(portalCode);
            curPortal.setI18nConfig(originalPortal.getI18nConfig());
            TrantorContext.setCurrentPortal(curPortal);
            return joinPoint.proceed();
        } finally {
            // 还原原始的Portal信息
            TrantorContext.setCurrentPortal(originalPortal);
        }
    }

    /**
     * 根据SpEL表达式从代理目标方法签名中计算实际值
     *
     * @param spEL      SpEL表达式
     * @param joinPoint 连接对象，可用于获取目标方法签名信息
     * @param <T>       T
     * @return 返回SpEL计算后的实际值
     */
    private <T> T parseValueBySpEL(String spEL, ProceedingJoinPoint joinPoint) {
        // 通过joinPoint获取被注解方法
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        // 使用spring的DefaultParameterNameDiscoverer获取方法形参名数组
        String[] paramNames = Objects.requireNonNull(nameDiscoverer.getParameterNames(method));
        // 解析过后的Spring表达式对象
        Expression expression = parser.parseExpression(spEL);
        // spring的表达式上下文对象
        EvaluationContext context = new StandardEvaluationContext();
        // 通过joinPoint获取被注解方法的形参
        Object[] args = joinPoint.getArgs();
        // 给上下文赋值
        for (int i = 0; i < args.length; i++) {
            context.setVariable(paramNames[i], args[i]);
        }
        // 表达式从上下文中计算出实际参数值
        return (T) expression.getValue(context);
    }

    private Portal findPortalByCode(String portalCode) {
        ModuleMeta moduleMeta = moduleQueryService.findByKey(portalCode);
        ModuleIamConfig moduleIamConfig = configurationService.query(moduleMeta.getTeamId(), moduleMeta.getKey(), ConfigType.Module_IAM);
        if (moduleIamConfig == null) {
            moduleIamConfig = new ModuleIamConfig();
        }
        String path = null;
        String url = moduleIamConfig.getLoginCallbackUrl();
        if (url != null && url.contains("/")) {
            path = url.substring(url.lastIndexOf('/') + 1);
        }

        return Portal.builder()
                .id(moduleMeta.getId())
                .teamId(moduleMeta.getTeamId())
                .teamCode(moduleMeta.getTeamCode())
                .code(moduleMeta.getKey())
                .name(moduleMeta.getName())
                .path(path)
                .iamEndpointId(moduleIamConfig.getIamEndPointId())
                .loginUrl(moduleIamConfig.getLoginUrl())
                .loginCallbackUrl(moduleIamConfig.getLoginCallbackUrl())
                .accessKey(moduleIamConfig.getAccessKey())
                .accessSecret(moduleIamConfig.getAccessSecret())
                .applicationKey(moduleIamConfig.getApplicationKey())
                .build();
    }
}
