package io.terminus.trantor2.permission.runtime.impl.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import io.terminus.iam.api.enums.permission.AuthorizationEffect;
import io.terminus.iam.api.enums.permission.PermissionGrantTargetType;
import io.terminus.iam.api.enums.permission.v2.PermissionType;
import io.terminus.iam.api.request.permission.v2.PermissionAssignFindParams;
import io.terminus.iam.api.response.permission.v2.PermissionAssign;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.iam.cache.CacheKey;
import io.terminus.trantor2.iam.cache.IAMCache;
import io.terminus.trantor2.iam.cache.PermissionCacheManager;
import io.terminus.trantor2.iam.cache.TrantorPermissionAssignCache;
import io.terminus.trantor2.iam.service.TrantorIAMPermissionAssignService;
import io.terminus.trantor2.ide.repository.PermissionAuthorizeViewRepo;
import io.terminus.trantor2.ide.repository.PermissionRepo;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.ResourceNodeLite;
import io.terminus.trantor2.meta.api.dto.ViewPermissionDTO;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.dto.page.Order;
import io.terminus.trantor2.meta.api.dto.page.PageReq;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.editor.util.IndexUtil;
import io.terminus.trantor2.meta.resource.BaseMeta;
import io.terminus.trantor2.meta.resource.ext.ExtModuleBaseMeta;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.service.MenuQueryService;
import io.terminus.trantor2.module.service.ModuleQueryService;
import io.terminus.trantor2.permission.ApiMeta;
import io.terminus.trantor2.permission.PermissionMeta;
import io.terminus.trantor2.permission.api.common.cache.PortalToIamAppConverter;
import io.terminus.trantor2.permission.api.common.consts.PermissionConstants;
import io.terminus.trantor2.permission.api.common.dto.ACLResourceType;
import io.terminus.trantor2.permission.api.common.dto.FunctionPermissionResourceProps;
import io.terminus.trantor2.permission.api.common.dto.PermissionAuthorizationVO;
import io.terminus.trantor2.permission.api.common.dto.PermissionResourceDTO;
import io.terminus.trantor2.permission.api.common.dto.PermissionVO;
import io.terminus.trantor2.permission.api.common.service.AccessControlQueryService;
import io.terminus.trantor2.permission.impl.common.util.PermAuthViewGenerator;
import io.terminus.trantor2.permission.impl.common.util.PermissionUtils;
import io.terminus.trantor2.permission.impl.common.util.PermissionVOFactory;
import io.terminus.trantor2.permission.impl.common.util.TrantorPermissionUtils;
import io.terminus.trantor2.permission.props.PermissionAuthorizeViewProps;
import io.terminus.trantor2.permission.runtime.api.dto.FunctionPermissionWithAssign;
import io.terminus.trantor2.permission.runtime.api.dto.FunctionPermissionWithAssignFindRequest;
import io.terminus.trantor2.permission.runtime.api.dto.PermissionWithAssignFindRequest;
import io.terminus.trantor2.permission.runtime.api.dto.RoleApiAccessControl;
import io.terminus.trantor2.permission.runtime.api.dto.RolePermissionRequest;
import io.terminus.trantor2.permission.runtime.api.dto.RoleResourceAccessControlPageRequest;
import io.terminus.trantor2.permission.runtime.api.service.IPermissionRuntimeService;
import io.terminus.trantor2.permission.runtime.api.service.RoleFunctionPermissionService;
import io.terminus.trantor2.permission.runtime.api.service.evaluate.UserPermissionEvaluateService;
import io.terminus.trantor2.permission.runtime.impl.cache.v2.PortalResourceTreeCache;
import io.terminus.trantor2.permission.runtime.impl.util.PermissionMetaConverter;
import io.terminus.trantor2.permission.runtime.impl.util.PermissionRuntimeUtils;
import io.terminus.trantor2.properties.PermissionProperties;
import io.terminus.trantor2.scene.repo.SceneRepo;
import io.terminus.trantor2.scene.service.SceneQueryService;
import io.terminus.trantor2.service.common.meta.AIAgentMeta;
import io.terminus.trantor2.service.management.repo.AIAgentRepo;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024/5/28 15:58
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class RoleFunctionPermissionServiceImpl implements RoleFunctionPermissionService {

    private final TrantorIAMPermissionAssignService trantorIAMPermissionAssignService;
    private final PermissionRepo permissionRepo;
    private final PortalToIamAppConverter portalToIamAppConverter;
    private final MetaQueryService metaQueryService;
    private final PermissionCacheManager permissionCacheManager;
    private final AccessControlQueryService accessControlQueryService;
    private final PermissionAuthorizeViewRepo permissionAuthorizeViewRepo;
    private final PermissionProperties permissionProperties;
    private final ModuleQueryService moduleQueryService;
    private final MenuQueryService menuQueryService;
    private final SceneQueryService sceneQueryService;
    private final SceneRepo sceneRepo;
    private final PortalResourceTreeCache portalResourceTreeCache;
    private final UserPermissionEvaluateService userPermissionEvaluateService;
    private final IPermissionRuntimeService permissionRuntimeService;
    private final AIAgentRepo agentRepo;

    @Override
    public List<PermissionResourceDTO> findFuncPermissionTree(@NotNull PermissionWithAssignFindRequest request) {
        ModuleMeta module = moduleQueryService.findByKey(request.getPortalCode());
        PermissionResourceDTO appPermissionResourceDTO = TrantorPermissionUtils.convertToPermissionResource(ACLResourceType.Application, module, permissionResourceDTO -> {
        });
        // 获取权限授权树（菜单-视图-按钮）
        Collection<PermissionResourceDTO> menuPermissionResourceDTOS = getPermissionTree(request.getPortalCode());

        if (permissionRuntimeService.needExecFunctionPermission()) {
            // 根据用户授权过滤权限树
            appPermissionResourceDTO.setChildren(PermissionRuntimeUtils.getFilteredByUserPermission(
                    userPermissionEvaluateService,
                    TrantorContext.getCurrentUserId(),
                    request.getPortalCode(),
                    menuPermissionResourceDTOS
            ));
        } else {
            appPermissionResourceDTO.setChildren(new ArrayList<>(menuPermissionResourceDTOS));
        }

        if (CollectionUtils.isNotEmpty(appPermissionResourceDTO.getChildren())) {
            // 查询当前角色权限授权信息
            PermissionAssignFindParams permissionAssignFindParams = new PermissionAssignFindParams();
            permissionAssignFindParams.setEffectiveApplicationIds(Collections.singletonList(portalToIamAppConverter.getIamAppIdByPortalCode(request.getPortalCode())));
            permissionAssignFindParams.setPermissionTypes(Collections.singletonList(PermissionType.FUNCTION_PERMISSION));
            permissionAssignFindParams.setTargetTypes(Collections.singletonList(PermissionGrantTargetType.ROLE));
            permissionAssignFindParams.setTargetIds(Collections.singletonList(String.valueOf(request.getRoleId())));
            List<PermissionAssign> permissionAssignList = getPermissionAssignCache().getValue(permissionAssignFindParams, () -> trantorIAMPermissionAssignService.findPermissionAssign(permissionAssignFindParams));
            Map<String, PermissionAssign> permissionKeyToPermissionAssign = permissionAssignList.stream().collect(Collectors.toMap(PermissionAssign::getPermissionKey, Function.identity()));

            // 填充权限项授权状态
            completePermissionResourceProps(appPermissionResourceDTO.getChildren(), permissionKeyToPermissionAssign);
        }

        return Collections.singletonList(appPermissionResourceDTO);
    }

    @Override
    public Map<String, List<PermissionResourceDTO>> findRefServiceSummary(List<String> resourceKeys) {
        // key to 服务keys
        Map<String, Set<String>> keyToServiceKeysMap = metaQueryService.findReferenceByKeysAndTargetType(EditUtil.ctxFromThreadLocal(), resourceKeys, MetaType.ServiceDefinition);
        Set<String> serviceKeys = keyToServiceKeysMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
        // 服务key to 权限keys
        Map<String, Set<String>> serviceKeyToPermissionKeysMap = metaQueryService.findReferenceByKeysAndTargetType(EditUtil.ctxFromThreadLocal(), serviceKeys, MetaType.Permission);
        Set<String> permissionKeys = serviceKeyToPermissionKeysMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
        Map<String, MetaTreeNodeExt> permissionKeyToMetaMap = metaQueryService.findByKeyIn(EditUtil.ctxFromThreadLocal(), permissionKeys).stream()
                .collect(Collectors.toMap(MetaTreeNode::getKey, Function.identity()));
        Map<String, MetaTreeNodeExt> serviceKeyToMetaMap = metaQueryService.findByKeyIn(EditUtil.ctxFromThreadLocal(), serviceKeys).stream()
                .collect(Collectors.toMap(MetaTreeNode::getKey, Function.identity()));
        // serviceKey -> a set of usage meta
        Map<String, List<MetaTreeNodeExt>> serviceKeyToUsageMetas = metaQueryService.findUsageMetaByTargetTypeAndTargetKeys(EditUtil.ctxFromThreadLocal(), MetaType.ServiceDefinition.name(), serviceKeys);

        Map<String, List<PermissionResourceDTO>> keyToPermissionResourceDTOMap = new HashMap<>();
        for (String resourceKey : resourceKeys) {
            List<PermissionResourceDTO> dtos = new ArrayList<>();
            // 组装资源引用的服务信息
            for (String serviceKey : keyToServiceKeysMap.getOrDefault(resourceKey, Collections.emptySet())) {
                PermissionResourceDTO dto = new PermissionResourceDTO();
                dto.setResourceType(ACLResourceType.Service);
                dto.setResourceKey(serviceKey);
                if (Objects.nonNull(serviceKeyToMetaMap.get(serviceKey))) {
                    dto.setResourceName(serviceKeyToMetaMap.get(serviceKey).getName());
                }
                // 组装服务绑定的权限项信息
                if (Objects.nonNull(serviceKeyToPermissionKeysMap.get(serviceKey))) {
                    PermissionVO permissionVO = new PermissionVO();
                    Set<String> servicePermissionKeys = serviceKeyToPermissionKeysMap.get(serviceKey);
                    if (CollectionUtils.isNotEmpty(servicePermissionKeys)) {
                        String servicePermissionKey = new ArrayList<>(servicePermissionKeys).get(0);
                        permissionVO.setKey(servicePermissionKey);
                        MetaTreeNodeExt permissionMeta = permissionKeyToMetaMap.get(servicePermissionKey);
                        if (Objects.nonNull(permissionMeta)
                                && MetaType.Permission.equals(permissionMeta.getMetaType())) {
                            permissionVO.setName(permissionMeta.getName());
                        }
                    }
                    FunctionPermissionResourceProps props = new FunctionPermissionResourceProps();
                    props.setPermission(permissionVO);
                    dto.setProps(props);
                }
                // 组装服务的被引用资源信息
                List<PermissionResourceDTO> references = serviceKeyToUsageMetas.getOrDefault(serviceKey, Collections.emptyList())
                        .stream()
                        .filter(metaTreeNodeExt -> Objects.nonNull(metaTreeNodeExt) && MetaType.View.equals(metaTreeNodeExt.getMetaType()))
                        .map(metaTreeNodeExt -> {
                            PermissionResourceDTO resourceDTO = new PermissionResourceDTO();
                            resourceDTO.setResourceType(ACLResourceType.valueOf(metaTreeNodeExt.getType()));
                            resourceDTO.setResourceKey(metaTreeNodeExt.getKey());
                            resourceDTO.setResourceName(metaTreeNodeExt.getName());
                            return resourceDTO;
                        }).collect(Collectors.toList());
                dto.setReference(references);
                dtos.add(dto);
            }
            if (CollectionUtils.isNotEmpty(dtos)) {
                keyToPermissionResourceDTOMap.put(resourceKey, dtos);
            }
        }
        return keyToPermissionResourceDTOMap;
    }

    @Override
    public Paging<FunctionPermissionWithAssign> pagingFunctionPermission(FunctionPermissionWithAssignFindRequest request) {
        Cond cond = Field.type().equal(MetaType.Permission.name());
        PageReq pageReq = PageReq.of(Math.max(request.getPageNumber() - 1, 0), request.getPageSize(), Order.byModifiedAt().desc());
        cond = tryAddFuzzyCond(cond, request.getFuzzyValue());
        Paging<PermissionMeta> paging = permissionRepo.findAll(cond, pageReq, ResourceContext.ctxFromThreadLocal());
        if (CollectionUtils.isEmpty(paging.getData())) {
            return Paging.empty();
        }
        Set<String> currentPermissionKeys = paging.getData().stream().map(BaseMeta::getKey).collect(Collectors.toSet());

        // 查询目标角色功能权限授权信息
        PermissionAssignFindParams findParams = new PermissionAssignFindParams();
        Long targetAppId = portalToIamAppConverter.getIamAppIdByPortalCode(request.getPortalCode());
        findParams.setEffectiveApplicationIds(Collections.singletonList(targetAppId));
        findParams.setPermissionTypes(Collections.singletonList(PermissionType.FUNCTION_PERMISSION));
        findParams.setTargetTypes(Collections.singletonList(PermissionGrantTargetType.ROLE));
        findParams.setTargetIds(Collections.singletonList(request.getRoleId()));
        if (CollectionUtils.isNotEmpty(currentPermissionKeys) && currentPermissionKeys.size() < 100) {
            findParams.setPermissionKeys(new ArrayList<>(currentPermissionKeys));
        }
        List<PermissionAssign> permissionAssign = getPermissionAssignCache().getValue(findParams, () -> trantorIAMPermissionAssignService.findPermissionAssign(findParams)).stream()
                .filter(assign -> currentPermissionKeys.contains(assign.getPermissionKey())).collect(Collectors.toList());

        // 补全权限项授权信息
        Map<String, AuthorizationEffect> permissionKeyToEffect = permissionAssign.stream().collect(Collectors.toMap(PermissionAssign::getPermissionKey, PermissionAssign::getEffect));
        List<FunctionPermissionWithAssign> functionPermissionList = paging.getData().stream().map(permissionMeta -> {
            FunctionPermissionWithAssign functionPermission = PermissionMetaConverter.INSTANCE.convert(permissionMeta);
            functionPermission.setEffect(permissionKeyToEffect.get(functionPermission.getKey()));
            return functionPermission;
        }).collect(Collectors.toList());

        // 补全权限项关联资源信息
        Map<String, List<MetaTreeNodeExt>> permissionKeyToUsageMetas = metaQueryService.findUsageMetaByTargetTypeAndTargetKeys(
                EditUtil.ctxFromThreadLocal(), MetaType.Permission.name(), currentPermissionKeys);
        for (FunctionPermissionWithAssign functionPermissionWithAssign : functionPermissionList) {
            if (permissionKeyToUsageMetas.containsKey(functionPermissionWithAssign.getKey())) {
                List<MetaTreeNodeExt> metaTreeNodes = permissionKeyToUsageMetas.get(functionPermissionWithAssign.getKey());
                if (CollectionUtils.isNotEmpty(metaTreeNodes)) {
                    List<ResourceNodeLite> nodeLites = metaTreeNodes.stream().map(IndexUtil::full2lite).collect(Collectors.toList());
                    functionPermissionWithAssign.setResources(nodeLites);
                }
            }
        }
        return new Paging<>(paging.getTotal(), functionPermissionList);
    }

    @Override
    public Paging<RoleApiAccessControl> pagingApiAccessControl(RoleResourceAccessControlPageRequest request) {
        // 只查询开启鉴权的用户访问接口
        request.setUserAuthorizationEvaluateEnabled(Boolean.TRUE);
        request.setOpenApi(Boolean.FALSE);
        Paging<ApiMeta> paging = accessControlQueryService.pagingApiMeta(request);
        if (Objects.isNull(paging) || CollUtil.isEmpty(paging.getData())) {
            return Paging.empty();
        }

        // 查询服务绑定权限项
        Map<String, String> apiMetaKeyToPermissionKeyMap = accessControlQueryService.getApiMetaKeyToPermissionKeyMap(paging.getData());
        Set<String> currentPermissionKeys = apiMetaKeyToPermissionKeyMap.values().stream().filter(Objects::nonNull).collect(Collectors.toSet());

        // 查询目标角色功能权限授权信息
        List<PermissionAssign> permissionAssign = new ArrayList<>();
        if (CollUtil.isNotEmpty(currentPermissionKeys)) {
            ListUtil.partition(new ArrayList<>(currentPermissionKeys), PermissionConstants.PARTITION_SIZE).forEach(subPermissionKeys -> {
                PermissionAssignFindParams findParams = new PermissionAssignFindParams();
                Long targetAppId = portalToIamAppConverter.getIamAppIdByPortalCode(request.getPortalCode());
                findParams.setEffectiveApplicationIds(Collections.singletonList(targetAppId));
                findParams.setPermissionTypes(Collections.singletonList(PermissionType.FUNCTION_PERMISSION));
                findParams.setTargetTypes(Collections.singletonList(PermissionGrantTargetType.ROLE));
                findParams.setTargetIds(Collections.singletonList(String.valueOf(request.getRoleId())));
                findParams.setPermissionKeys(new ArrayList<>(subPermissionKeys));
                permissionAssign.addAll(
                        getPermissionAssignCache().getValue(findParams, () -> trantorIAMPermissionAssignService.findPermissionAssign(findParams)).stream()
                                .filter(assign -> currentPermissionKeys.contains(assign.getPermissionKey())).collect(Collectors.toList())
                );
            });
        }

        // 补全权限项授权信息
        Map<String, AuthorizationEffect> permissionKeyToEffect = permissionAssign.stream().collect(Collectors.toMap(PermissionAssign::getPermissionKey, PermissionAssign::getEffect));
        List<RoleApiAccessControl> collect = paging.getData().stream()
                .map(apiMeta -> {
                    AuthorizationEffect effect = Optional.ofNullable(apiMetaKeyToPermissionKeyMap.get(apiMeta.getKey()))
                            .map(permissionKeyToEffect::get)
                            .orElse(AuthorizationEffect.NONE);
                    return RoleApiAccessControl.of(apiMeta, apiMetaKeyToPermissionKeyMap.get(apiMeta.getKey()), effect);
                }).collect(Collectors.toList());
        return new Paging<>(paging.getTotal(), collect);
    }

    @SneakyThrows
    @Override
    public Collection<PermissionAuthorizationVO> findAIAgentPermissions(@NotNull RolePermissionRequest request) {
        List<AIAgentMeta> agentMetas = agentRepo.findAll(Cond.all(), ResourceContext.ctxFromThreadLocal())
                .stream().filter(agentMeta -> Objects.nonNull(agentMeta.getResourceProps().getPermissionKey()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(agentMetas)) {
            return Collections.emptyList();
        }

        List<ViewPermissionDTO.BindAIAgent> bindAIAgents = agentMetas.stream().map(agentMeta -> {
            ViewPermissionDTO.BindAIAgent bindAIAgent = new ViewPermissionDTO.BindAIAgent();
            bindAIAgent.setAiAgentKey(agentMeta.getKey());
            return bindAIAgent;
        }).collect(Collectors.toList());
        List<ViewPermissionDTO.BindAIAgentPermissionDTO> permissionByAIAgentKey
                = metaQueryService.findPermissionByAIAgent(EditUtil.ctxFromThreadLocal(), request.getPortalCode(), bindAIAgents);
        Map<String, ViewPermissionDTO.BindAIAgentPermissionDTO> agentKeyToBindAIAgentPermissionDTO
                = permissionByAIAgentKey.stream().collect(Collectors.toMap(
                ViewPermissionDTO.BindAIAgent::getAiAgentKey,
                Function.identity()));
        Set<String> permissionKeys = new HashSet<>();
        for (ViewPermissionDTO.BindAIAgentPermissionDTO bindAIAgentPermissionDTO : permissionByAIAgentKey) {
            permissionKeys.add(bindAIAgentPermissionDTO.getPermissionKey());
            if (CollectionUtils.isNotEmpty(bindAIAgentPermissionDTO.getBindServices())) {
                for (ViewPermissionDTO.BindServicePermissionDTO servicePermission : bindAIAgentPermissionDTO.getBindServices()) {
                    permissionKeys.add(servicePermission.getPermissionKey());
                }
            }
        }

        // 查询目标角色功能权限授权信息
        List<PermissionAssign> permissionAssign = new ArrayList<>();
        if (CollUtil.isNotEmpty(permissionKeys)) {
            ListUtil.partition(new ArrayList<>(permissionKeys), PermissionConstants.PARTITION_SIZE).forEach(subPermissionKeys -> {
                PermissionAssignFindParams findParams = new PermissionAssignFindParams();
                Long targetAppId = portalToIamAppConverter.getIamAppIdByPortalCode(request.getPortalCode());
                findParams.setEffectiveApplicationIds(Collections.singletonList(targetAppId));
                findParams.setPermissionTypes(Collections.singletonList(PermissionType.FUNCTION_PERMISSION));
                findParams.setTargetTypes(Collections.singletonList(PermissionGrantTargetType.ROLE));
                findParams.setTargetIds(Collections.singletonList(String.valueOf(request.getRoleId())));
                findParams.setPermissionKeys(new ArrayList<>(subPermissionKeys));
                permissionAssign.addAll(
                        getPermissionAssignCache().getValue(findParams, () -> trantorIAMPermissionAssignService.findPermissionAssign(findParams)).stream()
                                .filter(assign -> permissionKeys.contains(assign.getPermissionKey())).collect(Collectors.toList())
                );
            });
        }

        // 设置智能体授权状态
        Map<String, AuthorizationEffect> permissionKeyToEffect = permissionAssign.stream().collect(Collectors.toMap(PermissionAssign::getPermissionKey, PermissionAssign::getEffect));
        return agentMetas.stream()
                .map(agentMeta -> PermissionAuthorizationVO.of(agentMeta, agentKeyToBindAIAgentPermissionDTO.get(agentMeta.getKey()), permissionKeyToEffect))
                .collect(Collectors.toList());
    }

    /**
     * 尝试拼接模糊查询条件
     *
     * @param cond       查询条件参数
     * @param fuzzyValue 模糊查询关键字
     */
    private Cond tryAddFuzzyCond(@NotNull Cond cond, String fuzzyValue) {
        if (StringUtils.isBlank(fuzzyValue)) {
            return cond;
        }
        fuzzyValue = "%" + fuzzyValue + "%";
        return cond.and(Cond.or(Field.key().like(fuzzyValue), Field.name().like(fuzzyValue)));
    }

    /**
     * 获取权限授权树
     *
     * @param portalCode 门户编号
     * @return 权限授权树
     */
    private Collection<PermissionResourceDTO> getPermissionTree(@NotNull String portalCode) {
        ResourceContext ctx = ResourceContext.ctxFromThreadLocal();
        String key = KeyUtil.newKeyUnderModule(portalCode, "PermissionAuthorizationView");
        if (Boolean.TRUE.equals(permissionProperties.getPermissionAuthorizationViewEnabled())) {
            return permissionAuthorizeViewRepo.findOneByKey(key, ctx)
                    .map(ExtModuleBaseMeta::getResourceProps)
                    .map(PermissionAuthorizeViewProps::getData)
                    .map(data -> JsonUtil.fromJson(data, new TypeReference<Collection<PermissionResourceDTO>>() {
                    }))
                    .orElse(Collections.emptyList());
        } else {
            return portalResourceTreeCache.getValue(CacheKey.of(portalCode, PermissionType.FUNCTION_PERMISSION), () -> {
                Collection<PermissionResourceDTO> menuPermissionResourceDTOS = PermAuthViewGenerator.generateForFuncPerm(
                        portalCode,
                        moduleQueryService,
                        menuQueryService,
                        sceneQueryService,
                        metaQueryService,
                        sceneRepo);
                return new ArrayList<>(menuPermissionResourceDTOS);
            });
        }
    }

    /**
     * 补全授权资源节点中的props属性参数
     *
     * @param permissionResourceDTOList       授权资源树列表
     * @param permissionKeyToPermissionAssign 权限授权映射 permissionKey -> PermissionAssign
     */
    private void completePermissionResourceProps(Collection<PermissionResourceDTO> permissionResourceDTOList,
                                                 Map<String, PermissionAssign> permissionKeyToPermissionAssign) {
        PermAuthViewGenerator.improveResourcePermissionRecursively(permissionResourceDTOList,
                (permissionResourceDTO, permissionResourceProps, permissionDescriptor) -> {
                    if (Objects.isNull(permissionResourceDTO.getProps())) {
                        permissionResourceDTO.setProps(new FunctionPermissionResourceProps());
                    }

                    FunctionPermissionResourceProps props = (FunctionPermissionResourceProps) permissionResourceDTO.getProps();

                    props.setResolvedPermissionMap(convertPermissionKeyToEffectMap(props.getResolvedPermissionKeys(), permissionKeyToPermissionAssign));

                    if (ACLResourceType.Menu.equals(permissionResourceDTO.getResourceType())
                            && Objects.isNull(props.getSceneKey())
                            && CollectionUtils.isNotEmpty(permissionResourceDTO.getChildren())) {
                        // 对于文件夹菜单的授权状态取决于是否存在有权访问的子菜单节点
                        boolean hasAllowChildMenu = permissionResourceDTO.getChildren().stream()
                                .filter(dto -> ACLResourceType.Menu.equals(dto.getResourceType()))
                                .map(dto -> (FunctionPermissionResourceProps) dto.getProps())
                                .map(FunctionPermissionResourceProps::getEffect)
                                .anyMatch(AuthorizationEffect.ALLOW::equals);
                        props.setEffect(hasAllowChildMenu ? AuthorizationEffect.ALLOW : AuthorizationEffect.NONE);
                        return;
                    }

                    // 先根据资源自己的ownPermissionKey决策出授权状态，如果ownPermissionKey为空，再根据绑定的服务的resolvedPermissionMap决策授权状态
                    if (Objects.nonNull(props.getOwnPermissionKey())) {
                        props.setEffect(determineAuthorizationEffect(Collections.singletonList(props.getOwnPermissionKey()), permissionKeyToPermissionAssign));
                    } else if (MapUtils.isNotEmpty(props.getResolvedPermissionMap())) {
                        props.setEffect(determineAuthorizationEffect(props.getResolvedPermissionMap().keySet(), permissionKeyToPermissionAssign));
                    }
                    props.setPermission(PermissionVOFactory.toVo(permissionDescriptor));
                });
    }

    private Map<String, AuthorizationEffect> convertPermissionKeyToEffectMap(Collection<String> permissionKeys,
                                                                             Map<String, PermissionAssign> permissionKeyToPermissionAssign) {
        if (CollectionUtils.isEmpty(permissionKeys)) {
            return Collections.emptyMap();
        }
        Map<String, AuthorizationEffect> effectMap = new TreeMap<>();
        for (String permissionKey : permissionKeys) {
            PermissionAssign permissionAssign = permissionKeyToPermissionAssign.get(permissionKey);
            effectMap.put(permissionKey, AuthorizationEffect.NONE);
            if (Objects.nonNull(permissionAssign) && Objects.nonNull(permissionAssign.getEffect())) {
                effectMap.put(permissionKey, permissionAssign.getEffect());
            }
        }
        return effectMap;
    }

    private AuthorizationEffect determineAuthorizationEffect(Collection<String> refPermissionKeys,
                                                             Map<String, PermissionAssign> permissionKeyToPermissionAssign) {
        if (CollectionUtils.isEmpty(refPermissionKeys)) return null;
        // 一个资源关联多个权限项时，任一权限项 DENIED 则最终 DENIED；任一权限项 NONE 则最终 NONE，当且仅当所有权限项 ALLOW 则最终 ALLOW
        Set<AuthorizationEffect> effects = refPermissionKeys.stream().map(permissionKeyToPermissionAssign::get)
                .filter(Objects::nonNull)
                .map(PermissionAssign::getEffect)
                .collect(Collectors.toSet());
        return PermissionUtils.determineAuthorizationEffectWithinSingleRole(effects);
    }

    private TrantorPermissionAssignCache getPermissionAssignCache() {
        return permissionCacheManager.getCache(IAMCache.Type.PERMISSION_ASSIGN);
    }
}
