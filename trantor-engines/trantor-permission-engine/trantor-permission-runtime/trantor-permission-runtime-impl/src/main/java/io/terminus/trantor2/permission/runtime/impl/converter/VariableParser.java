package io.terminus.trantor2.permission.runtime.impl.converter;

import io.terminus.iam.api.dto.condition.VariableValue;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.VariableParseException;
import io.terminus.trantor2.rule.engine.api.RuleEngineFactorApi;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;

/**
 * 变量解析器
 *
 * <AUTHOR>
 * 2024/9/6 10:12
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class VariableParser {

    private final RuleEngineFactorApi ruleEngineFactorApi;

    /**
     * 解析变量
     *
     * @param variableValue 变量对象
     * @param params        变量入参
     * @return 变量计算后得到的实际值
     */
    public Object parseVariableValue(@NotNull VariableValue variableValue,
                                     @NotNull Map<String, Object> params) {
        return parseVariableValue(variableValue.getValue(), params);
    }

    /**
     * 解析变量
     *
     * @param variableKey 变量对象key
     * @param params      变量入参
     * @return 变量计算后得到的实际值
     */
    public Object parseVariableValue(@NotNull String variableKey,
                                     @NotNull Map<String, Object> params) {
        Response<Object> result = ruleEngineFactorApi.execute(variableKey, params);
        if (Objects.nonNull(result) && result.isSuccess()) {
            Object data = result.getData();
            if (Objects.isNull(data)
                    || data instanceof Collection && ((Collection<?>) data).isEmpty()
                    || data instanceof Map && ((Map<?, ?>) data).isEmpty()) {
                log.warn("variable parse result may be an unexpected value (null or emptyList or emptyMap), " +
                        "variableKey: {}, actual parse value: {}", variableKey, data);
            }
            return data;
        } else {
            log.error("variable parse error, variableKey: {}", variableKey);
            String errorMsg = Objects.nonNull(result) && Objects.nonNull(result.getErr()) ? result.getErrMsg() : "server.error";
            throw new VariableParseException(
                    ErrorType.RULE_ENGINE_VARIABLE_PARSE_ERROR,
                    errorMsg,
                    String.format(ErrorType.RULE_ENGINE_VARIABLE_PARSE_ERROR.getMessage(), variableKey),
                    new Object[]{variableKey}
            );
        }
    }
}
