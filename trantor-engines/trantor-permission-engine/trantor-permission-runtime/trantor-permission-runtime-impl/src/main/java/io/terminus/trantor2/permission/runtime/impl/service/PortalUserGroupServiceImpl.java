package io.terminus.trantor2.permission.runtime.impl.service;

import io.terminus.iam.api.exception.IAMApplicationNotFoundException;
import io.terminus.iam.api.request.user.UpdateUserRelationActiveParam;
import io.terminus.iam.api.request.user.UserGroupParams;
import io.terminus.iam.api.request.usergroup.UserGroupCopyParams;
import io.terminus.iam.api.request.usergroup.UserGroupCreateParams;
import io.terminus.iam.api.response.application.Application;
import io.terminus.iam.api.response.role.Role;
import io.terminus.iam.api.response.user.User;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.iam.dto.role.AssignRoleRequest;
import io.terminus.trantor2.iam.dto.user.AssignUserRequest;
import io.terminus.trantor2.iam.dto.usergroup.UserGroupPageRequest;
import io.terminus.trantor2.iam.dto.usergroup.UserGroupVO;
import io.terminus.trantor2.iam.dto.usergroup.UserGroupWriteRequest;
import io.terminus.trantor2.iam.service.TrantorIAMApplicationService;
import io.terminus.trantor2.iam.service.TrantorIAMUserGroupService;
import io.terminus.trantor2.iam.utils.UserGroupConverter;
import io.terminus.trantor2.permission.api.common.cache.PortalToIamAppConverter;
import io.terminus.trantor2.permission.api.common.consts.PermissionConstants;
import io.terminus.trantor2.permission.runtime.api.service.PortalUserGroupService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Objects;

/**
 * <AUTHOR>
 * 2025/5/14 13:40
 **/
@Service
@RequiredArgsConstructor
public class PortalUserGroupServiceImpl implements PortalUserGroupService {

    private final TrantorIAMUserGroupService iamUserGroupService;
    private final PortalToIamAppConverter portalToIamAppConverter;
    private final TrantorIAMApplicationService iamApplicationService;

    @Override
    public Long create(UserGroupWriteRequest request) {
        request.setType(PermissionConstants.TRANTOR_PORTAL_USER_GROUP);
        UserGroupCreateParams userGroupCreateParams = UserGroupConverter.INSTANCE.convert(request);
        Long iamAppId = getUserGroupIamAppId(request);
        Application application = iamApplicationService.findById(iamAppId);
        if (Objects.isNull(application)) {
            throw new IAMApplicationNotFoundException("application can not be found by id: " + iamAppId);
        }
        userGroupCreateParams.setSourceApplicationId(application.getId());
        userGroupCreateParams.setApplicationKey(application.getKey());
        userGroupCreateParams.setUserPoolKey(application.getUserPoolKey());
        return iamUserGroupService.create(userGroupCreateParams);
    }

    @Override
    public void deleteById(Long id) {
        iamUserGroupService.deleteById(id);
    }

    @Override
    public void updateById(Long id, UserGroupWriteRequest request) {
        UserGroupParams userGroupParams = UserGroupConverter.INSTANCE.convertToUserGroupParams(request);
        Long iamAppId = getUserGroupIamAppId(request);
        Application application = iamApplicationService.findById(iamAppId);
        if (Objects.isNull(application)) {
            throw new IAMApplicationNotFoundException("application can not be found by id: " + iamAppId);
        }
        userGroupParams.setSourceApplicationId(application.getId());
        userGroupParams.setApplicationKey(application.getKey());
        userGroupParams.setUserPoolKey(application.getUserPoolKey());
        iamUserGroupService.updateById(id, userGroupParams);
    }

    @Override
    public void copyWithId(Long id, UserGroupWriteRequest request) {
        request.setType(PermissionConstants.TRANTOR_PORTAL_USER_GROUP);
        UserGroupCopyParams userGroupCopyParams = UserGroupConverter.INSTANCE.convertToUserGroupCopyParams(request);
        Long iamAppId = getUserGroupIamAppId(request);
        Application application = iamApplicationService.findById(iamAppId);
        if (Objects.isNull(application)) {
            throw new IAMApplicationNotFoundException("application can not be found by id: " + iamAppId);
        }
        userGroupCopyParams.setSourceApplicationId(application.getId());
        userGroupCopyParams.setApplicationKey(application.getKey());
        userGroupCopyParams.setUserPoolKey(application.getUserPoolKey());
        iamUserGroupService.copyWithId(id, userGroupCopyParams);
    }

    @Override
    public Paging<UserGroupVO> paging(UserGroupPageRequest request) {
        request.setType(PermissionConstants.TRANTOR_PORTAL_USER_GROUP);
        return iamUserGroupService.paging(request);
    }

    @Override
    public Collection<User> findUsersByUserGroupId(Long id) {
        return iamUserGroupService.findUsersByUserGroupId(id);
    }

    @Override
    public void removeOrAssignUsers(Long id, AssignUserRequest request) {
        iamUserGroupService.removeOrAssignUsers(id, request);
    }

    @Override
    public Collection<Role> findRolesByUserGroupId(Long id) {
        return iamUserGroupService.findRolesByUserGroupId(id);
    }

    @Override
    public void removeOrAssignRoles(Long id, AssignRoleRequest request) {
        iamUserGroupService.removeOrAssignRoles(id, request);
    }

    @Override
    public void activeById(Long id) {
        iamUserGroupService.activeRelationById(id);
    }

    @Override
    public void inactiveById(Long id) {
        iamUserGroupService.inactiveRelationById(id);
    }

    @Override
    public void changeActiveByUserIdsAndGroupId(UpdateUserRelationActiveParam param) {
        iamUserGroupService.activeByUserIdsAndGroupId(param);
    }

    /**
     * 获取用户组所属iam应用id
     * 1. 如果请求参数带有门户标识，则通过门户标识获取关联iam应用id
     * 2. 如果请求中未指定门户标识，则获取当前请求上下文中门户信息，从而获取关联iam应用id
     *
     * @param request 用户组操作请求对象
     * @return Long 用户组所属门户关联的iam应用id
     */
    private Long getUserGroupIamAppId(UserGroupWriteRequest request) {
        return StringUtils.isNotBlank(request.getPortalCode())
                ? portalToIamAppConverter.getIamAppIdByPortalCode(request.getPortalCode())
                : TrantorContext.getCurrentPortalOptional().map(Portal::getIamEndpointId).orElseThrow(() -> new TrantorRuntimeException("Current portal was not found"));
    }
}
