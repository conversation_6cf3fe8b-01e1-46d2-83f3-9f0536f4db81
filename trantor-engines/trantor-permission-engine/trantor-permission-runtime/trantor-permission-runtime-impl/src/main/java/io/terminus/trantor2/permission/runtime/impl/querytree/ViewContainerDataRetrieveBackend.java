package io.terminus.trantor2.permission.runtime.impl.querytree;

import com.google.common.collect.Lists;
import io.terminus.trantor2.lang.meta.ViewContainer;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.querytree.DataRetrieveBackend;
import io.terminus.trantor2.meta.util.EditUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class ViewContainerDataRetrieveBackend implements DataRetrieveBackend<String, List<ViewContainer>> {
    private final MetaQueryService metaQueryService;

    @Override
    public Map<String, List<ViewContainer>> getAll(Collection<String> keys) {
        if (keys.isEmpty()) {
            return Collections.emptyMap();
        }
        return metaQueryService.findViewContainers(EditUtil.ctxFromThreadLocal(), Lists.newArrayList(keys));
    }
}
