package io.terminus.trantor2.permission.runtime.impl.util;

import io.terminus.iam.api.enums.permission.FieldAccessType;
import io.terminus.iam.api.response.permission.FieldRule;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * 2024/7/24 11:21
 **/
public class FieldRuleUtil {

    private FieldRuleUtil() {
    }

    /**
     * 字段访问模式优先级，value 越大，优先级越高
     */
    private static final Map<FieldAccessType, Integer> FIELD_ACCESS_TYPE_PRIORITY = new EnumMap<>(FieldAccessType.class);

    static {
        // 优先级：WRITABLE > READONLY > INVISIBLE
        FIELD_ACCESS_TYPE_PRIORITY.put(FieldAccessType.WRITABLE, 3);
        FIELD_ACCESS_TYPE_PRIORITY.put(FieldAccessType.READONLY, 2);
        FIELD_ACCESS_TYPE_PRIORITY.put(FieldAccessType.INVISIBLE, 1);
    }

    /**
     * 将多层级字段权限规则根据字段路径拉平成Map结构
     * <p>
     * e.g.
     * {
     * "data": "READONLY",
     * "data.code": "READONLY",
     * "data.name": "WRITABLE",
     * "data.desc": "INVISIBLE",
     * "data.org": "INVISIBLE",
     * "data.org.id": "READONLY",
     * "data.org.code": "WRITABLE",
     * ...
     * "data.xxx.yyy.zzz": "WRITABLE",
     * }
     *
     * @param fieldRules 字段权限规则
     * @return 拉平后的Map结构，
     * Map key = 字段路径，比如data.code
     * Map value = 该路径下字段访问策略 FieldAccessType
     */
    public static Map<String, List<FieldAccessType>> convertToFlatFieldAccessTypeMap(@NotEmpty Collection<FieldRule> fieldRules) {
        Map<String, List<FieldAccessType>> fieldAccessTypeMap = new HashMap<>();
        convertToFlatFieldAccessTypeMap(fieldRules, "", fieldAccessTypeMap);
        return fieldAccessTypeMap;
    }

    /**
     * 去重合并规则中相同路径的字段访问策略
     *
     * @param fieldRules 字段访问规则列表
     * @return 去重合并后的字段访问规则列表
     */
    public static Collection<FieldRule> mergeFieldRules(@NotEmpty Collection<FieldRule> fieldRules) {
        Map<String, FieldRule> merged = new HashMap<>();
        for (FieldRule fieldRule : fieldRules) {
            if (CollectionUtils.isNotEmpty(fieldRule.getChildren())) {
                fieldRule.setChildren(new ArrayList<>(mergeFieldRules(fieldRule.getChildren())));
            }
            merged.merge(fieldRule.getEntityParamKey(), fieldRule, FieldRuleUtil::mergeFieldRule);
        }
        return merged.values();
    }

    /**
     * 选出优先级最高的字段权限访问策略
     *
     * @param fieldAccessTypes 字段权限访问策略列表
     * @return FieldAccessType 返回优先级最高的字段权限访问策略
     */
    public static FieldAccessType determinePrimaryAccessTypes(@NotEmpty Collection<FieldAccessType> fieldAccessTypes) {
        return fieldAccessTypes.stream().filter(Objects::nonNull).max(Comparator.comparing(FieldRuleUtil::getPriority)).orElse(null);
    }

    private static void convertToFlatFieldAccessTypeMap(@NotEmpty Collection<FieldRule> fieldRules, String parentPath,
                                                        @NotNull Map<String, List<FieldAccessType>> fieldAccessTypeMap) {
        for (FieldRule fieldRule : fieldRules) {
            String fieldPath = parentPath + fieldRule.getEntityParamKey();
            fieldAccessTypeMap.putIfAbsent(fieldPath, new ArrayList<>());
            fieldAccessTypeMap.get(fieldPath).add(fieldRule.getAccessType());
            if (CollectionUtils.isNotEmpty(fieldRule.getChildren())) {
                convertToFlatFieldAccessTypeMap(fieldRule.getChildren(), fieldPath + ".", fieldAccessTypeMap);
            }
        }
    }

    private static FieldRule mergeFieldRule(@NotNull FieldRule first, @NotNull FieldRule second) {
        if (getPriority(first.getAccessType()) < getPriority(second.getAccessType())) {
            first.setAccessType(second.getAccessType());
        }
        if (CollectionUtils.isNotEmpty(first.getChildren()) || CollectionUtils.isNotEmpty(second.getChildren())) {
            Map<String, FieldRule> childrenMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(first.getChildren())) {
                for (FieldRule child : first.getChildren()) {
                    childrenMap.put(child.getEntityParamKey(), child);
                }
            }
            if (CollectionUtils.isNotEmpty(second.getChildren())) {
                for (FieldRule child : second.getChildren()) {
                    childrenMap.merge(child.getEntityParamKey(), child, FieldRuleUtil::mergeFieldRule);
                }
            }
            first.setChildren(new ArrayList<>(childrenMap.values()));
        }
        return first;
    }

    private static int getPriority(FieldAccessType accessType) {
        return FIELD_ACCESS_TYPE_PRIORITY.getOrDefault(accessType, 0);
    }
}
