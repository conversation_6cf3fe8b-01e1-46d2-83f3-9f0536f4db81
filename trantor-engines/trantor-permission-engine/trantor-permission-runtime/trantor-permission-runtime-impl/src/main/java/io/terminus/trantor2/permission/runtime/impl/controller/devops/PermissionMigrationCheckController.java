package io.terminus.trantor2.permission.runtime.impl.controller.devops;

import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.permission.runtime.api.dto.devops.PermissionMigrationCheckRequest;
import io.terminus.trantor2.permission.runtime.impl.service.devops.PermissionMigrationCheckService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.constraints.NotNull;
import java.util.Collections;
import java.util.Objects;

/**
 * 权限迁移检查工具
 *
 * <AUTHOR>
 * 2024/8/20 14:53
 **/
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/trantor/permission/migration")
public class PermissionMigrationCheckController {

    private final PermissionMigrationCheckService permissionMigrationCheckService;

    @GetMapping(value = "/check")
    public Response<Object> checkPermissionMigration(@NotNull PermissionMigrationCheckRequest request) {
        if (Objects.isNull(request.getRoleId())) {
            return Response.ok(permissionMigrationCheckService.checkPermissionMigrationForSinglePortal(request));
        } else {
            return Response.ok(permissionMigrationCheckService.checkPermissionMigrationForSinglePortalAndRole(request, Collections.singletonList(request.getRoleId())));
        }
    }
}
