package io.terminus.trantor2.permission.runtime.impl.listener.handler;

import io.terminus.trantor2.iam.cache.PermissionCache;
import io.terminus.trantor2.iam.cache.TrantorPermissionAssignCache;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.event.MetaChangeEvent;
import io.terminus.trantor2.permission.impl.common.listener.handler.MetaChangeHandler;
import io.terminus.trantor2.permission.runtime.impl.cache.v2.PortalResourceTreeCache;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * 2024/7/25 18:07
 **/
@Lazy
@Component
@RequiredArgsConstructor
public class InvalidatePortalResourceTreeCacheHandler implements MetaChangeHandler {

    private final PermissionCache permissionCache;
    private final TrantorPermissionAssignCache permissionAssignCache;
    private final PortalResourceTreeCache portalResourceTreeCache;

    private static final List<String> META_TYPES = Arrays.asList(
            MetaType.MenuTree.name(), MetaType.Scene.name(), MetaType.View.name()
    );

    @Override
    public void handle(MetaChangeEvent metaChangeEvent) {
        if (CollectionUtils.isEmpty(metaChangeEvent.getOldMetas())) {
            return;
        }
        if (metaChangeEvent.getOldMetas().stream().anyMatch(metaId -> META_TYPES.contains(metaId.getType()))) {
            permissionCache.invalidateAllLocal();
            permissionAssignCache.invalidateAllLocal();
            portalResourceTreeCache.invalidateAllLocal();
        }
    }
}
