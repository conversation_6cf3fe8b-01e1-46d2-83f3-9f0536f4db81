package io.terminus.trantor2.permission.runtime.impl.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.iam.api.enums.permission.v2.PermissionType;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.module.runtime.service.PortalService;
import io.terminus.trantor2.permission.api.common.cache.ResourcePermissionCache;
import io.terminus.trantor2.permission.api.common.dto.ACLResourceType;
import io.terminus.trantor2.permission.api.common.dto.DataControlDimensionWithEffect;
import io.terminus.trantor2.permission.api.common.dto.PermissionAuthorizationVO;
import io.terminus.trantor2.permission.api.common.dto.PermissionResourceDTO;
import io.terminus.trantor2.permission.runtime.api.dto.DataConditionRefServiceFindRequest;
import io.terminus.trantor2.permission.runtime.api.dto.FunctionPermissionWithAssign;
import io.terminus.trantor2.permission.runtime.api.dto.FunctionPermissionWithAssignFindRequest;
import io.terminus.trantor2.permission.runtime.api.dto.PermissionAssignRequest;
import io.terminus.trantor2.permission.runtime.api.dto.PermissionWithAssignFindRequest;
import io.terminus.trantor2.permission.runtime.api.dto.ResourceRefServiceFindRequest;
import io.terminus.trantor2.permission.runtime.api.dto.RoleApiAccessControl;
import io.terminus.trantor2.permission.runtime.api.dto.RolePermissionRequest;
import io.terminus.trantor2.permission.runtime.api.dto.RoleResourceAccessControlPageRequest;
import io.terminus.trantor2.permission.runtime.api.service.RoleDataPermissionService;
import io.terminus.trantor2.permission.runtime.api.service.RoleFieldPermissionService;
import io.terminus.trantor2.permission.runtime.api.service.RoleFunctionPermissionService;
import io.terminus.trantor2.permission.runtime.api.service.RolePermissionAssignService;
import io.terminus.trantor2.rule.engine.api.RuleEngineFactorApi;
import io.terminus.trantor2.rule.engine.api.model.dto.RuleEngineFactorDTO;
import io.terminus.trantor2.rule.engine.api.model.dto.factor.FactorCombDTO;
import io.terminus.trantor2.service.report.annotation.TrantorServiceRegistry;
import io.terminus.trantor2.service.runtime.api.ServiceExecuteApi;
import io.terminus.trantor2.service.runtime.api.model.request.ServiceExecuteRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 角色权限操作接口
 *
 * <AUTHOR>
 * 2024/5/28 16:15
 **/
@Tag(name = "角色权限授权接口")
@TrantorServiceRegistry
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/trantor/portal/role/permission")
public class RolePermissionController {

    private final RoleFunctionPermissionService roleFunctionPermissionService;
    private final RolePermissionAssignService permissionAssignService;
    private final RoleDataPermissionService roleDataPermissionService;
    private final RoleFieldPermissionService roleFieldPermissionService;
    private final ServiceExecuteApi serviceExecuteApi;
    private final RuleEngineFactorApi ruleEngineFactorApi;
    private final ResourcePermissionCache resourcePermissionCache;
    private final PortalService portalService;

    @GetMapping(value = "/function-permission/tree")
    @Operation(summary = "角色-功能权限-角色授权树")
    public Response<List<PermissionResourceDTO>> findFuncPermissionTree(@Validated PermissionWithAssignFindRequest request) {
        List<PermissionResourceDTO> funcPermissionTree = roleFunctionPermissionService.findFuncPermissionTree(request);
        AtomicInteger localId = new AtomicInteger(1);
        // FIXME 这里的修改有可能污染缓存数据
        renameButtonResourceKey(funcPermissionTree, localId);
        return Response.ok(funcPermissionTree);
    }

    private void renameButtonResourceKey(List<PermissionResourceDTO> funcPermissionTree, AtomicInteger localId) {
        for (PermissionResourceDTO resource : funcPermissionTree) {
            if (resource.getResourceType() == ACLResourceType.Button) {
                // 按钮资源key加上额外唯一标识，防止跨视图资源key重复
                if (!resource.getResourceKey().endsWith("__uniq")) {
                    resource.setResourceKey(resource.getResourceKey() + "__" + localId.getAndIncrement() + "__uniq");
                }
            }
            if (resource.getChildren() != null) {
                renameButtonResourceKey(resource.getChildren(), localId);
            }
        }
    }

    @PostMapping(value = "find-ref-service-summary")
    @Operation(summary = "角色-功能权限-查询资源引用服务摘要信息")
    public Response<Map<String, List<PermissionResourceDTO>>> findRefService(@RequestBody @Validated ResourceRefServiceFindRequest request) {
        if (CollectionUtils.isEmpty(request.getResourceKeys())) {
            return Response.ok();
        }
        List<String> resourceKeys = new ArrayList<>();
        for (String resourceKey : request.getResourceKeys()) {
            if (resourceKey == null) {
                continue;
            }
            if (resourceKey.endsWith("__uniq")) {
                resourceKeys.add(resourceKey.split("__")[0]);
            } else {
                resourceKeys.add(resourceKey);
            }
        }
        return Response.ok(roleFunctionPermissionService.findRefServiceSummary(resourceKeys));
    }

    @GetMapping(value = "/function-permission/paging")
    @Operation(summary = "角色-功能权限-角色权限项分页列表")
    public Response<Paging<FunctionPermissionWithAssign>> pagingFunctionPermission(@Validated FunctionPermissionWithAssignFindRequest request) {
        return Response.ok(roleFunctionPermissionService.pagingFunctionPermission(request));
    }

    @GetMapping(value = "/api-access-control/paging")
    @Operation(summary = "角色-功能权限-接口访问控制列表")
    public Response<Paging<RoleApiAccessControl>> pagingApiAccessControl(@Validated RoleResourceAccessControlPageRequest request) {
        return Response.ok(roleFunctionPermissionService.pagingApiAccessControl(request));
    }

    @GetMapping(value = "/ai-agent/find")
    @Operation(summary = "角色-功能权限-智能体权限授权列表")
    public Response<Collection<PermissionAuthorizationVO>> findAIAgentPermissions(@Validated RolePermissionRequest request) {
        return Response.ok(roleFunctionPermissionService.findAIAgentPermissions(request));
    }

    @PostMapping(value = "/data-permission/find-ref-service-summary")
    @Operation(summary = "角色-数据权限-查询引用权限规则的服务摘要信息")
    public Response<Map<String, Collection<PermissionResourceDTO>>> findRefServiceByDataCondition(@RequestBody @Validated DataConditionRefServiceFindRequest request) {
        return Response.ok(roleDataPermissionService.findRefServiceSummary(request.getDataConditionKeys(), request.getViewKeys()));
    }

    @GetMapping(value = "/data-permission/only-tree")
    @Operation(summary = "角色-数据权限-角色授权树")
    public Response<Collection<PermissionResourceDTO>> findDataPermissionOnlyTree(@Validated PermissionWithAssignFindRequest request) {
        return Response.ok(roleDataPermissionService.findDataPermissionOnlyTree(request));
    }

    @GetMapping(value = "/data-permission/only-detail")
    @Operation(summary = "角色-数据权限-角色授权树")
    public Response<Collection<PermissionResourceDTO>> findDataPermissionOnlyDetail(@Validated PermissionWithAssignFindRequest request) {
        return Response.ok(roleDataPermissionService.findDataPermissionOnlyDetail(request));
    }

    @GetMapping(value = "/data-control-dimension/find")
    @Operation(summary = "角色-数据控权维度-授权列表")
    public Response<Collection<DataControlDimensionWithEffect>> findDataControlDimension(@Validated PermissionWithAssignFindRequest request) {
        return Response.ok(roleDataPermissionService.findDataControlDimension(request));
    }

    @PostMapping("/assign")
    @Operation(summary = "角色-权限授权")
    public Response<Void> assignPermission(@RequestBody @Validated PermissionAssignRequest request) {
        permissionAssignService.assignPermissionToRole(request);
        // 功能权限授权需要删除门户菜单缓存
        if (PermissionType.FUNCTION_PERMISSION.equals(request.getPermissionType())) {
            resourcePermissionCache.invalidatePortalCache(portalService.findPortalByCode(request.getPortalCode()).getId());
        }
        return Response.ok();
    }

    @PostMapping("/service/execute")
    @Operation(summary = "执行目标服务查询数据", description = "该服务内部存在鉴权处理逻辑，只允许访问数据规则或数据控权维度引用的服务")
    Response<Object> executeService(@RequestParam String serviceKey,
                                    @RequestParam String dataPermissionKey,
                                    @RequestBody ServiceExecuteRequest request) {
        roleDataPermissionService.checkAllowAccess(serviceKey, dataPermissionKey);
        return serviceExecuteApi.execute(serviceKey, request);
    }

    @PostMapping("/variable/find-by-sceneKey")
    @Operation(summary = "查询规则引擎因子变量")
    Response<FactorCombDTO> findVariable(@RequestBody @Validated RuleEngineFactorDTO request) {
        return ruleEngineFactorApi.queryFactorBySceneKey(request);
    }

    @GetMapping(value = "/field-permission/only-tree")
    @Operation(summary = "角色-字段权限-角色授权树")
    public Response<List<PermissionResourceDTO>> findFieldPermissionOnlyTree(@Validated PermissionWithAssignFindRequest request) {
        return Response.ok(roleFieldPermissionService.findFieldPermissionOnlyTree(request));
    }

    @GetMapping(value = "/field-permission/only-detail")
    @Operation(summary = "角色-字段权限-角色授权树")
    public Response<List<PermissionResourceDTO>> findFieldPermissionOnlyDetail(@Validated PermissionWithAssignFindRequest request) {
        return Response.ok(roleFieldPermissionService.findFieldPermissionOnlyDetail(request));
    }
}
