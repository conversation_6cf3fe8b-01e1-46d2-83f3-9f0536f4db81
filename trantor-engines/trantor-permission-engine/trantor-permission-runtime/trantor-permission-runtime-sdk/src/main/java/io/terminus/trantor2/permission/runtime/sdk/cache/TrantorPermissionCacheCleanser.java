package io.terminus.trantor2.permission.runtime.sdk.cache;

import io.terminus.trantor2.permission.api.common.cache.PermissionCacheCleanser;
import io.terminus.trantor2.permission.api.common.cache.PermissionCacheEvent;
import io.terminus.trantor2.permission.api.common.consts.PermissionConstants;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * 2023/10/9 5:15 PM
 **/
@Slf4j
public class TrantorPermissionCacheCleanser implements PermissionCacheCleanser {

    private static final String LOG_INFO_TEMPLATE = "publish a message [{}] to redis topic [{}]";

    private final RTopic expireTopic;

    public TrantorPermissionCacheCleanser(RedissonClient redissonClient) {
        this.expireTopic = redissonClient.getTopic(PermissionConstants.PERMISSION_CACHE_TOPIC);
    }

    public void invalidateAll() {
        PermissionCacheEvent event = new PermissionCacheEvent().setClearAll(Boolean.TRUE);
        expireTopic.publish(event);
        log.info(LOG_INFO_TEMPLATE, event, PermissionConstants.PERMISSION_CACHE_TOPIC);
    }

    @Override
    public void invalidateUserPortalCache(@NotNull Long userId, @NotNull Long portalId) {
        PermissionCacheEvent event = new PermissionCacheEvent().setUserId(userId).setPortalId(portalId).setClearAll(Boolean.FALSE);
        expireTopic.publish(event);
        log.info(LOG_INFO_TEMPLATE, event, PermissionConstants.PERMISSION_CACHE_TOPIC);
    }

    @Override
    public void invalidateUserCache(@NotNull Long userId) {
        PermissionCacheEvent event = new PermissionCacheEvent().setUserId(userId).setClearAll(Boolean.FALSE);
        expireTopic.publish(event);
        log.info(LOG_INFO_TEMPLATE, event, PermissionConstants.PERMISSION_CACHE_TOPIC);
    }

    @Override
    public void invalidatePortalCache(@NotNull Long portalId) {
        PermissionCacheEvent event = new PermissionCacheEvent().setPortalId(portalId).setClearAll(Boolean.FALSE);
        expireTopic.publish(event);
        log.info(LOG_INFO_TEMPLATE, event, PermissionConstants.PERMISSION_CACHE_TOPIC);
    }
}
