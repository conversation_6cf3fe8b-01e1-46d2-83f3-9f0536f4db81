package io.terminus.trantor2.datasource.model;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.common.LabelEnum;

/**
 * 支持的数据库类型
 *
 * <AUTHOR>
 */
@Schema(description = "支持的数据库类型")
public enum SupportDialectTypeEnum implements LabelEnum {
    /**
     * mysql
     */
    @Schema(description = "mysql")
    MYSQL("mysql"),
    /**
     * drds
     * todo 暂不支持 DRDS数据库类型已经废弃，可以使用MYSQL数据类型代替
     */
    @Deprecated
    @Schema(description = "drds")
    DRDS("drds"),
    /**
     * oracle
     * todo 暂不支持
     */
    @Schema(description = "oracle")
    ORACLE("oracle"),
    /**
     * h2
     * todo 暂不支持
     */
    @Schema(description = "h2")
    H2("h2"),

    /**
     * h2
     * todo 暂不支持
     */
    @Schema(description = "redis")
    REDIS("redis"),

    @Schema(description = "milvus")
    MILVUS("milvus")
    ;

    /**
     * 数据库类型label描述
     */
    private final String label;

    SupportDialectTypeEnum(String label1) {
        this.label = label1;
    }

    @Override
    public String getLabel() {
        return label;
    }

    /**
     * 类型查找
     *
     * @param value
     * @return 数据库类型
     */
    public static SupportDialectTypeEnum find(String value) {
        for (SupportDialectTypeEnum supportDialectTypeEnum : values()) {
            if (supportDialectTypeEnum.name().equalsIgnoreCase(value)) {
                return supportDialectTypeEnum;
            }
        }
        return MYSQL;
    }

    /**
     * 是否是 h2数据库
     *
     * @param dialect 数据库类型
     * @return yes or no
     */
    public static boolean isH2(String dialect) {
        return H2.name().equalsIgnoreCase(dialect);
    }

    /**
     * 是否是 mysql数据库
     *
     * @param dialect 数据库类型
     * @return yes or no
     */
    public static boolean isMySQL(String dialect) {
        return MYSQL.name().equalsIgnoreCase(dialect);
    }

    /**
     * 是否是 drds数据库
     *
     * @param dialect 数据库类型
     * @return yes or no
     */
    public static boolean isDRDS(String dialect) {
        return DRDS.name().equalsIgnoreCase(dialect);
    }

    /**
     * 是否是 oracle数据库
     *
     * @param dialect 数据库类型
     * @return yes or no
     */
    public static boolean isORACLE(String dialect) {
        return ORACLE.name().equalsIgnoreCase(dialect);
    }

    public static boolean isMilvus(String dialect) {
        return MILVUS.name().equalsIgnoreCase(dialect);
    }

    /**
     * 是否是 mysql or drds数据库
     *
     * @param dataSourceType 数据库类型
     * @return yes or no
     */
    public static boolean isMySQLOrDRDS(String dataSourceType) {
        return isMySQL(dataSourceType) || isDRDS(dataSourceType);
    }

    /**
     * 判断需要反转义JSON值的方言类型
     * <p>
     * 客户端传递过来的SQL是符合MySQL转义的，双引号会添加两个反斜杠转义，在插入数据库之后只会保留一个反斜杠转义，H2和Oracle不需要这层反斜杠转义
     *
     * @param dialectType 数据库类型
     * @return 是否需要反转义JSON值
     */
    public static boolean needUnEscapeJsonValue(SupportDialectTypeEnum dialectType) {
        return dialectType == H2 || dialectType == ORACLE;
    }
}
