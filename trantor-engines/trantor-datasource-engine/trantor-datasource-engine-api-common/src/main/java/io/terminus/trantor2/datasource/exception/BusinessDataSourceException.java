package io.terminus.trantor2.datasource.exception;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorBizException;

/**
 * 业务数据源异常
 */
public class BusinessDataSourceException extends TrantorBizException {

    /**
     * 构造器
     *
     * @param errorType
     * @param message1
     */
    public BusinessDataSourceException(ErrorType errorType, String message1) {
        super(errorType, message1);
    }

    public BusinessDataSourceException(ErrorType errorType, String message1, Object[] args) {
        super(errorType, message1, args);
    }

    /**
     * 最终抛给用户的异常信息，若此拼接规则不满足需求，请重写此方法
     *
     * @return
     */
    @Override
    public String getComposedMessage() {
        return super.getMessage();
    }
}
