package io.terminus.trantor2.datasource.model.sharding;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.model.management.meta.domain.sharding.ShardingConfig;
import io.terminus.trantor2.model.management.meta.domain.sharding.ShardingField;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 分表配置规则汇总
 */
@Schema(description = "分表配置")
@Data
public class TableShardingConfig implements Serializable {
    /**
     * 是否开启分表配置
     */
    private Boolean shardingEnable;
    /**
     * 分表数量
     */
    private Integer shardingNum;

    /**
     * 模型分表规则
     */
    private ShardingConfig shardingConfig;

    /**
     * 模型逻辑表名
     */
    private String logicTable;

    /**
     * 模型标识
     */
    private String modelKey;

    public String generateActualDataNodes(List<String> dbNames) {
        // CESHI_0.test_a_p_${0..1},CESHI_1.test_a_p_${2..3},CESHI_2.test_a_p_${4}
        List<String> tablePartition = generateTablePartitionNodes(dbNames.size());
        StringBuilder buffer = new StringBuilder();
        for (int i = 0; i < dbNames.size(); i++) {
            buffer.append(dbNames.get(i)).append(".").append(tablePartition.get(i));
            if ((i + 1) != dbNames.size()) {
                buffer.append(",");
            }

        }

        return buffer.toString();
    }

    public String generateShardingColumns() {
        List<String> fields = shardingConfig.getShardingFields().stream()
            .map(ShardingField::getField)
            .collect(Collectors.toList());
        return String.join(",", fields);
    }

    public Set<String> generateAllPhysicalTableName(boolean needLogicTable) {
        Set<String> tableNames = new HashSet<>();
        if (needLogicTable) {
            tableNames.add(logicTable);
        }
        for (int i = 0; i < shardingNum; i++) {
            tableNames.add(logicTable + "_" + i);
        }

        return tableNames;
    }

    /**
     * 生成每个数据库中物理表的分布
     * 实现原理：分表数/db个数，结果为每个库中应该存在的物理表的个数，然后剩下的表，从库0开始1个一个分布
     * 举例：数据库个数为3，表数为7， 那么最终分布为 3，2，2； 数据库个数为3，表数为8，那么最终分布为3，3，2
     *
     * @param dbNum
     * @return
     */
    public List<String> generateTablePartitionNodes(int dbNum) {
        ArrayList<Integer> tablePartNums = generateTableDistributeInDbs(dbNum);

        List<String> tablePartitionNodes = new ArrayList<>();
        // 上面部分负责计算每个part应该放几张表，下面组装逻辑表字符串返回
        int nowNum = 0;
        for (Integer tablePartNum : tablePartNums) {
            int end = Math.min((nowNum + tablePartNum - 1), shardingNum - 1);
            String node;
            if (nowNum == end) {
                node = getLogicTable() + "_${" + end + "}";
            } else {
                node = getLogicTable() + "_${" + nowNum + ".." + end + "}";
            }
            tablePartitionNodes.add(node);
            nowNum = end + 1;
        }
        return tablePartitionNodes;
    }

    /**
     * 生成每个数据库中物理表的分布
     * 实现原理：分表数/db个数，结果为每个库中应该存在的物理表的个数，然后剩下的表，从库0开始1个一个分布
     * 举例：数据库个数为3，表数为7， 那么最终分布为 3，2，2； 数据库个数为3，表数为8，那么最终分布为3，3，2
     *
     * @param dbNum
     * @return
     */
    @NotNull
    public ArrayList<Integer> generateTableDistributeInDbs(int dbNum) {
        int everyExistNum = shardingNum / dbNum;
        ArrayList<Integer> tablePartNums = new ArrayList<>();
        for (int i = 0; i < dbNum; i++) {
            tablePartNums.add(everyExistNum);
        }

        for (int i = 0; i < (shardingNum - everyExistNum * dbNum); i++) {
            int old = tablePartNums.get(i);
            tablePartNums.set(i, old + 1);
        }
        return tablePartNums;
    }

    /**
     * 生成每个库中存放的物理表名集合，比如存在数据库为3个，分表名为7
     * map.key = 数据源标记
     * map.value = 每个数据源上应该存储的物理表名
     *
     * @param dbNames
     * @return
     */
    public Map<String, List<String>> generateTableStoreInDbs(List<String> dbNames) {
        ArrayList<Integer> tablePartNums = generateTableDistributeInDbs(dbNames.size());

        Map<String, List<String>> resMap = new HashMap<>();

        AtomicInteger atomicInteger = new AtomicInteger(0);
        for (int i = 0; i < tablePartNums.size(); i++) {
            int tableNum = tablePartNums.get(i);
            List<String> tableNames = new ArrayList<>();
            for (int j = 0; j < tableNum; j++) {
                tableNames.add(logicTable + "_" + atomicInteger.get());
                atomicInteger.addAndGet(1);
            }
            resMap.put(dbNames.get(i), tableNames);
        }
        return resMap;
    }
}





