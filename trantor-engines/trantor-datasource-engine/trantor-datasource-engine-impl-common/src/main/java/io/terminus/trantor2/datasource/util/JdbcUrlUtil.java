package io.terminus.trantor2.datasource.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020-06-16
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class JdbcUrlUtil {

    /**
     * 时区
     */
    private static String dbZoneId;
    /**
     * System.getenv("DB_ZONE_ID")
     */
    public static final String ENV_DB_ZONE_ID = System.getenv("DB_ZONE_ID");
    /**
     * System.getenv("DB_ZONE_OFFSET")
     */
    public static final String ENV_DB_ZONE_OFFSET = System.getenv("DB_ZONE_OFFSET");
    /**
     * ZoneId.systemDefault().getId()
     */
    public static final String SYSTEM_DEFAULT_ZONE_ID = ZoneId.systemDefault().getId();
    /**
     * System.getenv("MYSQL_USE_SSL")
     */
    public static final String MYSQL_USE_SSL = System.getenv("MYSQL_USE_SSL");
    /**
     * mysql连接串
     * 2022-09-13设置maxReconnects=1，当连接不可用时，重试建立连接的次数，mysql默认为3，initialTimeout为2，即两秒重试一次，重试三次
     */
    public static final String JDBC_URL_PATTERN = "*************************************************************"
        + "&autoReconnect=true&maxReconnects=1&tcpKeepAlive=true&zeroDateTimeBehavior=convertToNull";
    /**
     * h2连接串
     */
    public static final String H2_JDBC_URL_PATTERN = "jdbc:h2:tcp://localhost:9092/%s:testdb;DB_CLOSE_DELAY=-1;"
        + "DB_CLOSE_ON_EXIT=FALSE;MODE=MYSQL;IGNORECASE=TRUE;DATABASE_TO_LOWER=TRUE";
    public static final String H2_SCHEMA_PATTERN = ";SCHEMA=%s";

    private static final int DEFAULT_ZONE_LENGTH = 3;

    /**
     * 时区简写
     *
     * @param zoneId zoneId
     * @return zoneId
     */
    public static String getShortZoneID(@NonNull String zoneId) {
        if (zoneId.length() > DEFAULT_ZONE_LENGTH) {
            return ZoneId.SHORT_IDS.entrySet().stream()
                .filter(e -> e.getValue().equals(zoneId))
                .findFirst()
                .map(Map.Entry::getKey)
                .orElse(zoneId);
        } else {
            return zoneId;
        }
    }

    public static String getMySQLOrDRDSJdbcUrl(@NonNull String dbHost, @NonNull String dbPort, String dbSchema) {
        String initZoneId = initDbZoneIdOnlyOnce();
        return getMySQLOrDRDSJdbcUrl(dbHost, dbPort, dbSchema, initZoneId);
    }

    private static String initDbZoneIdOnlyOnce() {
        if (dbZoneId != null) {
            return dbZoneId;
        }

        if (StringUtils.isNotBlank(ENV_DB_ZONE_ID)) {
            dbZoneId = ENV_DB_ZONE_ID;
        } else if (StringUtils.isNotBlank(ENV_DB_ZONE_OFFSET)) {
            ZoneOffset zoneOffset = ZoneOffset.of(ENV_DB_ZONE_OFFSET);
            dbZoneId = zoneOffset.getId();
            if (dbZoneId.startsWith("+")) {
                dbZoneId = "GMT%2B" + dbZoneId.substring(1);
            } else if (dbZoneId.startsWith("-")) {
                dbZoneId = "GMT%2D" + dbZoneId.substring(1);
            }
        } else {
            // 兼容两种格式的SYSTEM_DEFAULT_ZONE_ID：Asia/Shanghai、GMT+08:00
            dbZoneId = SYSTEM_DEFAULT_ZONE_ID
                .replace("\\+", "%2B")
                .replace("-", "%2D");
        }
        log.info("dbZoneId={}, DB_ZONE_ID={}, DB_ZONE_OFFSET={}, SYSTEM_DEFAULT_ZONE_ID={}",
            dbZoneId, ENV_DB_ZONE_ID, ENV_DB_ZONE_OFFSET, SYSTEM_DEFAULT_ZONE_ID);

        return dbZoneId;
    }

    /**
     *
     * @param dbHost
     * @param dbPort
     * @param dbSchema
     * @param dbZoneId
     * @return url
     */
    public static String getMySQLOrDRDSJdbcUrl(@NonNull String dbHost, @NonNull String dbPort,
                                               String dbSchema, @NonNull String dbZoneId) {
        if (dbSchema == null) {
            dbSchema = "";
        }
        String jdbcUrl = String.format(JDBC_URL_PATTERN, dbHost, dbPort, dbSchema, dbZoneId);
        if (Boolean.parseBoolean(MYSQL_USE_SSL)) {
            // 开启MySQL的SSL：需要在erda上添加两个环境变量，1）MYSQL_USE_SSL，值为true；2）ApsaraDB-CA-Chain.jks，文件类型，值为上传的证书文件（由erda或数据库厂商提供）
            jdbcUrl = jdbcUrl + "&useSSL=true&requireSSL=true&verifyServerCertificate=true&"
                + "clientCertificateKeyStoreUrl=file:/init-data/ApsaraDB-CA-Chain.jks&clientCertificateKeyStorePassword"
                + "=apsaradb&trustCertificateKeyStoreUrl=file:/init-data/ApsaraDB-CA-Chain.jks&"
                + "trustCertificateKeyStorePassword=apsaradb";
        } else {
            jdbcUrl = jdbcUrl + "&useSSL=false";
        }
        log.info("jdbcUrl={}", jdbcUrl);
        return jdbcUrl;
    }

    public static String getOracleJdbcUrl(String dbHost, String dbPort, String schema) {
        String jdbcUrl = "jdbc:oracle:thin:@" + dbHost + ":" + dbPort;
        if (schema != null) {
            jdbcUrl += "/" + schema;
        }
        log.info("jdbcUrl={}", jdbcUrl);
        return jdbcUrl;
    }

    public static String getH2JdbcUrl(String schema, String baseDir) {
        String urlPart = String.format(H2_JDBC_URL_PATTERN, StringUtils.isEmpty(baseDir) ? "mem" : "file");
        String schemaPart = StringUtils.isEmpty(schema) ? "" : String.format(H2_SCHEMA_PATTERN, schema);
        return urlPart + schemaPart;
    }
}
