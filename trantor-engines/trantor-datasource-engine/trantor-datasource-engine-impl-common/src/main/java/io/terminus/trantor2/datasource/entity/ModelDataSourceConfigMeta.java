package io.terminus.trantor2.datasource.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.common.storage.jpa.TeamBaseMetaEntity;
import io.terminus.trantor2.datasource.converter.ModelDataSourceExtraConfigMetaConverter;
import io.terminus.trantor2.datasource.converter.SubDataSourceConfigConverter;
import io.terminus.trantor2.datasource.model.DataSourceExtraConfigMeta;
import io.terminus.trantor2.datasource.model.SupportDialectTypeEnum;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.springframework.util.CollectionUtils;

import jakarta.persistence.*;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 模型业务数据源bean
 */
@Entity
@SuperBuilder
@Getter
@Setter
@NoArgsConstructor
@DynamicInsert
@DynamicUpdate
@Schema(description = "业务数据源结构")
@Table(
    indexes = {
        @Index(name = "uk_dataSourceName_unique", columnList = "teamId,dataSourceName", unique = true)
    }
)
public class ModelDataSourceConfigMeta extends TeamBaseMetaEntity {

    /**
     * 项目标识
     */
    private String teamCode;

    /**
     * 数据源唯一标识，数据库表唯一，所有数据源中唯一
     */
    private String dataSourceName;
    /**
     * 库名
     */
    private String dbName;
    /**
     * 数据库类型
     */
    @Enumerated
    private SupportDialectTypeEnum dbType;

    /**
     * host
     */
    private String dbHost;
    /**
     * 端口
     */
    private Integer dbPort;
    /**
     * 用户名
     */
    private String dbUsername;
    /**
     * 密码
     */
    private String dbPassword;

    /**
     * 数据源其余配置参数,包括连接池等
     */
    @Column(columnDefinition = "mediumtext DEFAULT NULL COMMENT '扩展字段'")
    @Convert(converter = ModelDataSourceExtraConfigMetaConverter.class)
    private DataSourceExtraConfigMeta extraConfig;

    private Boolean defaultConfig;

    /**
     * 数据源是否开启分库
     */
    @Column(columnDefinition = "int default 0 COMMENT '是否开启分库'", nullable = false)
    private Boolean needDbPartition = false;

    /**
     * 数据源子库配置
     */
    @Column(columnDefinition = "mediumtext DEFAULT NULL COMMENT '子库配置'")
    @Convert(converter = SubDataSourceConfigConverter.class)
    private List<ModelDataSourceConfigMeta> subDataSourceConfigs;

    /**
     * 合并最新的数据源配置
     *
     * @param dataSourceConfigDto
     */
    public void merge(ModelDataSourceConfigMeta dataSourceConfigDto) {
        setDbType(dataSourceConfigDto.getDbType());
        setDbName(dataSourceConfigDto.getDbName());
        setDbHost(dataSourceConfigDto.getDbHost());
        setDbPort(dataSourceConfigDto.getDbPort());
        setDbUsername(dataSourceConfigDto.getDbUsername());
        if (StringUtils.isNotEmpty(dataSourceConfigDto.getDbPassword())) {
            setDbPassword(dataSourceConfigDto.getDbPassword());
        }
        setNeedDbPartition(dataSourceConfigDto.getNeedDbPartition());


        if (CollectionUtils.isEmpty(getSubDataSourceConfigs())
            || CollectionUtils.isEmpty(dataSourceConfigDto.getSubDataSourceConfigs())) {
            setSubDataSourceConfigs(dataSourceConfigDto.getSubDataSourceConfigs());
        } else {
            // 数据源更新时，合并子数据源的信息，需要支持子数据源也没有入参数据库密码的场景
            Map<String, ModelDataSourceConfigMeta> dbConfigMap = getSubDataSourceConfigs().stream()
                .collect(Collectors.toMap(ModelDataSourceConfigMeta::getDataSourceName, Function.identity()));

            dataSourceConfigDto.getSubDataSourceConfigs().forEach(subDbConfig -> {
                if (StringUtils.isNotEmpty(subDbConfig.getDbPassword())) {
                    return;
                }
                ModelDataSourceConfigMeta metaInDb = dbConfigMap.get(subDbConfig.getDataSourceName());
                if (null != metaInDb) {
                    subDbConfig.setDbPassword(metaInDb.getDbPassword());
                }
            });
            setSubDataSourceConfigs(dataSourceConfigDto.getSubDataSourceConfigs());
        }

    }

    /**
     * equals
     *
     * @param o
     * @return true：是；false：否
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) { //NOSONAR
            return false;
        }
        ModelDataSourceConfigMeta that = (ModelDataSourceConfigMeta) o;
        return dataSourceName.equals(that.dataSourceName) && dbName.equals(that.dbName) && dbType == that.dbType
            && dbHost.equals(that.dbHost) && dbPort.equals(that.dbPort) && dbUsername.equals(that.dbUsername)
            && Objects.equals(dbPassword, that.dbPassword);
    }

    /**
     * hash code
     *
     * @return hash code
     */
    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), dataSourceName, dbName, dbType, dbHost, dbPort, dbUsername, dbPassword);
    }
}
