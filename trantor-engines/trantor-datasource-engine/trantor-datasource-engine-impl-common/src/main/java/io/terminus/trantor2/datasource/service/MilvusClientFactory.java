package io.terminus.trantor2.datasource.service;

import io.milvus.v2.client.ConnectConfig;
import io.milvus.v2.client.MilvusClientV2;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.datasource.exception.BusinessDataSourceException;
import io.terminus.trantor2.datasource.exception.DatasourceException;
import io.terminus.trantor2.datasource.model.DataSourceConfigDTO;
import io.terminus.trantor2.datasource.model.ModuleDataSourceDTO;
import io.terminus.trantor2.datasource.model.SupportDialectTypeEnum;
import io.terminus.trantor2.module.service.TeamService;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;

/**
 * Milvus客户端工厂类
 * 用于根据DataSourceConfigService获取的连接信息创建MilvusClientV2客户端实例
 * 支持内存缓存，避免重复创建客户端
 *
 * @author: trantor
 * @date: 2025/01/01
 */
@Slf4j
@Service
public class MilvusClientFactory {

    @Autowired
    private DataSourceConfigService dataSourceConfigService;
    @Autowired
    private TeamService teamService;

    /**
     * 客户端缓存，键为 teamCode_moduleKey，值为 MilvusClientV2 实例
     */
    private final ConcurrentHashMap<String, MilvusClientV2> clientCache = new ConcurrentHashMap<>();

    /**
     * 根据 teamId 和数据源名称创建 MilvusClientV2 客户端
     * 支持缓存，如果已存在则直接返回缓存的客户端
     *
     * @param teamId 团队ID
     * @param datasourceName 数据源名称
     * @return MilvusClientV2实例
     * @throws BusinessDataSourceException 当数据源不存在或配置错误时抛出异常
     */
    public MilvusClientV2 getClient(String teamCode, String moduleKey) {
        Long teamId = teamService.getTeamIdByCode(teamCode);
        ModuleDataSourceDTO moduleDataSource = dataSourceConfigService.queryModuleDataSourceConfig(teamId, moduleKey);
        if (moduleDataSource == null || StringUtils.isEmpty(moduleDataSource.getVectorDatasource())) {
            log.warn("No vector datasource configured for module {}, team: {}", moduleKey, teamCode);
            throw new DatasourceException(ErrorType.MODEL_DATASOURCE_NOT_EXIST, "No vector datasource configured for module " + moduleKey);
        }
        String cacheKey = generateCacheKey(teamCode, moduleKey);

        // 先检查缓存
        MilvusClientV2 cachedClient = clientCache.get(cacheKey);
        if (cachedClient != null) {
            if (BooleanUtils.isTrue(cachedClient.checkHealth().getIsHealthy())) {
                log.debug("Returning cached Milvus client for teamId: {}, module: {}", teamCode, moduleKey);
                return cachedClient;
            } else {
                // 从缓存拿到 client 后，不健康的连接从缓存剔除
                evictCache(teamCode, moduleKey);
            }
        }

        log.info("Creating new Milvus client for team: {}, module: {}", teamCode, moduleKey);

        // 从DataSourceConfigService获取数据源配置
        DataSourceConfigDTO dataSourceConfig = dataSourceConfigService.queryDataSource(teamId, moduleDataSource.getVectorDatasource(), true);

        if (dataSourceConfig == null) {
            log.info(String.format("Milvus datasource not found for team: %s, module: %s", teamCode, moduleKey));
            var dataSourceConfigs = dataSourceConfigService.queryDataSource(teamId, true);

            // 查找第一个 MILVUS 类型的数据源
            dataSourceConfig = dataSourceConfigs.stream()
                    .filter(config -> config.getDbType() == SupportDialectTypeEnum.MILVUS)
                    .findFirst()
                    .orElse(null);
        }

        if (dataSourceConfig == null) {
            String errMsg = String.format("Milvus datasource not found for team: %s, module: %s", teamCode, moduleKey);
            log.error(errMsg);
            throw new BusinessDataSourceException(ErrorType.MODEL_DATASOURCE_NOT_EXIST, errMsg, new Object[]{moduleDataSource.getVectorDatasource()});
        }

        // 验证数据源类型
        if (dataSourceConfig.getDbType() != SupportDialectTypeEnum.MILVUS) {
            String errMsg = String.format("Datasource type is not MILVUS for team: %s, datasource: %s, actual type: %s",
                    teamCode, moduleDataSource.getVectorDatasource(), dataSourceConfig.getDbType());
            log.error(errMsg);
            throw new BusinessDataSourceException(ErrorType.UN_SUPPORT_DB_TYPE_ERROR, errMsg, new Object[]{dataSourceConfig.getDbType()});
        }

        MilvusClientV2 client = getClient(dataSourceConfig);

        // 将新创建的客户端放入缓存
        clientCache.put(cacheKey, client);
        log.info("Cached Milvus client for team: {}, module: {}", teamCode, moduleKey);

        return client;
    }

    /**
     * 根据数据源配置创建MilvusClientV2客户端
     * 此方法不使用缓存，直接创建新的客户端实例
     *
     * @param dataSourceConfig 数据源配置
     * @return MilvusClientV2实例
     * @throws BusinessDataSourceException 当配置参数无效时抛出异常
     */
    public MilvusClientV2 getClient(DataSourceConfigDTO dataSourceConfig) {
        validateConfig(dataSourceConfig);

        try {
            // 构建连接URI：http://host:port
            String uri = String.format("http://%s:%s", dataSourceConfig.getDbHost(), dataSourceConfig.getDbPort());

            log.info("Connecting to Milvus at: {} with username: {}", uri, dataSourceConfig.getDbUsername());

            // 按照官方文档创建ConnectConfig
            ConnectConfig connectConfig = ConnectConfig.builder()
                    .uri(uri)
                    .username(dataSourceConfig.getDbUsername())
                    .password(dataSourceConfig.getDbPassword())
                    .dbName(dataSourceConfig.getDbName())
                    .build();

            // 创建MilvusClientV2实例
            MilvusClientV2 client = new MilvusClientV2(connectConfig);

            log.info("Successfully created MilvusClientV2 for datasource: {}", dataSourceConfig.getDataSourceName());
            return client;

        } catch (Exception e) {
            String errMsg = String.format("Failed to create Milvus client for datasource: %s, error: %s",
                    dataSourceConfig.getDataSourceName(), e.getMessage());
            log.error(errMsg, e);
            throw new BusinessDataSourceException(ErrorType.DATASOURCE_UNKNOWN_ERROR, errMsg, new Object[]{errMsg});
        }
    }

    /**
     * 清除指定客户端的缓存
     *
     * @param teamCode 团队ID
     * @param moduleKey 数据源名称
     */
    public void evictCache(String teamCode, String moduleKey) {
        String cacheKey = generateCacheKey(teamCode, moduleKey);
        MilvusClientV2 removedClient = clientCache.remove(cacheKey);
        if (removedClient != null) {
            try {
                removedClient.close();
                log.info("Evicted and closed Milvus client cache for team: {}, module: {}", teamCode, moduleKey);
            } catch (Exception e) {
                log.warn("Failed to close Milvus client while evicting cache: {}", e.getMessage());
            }
        }
    }

    /**
     * 清除所有客户端缓存
     */
    public void clearCache() {
        log.info("Clearing all Milvus client cache, current cache size: {}", clientCache.size());
        clientCache.forEach((key, client) -> {
            try {
                client.close();
            } catch (Exception e) {
                log.warn("Failed to close Milvus client for key {}: {}", key, e.getMessage());
            }
        });
        clientCache.clear();
        log.info("All Milvus client cache cleared");
    }

    /**
     * 获取当前缓存的客户端数量
     *
     * @return 缓存大小
     */
    public int getCacheSize() {
        return clientCache.size();
    }

    /**
     * 检查指定的客户端是否已缓存
     *
     * @param teamCode 团队ID
     * @param moduleKey 数据源名称
     * @return 是否已缓存
     */
    public boolean isCached(String teamCode, String moduleKey) {
        String cacheKey = generateCacheKey(teamCode, moduleKey);
        return clientCache.containsKey(cacheKey);
    }

    /**
     * 生成缓存键
     *
     * @param teamCode 团队ID
     * @param moduleKey 数据源名称
     * @return 缓存键
     */
    private String generateCacheKey(String teamCode, String moduleKey) {
        return teamCode + "_" + moduleKey;
    }

    /**
     * 在Bean销毁时清理所有缓存的客户端
     */
    @PreDestroy
    public void destroy() {
        log.info("MilvusClientFactory is being destroyed, cleaning up cached clients");
        clearCache();
    }

    /**
     * 验证数据源配置参数
     *
     * @param config 数据源配置
     * @throws BusinessDataSourceException 当必要参数缺失时抛出异常
     */
    private void validateConfig(DataSourceConfigDTO config) {
        if (config == null) {
            throw new BusinessDataSourceException(ErrorType.MODEL_DATASOURCE_NOT_EXIST, "DataSource config is null", new Object[]{});
        }

        if (StringUtils.isBlank(config.getDbHost())) {
            throw new BusinessDataSourceException(ErrorType.DATASOURCE_PARAM_CHECK_ERROR, "Milvus host is required", new Object[]{});
        }

        if (config.getDbPort() == null || config.getDbPort() <= 0) {
            throw new BusinessDataSourceException(ErrorType.DATASOURCE_PARAM_CHECK_ERROR, "Milvus port is required and must be positive", new Object[]{});
        }

        if (StringUtils.isBlank(config.getDbUsername())) {
            throw new BusinessDataSourceException(ErrorType.DATASOURCE_PARAM_CHECK_ERROR, "Milvus username is required", new Object[]{});
        }

        if (StringUtils.isBlank(config.getDbPassword())) {
            throw new BusinessDataSourceException(ErrorType.DATASOURCE_PARAM_CHECK_ERROR, "Milvus password is required", new Object[]{});
        }

        log.debug("Milvus config validation passed for datasource: {}", config.getDataSourceName());
    }
}