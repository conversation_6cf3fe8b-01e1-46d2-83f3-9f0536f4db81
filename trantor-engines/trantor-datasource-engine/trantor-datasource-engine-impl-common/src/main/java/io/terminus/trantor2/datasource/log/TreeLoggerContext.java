package io.terminus.trantor2.datasource.log;


import io.terminus.trantor2.datasource.enums.CacheFunctionType;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public final class TreeLoggerContext {

    private TreeLoggerContext() {

    }

    /**
     * 栈深度
     */
    private static final int MAX_STACK_TRACE_DEPTH = 10;

    /**
     * STACK_TRACE_START_INDEX
     */
    private static final int STACK_TRACE_START_INDEX = 3;

    /**
     * 代理方法标记
     */
    private static final String PROXY_METHOD_MARK = "$$FastClassBySpringCGLIB$$";

    /**
     * log ThreadLocal
     */
    private static ThreadLocal<PrintableTree> loggerThreadLocal = new ThreadLocal<>(); //NOSONAR
    /**
     * stackTrace ThreadLocal
     */
    private static ThreadLocal<Map<String, PrintableTree>> stackTraceMap = new ThreadLocal<>(); //NOSONAR

    /**
     * get PrintableTree
     *
     * @return PrintableTree
     */
    public static PrintableTree get() {
        return loggerThreadLocal.get();
    }

    /**
     * set PrintableTree
     *
     * @param tree
     */
    public static void set(PrintableTree tree) {
        loggerThreadLocal.set(tree);
    }

    /**
     * get stack trace map
     *
     * @return getStartTraceMap
     */
    public static synchronized Map<String, PrintableTree> getStartTraceMap() {
        Map<String, PrintableTree> stringPrintableTreeMap = stackTraceMap.get();
        if (stringPrintableTreeMap == null) {
            stringPrintableTreeMap = new HashMap<>();
            stackTraceMap.set(stringPrintableTreeMap);
        }
        return stringPrintableTreeMap;
    }

    /**
     * getCurrentTree
     *
     * @param stackTrace
     * @return PrintableTree
     */
    public static PrintableTree getCurrentTree(StackTraceElement[] stackTrace) {
        Map<String, PrintableTree> startTraceMap = TreeLoggerContext.getStartTraceMap();
        for (int i = 1; i < stackTrace.length; i++) {
            StackTraceElement stackTraceElement = stackTrace[i];
            String methodName = getStackTraceId(stackTraceElement);
            if (!methodName.contains("io.terminus.trantor2") || i > MAX_STACK_TRACE_DEPTH) {
                break;
            }
            // 确保可重入，当如果当前堆栈已经出现过，说明现在的节点是新的节点，map中只需要维护最新的节点
            if (i == 1) {
                PrintableTree tree = new PrintableTree();
                startTraceMap.put(methodName, tree);
            }
        }
        return startTraceMap.get(getStackTraceId(stackTrace[1]));
    }

    /**
     * getParentTree
     *
     * @param stackTrace
     * @return PrintableTree
     */
    public static PrintableTree getParentTree(StackTraceElement[] stackTrace) {
        Map<String, PrintableTree> startTraceMap = TreeLoggerContext.getStartTraceMap();
        StackTraceElement stackTraceElement = stackTrace[2];
        if (stackTraceElement.getClassName().contains(PROXY_METHOD_MARK)) {
            // 被切面代理过的方法需要找到真实的parent trace
            boolean flag = false;
            for (int i = STACK_TRACE_START_INDEX; i < stackTrace.length; i++) {
                StackTraceElement parent = stackTrace[i];
                String methodName = getStackTraceId(parent);
                if (methodName.contains("io.terminus.autumn")) {
                    if (flag) {
                        stackTraceElement = stackTrace[i];
                        break;
                    }
                    flag = true;

                }
            }
        }
        String stackTraceId = getStackTraceId(stackTraceElement);
        if (startTraceMap.containsKey(stackTraceId)) {
            return startTraceMap.get(stackTraceId);
        }
        // 如果无法根据上一层堆栈找到父节点，则直接使用根节点作为当前的父节点
        return get();
    }

    /**
     * getStackTraceId
     *
     * @param stackTraceElement
     * @return stack trace id
     */
    public static String getStackTraceId(StackTraceElement stackTraceElement) {
        return stackTraceElement.getClassName() + stackTraceElement.getMethodName();
    }

    /**
     * getPrintableTree
     *
     * @param stackTrace
     * @return PrintableTree
     */
    public static PrintableTree getPrintableTree(StackTraceElement[] stackTrace) {
        PrintableTree parent = TreeLoggerContext.get();
        PrintableTree currentTree = getCurrentTree(stackTrace);
        if (parent == null) {
            parent = new PrintableTree();
            TreeLoggerContext.set(parent);
        } else {
            parent = getParentTree(stackTrace);
        }
        parent.addChild(currentTree);
        currentTree.setStartTime(System.currentTimeMillis());
        return currentTree;
    }

    /**
     * setUpRootTree
     */
    public static void setUpRootTree() {
        TreeLoggerContext.set(new PrintableTree());
    }

    /**
     * loggerThreadLocal、stackTraceMap清理
     */
    public static void remove() {
        loggerThreadLocal.remove();
        stackTraceMap.remove();
    }

    /**
     * 测试用main主方法
     *
     * @param args
     */
    public static void main(String[] args) {
        // root
        //└──operation:[CLEAR], type:[a cache], projectId:[a], cost:[58]
        //   ├──operation:[CLEAR], type:[b cache], projectId:[a], cost:[55]
        //   │  └──operation:[CLEAR], type:[c cache], projectId:[a], cost:[2]
        //   ├──operation:[CLEAR], type:[b cache], projectId:[a], cost:[1]
        //   │  └──operation:[CLEAR], type:[c cache], projectId:[a], cost:[0]
        //   └──operation:[CLEAR], type:[b cache], projectId:[a], cost:[0]
        //      └──operation:[CLEAR], type:[c cache], projectId:[a], cost:[0]
        TreeLoggerContext.methodA();
        PrintableTree tree = TreeLoggerContext.get();
        if (tree != null) {
            tree.print();
        }
    }

    /**
     * 测试用
     */
    public static void methodA() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        PrintableTree currentTree = getPrintableTree(stackTrace);
        try {
            for (int i = 0; i < STACK_TRACE_START_INDEX; i++) {
                methodB();
            }
        } finally {
            currentTree.buildValue(CacheFunctionType.CLEAR, "a cache", "a");
        }
    }

    /**
     * 测试用
     */
    public static void methodB() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        PrintableTree currentTree = getPrintableTree(stackTrace);

        try {
            methodC();
        } finally {
            currentTree.buildValue(CacheFunctionType.CLEAR, "b cache", "a");
        }
    }

    /**
     * 测试用
     */
    public static void methodC() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        PrintableTree currentTree = getPrintableTree(stackTrace);

        currentTree.buildValue(CacheFunctionType.CLEAR, "c cache", "a");
    }
}
