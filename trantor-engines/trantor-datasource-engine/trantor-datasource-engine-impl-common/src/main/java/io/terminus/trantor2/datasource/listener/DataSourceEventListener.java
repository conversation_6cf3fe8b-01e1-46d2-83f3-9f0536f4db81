package io.terminus.trantor2.datasource.listener;

import io.terminus.trantor2.datasource.manager.BusinessDDLDataSourceHolderManager;
import io.terminus.trantor2.datasource.manager.BusinessDMLDataSourceHolderManager;
import io.terminus.trantor2.datasource.service.DataSourceConfigServiceImpl;
import io.terminus.trantor2.meta.event.DatasourceDeleteEvent;
import io.terminus.trantor2.meta.event.DatasourceUpdateEvent;
import io.terminus.trantor2.meta.event.DefaultDatasourceSwitchEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

/**
 * 数据源变更监听器
 * 监听：
 * 1、数据源修改
 * 2、数据源删除
 */
@Service
@Slf4j
public class DataSourceEventListener {

    /**
     * 业务数据源管理类
     */
    @Autowired
    private BusinessDMLDataSourceHolderManager businessDMLDataSourceManager;

    @Autowired
    private BusinessDDLDataSourceHolderManager businessDDLDataSourceManager;


    @Autowired
    private DataSourceConfigServiceImpl dataSourceConfigService;

    /**
     * 数据源更新事件
     *
     * @param event
     */
    @EventListener(DatasourceUpdateEvent.class)
    @Order(1)
    public void handleDataSourceUpdate(DatasourceUpdateEvent event) {
        log.info("received DatasourceUpdateEvent:{}", event);
        businessDMLDataSourceManager.cleanByDataSourceName(event.getTeamId(), event.getDatasourceName());
        businessDDLDataSourceManager.cleanByDataSourceName(event.getTeamId(), event.getDatasourceName());
    }

    /**
     * 数据源删除事件
     *
     * @param event
     */
    @EventListener(DatasourceDeleteEvent.class)
    @Order(1)
    public void handleDataSourceDelete(DatasourceDeleteEvent event) {
        log.info("received DatasourceDeleteEvent:{}", event);
        businessDMLDataSourceManager.cleanByDataSourceName(event.getTeamId(), event.getDatasourceName());
        businessDDLDataSourceManager.cleanByDataSourceName(event.getTeamId(), event.getDatasourceName());
    }

    /**
     * 默认数据源切换事件
     *
     * @param event
     */
    @EventListener(DefaultDatasourceSwitchEvent.class)
    @Order(1)
    public void handleDefaultDatasourceSwitchEvent(DefaultDatasourceSwitchEvent event) {
        log.info("received DefaultDatasourceSwitchEvent:{}", event);
        dataSourceConfigService.defaultDatasourceSwitch(event.getTeamId());
    }
}
