package io.terminus.trantor2.datasource.manager;

import com.google.common.collect.Maps;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.datasource.dialect.MultiDbDataSourceProcessorTemplate;
import io.terminus.trantor2.datasource.enums.DataSourceEnvType;
import io.terminus.trantor2.datasource.exception.BusinessDataSourceException;
import io.terminus.trantor2.datasource.model.DataSourceConfigDTO;
import io.terminus.trantor2.datasource.model.ModuleDataSourceDTO;
import io.terminus.trantor2.datasource.model.sharding.TableShardingConfig;
import io.terminus.trantor2.datasource.service.DataSourceConfigServiceImpl;
import io.terminus.trantor2.model.management.meta.util.AliasUtil;
import io.terminus.trantor2.module.meta.ModelShardingConfig;
import io.terminus.trantor2.properties.DatasourceProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 业务数据源连接器
 */
@Component
@Slf4j
public class BusinessDataSourceActionConnector {
    /**
     * 业务数据源service
     */
    @Autowired
    private DataSourceConfigServiceImpl modelDataSourceService;

    /**
     * 多数据库类型处理
     */
    @Autowired
    private MultiDbDataSourceProcessorTemplate multiDbProcessorTemplate;

    /**
     * 数据源相关配置
     */
    @Autowired
    private DatasourceProperties datasourceProperties;

    /**
     * BusinessDataSourceAction创建
     *
     * @param teamId            teamId
     * @param dataSourceEnvType
     * @return BusinessDataSourceAction
     */
    public BusinessDataSourceAction getBusinessDataSourceAction(final long teamId, final String datasourceName,
                                                                DataSourceEnvType dataSourceEnvType) {
        DataSourceConfigDTO modelDataSourceConfigMeta = getDataSourceConfig(teamId, datasourceName);
        log.info("get dataSourceConfigMetas when getBusinessDMLDataSourceAction, teamId:{},datasourceName:{},meta:{}",
            teamId, datasourceName, modelDataSourceConfigMeta);
        if (null == modelDataSourceConfigMeta) {
            throw new BusinessDataSourceException(ErrorType.MODEL_DATASOURCE_NOT_EXIST,
                String.format(ErrorType.MODEL_DATASOURCE_NOT_EXIST.getMessage(), "TeamId:" + teamId + ": datasourceName:" + datasourceName),
                new Object[]{"TeamId:" + teamId + ": datasourceName:" + datasourceName});
        }

        // 获取模型分表配置信息，需要获取所有使用该数据源的所有模块下的所有分表规则
        List<TableShardingConfig> tableShardingConfigs = getShardingConfigByDatasource(teamId, datasourceName);
        log.info("get tableShardingConfigs by modules when getBusinessDMLDataSourceAction, teamId:{},datasourceName:{},tableShardingConfigs:{}",
            teamId, datasourceName, tableShardingConfigs);

        return new BusinessSingleDbDataSourceAction(modelDataSourceConfigMeta, multiDbProcessorTemplate,
            tableShardingConfigs, datasourceProperties.isShardingSphereSqlShow(), dataSourceEnvType);
    }

    /**
     * 查询指定模块下的所有分表配置信息
     *
     * @param teamId
     * @param datasourceName
     * @return
     */
    public List<TableShardingConfig> getShardingConfigByDatasource(long teamId, String datasourceName) {
        return modelDataSourceService.getShardingConfigByDatasource(teamId, datasourceName);
    }

    /**
     * 查询指定模型下的分表配置信息
     *
     * @param teamId
     * @param modelKey
     * @return
     */
    public TableShardingConfig getTableShardingConfig(long teamId, String modelKey) {
        return modelDataSourceService.getShardingConfigByModel(teamId, modelKey);
    }

    /**
     * 根据datasource name 查询使用该datasource的所有模块信息
     *
     * @param teamId
     * @param datasourceName
     * @return
     */
    private List<String> getModuleNamesByDatasource(long teamId, String datasourceName) {
        List<String> modules = new ArrayList<>();
        Map<String, String> moduleDatasourceMap = getModuleAndDataSourceMap(teamId);
        moduleDatasourceMap.forEach((module, moduleDatasource) -> {
            if (moduleDatasource.equalsIgnoreCase(datasourceName)) {
                modules.add(module);
            }
        });
        return modules;
    }

    /**
     * 获取team下所有的数据源信息
     *
     * @param teamId
     * @return
     */
    public DataSourceConfigDTO getDataSourceConfig(long teamId, String datasourceName) {
        return modelDataSourceService.queryDataSource(teamId, datasourceName, true);
    }

    /**
     * 获取模块对应的数据源名
     *
     * @param teamId
     * @param moduleName
     * @return
     */
    public String getDataSourceNameByModule(long teamId, String moduleName) {
        ModuleDataSourceDTO moduleDataSourceDTO = modelDataSourceService.queryModuleDataSourceConfig(teamId, moduleName);
        return moduleDataSourceDTO.getDataSourceName();
    }

    /**
     * 获取team下默认数据源名
     *
     * @param teamId
     * @return
     */
    public String getTeamDefaultDataSourceName(long teamId) {
        return modelDataSourceService.queryDefaultDatasourceName(teamId);
    }


    /**
     * 获取moduleKey同datasourceName的映射关系
     *
     * @param teamId
     * @return
     */
    public Map<String, String> getModuleAndDataSourceMap(long teamId) {
        List<ModuleDataSourceDTO> moduleDataSourceDTOs = modelDataSourceService.queryModuleDataSourceConfig(teamId);
        if (CollectionUtils.isEmpty(moduleDataSourceDTOs)) {
            return Maps.newHashMap();
        }
        Map<String, String> moduleDatasourceMap = new HashMap<>();
        moduleDataSourceDTOs.forEach(dto -> {
            if (null != dto && StringUtils.isNotEmpty(dto.getDataSourceName())) {
                moduleDatasourceMap.put(dto.getModuleKey(), dto.getDataSourceName());
            }
        });
        return moduleDatasourceMap;
    }

    public void clearCache() {
        modelDataSourceService.clearCache();
    }

    /**
     * 获取需要动态更新的模型表名
     *
     * @param teamId
     * @param module
     * @param modelKeyList
     * @return
     */
    public List<String> getNeedDynamicUpdateTablesWhenModelChange(Long teamId, String module, List<String> modelKeyList) {
        return modelDataSourceService.getNeedDynamicUpdateTablesWhenModelChange(teamId, module, modelKeyList);
    }

    public Set<String> getHasShardingConfigModules(long teamId, Set<String> modules) {
        Set<String> needInitModules = new HashSet<>();
        Map<String, ModelShardingConfig> configs = modelDataSourceService.getShardingConfigByTeam(teamId);
        configs.forEach((modelKey, config) -> {
            String module = AliasUtil.moduleKey(modelKey);
            if (modules.contains(module) && Boolean.TRUE.equals(config.getShardingEnable())) {
                needInitModules.add(module);
            }
        });
        return needInitModules;
    }
}
