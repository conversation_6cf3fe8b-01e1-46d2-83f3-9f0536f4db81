package io.terminus.trantor2.datasource.util;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.datasource.exception.BusinessDataSourceException;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * AES-CBC加密工具类
 */
@Slf4j
public final class AesCBCUtil {

    private AesCBCUtil() {

    }

    /**
     * 加密类型
     */
    public static final String AES = "AES";
    /**
     * 加密算法
     */
    public static final String AES_CIPHER = "AES/CBC/PKCS5Padding";

    /**
     * AES加密
     *
     * @param content 待加密内容
     * @param key     加密使用的key 需16位长度
     * @param iv      加密使用的偏移量 需16位长度
     * @return 加密后的内容
     */
    @SuppressWarnings("all")
    public static String encrypt(String content, String key, String iv) {
        // 对于前端的密文要先base64解密
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), AES);
        try {
            Cipher cipher = Cipher.getInstance(AES_CIPHER);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, new IvParameterSpec(iv.getBytes()));
            return Base64.getEncoder().encodeToString(cipher.doFinal(content.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception e) {
            ErrorType errorCodeEnum = ErrorType.DATA_SOURCE_PASSWORD_DECRYPT_ERROR;
            String errMsg = String.format(errorCodeEnum.getMessage(), e.getMessage());
            log.error(errMsg);
            throw new BusinessDataSourceException(errorCodeEnum, errMsg, new Object[]{e.getMessage()});
        }
    }

    /**
     * AES解密
     *
     * @param cipherStr 加密后的内容
     * @param key       加密使用的key  需16位长度
     * @param iv        加密使用的偏移量  需16位长度
     * @return 解密后的内容
     */
    public static String decrypt(String cipherStr, String key, String iv) {
        // 对于前端的密文要先base64解密
        IvParameterSpec ivParameter = new IvParameterSpec(iv.getBytes());
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), AES);
        try {
            Cipher cipher = Cipher.getInstance(AES_CIPHER);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivParameter);
            return new String(cipher.doFinal(Base64.getDecoder().decode(cipherStr)), StandardCharsets.UTF_8);
        } catch (Exception e) {
            ErrorType errorCodeEnum = ErrorType.DATA_SOURCE_PASSWORD_DECRYPT_ERROR;
            String errMsg = String.format(errorCodeEnum.getMessage(), e.getMessage());
            log.error(errMsg);
            throw new BusinessDataSourceException(errorCodeEnum, errMsg, new Object[]{e.getMessage()});
        }
    }

}
