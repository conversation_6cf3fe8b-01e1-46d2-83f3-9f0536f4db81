<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>io.terminus.trantor2</groupId>
        <artifactId>trantor-model-runtime</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>trantor-model-runtime-impl</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>commons-compiler-jdk</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>shardingsphere-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.common</groupId>
            <artifactId>terminus-spring-boot-starter-rocketmq</artifactId>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.10.2</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test-autoconfigure</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>test-container</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-meta-runtime-impl</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-model-impl-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>redisson-spring-data-33</artifactId>
                    <groupId>org.redisson</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-model-runtime-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-model-runtime-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-module-engine-runtime-api</artifactId>
        </dependency>
        <!-- todo 后续要想办法去除 management 的依赖 -->
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-model-management-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-datasource-engine-runtime-impl</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.terminus.common</groupId>
            <artifactId>terminus-spring-boot-starter-sequence</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.terminus.trantor2</groupId>
                    <artifactId>trantor-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.terminus.trantor2</groupId>
                    <artifactId>trantor-code-engine-runtime-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>trantor-rule-engine-runtime-api</artifactId>
                    <groupId>io.terminus.trantor2</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.opentelemetry</groupId>
            <artifactId>opentelemetry-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>io.opentelemetry</groupId>
            <artifactId>opentelemetry-sdk-extension-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>io.opentelemetry</groupId>
            <artifactId>opentelemetry-exporter-otlp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.openai</groupId>
            <artifactId>openai-java</artifactId>
        </dependency>
    </dependencies>

</project>
