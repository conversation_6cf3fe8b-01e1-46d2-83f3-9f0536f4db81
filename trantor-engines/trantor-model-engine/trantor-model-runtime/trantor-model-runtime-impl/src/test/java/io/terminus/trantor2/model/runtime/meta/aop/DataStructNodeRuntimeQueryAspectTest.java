package io.terminus.trantor2.model.runtime.meta.aop;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.common.dto.PortalI18nConfig;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.runtime.service.I18nRuntimeTarget;
import io.terminus.trantor2.module.runtime.service.PortalService;
import io.terminus.trantor2.test.tool.ResourceHelper;
import io.terminus.trantor2.test.tool.mysql.MysqlSpringTest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.json.AutoConfigureJson;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@DataJpaTest(
        includeFilters = {
                @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = DataStructNodeRuntimeQueryAspect.class),
        },
        properties = "spring.flyway.enabled=false"
)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@AutoConfigureJson
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class DataStructNodeRuntimeQueryAspectTest implements MysqlSpringTest {
    @Autowired
    private DataStructNodeRuntimeQueryAspect dataStructNodeRuntimeQueryAspect;
    @MockBean
    private I18nRuntimeTarget i18nRuntimeTarget;
    @MockBean
    private PortalService portalService;

    protected static final String team = "team";
    protected static final String portalCode = "portal";
    protected static final Long userId = 1L;
    protected static final String enUS = "en_US";

    @BeforeEach
    void init() {
        TrantorContext.init();
        TrantorContext.setPortalCode(portalCode);
        TrantorContext.setTeamCode(team);
        TrantorContext.setCurrentPortal(Portal.builder()
                .code(portalCode)
                .i18nConfig(new PortalI18nConfig(true, "zh_CN", Arrays.asList(
                        new PortalI18nConfig.PortalI18nLanguage("zh_CN", "中文"),
                        new PortalI18nConfig.PortalI18nLanguage(enUS, "美国英语")
                )))
                .build()
        );
        User user = new User();
        user.setId(userId);
        TrantorContext.setCurrentUser(user);

        ModuleMeta moduleMeta = new ModuleMeta();
        moduleMeta.setTeamCode(team);
        moduleMeta.setKey(portalCode);

        TrantorContext.setLang(enUS);

        Map<String, String> resourceMap = new HashMap<>();
        Map<String, String> prefixResourceMap = new HashMap<>();
        prefixResourceMap.put("model.熊猫", "panda");
        prefixResourceMap.put("model.斑马", "zebra");
        resourceMap.put("B模型", "B Model");
        resourceMap.put("名称", "name");
        resourceMap.putAll(prefixResourceMap);
        when(i18nRuntimeTarget.findI18nResources(Mockito.anyCollection()))
                .thenReturn(resourceMap);
        when(i18nRuntimeTarget.findI18nResourcesByPrefix("model."))
                .thenReturn(prefixResourceMap);
    }


    @AfterEach
    void tearDown() {
        TrantorContext.clear();
    }

    @Test
    void enhanceI18nModel_single() {
        DataStructNode dataStructNode =
                ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/model.json", DataStructNode.class);

        DataStructFieldNode nameField = dataStructNode.getChildren().get(0);
        DataStructFieldNode enumField = dataStructNode.getChildren().get(2);
        assertEquals("B模型", dataStructNode.getName());
        assertEquals("名称", nameField.getName());
        assertEquals("熊猫", enumField.getProps().getDictPros().getDictValues().get(0).get("label").toString());
        assertEquals("斑马", enumField.getProps().getDictPros().getDictValues().get(1).get("label").toString());
        assertEquals("长颈鹿", enumField.getProps().getDictPros().getDictValues().get(2).get("label").toString());
        dataStructNodeRuntimeQueryAspect.enhanceI18nModel(dataStructNode);

        // model name
        assertEquals("B Model", dataStructNode.getName());
        // field name
        assertEquals("name", nameField.getName());
        //enum value
        assertEquals("panda", enumField.getProps().getDictPros().getDictValues().get(0).get("label").toString());
        assertEquals("zebra", enumField.getProps().getDictPros().getDictValues().get(1).get("label").toString());
        assertEquals("长颈鹿", enumField.getProps().getDictPros().getDictValues().get(2).get("label").toString());
    }

    @Test
    void enhanceI18nModel_single_optional() {
        DataStructNode dataStructNode =
                ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/model.json", DataStructNode.class);

        DataStructFieldNode nameField = dataStructNode.getChildren().get(0);
        DataStructFieldNode enumField = dataStructNode.getChildren().get(2);
        assertEquals("B模型", dataStructNode.getName());
        assertEquals("名称", nameField.getName());
        assertEquals("熊猫", enumField.getProps().getDictPros().getDictValues().get(0).get("label").toString());
        assertEquals("斑马", enumField.getProps().getDictPros().getDictValues().get(1).get("label").toString());
        assertEquals("长颈鹿", enumField.getProps().getDictPros().getDictValues().get(2).get("label").toString());
        dataStructNodeRuntimeQueryAspect.enhanceI18nModel(Optional.of(dataStructNode));

        // model name
        assertEquals("B Model", dataStructNode.getName());
        // field name
        assertEquals("name", nameField.getName());
        //enum value
        assertEquals("panda", enumField.getProps().getDictPros().getDictValues().get(0).get("label").toString());
        assertEquals("zebra", enumField.getProps().getDictPros().getDictValues().get(1).get("label").toString());
        assertEquals("长颈鹿", enumField.getProps().getDictPros().getDictValues().get(2).get("label").toString());
    }

    @Test
    void enhanceI18nModel_collection() {
        List<DataStructNode> dataStructNodes = new ArrayList<>();
        DataStructNode dataStructNode =
                ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/model.json", DataStructNode.class);
        dataStructNodes.add(dataStructNode);

        DataStructFieldNode nameField = dataStructNode.getChildren().get(0);
        DataStructFieldNode enumField = dataStructNode.getChildren().get(2);
        assertEquals("B模型", dataStructNode.getName());
        assertEquals("名称", nameField.getName());
        assertEquals("熊猫", enumField.getProps().getDictPros().getDictValues().get(0).get("label").toString());
        assertEquals("斑马", enumField.getProps().getDictPros().getDictValues().get(1).get("label").toString());
        assertEquals("长颈鹿", enumField.getProps().getDictPros().getDictValues().get(2).get("label").toString());

        dataStructNodeRuntimeQueryAspect.enhanceI18nModel(dataStructNodes);

        // model name
        assertEquals("B Model", dataStructNode.getName());
        // field name
        assertEquals("name", nameField.getName());
        //enum value
        assertEquals("panda", enumField.getProps().getDictPros().getDictValues().get(0).get("label").toString());
        assertEquals("zebra", enumField.getProps().getDictPros().getDictValues().get(1).get("label").toString());
        assertEquals("长颈鹿", enumField.getProps().getDictPros().getDictValues().get(2).get("label").toString());
    }
}
