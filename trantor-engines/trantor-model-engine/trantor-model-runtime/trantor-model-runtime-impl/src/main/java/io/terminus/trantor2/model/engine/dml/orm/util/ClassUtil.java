package io.terminus.trantor2.model.engine.dml.orm.util;

import lombok.extern.slf4j.Slf4j;
import org.codehaus.commons.compiler.CompileException;
import org.codehaus.commons.compiler.CompilerFactoryFactory;
import org.codehaus.commons.compiler.ICompiler;
import org.codehaus.commons.compiler.util.resource.MapResourceCreator;
import org.codehaus.commons.compiler.util.resource.Resource;
import org.codehaus.commons.compiler.util.resource.StringResource;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

/**
 * 字节码生成工具类 使用janino框架
 *
 * <AUTHOR>
 * @since 2022/8/23
 */
@Slf4j
public final class ClassUtil {

    private static final ICompiler COMPILER;

    private static final String REPO_CLASS_NAME_SUFFIX = "Repository";

    private static final String LIB_PATH = "/Users/<USER>/Development/Repositories/terminus/trantor2/trantor-console/trantor-console-starter/target/BOOT-INF/lib/";

    private static final String APP_LIB_PATH = "/Users/<USER>/Development/Repositories/terminus/trantor2/trantor-console/trantor-console-starter/target/app/BOOT-INF/lib/";

    private static final String JAR_PATH = "/Users/<USER>/Development/Repositories/terminus/trantor2/trantor-console/trantor-console-starter/target/app/trantor2-console.jar";

    public static final Map<String, byte[]> CLASSES = new HashMap<>();

    static {
        try {
            COMPILER = CompilerFactoryFactory.getCompilerFactory("org.codehaus.commons.compiler.jdk.CompilerFactory",
                Thread.currentThread().getContextClassLoader()).newCompiler();
            COMPILER.setClassFileCreator(new MapResourceCreator(CLASSES));

            // Check if we're using extracted layers or a single JAR
            File jarFile = new File(JAR_PATH);
            File extractedLibDir = new File(APP_LIB_PATH);

            if (jarFile.exists()) {
                // Original JAR-based approach
                getLibFromJarFile();
                setupClassPathFromJar();
            } else if (extractedLibDir.exists()) {
                // New extracted layers approach
                setupClassPathFromExtractedLayers();
            } else {
                log.error("Neither {} nor {} exists. Cannot set up compiler classpath.", JAR_PATH, APP_LIB_PATH);
                throw new RuntimeException("Failed to set up compiler classpath - required paths not found");
            }
        } catch (Exception e) {
            log.error("fail to get janino compiler instance", e);
            throw new RuntimeException("fail to get janino compiler instance");
        }
    }

    private ClassUtil() {
    }

    private static void setupClassPathFromExtractedLayers() throws IOException {
        log.info("Setting up classpath from extracted layers");
        File[] files = new File[]{
                new File(getFullJarName(APP_LIB_PATH, "spring-data-commons", "2.7.5")),
                new File(getFullJarName(APP_LIB_PATH, "spring-data-relational", "2.4.5")),
                new File(getFullJarName(APP_LIB_PATH, "jackson-annotations", "2.13.4")),
                new File(getFullJarName(APP_LIB_PATH, "jackson-core", "2.13.4")),
                new File(getFullJarName(APP_LIB_PATH, "trantor-api-common", "3.0.DEV-SNAPSHOT")),
                new File(getFullJarName(APP_LIB_PATH, "trantor-impl-common", "3.0.DEV-SNAPSHOT"))
        };
        COMPILER.setClassPath(files);
    }

    private static void setupClassPathFromJar() throws IOException {
        log.info("Setting up classpath from JAR file");
        // Check where BOOT-INF/lib exists
        File bootInfLib = new File(LIB_PATH);
        String libPath = bootInfLib.exists() ? LIB_PATH : APP_LIB_PATH;

        File[] files = new File[]{
                new File(JAR_PATH),
                new File(getFullJarName(libPath, "spring-data-commons", "2.7.5")),
                new File(getFullJarName(libPath, "spring-data-relational", "2.4.5")),
                new File(getFullJarName(libPath, "jackson-annotations", "2.13.4")),
                new File(getFullJarName(libPath, "jackson-core", "2.13.4")),
                new File(getFullJarName(libPath, "trantor-api-common", "3.0.DEV-SNAPSHOT")),
                new File(getFullJarName(libPath, "trantor-impl-common", "3.0.DEV-SNAPSHOT"))
        };
        COMPILER.setClassPath(files);
    }

    private static void getLibFromJarFile() throws IOException {
        String[] cmd = {"/bin/sh", "-c", "jar -xvf /app/app.jar"};
        Process process = Runtime.getRuntime().exec(cmd);
        try (InputStream fis = process.getInputStream()) {
            InputStreamReader isr = new InputStreamReader(fis);
            BufferedReader br = new BufferedReader(isr);
            String line;
            while ((line = br.readLine()) != null) {
                log.debug(line);
            }
        }
    }

    private static String getFullJarName(String path, String jarName, String defaultVersion) throws IOException {
        String[] cmd = {"/bin/sh", "-c", String.format("ls %s | grep -i %s", path, jarName)};
        Process process = Runtime.getRuntime().exec(cmd);
        try (InputStream fis = process.getInputStream()) {
            InputStreamReader isr = new InputStreamReader(fis);
            BufferedReader br = new BufferedReader(isr);
            String line = br.readLine();
            if (line != null) {
                log.debug("find jar:[{}]", line);
                return path + line;
            }
        }
        return path + jarName + "-" + defaultVersion + ".jar";
    }

    /**
     * 基于Java源码编译成Class对象实例
     *
     * @param className      Class名
     * @param classString    Class内容
     * @param repoSourceCode repoSourceCode
     * @param packageName    生成到的package
     */
    public static void compileJavaSourceCodeToClass(String className, String classString, String repoSourceCode,
                                                    String packageName) throws CompileException, IOException {
        COMPILER.compile(new Resource[]{new StringResource(String.format("%s/%s.java", packageName, className),
            classString),
            new StringResource(String.format("%s/%s.java", packageName, className + REPO_CLASS_NAME_SUFFIX),
                repoSourceCode)});
    }

}
