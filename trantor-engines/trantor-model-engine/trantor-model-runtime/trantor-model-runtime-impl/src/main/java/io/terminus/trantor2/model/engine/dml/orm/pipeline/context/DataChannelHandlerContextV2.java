package io.terminus.trantor2.model.engine.dml.orm.pipeline.context;

import io.terminus.trantor2.model.engine.dml.orm.pipeline.DataChannelHandlerV2;
import io.terminus.trantor2.model.engine.dml.orm.pipeline.DataChannelPipelineV2;

/**
 * Handler上下文
 *
 * <AUTHOR>
 * @since 2022/8/29
 */
public interface DataChannelHandlerContextV2 {

    /**
     * 获取当前上下文中的Handler
     *
     * @return Handler
     */
    DataChannelHandlerV2 handler();

    /**
     * 获取当前Pipeline
     *
     * @return Pipeline
     */
    DataChannelPipelineV2 pipeline();

    /**
     * 处理读事件
     *
     * @param msg 消息
     */
    void fireChannelRead(Object msg);

    /**
     * 处理写事件
     *
     * @param msg 消息
     */
    void fireChannelWrite(Object msg);
}
