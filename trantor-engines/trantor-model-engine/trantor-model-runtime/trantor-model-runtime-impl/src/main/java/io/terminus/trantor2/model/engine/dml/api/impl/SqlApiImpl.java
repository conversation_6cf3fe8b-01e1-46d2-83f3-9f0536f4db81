package io.terminus.trantor2.model.engine.dml.api.impl;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.datasource.runtime.factory.BusinessDMLDataSourceFactory;
import io.terminus.trantor2.model.common.model.request.SqlApiBaseRequest;
import io.terminus.trantor2.model.common.model.request.SqlApiRequest;
import io.terminus.trantor2.model.engine.dml.orm.exception.ModelDataException;
import io.terminus.trantor2.model.engine.dml.orm.transaction.TransactionContext;
import io.terminus.trantor2.model.management.meta.util.AliasUtil;
import io.terminus.trantor2.model.runtime.api.dml.SqlApi;
import io.terminus.trantor2.model.runtime.meta.transaction.TrantorTransactional;
import io.terminus.trantor2.model.runtime.meta.transaction.aop.TrantorTransactionalAspect;
import io.terminus.trantor2.model.runtime.meta.transaction.aop.TrantorTransactionalAspectSupport;
import io.terminus.trantor2.module.service.TeamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.interceptor.TransactionAttribute;
import org.springframework.util.CollectionUtils;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class SqlApiImpl implements SqlApi {
    @Autowired
    private BusinessDMLDataSourceFactory businessDMLDataSourceFactory;
    @Autowired
    private TrantorTransactionalAspectSupport interceptor;
    @Autowired
    private TeamService teamService;

    @Override
    public List<Map<String, Object>> query(SqlApiRequest request) {
        if (log.isDebugEnabled()) {
            log.info("selectAll request: {}", JsonUtil.toJson(request));
        }
        validateCommonParams(request);
        startTransactionIfNotStarted(request);

        try {
            JdbcTemplate jdbcTemplate = obtainJdbcTemplateInstance(request);

            if (CollectionUtils.isEmpty(request.getParameters())) {
                return jdbcTemplate.queryForList(request.getSql());
            } else {
                return jdbcTemplate.queryForList(request.getSql(), request.getParameters().toArray());
            }
            // todo 数据解密 & 数据脱敏
        } finally {
            // 解决 procode 调用此方法时添加了 @Transactional 注解，延时到此方法开启事务时需要清除事务方法
            TransactionContext.removeContextIfNecessary(null, false);
        }
    }

    @Override
    @TrantorTransactional(label = TrantorTransactionalAspect.LAZY_START_TRANSACTION_LABEL)
    public int update(SqlApiRequest request) {
        log.info("update request: {}", JsonUtil.toJson(request));
        validateCommonParams(request);
        startTransactionIfNotStarted(request);

        JdbcTemplate jdbcTemplate = obtainJdbcTemplateInstance(request);

        if (CollectionUtils.isEmpty(request.getParameters())) {
            return jdbcTemplate.update(request.getSql());
        } else {
            return jdbcTemplate.update(request.getSql(), request.getParameters().toArray());
        }
    }

    private JdbcTemplate obtainJdbcTemplateInstance(SqlApiBaseRequest request) {
        DataSource dataSource = businessDMLDataSourceFactory.getDataSourceByTeamIdAndModule(request.getTeamId(), request.getModuleKey());
        return new JdbcTemplate(dataSource);
    }

    private void validateCommonParams(SqlApiBaseRequest request) {
        assert request != null;

        if (request.getTeamId() == null) {
            throw new ModelDataException(ErrorType.MODEL_MISSING_PARAM, "teamId", new Object[]{"teamId"});
        }

        if (StringUtils.isEmpty(request.getModuleKey())) {
            throw new ModelDataException(ErrorType.MODEL_MISSING_PARAM, "moduleKey", new Object[]{"moduleKey"});
        }

        if (StringUtils.isEmpty(request.getModelKey())) {
            throw new ModelDataException(ErrorType.MODEL_MISSING_PARAM, "modelKey", new Object[]{"modelKey"});
        }

        if (StringUtils.isEmpty(request.getSql())) {
            throw new ModelDataException(ErrorType.MODEL_MISSING_PARAM, "sql", new Object[]{"sql"});
        }
    }

    /**
     * 如果 procode 側添加了 @TrantorTransactional 注解，但由于 procode 側未设置事务上下文，
     * 针对此情况需要延时在执行模型API 时开启事务
     *
     * @param request
     */
    private void startTransactionIfNotStarted(SqlApiBaseRequest request) {
        if (TransactionContext.getTeamId() == null) {
            TransactionContext.setTeamId(request.getTeamId());
            if (TrantorContext.getTeamCode() == null) {
                TransactionContext.setTeamCode(teamService.getTeamCode(request.getTeamId()));
            } else {
                TransactionContext.setTeamCode(TrantorContext.getTeamCode());
            }
            TransactionContext.setModuleKey(AliasUtil.moduleKey(request.getModelKey()));
        }

        // 开启事务
        if (TransactionContext.getTransactionAttribute() != null) {
            List<TransactionAttribute> attributes = TransactionContext.getTransactionAttribute();
            log.info("starting transaction..., attributes:{}", attributes);
            attributes.forEach(attr -> {
                TrantorTransactionalAspectSupport.TrantorTransactionInfo info = interceptor.getTrantorTransactionInfo(attr);
                TransactionContext.setTransactionInfo(attr, info);
            });
        }
    }
}
