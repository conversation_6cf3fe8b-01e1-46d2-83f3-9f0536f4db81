package io.terminus.trantor2.model.engine.dml.orm.transaction;

import io.terminus.trantor2.model.runtime.meta.transaction.aop.TrantorTransactionalAspectSupport;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.interceptor.TransactionAttribute;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 事务上下文
 * <p>
 * 1、<p>存储事务相关操作时用到的 team 信息 & module 信息</p>
 * 2、存储需要延迟开启的事务TransactionAttribute
 * 3、存储延时开启的事务TransactionAttribute与TrantorTransactionalAspectSupport.TrantorTransactionInfo映射关系
 * 4、存储事务开启时对应的事务id
 * <p>
 * 事务上下文统一清理地点，参见TrantorPlatformTransactionManager.doCleanupAfterCompletion
 */
@Slf4j
public final class TransactionContext {
    private static final ThreadLocal<Context> moduleContext = new ThreadLocal<>();

    private static final ThreadLocal<List<TransactionAttribute>> attributeThreadLocal = new ThreadLocal<>();
    private static final ThreadLocal<Map<TransactionAttribute, TrantorTransactionalAspectSupport.TrantorTransactionInfo>> transactionInfoThreadLocal = new ThreadLocal<>();

    private static final ThreadLocal<List<String>> transactionIdThreadLocal = new ThreadLocal<>();

    public static void setTeamId(Long teamId) {
        if (moduleContext.get() == null) {
            moduleContext.set(new Context());
        }
        moduleContext.get().setTeamId(teamId);
    }

    public static Long getTeamId() {
        if (moduleContext.get() == null) {
            return null;
        }
        return moduleContext.get().getTeamId();
    }

    public static void setTeamCode(String teamCode) {
        if (moduleContext.get() == null) {
            moduleContext.set(new Context());
        }
        moduleContext.get().setTeamCode(teamCode);
    }

    public static String getTeamCode() {
        if (moduleContext.get() == null) {
            return null;
        }
        return moduleContext.get().getTeamCode();
    }

    public static void setModuleKey(String moduleKey) {
        if (moduleContext.get() == null) {
            moduleContext.set(new Context());
        }
        moduleContext.get().setModuleKey(moduleKey);
    }

    public static String getModuleKey() {
        if (moduleContext.get() == null) {
            return null;
        }
        return moduleContext.get().getModuleKey();
    }

    public static void remove() {
        moduleContext.remove();
        clearTransactionalInfoAndAttribute();
    }

    /**
     * 当事务上下文中存在teamCode时，可以立即开启事务
     * 目前事务上下文中teamId、teamCode、moduleKey是一起设置/清理的，事务开启的时候，需要teamCode+moduleKey获取数据源
     * moduleKey如果为空代表使用默认默认数据源
     *
     * @return
     */
    public static boolean needStartTransactionImmediate() {
        return StringUtils.isNotEmpty(TransactionContext.getTeamCode());
    }

    @Data
    static class Context {
        private Long teamId;
        private String teamCode;
        private String moduleKey;
    }

    public static void removeTransactionAttribute() {
        attributeThreadLocal.remove();
    }

    public static void setTransactionAttribute(TransactionAttribute txAttr) {
        List<TransactionAttribute> attributeList = attributeThreadLocal.get();
        if (null == attributeList) {
            attributeList = new ArrayList<>();
        }
        attributeList.add(txAttr);
        attributeThreadLocal.set(attributeList);
    }

    public static List<TransactionAttribute> getTransactionAttribute() {
        return attributeThreadLocal.get();
    }

    public static TrantorTransactionalAspectSupport.TrantorTransactionInfo getTransactionInfo(TransactionAttribute attribute) {
        Map<TransactionAttribute, TrantorTransactionalAspectSupport.TrantorTransactionInfo> map = transactionInfoThreadLocal.get();
        if (null == map) {
            return null;
        }
        return map.get(attribute);
    }

    public static Map<TransactionAttribute, TrantorTransactionalAspectSupport.TrantorTransactionInfo> getTransactionInfoMap() {
        return transactionInfoThreadLocal.get();
    }

    public static void setTransactionInfo(TransactionAttribute attribute, TrantorTransactionalAspectSupport.TrantorTransactionInfo info) {
        Map<TransactionAttribute, TrantorTransactionalAspectSupport.TrantorTransactionInfo> map = transactionInfoThreadLocal.get();
        if (null == map) {
            map = new HashMap<>();
        }
        map.put(attribute, info);
        transactionInfoThreadLocal.set(map);
    }

    public static List<String> getTransactionId() {
        return transactionIdThreadLocal.get();
    }

    public static void addTransactionId(String transactionId) {
        List<String> transactionIdLocal = transactionIdThreadLocal.get();
        if (null == transactionIdLocal) {
            transactionIdLocal = new ArrayList<>();
        }
        transactionIdLocal.add(transactionId);
        transactionIdThreadLocal.set(transactionIdLocal);
    }

    public static void deleteTransactionId(String nowTransactionId) {
        List<String> transactionIdList = transactionIdThreadLocal.get();
        if (null == transactionIdList) {
            return;
        }
        transactionIdList.remove(nowTransactionId);
    }

    public static void clearTransactionalInfoAndAttribute() {
        attributeThreadLocal.remove();
        transactionInfoThreadLocal.remove();
        transactionIdThreadLocal.remove();
    }

    /**
     * 当前线程不存在事务
     *
     * @return
     */
    public static boolean notExistTransaction() {
        return CollectionUtils.isEmpty(getTransactionId())
            && CollectionUtils.isEmpty(getTransactionAttribute())
            && CollectionUtils.isEmpty(getTransactionInfoMap());
    }

    /**
     * 延时事务未开启
     *
     * @return
     */
    public static boolean lazyTransactionNotStarted() {
        return !CollectionUtils.isEmpty(TransactionContext.getTransactionAttribute()) && CollectionUtils.isEmpty(TransactionContext.getTransactionInfoMap());
    }

    /**
     * 清理事务上下文
     * 判断依据是 transactionId thread local is empty
     *
     * @param nowTransactionId
     */
    public static void removeContextIfNecessary(String nowTransactionId, boolean needLog) {
        if (CollectionUtils.isEmpty(getTransactionId())
            && (StringUtils.isNotEmpty(getModuleKey()) || null != getTeamId() || !CollectionUtils.isEmpty(getTransactionAttribute()))) {
            if (needLog) {
                log.info("transactionId:" + nowTransactionId + " will force clear context,teamCode:"
                    + TransactionContext.getTeamCode() + ", moduleKey:" + TransactionContext.getModuleKey());
            }
            TransactionContext.remove();
        }
    }
}
