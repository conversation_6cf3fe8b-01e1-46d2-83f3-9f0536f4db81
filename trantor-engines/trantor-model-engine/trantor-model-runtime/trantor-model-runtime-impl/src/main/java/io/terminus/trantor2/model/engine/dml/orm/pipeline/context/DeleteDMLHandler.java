package io.terminus.trantor2.model.engine.dml.orm.pipeline.context;

import io.terminus.common.api.exception.BusinessException;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.datasource.model.ModuleDataSourceDTO;
import io.terminus.trantor2.datasource.service.DataSourceConfigService;
import io.terminus.trantor2.model.common.consts.ConditionLogicalOperator;
import io.terminus.trantor2.model.common.consts.ConditionType;
import io.terminus.trantor2.model.common.consts.DataQueryType;
import io.terminus.trantor2.model.common.model.condition.*;
import io.terminus.trantor2.model.common.model.request.CountRequest;
import io.terminus.trantor2.model.common.model.request.DeleteObject;
import io.terminus.trantor2.model.common.model.request.Select;
import io.terminus.trantor2.model.engine.dml.orm.exception.ModelDataException;
import io.terminus.trantor2.model.engine.dml.orm.pipeline.repository.Repository;
import io.terminus.trantor2.model.engine.dml.orm.pipeline.repository.model.CondQuery;
import io.terminus.trantor2.model.engine.dml.orm.pipeline.repository.model.ModelQuery;
import io.terminus.trantor2.model.engine.dml.orm.transaction.TransactionContext;
import io.terminus.trantor2.model.management.meta.cache.DataStructMetaCache;
import io.terminus.trantor2.model.management.meta.consts.SystemFieldGeneratorV2;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.domain.RelationMeta;
import io.terminus.trantor2.model.management.meta.enums.ModelRelationTypeEnum;
import io.terminus.trantor2.model.management.meta.util.AliasUtil;
import io.terminus.trantor2.setting.BusinessSettingConst;
import io.terminus.trantor2.setting.BusinessSettingUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 级联删除树
 *
 * <AUTHOR>
 * @since 2022/9/5
 */
@Slf4j
public class DeleteDMLHandler extends AbstractDMLHandler {
    private final DataSourceConfigService dataSourceConfigService;

    public DeleteDMLHandler(Repository repository, DataStructMetaCache modelMetaCache, DataSourceConfigService dataSourceConfigService) {
        super(repository, modelMetaCache);
        this.dataSourceConfigService = dataSourceConfigService;
    }

    /**
     * Key: model alias
     * Value: model ids
     */
    private Map<String, Set<Object>> modelIdMapping;

    /**
     * 记录当前主模型所用数据源，用于判断关联模型操作时是否存在跨库操作，同一事务内不允许跨库操作
     */
    private String  currentDatasource;

    private void init() {
        // 获取同一事务内入口时使用的数据源，用于后续的跨库逻辑判断。有两种场景: 1. 一个服务事务内操作多个模型，如 modelA、modelB，记录入口操作模型 modelA 的所属数据源 2. 主子模型跨数据源场景(理论不存在这种场景)
        ModuleDataSourceDTO moduleDataSourceDTO = dataSourceConfigService.queryModuleDataSourceConfig(TransactionContext.getTeamId(), TransactionContext.getModuleKey());
        if (moduleDataSourceDTO == null) {
            throw new ModelDataException(ErrorType.MODEL_DATASOURCE_NOT_EXIST,
                String.format(ErrorType.MODEL_DATASOURCE_NOT_EXIST.getMessage(), obj.getRequest().getModelAlias()),
                new Object[]{obj.getRequest().getModelAlias()});
        }

        currentDatasource = moduleDataSourceDTO.getDataSourceName();

        modelIdMapping = new HashMap<>();
    }

    @Override
    public void doExecute() {
        if (isSetting()) {
            // 若为业务配置模型更新
            settingDelete(obj);
            return;
        }

        init();

        // 获取需要删除的ids
        this.collectDeleteIds();
        // 删除
        this.delete(modelIdMapping);
    }

    private void delete(Map<String, Set<Object>> modelIdMapping) {
        for (Map.Entry<String, Set<Object>> deleteEntry : modelIdMapping.entrySet()) {
            doDelete(deleteEntry.getKey(), deleteEntry.getValue());
        }
    }

    private void collectDeleteIds() {
        if (obj.getRequest() instanceof CountRequest && !containsShardingField((CountRequest) obj.getRequest())) {
            throw new BusinessException("开启分表模型操作时需包含分表字段条件");
        }

        DeleteObject request = ((DeleteObject) obj.getRequest());
        List<Object> ids = request.getIds();
        if (CollectionUtils.isEmpty(ids)) {
            if (request.getConditionGroup() != null) {
                ids = findModelIds(request.getModelAlias(), request.getExtractRelation(), request.getConditionGroup());
            }
        }
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        String modelAlias = request.getModelAlias();
        modelIdMapping.put(modelAlias, new HashSet<>(ids));
        // 填充级联删除的数据 id 列表
        recursiveFindToDeleteIds(ids, modelAlias);
    }

    /**
     * @param currentMainModelIds  当前主模型id
     * @param currentMainModelName 当前主模型别名
     */
    private void recursiveFindToDeleteIds(List<Object> currentMainModelIds, String currentMainModelName) {
        List<RelationMeta> relations = modelMetaCache.getModelRelationMeta(teamId, currentMainModelName);
        if (CollectionUtils.isEmpty(relations)) {
            return;
        }
        List<RelationMeta> syncParentChild = relations.stream()
                .filter(it -> it.getRelationType() == ModelRelationTypeEnum.PARENT_CHILD)
                .filter(RelationMeta::isSync)
                .collect(Collectors.toList());
        for (RelationMeta relationMeta : syncParentChild) {
            cascadeDeleteChild(currentMainModelIds, relationMeta);
        }
    }

    private void cascadeDeleteChild(List<Object> currentMainModelIds, RelationMeta relation) {
        String childModelAlias = relation.getRelationModelAlias();
        String childModelFieldAlias = relation.getLinkModelFieldAlias();

        // 同一事务内不允许跨库操作, 判断此数据源是否与主模型的数据源为同一个，若不是同一个数据源，则存在跨库，同一事务内不允许跨库操作
        ModuleDataSourceDTO moduleDataSourceDTO = dataSourceConfigService.queryModuleDataSourceConfig(teamId, AliasUtil.moduleKey(childModelAlias));
        if (!StringUtils.equals(moduleDataSourceDTO.getDataSourceName(), currentDatasource)) {
            log.warn("transaction crossed database, teamId:{}, model:{}, current datasource: {}, entrypoint datasource: {}",
                teamId,
                childModelAlias,
                moduleDataSourceDTO.getDataSourceName(),
                currentDatasource);
            throw new RuntimeException("同一事务不可跨数据源操作");
        }

        SingleCondition condition = SingleCondition.builder()
            .field(childModelFieldAlias)
            .type(ConditionType.IN)
            .value(ConditionValue.builder().values(currentMainModelIds).build())
            .build();
        List<Condition> conditions = addDeletedConditionIfNecessary(childModelAlias, condition);
        List<Object> childModelIds = findModelIds(childModelAlias, new ConditionGroup(conditions));
        if (CollectionUtils.isEmpty(childModelIds)) {
            return;
        }

        Set<Object> modelToDeleteIds = modelIdMapping.getOrDefault(childModelAlias, new HashSet<>());
        // 已经遍历过的id需要跳过，避免环形依赖无限递归
        childModelIds.removeAll(modelToDeleteIds);
        if (childModelIds.isEmpty()) {
            return;
        }

        modelToDeleteIds.addAll(childModelIds);
        modelIdMapping.put(childModelAlias, modelToDeleteIds);

        // 递归遍历
        recursiveFindToDeleteIds(childModelIds, childModelAlias);
    }

    private void doDelete(String modelAlias, Collection<Object> ids) {
        DataStructNode modelMeta = modelMetaCache.getModelMeta(teamId, null, modelAlias);
        if (modelMeta == null) {
            throw new ModelDataException(ErrorType.MODEL_NOT_EXISTS, String.format(ErrorType.MODEL_NOT_EXISTS.getMessage(), modelAlias),new Object[]{modelAlias});
        }

        SingleCondition singleCondition = new SingleCondition(
                SystemFieldGeneratorV2.ID.getFieldAlias(),
                ConditionType.IN,
                ConditionValue.builder().values(new ArrayList<>(ids)).type(ConditionValueType.VALUE).build());
        CondQuery deleteCondition = buildQueryModel(modelAlias, singleCondition);

        DeleteObject deleteRequest = (DeleteObject)obj.getRequest();
        if (modelMeta.getProps().isPhysicalDelete() || deleteRequest.isPhysicalDelete()) {
            repository.delete(deleteCondition);
        } else {
            // 更新deleted标记
            Map<String, Object> value = new HashMap<>();
            value.put(SystemFieldGeneratorV2.DELETED.getFieldAlias(), System.currentTimeMillis());
            User currentUser = TrantorContext.getCurrentUser();
            if (Objects.nonNull(currentUser)) {
                value.put(SystemFieldGeneratorV2.UPDATED_BY.getFieldAlias(), currentUser.getId());
            }
            repository.update(buildGeneralModel(modelAlias, value), deleteCondition);
        }
    }

    private void settingDelete(DataObject obj) {
        if (obj.getType() != DataQueryType.DELETE_BY_ID) {
            throw new BusinessException("业务配置暂不支持批量删除");
        }

        DeleteObject deleteObject = (DeleteObject) obj.getRequest();
        if (CollectionUtils.isEmpty(deleteObject.getIds())) {
            throw new BusinessException("业务配置删除时 id 不可为空");
        }

        String moduleKey = AliasUtil.moduleKey(obj.getRequest().getModelAlias());

        try {
            // 删除主子关联的子模型数据
            deleteChildren(BusinessSettingUtil.composeSettingKey(moduleKey), deleteObject.getIds());
        } catch (Exception e) {
            if  (!e.getMessage().contains("更新影响的数据行数为 0")) {
                throw e;
            }
        }

        // 组装软删除条件
        ConditionGroup conditionGroup = new ConditionGroup(Arrays.asList(
                new SingleCondition(BusinessSettingConst.FIELD_ID, ConditionType.IN, new ConditionValue(null, null, deleteObject.getIds(), ConditionValueType.VALUE), ConditionLogicalOperator.AND),
                new SingleCondition(BusinessSettingConst.FIELD_GROUP_KEY, ConditionType.EQ, new ConditionValue(deleteObject.getModelAlias(), null, null, ConditionValueType.VALUE), ConditionLogicalOperator.AND),
                new SingleCondition(BusinessSettingConst.FIELD_DELETED, ConditionType.EQ, new ConditionValue(0, null, null, ConditionValueType.VALUE), null)
        ));
        CondQuery deleteCondition = buildQueryModel(BusinessSettingUtil.composeSettingKey(moduleKey), conditionGroup);

        // 软删除时更新的数据
        Map<String, Object> value = new HashMap<>();
        value.put(BusinessSettingConst.FIELD_DELETED, System.currentTimeMillis());
        User currentUser = TrantorContext.getCurrentUser();
        if (currentUser != null) {
            value.put(BusinessSettingConst.FIELD_UPDATED_BY, currentUser.getId());
        }

        repository.update(buildGeneralModel(BusinessSettingUtil.composeSettingKey(moduleKey), value), deleteCondition);
    }

    /**
     * 删除指定模型的主子关联子模型数据
     * @param groupKey 底层业务配置的 key
     * @param ids 当前业务配置 id 列表
     */
    private void deleteChildren(String groupKey, List<Object> ids) {
        // 获取当前模型的 code 列表
        ConditionGroup conditionGroup = new ConditionGroup(Arrays.asList(
            new SingleCondition(BusinessSettingConst.FIELD_ID, ConditionType.IN, new ConditionValue(null, null, ids, ConditionValueType.VALUE), ConditionLogicalOperator.AND),
            new SingleCondition(BusinessSettingConst.FIELD_DELETED, ConditionType.EQ, new ConditionValue(0, null, null, ConditionValueType.VALUE), null)
        ));

        ModelQuery queryModel = new ModelQuery(teamId, groupKey, null, conditionGroup);
        queryModel.setSelectFields(Collections.singletonList(new Select(BusinessSettingConst.FIELD_CODE)));
        List<Map<String, Object>> result = repository.findAll(queryModel);
        if (CollectionUtils.isEmpty(result)) {
            return;
        }

        // 根据 parentCode 列表删除主子关联的子模型数据
        List<Object> parentCodes = result.stream().map(x -> x.get(BusinessSettingConst.FIELD_CODE)).collect(Collectors.toList());

        ConditionGroup childrenConditionGroup = new ConditionGroup(Arrays.asList(
            new SingleCondition(BusinessSettingConst.FIELD_PARENT_CODE, ConditionType.IN, new ConditionValue(null, null, parentCodes, ConditionValueType.VALUE), ConditionLogicalOperator.AND),
            new SingleCondition(BusinessSettingConst.FIELD_DELETED, ConditionType.EQ, new ConditionValue(0, null, null, ConditionValueType.VALUE), null)
        ));
        CondQuery deleteCondition = buildQueryModel(groupKey, childrenConditionGroup);

        Map<String, Object> value = new HashMap<>();
        value.put(BusinessSettingConst.FIELD_DELETED, System.currentTimeMillis());
        User currentUser = TrantorContext.getCurrentUser();
        if (currentUser != null) {
            value.put(SystemFieldGeneratorV2.UPDATED_BY.getFieldAlias(), currentUser.getId());
        }

        repository.update(buildGeneralModel(groupKey, value), deleteCondition);
    }
}
