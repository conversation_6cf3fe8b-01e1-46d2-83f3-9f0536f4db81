package io.terminus.trantor2.model.engine.dml.orm.sql;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * ParameterRender
 *
 * <AUTHOR> Created on 2023/6/10 23:06
 */
public abstract class ParameterRender {

    public static final String DEFAULT_PARAMETER_PREFIX = "parameters";

    /**
     * 得到个参数名
     *
     * @param sequence 参数序列
     * @return 参数名
     */
    public String formatParameterName(AtomicInteger sequence) {
        return "p" + sequence.getAndIncrement();
    }

    /**
     * 获取一个参数占位符
     *
     * @param prefix        占位符前缀
     * @param parameterName 参数名
     * @return 参数占位符
     */
    public abstract String getFormattedParameterPlaceholder(String prefix, String parameterName);
}
