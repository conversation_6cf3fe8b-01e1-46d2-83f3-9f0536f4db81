package io.terminus.trantor2.model.engine.dml.orm.util;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.model.engine.dml.orm.exception.ModelDataException;
import io.terminus.trantor2.model.engine.dml.orm.cache.ModelClassCache;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static io.terminus.trantor2.common.exception.ErrorType.MODEL_FAIL_TO_GET_ID_FROM_MODEL_ENTITY;


/**
 * <AUTHOR>
 * @since 2022/8/31
 */
public class ModelEntityUtil {
    public static Object convertMapToModelEntity(Long teamId, String modelAlias,
                                                 Map<String, Object> modelMap) {
        Class modelClass = ModelClassCache.getModelClass(teamId, modelAlias);
        return JsonUtil.NON_INDENT.getObjectMapper().convertValue(modelMap, modelClass);
    }

    public static List<Map<String, Object>> convertModelEntityListToListMap(List<Object> modelEntities) {
        return modelEntities.stream().map(ModelEntityUtil::convertModelEntityToMap).collect(Collectors.toList());
    }

    public static Map<String, Object> convertModelEntityToMap(Object entity) {
        return JsonUtil.toMap(entity);
    }

    public static List<Object> convertMapToModelEntity(Long teamId, String modelAlias,
                                                       List<Map<String, Object>> modelMap) {
        Class modelClass = ModelClassCache.getModelClass(teamId, modelAlias);
        List<Object> result = new ArrayList<>();
        for (Map<String, Object> objectMap : modelMap) {
            result.add(JsonUtil.NON_INDENT.getObjectMapper().convertValue(objectMap, modelClass));
        }
        return result;
    }

    public static Object getIdFromModelEntity(Long teamId, String modelAlias, Object modelEntity) {
        try {
            Class modelClass = ModelClassCache.getModelClass(teamId, modelAlias);
            Method getIdMethod = modelClass.getMethod("getId");
            return getIdMethod.invoke(modelEntity);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            throw new ModelDataException(MODEL_FAIL_TO_GET_ID_FROM_MODEL_ENTITY,
                String.format(MODEL_FAIL_TO_GET_ID_FROM_MODEL_ENTITY.getMessage(), modelAlias),
                new  Object[]{modelAlias}
            );
        }
    }

    public static List<Object> getIdsFromModelEntity(Long teamId, String modelAlias,
                                                     Iterable<Object> modelEntities) {
        List<Object> ids = new ArrayList<>();
        for (Object modelEntity : modelEntities) {
            Object idFromModelEntity = getIdFromModelEntity(teamId, modelAlias, modelEntity);
            ids.add(idFromModelEntity);
        }
        return ids;
    }

}
