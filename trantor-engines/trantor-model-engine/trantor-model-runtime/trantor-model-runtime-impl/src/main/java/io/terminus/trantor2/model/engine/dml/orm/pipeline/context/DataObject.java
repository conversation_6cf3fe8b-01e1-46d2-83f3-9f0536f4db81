package io.terminus.trantor2.model.engine.dml.orm.pipeline.context;

import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.model.common.consts.DataQueryType;
import io.terminus.trantor2.model.common.model.BasicObject;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/8/29
 */
@AllArgsConstructor
@Data
public class DataObject {

    /**
     * 类型
     */
    private DataQueryType type;

    /**
     * 原始请求
     */
    private BasicObject request;

    /**
     * 是否可以继续执行
     */
    private boolean shouldHandle = true;

    /**
     * 响应
     */
    private Object responseData;

    public DataObject(DataQueryType type, BasicObject request) {
        this.type = type;
        this.request = request;
    }

    public Response getResponse() {
        return Response.ok(responseData);
    }
}
