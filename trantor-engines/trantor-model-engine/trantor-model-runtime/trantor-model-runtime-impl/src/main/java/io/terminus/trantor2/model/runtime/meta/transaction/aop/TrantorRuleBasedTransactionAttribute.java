package io.terminus.trantor2.model.runtime.meta.transaction.aop;

import org.springframework.transaction.interceptor.RuleBasedTransactionAttribute;

public class TrantorRuleBasedTransactionAttribute extends RuleBasedTransactionAttribute {
    @Override
    public boolean equals(Object other) {
        return this == other;
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}
