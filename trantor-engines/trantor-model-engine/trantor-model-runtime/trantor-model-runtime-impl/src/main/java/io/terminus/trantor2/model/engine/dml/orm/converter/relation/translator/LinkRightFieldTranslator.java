package io.terminus.trantor2.model.engine.dml.orm.converter.relation.translator;

import io.terminus.trantor2.model.engine.dml.orm.converter.relation.RelationNode;
import io.terminus.trantor2.model.management.meta.consts.SystemFieldGeneratorV2;
import io.terminus.trantor2.model.management.meta.system.SystemUser;
import io.terminus.trantor2.model.management.meta.util.AliasUtil;

/**
 * LinkRightFieldTranslator
 *
 * <AUTHOR> Created on 2023/8/18 10:50
 */
public class LinkRightFieldTranslator extends AbstractRightFieldTranslator {

    @Override
    public SqlNode translate(RelationNode relationNode) {
        String tableName = relationNode.getTableName();
        if (relationNode.isStart()) {
            return processStartNode(relationNode.getLinkFieldDbName());
        } else if (relationNode.isEnd()) {
            return processEndNode(tableName, relationNode.getRelationDbName(), SystemFieldGeneratorV2.ID.getFieldAlias());
        } else {
            String selectFieldName = buildLinkOrLookupProgressNode(relationNode);
            return processProgressNode(tableName, selectFieldName, relationNode.getLinkFieldDbName());
        }
    }
}
