package io.terminus.trantor2.model.engine.dml.orm.pipeline.handler;

import com.github.benmanes.caffeine.cache.Cache;
import io.terminus.trantor2.datasource.service.DataSourceConfigService;
import io.terminus.trantor2.model.engine.dml.orm.pipeline.DataChannelHandlerAdapter;
import io.terminus.trantor2.model.engine.dml.orm.pipeline.context.*;
import io.terminus.trantor2.model.engine.dml.orm.pipeline.repository.Repository;
import io.terminus.trantor2.model.management.meta.cache.DataStructMetaCache;
import io.terminus.trantor2.module.service.ModuleQueryService;
import io.terminus.trantor2.module.service.ModuleRuntimeQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/8/29
 */
@Slf4j
@Order
@SuppressWarnings("unchecked")
@Component
public class QueryHandlerV2 extends DataChannelHandlerAdapter {
    @Autowired
    private Repository repository;
    @Autowired
    private DataStructMetaCache modelMetaCache;
    @Autowired
    private DataSourceConfigService dataSourceConfigService;
    @Autowired
    private ModuleQueryService moduleQueryService;
    @Autowired
    Cache<String, Object> caffeineCache;

    @Override
    public void doHandleInput(DataChannelHandlerContextV2 dataChannelHandlerContext, DataObject obj) {
        switch (obj.getType()) {
            case CREATE:
            case BATCH_CREATE:
                new CreateDMLHandler(repository, modelMetaCache, dataSourceConfigService).execute(obj);
                break;
            case UPDATE_BY_ID:
            case BATCH_UPDATE_BY_ID:
                new UpdateDMLHandler(repository, modelMetaCache, dataSourceConfigService).execute(obj);
                break;
            case DELETE_BY_ID:
            case BATCH_DELETE_BY_ID:
                new DeleteDMLHandler(repository, modelMetaCache, dataSourceConfigService).execute(obj);
                break;
            case FIND_ONE:
            case FIND:
            case FIND_BY_ID:
            case PAGING:
                new QueryDMLHandler(repository, modelMetaCache, moduleQueryService, caffeineCache).execute(obj);
                break;
            case COUNT:
                new CountDMLHandler(repository, modelMetaCache).execute(obj);
                break;
            case AGGREGATE:
                new AggregateDMLHandler(repository, modelMetaCache).execute(obj);
                break;
            default:
                break;
        }
    }
}
