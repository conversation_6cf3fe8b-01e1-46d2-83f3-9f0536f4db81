package io.terminus.trantor2.model.engine.dml.orm.util;

/**
 * 多数据源时根据 key 获取数据源，key 的规则生成由此类控制
 */
public class DatasourceUtil {
    /**
     * 根据 teamCode + moduleKey 作为数据源的 key
     *
     * @param teamCode
     * @param module
     * @return
     */
    public static String obtainKey(String teamCode, String module) {
        return teamCode + "#" + module;
    }

    /**
     * 根据 teamId + moduleKey 作为数据源的 key
     *
     * @param teamId
     * @param module
     * @return
     */
    public static String obtainKey(Long teamId, String module) {
        return teamId + "#" + module;
    }
}
