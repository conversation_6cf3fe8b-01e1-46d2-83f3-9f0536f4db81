package io.terminus.trantor2.model.engine.dml.orm.converter.relation.translator;

import io.terminus.trantor2.model.common.model.condition.SingleCondition;
import io.terminus.trantor2.model.engine.dml.orm.converter.relation.RelationList;
import io.terminus.trantor2.model.engine.dml.orm.converter.relation.RelationNode;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/11/14
 */
public final class RelationTranslatorFactory {

    private RelationTranslatorFactory() {

    }

    private static final LinkTranslator LINK_TRANSLATOR = new LinkTranslator();

    private static final LookupTranslator LOOKUP_TRANSLATOR = new LookupTranslator();

    private static final LinkRightFieldTranslator LINK_RIGHT_FIELD_TRANSLATOR = new LinkRightFieldTranslator();



    /**
     * <pre>
     *     SELECT `name`
     *                  FROM org_sls_dc_cf
     *                  WHERE `id` = `sls_dc_id`
     * </pre>
     *
     * @param fields
     * @param namespaceRelationMap
     * @return
     */
    public static SqlNode translateRightField(String[] fields, Map<String, RelationList> namespaceRelationMap) {
        String firstField = fields[0];
        List<RelationNode> relationList = namespaceRelationMap.get(firstField).getRelationList();

        ConditionTranslatorV2 lastTranslator = null;
        SqlNode lastNode = null;
        for (RelationNode relationNode : relationList) {
            ConditionTranslatorV2 translator;
            if (relationNode.isEnd()) {
                // 终结节点构造逻辑取决于上一级非终结节点的类型
                translator = lastTranslator;
            } else {
                translator = getFieldTranslator(relationNode);
            }
            assert translator != null;
            SqlNode translate = translator.translate(relationNode);
            if (lastNode != null) {
                lastNode.merge(translate);
            } else {
                lastNode = translate;
            }
            lastTranslator = translator;
        }

        return lastNode;
    }

    private static ConditionTranslatorV2 getFieldTranslator(RelationNode relationNode) {
        if (relationNode.isLink()) {
            return LINK_RIGHT_FIELD_TRANSLATOR;
        }
        throw new UnsupportedOperationException();

    }

    /**
     * 翻译namespace
     *
     * @param condition       存在namespace的条件
     * @param extractRelation 关联关系map
     * @return 翻译后的sql
     */
    public static SqlNode translate(SingleCondition condition, Map<String, RelationList> extractRelation) {
        // fields、fieldDbNameMapping元信息根据modelAlias重新获取
        String namespace = condition.getNamespace();
        List<RelationNode> relationList = extractRelation.get(namespace).getRelationList();
        ConditionTranslatorV2 lastTranslator = null;
        SqlNode lastNode = null;
        for (RelationNode relationNode : relationList) {
            ConditionTranslatorV2 translator;
            if (relationNode.isEnd()) {
                // 终结节点构造逻辑取决于上一级非终结节点的类型
                translator = lastTranslator;
            } else {
                translator = getTranslator(relationNode);
            }
            assert translator != null;
            SqlNode translate = translator.translate(relationNode);
            if (lastNode != null) {
                lastNode.merge(translate);
            } else {
                lastNode = translate;
            }
            lastTranslator = translator;
        }
//        condition.setNamespace(null);
        return lastNode;
    }

    private static ConditionTranslatorV2 getTranslator(RelationNode relationNode) {
        if (relationNode.isLink()) {
            return LINK_TRANSLATOR;
        }
        if (relationNode.isLookup()) {
            return LOOKUP_TRANSLATOR;
        }
        throw new UnsupportedOperationException();
    }

}
