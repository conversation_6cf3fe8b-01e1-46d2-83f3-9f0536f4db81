package io.terminus.trantor2.model.engine.dml.orm.pipeline.repository.model;

import io.terminus.trantor2.model.common.model.condition.ConditionGroup;
import io.terminus.trantor2.model.common.model.condition.Pageable;
import io.terminus.trantor2.model.common.model.request.Order;
import io.terminus.trantor2.model.common.model.request.Select;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * ModelQuery
 *
 * <AUTHOR> Created on 2023/8/18 19:22
 */
@Setter
@Getter
public class ModelQuery extends CondQuery {
    private List<Select> selectFields;
    private List<Order> orders;
    private boolean skipDesensitization;
    private Pageable pageable;
    private boolean dataI18n;
    private String defaultLocale;

    public ModelQuery(Long teamId, String modelAlias) {
        super(teamId, modelAlias);
    }

    public ModelQuery(Long teamId, String modelAlias, ConditionGroup conditionGroup) {
        super(teamId, modelAlias, conditionGroup);
    }

    public ModelQuery(Long teamId, String modelAlias, Object extractRelation, ConditionGroup conditionGroup) {
        super(teamId, modelAlias, extractRelation, conditionGroup);
    }
}
