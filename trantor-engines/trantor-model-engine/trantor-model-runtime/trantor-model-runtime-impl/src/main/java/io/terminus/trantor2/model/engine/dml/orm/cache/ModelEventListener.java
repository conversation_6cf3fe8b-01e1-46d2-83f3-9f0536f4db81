package io.terminus.trantor2.model.engine.dml.orm.cache;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.meta.event.MetaChangeEvent;
import io.terminus.trantor2.model.engine.dml.orm.springdata.JdbcRepositoryLoaderV2;
import io.terminus.trantor2.model.engine.dml.orm.springdata.JdbcRepositoryQueryCreatorV2;
import io.terminus.trantor2.model.management.meta.util.AliasUtil;
import io.terminus.trantor2.module.service.TeamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ModelEventListener {
    @Autowired
    private TeamService teamService;

    @Autowired
    private JdbcRepositoryLoaderV2 jdbcRepositoryLoaderV2;

    @Autowired
    private JdbcRepositoryQueryCreatorV2 jdbcRepositoryQueryCreatorV2;

    @Async
    @Order(3)
    @EventListener(condition = "#event.oldMetas != null " +
            "&& !T(java.util.Arrays).asList(#event.oldMetas.?[type == 'Model']).isEmpty()")
    public void handleModelChange(MetaChangeEvent event) {
        log.info("received model change event: {}", JsonUtil.toJson(event));
        event.getOldMetas().stream().filter(it -> it.getType().equals("Model")).forEach(modelMeta -> {
            Long teamId = teamService.getTeamIdByCode(event.getTeamCode());
            ModelClassCache.cleanCache(teamId, modelMeta.getKey());

            jdbcRepositoryLoaderV2.clean(
                    teamId,
                    modelMeta.getAppId(),
                    AliasUtil.moduleKey(modelMeta.getKey()),
                    modelMeta.getKey());

            jdbcRepositoryQueryCreatorV2.clean(
                    teamId,
                    modelMeta.getAppId(),
                    AliasUtil.moduleKey(modelMeta.getKey()),
                    modelMeta.getKey()
            );
        });
    }
}
