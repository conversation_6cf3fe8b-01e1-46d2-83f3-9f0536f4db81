package io.terminus.trantor2.model.engine.dml.orm.mybatis.session;

import io.terminus.trantor2.datasource.runtime.factory.BusinessDMLDataSourceFactory;
import io.terminus.trantor2.model.common.cache.CacheCleanable;
import io.terminus.trantor2.model.engine.dml.orm.mybatis.mapper.SharedMapper;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.mapping.Environment;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.mybatis.spring.SqlSessionUtils;
import org.mybatis.spring.transaction.SpringManagedTransactionFactory;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SqlSessionFactoryHolder
 *
 * <AUTHOR> Created on 2023/6/5 09:53
 */
@RequiredArgsConstructor
@Component
public class SqlSessionFactoryHolder implements CacheCleanable {
    private final Map<Long, SqlSessionFactory> CACHE = new ConcurrentHashMap<>();
    private final BusinessDMLDataSourceFactory businessDMLDataSourceFactory;

    public SqlSessionFactory getSqlSessionFactory(Long teamId) {
        return CACHE.computeIfAbsent(teamId, this::build);
    }

    public SqlSession openSession(Long teamId) {
        return SqlSessionUtils.getSqlSession(getSqlSessionFactory(teamId));
    }

    public void clean(Long teamId, Long appId, String moduleKey, String key) {
        CACHE.remove(teamId);
    }

    private SqlSessionFactory build(Long teamId) {
        DataSource dataSource = businessDMLDataSourceFactory.getDataSourceByTeamId(teamId);
        Environment environment = new Environment("trantor2", new SpringManagedTransactionFactory(), dataSource);
        Configuration config = new Configuration(environment);
        config.setMapUnderscoreToCamelCase(true);
        config.setObjectWrapperFactory(new MapWrapperFactory());
        config.addMapper(SharedMapper.class);
        return new SqlSessionFactoryBuilder().build(config);
    }
}
