package io.terminus.trantor2.model.engine.dml.orm.mybatis;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.model.common.model.condition.Pageable;
import io.terminus.trantor2.model.common.model.request.AggregateQueryRequest;
import io.terminus.trantor2.model.common.model.request.Select;
import io.terminus.trantor2.model.engine.dml.orm.mybatis.mapper.SharedMapper;
import io.terminus.trantor2.model.engine.dml.orm.mybatis.provider.DeleteStatementProvider;
import io.terminus.trantor2.model.engine.dml.orm.mybatis.provider.InsertStatementProvider;
import io.terminus.trantor2.model.engine.dml.orm.mybatis.provider.SelectStatementProvider;
import io.terminus.trantor2.model.engine.dml.orm.mybatis.provider.UpdateStatementProvider;
import io.terminus.trantor2.model.engine.dml.orm.mybatis.session.SqlSessionFactoryHolder;
import io.terminus.trantor2.model.engine.dml.orm.pipeline.repository.Repository;
import io.terminus.trantor2.model.engine.dml.orm.pipeline.repository.model.BatchGeneralModel;
import io.terminus.trantor2.model.engine.dml.orm.pipeline.repository.model.CondQuery;
import io.terminus.trantor2.model.engine.dml.orm.pipeline.repository.model.GeneralModel;
import io.terminus.trantor2.model.engine.dml.orm.pipeline.repository.model.ModelQuery;
import io.terminus.trantor2.model.engine.dml.orm.sql.SqlBuilder;
import io.terminus.trantor2.model.management.meta.cache.DataStructMetaCache;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.ibatis.session.SqlSession;

import java.util.List;
import java.util.Map;

/**
 * MybatisRepository
 *
 * 不可用，没有适配多数据源场景(根据 teamCode+moduleKey 获取数据源)
 *
 * <AUTHOR> Created on 2023/6/5 10:59
 */
@Deprecated
@RequiredArgsConstructor
public class MybatisRepository implements Repository {
    private final DataStructMetaCache metaCache;
    private final SqlSessionFactoryHolder sqlSessionFactoryHolder;

    @Override
    public Map<String, Object> findOne(ModelQuery queryModel) {
        queryModel.setPageable(new Pageable(1, 1, false));
        SelectStatementProvider statement = MybatisSqlBuilder.of(metaCache)
                .withStatementType(SqlBuilder.StatementType.SELECT)
                .withQueryModel(queryModel)
                .build();
        return getMapper(queryModel.getTeamId()).selectOne(statement);
    }

    @Override
    public List<Map<String, Object>> findRelationInfo(ModelQuery queryModel, List<Object> relationIds, String groupByField) {
        return findAll(queryModel);
    }

    @Override
    public List<Map<String, Object>> findAll(ModelQuery queryModel) {
        SelectStatementProvider statement = MybatisSqlBuilder.of(metaCache)
                .withStatementType(SqlBuilder.StatementType.SELECT)
                .withQueryModel(queryModel)
                .build();
        return getMapper(queryModel.getTeamId()).selectList(statement);
    }

    @Override
    public Long count(CondQuery queryModel) {
        SelectStatementProvider statement = MybatisSqlBuilder.of(metaCache)
                .withStatementType(SqlBuilder.StatementType.COUNT)
                .withQueryModel(queryModel)
                .build();
        return getMapper(queryModel.getTeamId()).count(statement);
    }

    @Override
    public Object aggregate(AggregateQueryRequest request) {
        throw new NotImplementedException("MyBatis aggregate function not yet implemented!");
    }

    @Override
    public void create(GeneralModel generalModel) {
        InsertStatementProvider statement = MybatisSqlBuilder.of(metaCache)
                .withStatementType(SqlBuilder.StatementType.INSERT)
                .withGeneralModel(generalModel)
                .build();
        getMapper(generalModel.getTeamId()).insert(statement);
    }

    @Override
    public void batchCreate(BatchGeneralModel generalModel) {
        throw new NotImplementedException("MyBatis not yet implemented!");
    }

    @Override
    public void update(GeneralModel generalModel, CondQuery queryModel) {
        UpdateStatementProvider statement = MybatisSqlBuilder.of(metaCache)
                .withStatementType(SqlBuilder.StatementType.UPDATE)
                .withGeneralModel(generalModel)
                .withQueryModel(queryModel)
                .build();
        getMapper(generalModel.getTeamId()).update(statement);
    }

    @Override
    public void updateById(GeneralModel model) {
        UpdateStatementProvider statement = MybatisSqlBuilder.of(metaCache)
                .withStatementType(SqlBuilder.StatementType.UPDATE)
                .withGeneralModel(model)
                .build();
        getMapper(model.getTeamId()).update(statement);
    }

    @Override
    public void delete(CondQuery queryModel) {
        DeleteStatementProvider statement = MybatisSqlBuilder.of(metaCache)
                .withStatementType(SqlBuilder.StatementType.DELETE)
                .withQueryModel(queryModel)
                .build();
        getMapper(queryModel.getTeamId()).delete(statement);
    }

    protected SharedMapper getMapper() {
        return getMapper(TrantorContext.getTeamId());
    }

    protected SharedMapper getMapper(Long teamId) {
        SqlSession sqlSession = sqlSessionFactoryHolder.openSession(teamId);
        return sqlSession.getMapper(SharedMapper.class);
    }
}
