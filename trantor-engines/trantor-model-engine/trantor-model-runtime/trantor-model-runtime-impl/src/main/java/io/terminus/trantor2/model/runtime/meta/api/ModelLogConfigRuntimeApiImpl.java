package io.terminus.trantor2.model.runtime.meta.api;

import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.model.runtime.api.ModelLogConfigRuntimeApi;
import io.terminus.trantor2.model.runtime.api.ModelLogConfigRuntimeService;
import io.terminus.trantor2.model.runtime.api.dto.ModelLogConfigDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 模型日志配置运行时查询 API 实现
 *
 * 供其它 Runtime 通过 Feign 调用。
 */
@RestController
@RequestMapping("/api/internal/trantor/portal/model-log-config")
@RequiredArgsConstructor
public class ModelLogConfigRuntimeApiImpl implements ModelLogConfigRuntimeApi {

    private final ModelLogConfigRuntimeService modelLogConfigRuntimeService;

    @Override
    public Response<ModelLogConfigDTO> findModelLogConfig(String teamCode, String tableName, String moduleKey) {
        return Response.ok(modelLogConfigRuntimeService.findModelLogConfig(teamCode, tableName, moduleKey));
    }
}
