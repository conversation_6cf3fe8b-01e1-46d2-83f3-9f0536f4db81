package io.terminus.trantor2.model.engine.dml.orm.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/9/29
 */
@ConfigurationProperties(prefix = "trantor2.model.data.transaction")
@Component
@Data
public class TransactionConfig {

    private Integer timeout;

}
