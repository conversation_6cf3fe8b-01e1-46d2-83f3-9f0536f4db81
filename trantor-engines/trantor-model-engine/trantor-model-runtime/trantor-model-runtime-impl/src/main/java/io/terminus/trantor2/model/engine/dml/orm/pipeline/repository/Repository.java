package io.terminus.trantor2.model.engine.dml.orm.pipeline.repository;

import io.terminus.trantor2.model.common.model.request.AggregateQueryRequest;
import io.terminus.trantor2.model.common.model.request.Select;
import io.terminus.trantor2.model.engine.dml.orm.pipeline.repository.model.BatchGeneralModel;
import io.terminus.trantor2.model.engine.dml.orm.pipeline.repository.model.CondQuery;
import io.terminus.trantor2.model.engine.dml.orm.pipeline.repository.model.GeneralModel;
import io.terminus.trantor2.model.engine.dml.orm.pipeline.repository.model.ModelQuery;

import java.util.List;
import java.util.Map;

/**
 * Repository
 *
 * <AUTHOR> Created on 2023/6/6 10:19
 */
public interface Repository {

    Map<String, Object> findOne(ModelQuery queryModel);
    List<Map<String, Object>> findAll(ModelQuery queryModel);

    /**
     * 查询关联模型数据
     *
     * @param queryModel
     * @return
     */
    List<Map<String, Object>> findRelationInfo(ModelQuery queryModel, List<Object> relationIds, String groupByField);

    Long count(CondQuery queryModel);

    /**
     * 聚合类型查询，如 sum/min/max/avg 等
     *
     * @param request
     * @return
     */
    Object aggregate(AggregateQueryRequest request);

    void create(GeneralModel generalModel);
    void batchCreate(BatchGeneralModel generalModel);
    void update(GeneralModel generalModel, CondQuery queryModel);
    void updateById(GeneralModel generalModel);
    void delete(CondQuery queryModel);
}
