package io.terminus.trantor2.model.engine.dml.orm.springdata;

import io.terminus.trantor2.condition.ConditionItem;
import io.terminus.trantor2.datasource.runtime.factory.BusinessDMLDataSourceFactory;
import io.terminus.trantor2.model.common.cache.CacheCleanable;
import io.terminus.trantor2.model.common.consts.ConditionType;
import io.terminus.trantor2.model.common.consts.MethodType;
import io.terminus.trantor2.model.common.model.BasicObject;
import io.terminus.trantor2.model.common.model.condition.Condition;
import io.terminus.trantor2.model.common.model.condition.ConditionGroup;
import io.terminus.trantor2.model.common.model.condition.SingleCondition;
import io.terminus.trantor2.model.common.model.request.UpdateObject;
import io.terminus.trantor2.model.engine.dml.orm.cache.ModelClassCache;
import io.terminus.trantor2.model.engine.dml.orm.exception.ModelDataException;
import io.terminus.trantor2.model.engine.dml.orm.loader.CachingResourceFinderClassLoader;
import io.terminus.trantor2.model.engine.dml.orm.transaction.TransactionAwareJdbcTemplateV2;
import io.terminus.trantor2.model.engine.dml.orm.transaction.TransactionContext;
import io.terminus.trantor2.model.engine.dml.orm.util.ConditionUtil;
import io.terminus.trantor2.model.management.meta.cache.DataStructMetaCache;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.util.AliasUtil;
import io.terminus.trantor2.properties.runtime.ModelRuntimeProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.core.Ordered;
import org.springframework.data.jdbc.repository.query.CustomJdbcQueryMethod;
import org.springframework.data.jdbc.repository.query.JdbcQueryMethod;
import org.springframework.data.jdbc.repository.query.PartTreeJdbcQuery;
import org.springframework.data.jdbc.repository.query.StringBasedJdbcQuery;
import org.springframework.data.jdbc.repository.support.JdbcRepositoryFactoryBean;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.data.repository.core.support.PropertiesBasedNamedQueries;
import org.springframework.data.repository.query.QueryMethodEvaluationContextProvider;
import org.springframework.data.repository.query.RepositoryQuery;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import java.lang.reflect.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static io.terminus.trantor2.common.exception.ErrorType.MODEL_FAIL_TO_CREATE_JDBC_QUERY;
import static io.terminus.trantor2.common.exception.ErrorType.MODEL_NOT_EXISTS;
import static io.terminus.trantor2.model.engine.dml.orm.util.ConditionUtil.isFieldTypeConditionValue;
import static io.terminus.trantor2.model.engine.dml.orm.util.RowMapperUtil.enhanceRowMapper;

/**
 * <AUTHOR>
 * @since 2022/8/24
 */
@Slf4j
public class JdbcRepositoryQueryCreatorV2 implements RepositoryQueryCreatorV2, CacheCleanable, Ordered {

    /**
     * Key: API关键信息
     */
    private static final Map<String, RepositoryQuery> REPOSITORY_QUERY_CACHE = new ConcurrentHashMap<>();

    private final BusinessDMLDataSourceFactory businessDMLDataSourceFactory;

    private final AtomicInteger methodSequenceGenerator = new AtomicInteger();

    private final DataStructMetaCache modelMetaDocCache;

    private final ModelRuntimeProperties modelRuntimeProperties;

    private final CachingResourceFinderClassLoader cachingResourceFinderClassLoader;

    private static final int DEFAULT = 1000;

    public JdbcRepositoryQueryCreatorV2(DataStructMetaCache modelMetaDocCache, BusinessDMLDataSourceFactory businessDMLDataSourceFactory, ModelRuntimeProperties modelRuntimeProperties, CachingResourceFinderClassLoader cachingResourceFinderClassLoader) {
        this.modelMetaDocCache = modelMetaDocCache;
        this.businessDMLDataSourceFactory = businessDMLDataSourceFactory;
        this.modelRuntimeProperties = modelRuntimeProperties;
        this.cachingResourceFinderClassLoader = cachingResourceFinderClassLoader;
    }

    @Override
    public int getOrder() {
        return 100;
    }

    @Override
    public RepositoryQuery createPartTreeQuery(BasicObject basicObject, RepositoryParam repositoryParam) {
        return REPOSITORY_QUERY_CACHE.computeIfAbsent(getKey(basicObject), k -> {
            try {
                String modelAlias = basicObject.getModelAlias();
                MethodType methodType = basicObject.getMethodType();
                JdbcRepositoryParam jdbcParam = (JdbcRepositoryParam) repositoryParam;
                JdbcRepositoryFactoryBean jdbcRepositoryFactoryBean = jdbcParam.getJdbcRepositoryFactoryBean();

                DataStructNode modelMeta = modelMetaDocCache.getModelMeta(basicObject.getTeamId(), null, modelAlias);
                if (modelMeta == null) {
                    throw new ModelDataException(MODEL_NOT_EXISTS,
                        String.format(MODEL_NOT_EXISTS.getMessage(), modelAlias),new Object[]{modelAlias});
                }

                Method method = createMethod(
                        true,
                        modelMeta,
                        methodType,
                        jdbcRepositoryFactoryBean,
                        basicObject);
                ProjectionFactory projectionFactory = jdbcParam.getProjectionFactory();

                // Repository　内的每个方法对应一个 JdbcQueryMethod，一个RepositoryQuery实例持有一个JpaQueryMethod实例，JpaQueryMethod又持有一个Method实例
                JdbcQueryMethod jdbcQueryMethod = new JdbcQueryMethod(method,
                        jdbcRepositoryFactoryBean.getRepositoryInformation(), projectionFactory,
                        PropertiesBasedNamedQueries.EMPTY, jdbcParam.getMappingContext());
                RowMapper<Object> configuredQueryMapper = methodType.getRowMapper(jdbcParam.getModelClass());
                enhanceRowMapper(modelMeta, configuredQueryMapper);
                NamedParameterJdbcTemplate namedParameterJdbcTemplate =
                        new NamedParameterJdbcTemplate(new TransactionAwareJdbcTemplateV2(businessDMLDataSourceFactory, basicObject.getTeamId(), AliasUtil.moduleKey(basicObject.getModelAlias())));
                return new PartTreeJdbcQuery(jdbcParam.getMappingContext(), jdbcQueryMethod, jdbcParam.getDialect(),
                        jdbcParam.getConverter(), namedParameterJdbcTemplate, configuredQueryMapper);
            } catch (Exception e) {
                log.error("fail to create jdbc part tree repository query", e);
                throw new ModelDataException(MODEL_FAIL_TO_CREATE_JDBC_QUERY,
                        new Object[]{e.getMessage()}, String.format(MODEL_FAIL_TO_CREATE_JDBC_QUERY.getMessage(), e.getMessage()));
            }
        });
    }

    private String getKey(BasicObject basicObject) {
        return getCacheKeyPrefix(basicObject.getTeamId(), TransactionContext.getModuleKey(), basicObject.getModelAlias()) + basicObject.calculateObjectKey();
    }

    private String getCacheKeyPrefix(Long teamId, String moduleKey, String modelAlias) {
        return teamId + ":" + moduleKey + ":" + modelAlias + ":";
    }

    @Override
    public RepositoryQuery createStringBasedQuery(BasicObject basicObject, RepositoryParam repositoryParam) {
        // 批量新增，因为参数不定，不进行缓存，后期考虑优化
        if (basicObject.isBatchInsert()) {
            return doCreateStringBasedQuery(basicObject, repositoryParam);
        }
        return REPOSITORY_QUERY_CACHE.computeIfAbsent(getKey(basicObject), k -> doCreateStringBasedQuery(basicObject, repositoryParam));
    }

    private RepositoryQuery doCreateStringBasedQuery(BasicObject basicObject, RepositoryParam repositoryParam) {
        try {
            String modelAlias = basicObject.getModelAlias();
            DataStructNode modelMeta = modelMetaDocCache.getModelMeta(basicObject.getTeamId(), null, modelAlias);
            if (modelMeta == null) {
                throw new ModelDataException(MODEL_NOT_EXISTS, String.format(MODEL_NOT_EXISTS.getMessage(), modelAlias), new Object[]{modelAlias});
            }
            Map<String, List<DataStructFieldNode>> modelFields = new HashMap<>();
            String tableName = modelMeta.getProps().getTableName();
            modelFields.put(tableName, modelMeta.getChildren());
            MethodType methodType = basicObject.getMethodType();
            String sql = SpringSqlConverter.convertToSql(tableName, modelFields, methodType, basicObject, modelMetaDocCache, modelRuntimeProperties.getSubQueryWhitelist());
            log.info("generate logical sql:[{}]", sql);

            JdbcRepositoryParam jdbcParam = (JdbcRepositoryParam) repositoryParam;
            JdbcRepositoryFactoryBean factoryBean = jdbcParam.getJdbcRepositoryFactoryBean();
            Method method = createMethod(false, modelMeta, methodType, factoryBean, basicObject);
            markMethodParameterNameAccessible(method);
            ProjectionFactory projectionFactory = jdbcParam.getProjectionFactory();

            CustomJdbcQueryMethod jdbcQueryMethod = new CustomJdbcQueryMethod(
                    method,
                    factoryBean.getRepositoryInformation(), projectionFactory,
                    PropertiesBasedNamedQueries.EMPTY,
                    jdbcParam.getMappingContext(),
                    sql,
                    basicObject.isModify());

            RowMapper<Object> configuredQueryMapper = methodType.getRowMapper(jdbcParam.getModelClass());

            NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(new TransactionAwareJdbcTemplateV2(businessDMLDataSourceFactory, basicObject.getTeamId(), AliasUtil.moduleKey(basicObject.getModelAlias())));

            return new StringBasedJdbcQuery(jdbcQueryMethod, jdbcTemplate, configuredQueryMapper, jdbcParam.getConverter(), QueryMethodEvaluationContextProvider.DEFAULT);
        } catch (Exception e) {
            log.error("fail to create jdbc part tree repository query", e);
            throw new ModelDataException(MODEL_FAIL_TO_CREATE_JDBC_QUERY, new Object[]{e.getMessage()}, String.format(MODEL_FAIL_TO_CREATE_JDBC_QUERY.getMessage(), e.getMessage()));
        }
    }

    public void clean(Long teamId, Long appId, String moduleKey, String modelAlias) {
        log.info("cleaning model repository query cache, key: {}", teamId + ":" + moduleKey + ":" + modelAlias);
        REPOSITORY_QUERY_CACHE.entrySet()
                .removeIf(entry -> entry.getKey().startsWith(getCacheKeyPrefix(teamId, moduleKey, modelAlias)));
    }

    /**
     * 当 team 下的数据源信息修改时，清空此 team 下的所有模型相关的缓存
     *
     * @param teamId
     */
    public void cleanByTeamId(Long teamId) {
        REPOSITORY_QUERY_CACHE.entrySet()
            .removeIf(entry -> entry.getKey().startsWith(teamId + ":"));
    }

    public void cleanByTeamIdAndModuleId(Long teamId, String moduleKey) {
        REPOSITORY_QUERY_CACHE.entrySet()
            .removeIf(entry -> entry.getKey().startsWith(teamId + ":" + moduleKey + ":"));
    }

    public void cleanCache() {
        REPOSITORY_QUERY_CACHE.clear();
    }

    /**
     * 确保QueryMethod解析Method对象的参数名时，可以获取到自动生成的arg0、arg1这种参数
     *
     * @param method
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     */
    private void markMethodParameterNameAccessible(Method method)
            throws NoSuchFieldException, IllegalAccessException {
        method.getParameters();
        Field hasRealParameterData = method.getClass().getSuperclass().getDeclaredField("hasRealParameterData");
        hasRealParameterData.setAccessible(true);
        hasRealParameterData.setBoolean(method, true);
    }

    /**
     * @param isPartTreeQuery
     * @param modelMeta
     * @param methodType
     * @param jdbcRepositoryFactoryBean
     * @param basicObject
     * @return
     * @return Method
     * @throws NoSuchMethodException
     * @throws InvocationTargetException
     * @throws InstantiationException
     * @throws IllegalAccessException
     */
    public Method createMethod(boolean isPartTreeQuery,
                               DataStructNode modelMeta,
                               MethodType methodType,
                               JdbcRepositoryFactoryBean jdbcRepositoryFactoryBean,
                               BasicObject basicObject)
            throws NoSuchMethodException, InvocationTargetException, InstantiationException, IllegalAccessException {
        // 根据条件生成方法名、方法入参Class集合、方法签名
        String methodName;
        int conditionFieldSize = 0;
        List<Integer> collectionTypeIndex = new ArrayList<>();

        // 计算简单查询参数数量
        if (basicObject.getConditionItems() != null && MapUtils.isNotEmpty(basicObject.getConditionItems().getConditions())) {
            AtomicInteger simpleQueryFieldSize = new AtomicInteger(0);
            for (Map.Entry<String, ConditionItem> conditionItemEntry : basicObject.getConditionItems().getConditions().entrySet()) {
                ConditionItem value = conditionItemEntry.getValue();
                Map<String, DataStructFieldNode> fieldMapping = modelMeta.toFieldMap();
                if (!conditionItemEntry.getKey().contains(".") && !fieldMapping.containsKey(conditionItemEntry.getKey())) {
                    // 忽略简单条件组中模型元数据中不存在的字段
                    continue;
                }

                switch (value.getOperator()) {
                    case EQ:
                    case NEQ:
                    case GT:
                    case GTE:
                    case LT:
                    case LTE:
                    case START_WITH:
                    case END_WITH:
                    case CONTAINS:
                    case NOT_CONTAINS:
                        simpleQueryFieldSize.incrementAndGet();
                        break;
                    case IN:
                    case NOT_IN:
                        if (fieldMapping.containsKey(conditionItemEntry.getKey()) && ConditionUtil.isMultiEnum(fieldMapping.get(conditionItemEntry.getKey()))) {
                            List<Object> values = (List<Object>) value.getValue();
                            simpleQueryFieldSize.addAndGet(values.size());
                        } else {
                            collectionTypeIndex.add(simpleQueryFieldSize.get());
                            simpleQueryFieldSize.incrementAndGet();
                        }
                        break;
                    case BETWEEN_AND:
                        simpleQueryFieldSize.addAndGet(2);
                        break;
                }
            }
            conditionFieldSize = simpleQueryFieldSize.get();
        }

        ConditionGroup conditionGroup = basicObject.getConditionGroup();
        if (basicObject.isUpdate()) {
            List<String> updateFields = new ArrayList<>();
            List<String> fieldNames = modelMeta.getChildren().stream().map(DataStructFieldNode::getAlias).collect(Collectors.toList());
            ((UpdateObject) basicObject).getData().forEach((k, v) -> {
                // 更新时计算方法签名时也要过滤掉 id, 与拼接 sql 模板、sql 参数操作一致，解决 Invalid number of parameters given 问题
                if (!Objects.equals(k, "id") && fieldNames.contains(k)) {
                    updateFields.add(k);
                }
            });

            int updateFieldSize = updateFields.size();
            if (conditionGroup == null) {
                conditionFieldSize += updateFieldSize;
            } else {
                int simpleConditionFieldSize = conditionFieldSize;
                conditionFieldSize += conditionGroup.calculateConditionFieldSize();
                conditionFieldSize += updateFieldSize;

                // 获取参数为集合的参数下标，用于计算方法签名
                calculateInParamIndex(collectionTypeIndex, conditionGroup, new AtomicInteger(simpleConditionFieldSize + updateFieldSize));
            }
        } else if (conditionGroup != null) {
            int simpleConditionFieldSize = conditionFieldSize;
            conditionFieldSize += conditionGroup.calculateConditionFieldSize();
            calculateInParamIndex(collectionTypeIndex, conditionGroup, new AtomicInteger(simpleConditionFieldSize));
        }

        if (basicObject.getPage() != null) {
            if (basicObject.getPage().getAnchorId() == null) {
                conditionFieldSize += 2;
            } else {
                conditionFieldSize += 1;
            }
        }

        if (basicObject.isInsert()) {
            if (basicObject.getStatementParameters() != null) {
                conditionFieldSize += basicObject.getStatementParameters().length;
            }
        }

        if (isPartTreeQuery) {
            methodName = SpringSqlConverter.convertToMethodName(methodType, conditionGroup,
                    basicObject.getOrderBy());
        } else {
            methodName = "method" + methodSequenceGenerator.getAndIncrement();
        }
        Class<?>[] methodParam;
        String versionedModelAlias = ModelClassCache.getModelClass(basicObject.getTeamId(), modelMeta.getAlias()).getSimpleName();
        String methodSignature = SpringSqlConverter.convertToMethodSignature(versionedModelAlias, methodType,
                conditionFieldSize, collectionTypeIndex);
        methodParam = new Class[conditionFieldSize];
        // 如果条件是in/not_in，参数类型必须是Collection，否则会抛出java.lang.IllegalStateException:
        // Operator IN on relationSo requires a Collection argument, found class java.lang.Object
        Arrays.fill(methodParam, Object.class);
        for (Integer typeIndex : collectionTypeIndex) {
            methodParam[typeIndex] = Collection.class;
        }
        Constructor<?> c = Method.class.getDeclaredConstructor(Class.class, String.class, Class[].class, Class.class,
                Class[].class, int.class, int.class, String.class, byte[].class, byte[].class, byte[].class);
        c.setAccessible(true);
        Method method = (Method) c.newInstance(jdbcRepositoryFactoryBean.getObject().getClass(), methodName,
                methodParam, methodType.getReturnClassType(), null, DEFAULT, 0, methodSignature, null, null, null);

        // 手动设置 parameters 字段，解决 markMethodParameterNameAccessible 内参数不匹配的问题
        try {
            // 设置 parameters 字段为 null，强制使用合成参数
            Field parametersField = Executable.class.getDeclaredField("parameters");
            parametersField.setAccessible(true);
            parametersField.set(method, null);

            // 创建合成的 Parameter 数组，避免 MalformedParametersException
            Parameter[] syntheticParams = new Parameter[methodParam.length];
            for (int i = 0; i < methodParam.length; i++) {
                // 使用 Parameter 构造器创建合成参数
                Constructor<Parameter> paramConstructor = Parameter.class.getDeclaredConstructor(
                        String.class, int.class, Executable.class, int.class);
                paramConstructor.setAccessible(true);
                syntheticParams[i] = paramConstructor.newInstance("arg" + i, 0, method, i);
            }

            // 直接设置 parameters 字段，避免调用 getParameters()
            parametersField.set(method, syntheticParams);

        } catch (Exception e) {
            // 如果反射失败，记录日志但不抛出异常
            log.warn("Failed to set parameters field for method: " + methodName, e);
        }
        return method;
    }

    private void calculateInParamIndex(List<Integer> collectionTypeIndex,
                                       ConditionGroup conditionGroup,
                                       AtomicInteger atomicInteger) {
        for (Condition condition : conditionGroup.getConditions()) {
            if (condition instanceof ConditionGroup) {
                calculateInParamIndex(collectionTypeIndex, (ConditionGroup) condition, atomicInteger);
            } else {
                SingleCondition singleCondition = (SingleCondition) condition;
                if (isFieldTypeConditionValue(singleCondition.getValue())) {
                    continue;
                }
                ConditionType type = singleCondition.getType();
                if (type == ConditionType.IN || type == ConditionType.NOT_IN) {
                    collectionTypeIndex.add(atomicInteger.get());
                }
                atomicInteger.addAndGet(type.getValueType().getConditionSize());
            }
        }
    }
}
