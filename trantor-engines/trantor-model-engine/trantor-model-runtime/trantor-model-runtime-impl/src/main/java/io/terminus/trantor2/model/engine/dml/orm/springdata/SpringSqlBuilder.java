package io.terminus.trantor2.model.engine.dml.orm.springdata;

import io.terminus.trantor2.model.engine.dml.orm.sql.AbstractSQL;
import io.terminus.trantor2.model.engine.dml.orm.sql.ParameterRender;
import io.terminus.trantor2.model.engine.dml.orm.sql.SqlBuilder;
import io.terminus.trantor2.model.management.meta.cache.DataStructMetaCache;
import org.apache.commons.lang3.tuple.Pair;

import java.util.LinkedHashMap;

/**
 * SpringSqlBuilder
 *
 * <AUTHOR> Created on 2023/6/16 18:41
 */
public class SpringSqlBuilder extends SqlBuilder<Pair<String, LinkedHashMap<String, Object>>> {

    public SpringSqlBuilder(DataStructMetaCache metaCache) {
        super(new SpringSqlParameterRender(), metaCache);
    }

    @Override
    protected Pair<String, LinkedHashMap<String, Object>> render(AbstractSQL SQL) {
        return Pair.of(SQL.getSql(), SQL.getParameters());
    }

    /**
     * SpringSqlParameterRender
     *
     * <AUTHOR> Created on 2023/6/10 23:09
     */
    private static class SpringSqlParameterRender extends ParameterRender {
        @Override
        public String getFormattedParameterPlaceholder(String prefix, String parameterName) {
            return ":" + parameterName;
        }
    }
}
