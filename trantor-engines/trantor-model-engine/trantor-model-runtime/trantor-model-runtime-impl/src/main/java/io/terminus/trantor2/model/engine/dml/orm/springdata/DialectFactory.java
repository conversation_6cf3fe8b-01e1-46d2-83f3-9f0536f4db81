package io.terminus.trantor2.model.engine.dml.orm.springdata;

import io.terminus.trantor2.datasource.model.SupportDialectTypeEnum;
import org.springframework.data.relational.core.dialect.Dialect;
import org.springframework.data.relational.core.dialect.H2Dialect;
import org.springframework.data.relational.core.dialect.MySqlDialect;
import org.springframework.data.relational.core.dialect.OracleDialect;

/**
 * <AUTHOR>
 * @since 2022/8/23
 */
public class DialectFactory {
    /**
     * 数据库类型获取
     *
     * @param dialectType
     * @return 数据库类型
     */
    public static Dialect getDialect(SupportDialectTypeEnum dialectType) {
        switch (dialectType) {
            case MYSQL:
            case DRDS:
                return MySqlDialect.INSTANCE;
            case ORACLE:
                return OracleDialect.INSTANCE;
            case H2:
                return H2Dialect.INSTANCE;
            default:
                throw new UnsupportedOperationException("dialect not supported");
        }
    }

}
