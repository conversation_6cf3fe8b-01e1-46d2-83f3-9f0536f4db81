package io.terminus.trantor2.model.runtime.meta.transaction.aop;

import cn.hutool.core.util.RandomUtil;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.datasource.util.DateUtils;
import io.terminus.trantor2.model.engine.dml.orm.transaction.TransactionContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.interceptor.TransactionAttribute;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Method;
import java.util.Date;
import java.util.List;

@Aspect
@Component
@Slf4j
public class TrantorTransactionalAspect {
    public static final String LAZY_START_TRANSACTION_LABEL = "TrantorModel";

    @Autowired
    private TrantorTransactionalAspectSupport aspectSupport;

    /**
     * 事务切面，模仿spring事务interceptor
     *
     * @param proceedingJoinPoint
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(io.terminus.trantor2.model.runtime.meta.transaction.TrantorTransactional)")
    public Object around(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        // 事务id生成以及保存到事务上下文中
        String nowTransactionId = generateTransactionId(((MethodSignature) proceedingJoinPoint.getSignature()).getMethod(),
            proceedingJoinPoint.getTarget().getClass());
        List<String> alreadyExistsTransactionId = TransactionContext.getTransactionId();
        if (CollectionUtils.isEmpty(alreadyExistsTransactionId)) {
            log.info("aspect aop begin execute new tx, txId:{}", nowTransactionId);
        } else {
            log.info("aspect aop begin execute already exist tx, nowTxId:{},topTxId:{}",
                nowTransactionId, alreadyExistsTransactionId);
        }
        TransactionContext.addTransactionId(nowTransactionId);

        // 解析事务注解
        TrantorTransactionalAspectSupport.TrantorTransactionInfo txInfo = null;
        TransactionAttribute attribute = aspectSupport.getTrantorTransactionAttribute(((MethodSignature) proceedingJoinPoint.getSignature()).getMethod(),
            proceedingJoinPoint.getTarget().getClass(), nowTransactionId);


        // 当通过模型侧开启的事务时，将TransactionAttribute上下文清理掉
        if (attribute.getLabels().contains(LAZY_START_TRANSACTION_LABEL)) {
            TransactionContext.removeTransactionAttribute();
        }

        if (TransactionContext.getTeamId() == null && TrantorContext.getTeamId() != null) {
            TransactionContext.setTeamId(TrantorContext.getTeamId());
            TransactionContext.setTeamCode(TrantorContext.getTeamCode());
        }

        if (StringUtils.isEmpty(TransactionContext.getModuleKey()) && StringUtils.isNotEmpty(TrantorContext.getModuleKey())) {
            TransactionContext.setModuleKey(TrantorContext.getModuleKey());
        }

        // 如果满足立即开启事务的条件，直接调用getTrantorTransactionInfo
        if (TransactionContext.needStartTransactionImmediate()) {
            txInfo = aspectSupport.getTrantorTransactionInfo(attribute);
        } else {
            log.info("aspect aop execute but TransactionContext.teamCode is empty, will lazy begin tx, nowTxId:{},txStackId:{}",
                nowTransactionId, TransactionContext.getTransactionId());
            // 上下文中没有模块信息，延时开启事务，模型api执行前再开启，延时开启后的事务信息存储在TransactionContext.transactionInfoThreadLocal中
            TransactionContext.setTransactionAttribute(attribute);
        }

        // method execute
        Object retVal;
        try {
            // This is an around advice: Invoke the next interceptor in the chain.
            // This will normally result in a target object being invoked.
            retVal = proceedingJoinPoint.proceed(proceedingJoinPoint.getArgs());
        } catch (Throwable ex) {
            TransactionContext.deleteTransactionId(nowTransactionId);
            if (TransactionContext.lazyTransactionNotStarted()) {
                // 延时事务未开启
                log.warn("aspect aop catch exception but lazy transaction not started...nowTxId:{},txIdStack:{}",
                    nowTransactionId, TransactionContext.getTransactionId());
            } else {
                log.error("aspect aop catch exception will do completeTransactionAfterThrowing, nowTxId:{}, transactionIdStack:{}",
                    nowTransactionId, TransactionContext.getTransactionId());

                // target invocation exception
                aspectSupport.completeTransactionAfterThrowing(getTxInfo(txInfo, attribute), ex);
            }
            throw ex;
        } finally {
            // 如果当前上下文中事务id为空了，则强制清空事务上下文
            TransactionContext.removeContextIfNecessary(nowTransactionId, true);
        }

        try {
            if (TransactionContext.lazyTransactionNotStarted()) {
                // 延时事务未开启
                log.warn("aspect aop lazy transaction not started will return...nowTxId:{},txIdStack:{}",
                    nowTransactionId, TransactionContext.getTransactionId());
            } else {
                if (log.isDebugEnabled()) {
                    log.debug("aspect aop proceed success will do commitTransactionAfterReturning, nowTxId:{},txIdStack:{}",
                        nowTransactionId, TransactionContext.getTransactionId());
                }
                // target invocation exception
                aspectSupport.commitTransactionAfterReturning(getTxInfo(txInfo, attribute));
            }
        } finally {
            TransactionContext.deleteTransactionId(nowTransactionId);
            // 如果当前上下文中事务id为空了，则强制清空事务上下文
            TransactionContext.removeContextIfNecessary(nowTransactionId, true);
        }


        return retVal;
    }

    /**
     * 获取当前事务aop使用的事务对象，如果事务对象为空，代表事务对象是延时开启的，则需要根据事务注解属性，去上下文中获取
     *
     * @param txInfo
     * @param attribute
     * @return
     */
    private TrantorTransactionalAspectSupport.TrantorTransactionInfo getTxInfo(TrantorTransactionalAspectSupport.TrantorTransactionInfo txInfo,
                                                                               TransactionAttribute attribute) {
        if (null != txInfo) {
            return txInfo;
        }
        TrantorTransactionalAspectSupport.TrantorTransactionInfo trantorTransactionInfo = TransactionContext.getTransactionInfo(attribute);
        if (null == trantorTransactionInfo) {
            String errMsg = "cannot find TrantorTransactionInfo by TransactionAttribute in TrantorTransactionalAspect, TransactionAttribute:" + attribute;
            throw new TrantorRuntimeException(errMsg);
        }
        return trantorTransactionInfo;
    }

    /**
     * 全局事务id
     *
     * @return
     */
    private static String generateTransactionId(Method method, @Nullable Class<?> targetClass) {
        return targetClass.getSimpleName() + "#" + method.getName() + "#" + DateUtils.formatDate(new Date(), "yyyyMMdd_HHmmss_SSS_")
            + "#" + RandomUtil.randomString(4);
    }

}
