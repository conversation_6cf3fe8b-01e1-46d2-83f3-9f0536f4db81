package io.terminus.trantor2.model.engine.dml.orm.springdata;

import io.terminus.trantor2.model.common.model.BasicObject;
import io.terminus.trantor2.datasource.model.SupportDialectTypeEnum;
import org.springframework.data.jdbc.core.JdbcAggregateTemplate;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.RepositoryQuery;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

/**
 * <AUTHOR>
 * @since 2022/8/24
 */
public interface RepositoryLoader {

    /**
     * model CrudRepository实例获取
     * 优先从缓存中获取
     *
     * @param basicObject
     * @param dialectType
     * @return CrudRepository实例
     */
    CrudRepository createRepository(BasicObject basicObject, SupportDialectTypeEnum dialectType);

    /**
     * 单层级条件，直接通过PartTree方式生成，多级再使用StringBased
     *
     * @param basicObject
     * @return RepositoryQuery
     */
    RepositoryQuery createRepositoryQuery(BasicObject basicObject);

    /**
     * 返回NamedParameterJdbcTemplate
     */
    NamedParameterJdbcTemplate getNamedParameterJdbcTemplate(Long teamId ,String moduleKey);

    /**
     * 用于创建
     */
    JdbcAggregateTemplate getJdbcAggregateTemplate(Long teamId, String moduleKey);

}
