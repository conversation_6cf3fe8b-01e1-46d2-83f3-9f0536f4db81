package io.terminus.trantor2.model.engine.dml.orm.springdata;

import lombok.Data;
import org.springframework.data.jdbc.core.convert.JdbcConverter;
import org.springframework.data.jdbc.repository.support.JdbcRepositoryFactoryBean;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.data.relational.core.dialect.Dialect;
import org.springframework.data.relational.core.mapping.RelationalMappingContext;

/**
 * <AUTHOR>
 * @since 2022/8/24
 */
@Data
public class JdbcRepositoryParam implements RepositoryParam {

    private JdbcRepositoryFactoryBean jdbcRepositoryFactoryBean;

    private ProjectionFactory projectionFactory;

    private RelationalMappingContext mappingContext;

    private JdbcConverter converter;

    private Dialect dialect;

    private Class modelClass;
}
