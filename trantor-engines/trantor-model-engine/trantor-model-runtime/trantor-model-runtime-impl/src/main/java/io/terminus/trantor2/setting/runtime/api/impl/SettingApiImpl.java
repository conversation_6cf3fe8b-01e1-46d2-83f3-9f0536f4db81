package io.terminus.trantor2.setting.runtime.api.impl;

import io.terminus.common.api.exception.BusinessException;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.model.common.consts.ConditionLogicalOperator;
import io.terminus.trantor2.model.common.consts.ConditionType;
import io.terminus.trantor2.model.common.model.condition.*;
import io.terminus.trantor2.model.common.model.request.PagingRequest;
import io.terminus.trantor2.model.common.model.request.QueryRequest;
import io.terminus.trantor2.model.runtime.api.dml.DataStructDataApi;
import io.terminus.trantor2.setting.BusinessSettingConst;
import io.terminus.trantor2.setting.runtime.api.SettingApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Component
public class SettingApiImpl implements SettingApi {
    @Autowired
    private DataStructDataApi dataApi;

    @Override
    public Response<List<Map<String, Object>>> listSettingsByGroupKey(String groupKey) {
        PagingRequest pagingRequest = new PagingRequest();
        pagingRequest.setTeamId(TrantorContext.getTeamId());
        pagingRequest.setModelAlias(groupKey);
        pagingRequest.setPage(new Pageable(1, 5000, false));

        ConditionGroup conditionGroup = new ConditionGroup(Arrays.asList(
                new SingleCondition(BusinessSettingConst.FIELD_GROUP_KEY, ConditionType.EQ, new ConditionValue(groupKey, null, null, ConditionValueType.VALUE), ConditionLogicalOperator.AND),
                new SingleCondition(BusinessSettingConst.FIELD_DELETED, ConditionType.EQ, new ConditionValue(0, null, null, ConditionValueType.VALUE), null)
        ));
        pagingRequest.setConditionGroup(conditionGroup);

        Response<Paging<Map<String, Object>>> response = dataApi.paging(pagingRequest);
        if (!response.isSuccess()) {
            throw new BusinessException(response.getErrMsg());
        }

        return Response.ok(response.getData().getData());
    }

    @Override
    public Response<Map<String, Object>> getSettingByCode(String groupKey, String code) {
        QueryRequest queryRequest = new QueryRequest();
        queryRequest.setTeamId(TrantorContext.getTeamId());
        queryRequest.setModelAlias(groupKey);

        ConditionGroup conditionGroup = new ConditionGroup(Arrays.asList(
                new SingleCondition(BusinessSettingConst.FIELD_GROUP_KEY, ConditionType.EQ, new ConditionValue(groupKey, null, null, ConditionValueType.VALUE), ConditionLogicalOperator.AND),
                new SingleCondition(BusinessSettingConst.FIELD_CODE, ConditionType.EQ, new ConditionValue(code, null, null, ConditionValueType.VALUE), ConditionLogicalOperator.AND),
                new SingleCondition(BusinessSettingConst.FIELD_DELETED, ConditionType.EQ, new ConditionValue(0, null, null, ConditionValueType.VALUE), null)
        ));

        queryRequest.setConditionGroup(conditionGroup);

        return dataApi.findOne(queryRequest);
    }

    @Override
    public Response<List<Map<String, Object>>> listSettingsByParentCode(String groupKey, String parentCode) {
        PagingRequest pagingRequest = new PagingRequest();
        pagingRequest.setTeamId(TrantorContext.getTeamId());
        pagingRequest.setModelAlias(groupKey);
        pagingRequest.setPage(new Pageable(1, 5000, false));

        ConditionGroup conditionGroup = new ConditionGroup(Arrays.asList(
                new SingleCondition(BusinessSettingConst.FIELD_GROUP_KEY, ConditionType.EQ, new ConditionValue(groupKey, null, null, ConditionValueType.VALUE), ConditionLogicalOperator.AND),
                new SingleCondition(BusinessSettingConst.FIELD_PARENT_CODE, ConditionType.EQ, new ConditionValue(parentCode, null, null, ConditionValueType.VALUE), ConditionLogicalOperator.AND),
                new SingleCondition(BusinessSettingConst.FIELD_DELETED, ConditionType.EQ, new ConditionValue(0, null, null, ConditionValueType.VALUE), null)
        ));
        pagingRequest.setConditionGroup(conditionGroup);

        Response<Paging<Map<String, Object>>> response = dataApi.paging(pagingRequest);
        if (!response.isSuccess()) {
            throw new BusinessException(response.getErrMsg());
        }

        return Response.ok(response.getData().getData());
    }
}
