package io.terminus.trantor2.model.engine.dml.orm.converter.relation.translator;

import io.terminus.trantor2.model.management.meta.consts.SystemFieldGeneratorV2;
import io.terminus.trantor2.model.engine.dml.orm.converter.relation.RelationNode;

/**
 * <AUTHOR>
 * @since 2022/11/14
 */
public class LookupTranslator extends AbstractLinkTranslator {

    @Override
    protected SqlNode doTranslate(RelationNode relationNode) {
        String tableName = relationNode.getTableName();
        if (relationNode.isStart()) {
            return processStartNode(SystemFieldGeneratorV2.ID.getFieldAlias());
        } else if (relationNode.isEnd()) {
            return processEndNode(tableName, relationNode.getParent().getLookupLinkFieldDbName());
        } else {
            String selectFieldName = buildLinkOrLookupProgressNode(relationNode);
            return processProgressNode(tableName, selectFieldName, SystemFieldGeneratorV2.ID.getFieldAlias());
        }
    }

}
