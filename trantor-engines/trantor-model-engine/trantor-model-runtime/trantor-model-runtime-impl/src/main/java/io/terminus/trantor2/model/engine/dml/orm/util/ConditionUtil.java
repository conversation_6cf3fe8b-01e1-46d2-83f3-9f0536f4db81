package io.terminus.trantor2.model.engine.dml.orm.util;

import cn.hutool.core.collection.CollUtil;
import io.terminus.trantor2.condition.ConditionItems;
import io.terminus.trantor2.model.common.consts.ConditionLogicalOperator;
import io.terminus.trantor2.model.common.consts.ConditionType;
import io.terminus.trantor2.model.common.consts.DateTimeVariableValue;
import io.terminus.trantor2.model.common.model.condition.*;
import io.terminus.trantor2.model.engine.dml.orm.converter.relation.RelationList;
import io.terminus.trantor2.model.engine.dml.orm.exception.ModelDataException;
import io.terminus.trantor2.model.management.meta.cache.DataStructMetaCache;
import io.terminus.trantor2.model.management.meta.consts.FieldType;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import static io.terminus.trantor2.common.exception.ErrorType.MODEL_FIELD_NOT_EXISTS;
import static io.terminus.trantor2.common.exception.ErrorType.MODEL_NOT_EXISTS;


/**
 * <AUTHOR>
 * @since 2022/9/9
 */
public final class ConditionUtil {

    private ConditionUtil() {

    }

    public static void doConvert(Condition condition, Long teamId, String modelAlias,
                                 DataStructMetaCache docCache, Map<String, RelationList> extractRelation) {
        FieldTypeMapping fieldTypeMapping = new FieldTypeMapping(teamId, modelAlias, docCache, extractRelation);
        convertMultiSelect(condition, fieldTypeMapping);
        doConvert(condition, fieldTypeMapping);
    }

    private static void convertMultiSelect(Condition condition, FieldTypeMapping fieldTypeMapping) {
        if (condition == null) {
            return;
        }
        if (!fieldTypeMapping.hasMultiSelectField()) {
            return;
        }
        ((ConditionGroup) condition).getConditions()
            .forEach(condition1 -> convertMultiSelect0((ConditionGroup) condition, condition1, fieldTypeMapping));
    }

    public static void collectConditionGroupFields(ConditionGroup conditionGroup, Set<String> fields) {
        if (conditionGroup == null || CollectionUtils.isEmpty(conditionGroup.getConditions())) {
            return;
        }

        conditionGroup.getConditions().forEach(condition -> {
            if (condition instanceof SingleCondition) {
                SingleCondition singleCondition = (SingleCondition) condition;
                fields.add(singleCondition.getField());
            } else {
                collectConditionGroupFields((ConditionGroup) condition, fields);
            }
        });
    }

    public static void collectConditionItemFields(ConditionItems conditionItems, Set<String> fields) {
        if (conditionItems == null || MapUtils.isEmpty(conditionItems.getConditions())) {
            return;
        }
        fields.addAll(conditionItems.getConditions().keySet());
    }

    private static void convertMultiSelect0(ConditionGroup parentConditionGroup, Condition condition,
                                            FieldTypeMapping fieldTypeMapping) {
        if (condition == null) {
            return;
        }
        if (condition instanceof ConditionGroup) {
            ((ConditionGroup) condition).getConditions()
                .forEach(condition1 -> convertMultiSelect0((ConditionGroup) condition, condition1, fieldTypeMapping));
        } else {
            String field = ((SingleCondition) condition).getField();
            DataStructFieldNode fieldType = fieldTypeMapping.getFieldType(((SingleCondition) condition).getNamespace(), field);
            if (fieldType == null) {
                throw new ModelDataException(MODEL_FIELD_NOT_EXISTS,
                    String.format(MODEL_FIELD_NOT_EXISTS.getMessage(), field),new Object[]{field});
            }
            if (!isMultiEnum(fieldType)) {
                return;
            }
            ConditionType type = ((SingleCondition) condition).getType();
            ConditionValue conditionValue = ((SingleCondition) condition).getValue();
            switch (type) {
                case IN:
                case CONTAINS:
                    ConditionGroup jsonContains = getJsonCondition(condition, field, conditionValue,
                        ConditionType.JSON_CONTAINS);
                    for (Condition item : jsonContains.getConditions()) {
                        SingleCondition singleCondition = (SingleCondition) item;
                        singleCondition.setNamespace(((SingleCondition ) condition).getNamespace());
                    }
                    parentConditionGroup.replaceCondition(condition, jsonContains);
                    break;
                case NOT_IN:
                case NOT_CONTAINS:
                    ConditionGroup jsonNotContains = getJsonCondition(condition, field, conditionValue,
                        ConditionType.JSON_NOT_CONTAINS);
                    for (Condition item : jsonNotContains.getConditions()) {
                        SingleCondition singleCondition = (SingleCondition) item;
                        singleCondition.setNamespace(((SingleCondition ) condition).getNamespace());
                    }
                    parentConditionGroup.replaceCondition(condition, jsonNotContains);
                    break;
                case EQ:
                    ConditionGroup jsonEq = getJsonCondition(condition, field, conditionValue,
                        ConditionType.JSON_CONTAINS);
                    appendJsonLength(field, jsonEq);
                    parentConditionGroup.replaceCondition(condition, jsonEq);
                    break;
                case NEQ:
                    throw new UnsupportedOperationException("multi select field not support not-equal condition");
                default:
                    break;
            }
        }
    }

    private static void appendJsonLength(String field, ConditionGroup conditionGroup) {
        List<Condition> conditions = conditionGroup.getConditions();
        conditions.get(conditions.size() - 1).setNextOperator(ConditionLogicalOperator.AND);
        ConditionValue value = new ConditionValue(conditions.size(), null, null, null);
        SingleCondition jsonLength = new SingleCondition(field, ConditionType.JSON_LENGTH, value, null);
        conditions.add(jsonLength);
    }

    private static ConditionGroup getJsonCondition(Condition condition, String field,
                                                   ConditionValue conditionValue, ConditionType conditionType) {
        ConditionGroup jsonConditionGroup = new ConditionGroup();
        int size = 1;
        List<Condition> conditions = new ArrayList<>(size);
        if (conditionValue.getValues() != null) {
            size = conditionValue.getValues().size();
            for (int i = 0; i < size; i++) {
                Object value = conditionValue.getValues().get(i);
                ConditionLogicalOperator operator = ConditionLogicalOperator.OR;
                if (i == size - 1) {
                    operator = null;
                }
                addCondition(field, conditionType, conditions, value, operator);
            }
        } else {
            Object value = conditionValue.getValue();
            addCondition(field, conditionType, conditions, value, null);
        }
        jsonConditionGroup.setConditions(conditions);
        jsonConditionGroup.setNextOperator(condition.getNextOperator());
        return jsonConditionGroup;
    }

    private static void addCondition(String field, ConditionType conditionType, List<Condition> conditions,
                                     Object value, ConditionLogicalOperator operator) {
        ConditionValue jsonValue = new ConditionValue("\"" + value + "\"", null, null, null);
        SingleCondition singleCondition = new SingleCondition(field, conditionType, jsonValue, operator);
        conditions.add(singleCondition);
    }

    private static void doConvert(Condition condition, FieldTypeMapping fieldTypeMapping) {
        if (condition == null) {
            return;
        }
        if (condition instanceof ConditionGroup) {
            ((ConditionGroup) condition).getConditions().forEach(condition1 -> doConvert(condition1, fieldTypeMapping));
        } else {
            ConditionType type = ((SingleCondition) condition).getType();
            String field = ((SingleCondition) condition).getField();
            DataStructFieldNode fieldType = fieldTypeMapping.getFieldType(((SingleCondition) condition).getNamespace(), field);
            if (fieldType == null) {
                throw new ModelDataException(MODEL_FIELD_NOT_EXISTS,
                    String.format(MODEL_FIELD_NOT_EXISTS.getMessage(), field),new Object[]{field});
            }
            ConditionValue conditionValue = ((SingleCondition) condition).getValue();
            switch (type) {
                case CONTAINS:
                case NOT_CONTAINS:
                    conditionValue.setValue("%" + conditionValue.getValue() + "%");
                    break;
                case END_WITH:
                    conditionValue.setValue("%" + conditionValue.getValue());
                    break;
                case START_WITH:
                    conditionValue.setValue(conditionValue.getValue() + "%");
                    break;
                case RANGE:
                case BETWEEN_AND:
                    convertRangeValue(type, fieldType, conditionValue);
                    ((SingleCondition) condition).setType(ConditionType.BETWEEN_AND);
                    break;
                case OUT_OF_RANGE:
                case NOT_BETWEEN_AND:
                    convertRangeValue(type, fieldType, conditionValue);
                    ((SingleCondition) condition).setType(ConditionType.NOT_BETWEEN_AND);
                    break;
                case LT:
                case LTE:
                case GT:
                case GTE:
                case EQ:
                case NEQ:
                    convertSingleValue(type, fieldType, conditionValue);
                    break;
                case IN:
                case NOT_IN:
                    convertMultiValue(fieldType, conditionValue);
                    break;
                default:
                    break;
            }
        }
    }

    private static void convertMultiValue(DataStructFieldNode fieldType, ConditionValue conditionValue) {
        List<Object> values = conditionValue.getValues();
        if (fieldType.getProps().getFieldType() == FieldType.TIME) {
            List<Object> convert = values.stream()
                .map(it -> LocalTime.parse(String.valueOf(conditionValue.getValue())))
                .collect(Collectors.toList());
            conditionValue.setValues(convert);
        }
    }

    private static void convertSingleValue(ConditionType type, DataStructFieldNode fieldType, ConditionValue value) {
        if (fieldType.getProps().getFieldType() == FieldType.DATE) {
            convertVariable(value, type);
        } else if (fieldType.getProps().getFieldType() == FieldType.TIME) {
            value.setValue(LocalTime.parse(String.valueOf(value.getValue())));
        }
    }

    private static void convertRangeValue(ConditionType type, DataStructFieldNode fieldType, ConditionValue value) {
        if (fieldType.getProps().getFieldType() == FieldType.DATE) {
            convertVariable(value, type);
        } else if (fieldType.getProps().getFieldType() == FieldType.TIME) {
            value.setValue(LocalTime.parse(String.valueOf(value.getValue())));
            value.setRightValue(LocalTime.parse(String.valueOf(value.getRightValue())));
        }
    }

    private static void convertVariable(ConditionValue conditionValue, ConditionType type) {
        Object leftValue = conditionValue.getValue();
        Object rightValue = conditionValue.getRightValue();
        if (rightValue == null && leftValue instanceof String) {
            // 变量
            DateTimeVariableValue dateTimeValue = DateTimeVariableValue.valueOf((String) leftValue);
            Pair<Date, Date> startAndEndTime = dateTimeValue.getStartAndEndTime();
            ConditionType.ConditionValueType valueType = type.getValueType();
            switch (valueType) {
                case MATCH:
                    if (type == ConditionType.LT || type == ConditionType.LTE) {
                        conditionValue.setValue(startAndEndTime.getLeft());
                    } else {
                        conditionValue.setValue(startAndEndTime.getRight());
                    }
                    break;
                case RANGE:
                    conditionValue.setValue(startAndEndTime.getLeft());
                    conditionValue.setRightValue(startAndEndTime.getRight());
                    break;
                default:
                    break;
            }
        }
    }

    public static boolean isFieldTypeConditionValue(ConditionValue value) {
        return value != null && value.getType() == ConditionValueType.FIELD;
    }

    @Data
    private static class FieldTypeMapping {

        private Map<String, DataStructFieldNode> mainModelFieldTypeMapping;

        private Map<String, Map<String, DataStructFieldNode>> relationModelFieldTypeMapping =
            new HashMap<>();

        FieldTypeMapping(Long teamId, String modelAlias, DataStructMetaCache docCache,
                         Map<String, RelationList> extractRelation) {
            DataStructNode modelMeta = docCache.getModelMeta(teamId, null, modelAlias);
            if (modelMeta == null) {
                throw new ModelDataException(MODEL_NOT_EXISTS,
                    String.format(MODEL_NOT_EXISTS.getMessage(), modelAlias),new Object[]{modelAlias});
            }
            this.mainModelFieldTypeMapping = modelMeta.getChildren().stream()
                .collect(Collectors.toMap(DataStructFieldNode::getAlias, it -> it));
            if (CollUtil.isNotEmpty(extractRelation)) {
                for (Map.Entry<String, RelationList> entry : extractRelation.entrySet()) {
                    String namespace = entry.getKey();
                    RelationList value = entry.getValue();
                    String lastRelationModelAlias = value.getLastRelationModelAlias();
                    DataStructNode currentModel = docCache.getModelMeta(teamId, null, lastRelationModelAlias);
                    Map<String, DataStructFieldNode> relationModelFieldMapping = currentModel.getChildren()
                        .stream()
                        .collect(Collectors.toMap(DataStructFieldNode::getAlias,
                            it -> it));
                    relationModelFieldTypeMapping.put(namespace, relationModelFieldMapping);
                }
            }
        }

        public DataStructFieldNode getFieldType(String namespace, String field) {
            if (namespace == null) {
                return mainModelFieldTypeMapping.get(field);
            }
            return relationModelFieldTypeMapping.get(namespace).get(field);
        }

        public boolean hasMultiSelectField() {
            return mainModelFieldTypeMapping.values().stream()
                .anyMatch(ConditionUtil::isMultiEnum) ||
                    relationModelFieldTypeMapping.values().stream()
                            .flatMap(map -> map.values().stream())
                            .anyMatch(ConditionUtil::isMultiEnum);
        }
    }

    public static boolean isMultiEnum(DataStructFieldNode field) {
        if (field == null || field.getProps() == null) {
            return false;
        }

        if (field.getProps().getFieldType() == FieldType.ENUM) {
            return field.getProps().getDictPros() != null && BooleanUtils.isTrue(field.getProps().getDictPros().getMultiSelect());

        } else if (field.getProps().getFieldType() == FieldType.ATTACHMENT) {
            return field.getProps().getAttachmentProps() != null && BooleanUtils.isTrue(field.getProps().getAttachmentProps().isMulti());
        }

        return false;
    }

}
