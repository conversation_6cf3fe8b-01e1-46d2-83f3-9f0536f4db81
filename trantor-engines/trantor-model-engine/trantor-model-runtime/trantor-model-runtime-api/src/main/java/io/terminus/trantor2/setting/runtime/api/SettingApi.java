package io.terminus.trantor2.setting.runtime.api;

import io.terminus.trantor2.common.dto.Response;

import java.util.List;
import java.util.Map;

public interface SettingApi {
    /**
     * 根据 groupKey 获取全量的业务配置
     *
     * @param groupKey
     * @return
     */
    Response<List<Map<String, Object>>> listSettingsByGroupKey(String groupKey);

    /**
     * 根据 code 获取业务配置详情
     * @param code
     * @return
     */
    Response<Map<String, Object>> getSettingByCode(String groupKey, String code);

    /**
     * 根据 parentCode 获取子业务配置列表
     * @param parentCode
     * @return
     */
    Response<List<Map<String, Object>>> listSettingsByParentCode(String groupKey, String parentCode);
}
