package io.terminus.trantor2.model.runtime.api;

import io.swagger.v3.oas.annotations.Parameter;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.model.runtime.api.dto.ModelLogConfigDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 模型日志配置运行时查询 API（用于 FeignClient）
 *
 * 仿照 DataStructNodeQueryApi 的风格，声明 HTTP 方法签名，
 * 由 runtime-impl 中的 Controller 实现，runtime-sdk 通过 FeignClient 调用。
 *
 * <AUTHOR>
 */
public interface ModelLogConfigRuntimeApi {

    /**
     * 根据团队Code、表名和模块key查询模型日志配置
     *
     * @param teamCode  团队Code
     * @param tableName 表名
     * @param moduleKey 模块key（可选）
     * @return 模型日志配置 DTO（未配置或未找到返回 null）
     */
    @GetMapping("/find")
    Response<ModelLogConfigDTO> findModelLogConfig(
        @Parameter(required = true) @RequestParam("teamCode") String teamCode,
        @Parameter(required = true) @RequestParam("tableName") String tableName,
        @Parameter(required = false) @RequestParam(value = "moduleKey", required = false) String moduleKey
    );
}
