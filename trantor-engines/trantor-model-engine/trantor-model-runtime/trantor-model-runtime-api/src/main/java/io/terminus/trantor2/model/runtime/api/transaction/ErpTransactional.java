package io.terminus.trantor2.model.runtime.api.transaction;

import org.springframework.transaction.annotation.Transactional;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @since 2023/4/17
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
//@Transactional(transactionManager = "trantorPlatformTransactionManager") 2023-11月新增TrantorTransactional事务注解后，该注解废弃
public @interface ErpTransactional {
}
