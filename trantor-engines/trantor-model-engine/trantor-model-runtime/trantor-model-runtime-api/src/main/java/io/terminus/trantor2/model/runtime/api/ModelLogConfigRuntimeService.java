package io.terminus.trantor2.model.runtime.api;

import io.terminus.trantor2.model.runtime.api.dto.ModelLogConfigDTO;
import jakarta.annotation.Nullable;

/**
 * 模型日志配置运行时服务
 *
 * <AUTHOR>
 */
public interface ModelLogConfigRuntimeService {

    /**
     * 根据团队Code、表名和模块key查询模型日志配置
     *
     * @param teamCode  团队Code
     * @param tableName 表名
     * @param moduleKey 模块key（可选）
     * @return 模型日志配置 DTO，如果未找到则返回null
     */
    @Nullable
    ModelLogConfigDTO findModelLogConfig(String teamCode, String tableName, @Nullable String moduleKey);
}
