package io.terminus.trantor2.model.runtime.api.dto;

import io.terminus.trantor2.model.management.meta.domain.log.ModelLogConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 模型操作日志配置 DTO，包含模型名称
 *
 * <AUTHOR>
 */
@Schema(description = "模型操作日志配置 DTO")
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class ModelLogConfigDTO extends ModelLogConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 模型名称
     */
    @Schema(description = "模型名称")
    private String modelName;
    
    public ModelLogConfigDTO(boolean enabled, String displayField, String modelName) {
        super(enabled, displayField);
        this.modelName = modelName;
    }
}