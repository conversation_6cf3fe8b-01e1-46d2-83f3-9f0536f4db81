package io.terminus.trantor2.model.runtime.sdk.meta;

import io.terminus.trantor2.model.common.api.DataStructNodeQueryApi;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(
    name = "${TRANTOR_MODEL_ENGINE_NAME:${TRANTOR_ENGINE_NAME:trantor2-runtime}}",
    url = "${TRANTOR_MODEL_ENGINE_HOST:${TRANTOR_ENGINE_HOST:}}",
    path = "/api/internal/trantor/portal/struct-node"
)
@ConditionalOnMissingClass({
    "io.terminus.trantor2.model.runtime.meta.api.DataStructNodeQueryApiImpl",
    "io.terminus.trantor2.model.management.meta.api.DataStructNodeApi"
})
public interface DataStructNodeQuery extends DataStructNodeQueryApi {
}
