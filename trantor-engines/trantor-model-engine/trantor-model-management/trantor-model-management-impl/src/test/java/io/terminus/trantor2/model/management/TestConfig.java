package io.terminus.trantor2.model.management;

import io.terminus.iam.sdk.client.IAMClient;
import io.terminus.iam.sdk.toolkit.invoker.WebInvoker;
import io.terminus.trantor2.meta.api.cache.MetaCache;
import io.terminus.trantor2.meta.editor.event.publish.RedisMetaEventPublisher;
import io.terminus.trantor2.module.service.ConfigurationService;
import org.mockito.Mockito;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR>
 */
@TestConfiguration
public class TestConfig {
    @Bean
    public MetaCache metaCache() {
        return Mockito.mock(MetaCache.class);
    }

    @Bean
    public WebInvoker webInvoker() {
        return Mockito.mock(WebInvoker.class);
    }

    @Bean
    public ConfigurationService configurationService() {
        return Mockito.mock(ConfigurationService.class);
    }

    @Bean
    public RedisMetaEventPublisher redisMetaEventPublisher() {
        return Mockito.mock(RedisMetaEventPublisher.class);
    }

    @Bean
    public IAMClient iamClient() {
        return Mockito.mock(IAMClient.class);
    }
}
