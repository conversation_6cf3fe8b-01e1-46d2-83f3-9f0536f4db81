package io.terminus.trantor2.model.management.meta.util;

import cn.hutool.core.collection.CollUtil;
import io.terminus.trantor2.model.management.meta.cache.DataStructMetaCache;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.domain.RelationMeta;
import io.terminus.trantor2.model.management.meta.repository.DataStructNodeRepo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/3/29
 */
@Component
@RequiredArgsConstructor
public class InitCurrentModelAlias {

    private final DataStructNodeRepo dataStructNodeRepo;

    private final DataStructMetaCache cache;

    public void execute() {
        List<DataStructNode> nodes = dataStructNodeRepo.findAll();
        for (DataStructNode node : nodes) {
            List<DataStructFieldNode> parentChild = node.getChildren().stream()
                .filter(it -> it.getProps().getRelationMeta() != null)
                .collect(Collectors.toList());
            if (CollUtil.isEmpty(parentChild)) {
                continue;
            }
            for (DataStructFieldNode dataStructFieldNode : parentChild) {
                RelationMeta relationMeta = dataStructFieldNode.getProps().getRelationMeta();
                relationMeta.setCurrentModelAlias(node.getAlias());
            }
            try {
                dataStructNodeRepo.save(node);
            } catch (Exception e) {

            }
        }
        cache.batchRemoveDataStructCache();
    }

}
