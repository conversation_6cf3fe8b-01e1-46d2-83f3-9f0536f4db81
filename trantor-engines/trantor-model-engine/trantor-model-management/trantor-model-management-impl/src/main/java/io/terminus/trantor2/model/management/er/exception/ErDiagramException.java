package io.terminus.trantor2.model.management.er.exception;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.exception.TrantorBizException;

public class ErDiagramException extends TrantorBizException {
    public ErDiagramException(ErrorType errorType, String message) {
        super(errorType, message);
    }

    public ErDiagramException(ErrorType errorType, String message, Object[] args) {
        super(errorType, message, args);
    }
}
