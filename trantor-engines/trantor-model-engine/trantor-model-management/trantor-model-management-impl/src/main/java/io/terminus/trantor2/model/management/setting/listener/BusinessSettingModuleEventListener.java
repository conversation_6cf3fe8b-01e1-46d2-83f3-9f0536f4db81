package io.terminus.trantor2.model.management.setting.listener;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.meta.event.ModuleCreateEvent;
import io.terminus.trantor2.meta.event.ModuleDeleteEvent;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.model.management.meta.consts.DataStructType;
import io.terminus.trantor2.model.management.meta.consts.FieldType;
import io.terminus.trantor2.model.management.meta.domain.*;
import io.terminus.trantor2.model.management.meta.enums.ModelRelationTypeEnum;
import io.terminus.trantor2.model.management.meta.service.DataStructNodeService;
import io.terminus.trantor2.module.service.ModuleManagerQueryService;
import io.terminus.trantor2.module.service.ModuleQueryService;
import io.terminus.trantor2.module.service.TeamService;
import io.terminus.trantor2.setting.BusinessSettingConst;
import io.terminus.trantor2.setting.BusinessSettingUtil;
import io.terminus.trantor2.setting.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class BusinessSettingModuleEventListener {
    @Autowired
    private DataStructNodeService dataStructNodeService;
    @Autowired
    private TeamService teamService;
    @Autowired
    private ModuleQueryService moduleQueryService;

    /**
     * 模块创建时，新增模块下的业务配置
     * @param event
     */
    @Order
    @EventListener(ModuleCreateEvent.class)
    public void createBusinessSettingWhenModuleCreated(ModuleCreateEvent event) {
        if (BooleanUtils.isTrue(event.getModule().getPortal()) || event.getModule().getKey().equals(KeyUtil.SYS_MODULE_KEY)) {
            return;
        }

        log.info("creating business setting when received module create event: {}", event);
        // 判断幂等
        String teamCode = event.getModule().getTeamCode();
        DataStructNode exist = dataStructNodeService.findModel(
                teamService.getTeamIdByCode(teamCode),
                BusinessSettingUtil.composeSettingKey(event.getModule().getKey()));
        if (exist != null) {
            log.info("business setting model already exists");
            return;
        }

        // 创建模块级别的业务配置
        DataStructNode businessSettingModel = buildBusinessSetting(event);
        dataStructNodeService.doSave(businessSettingModel);
    }

    /**
     * 模块删除时，删除模块下的业务配置
     * @param event
     */
    @EventListener(ModuleDeleteEvent.class)
    public void deleteBusinessSettingWhenModuleDeleted(ModuleDeleteEvent event) {
        if (BooleanUtils.isTrue(event.getModule().getPortal()) || event.getModule().getKey().equals(KeyUtil.SYS_MODULE_KEY)) {
            return;
        }

        log.info("deleting business setting when received module delete event: {}", event);

        String teamCode = event.getModule().getTeamCode();
        Long teamId = teamService.getTeamIdByCode(teamCode);
        String moduleKey = event.getModule().getKey();
        TrantorContext.setTeamId(teamId);
        TrantorContext.setTeamCode(teamCode);

        // 删除模块级别的业务配置
        DataStructNode toDeleted = dataStructNodeService.findModel(teamId, BusinessSettingUtil.composeSettingKey(moduleKey));
        if (toDeleted == null) {
            return;
        }

        dataStructNodeService.delete(teamId, Collections.singleton(toDeleted.getKey()), false);
    }

    private DataStructNode buildBusinessSetting(ModuleCreateEvent event) {
        String teamCode = event.getModule().getTeamCode();
        String moduleKey = event.getModule().getKey();
        if (TrantorContext.getContext() == null) {
            TrantorContext.init();
        }
        TrantorContext.setTeamCode(teamCode);
        User user = new User();
        user.setId(1L);
        TrantorContext.setCurrentUser(user);

        DataStructNode businessSetting = new DataStructNode();
        businessSetting.setKey(BusinessSettingUtil.composeSettingKey(moduleKey));
        businessSetting.setName("业务配置");

        businessSetting.setTeamId(teamService.getTeamIdByCode(teamCode));
        businessSetting.setAppId(moduleQueryService.findByKey(moduleKey).getId());
        businessSetting.setParentKey(moduleKey + KeyUtil.MODULE_SEPARATOR + "sys");

        // 设置模型属性
        DataStructProperties props = new DataStructProperties();
        props.setType(DataStructType.PERSIST);
        props.setTableName(event.getModule().getKey() + BusinessSettingConst.SETTING_SUFFIX);
        props.setMainField("code");
        props.setOriginOrgIdEnabled(true);

        DataStructConfig config = new DataStructConfig();
        props.setConfig(config);
        businessSetting.setProps(props);

        // 设置模型字段信息
        businessSetting.setChildren(generateFields(moduleKey));

        return businessSetting;
    }

    private List<DataStructFieldNode> generateFields(String moduleKey) {
        List<DataStructFieldNode> fields = new ArrayList<>();

        // id field
        DataStructFieldNode idField = new DataStructFieldNode();
        idField.setKey("id");
        idField.setAlias("id");
        idField.setName("ID");
        DataStructFieldProperties idFieldProps = new DataStructFieldProperties();
        idFieldProps.setRequired(true);
        idFieldProps.setColumnName("id");
        idFieldProps.setIsSystemField(true);
        idFieldProps.setFieldType(FieldType.NUMBER);
        idFieldProps.setLength(20);
        idFieldProps.setNumberDisplayType("digit");
        idField.setProps(idFieldProps);
        fields.add(idField);

        // groupKey field
        DataStructFieldNode groupKeyField = new DataStructFieldNode();
        groupKeyField.setKey("group_key");
        groupKeyField.setAlias("groupKey");
        groupKeyField.setName("配置标识");
        DataStructFieldProperties groupKeyFieldProps = new DataStructFieldProperties();
        groupKeyFieldProps.setRequired(true);
        groupKeyFieldProps.setColumnName("group_key");
        groupKeyFieldProps.setIsSystemField(false);
        groupKeyFieldProps.setFieldType(FieldType.TEXT);
        groupKeyFieldProps.setCompositeKey(true);
        groupKeyField.setProps(groupKeyFieldProps);
        fields.add(groupKeyField);

        // groupName field
        DataStructFieldNode groupNameField = new DataStructFieldNode();
        groupNameField.setKey("group_name");
        groupNameField.setAlias("groupName");
        groupNameField.setName("配置名称");
        DataStructFieldProperties groupNameFieldProps = new DataStructFieldProperties();
        groupNameFieldProps.setRequired(true);
        groupNameFieldProps.setColumnName("group_name");
        groupNameFieldProps.setIsSystemField(false);
        groupNameFieldProps.setFieldType(FieldType.TEXT);
        groupNameField.setProps(groupNameFieldProps);
        fields.add(groupNameField);

        // code field
        DataStructFieldNode codeField = new DataStructFieldNode();
        codeField.setKey("code");
        codeField.setAlias("code");
        codeField.setName("配置项编码");
        DataStructFieldProperties codeFieldProps = new DataStructFieldProperties();
        codeFieldProps.setRequired(true);
        codeFieldProps.setColumnName("code");
        codeFieldProps.setIsSystemField(false);
        codeFieldProps.setFieldType(FieldType.TEXT);
        codeFieldProps.setCompositeKey(true);
        codeField.setProps(codeFieldProps);
        fields.add(codeField);

        // name field
        DataStructFieldNode nameField = new DataStructFieldNode();
        nameField.setKey("name");
        nameField.setAlias("name");
        nameField.setName("配置项名称");
        DataStructFieldProperties nameFieldProps = new DataStructFieldProperties();
        nameFieldProps.setRequired(false);
        nameFieldProps.setColumnName("name");
        nameFieldProps.setIsSystemField(false);
        nameFieldProps.setFieldType(FieldType.TEXT);
        nameField.setProps(nameFieldProps);
        fields.add(nameField);

        // value field
        DataStructFieldNode valueField = new DataStructFieldNode();
        valueField.setKey("value");
        valueField.setAlias("value");
        valueField.setName("配置项值");
        DataStructFieldProperties valueFieldProps = new DataStructFieldProperties();
        valueFieldProps.setRequired(false);
        valueFieldProps.setColumnName("value");
        valueFieldProps.setIsSystemField(false);
        valueFieldProps.setFieldType(FieldType.TEXT);
        valueField.setProps(valueFieldProps);
        fields.add(valueField);

        // description field
        DataStructFieldNode descField = new DataStructFieldNode();
        descField.setKey("description");
        descField.setAlias("description");
        descField.setName("配置项备注");
        DataStructFieldProperties descFieldProps = new DataStructFieldProperties();
        descFieldProps.setRequired(false);
        descFieldProps.setColumnName("description");
        descFieldProps.setIsSystemField(false);
        descFieldProps.setFieldType(FieldType.TEXT);
        descField.setProps(descFieldProps);
        fields.add(descField);

        // properties field
        DataStructFieldNode propsField = new DataStructFieldNode();
        propsField.setKey("properties");
        propsField.setAlias("properties");
        propsField.setName("配置项属性");
        DataStructFieldProperties propsFieldProps = new DataStructFieldProperties();
        propsFieldProps.setRequired(false);
        propsFieldProps.setColumnName("properties");
        propsFieldProps.setIsSystemField(false);
        propsFieldProps.setFieldType(FieldType.MULTI_TEXT);
        propsField.setProps(propsFieldProps);
        fields.add(propsField);

        // parentCode field
        DataStructFieldNode parentCodeField = new DataStructFieldNode();
        parentCodeField.setKey("parent_code");
        parentCodeField.setAlias("parentCode");
        parentCodeField.setName("父配置项 code");
        DataStructFieldProperties parentCodeFieldProps = new DataStructFieldProperties();
        parentCodeFieldProps.setRequired(false);
        parentCodeFieldProps.setColumnName("parent_code");
        parentCodeFieldProps.setIsSystemField(false);
        parentCodeFieldProps.setFieldType(FieldType.TEXT);
        parentCodeField.setProps(parentCodeFieldProps);
        fields.add(parentCodeField);

        DataStructFieldNode originOrgIdField = new DataStructFieldNode();
        originOrgIdField.setKey("origin_org_id");
        originOrgIdField.setAlias("originOrgId");
        originOrgIdField.setName("所属组织");
        DataStructFieldProperties originOrgIdFieldProps = new DataStructFieldProperties();
        originOrgIdFieldProps.setRequired(false);
        originOrgIdFieldProps.setColumnName("origin_org_id");
        originOrgIdFieldProps.setIsSystemField(true);
        originOrgIdFieldProps.setFieldType(FieldType.NUMBER);
        originOrgIdFieldProps.setLength(20);
        originOrgIdFieldProps.setNumberDisplayType("digit");
        originOrgIdField.setProps(originOrgIdFieldProps);
        fields.add(originOrgIdField);

        // deleted field
        DataStructFieldNode deletedField = new DataStructFieldNode();
        deletedField.setKey("deleted");
        deletedField.setAlias("deleted");
        deletedField.setName("逻辑删标识");
        DataStructFieldProperties deletedFieldProps = new DataStructFieldProperties();
        deletedFieldProps.setRequired(true);
        deletedFieldProps.setColumnName("deleted");
        deletedFieldProps.setIsSystemField(true);
        deletedFieldProps.setFieldType(FieldType.NUMBER);
        deletedFieldProps.setLength(20);
        deletedFieldProps.setNumberDisplayType("digit");
        deletedField.setProps(deletedFieldProps);
        fields.add(deletedField);

        // version field
        DataStructFieldNode versionField = new DataStructFieldNode();
        versionField.setKey("version");
        versionField.setAlias("version");
        versionField.setName("版本号");
        DataStructFieldProperties versionFieldProps = new DataStructFieldProperties();
        versionFieldProps.setRequired(false);
        versionFieldProps.setColumnName("version");
        versionFieldProps.setIsSystemField(true);
        versionFieldProps.setFieldType(FieldType.NUMBER);
        versionFieldProps.setLength(20);
        versionFieldProps.setNumberDisplayType("digit");
        versionField.setProps(versionFieldProps);
        fields.add(versionField);

        // createdBy field
        DataStructFieldNode createdByField = new DataStructFieldNode();
        createdByField.setKey("created_by");
        createdByField.setAlias("createdBy");
        createdByField.setName("创建人");
        DataStructFieldProperties createdByFieldProps = new DataStructFieldProperties();
        createdByFieldProps.setRequired(false);
        createdByFieldProps.setColumnName("created_by");
        createdByFieldProps.setIsSystemField(true);
        createdByFieldProps.setFieldType(FieldType.OBJECT);
        RelationMeta userRelation = new RelationMeta();
        userRelation.setRelationType(ModelRelationTypeEnum.LINK);
        userRelation.setRelationModelAlias(moduleKey + KeyUtil.MODULE_SEPARATOR + "user");
        userRelation.setSync(false);
        createdByFieldProps.setRelationMeta(userRelation);
        createdByField.setProps(createdByFieldProps);
        fields.add(createdByField);

        // createdAt field
        DataStructFieldNode createdAtField = new DataStructFieldNode();
        createdAtField.setKey("created_at");
        createdAtField.setAlias("createdAt");
        createdAtField.setName("创建时间");
        DataStructFieldProperties createdAtFieldProps = new DataStructFieldProperties();
        createdAtFieldProps.setRequired(false);
        createdAtFieldProps.setColumnName("created_at");
        createdAtFieldProps.setIsSystemField(true);
        createdAtFieldProps.setFieldType(FieldType.DATE);
        createdAtField.setProps(createdAtFieldProps);
        fields.add(createdAtField);

        // updatedBy field
        DataStructFieldNode updatedByField = new DataStructFieldNode();
        updatedByField.setKey("updated_by");
        updatedByField.setAlias("updatedBy");
        updatedByField.setName("更新人");
        DataStructFieldProperties updatedByFieldProps = new DataStructFieldProperties();
        updatedByFieldProps.setRequired(false);
        updatedByFieldProps.setColumnName("updated_by");
        updatedByFieldProps.setIsSystemField(true);
        updatedByFieldProps.setFieldType(FieldType.OBJECT);
        userRelation = new RelationMeta();
        userRelation.setRelationType(ModelRelationTypeEnum.LINK);
        userRelation.setRelationModelAlias(moduleKey + KeyUtil.MODULE_SEPARATOR + "user");
        userRelation.setSync(false);
        updatedByFieldProps.setRelationMeta(userRelation);
        updatedByField.setProps(updatedByFieldProps);
        fields.add(updatedByField);

        // updatedAt field
        DataStructFieldNode updatedAtField = new DataStructFieldNode();
        updatedAtField.setKey("updated_at");
        updatedAtField.setAlias("updatedAt");
        updatedAtField.setName("更新时间");
        DataStructFieldProperties updatedAtFieldProps = new DataStructFieldProperties();
        updatedAtFieldProps.setRequired(false);
        updatedAtFieldProps.setColumnName("updated_at");
        updatedAtFieldProps.setIsSystemField(true);
        updatedAtFieldProps.setFieldType(FieldType.DATE);
        updatedAtField.setProps(updatedAtFieldProps);
        fields.add(updatedAtField);

        DataStructFieldNode statusField = new DataStructFieldNode();
        statusField.setKey(BusinessSettingConst.FIELD_STATUS);
        statusField.setAlias(BusinessSettingConst.FIELD_STATUS);
        statusField.setName("状态");
        DataStructFieldProperties statusFieldProps = getStatusFieldProps();
        statusField.setProps(statusFieldProps);
        fields.add(statusField);

        return fields;
    }

    @NotNull
    private static DataStructFieldProperties getStatusFieldProps() {
        DataStructFieldProperties statusFieldProps = new DataStructFieldProperties();
        statusFieldProps.setRequired(false);
        statusFieldProps.setDefaultValue(StatusEnum.INACTIVE.name());
        statusFieldProps.setColumnName(BusinessSettingConst.FIELD_STATUS);
        statusFieldProps.setIsSystemField(true);
        statusFieldProps.setFieldType(FieldType.ENUM);

        DataDictProperties dictProps = new DataDictProperties();
        dictProps.setMultiSelect(false);
        dictProps.setDictValues(getStatusDictProps());
        statusFieldProps.setDictPros(dictProps);

        statusFieldProps.setLength(20);
        statusFieldProps.setNumberDisplayType("digit");
        return statusFieldProps;
    }

    @NotNull
    private static List<Map<String, Object>> getStatusDictProps() {
        List<Map<String, Object>> dictValues = new ArrayList<>();
        Map<String, Object> dictValue = new HashMap<>();
        dictValue.put("label", StatusEnum.DRAFT.getLabel());
        dictValue.put("value", StatusEnum.DRAFT.name());
        dictValues.add(dictValue);

        dictValue = new HashMap<>();
        dictValue.put("label", StatusEnum.INACTIVE.getLabel());
        dictValue.put("value", StatusEnum.INACTIVE.name());
        dictValues.add(dictValue);

        dictValue = new HashMap<>();
        dictValue.put("label", StatusEnum.ENABLED.getLabel());
        dictValue.put("value", StatusEnum.ENABLED.name());
        dictValues.add(dictValue);

        dictValue = new HashMap<>();
        dictValue.put("label", StatusEnum.DISABLED.getLabel());
        dictValue.put("value", StatusEnum.DISABLED.name());
        dictValues.add(dictValue);

        dictValue = new HashMap<>();
        dictValue.put("label", StatusEnum.DELETED.getLabel());
        dictValue.put("value", StatusEnum.DELETED.name());
        dictValues.add(dictValue);
        return dictValues;
    }
}
