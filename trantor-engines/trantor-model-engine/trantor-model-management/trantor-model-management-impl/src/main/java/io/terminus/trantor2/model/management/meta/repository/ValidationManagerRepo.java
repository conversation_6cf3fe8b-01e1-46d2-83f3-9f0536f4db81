package io.terminus.trantor2.model.management.meta.repository;

import io.terminus.trantor2.meta.api.dto.*;
import io.terminus.trantor2.meta.api.service.MetaEditService;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.model.management.meta.domain.validation.ValidationNode;
import io.terminus.trantor2.model.meta.repository.ValidationQueryRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nonnull;
import java.util.UUID;

import static io.terminus.trantor2.model.management.meta.domain.validation.ValidationNode.KEY_JOIN_SYMBOL;

/**
 * <AUTHOR>
 */
@Component
public class ValidationManagerRepo extends ValidationQueryRepo {
    /**
     * FIXME runtime-starter 依赖了 model-management, 这里需要使用 required = false
     */
    @Autowired(required = false)
    private MetaEditService editService;

    public ValidationManagerRepo(MetaQueryService queryService) {
        super(queryService);
    }

    public String save(@Nonnull MetaEditAndQueryContext ctx, @Nonnull ValidationNode node) {
        MetaTreeNodeExt ext = node.convert2Meta();
        EditOpRequest nodeOp;
        if (ext.getKey() == null) {
            ext.setKey(generateKey(node.getParentKey()));
            nodeOp = EditUtil.createNodeOp(ext
                    , new MoveTarget(node.getParentKey(), MoveTargetType.ChildFirst));
        } else {
            nodeOp = EditUtil.updateNodeOp(ext);
        }
        editService.submitOp(ctx, nodeOp);
        return ext.getKey();
    }

    private String generateKey(String parentKey) {
        return parentKey + KEY_JOIN_SYMBOL + UUID.randomUUID();
    }
}
