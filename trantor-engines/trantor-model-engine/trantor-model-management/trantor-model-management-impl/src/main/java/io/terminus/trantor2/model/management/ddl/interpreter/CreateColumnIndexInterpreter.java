package io.terminus.trantor2.model.management.ddl.interpreter;

import io.terminus.trantor2.model.management.ddl.SqlInfo;
import io.terminus.trantor2.model.management.ddl.builder.DDLExecutorProxy;
import io.terminus.trantor2.model.management.ddl.event.CommonDDLParam;
import io.terminus.trantor2.model.management.ddl.event.CreateColumnIndex;

import java.util.Collections;
import java.util.List;

public class CreateColumnIndexInterpreter extends AbstractSqlInterpreter {

    private final DDLExecutorProxy ddlExecutor;

    public CreateColumnIndexInterpreter() {
        this.ddlExecutor = new DDLExecutorProxy();
    }

    @Override
    public List<SqlInfo> execute(CommonDDLParam ddlParam, boolean execute) {
        CreateColumnIndex createColumnIndex = (CreateColumnIndex) ddlParam;

        SqlInfo sqlInfo = ddlExecutor.getMySQLDDLExecutor().createIndex(
            ddlParam.getDataSource(),
            createColumnIndex.getTableName(),
            createColumnIndex.getIndexName(),
            createColumnIndex.getIndexType(),
            createColumnIndex.getIndexFields(),
            execute
        );

        return Collections.singletonList(sqlInfo);
    }

    @Override
    public List<SqlInfo> rollback(CommonDDLParam ddlParam, boolean execute) {
        CreateColumnIndex createColumnIndex = (CreateColumnIndex) ddlParam;
        SqlInfo sqlInfo = ddlExecutor.getMySQLDDLExecutor().removeIndex(
            createColumnIndex.getDataSource(),
            createColumnIndex.getTableName(),
            createColumnIndex.getIndexName(),
            execute
        );

        return Collections.singletonList(sqlInfo);
    }
}
