package io.terminus.trantor2.model.management.ddl.event;


import io.terminus.trantor2.model.management.ddl.IndexTypeEnum;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
public class CreateColumnIndex extends CommonDDLParam {
    private String indexName;

    /**
     * 索引类型（唯一索引，普通索引）
     */
    private IndexTypeEnum indexType;

    private List<DataStructFieldNode> indexFields;
}
