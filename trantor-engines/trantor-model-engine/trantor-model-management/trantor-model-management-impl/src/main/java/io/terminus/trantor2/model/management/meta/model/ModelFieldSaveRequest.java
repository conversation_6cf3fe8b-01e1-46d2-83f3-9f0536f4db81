package io.terminus.trantor2.model.management.meta.model;

import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.enums.ModelMetaOperationType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 模型字段新增/更新请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ModelFieldSaveRequest extends AbstractRequest {
    /**
     * 格式: <moduleKey>$<modelKey>, eg: sys_common$sales_order_22220
     */
    private String modelKey;

    /**
     * 待新增/更新字段
     */
    DataStructFieldNode field;

    ModelMetaOperationType operationType;
}
