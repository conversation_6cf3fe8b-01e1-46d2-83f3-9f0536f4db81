package io.terminus.trantor2.model.management.meta.api.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.api.service.QueryOp;
import io.terminus.trantor2.meta.request.QueryByKeyRequest;
import io.terminus.trantor2.meta.request.QueryByKeysRequest;
import io.terminus.trantor2.meta.request.SimpleModelInfoRequest;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.model.common.util.DeepCopyUtil;
import io.terminus.trantor2.model.management.api.model.*;
import io.terminus.trantor2.model.management.ddl.DDLInfo;
import io.terminus.trantor2.model.management.meta.api.DataStructNodeApi;
import io.terminus.trantor2.model.management.meta.check.ModelMetaDifference;
import io.terminus.trantor2.model.management.meta.check.ModuleModelMetaDifference;
import io.terminus.trantor2.model.management.meta.consts.FieldType;
import io.terminus.trantor2.model.management.meta.desensitization.DesensitizationRule;
import io.terminus.trantor2.model.management.meta.desensitization.DesensitizationRuleFactory;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.domain.SimpleDataStructNode;
import io.terminus.trantor2.model.management.meta.model.*;
import io.terminus.trantor2.model.management.meta.service.DataStructNodeService;
import io.terminus.trantor2.model.management.meta.system.SystemModelInstanceHolder;
import io.terminus.trantor2.model.management.meta.util.AliasUtil;
import io.terminus.trantor2.model.management.model.DDLGenerationRequest;
import io.terminus.trantor2.model.management.model.DiffRequest;
import io.terminus.trantor2.model.management.model.ModelShardingConfigDTO;
import io.terminus.trantor2.model.management.model.TableInfo;
import io.terminus.trantor2.model.management.model.search.SearchFullSyncRequest;
import io.terminus.trantor2.model.management.model.search.SearchModelSyncTaskDto;
import io.terminus.trantor2.model.management.model.search.SyncTaskQueryRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.ValidationException;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Author: 诸立达
 * @Date: 2023/2/24 4:08 下午
 */
@Tag(name = "数据结构对象")
@RestController
@Slf4j
@RequiredArgsConstructor
@Primary
public class DataStructNodeController implements DataStructNodeApi {

    private final DataStructNodeService dataStructNodeService;

    private final SystemModelInstanceHolder systemModelInstanceHolder;

    private final MetaQueryService metaQueryService;

    /**
     * 获取数据对象列表
     *
     * @param pageRequest
     * @return Pageable<DataStructNode>
     */
    public Response<Paging<DataStructNode>> page(DataStructNodePageRequest pageRequest) {
        Paging<DataStructNode> page = dataStructNodeService.page(pageRequest);
        systemModelInstanceHolder.fillUserName(page.getData());
        return Response.ok(page);
    }

    @Override
    public Response<List<DataStructNode>> findAll(DataStructNodeQueryAllRequest pageRequest) {
        return Response.ok(dataStructNodeService.findAll(pageRequest));
    }

    @Override
    public Response<List<DataStructNode>> simpleList(DataStructNodeQueryAllRequest pageRequest) {
        return Response.ok(dataStructNodeService.findAllSimpleInfo(pageRequest));
    }

    @Override
    public Response<List<DataStructNode>> list(ModelListRequest request) {
        return Response.ok(dataStructNodeService.listModelInfo(request));
    }

    @Override
    public Response<Paging<DataStructObjectInfo>> info(DataStructNodePageRequest request) {
        return Response.ok(dataStructNodeService.getInfo(request));
    }

    @Override
    public Response<RelatedDataStructDetail> relatedById(IdRequest idRequest) {
        Response<DataStructNode> mainModel = detail(idRequest);
        DataStructNode mainModelData = mainModel.getData();
        if (mainModelData == null) {
            return Response.ok();
        }

        return Response.ok(constructModelInfo(mainModelData, idRequest.getTeamId()));
    }

    @Override
    public Response<DataStructNode> findByModelKey(String modelKey) {
        DataStructNode result = dataStructNodeService.queryByKey(modelKey, TrantorContext.getTeamCode());
        return Response.ok(result);
    }

    @Override
    public Response<List<RelatedDataStructDetail>> relatedByAlias(QueryByAliasRequest aliasRequest) {
        List<RelatedDataStructDetail> response = new ArrayList<>();
        QueryByAliasRequest mainModelAlias = new QueryByAliasRequest();
        mainModelAlias.setTeamId(aliasRequest.getTeamId());
        mainModelAlias.setAliases(aliasRequest.getAliases());
        Response<List<DataStructNode>> mainModelResponse = findByAlias(mainModelAlias);
        List<DataStructNode> data = mainModelResponse.getData();
        if (CollectionUtil.isNotEmpty(data)) {
            for (DataStructNode structNode : data) {
                RelatedDataStructDetail modelInfo = constructModelInfo(structNode, aliasRequest.getTeamId());
                response.add(modelInfo);
            }
        }
        return Response.ok(response);
    }

    @Override
    public Response<CascadeModel> cascadeInfo(Boolean isSync, Integer depth, KeyRequest keyRequest) {
        QueryByAliasRequest aliasRequest = new QueryByAliasRequest();
        aliasRequest.setTeamId(keyRequest.getTeamId());
        aliasRequest.setAliases(Collections.singletonList(keyRequest.getKey()));

        List<DataStructNode> mainModels = dataStructNodeService.queryByAlias(aliasRequest);
        if (CollectionUtils.isEmpty(mainModels)) {
            return Response.ok();
        }

        // 已经查过的模型 alias 列表
        // 已经查过的模型 alias Map
        Map<String, DataStructNode> queriedModelMap = mainModels.stream().collect(Collectors.toMap(DataStructNode::getKey, model -> model));
        CascadeModel response = new CascadeModel();

        // 递归填充子模型元数据
        List<CascadeModel> children = new ArrayList<>();
        recursiveFillChildrenModel(aliasRequest.getTeamId(), mainModels.get(0), queriedModelMap, children, isSync, depth-1);

        response.setCurrent(mainModels.get(0));
        response.setChildren(children);

        return Response.ok(response);
    }

    /**
     * 根据主模型信息获取其关联模型，构造新的数据结构包含: 主模型信息+关联模型信息
     *
     * @param mainModelData
     * @param teamId
     * @return
     */
    private RelatedDataStructDetail constructModelInfo(DataStructNode mainModelData, Long teamId) {
        List<DataStructFieldNode> children = mainModelData.getChildren();
        if (CollectionUtils.isEmpty(children)) {
            return new RelatedDataStructDetail(mainModelData, null);
        }

        // 获取当前模型的关联模型 alias 列表
        Set<String> relatedModelAliases = children.stream()
            .filter(it -> it.getProps().getRelationMeta() != null)
            .map(it -> it.getProps().getRelationMeta().getRelationModelAlias())
            .collect(Collectors.toSet());

        QueryByAliasRequest queryByAliasRequest = new QueryByAliasRequest();
        queryByAliasRequest.setTeamId(teamId);
        queryByAliasRequest.setAliases(new ArrayList<>(relatedModelAliases));
        // 获取当前模型的关联模型元信息
        List<DataStructNode> relationModels = dataStructNodeService.queryByAlias(queryByAliasRequest);

        return new RelatedDataStructDetail(mainModelData, relationModels);
    }

    /**
     * 递归填充关联模型
     *
     * @param teamId
     * @param parentModel
     */
    private void recursiveFillChildrenModel(long teamId, DataStructNode parentModel, Map<String, DataStructNode> queriedModelMap, List<CascadeModel> childrenModels, boolean isSync, Integer depth) {
        if (parentModel == null) {
            return;
        }

        if (depth <= 0) {
            return;
        }

        List<DataStructFieldNode> fields = parentModel.getChildren();
        if (CollectionUtils.isEmpty(fields)) {
            return;
        }

        // 获取当前模型的关联模型 alias 列表
        Set<String> relationModelKeys = new HashSet<>();
        List<DataStructNode> queriedChildren = new ArrayList<>();
        fields.forEach(field -> {
            if (field.getProps().getRelationMeta() == null) {
                return;
            }

            if (BooleanUtils.isTrue(isSync) && !field.getProps().getRelationMeta().isSync()) {
                return;
            }

            // 关联模型已经获取过元数据，不再重复获取，防止自关联时的无限递归
            if (queriedModelMap.containsKey(field.getProps().getRelationMeta().getRelationModelAlias())) {
                queriedChildren.add(queriedModelMap.get(field.getProps().getRelationMeta().getRelationModelAlias()));
                return;
            }

            relationModelKeys.add(field.getProps().getRelationMeta().getRelationModelAlias());
        });
        if (CollectionUtils.isNotEmpty(relationModelKeys)) {
            QueryByAliasRequest queryByAliasRequest = new QueryByAliasRequest();
            queryByAliasRequest.setTeamId(teamId);
            queryByAliasRequest.setAliases(new ArrayList<>(relationModelKeys));
            // 获取当前模型的关联模型元信息
            List<DataStructNode> relationModels = dataStructNodeService.findByAlias(queryByAliasRequest);
            relationModels.forEach(it -> queriedModelMap.put(it.getKey(), it));

            queriedChildren.addAll(relationModels);
        }

        if (CollectionUtils.isEmpty(queriedChildren)) {
            return;
        }

        queriedChildren.forEach(childModel -> {
            List<CascadeModel> children = new ArrayList<>();
            recursiveFillChildrenModel(teamId, childModel, queriedModelMap, children, isSync, depth-1);

            CascadeModel cascadeModel = new CascadeModel();
            cascadeModel.setCurrent(childModel);
            cascadeModel.setChildren(children);
            childrenModels.add(cascadeModel);
        });
    }

    /**
     * 保存数据对象模型
     *
     * @param updateStructNodeRequest
     * @return Boolean
     */
    @Override
    public Response<DataStructNode> save(SaveStructNodeRequest updateStructNodeRequest) {
        log.info("creating/updating model meta, request body: {}", JsonUtil.toJson(updateStructNodeRequest));
        return Response.ok(dataStructNodeService.save(updateStructNodeRequest));
    }

    /**
     * 删除数据对象模型
     *
     * @param idsRequest
     * @return Boolean
     */
    public Response<Boolean> delete(IdsRequest idsRequest) {
        log.info("deleting model meta, request body: {}", JsonUtil.toJson(idsRequest));
        dataStructNodeService.delete(idsRequest.getIds(), BooleanUtils.isTrue(idsRequest.getForce()));
        return Response.ok();
    }

    @Override
    public Response<Void> createStatisticModelByPersistentModel(StatisticModelCreateByPersistentModel request) {
        log.info("creating statistic model by persistent model, request body: {}", JsonUtil.toJson(request));
        dataStructNodeService.createStatisticModelByPersistentModel(request);
        return Response.ok();
    }

    @Override
    public Response<Void> syncStatisticModel(StatisticModelSyncRequest request) {
        log.info("sync statistic model, request body: {}", JsonUtil.toJson(request));
        dataStructNodeService.syncStatisticModel(request);
        return Response.ok();
    }

    @Override
    public Response<Void> saveField(ModelFieldSaveRequest fieldSaveRequest) {
        log.info("creating/updating model field: {}", JsonUtil.toJson(fieldSaveRequest));
        dataStructNodeService.saveField(fieldSaveRequest);
        return Response.ok();
    }

    @Override
    public Response<Void> deleteField(ModelFieldDeleteRequest fieldDeleteRequest) {
        log.info("deleting model field: {}", JsonUtil.toJson(fieldDeleteRequest));
        dataStructNodeService.deleteField(fieldDeleteRequest);
        return Response.ok();
    }

    /**
     * 获取数据节点对象详情
     *
     * @param idRequest
     * @return Response<DataStructNode>
     */
    public Response<DataStructNode> detail(IdRequest idRequest) {
        return Response.ok(dataStructNodeService.findById(idRequest.getId()));
    }

    @Override
    public Response<Schema> schemaInfo(String modelKey) {
        return Response.ok(dataStructNodeService.findSchemaByKey(modelKey));
    }

    @Override
    public Response<Void> saveIndex(SaveIndexRequest saveIndexRequest) {
        log.info("creating/updating model index: {}", JsonUtil.toJson(saveIndexRequest));
        dataStructNodeService.saveIndex(saveIndexRequest);
        return Response.ok();
    }

    @Override
    public Response<Void> deleteIndex(DeleteIndexRequest deleteIndexRequest) {
        log.info("deleting model index: {}", JsonUtil.toJson(deleteIndexRequest));
        dataStructNodeService.deleteIndex(deleteIndexRequest);
        return Response.ok();
    }

    @Override
    public Response<List<DataStructNode>> findByAlias(QueryByAliasRequest queryByAliasRequest) {
        return Response.ok(dataStructNodeService.findByAlias(queryByAliasRequest));
    }

    @Override
    public <T extends Serializable> Response<DataStructNode> findByKeyAndTeam(String key, QueryByKeyRequest<T> request) {
        return Response.ok(dataStructNodeService.queryByKey(key, Optional.ofNullable(request).map(QueryByKeyRequest::getTeam).orElse(null)));
    }

    @Override
    public <T extends Serializable> Response<Collection<DataStructNode>> findByKeys(QueryByKeysRequest<T> request) {
        return Response.ok(dataStructNodeService.queryByKeys(request));
    }

    @Override
    public Response<Map<String, String>> findModelSampleInfoByAlias(QueryByAliasRequest queryByAliasRequest) {
        if (Objects.isNull(queryByAliasRequest.getAliases()) || queryByAliasRequest.getAliases().size() != 2) {
            throw new ValidationException("sourceKey or targetKey must not be block");
        }
        List<DataStructNode> dataStructNodes = dataStructNodeService.queryByAlias(queryByAliasRequest);
        if (dataStructNodes.size() != 2) {
            throw new ValidationException("sourceModel or targetModel no found");
        }
        List<String> fieldsInfo = dataStructNodes.stream()
                                                 .map(item -> {
                                                     String fields = item.getChildren()
                                                                         .stream()
                                                                         .map(field -> String.format("%s(%s)",
                                                                                                     field.getAlias(),
                                                                                                     field.getName()))
                                                                         .collect(Collectors.joining(","));

                                                     return String.format("%s(%s),其中字段如下：%s,主字段为：%s",
                                                                          item.getName(),
                                                                          KeyUtil.shortKey(item.getAlias()),
                                                                          fields,
                                                                          AliasUtil.fieldKeyToAlias(item.getProps().getMainField())
                                                                          );
                                                 })
                                                 .collect(Collectors.toList());
        Map<String, String> modelInfo = new HashMap<>();
        if (fieldsInfo.size() == 2) {
            modelInfo.putIfAbsent("sourceModel", fieldsInfo.get(0));
            modelInfo.putIfAbsent("targetModel", fieldsInfo.get(1));
        }
        return Response.ok(modelInfo);
    }

    @Override
    public Response<List<Pair<FieldType, String>>> findFieldType() {
        List<Pair<FieldType, String>> fields = new ArrayList<>(FieldType.values().length);
        for (FieldType fieldType : FieldType.values()) {
            Pair<FieldType, String> of = Pair.of(fieldType, fieldType.getName());
            fields.add(of);
        }
        return Response.ok(fields);
    }

    @Override
    public Response<Void> clearCache() {
        dataStructNodeService.deleteCache();
        return Response.ok();
    }

    @Override
    public Response<Void> initSystemModel(String moduleKey, String parentKey, Long teamId, Long appId) {
        List<DataStructNode> systemStructs = systemModelInstanceHolder.getAllSystemDataStruct();
        QueryOp queryOp = metaQueryService.queryInTeam(teamId);
        systemStructs.forEach(sysModel -> {
            // 进行深拷贝
            DataStructNode n = DeepCopyUtil.copy(sysModel);
            n.setTeamId(teamId);
            n.setAppId(appId);
            n.setParentKey(parentKey);
            n.setKey(moduleKey + "$" + n.getKey());
            n.setAlias(n.getKey());
            n.setId(null);
            queryOp.findOne(Field.key().equal(n.getKey())).ifPresent(it -> {
                n.setId(it.getId());
            });
            dataStructNodeService.doSave(n);
        });
        return Response.ok();
    }

    @Override
    public Response<List<DesensitizationRule>> getDesensitizedRules() {
        return Response.ok(DesensitizationRuleFactory.getAllRules());
    }

    @Override
    public Response<Boolean> updateSearchModel(SaveStructNodeRequest updateStructNodeRequest) {
        dataStructNodeService.updateSearchModel(updateStructNodeRequest);
        return Response.ok();
    }

    @Override
    public Response<Boolean> fullSyncSearchModel(SearchFullSyncRequest idRequest) {
        dataStructNodeService.fullSyncSearchModel(idRequest);
        return Response.ok();
    }

    @Override
    public Response<List<SearchModelSyncTaskDto>> querySearchModelSyncTasks(SyncTaskQueryRequest syncTaskQueryRequest) {
        return Response.ok(dataStructNodeService.querySearchModelSyncTasks(syncTaskQueryRequest));
    }

    @Override
    public Response<DataStructNode> querySearchModelConfig(IdRequest idRequest) {
        return Response.ok(dataStructNodeService.querySearchModelConfig(idRequest));
    }

    @Override
    public Response<Paging<ModelShardingConfigDTO>> queryModelShardingConfig(String moduleKey, Integer pageNo, Integer pageSize) {
        return Response.ok(dataStructNodeService.queryModelShardingConfig(TrantorContext.getTeamId(), TrantorContext.getTeamCode(), moduleKey, pageNo - 1, pageSize));
    }

    @Override
    public Response<Void> updateModelShardingConfig(ModelShardingConfigDTO request) {
        dataStructNodeService.updateModelShardingConfig(TrantorContext.getTeamId(), TrantorContext.getTeamCode(), request);
        return Response.ok();
    }

    @Override
    public Response<Void> updateModelShardingConfigBatch(List<ModelShardingConfigDTO> request) {
        dataStructNodeService.updateModelShardingConfigBatch(TrantorContext.getTeamId(), TrantorContext.getTeamCode(), request);
        return Response.ok();
    }

    @Override
    public Response<List<DDLInfo>> diffModelMetaWithSchema(DiffRequest request){
        QueryOp queryOp = metaQueryService.queryInTeam(request.getTeamId());
        return Response.ok(dataStructNodeService.diffModelMetaWithSchema(request, queryOp));
    }

    @Override
    public Response<Void> batchCreate(ModelBatchCreateRequest request) {
        dataStructNodeService.batchCreate(request);

        return Response.ok();
    }

    @Override
    public Response<Paging<TableInfo>> queryTables(SchemaPageRequest request) {
        return Response.ok(dataStructNodeService.queryTables(request));
    }

    @Override
    public Response<List<ModuleModelMetaDifference>> diffModelMetaByTeam() {
        return Response.ok(dataStructNodeService.diffModeMetaByTeam());
    }

    @Override
    public Response<ModelMetaDifference> diffModelMetaByKey(String key) {
        return Response.ok(dataStructNodeService.diffModeMetaByKey(key));
    }

    @Override
    public Response<Map<String, String>> generateDDLByModelKey(DDLGenerationRequest request) {
        return Response.ok(dataStructNodeService.generateDDLByModelKey(request));
    }

    @Override
    public <T extends Serializable> Response<Collection<SimpleDataStructNode>> listSimpleByKeys(SimpleModelInfoRequest<T> request) {
        return Response.ok(dataStructNodeService.listSimpleByKeys(request));
    }
}
