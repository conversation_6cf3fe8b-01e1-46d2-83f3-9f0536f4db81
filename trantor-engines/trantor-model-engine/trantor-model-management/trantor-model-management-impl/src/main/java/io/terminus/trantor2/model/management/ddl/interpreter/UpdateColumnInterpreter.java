package io.terminus.trantor2.model.management.ddl.interpreter;

import io.terminus.trantor2.model.management.ddl.SqlInfo;
import io.terminus.trantor2.model.management.ddl.builder.DDLExecutorProxy;
import io.terminus.trantor2.model.management.ddl.event.CommonDDLParam;
import io.terminus.trantor2.model.management.ddl.event.UpdateColumn;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
public class UpdateColumnInterpreter extends AbstractSqlInterpreter {

    private final DDLExecutorProxy ddlExecutor;

    public UpdateColumnInterpreter() {
        this.ddlExecutor = new DDLExecutorProxy();
    }

    @Override
    public List<SqlInfo> execute(CommonDDLParam commonDDLParam, boolean execute) {
        UpdateColumn updateColumn = (UpdateColumn) commonDDLParam;

        return doUpdate(updateColumn, execute);
    }

    private List<SqlInfo> doUpdate(UpdateColumn updateColumn, boolean execute) {
        List<SqlInfo> result = new ArrayList<>();
        if (Objects.equals(updateColumn.getNewField().getProps(), updateColumn.getOldField().getProps())) {
            return result;
        }

        // when type changes to/from String we need to change the subpart
        // when fieldName changes we need to update index name
        // recreating an index is expensive, so we might need to make this smarter in the future
        // update column
        SqlInfo sqlInfo = ddlExecutor.getMySQLDDLExecutor().updateColumn(
            updateColumn.getDataSource(),
            updateColumn.getTableName(),
            updateColumn.getNewField(),
            updateColumn.getAfterColumn(),
            updateColumn.getOldField(),
            execute
        );

        result.add(sqlInfo);
        return result;
    }

    @Override
    public List<SqlInfo> rollback(CommonDDLParam commonDDLParam, boolean execute) {
        UpdateColumn updateColumn = (UpdateColumn) commonDDLParam;

        return doUpdate(updateColumn, execute);
    }
}
