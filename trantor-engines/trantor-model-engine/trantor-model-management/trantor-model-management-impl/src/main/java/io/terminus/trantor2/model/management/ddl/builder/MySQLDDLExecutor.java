package io.terminus.trantor2.model.management.ddl.builder;

import com.google.common.collect.Lists;
import io.terminus.trantor2.datasource.model.SupportDialectTypeEnum;
import io.terminus.trantor2.model.management.ddl.IndexTypeEnum;
import io.terminus.trantor2.model.management.ddl.SqlInfo;
import io.terminus.trantor2.model.management.ddl.builder.build.AddColumn;
import io.terminus.trantor2.model.management.ddl.builder.build.AlterColumnType;
import io.terminus.trantor2.model.management.ddl.builder.build.DdlGenerator;
import io.terminus.trantor2.model.management.ddl.builder.build.TableStructureUtil;
import io.terminus.trantor2.model.management.meta.consts.FieldType;
import io.terminus.trantor2.model.management.meta.dialect.entity.TableStructure;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.util.AliasUtil;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DataType;
import org.jooq.Field;
import org.jooq.SQLDialect;
import org.jooq.conf.ParamType;
import org.jooq.impl.DSL;
import org.jooq.impl.DefaultDataType;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.CollectionUtils;

import javax.sql.DataSource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class MySQLDDLExecutor extends AbstractDDLExecutor {
    /**
     * 索引字段最大长度
     */
    public static final String INDEX_FIELD_MAX_SIZE = "(191)";

    private SQLDialect sqlDialect;

    public MySQLDDLExecutor() {
        this(SQLDialect.MYSQL_5_7);
    }

    public MySQLDDLExecutor(SQLDialect sqlDialect) {
        // dslContext只用于sql生成，不用于执行，dataSource传空不会影响逻辑
        this.sqlDialect = sqlDialect;
        super.dslContext = DSL.using((DataSource) null, sqlDialect, SETTINGS);
    }

    @Override
    public SQLDialect getDialect() {
        return sqlDialect;
    }

    @Override
    public SqlInfo createTable(DataSource dataSource, DataStructNode model, String tableName, boolean execute) {
        TableStructure tableStructure = TableStructureUtil.convertModelToTableStructure(model, getDialect());
        // 将逻辑表名设置为真实的物理表名，比如存在分表时，逻辑表为 table, 物理表为: table_0, table_1 等
        tableStructure.setTableName(tableName);
        List<String> sqls = DdlGenerator.generateCreateTable(tableStructure.getTableName(), tableStructure, dslContext);
        if (execute && !CollectionUtils.isEmpty(sqls)) {
            log.info("create table:{}{}", System.lineSeparator(), sqls);
            sqls.forEach(sql -> execute(dataSource, sql));
        }
        return SqlInfo.builder().executeSql(sqls).tableName(model.getProps().getTableName()).build();
    }

    @Override
    public SqlInfo dropTable(DataSource dataSource, String tableName, boolean execute) {
        String sql = dslContext.dropTable(table(tableName)).getSQL();
        List<String> sqls = Lists.newArrayList(addSemicolon(sql));
        if (execute && !CollectionUtils.isEmpty(sqls)) {
            log.info("dropTable:{}{}", System.lineSeparator(), sqls);
            sqls.forEach(executeSql -> execute(dataSource, executeSql));
        }

        return SqlInfo.builder().executeSql(sqls).tableName(tableName).build();
    }

    @Override
    public SqlInfo removeIndex(DataSource dataSource, String tableName, String indexName, boolean execute) {
        String sql = dslContext.dropIndex(qualify(indexName)).on(qualify(tableName)).getSQL();
        if (execute) {
            log.info("remove index:{}{}", System.lineSeparator(), addSemicolon(sql));
            try {
                execute(dataSource, sql);
            } catch (BadSqlGrammarException e) {
                if (e.getCause().getMessage().contains("check that column/key exists")
                    || e.getCause().getMessage().contains("check that it exists")) {
                    // 如果删除索引失败的原因是索引不存在需要ignore，这种跟执行成功的效果是一样的，避免手动改索引导致的发布失败
                } else {
                    throw e;
                }
            }
        }
        return SqlInfo.builder().executeSql(Lists.newArrayList(addSemicolon(sql))).tableName(tableName).build();
    }

    @Override
    public SqlInfo createIndex(DataSource dataSource, String tableName, String indexName, IndexTypeEnum indexType, List<DataStructFieldNode> indexFields, boolean execute) {
        List<Field<Object>> collect = indexFields.stream().map(it -> field(it.getProps().getColumnName())).collect(Collectors.toList());

        String sql;
        if (indexType == IndexTypeEnum.UNIQUE) {
            sql = dslContext.createUniqueIndex(qualify(indexName)).on(qualify(tableName)).include(collect).getSQL(ParamType.INLINED);
        } else {
            sql = dslContext.createIndex(qualify(indexName)).on(qualify(tableName)).include(collect).getSQL(ParamType.INLINED);
        }

        // 针对String和Json类型字段需要增加长度限制
        for (DataStructFieldNode field : indexFields) {
            FieldType byCode = field.getProps().getFieldType();
            String indexSize = null;
            if (byCode == FieldType.TEXT) {
                // innodb 默认联合索引长度为 767, 常见联合索引参与的字段不超过 4 个，所以这里单个字段参与索引生成的长度限制为 191
                if (field.getProps().getLength() == null || field.getProps().getLength() >= 191) {
                    indexSize = INDEX_FIELD_MAX_SIZE;
                }
            }
            if (byCode == FieldType.MULTI_TEXT || byCode == FieldType.ATTACHMENT) {
                indexSize = INDEX_FIELD_MAX_SIZE;
            }
            if (indexSize != null) {
                // 限制联合索引生成规则，若联合索引内的单字段长度过长，生成索引时仅包含其前缀 191 个字符
                // eg: original: create unique index `udx_composite_key` on `xx`(`name`, `deleted`)，替换后: create unique index `udx_composite_key` on `xx`(`name`(191) ASC, `deleted`)
                sql = sql.replace("`" + field.getProps().getColumnName() + "`", "`" + field.getProps().getColumnName() + "`" + indexSize + " ASC");
            }
        }
        if (execute) {
            log.info("create index:{}{}", System.lineSeparator(), addSemicolon(sql));
            execute(dataSource, sql);
        }

        return SqlInfo.builder().executeSql(Lists.newArrayList(addSemicolon(sql))).tableName(tableName).build();
    }

    @Override
    public SqlInfo createRelationColumn(DataSource dataSource, String tableName, String relationFieldName, boolean execute) {
        DataType<Object> dataType = (DataType<Object>) DefaultDataType.getDataType(SQLDialect.MYSQL_5_7, "bigint").length(20);
        String shortKey = AliasUtil.shortKey(relationFieldName);
        String columnSql = dslContext.alterTable(table(tableName)).addColumn(field(shortKey), dataType).getSQL();
        String indexSql = dslContext.createIndex(qualify(shortKey)).on(qualify(tableName), qualify(shortKey)).getSQL();
        if (execute) {
            log.info("create relation column:{}{}", System.lineSeparator(), addSemicolon(columnSql));
            log.info("create relation column index:{}{}", System.lineSeparator(), addSemicolon(indexSql));
            execute(dataSource, columnSql);
            execute(dataSource, indexSql);
        }
        return SqlInfo.builder()
            .executeSql(Lists.newArrayList(addSemicolon(columnSql), addSemicolon(indexSql))).tableName(tableName)
            .build();
    }

    @Override
    public SqlInfo createColumn(DataSource dataSource, String tableName, DataStructFieldNode field, String afterColumn, boolean execute) {
        DataType nullable = getDataType(field);
        Integer firstPartOfLength = field.getProps().getLength() == null ? field.getProps().getIntLength() : field.getProps().getLength();
        if (field.getProps().getScale() != null) {
            firstPartOfLength += field.getProps().getScale();
        }

        // 如果是加密字段，长度设置为null
        if (field.getProps().isEncrypted()) {
            firstPartOfLength = null;
        }

        String sql = AddColumn.builder()
            .tableName(tableName)
            .columnName(field.getProps().getColumnName())
            .afterColumn(afterColumn)
            .isNull(field.getProps().isRequired())
            .type(nullable.getTypeName())
            .firstPartOfLength(firstPartOfLength)
            .secondPartOfLength(field.getProps().getScale())
            .defaultValue(field.getProps().fetchDefaultValue())
            .comment(field.getProps().getComment())
            .dialectType(SupportDialectTypeEnum.MYSQL)
            .build().getSql();
        if (execute) {
            log.info("create column:{}{}", System.lineSeparator(), addSemicolon(sql));
            execute(dataSource, sql);
        }
        return SqlInfo.builder().executeSql(Lists.newArrayList(addSemicolon(sql))).tableName(tableName).build();
    }

    /**
     * 更改数据类型
     *
     * @param dataSource
     * @param tableName
     * @param field
     * @param afterColumn
     * @param oldField
     * @param execute
     * @return
     */
    @Override
    public SqlInfo updateColumn(DataSource dataSource, String tableName, DataStructFieldNode field, String afterColumn, DataStructFieldNode oldField, boolean execute) {
        DataType dataType = getDataType(field);
        Integer firstPartOfLength = field.getProps().getLength() == null ? field.getProps().getIntLength() : field.getProps().getLength();
        if (field.getProps().getScale() != null) {
            firstPartOfLength += field.getProps().getScale();
        }

        // 如果是加密字段，长度设置为null
        if (field.getProps().isEncrypted()) {
            firstPartOfLength = null;
        }

        String sql = AlterColumnType.builder()
            .tableName(tableName)
            .columnName(oldField.getProps().getColumnName())
            .newColumnName(field.getProps().getColumnName())
            .afterColumn(afterColumn)
            .isNull(field.getProps().isRequired())
            .type(dataType.getTypeName())
            .firstPartOfLength(firstPartOfLength)
            .secondPartOfLength(field.getProps().getScale())
            .defaultValue(field.getProps().fetchDefaultValue())
            .comment(field.getProps().getComment())
            .build().getSql();
        if (execute) {
            log.info("update column:{}{}", System.lineSeparator(), addSemicolon(sql));
            execute(dataSource, sql);
        }
        return SqlInfo.builder().executeSql(Lists.newArrayList(addSemicolon(sql))).tableName(tableName).build();
    }

    private DataType getDataType(DataStructFieldNode field) {
        FieldType type = field.getProps().getFieldType();
        DataType dataType = type.getDataType(SQLDialect.MYSQL_5_7);
        if (type == FieldType.NUMBER && (field.getProps().getScale() == null || field.getProps().getScale() == 0)) {
            dataType = DefaultDataType.getDataType(SQLDialect.MYSQL_5_7, "bigint").length(20);
        }

        if (field.getProps().isEncrypted()) {
            dataType = FieldType.MULTI_TEXT.getDataType(SQLDialect.MYSQL_5_7);
        }

        return dataType;
    }

    @Override
    public SqlInfo renameColumn(DataSource dataSource, String tableName, String oldColumnName, String newColumnName, DataStructFieldNode field, boolean execute) {
        FieldType type = field.getProps().getFieldType();

        String sql = dslContext.alterTable(qualify(tableName)).alterColumn(oldColumnName).set(type.getDataType(SQLDialect.MYSQL_5_7)).getSQL();
        String finalSql = replaceLast(sql, oldColumnName, newColumnName);
        if (execute) {
            log.info("rename column:{}{}", System.lineSeparator(), addSemicolon(finalSql));
            execute(dataSource, finalSql);
        }
        return SqlInfo.builder().executeSql(Lists.newArrayList(addSemicolon(finalSql))).tableName(tableName).build();
    }

    @Override
    public SqlInfo createTableByDdl(DataSource dataSource, String ddl, boolean execute) {
        if (execute) {
            execute(dataSource, ddl);
        }
        return SqlInfo.builder().executeSql(Lists.newArrayList(ddl)).build();
    }

    @Override
    public SqlInfo deleteColumn(DataSource dataSource, String tableName, String columnName, boolean execute) {
        String sql = dslContext.alterTable(qualify(tableName)).dropColumn(qualify(columnName)).getSQL();
        if (execute) {
            log.info("delete column:{}{}", System.lineSeparator(), addSemicolon(sql));
            execute(dataSource, sql);
        }
        return SqlInfo.builder().executeSql(Lists.newArrayList(addSemicolon(sql))).tableName(tableName).build();
    }

    /**
     * 使用给定的数据源执行 SQL
     *
     * @param dataSource 数据源
     * @param sql 待执行 SQL
     */
    private void execute(DataSource dataSource, String sql) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);

        log.info("start to execute DDL, sql: {}", sql);
        long start = System.currentTimeMillis();
        long cost;

        try {
            jdbcTemplate.execute(sql);

            cost = System.currentTimeMillis() - start;
            log.info("finished to execute DDL, cost: {}ms, sql: {}", cost, sql);
        } catch (Exception e) {
            cost = System.currentTimeMillis() - start;
            log.error("failed to execute DDL, cost: {}ms, sql: {}, ", cost, sql, e);
            throw e;
        }
    }

    private static String replaceLast(String origin, String regex, String replacement) {

        int index = origin.lastIndexOf(regex);

        return origin.substring(0, index) + replacement + origin.substring(index + regex.length());

    }
}
