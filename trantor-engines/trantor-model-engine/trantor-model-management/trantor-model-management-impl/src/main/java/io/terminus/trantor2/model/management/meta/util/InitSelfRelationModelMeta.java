package io.terminus.trantor2.model.management.meta.util;

import io.terminus.trantor2.model.management.meta.cache.DataStructMetaCache;
import io.terminus.trantor2.model.management.meta.domain.DataStructConfig;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.domain.RelationMeta;
import io.terminus.trantor2.model.management.meta.enums.ModelRelationTypeEnum;
import io.terminus.trantor2.model.management.meta.repository.DataStructNodeRepo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/21
 */
@Component
@RequiredArgsConstructor
public class InitSelfRelationModelMeta {

    private final DataStructNodeRepo dataStructNodeRepo;

    private final DataStructMetaCache cache;

    public void execute() {
        List<DataStructNode> nodes = dataStructNodeRepo.findAll();
        for (DataStructNode node : nodes) {
            boolean flag = false;
            for (DataStructFieldNode child : node.getChildren()) {
                if (child.getProps().getRelationMeta() == null) {
                    continue;
                }
                RelationMeta relationMeta = child.getProps().getRelationMeta();
                if (node.getAlias().equals(relationMeta.getRelationModelAlias()) && relationMeta.getRelationType() == ModelRelationTypeEnum.LINK) {
                    flag = true;
                    DataStructConfig config = node.getProps().getConfig();
                    if (config == null) {
                        config = new DataStructConfig();
                    }
                    config.setSelf(true);
                    config.setSelfRelationFieldAlias(child.getAlias());
                    node.getProps().setConfig(config);
                }
            }
            try {
                if (flag) {
                    dataStructNodeRepo.save(node);
                }
            } catch (Exception e) {
                // 元信息存在并发修改时候会抛异常，暂时规避
            }
        }
        cache.batchRemoveDataStructCache();
    }


}
