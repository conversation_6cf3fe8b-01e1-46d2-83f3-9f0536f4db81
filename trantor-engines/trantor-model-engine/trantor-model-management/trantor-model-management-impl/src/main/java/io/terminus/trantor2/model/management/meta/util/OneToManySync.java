package io.terminus.trantor2.model.management.meta.util;

import io.terminus.trantor2.model.management.meta.cache.DataStructMetaCache;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.domain.RelationMeta;
import io.terminus.trantor2.model.management.meta.enums.ModelRelationTypeEnum;
import io.terminus.trantor2.model.management.meta.repository.DataStructNodeRepo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/31
 */
@Component
@RequiredArgsConstructor
public class OneToManySync {

    private final DataStructNodeRepo dataStructNodeRepo;

    private final DataStructMetaCache cache;

    public void execute() {
        List<DataStructNode> nodes = dataStructNodeRepo.findAll();
        for (DataStructNode node : nodes) {
            boolean refresh = false;
            for (DataStructFieldNode child : node.getChildren()) {
                child.setAlias(AliasUtil.fieldKeyToAlias(child.getKey()));
                RelationMeta relationMeta = child.getProps().getRelationMeta();
                if (relationMeta == null || relationMeta.getRelationType() != ModelRelationTypeEnum.PARENT_CHILD) {
                    continue;
                }
                refresh = true;
                relationMeta.setSync(true);
            }
            try {
                if (refresh) {
                    dataStructNodeRepo.save(node);
                }
            } catch (Exception e) {
                // 元信息存在并发修改时候会抛异常，暂时规避
            }
        }
        cache.batchRemoveDataStructCache();
    }

}
