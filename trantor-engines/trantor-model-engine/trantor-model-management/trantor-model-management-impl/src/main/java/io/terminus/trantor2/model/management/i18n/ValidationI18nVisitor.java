package io.terminus.trantor2.model.management.i18n;

import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.model.management.meta.domain.validation.ValidationNode;
import io.terminus.trantor2.model.meta.repository.ValidationQueryRepo;
import io.terminus.trantor2.module.i18n.I18nVisitor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import java.util.List;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static io.terminus.trantor2.module.constants.I18nConst.VALIDATION_PREFIX;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ValidationI18nVisitor implements I18nVisitor {
    private final ValidationQueryRepo validationRepo;

    @Override
    public MetaType getType() {
        return MetaType.Validation;
    }

    @Override
    public Set<String> visit(Cond cond, @Nullable Predicate<String> predicate) {
        List<ValidationNode> validations = validationRepo.findAll(cond, ResourceContext.ctxFromThreadLocal())
                .stream()
                .filter(it -> predicate == null || predicate.test(KeyUtil.moduleKey(it.getKey())))
                .collect(Collectors.toList());
        return validations.stream()
                .filter(it -> it != null && it.getProps() != null && it.getProps().getFieldValidations() != null)
                .flatMap(it -> it.getProps().getFieldValidations().stream()
                        .filter(v -> v != null && v.getMessage() != null)
                        .map(v -> VALIDATION_PREFIX + v.getMessage()))
                .collect(Collectors.toSet());
    }
}
