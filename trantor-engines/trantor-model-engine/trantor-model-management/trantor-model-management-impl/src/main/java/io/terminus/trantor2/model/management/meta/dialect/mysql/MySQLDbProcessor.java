package io.terminus.trantor2.model.management.meta.dialect.mysql;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mysql.cj.MysqlType;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.datasource.model.DataSourceConfigDTO;
import io.terminus.trantor2.model.common.exception.ModelMetaException;
import io.terminus.trantor2.model.management.meta.dialect.DbProcessor;
import io.terminus.trantor2.model.management.meta.dialect.entity.ColumnStructure;
import io.terminus.trantor2.model.management.meta.dialect.entity.IndexStructure;
import io.terminus.trantor2.model.management.model.TableInfo;
import io.terminus.trantor2.model.management.meta.dialect.entity.TableStructure;
import io.terminus.trantor2.model.management.meta.enums.IndexTypeEnum;
import io.terminus.trantor2.datasource.model.SupportDialectTypeEnum;
import io.terminus.trantor2.service.dsl.properties.Pageable;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static io.terminus.trantor2.model.management.meta.dialect.MultiDbProcessorTemplate.getJdbcTypeSupportDisplayTypeMap;

/**
 * mysql 表结构解析
 */
@Data
@Slf4j
@Service
public class MySQLDbProcessor implements DbProcessor {
    /**
     * mysql驱动
     */
    private static final String DRIVER_CLASS_NAME_MYSQL = "com.mysql.cj.jdbc.Driver";

    /**
     * sql入参标准
     */
    private static final String MODEL_FIELD_REGEX = "^[\\da-zA-Z_]{0,255}$";

    /**
     * test query
     */
    protected static final String CONNECTION_TEST_QUERY_MYSQL = "SELECT 1";

    /**
     * 字段名
     */
    public static final String COLUMN_NAME = "COLUMN_NAME";
    /**
     * 表注释信息
     */
    public static final String TABLE_COMMENT = "TABLE_COMMENT";

    /**
     * 表名
     */
    public static final String TABLE_NAME = "TABLE_NAME";

    /**
     * 库级别表注释查询
     */
    private static final String QUERY_MYSQL_WHOLE_TABLE_COMMENTS = "SELECT TABLE_NAME, TABLE_COMMENT,AUTO_INCREMENT "
        + "FROM INFORMATION_SCHEMA.TABLES "
        + "WHERE `TABLE_SCHEMA` = '%s' ORDER BY TABLE_NAME ASC";

    /**
     * 表级别表注释查询
     */
    private static final String QUERY_MYSQL_TABLE_COMMENTS = "SELECT TABLE_NAME, TABLE_COMMENT,AUTO_INCREMENT "
        + "FROM INFORMATION_SCHEMA.TABLES "
        + "WHERE `TABLE_SCHEMA` = '%s' AND `TABLE_NAME`= '%s'";

    /**
     * 查询表类型
     */
    private static final String QUERY_MYSQL_TABLE_TYPE = "SELECT TABLE_TYPE " +
        "FROM INFORMATION_SCHEMA.TABLES WHERE `TABLE_SCHEMA` = '%s' AND `TABLE_NAME`= '%s'";

    /**
     * 字段信息查询
     */
    private static final String QUERY_MYSQL_TABLE_COLUMNS = "SELECT COLUMN_NAME,COLUMN_DEFAULT,"
        + "IS_NULLABLE,DATA_TYPE,CHARACTER_MAXIMUM_LENGTH,"
        + "NUMERIC_PRECISION,NUMERIC_SCALE,COLUMN_TYPE,"
        + "COLUMN_COMMENT "
        + "FROM INFORMATION_SCHEMA.COLUMNS "
        + "WHERE TABLE_SCHEMA='%s' AND TABLE_NAME='%s' ORDER BY ORDINAL_POSITION ASC";

    /**
     * 校验主键字段
     */
    private static final String QUERY_MYSQL_BY_FIELD = "SELECT COLUMN_KEY,EXTRA FROM INFORMATION_SCHEMA.COLUMNS"
        + " WHERE TABLE_SCHEMA='%s' AND TABLE_NAME='%s' AND COLUMN_NAME='%s'";

    /**
     * 校验表是否存在自增主键
     */
    private static final String CHECK_MYSQL_TABLE_AUTO_INCREMENT = "SELECT TABLE_NAME FROM"
        + " INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA='%s' "
        + " AND COLUMN_KEY='PRI' AND EXTRA='auto_increment'";

    /**
     * mysql索引查询
     */
    private static final String QUERY_MYSQL_TABLE_INDEXES = "SHOW INDEX FROM `%s`.`%s`";

    /**
     * mysql约束类索引查询
     */
    private static final String QUERY_MYSQL_TABLE_FOREIGN_INDEXES = "SELECT CONSTRAINT_NAME,COLUMN_NAME "
        + "FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE "
        + "WHERE CONSTRAINT_SCHEMA='%s' AND TABLE_NAME='%s'";

    /**
     * 时区
     */
    private static String dbZoneId;
    /**
     * System.getenv("DB_ZONE_ID")
     */
    public static final String ENV_DB_ZONE_ID = System.getenv("DB_ZONE_ID");
    /**
     * System.getenv("DB_ZONE_OFFSET")
     */
    public static final String ENV_DB_ZONE_OFFSET = System.getenv("DB_ZONE_OFFSET");
    /**
     * ZoneId.systemDefault().getId()
     */
    public static final String SYSTEM_DEFAULT_ZONE_ID = ZoneId.systemDefault().getId();
    /**
     * System.getenv("MYSQL_USE_SSL")
     */
    public static final String MYSQL_USE_SSL = System.getenv("MYSQL_USE_SSL");
    /**
     * mysql连接串
     * 2022-09-13设置maxReconnects=1，当连接不可用时，重试建立连接的次数，mysql默认为3，initialTimeout为2，即两秒重试一次，重试三次
     */
    public static final String JDBC_URL_PATTERN = "*************************************************************"
        + "&autoReconnect=true&&maxReconnects=1&tcpKeepAlive=true&zeroDateTimeBehavior=convertToNull&allowMultiQueries=true";

    /**
     * 数据库类型
     *
     * @return 数据库类型
     */
    @Override
    public SupportDialectTypeEnum getDbType() {
        return SupportDialectTypeEnum.MYSQL;
    }

    /**
     * 表查询
     *
     * @param jdbcTemplate jdbcTemplate
     * @param dbName       数据库名
     * @return map.key表名，map.value表结构
     */
    @Override
    public Map<String, TableStructure> getTables(JdbcTemplate jdbcTemplate, String dbName) {
        Map<String, TableStructure> tableInfoMap = Maps.newLinkedHashMap();
        try {
            String sql = String.format(QUERY_MYSQL_WHOLE_TABLE_COMMENTS, dbName);
            List<Map<String, Object>> dbTableMaps = jdbcTemplate.queryForList(sql);
            if (CollectionUtils.isEmpty(dbTableMaps)) {
                return tableInfoMap;
            }
            for (Map<String, Object> dbTableMap : dbTableMaps) {
                String tableName = null == dbTableMap.get("TABLE_NAME") ? ""
                    : String.valueOf(dbTableMap.get("TABLE_NAME"));
                String tableComment = null == dbTableMap.get(TABLE_COMMENT) ? ""
                    : String.valueOf(dbTableMap.get(TABLE_COMMENT));

                TableStructure tableInfo = TableStructure.builder()
                    .tableName(tableName)
                    .comment(tableComment)
                    .build();
                tableInfoMap.put(tableName, tableInfo);
            }

            // 检查表是否存在自增主键
            sql = String.format(CHECK_MYSQL_TABLE_AUTO_INCREMENT, dbName);
            List<Map<String, Object>> autoIncrementTables = jdbcTemplate.queryForList(sql);
            if (CollectionUtils.isEmpty(autoIncrementTables)) {
                return tableInfoMap;
            }
            autoIncrementTables.forEach(autoIncrementTableData -> {
                String autoIncrementTableName = String.valueOf(autoIncrementTableData.get("TABLE_NAME"));
                TableStructure tableStructure = tableInfoMap.get(autoIncrementTableName);
                if (null != tableStructure) {
                    tableStructure.setAutoIncrement(true);
                }
            });
        } catch (Exception e) {
            log.error("MySQLDbProcessor.getAllTables error....error:{},dbName:{}",
                e.getMessage(), dbName, e);
        }

        return tableInfoMap;
    }

    /**
     * 表查询
     *
     * @param jdbcTemplate jdbcTemplate
     * @param dbName       数据库名
     * @param fuzzyTableName    模糊匹配表名
     * @param pageable     分页
     * @return map.key表名，map.value表结构
     */
    @Override
    public List<TableInfo> getTables(JdbcTemplate jdbcTemplate, String dbName, String fuzzyTableName, Pageable pageable) {
        if (StringUtils.isNotEmpty(fuzzyTableName)) {
            Pattern pattern = Pattern.compile(MODEL_FIELD_REGEX);
            Matcher matcher= pattern.matcher(fuzzyTableName);
            if (!matcher.matches()) {
                throw new ModelMetaException(ErrorType.META_INVALID_REQUEST,new Object[]{"入参不规范,tableName:  " + fuzzyTableName});
            }
        }
        if (null == pageable) {
            pageable = new Pageable(1,5000);
        }
        List<TableInfo> tableInfos = new ArrayList<>();
        String sql = "SELECT `TABLE_NAME`, `TABLE_COMMENT` " +
            "FROM INFORMATION_SCHEMA.TABLES " +
            "WHERE `TABLE_SCHEMA`='" + dbName +"'";
        if (StringUtils.isNotEmpty(fuzzyTableName)) {
            sql += " AND `TABLE_NAME` LIKE '%" + fuzzyTableName + "%'";
        }
        sql += " LIMIT " + pageable.offset() + "," + pageable.getPageSize();

        List<Map<String, Object>> tables = jdbcTemplate.queryForList(sql);
        if (null == tables) {
            return tableInfos;
        }
        for (Map<String, Object> table : tables) {
            if (!table.containsKey(TABLE_NAME)) {
                continue;
            }
            String tableName = String.valueOf(table.get(TABLE_NAME));
            String tableComment = String.valueOf(table.get(TABLE_COMMENT)).isEmpty()
                ? tableName
                : String.valueOf(table.get(TABLE_COMMENT));
            sql = "SELECT `COLUMN_NAME` FROM INFORMATION_SCHEMA.COLUMNS " +
                "WHERE `TABLE_SCHEMA`='" + dbName +
                "' AND `TABLE_NAME`='" + tableName +"'";
            List<Map<String, Object>> columns = jdbcTemplate.queryForList(sql);
            boolean originOrgId = false;
            if (null == columns) {
                continue;
            }

            boolean tenantIdEnabled = false;
            for (Map<String, Object> column : columns) {
                if (column.containsValue("origin_org_id")) {
                    originOrgId = true;
                } else if (column.containsValue("tenant_id")) {
                    tenantIdEnabled = true;
                }
            }
            TableInfo tableInfo = new TableInfo();
            tableInfo.setTableName(tableName);
            tableInfo.setComment(tableComment);
            tableInfo.setOriginOrgId(originOrgId);
            tableInfo.setTenantIdEnabled(tenantIdEnabled);
            tableInfos.add(tableInfo);
        }
        return tableInfos;
    }

    public Long getPageTotal(JdbcTemplate jdbcTemplate, String dbName, String fuzzyTableName, Pageable pageable ) {
        if (StringUtils.isNotEmpty(fuzzyTableName)) {
            Pattern pattern = Pattern.compile(MODEL_FIELD_REGEX);
            Matcher matcher= pattern.matcher(fuzzyTableName);
            if (!matcher.matches()) {
                throw new ModelMetaException(ErrorType.META_INVALID_REQUEST, "入参不规范,tableName:  " + fuzzyTableName, new Object[]{ "入参不规范,tableName:" + fuzzyTableName });
            }
        }
        String sql = "SELECT COUNT(*) " +
            "FROM INFORMATION_SCHEMA.TABLES " +
            "WHERE `TABLE_SCHEMA`='" + dbName +"'";
        if (StringUtils.isNotEmpty(fuzzyTableName)) {
            sql += " AND `TABLE_NAME` LIKE '%" + fuzzyTableName + "%'";
        }
        Long total = jdbcTemplate.queryForObject(sql, Long.class);
        return total;
    }
    /**
     * 表结构解析
     *
     * @param jdbcTemplate jdbcTemplate
     * @param dbName       数据库名
     * @param tableName    表名
     * @return 物理表结构
     */
    @Override
    public TableStructure getTableStructure(JdbcTemplate jdbcTemplate, String dbName, String tableName) {
        // 表字段信息
        Map<String, ColumnStructure> columns = getColumns(jdbcTemplate, dbName, tableName);
        if (CollectionUtils.isEmpty(columns)) {
            return null;
        }
        TableStructure tableStructure = new TableStructure();
        tableStructure.setColumns(columns);

        // 表名
        tableStructure.setTableName(tableName);

        // 注释信息
        String comment = getTableComment(jdbcTemplate, dbName, tableName);
        tableStructure.setComment(comment);

        // 索引信息
        Map<String, IndexStructure> indexes = getIndexes(jdbcTemplate, dbName, tableName);
        tableStructure.setIndexes(indexes);

        return tableStructure;
    }

    private static Map<String, IndexStructure> getIndexes(JdbcTemplate jdbcTemplate, String dbName, String tableName) {
        Map<String, IndexStructure> indexes = Maps.newLinkedHashMap();
        try {
            String sql = String.format(QUERY_MYSQL_TABLE_INDEXES, dbName, tableName);
            List<Map<String, Object>> dbTableMaps = jdbcTemplate.queryForList(sql);
            if (CollectionUtils.isEmpty(dbTableMaps)) {
                return indexes;
            }

            // 字段解析
            dbTableMaps.forEach(map -> {
                String indexName = String.valueOf(map.get("KEY_NAME"));
                indexes.putIfAbsent(indexName, IndexStructure.builder()
                    .name(indexName)
                    .columns(Lists.newArrayList())
                    .build());
                IndexStructure indexStructure = indexes.get(indexName);
                // 索引类型
                String nonUnique = String.valueOf(map.get("NON_UNIQUE"));
                if ("PRIMARY".equals(indexName)) {
                    indexStructure.setType(IndexTypeEnum.PRIMARY);
                } else if ("0".equals(nonUnique)) {
                    indexStructure.setType(IndexTypeEnum.UNIQUE);
                } else {
                    indexStructure.setType(IndexTypeEnum.COMMON);
                }

                String columnName = String.valueOf(map.get(COLUMN_NAME));
                indexStructure.addColumn(columnName);

                String comment = null == map.get("COMMENT") ? "" : String.valueOf(map.get("COMMENT"));
                indexStructure.setComment(comment);
            });

            // 外键约束索引查询
            sql = String.format(QUERY_MYSQL_TABLE_FOREIGN_INDEXES, dbName, tableName);
            List<Map<String, Object>> foreignDataList = jdbcTemplate.queryForList(sql);
            if (CollectionUtils.isEmpty(foreignDataList)) {
                return indexes;
            }

            // 字段解析
            foreignDataList.forEach(map -> {
                String indexName = String.valueOf(map.get("CONSTRAINT_NAME"));
                indexes.putIfAbsent(indexName, IndexStructure.builder()
                    .name(indexName)
                    .columns(Lists.newArrayList())
                    .build());
                IndexStructure indexStructure = indexes.get(indexName);
                // 当前表关联字段
                String columnName = String.valueOf(map.get(COLUMN_NAME));
                indexStructure.addColumn(columnName);
            });

        } catch (Exception e) {
            log.info("MySQLDbProcessor.getIndexes error....error:{},dbName:{}",
                e.getMessage(), dbName, e);
        }
        return indexes;
    }

    private static Map<String, ColumnStructure> getColumns(JdbcTemplate jdbcTemplate, String dbName, String tableName) {
        Map<String, ColumnStructure> columns = Maps.newLinkedHashMap();
        try {
            String sql = String.format(QUERY_MYSQL_TABLE_COLUMNS, dbName, tableName);
            List<Map<String, Object>> dbTableMaps = jdbcTemplate.queryForList(sql);
            if (CollectionUtils.isEmpty(dbTableMaps)) {
                return columns;
            }

            // 字段解析
            dbTableMaps.forEach(map -> {
                ColumnStructure columnStructure = new ColumnStructure();

                String columnName = String.valueOf(map.get(COLUMN_NAME));
                columnStructure.setColumnName(columnName);
                Object defaultValue = map.get("COLUMN_DEFAULT");
                columnStructure.setDefaultValue(defaultValue);
                boolean nonNull = Objects.equals("NO", map.get("IS_NULLABLE"));
                columnStructure.setNonNull(nonNull);
                String comment = String.valueOf(map.get("COLUMN_COMMENT"));
                columnStructure.setComment(comment);

                String columnType = String.valueOf(map.get("COLUMN_TYPE"));
                formColumnTypeAndLength(columnStructure, columnType);
                columns.put(columnName.toLowerCase(), columnStructure);
            });

        } catch (Exception e) {
            log.error("MySQLDbProcessor.getColumns error....error:{},dbName:{}",
                e.getMessage(), dbName, e);
        }
        return columns;
    }

    private static void formColumnTypeAndLength(ColumnStructure columnStructure, String columnType) {
        String[] strings = splitByStartAndEnd(columnType, "(", ")");
        columnStructure.setType(strings[0]);
        if (strings.length == 2 && !StringUtils.isEmpty(strings[1])) {
            String[] lengths = strings[1].split(",");
            if (lengths.length == 1) {
                columnStructure.setOneLength(Integer.parseInt(lengths[0]));
            } else if (lengths.length == 2) {
                columnStructure.setOneLength(Integer.parseInt(lengths[0]));
                columnStructure.setTwoLength(Integer.parseInt(lengths[1]));
            }
        } else if(columnType.equals("bigint")) {
            // 兼容 MYSQL 8.0, MYSQL 5.7 整型字段类型是 bigint(20), MYSQL 8.0为: bigint，在 mysql 字段元数据与模型字段元数据比对时防止 id 的 auto_increment 属性丢失
            columnStructure.setOneLength(20);
        }
    }

    private static String getTableComment(JdbcTemplate jdbcTemplate, String dbName, String tableName) {
        try {
            String sql = String.format(QUERY_MYSQL_TABLE_COMMENTS, dbName, tableName);
            List<Map<String, Object>> dbTableMaps = jdbcTemplate.queryForList(sql);
            if (CollectionUtils.isEmpty(dbTableMaps)) {
                return "";
            }
            Object comment = dbTableMaps.get(0).get(TABLE_COMMENT);

            return null == comment ? "" : String.valueOf(comment);
        } catch (Exception e) {
            log.error("MySQLDbProcessor.getTableComment error....error:{},dbName:{}",
                e.getMessage(), dbName, e);
        }
        return "";
    }

    /**
     * 根据指定开始和结束分隔符切分字符串，Hash(author) = [Hash,author]
     *
     * @param value 待分割数据
     * @param start 起始
     * @param end   结束
     * @return 分割后的数组
     */
    public static String[] splitByStartAndEnd(String value, String start, String end) {
        String[] result = new String[2];
        if (StringUtils.isEmpty(value)) {
            return result;
        }
        int startIndex = value.indexOf(start);
        int endIndex = value.indexOf(end, startIndex + 1);
        int lastEndIndex = value.lastIndexOf(end);
        if (lastEndIndex != endIndex) {
            // 说明存在重复的分隔符，则取最后匹配到的作为结束符
            // Hash(author(1)) = [Hash,author(1)]
            endIndex = lastEndIndex;
        }
        if (startIndex == -1 || endIndex == -1) {
            result[0] = value;
            return result;
        }
        result[0] = value.substring(0, startIndex);
        result[1] = value.substring(startIndex + 1, endIndex);
        return result;
    }

    /**
     * 校验字段是否为主键字段，且为自增
     *
     * @param jdbcTemplate
     * @param dbName
     * @param tableName
     * @param fieldName
     * @return true：合法；false：不合法
     */
    @Override
    public Boolean checkPrimaryFieldLegal(JdbcTemplate jdbcTemplate, String dbName, String tableName,
                                          String fieldName) {
        try {
            String sql = String.format(QUERY_MYSQL_BY_FIELD, dbName, tableName, fieldName);
            List<Map<String, Object>> dbTableMaps = jdbcTemplate.queryForList(sql);
            if (CollectionUtils.isEmpty(dbTableMaps)) {
                return false;
            }

            // 字段解析
            Map<String, Object> dataMap = dbTableMaps.get(0);
            String columnKey = null == dataMap.get("COLUMN_KEY") ? "" : String.valueOf(dataMap.get("COLUMN_KEY"));
            String extra = null == dataMap.get("EXTRA") ? "" : String.valueOf(dataMap.get("EXTRA"));
            if ("PRI".equalsIgnoreCase(columnKey) && extra.contains("auto_increment")) {
                return true;
            }
            String typeSql = String.format(QUERY_MYSQL_TABLE_TYPE, dbName, tableName);
            List<Map<String, Object>> types = jdbcTemplate.queryForList(typeSql);
            if (!CollectionUtils.isEmpty(dbTableMaps)) {
                Object tableType = types.get(0).get("TABLE_TYPE");
                if ("VIEW".equals(tableType)) {
                    // 视图忽略自增检查
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("MySQLDbProcessor.checkPrimaryFieldLegal error....error:{},dbName:{},tableName:{},fieldName:{}",
                e.getMessage(), dbName, tableName, fieldName, e);
        }
        return false;
    }

    /**
     * 根据前端展示类型，计算java字段类型
     *
     * @param displayFieldType
     * @param dbFieldTypeUpperCase
     * @param fieldAlias
     * @return java字段类型
     */
    @Override
    public Class<?> calcFieldJavaClass(String displayFieldType, String dbFieldTypeUpperCase, String fieldAlias) {
//        ModelFieldConfigTypeEnum typeEnum = ModelFieldConfigTypeEnum.findModelFieldConfigByType(displayFieldType);
//        if (null == typeEnum) {
//            String errMsg = String.format(MODEL_FIELD_JAVA_CLASS_CALC_ERROR.getMessage(),
//                fieldAlias, "不支持的前端展示类型配置" + displayFieldType);
//            throw new ModelMetaException(MODEL_FIELD_JAVA_CLASS_CALC_ERROR, errMsg);
//        }
//
//        if (!displayFieldType.equals(BuiltInFieldTypes.NUMBER)) {
//            return typeEnum.getFieldJavaClass();
//        }
//
//        // number前端类型特殊处理
//        MysqlFieldTypeEnum dbTypeEnum = MysqlFieldTypeEnum.findTypeEnum(dbFieldTypeUpperCase);
//        switch (dbTypeEnum) {
//            case TINYINT:
//            case INT:
//                return Integer.class;
//            case BIGINT:
//                return Long.class;
//            case DECIMAL:
//            case DOUBLE:
//            case FLOAT:
//                return BigDecimal.class;
//            default:
//                String errMsg = String.format(MODEL_FIELD_JAVA_CLASS_CALC_ERROR.getMessage(),
//                    fieldAlias, "前端展示类型为Number时，发现了不支持的数据库字段类型"
//                        + dbFieldTypeUpperCase);
//                throw new ModelMetaException(MODEL_FIELD_JAVA_CLASS_CALC_ERROR, errMsg);
//
//        }
        return null;
    }

    /**
     * 根据数据库中字段类型获取支持的前端展示类型
     *
     * @param columnType
     * @return 支持的前端展示类型列表
     */
    @Override
    public List<String> getSupportDisplayTypesByDbFieldType(String columnType) {
        List<String> supportFieldTypes = Lists.newArrayList();
        MysqlType mysqlType = MysqlType.getByName(columnType);

        getJdbcTypeSupportDisplayTypeMap().forEach((key, displayTypes) -> {
            if (mysqlType.getJdbcType() == key.getVendorTypeNumber()) {
                supportFieldTypes.addAll(displayTypes);
            }
        });
        return supportFieldTypes;
    }

    /**
     * 数据库驱动获取
     *
     * @return 数据库驱动
     */
    @Override
    public String getDriverClassName() {
        return DRIVER_CLASS_NAME_MYSQL;
    }

    /**
     * 数据连接测试sql
     *
     * @return sql
     */
    @Override
    public String getConnectionTestQuery() {
        return CONNECTION_TEST_QUERY_MYSQL;
    }

    /**
     * 数据库用户名
     *
     * @param schema
     * @param dbUsername
     * @param drdsEnable
     * @return 用户名
     */
    @Override
    public String getCreateDataSourceUserName(String schema, String dbUsername, boolean drdsEnable) {
        return dbUsername;
    }

    /**
     * jdbc url获取
     *
     * @param dbSchema
     * @param dataSourceConfig
     * @return jdbc url
     */
    @Override
    public String getJdbcUrl(String dbSchema, DataSourceConfigDTO dataSourceConfig) {
        String dbZoneIdNew = initDbZoneIdOnlyOnce();

        if (dbSchema == null) {
            dbSchema = "";
        }
        String jdbcUrl = String.format(JDBC_URL_PATTERN, dataSourceConfig.getDbHost(),
            dataSourceConfig.getDbPort(), dbSchema, dbZoneIdNew);
        if (Boolean.parseBoolean(MYSQL_USE_SSL)) {
            // 开启MySQL的SSL：需要在erda上添加两个环境变量，1）MYSQL_USE_SSL，值为true；2）ApsaraDB-CA-Chain.jks，文件类型，值为上传的证书文件（由erda或数据库厂商提供）
            jdbcUrl = jdbcUrl + "&useSSL=true&requireSSL=true&verifyServerCertificate=true&"
                + "clientCertificateKeyStoreUrl=file:/init-data/ApsaraDB-CA-Chain.jks&clientCertificateKeyStorePassword"
                + "=apsaradb&trustCertificateKeyStoreUrl=file:/init-data/ApsaraDB-CA-Chain.jks&"
                + "trustCertificateKeyStorePassword=apsaradb";
        } else {
            jdbcUrl = jdbcUrl + "&useSSL=false";
        }
        log.info("jdbcUrl={}", jdbcUrl);
        return jdbcUrl;
    }

    private static String initDbZoneIdOnlyOnce() {
        if (dbZoneId != null) {
            return dbZoneId;
        }

        if (StringUtils.isNotBlank(ENV_DB_ZONE_ID)) {
            dbZoneId = ENV_DB_ZONE_ID;
        } else if (StringUtils.isNotBlank(ENV_DB_ZONE_OFFSET)) {
            ZoneOffset zoneOffset = ZoneOffset.of(ENV_DB_ZONE_OFFSET);
            dbZoneId = zoneOffset.getId();
            if (dbZoneId.startsWith("+")) {
                dbZoneId = "GMT%2B" + dbZoneId.substring(1);
            } else if (dbZoneId.startsWith("-")) {
                dbZoneId = "GMT%2D" + dbZoneId.substring(1);
            }
        } else {
            // 兼容两种格式的SYSTEM_DEFAULT_ZONE_ID：Asia/Shanghai、GMT+08:00
            dbZoneId = SYSTEM_DEFAULT_ZONE_ID
                .replace("\\+", "%2B")
                .replace("-", "%2D");
        }
        log.info("dbZoneId={}, DB_ZONE_ID={}, DB_ZONE_OFFSET={}, SYSTEM_DEFAULT_ZONE_ID={}",
            dbZoneId, ENV_DB_ZONE_ID, ENV_DB_ZONE_OFFSET, SYSTEM_DEFAULT_ZONE_ID);

        return dbZoneId;
    }
}
