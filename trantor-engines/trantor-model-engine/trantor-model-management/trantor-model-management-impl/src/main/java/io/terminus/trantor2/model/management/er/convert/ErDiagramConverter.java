package io.terminus.trantor2.model.management.er.convert;


import io.terminus.trantor2.model.management.er.vo.ErDiagramField;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.domain.RelationMeta;
import io.terminus.trantor2.model.management.er.vo.ErDiagramFieldTypeMeta;
import io.terminus.trantor2.model.management.er.vo.ErDiagramNode;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel="spring", uses = DataStructFieldPropertiesMapper.class)
public interface ErDiagramConverter {

    @Mapping(target = "module", ignore = true)
    @Mapping(target = "fields", source = "children")
    @Mapping(target = "name", source = "alias")
    @Mapping(target = "label", source = "desc")
    @Mapping(target = "type", ignore = true)
    ErDiagramNode convert(DataStructNode dataStructNode);

    @Mapping(target = "label", source = "name")
    @Mapping(target = "name", source = "alias")
    @Mapping(target = "typeMeta", source = "props.relationMeta")
    @Mapping(target = "type", source = "props")
    ErDiagramField toErDiagramField(DataStructFieldNode dataStructFieldNode);

    @Mapping(target = "type", ignore = true)
    @Mapping(target = "relationModel", source = "relationModelAlias")
    ErDiagramFieldTypeMeta toErDiagramFieldTypeMeta(RelationMeta relationMeta);

}
