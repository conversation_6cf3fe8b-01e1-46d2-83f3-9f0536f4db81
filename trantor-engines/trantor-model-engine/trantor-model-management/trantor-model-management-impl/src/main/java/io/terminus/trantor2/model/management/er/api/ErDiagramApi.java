package io.terminus.trantor2.model.management.er.api;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.model.management.er.model.ErDiagramQueryRequest;
import io.terminus.trantor2.model.management.er.vo.ErDiagramNode;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Tag(name = "ER模型管理接口")
public interface ErDiagramApi {
    @Operation(summary = "查询ER模型")
    @PostMapping("/api/trantor/er")
    Response<List<ErDiagramNode>> find(@RequestBody ErDiagramQueryRequest erDiagramQueryRequest);
}
