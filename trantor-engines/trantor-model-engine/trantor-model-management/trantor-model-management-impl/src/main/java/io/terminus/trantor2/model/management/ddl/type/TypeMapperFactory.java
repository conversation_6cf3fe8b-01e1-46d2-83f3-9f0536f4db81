package io.terminus.trantor2.model.management.ddl.type;


import io.terminus.trantor2.datasource.model.SupportDialectTypeEnum;

/**
 * <AUTHOR>
 * @Date 2022/7/4
 */
public class TypeMapperFactory {

    public static TypeMapper mapperOf(SupportDialectTypeEnum dialectType, String extraInfo) {
        switch (dialectType) {
            case MYSQL:
            case DRDS:
                // mysql时，extraInfo入参数据库版本号，当mysql8时，独自使用一套校验逻辑
                if (null != extraInfo && extraInfo.startsWith("5")) {
                    return new MySQL57TypeMapper();
                } else {
                    return new MySQL8TypeMapper();
                }
            default:
                throw new UnsupportedOperationException("un supported dialect type of " + dialectType);
        }
    }

}
