package io.terminus.trantor2.model.management.ddl.interpreter;

import io.terminus.trantor2.model.management.ddl.SqlInfo;
import io.terminus.trantor2.model.management.ddl.event.CommonDDLParam;

import java.util.List;

public interface SqlInterpreter {

    List<SqlInfo> execute(CommonDDLParam commonDDLParam, boolean execute);

    List<SqlInfo> rollback(CommonDDLParam commonDDLParam, boolean execute);

}
