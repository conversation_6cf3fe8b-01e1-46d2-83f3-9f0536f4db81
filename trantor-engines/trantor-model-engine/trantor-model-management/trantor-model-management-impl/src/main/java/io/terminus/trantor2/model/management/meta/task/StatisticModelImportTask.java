package io.terminus.trantor2.model.management.meta.task;

import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.task.TaskOutput;
import io.terminus.trantor2.meta.management.task.TaskService;
import io.terminus.trantor2.model.management.api.model.ImportModelInfo;
import io.terminus.trantor2.model.management.api.model.StatisticModelCreateBySchema;
import io.terminus.trantor2.model.management.meta.service.DataStructNodeService;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 根据数据库表反向批量生成统计模型元数据
 *
 * <p>模块与数据源有关联，当在模块内的指定目录下执行导入操作时，根据用户选定的表范围，反向生成模型元数据</p>
 */
@Component
@TaskService(displayName = "根据物理表生成统计模型", visible = true)
public class StatisticModelImportTask extends BaseTask<StatisticModelImportTask.Options> {
    @Autowired
    private DataStructNodeService dataStructNodeService;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
        /**
         * 模型所在目录 key"
         */
        private String parentKey;

        /**
         * 待导入的模型信息
         */
        private List<ImportModelInfo> models;
    }

    @Override
    public void exec(Options opts, TaskOutput taskOutput, TaskContext ctx) {
        StatisticModelCreateBySchema request = new StatisticModelCreateBySchema();
        request.setTeamId(ctx.getTeamId());
        request.setParentKey(opts.getParentKey());
        request.setModels(opts.getModels());
        taskOutput.result(dataStructNodeService.createStatisticModelBySchema(request));
    }
}
