package io.terminus.trantor2.model.management.meta.model.validation;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.model.management.meta.model.AbstractPageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "校验规则分页请求")
@EqualsAndHashCode(callSuper = true)
public class PageValidationRequest extends AbstractPageRequest {
    @NotNull(message = "parentKey can not be null")
    private String parentKey;
}
