package io.terminus.trantor2.model.management.meta.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KeyRequest extends AbstractRequest {
    @Schema(description = "标识")
    @NotNull(message = "key must not be null")
    private String key;
}
