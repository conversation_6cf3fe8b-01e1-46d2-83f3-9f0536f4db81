package io.terminus.trantor2.model.management.meta.model;

import io.terminus.trantor2.model.management.meta.consts.DataStructType;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: 诸立达
 * @Date: 2023/2/24 4:27 下午
 */
@Data
public class DataStructNodePageRequest extends AbstractPageRequest {

    private Long updatedBy;

    private Date updatedAtStart;

    private Date updatedAtEnd;

    private String key;

    private String name;

    private List<String> alias;

    private DataStructType type;

    private String parentKey;

    private Boolean searchModelStatus;

}
