package io.terminus.trantor2.partition;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.terminus.trantor2.BaseTest;
import io.terminus.trantor2.Constants;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.condition.ConditionItem;
import io.terminus.trantor2.condition.ConditionItems;
import io.terminus.trantor2.condition.enums.LogicOperator;
import io.terminus.trantor2.condition.enums.Operator;
import io.terminus.trantor2.datasource.dto.PartitionDatasourceDto;
import io.terminus.trantor2.datasource.management.factory.BusinessDDLDataSourceFactory;
import io.terminus.trantor2.datasource.manager.BusinessDDLDataSourceHolderManager;
import io.terminus.trantor2.datasource.manager.BusinessDMLDataSourceHolderManager;
import io.terminus.trantor2.datasource.service.DataSourceDebugService;
import io.terminus.trantor2.meta.cache.MetaNodeCache;
import io.terminus.trantor2.model.common.model.condition.Pageable;
import io.terminus.trantor2.model.common.model.request.AggregateQueryRequest;
import io.terminus.trantor2.model.common.model.request.Order;
import io.terminus.trantor2.model.common.model.request.PagingRequest;
import io.terminus.trantor2.model.common.model.request.Select;
import io.terminus.trantor2.model.common.model.request.aggregate.AggregateFuncEnum;
import io.terminus.trantor2.model.engine.dml.api.impl.SqlExecuteApiImpl;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.model.SaveStructNodeRequest;
import io.terminus.trantor2.model.management.meta.service.DataStructNodeService;
import io.terminus.trantor2.model.runtime.api.dml.DataStructDataApi;
import io.terminus.trantor2.model.ts.impl.AnnotationTranServiceImpl;
import org.apache.shardingsphere.driver.jdbc.core.connection.ShardingSphereConnection;
import org.apache.shardingsphere.infra.metadata.database.schema.model.ShardingSphereColumn;
import org.assertj.core.util.Arrays;
import org.jetbrains.annotations.NotNull;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.CollectionUtils;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class TablePartitionTest extends BaseTest {
    public static final String test_a_alias = "testAdd$test_a";
    public static final String test_a_p_alias = "testAdd$test_a_p";
    public static final String test_b_alias = "testAdd2$test_b";
    public static final String test_c_alias = "testAdd3$test_c";

    @Autowired
    protected DataSourceDebugService dataSourceDebugService;

    @Autowired
    protected DataStructNodeService dataStructNodeService;

    @Autowired
    protected PartitionMockService mockService;

    @Autowired
    protected AnnotationTranServiceImpl annotationTranService;

    /**
     * todo 临时处理，当模型支持分表ddl之后，这里删除掉
     */
    @Autowired
    protected BusinessDDLDataSourceFactory businessDDLDataSourceFactory;

    @Autowired
    protected BusinessDMLDataSourceHolderManager businessDMLDataSourceHolderManager;
    @Autowired
    protected BusinessDDLDataSourceHolderManager businessDDLDataSourceHolderManager;

    @Autowired
    protected SqlExecuteApiImpl sqlExecuteApi;

    @Autowired
    private DataStructDataApi dataStructDataApi;

    @Autowired
    private MetaNodeCache cache;

    @Before
    public void before() {
        super.before();

        beforeCustum();
    }

    @After
    public void clean() {
        super.clean();

        afterCustum();
    }

    protected abstract void beforeCustum();

    protected abstract void afterCustum();


    @Test
    public void modelCreate() {
        // 模型准备
        prepareModelDefinition();

        _innerInitModelPartitionData();
    }

    /**
     * 查询
     * case EQ:
     * case NEQ:
     * case IN:
     * case NOT_IN:
     * case BETWEEN_AND:
     * case GT:
     * case GTE:
     * case LT:
     * case LTE:
     * case START_WITH:
     * case END_WITH:
     * case CONTAINS:
     * case NOT_CONTAINS:
     */
    @Test
    public void modelQuery() {
        // 模型准备
        prepareModelDefinition();

        // 期望test_a_p_alias模型这条数据分布在test_a_p_1表
        _innerInitModelPartitionData();

        // 根据id = 查询
        findModelDataByEqOrInAssert(test_a_p_alias, "id", 11001L);

        // 根据id != 查询
        findModelDataByNotEqOrNotInAssert(test_a_p_alias, "id", Lists.newArrayList(11001L), Lists.newArrayList(11000L, 11002L, 11003L, 11004L));

        // 根据id in查询
        findModelDataByEqOrInAssert(test_a_p_alias, "id", 11001L, 11000L, 11002L, 11003L, 11004L);

        // 根据id 范围查询
        findModelDataByBetweenAndAssert("between", test_a_p_alias, "id", 11000L, 11004L, 11001L, 11000L, 11002L, 11003L, 11004L);
        findModelDataByBetweenAndAssert("gtelte", test_a_p_alias, "id", 11000L, 11004L, 11001L, 11000L, 11002L, 11003L, 11004L);

        // 根据code = 查询
        findModelDataByEqOrInAssert(test_a_p_alias, "code", "100011");

        // 根据code in 查询
        findModelDataByEqOrInAssert(test_a_p_alias, "code", "100011", "100111", "100211", "100311", "100411");

        // 根据order_id =查询
        findModelDataByEqOrInAssert(test_a_p_alias, "orderId", "95");

        // 根据order_id in 查询
        findModelDataByEqOrInAssert(test_a_p_alias, "orderId", "95", "96", "97", "98", "99");

        // 根据code START_WITH 查询，属于全表扫描
        findModelDataByLikeAssert(Operator.START_WITH, test_a_p_alias, "code", "110", Lists.newArrayList("100011", "100111", "100211", "100311", "100411"));

        // 根据code END_WITH 查询，属于全表扫描
        findModelDataByLikeAssert(Operator.END_WITH, test_a_p_alias, "code", "11", Lists.newArrayList("100011", "100111", "100211", "100311", "100411"));

        // 根据code CONTAINS 查询，属于全表扫描
        findModelDataByLikeAssert(Operator.CONTAINS, test_a_p_alias, "code", "10", Lists.newArrayList("100011", "100111", "100211", "100311", "100411"));

        // 根据id is not null 查询，属于全表扫描
        findModelDataByIsNullAssert(false, test_a_p_alias, "id", Lists.newArrayList(11000L, 11001L, 11002L, 11003L, 11004L));

        // 根据id is null 查询，属于全表扫描
        findModelDataByIsNullAssert(true, test_a_p_alias, "id", Lists.newArrayList());

        // 根据code NOT_CONTAINS 查询，属于全表扫描
        JdbcTemplate jdbcTemplate1 = new JdbcTemplate(businessDMLDataSourceFactory.getDataSourceByTeamIdAndModule(teamId, testAdd_module));
        List<Map<String, Object>> resp = jdbcTemplate1.queryForList("select * from test_a_p where code not like '%243%'");
        Assert.assertTrue(resp.size() == 5);

        try {
            // 根据name =查询 TODO 理论上应该报错，因为没有指定分表字段
            findModelDataByEqOrInAssert(test_a_p_alias, "name", "zzz1");
            Assert.assertTrue(false);
        } catch (Exception e) {
            Assert.assertTrue(true);
            Assert.assertTrue(e.getMessage().contains("开启分表模型操作时需包含分表字段条件"));
        }

        // 根据id not in查询 TODO shardingsphere 5.4.1处理not in 存在bug https://github.com/apache/shardingsphere/issues/29735
        findModelDataByNotEqOrNotInAssert(test_a_p_alias, "id", Lists.newArrayList(11000L, 11001L), Lists.newArrayList(11002L, 11003L, 11004L));
    }

    /**
     * 聚合查询
     * * - 求和: select sum(<field>) from <table> where <conditionField>=<conditionValue>
     * * - 计数: select count(<field>) from <table> where <conditionField>=<conditionValue>
     * * - 平均值: select avg(<field>) from <table> where <conditionField>=<conditionValue>
     * * - 最大值: select max(<field>) from <table> where <conditionField>=<conditionValue>
     * * - 最小值: select min(<field>) from <table> where <conditionField>=<conditionValue>
     */
    @Test
    public void modelAggrate() {
        // 模型准备
        prepareModelDefinition();

        // 期望test_a_p_alias模型这条数据分布在test_a_p_1表
        _innerInitModelPartitionData();

        // 根据id in做路由，执行sum查询
        findModelDataByInAggrateAssert(AggregateFuncEnum.SUM, test_a_p_alias, "id", Lists.newArrayList(11001L, 11000L, 11002L, 11003L), 11001 + 11000 + 11002 + 11003);
        // 根据id in做路由，执行count查询
        findModelDataByInAggrateAssert(AggregateFuncEnum.COUNT, test_a_p_alias, "id", Lists.newArrayList(11001L, 11000L, 11002L, 11003L), 4);
        // 根据id in做路由，执行avg查询
        findModelDataByInAggrateAssert(AggregateFuncEnum.AVG, test_a_p_alias, "id", Lists.newArrayList(11001L, 11000L, 11002L, 11003L), (11001 + 11000 + 11002 + 11003) / 4);
        // 根据id in做路由，执行max查询
        findModelDataByInAggrateAssert(AggregateFuncEnum.MAX, test_a_p_alias, "id", Lists.newArrayList(11001L, 11000L, 11002L, 11003L), 11003);
        // 根据id in做路由，执行min查询
        findModelDataByInAggrateAssert(AggregateFuncEnum.MIN, test_a_p_alias, "id", Lists.newArrayList(11001L, 11000L, 11002L, 11003L), 11000);

        JdbcTemplate jdbcTemplate1 = new JdbcTemplate(businessDMLDataSourceFactory.getDataSourceByTeamIdAndModule(teamId, testAdd_module));
        List<Map<String, Object>> resp = jdbcTemplate1.queryForList("SELECT `name`, COUNT(*) as total_count FROM test_a_p WHERE id in (11000,11004)  GROUP BY name ");
        Assert.assertTrue(resp.size() == 1);
        Assert.assertTrue(((BigDecimal) resp.get(0).get("total_count")).intValue() == 2);

        // todo 这种情况不支持，having语句后的查询也是打到每个物理表的
        resp = jdbcTemplate1.queryForList("SELECT `name`, COUNT(*) as total_count FROM test_a_p WHERE id in (11000,11004)  GROUP BY name HAVING COUNT(*) >=2");
        Assert.assertTrue(resp.size() == 0);
    }

    /**
     * 多条件复合查询
     */
    @Test
    public void modelComplex() {
        // 模型准备
        prepareModelDefinition();

        // 期望test_a_p_alias模型这条数据分布在test_a_p_1表
        _innerInitModelPartitionData();

        // 取并集
        JdbcTemplate jdbcTemplate1 = new JdbcTemplate(businessDMLDataSourceFactory.getDataSourceByTeamIdAndModule(teamId, testAdd_module));
        List<Map<String, Object>> resp = jdbcTemplate1.queryForList("SELECT * FROM test_a_p WHERE id in (11000,11001) or code in('100211', '100311', '100411')");
        Assert.assertTrue(resp.size() == 5);

        // 取交集
        resp = jdbcTemplate1.queryForList("SELECT * FROM test_a_p WHERE id in (11000,11004) and code in('100011', '100311', '100411')");
        Assert.assertTrue(resp.size() == 2);

    }

    /**
     * 更新
     */
    @Test
    public void modelUpdateTest() {
        // 模型准备
        prepareModelDefinition();

        // 期望test_a_p_alias模型这条数据分布在test_a_p_1表
        _innerInitModelPartitionData();

        // 取并集
        JdbcTemplate jdbcTemplate1 = new JdbcTemplate(businessDMLDataSourceFactory.getDataSourceByTeamIdAndModule(teamId, testAdd_module));
        jdbcTemplate1.execute("update test_a_p set name='aaa' where id in (11000,11001)");
        List<Map<String, Object>> resp = jdbcTemplate1.queryForList("SELECT * FROM test_a_p WHERE id=11000");
        Assert.assertTrue(String.valueOf(resp.get(0).get("name")).equals("aaa"));

        try {
            // 不支持对分表字段的更新
            jdbcTemplate1.update("update test_a_p set code='?',order_id='?' where id = ?", "100011", "95", 11000);
            Assert.assertTrue(false);
        } catch (Exception e) {
            // PreparedStatementCallback; SQL [update test_a_p set code='?',order_id='?' where id = ?]; Can not update sharding value for table `test_a_p`.; nested exception is java.sql.SQLException: Can not update sharding value for table `test_a_p`.
            e.printStackTrace();
            Assert.assertTrue(true);
        }
    }

    /**
     * 更新
     */
    @Test
    public void modelMetaChangeTest() {
        // 模型准备
        prepareModelDefinition();

        // 期望test_a_p_alias模型这条数据分布在test_a_p_1表
        _innerInitModelPartitionData();

        // 变更前元信息
        assertColumnExist(false);

        Map<String, PartitionDatasourceDto> dtos = businessDDLDataSourceFactory.getDDLDataSourceByModel(teamId, test_a_p_alias, "test_a_p");
        dtos.forEach((key, dto) -> {
            JdbcTemplate jdbcTemplate1 = new JdbcTemplate(dto.getDataSource());
            jdbcTemplate1.execute("alter table `" + key + "` add `namenew` mediumtext DEFAULT NULL COMMENT 'namenew'");
        });

        assertColumnExist(false);

        // 动态更新sharding元信息
        businessDMLDataSourceHolderManager.handleModelChange(teamCode, Sets.newHashSet(test_a_p_alias));

        assertColumnExist(true);
    }

    /**
     * 分表数据源处理动态普通表新增
     */
    @Test
    public void modelMetaNewAddTest() {
        // 模型准备
        initContext();
        DataStructNode testAP = mockModel(Constants.PERSIST_MODEL_ADD_STR_PARTITION);
        SaveStructNodeRequest request3 = createSaveRequest(testAP);
        dataStructNodeService.save(request3);

        DataSource dataSource = businessDMLDataSourceFactory.getDataSourceByTeamCodeAndModule(teamCode, testAdd_module);

        // 这一次插入应该是成功的
        assertInsertWhenNewCreate(dataSource, false);

        // 新增模型test_a的定义，这里会自动触发handleModelChange操作
        initContext();
        DataStructNode testA = mockModel(Constants.PERSIST_MODEL_ADD_STR);
        SaveStructNodeRequest request = createSaveRequest(testA);
        dataStructNodeService.save(request);

        // 临时解决宇强那边元数据为null时，也缓存的问题
        cache.clear();

        // 动态更新sharding元信息
        businessDMLDataSourceHolderManager.handleModelChange(teamCode, Sets.newHashSet(test_a_alias));

        // 这一次插入应该是成功的
        assertInsertWhenNewCreate(dataSource, true);
    }

    private static void assertInsertWhenNewCreate(DataSource dataSource, boolean success) {
        try {
            new JdbcTemplate(dataSource)
                .execute("INSERT INTO `test_a` (`id`, `created_by`, `updated_by`, `created_at`, `updated_at`, `version`, `deleted`, `name`, `boll`)\n" +
                    "VALUES\n" +
                    "\t(11, NULL, NULL, '2022-11-11 11:11:11', '2022-11-11 11:11:11', 0, 0, '1', 1);\n");

            Assert.assertTrue(success ? true : false);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertFalse(success ? true : false);
        }
    }

    private void assertColumnExist(boolean checkExist) {
        DataSource dataSource = businessDMLDataSourceFactory.getDataSourceByTeamCodeAndModule(teamCode, testAdd_module);
        ShardingSphereConnection connection = null;
        try {
            connection = (ShardingSphereConnection) dataSource.getConnection();
            ShardingSphereColumn column = connection.getContextManager().getMetaDataContexts().getMetaData().getDatabase("1_ceshi").
                getSchema("1_ceshi").getTable("test_a_p").getColumn("namenew");
            if (checkExist) {
                Assert.assertNotNull(column);
            } else {
                Assert.assertNull(column);
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        } finally {
            if (null != connection) {
                try {
                    connection.close();
                } catch (SQLException e) {
                }
            }
        }
    }

    private void findModelDataByInAggrateAssert(AggregateFuncEnum aggType, String modelAlias, String fieldName, ArrayList<Long> params, long result) {
        Response<Object> response = findModelEngineByAggrate(aggType, modelAlias, fieldName, params.toArray(new Object[]{}));
        if (response.getData() instanceof BigDecimal) {
            Assert.assertTrue(((BigDecimal) response.getData()).longValue() == result);
        } else {
            Assert.assertTrue((Long) response.getData() == result);
        }
    }

    private Response<Object> findModelEngineByAggrate(AggregateFuncEnum aggType, String modelAlias, String fieldName, Object[] array) {
        AggregateQueryRequest aggreteRequest = new AggregateQueryRequest();
        aggreteRequest.setModelAlias(modelAlias);
        aggreteRequest.setAggregateFunc(aggType);
        aggreteRequest.setAggregateField("id");

        ConditionItems conditionItems = new ConditionItems();
        conditionItems.setLogicOperator(LogicOperator.AND);

        Map<String, ConditionItem> idMap = new HashMap<>();
        ConditionItem idCondition = new ConditionItem();
        idCondition.setOperator(Operator.IN);
        List<Object> objs = new ArrayList<>();
        for (Object id : array) {
            objs.add(id);
        }
        idCondition.setValue(objs);
        idMap.put(fieldName, idCondition);

        conditionItems.setConditions(idMap);

        aggreteRequest.setConditionItems(conditionItems);
        aggreteRequest.setTeamId(teamId);
        Response<Object> response = dataStructDataApi.aggregate(aggreteRequest);
        return response;
    }

    private void findModelDataByBetweenAndAssert(String type, String modelAlias, String fieldName,
                                                 Object start, Object end, Object... aspectValues) {
        Response<Paging<Map<String, Object>>> response = findModelEngineByRange(type, modelAlias, fieldName, start, end);

        assertDataFount(fieldName, Arrays.asList(aspectValues), response);
    }

    public Response<Paging<Map<String, Object>>> findModelEngineByLike(Operator queryType, String modelAlias, String fieldName, Object param) {
        PagingRequest pagingRequest = createPagingRequest(modelAlias);

        ConditionItems conditionItems = new ConditionItems();
        conditionItems.setLogicOperator(LogicOperator.AND);

        Map<String, ConditionItem> idMap = new HashMap<>();
        ConditionItem idCondition = new ConditionItem();

        idCondition.setOperator(queryType);

        idCondition.setValue(param);
        idMap.put(fieldName, idCondition);

        conditionItems.setConditions(idMap);

        pagingRequest.setConditionItems(conditionItems);
        pagingRequest.setTeamId(teamId);
        Response<Paging<Map<String, Object>>> response = dataStructDataApi.paging(pagingRequest);
        return response;
    }

    @NotNull
    private static PagingRequest createPagingRequest(String modelAlias) {
        PagingRequest pagingRequest = new PagingRequest();
        pagingRequest.setPage(new Pageable(1, 100, true));
        pagingRequest.setModelAlias(modelAlias);

        List<Select> selectList = new ArrayList<>();
        selectList.add(new Select("id"));
        selectList.add(new Select("code"));
        selectList.add(new Select("orderId"));
        selectList.add(new Select("name"));
        pagingRequest.setSelect(selectList);

        List<Order> orderList = new ArrayList<>();
        orderList.add(new Order("id", false));
        pagingRequest.setOrderBy(orderList);
        return pagingRequest;
    }

    public Response<Paging<Map<String, Object>>> findModelEngineByIsNull(boolean forward, String modelAlias, String fieldName) {
        PagingRequest pagingRequest = createPagingRequest(modelAlias);

        ConditionItems conditionItems = new ConditionItems();
        conditionItems.setLogicOperator(LogicOperator.AND);

        Map<String, ConditionItem> idMap = new HashMap<>();
        ConditionItem idCondition = new ConditionItem();
        idCondition.setOperator(forward ? Operator.IS_NULL : Operator.IS_NOT_NULL);
        idMap.put(fieldName, idCondition);

        conditionItems.setConditions(idMap);

        pagingRequest.setConditionItems(conditionItems);
        pagingRequest.setTeamId(teamId);
        Response<Paging<Map<String, Object>>> response = dataStructDataApi.paging(pagingRequest);
        return response;
    }

    public Response<Paging<Map<String, Object>>> findModelEngineById(boolean forward, String modelAlias, String fieldName, Object... ids) {
        PagingRequest pagingRequest = createPagingRequest(modelAlias);

        ConditionItems conditionItems = new ConditionItems();
        conditionItems.setLogicOperator(LogicOperator.AND);

        Map<String, ConditionItem> idMap = new HashMap<>();
        ConditionItem idCondition = new ConditionItem();
        if (ids.length == 1) {
            idCondition.setOperator(forward ? Operator.EQ : Operator.NEQ);
            idCondition.setValue(ids[0]);
            idMap.put(fieldName, idCondition);
        } else {
            idCondition.setOperator(forward ? Operator.IN : Operator.NOT_IN);
            List<Object> objs = new ArrayList<>();
            for (Object id : ids) {
                objs.add(id);
            }
            idCondition.setValue(objs);
            idMap.put(fieldName, idCondition);
        }

        conditionItems.setConditions(idMap);

        pagingRequest.setConditionItems(conditionItems);
        pagingRequest.setTeamId(teamId);
        Response<Paging<Map<String, Object>>> response = dataStructDataApi.paging(pagingRequest);
        return response;
    }

    public Response<Paging<Map<String, Object>>> findModelEngineByRange(String queryType, String modelAlias, String fieldName, Object start, Object end) {
        PagingRequest pagingRequest = createPagingRequest(modelAlias);

        ConditionItems conditionItems = new ConditionItems();
        conditionItems.setLogicOperator(LogicOperator.AND);

        Map<String, ConditionItem> idMap = new HashMap<>();

        if (queryType.equalsIgnoreCase("between")) {
            ConditionItem rangeCondition = new ConditionItem();
            rangeCondition.setOperator(Operator.BETWEEN_AND);
            List<Object> ids = new ArrayList<>();
            ids.add(start);
            ids.add(end);
            rangeCondition.setValue(ids);
            idMap.put(fieldName, rangeCondition);
        } else {

            ConditionItem gteCondition = new ConditionItem();
            gteCondition.setOperator(Operator.GTE);
            gteCondition.setValue(start);
            idMap.put(fieldName, gteCondition);

            ConditionItem lteCondition = new ConditionItem();
            lteCondition.setOperator(Operator.LTE);
            lteCondition.setValue(end);
            idMap.put(fieldName, lteCondition);
        }

        conditionItems.setConditions(idMap);

        pagingRequest.setConditionItems(conditionItems);
        pagingRequest.setTeamId(teamId);
        Response<Paging<Map<String, Object>>> response = dataStructDataApi.paging(pagingRequest);
        return response;
    }

    private void findModelDataByEqOrInAssert(String modelAlias, String fieldName, Object... ids) {
        Response<Paging<Map<String, Object>>> response = findModelEngineById(true, modelAlias, fieldName, ids);
        assertDataFount(fieldName, Arrays.asList(ids), response);

    }

    private void findModelDataByLikeAssert(Operator operator, String modelAlias, String fieldName, String param, List<Object> results) {
        Response<Paging<Map<String, Object>>> response = findModelEngineByLike(operator, modelAlias, fieldName, param);
        assertDataFount(fieldName, results, response);
    }

    private static void assertDataFount(String fieldName, List<Object> results, Response<Paging<Map<String, Object>>> response) {
        List<Object> resIds = new ArrayList<>();
        response.getData().getData().forEach(data -> {
            resIds.add(data.get(fieldName));
        });

        Assert.assertFalse(CollectionUtils.isEmpty(resIds));

        boolean found = true;
        for (Object id : results) {
            if (!resIds.contains(id)) {
                found = false;
                break;
            }
        }

        Assert.assertTrue(found);
    }

    private void findModelDataByIsNullAssert(boolean forward, String modelAlias, String fieldName, List<Object> results) {
        Response<Paging<Map<String, Object>>> response = findModelEngineByIsNull(forward, modelAlias, fieldName);

        if (CollectionUtils.isEmpty(results)) {
            Assert.assertTrue(null == response.getData() || null == response.getData().getData() || response.getData().getData().isEmpty());
        } else {
            assertDataFount(fieldName, results, response);
        }
    }

    private void findModelDataByNotEqOrNotInAssert(String modelAlias, String fieldName, List<Object> params, List<Object> results) {
        Response<Paging<Map<String, Object>>> response = findModelEngineById(false, modelAlias, fieldName, params.toArray(new Object[]{}));
        assertDataFount(fieldName, results, response);

    }


    protected void prepareModelDefinition() {
        // 创建普通模型
        initContext();
        DataStructNode testA = mockModel(Constants.PERSIST_MODEL_ADD_STR);
        SaveStructNodeRequest request = createSaveRequest(testA);
        dataStructNodeService.save(request);

        initContext();
        DataStructNode testC = mockModel(Constants.PERSIST_MODEL_ADD_STR3);
        SaveStructNodeRequest request2 = createSaveRequest(testC);
        dataStructNodeService.save(request2);

        initContext();
        DataStructNode testAP = mockModel(Constants.PERSIST_MODEL_ADD_STR_PARTITION);
        SaveStructNodeRequest request3 = createSaveRequest(testAP);
        dataStructNodeService.save(request3);

        prepareModelDefinitionCustum();
    }

    private void sleepSecond(int second) {
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
        }
    }

    @NotNull
    private SaveStructNodeRequest createSaveRequest(DataStructNode dataStructNode) {
        SaveStructNodeRequest request = new SaveStructNodeRequest();
        request.setTeamId(dataStructNode.getTeamId());
        request.setAlias(dataStructNode.getAlias());
        request.setName(dataStructNode.getName());
        request.setProps(dataStructNode.getProps());
        request.setChildren(dataStructNode.getChildren());
        request.setParentKey(dataStructNode.getParentKey());
        if (null != dataStructNode.getId()) {
            request.setId(dataStructNode.getId());
        }
        return request;
    }

    protected void _innerInitModelPartitionData() {
        // 期望test_a_p_alias模型这条数据分布在test_a_p_1表
        mockService.createByModelEngine(test_a_p_alias, 11000L, "100011", "95", "zzz1");
        mockService.createByModelEngine(test_a_p_alias, 11001L, "100111", "96", "zzz2");
        mockService.createByModelEngine(test_a_p_alias, 11002L, "100211", "97", "zzz3");
        mockService.createByModelEngine(test_a_p_alias, 11003L, "100311", "98", "zzz4");
        mockService.createByModelEngine(test_a_p_alias, 11004L, "100411", "99", "zzz1");

        mockService.createByModelEngine(test_a_alias, 11001L, "zzz");

        // 验证数据插入成功了
        mockService.assertModelDataFound(test_a_p_alias, 11000);
        mockService.assertModelDataFound(test_a_p_alias, 11001);
        mockService.assertModelDataFound(test_a_p_alias, 11002);
        mockService.assertModelDataFound(test_a_p_alias, 11003);
        mockService.assertModelDataFound(test_a_p_alias, 11004);
        mockService.assertModelDataFound(test_a_alias, 11001L);
    }

    public void prepareModelDefinitionCustum() {
    }
}
