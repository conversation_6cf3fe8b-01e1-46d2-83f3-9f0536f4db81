package io.terminus.trantor2.model.ts.impl;

import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.model.common.model.request.CreateObject;
import io.terminus.trantor2.model.common.model.request.IdQueryRequest;
import io.terminus.trantor2.model.common.model.request.Select;
import io.terminus.trantor2.model.common.model.request.UpdateObject;
import io.terminus.trantor2.model.runtime.api.dml.DataStructDataApi;
import io.terminus.trantor2.model.runtime.api.transaction.ErpTransactional;
import io.terminus.trantor2.model.ts.api.AnnotationTranService;
import io.terminus.trantor2.model.ts.api.SubAnnotationTranService;
import org.junit.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class BaseServiceImpl {
    @Autowired
    private DataStructDataApi dataApi;
    public static final String modelAlias1 = "testAdd$dump_search";
    public static final String modelAlias2 = "testAdd2$dump_search_2";

    protected void _testBatchAddDataValidate(long id, boolean needRollback, String modelAlias) {
        IdQueryRequest idQueryRequest = new IdQueryRequest();
        idQueryRequest.setTeamId(1L);
        idQueryRequest.setModelAlias(modelAlias);
        List<Select> selectList = new ArrayList<>();
        Select select = new Select();
        select.setField("id");
        selectList.add(select);
        idQueryRequest.setId(id);
        idQueryRequest.setSelect(selectList);
        Response<Map<String, Object>> response = dataApi.findById(idQueryRequest);
        if (needRollback) {
            Assert.assertTrue(null == response.getData() || response.getData().isEmpty());
        } else {
            Assert.assertTrue(Objects.equals(String.valueOf(response.getData().get("id")), "" + id));
        }
    }

    protected Response dataApiCreateObj(long teamId, String modelAlias, Map<String, Object> dataMap) {
        CreateObject createObject = new CreateObject();
        createObject.setTeamId(teamId);
        createObject.setModelAlias(modelAlias);
        createObject.setData(dataMap);
        return dataApi.create(createObject);
    }

    protected Response dataApiUpdateObj(long teamId, String modelAlias, Map<String, Object> dataMap) {
        UpdateObject updateObject = new UpdateObject();
        updateObject.setTeamId(teamId);
        updateObject.setModelAlias(modelAlias);
        updateObject.setData(dataMap);
        return dataApi.updateById(updateObject);
    }

    protected void mockData(String test_a_alias, long id, String name) {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("id", id);
        dataMap.put("name", name);
        dataApiCreateObj(1L, test_a_alias, dataMap);
    }
}
