package io.terminus.trantor2.normal;

import io.terminus.trantor2.BaseTest;
import io.terminus.trantor2.Constants;
import io.terminus.trantor2.model.engine.dml.orm.transaction.TransactionContext;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.model.SaveStructNodeRequest;
import io.terminus.trantor2.model.management.meta.service.DataStructNodeService;
import io.terminus.trantor2.model.ts.impl.ProgramTranServiceImpl;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Propagation;

import jakarta.persistence.EntityManager;

/**
 * 跨模块单库
 */
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT,
    properties = {
        "ELASTICSEARCH_HOST=127.0.0.1",
        "ELASTICSEARCH_PORT=9200",
        "MYSQL_HOST=127.0.0.1",
        "MYSQL_PORT=3306",
        "server.port=8081",
        "UNIT_TEST_MODE=true"
    })
//@ActiveProfiles("model")
public class SingleDbProgramPropagateTest extends BaseTest {

    @Autowired
    private DataStructNodeService dataStructNodeService;

    @Autowired
    private ProgramTranServiceImpl programTranService;

    @Autowired
    private EntityManager entityManager;


    /**
     * required 正常执行
     * 期望：所有结果正常执行
     */
    @Test
    public void testREQUIRED1() {
        // 模型准备
        prepareModelDefinition();

        // 上下文
        initContext();
        programInitContext();
        try {
            programTranService.remoteTest(Propagation.REQUIRED, false, false, false);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.fail();
        }

        // 数据校验
        programTranService.remoteTestValidate(Propagation.REQUIRED, false, false, false);
    }

    /**
     * required 内部方法异常，并且外部方法并没有捕获内部方法的异常
     * 期望：数据全部回滚成功
     */
    @Test
    public void testREQUIRED2() {
        // 模型准备
        prepareModelDefinition();

        // 上下文
        initContext();
        programInitContext();
        try {
            programTranService.remoteTest(Propagation.REQUIRED, true, false, false);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(true);
        }

        // 数据校验
        programTranService.remoteTestValidate(Propagation.REQUIRED, true, false, false);
    }

    /**
     * required 内部方法异常，并且外部方法捕获了内部方法抛出的异常
     * 期望：数据全部回滚成功
     * 内部异常捕获后，如果还有db操作会抛异常：org.springframework.transaction.UnexpectedRollbackException: Transaction rolled back because it has been marked as rollback-only
     */
    @Test
    public void testREQUIRED3() {
        // 模型准备
        prepareModelDefinition();

        // 上下文
        initContext();
        programInitContext();
        try {
            programTranService.remoteTest(Propagation.REQUIRED, true, false, true);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(true);
        }

        // 数据校验
        programTranService.remoteTestValidate(Propagation.REQUIRED, true, false, true);
    }

    /**
     * required 外部方法异常
     * 期望：数据全部回滚成功
     */
    @Test
    public void testREQUIRED4() {
        // 模型准备
        prepareModelDefinition();

        // 上下文
        initContext();
        programInitContext();
        try {
            programTranService.remoteTest(Propagation.REQUIRED, false, true, false);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(true);
        }

        // 数据校验
        programTranService.remoteTestValidate(Propagation.REQUIRED, false, true, false);
    }

    /**
     * NESTED 正常执行
     * 期望：所有结果正常执行
     */
    @Test
    public void testNEST1() {
        // 模型准备
        prepareModelDefinition();

        // 上下文
        initContext();
        programInitContext();
        try {
            programTranService.remoteTest(Propagation.NESTED, false, false, false);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.fail();
        }

        // 数据校验
        programTranService.remoteTestValidate(Propagation.NESTED, false, false, false);
    }

    /**
     * NESTED 内部方法异常，并且外部方法并没有捕获内部方法的异常
     * 期望：所有数据都回滚
     */
    @Test
    public void testNEST2() {
        // 模型准备
        prepareModelDefinition();

        // 上下文
        initContext();
        programInitContext();
        try {
            programTranService.remoteTest(Propagation.NESTED, true, false, false);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(true);
        }

        // 数据校验
        programTranService.remoteTestValidate(Propagation.NESTED, true, false, false);
    }

    /**
     * NESTED 内部方法异常，并且外部方法捕获了内部方法抛出的异常
     * 期望：内部方法数据回滚，外部方法数据正常插入
     */
    @Test
    public void testNEST3() {
        // 模型准备
        prepareModelDefinition();

        // 上下文
        initContext();
        programInitContext();
        try {
            programTranService.remoteTest(Propagation.NESTED, true, false, true);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(true);
        }

        // 数据校验
        programTranService.remoteTestValidate(Propagation.NESTED, true, false, true);
    }

    /**
     * NESTED 外部方法抛出异常
     * 期望：数据全部回滚成功
     */
    @Test
    public void testNEST4() {
        // 模型准备
        prepareModelDefinition();

        // 上下文
        initContext();
        programInitContext();
        try {
            programTranService.remoteTest(Propagation.NESTED, false, true, false);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(true);
        }

        // 数据校验
        programTranService.remoteTestValidate(Propagation.NESTED, false, true, false);
    }


    /**
     * REQUIRES_NEW 正常执行
     * 期望：所有结果正常执行
     */
    @Test
    public void testREQUIRES_NEW1() {
        // 模型准备
        prepareModelDefinition();

        // 上下文
        initContext();
        programInitContext();
        try {
            programTranService.remoteTest(Propagation.REQUIRES_NEW, false, false, false);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.fail();
        }

        // 数据校验
        programTranService.remoteTestValidate(Propagation.REQUIRES_NEW, false, false, false);
    }

    /**
     * REQUIRES_NEW 内部方法异常，并且外部方法并没有捕获内部方法的异常
     * 期望：所有数据都回滚
     */
    @Test
    public void testREQUIRES_NEW2() {
        // 模型准备
        prepareModelDefinition();

        // 上下文
        initContext();
        programInitContext();
        try {
            programTranService.remoteTest(Propagation.REQUIRES_NEW, true, false, false);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(true);
        }

        // 数据校验
        programTranService.remoteTestValidate(Propagation.REQUIRES_NEW, true, false, false);
    }

    /**
     * REQUIRES_NEW 内部方法异常，并且外部方法捕获了内部方法抛出的异常
     * 期望：内部方法数据回滚，外部方法数据正常插入
     */
    @Test
    public void testREQUIRES_NEW3() {
        // 模型准备
        prepareModelDefinition();

        // 上下文
        initContext();
        programInitContext();
        try {
            programTranService.remoteTest(Propagation.REQUIRES_NEW, true, false, true);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(true);
        }

        // 数据校验
        programTranService.remoteTestValidate(Propagation.REQUIRES_NEW, true, false, true);
    }

    /**
     * REQUIRES_NEW 外部方法抛出异常
     * 期望：外部数据回滚，内部方法数据不回滚
     */
    @Test
    public void testREQUIRES_NEW4() {
        // 模型准备
        prepareModelDefinition();

        // 上下文
        initContext();
        programInitContext();
        try {
            programTranService.remoteTest(Propagation.REQUIRES_NEW, false, true, false);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(true);
        }

        // 数据校验
        programTranService.remoteTestValidate(Propagation.REQUIRES_NEW, false, true, false);
    }


    private void prepareModelDefinition() {
        // 创建普通模型
        initContext();
        DataStructNode testA = mockModel(Constants.PERSIST_MODEL_ADD_STR);
        SaveStructNodeRequest request = createSaveRequest(testA);
        dataStructNodeService.save(request);

        initContext();
        DataStructNode testC = mockModel(Constants.PERSIST_MODEL_ADD_STR3);
        SaveStructNodeRequest request2 = createSaveRequest(testC);
        dataStructNodeService.save(request2);

        initContext();
        DataStructNode testB = mockModel(Constants.PERSIST_MODEL_ADD_STR2);
        SaveStructNodeRequest request3 = createSaveRequest(testB);
        dataStructNodeService.save(request3);

//        initContext();
//        DataStructNode orderLine = mockModel(Constants.ORDER_LINE);
//        SaveStructNodeRequest request4 = createSaveRequest(orderLine);
//        dataStructNodeService.save(request4);
//
//        initContext();
//        DataStructNode sameLine = mockModel(Constants.SAME_LINE);
//        SaveStructNodeRequest request5 = createSaveRequest(sameLine);
//        dataStructNodeService.save(request5);
//
//        initContext();
//        DataStructNode orderMain = mockModel(Constants.ORDER_MAIN);
//        SaveStructNodeRequest request6 = createSaveRequest(orderMain);
//        dataStructNodeService.save(request6);
    }

    private void sleepSecond(int second) {
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
        }
    }

    @NotNull
    private SaveStructNodeRequest createSaveRequest(DataStructNode dataStructNode) {
        SaveStructNodeRequest request = new SaveStructNodeRequest();
        request.setTeamId(dataStructNode.getTeamId());
        request.setAlias(dataStructNode.getAlias());
        request.setName(dataStructNode.getName());
        request.setProps(dataStructNode.getProps());
        request.setChildren(dataStructNode.getChildren());
        request.setParentKey(dataStructNode.getParentKey());
        if (null != dataStructNode.getId()) {
            request.setId(dataStructNode.getId());
        }
        return request;
    }

    private void programInitContext() {
        TransactionContext.setTeamCode(teamCode);
        TransactionContext.setTeamId(teamId);
        TransactionContext.setModuleKey(testAdd_module);
    }
}
