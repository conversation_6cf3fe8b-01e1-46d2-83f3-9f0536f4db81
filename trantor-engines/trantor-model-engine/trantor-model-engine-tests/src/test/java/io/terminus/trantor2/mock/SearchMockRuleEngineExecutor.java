package io.terminus.trantor2.mock;

import io.terminus.trantor2.rule.engine.api.model.result.RuleEngineResult;
import io.terminus.trantor2.rule.engine.client.RuleEngineExecutor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 模型单侧 mock 规则引擎执行器， 因为单侧不需要这里，而permission模块中需要强依赖于RuleEngineExecutor
 */
@Slf4j
@Component
public class SearchMockRuleEngineExecutor implements RuleEngineExecutor {
    private static final String ERR_MSG = "模型测试不应该调用到这里。。。";

    @Override
    public Object execute(String express, Map<String, Object> context) {
        log.error(ERR_MSG);
        throw new IllegalStateException(ERR_MSG);
    }

    @Override
    public Object execute(String express, List<Map<String, Object>> params) {
        log.error(ERR_MSG);
        throw new IllegalStateException(ERR_MSG);
    }

    @Override
    public RuleEngineResult executeResult(String express, Map<String, Object> context) {
        log.error(ERR_MSG);
        throw new IllegalStateException(ERR_MSG);
    }

    @Override
    public Object execute(String express, Collection<Object> params) {
        log.error(ERR_MSG);
        throw new IllegalStateException(ERR_MSG);
    }
}
