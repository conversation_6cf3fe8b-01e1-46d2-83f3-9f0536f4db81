package io.terminus.trantor2.ddl;

import io.terminus.trantor2.BaseTest;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.datasource.dto.PartitionDatasourceDto;
import io.terminus.trantor2.datasource.management.factory.BusinessDDLDataSourceFactory;
import io.terminus.trantor2.ddl.data.DataConst;
import io.terminus.trantor2.model.management.ddl.DDLGenerator;
import io.terminus.trantor2.model.management.ddl.DataStructDDLFetcher;
import io.terminus.trantor2.model.management.meta.consts.DataStructType;
import io.terminus.trantor2.model.management.meta.consts.FieldType;
import io.terminus.trantor2.model.management.meta.dialect.entity.TableStructure;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldProperties;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.model.SaveStructNodeRequest;
import io.terminus.trantor2.model.management.meta.repository.DataStructNodeRepo;
import io.terminus.trantor2.model.management.meta.service.DataStructNodeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT,
    properties = {
        "MYSQL_HOST=127.0.0.1",
        "MYSQL_PORT=3306",
        "server.port=8088",
        "UNIT_TEST_MODE=true",
        "trantor2.meta.event.type=redis"
    })
@Slf4j
public class DDLTest extends BaseTest {
    @Autowired
    private DataStructNodeService dataStructNodeService;
    @Autowired
    private DataStructNodeRepo dataStructNodeRepo;
    @Autowired
    private DDLGenerator ddlGenerator;
    @Autowired
    private BusinessDDLDataSourceFactory businessDDLDataSourceFactory;
    @Autowired
    private DataStructDDLFetcher ddlFetcher;

    private static final String MODEL_KEY = "ddl$model_test";

    /**
     * 验证模型创建的场景
     */
    @Test
    public void modelCreate() {
        SaveStructNodeRequest modelCreateRequest = JsonUtil.fromJson(DataConst.CREATE_MODEL, SaveStructNodeRequest.class);
        DataStructNode model = modelCreateRequest.convertToEntity();
        dataStructNodeRepo.save(model);
        if (model.getProps().getType() == DataStructType.PERSIST) {
            ddlGenerator.update(model);
        }

        model = dataStructNodeService.findModel(teamId, MODEL_KEY);
        Assert.assertNotNull(model);
    }

    /**
     * 验证模型新增字段的场景
     */
    @Test
    public void modelAddColumn() {
        // 新增模型
        SaveStructNodeRequest modelCreateRequest = JsonUtil.fromJson(DataConst.CREATE_MODEL, SaveStructNodeRequest.class);
        DataStructNode model = modelCreateRequest.convertToEntity();
        dataStructNodeRepo.save(model);
        if (model.getProps().getType() == DataStructType.PERSIST) {
            ddlGenerator.update(model);
        }

        // 为新增模型添加字段
        DataStructFieldProperties properties = new DataStructFieldProperties();
        properties.setFieldType(FieldType.TEXT);
        properties.setColumnName("add_field");
        properties.setLength(20);
        DataStructFieldNode field = DataStructFieldNode.builder()
            .key("add_field")
            .name("新增字段")
            .props(properties)
            .build();
        model.getChildren().add(field);
        dataStructNodeRepo.save(model);
        if (model.getProps().getType() == DataStructType.PERSIST) {
            ddlGenerator.update(model, false);
        }

        DataStructNode result = dataStructNodeService.findModel(teamId, MODEL_KEY);
        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.getChildren().stream().filter(x -> x.getKey().equals("add_field")).count());
    }

    /**
     * 验证模型更新字段属性的场景
     */
    @Test
    public void modelUpdateColumn() {
        // 新增模型
        SaveStructNodeRequest modelCreateRequest = JsonUtil.fromJson(DataConst.CREATE_MODEL, SaveStructNodeRequest.class);
        DataStructNode model = modelCreateRequest.convertToEntity();
        dataStructNodeRepo.save(model);
        if (model.getProps().getType() == DataStructType.PERSIST) {
            ddlGenerator.update(model);
        }

        // 修改字段属性信息
        model = dataStructNodeService.findModel(teamId, MODEL_KEY);
        for (DataStructFieldNode child : model.getChildren()) {
            if (child.getKey().equals("field_text")) {
                Assert.assertTrue(child.getProps().isRequired());

                child.getProps().setRequired(false);
                child.getProps().setLength(250);
                break;
            }
        }

        DataStructNode result = dataStructNodeRepo.save(model);
        if (model.getProps().getType() == DataStructType.PERSIST) {
            ddlGenerator.update(model, false);
        }
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getChildren());
        result.getChildren().forEach(child -> {
            if (child.getKey().equals("field_text")) {
                Assert.assertFalse(child.getProps().isRequired());
                Assert.assertEquals(250, child.getProps().getLength().intValue());
            }
        });
    }

    /**
     * 验证模型字段删除的场景
     */
    @Test
    public void modelDeleteColumn() {
        // 新增模型
        SaveStructNodeRequest modelCreateRequest = JsonUtil.fromJson(DataConst.CREATE_MODEL, SaveStructNodeRequest.class);
        DataStructNode model = modelCreateRequest.convertToEntity();
        dataStructNodeRepo.save(model);
        if (model.getProps().getType() == DataStructType.PERSIST) {
            ddlGenerator.update(model);
        }

        // 删除 field_text 字段
        model = dataStructNodeService.findModel(teamId, MODEL_KEY);
        List<DataStructFieldNode> fieldNodes = model.getChildren().stream().filter(field -> !field.getKey().equals("field_text")).collect(Collectors.toList());
        model.setChildren(fieldNodes);
        dataStructNodeRepo.save(model);
        if (model.getProps().getType() == DataStructType.PERSIST) {
            ddlGenerator.update(model, false);
        }

        DataStructNode result = dataStructNodeService.findModel(teamId, MODEL_KEY);
        Assert.assertNotNull(result);
        Assert.assertEquals(0, result.getChildren().stream().filter(x -> x.getKey().equals("field_text")).count());
    }

    @Test
    public void modelDelete() {
        // 创建模型
        SaveStructNodeRequest modelCreateRequest = JsonUtil.fromJson(DataConst.CREATE_MODEL, SaveStructNodeRequest.class);
        DataStructNode model = modelCreateRequest.convertToEntity();
        dataStructNodeRepo.save(model);
        if (model.getProps().getType() == DataStructType.PERSIST) {
            ddlGenerator.update(model);
        }

        // 获取数据源
        Map<String, PartitionDatasourceDto> tableDatasourceMap = businessDDLDataSourceFactory.getDDLDataSourceByModel(
            model.getTeamId(),
            model.getKey(),
            model.getProps().getTableName());
        Assert.assertEquals(1, tableDatasourceMap.size());

        // 删除模型
        model = dataStructNodeService.findModel(teamId, MODEL_KEY);
        dataStructNodeRepo.delete(model, false, false);
        if (model.getProps().getType() == DataStructType.PERSIST) {
            ddlGenerator.delete(model);
        }
        DataStructNode result = dataStructNodeService.findModel(teamId, MODEL_KEY);
        Assert.assertNull(result);

        tableDatasourceMap.forEach((tableName, datasourceInfo) -> {
            TableStructure tableStructure = ddlFetcher.getTableStructure(datasourceInfo, tableName);
            Assert.assertTrue(MapUtils.isEmpty(tableStructure.getColumns()));
            Assert.assertTrue(MapUtils.isEmpty(tableStructure.getIndexes()));
        });
    }
}
