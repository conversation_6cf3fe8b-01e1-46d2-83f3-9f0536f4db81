package io.terminus.trantor2.normal;

import io.terminus.trantor2.BaseTest;
import io.terminus.trantor2.Constants;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.model.SaveStructNodeRequest;
import io.terminus.trantor2.model.management.meta.service.DataStructNodeService;
import io.terminus.trantor2.model.runtime.api.dml.DataStructDataApi;
import io.terminus.trantor2.model.ts.impl.AnnotationTranServiceImpl;
import io.terminus.trantor2.model.ts.impl.ProgramTranServiceImpl;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 跨模块多库
 */
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT,
    properties = {
        "ELASTICSEARCH_HOST=127.0.0.1",
        "ELASTICSEARCH_PORT=9200",
        "MYSQL_HOST=127.0.0.1",
        "MYSQL_PORT=3306",
        "server.port=8081"
    })
//@ActiveProfiles("model")
public class MultiDbModelTransactionTest extends BaseTest {

    @Autowired
    private DataStructNodeService dataStructNodeService;

    @Autowired
    private DataStructDataApi dataApi;

    @Autowired
    private ProgramTranServiceImpl programTranService;

    @Autowired
    private AnnotationTranServiceImpl annotationTranService;

    @Test
    public void annotationTranTest() {
        // 模型准备
        prepareModelDefinition();

        // 上下文
        initContext();
        try {
            annotationTranService.testMulDbBatchAddWithTran(false);
        } catch (Exception e) {
            Assert.assertTrue(true);
        }

        // 数据校验
        annotationTranService.testMulDbBatchAddDataValidate(true);
    }


    private void prepareModelDefinition() {
        // 创建普通模型
        initContext();
        DataStructNode dataStructNode = mockModel(Constants.PERSIST_MODEL_ADD_STR);
        SaveStructNodeRequest request = createSaveRequest(dataStructNode);
        dataStructNodeService.save(request);

        initContext();
        DataStructNode dataStructNode2 = mockModel(Constants.PERSIST_MODEL_ADD_STR2);
        SaveStructNodeRequest request2 = createSaveRequest(dataStructNode2);
        dataStructNodeService.save(request2);
    }

    private void sleepSecond(int second) {
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
        }
    }

    @NotNull
    private SaveStructNodeRequest createSaveRequest(DataStructNode dataStructNode) {
        SaveStructNodeRequest request = new SaveStructNodeRequest();
        request.setTeamId(dataStructNode.getTeamId());
        request.setAlias(dataStructNode.getAlias());
        request.setName(dataStructNode.getName());
        request.setProps(dataStructNode.getProps());
        request.setChildren(dataStructNode.getChildren());
        request.setParentKey(dataStructNode.getParentKey());
        if (null != dataStructNode.getId()) {
            request.setId(dataStructNode.getId());
        }
        return request;
    }
}
