package io.terminus.trantor2.tx;

import io.terminus.trantor2.tx.mock.procode.MockActionComposite;
import io.terminus.trantor2.tx.mock.procode.MockOpenapiComposite;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 在配置了垂直分库时，业务线下代码开启事务场景
 * 共有两个数据源，其中test_a和test_c在trantor2_ceshi库中，test_b在trantor2库中
 */
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT,
    properties = {
        "ELASTICSEARCH_HOST=127.0.0.1",
        "ELASTICSEARCH_PORT=9200",
        "MYSQL_HOST=127.0.0.1",
        "MYSQL_PORT=3306",
        "server.port=8081",
        "trantor2.meta.event.type=redis",
        "UNIT_TEST_MODE=true"
    })
//@ActiveProfiles("model")
public class ProcodeMultiDbTxTest extends _MultiDbBaseTest {
    @Autowired
    protected MockOpenapiComposite mockOpenapiComposite;
    @Autowired
    protected MockActionComposite mockActionComposite;

    @Test
    public void testOpenapiWithoutTxInJvmCall() {
        mockOpenapiComposite.mockWithoutTxInJvmCall();

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundB1();
        mockOpenapiComposite.assertModelDataNotFoundB2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
    }

    @Test
    public void testOpenapiWithoutTxHttpCall() {
        mockOpenapiComposite.mockWithoutTxHttpCall();

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundB1();
        mockOpenapiComposite.assertModelDataNotFoundB2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
    }

    @Test
    public void testOpenapiWithoutTxInJvmCallHttpCall() {
        mockOpenapiComposite.mockWithoutTxInJvmCallHttpCall();

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundB1();
        mockOpenapiComposite.assertModelDataNotFoundB2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
    }

    @Test
    public void testOpenapiWithoutTxModelEngineCallA1() {
        mockOpenapiComposite.mockWithoutTxModelEngineCallA1();

        mockOpenapiComposite.assertModelDataFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundB1();
        mockOpenapiComposite.assertModelDataNotFoundB2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
    }

    @Test
    public void testOpenapiWithoutTxModelEngineCallA1A2C1C2() {
        mockOpenapiComposite.mockWithoutTxModelEngineCallA1A2C1C2();

        mockOpenapiComposite.assertModelDataFoundA1();
        mockOpenapiComposite.assertModelDataFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundB1();
        mockOpenapiComposite.assertModelDataNotFoundB2();
        mockOpenapiComposite.assertModelDataFoundC1();
        mockOpenapiComposite.assertModelDataFoundC2();
    }

    @Test
    public void testOpenapiWithoutTxModelEngineCallB1B2() {
        mockOpenapiComposite.mockWithoutTxModelEngineCallB1B2();

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataFoundB1();
        mockOpenapiComposite.assertModelDataFoundB2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
    }

    @Test
    public void testOpenapiWithoutTxBatchCreateA1A2C1C2() {
        mockOpenapiComposite.mockWithoutTxBatchCreateA1A2C1C2();

        mockOpenapiComposite.assertModelDataFoundA1();
        mockOpenapiComposite.assertModelDataFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundB1();
        mockOpenapiComposite.assertModelDataNotFoundB2();
        mockOpenapiComposite.assertModelDataFoundC1();
        mockOpenapiComposite.assertModelDataFoundC2();
    }

    @Test
    public void testOpenapiWithoutTxBatchCreateB1B2() {
        mockOpenapiComposite.mockWithoutTxBatchCreateB1B2();

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataFoundB1();
        mockOpenapiComposite.assertModelDataFoundB2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
    }

    @Test
    public void testOpenapiWithTxInJvmCall() {
        mockOpenapiComposite.mockWithTxInJvmCall();

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundB1();
        mockOpenapiComposite.assertModelDataNotFoundB2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
    }

    @Test
    public void testOpenapiWithTxHttpCall() {
        mockOpenapiComposite.mockWithTxHttpCall();

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundB1();
        mockOpenapiComposite.assertModelDataNotFoundB2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
    }

    @Test
    public void testOpenapiWithTxInJvmCallHttpCall() {
        mockOpenapiComposite.mockWithTxInJvmCallHttpCall();

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundB1();
        mockOpenapiComposite.assertModelDataNotFoundB2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
    }

    @Test
    public void testOpenapiWithTxModelEngineCallA1() {
        mockOpenapiComposite.mockWithTxModelEngineCallA1();

        mockOpenapiComposite.assertModelDataFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundB1();
        mockOpenapiComposite.assertModelDataNotFoundB2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
    }

    @Test
    public void testOpenapiWithTxModelEngineCallA1A2C1C2() {
        mockOpenapiComposite.mockWithTxModelEngineCallA1A2C1C2();

        mockOpenapiComposite.assertModelDataFoundA1();
        mockOpenapiComposite.assertModelDataFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundB1();
        mockOpenapiComposite.assertModelDataNotFoundB2();
        mockOpenapiComposite.assertModelDataFoundC1();
        mockOpenapiComposite.assertModelDataFoundC2();
    }

    @Test
    public void testOpenapiWithTxModelEngineCallB1B2() {
        mockOpenapiComposite.mockWithTxModelEngineCallB1B2();

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataFoundB1();
        mockOpenapiComposite.assertModelDataFoundB2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
    }

    @Test
    public void testOpenapiWithTxBatchCreateA1A2C1C2() {
        mockOpenapiComposite.mockWithTxBatchCreateA1A2C1C2();

        mockOpenapiComposite.assertModelDataFoundA1();
        mockOpenapiComposite.assertModelDataFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundB1();
        mockOpenapiComposite.assertModelDataNotFoundB2();
        mockOpenapiComposite.assertModelDataFoundC1();
        mockOpenapiComposite.assertModelDataFoundC2();
    }

    @Test
    public void testOpenapiWithTxBatchCreateB1B2() {
        mockOpenapiComposite.mockWithTxBatchCreateB1B2();

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataFoundB1();
        mockOpenapiComposite.assertModelDataFoundB2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
    }

    @Test
    public void testOpenapiCommitWithoutTx1() {
        mockOpenapiComposite.mockWithoutTxCreateA1A2C1C2();

        mockOpenapiComposite.assertModelDataFoundA1();
        mockOpenapiComposite.assertModelDataFoundA2();
        mockOpenapiComposite.assertModelDataFoundC1();
        mockOpenapiComposite.assertModelDataFoundC2();
    }

    @Test
    public void testOpenapiCommitWithoutTx2() {
        mockOpenapiComposite.mockWithoutTxCreateB1B2();

        mockOpenapiComposite.assertModelDataFoundB1();
        mockOpenapiComposite.assertModelDataFoundB2();
    }

    @Test
    public void testOpenapiRollbackWithoutTx1() {
        try {
            mockOpenapiComposite.mockWithoutTxCreateA1A2A1C1C2();
        } catch (Exception e) {
            e.printStackTrace();
        }

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
    }

    @Test
    public void testOpenapiRollbackWithoutTx2() {
        try {
            mockOpenapiComposite.mockWithoutTxCreateA1A2B1B2();
        } catch (Exception e) {
            e.printStackTrace();
        }

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundB1();
        mockOpenapiComposite.assertModelDataNotFoundB2();
    }

    @Test
    public void testOpenapiRollbackWithoutTx3() {
        try {
            mockOpenapiComposite.mockWithoutTxBatchCreateA1A2C1C2B1B2();
        } catch (Exception e) {
            e.printStackTrace();
        }

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
        mockOpenapiComposite.assertModelDataNotFoundB1();
        mockOpenapiComposite.assertModelDataNotFoundB2();
    }

    @Test
    public void testOpenapiCommitWithTx1() {
        mockOpenapiComposite.mockWithTxCreateA1A2C1C2();

        mockOpenapiComposite.assertModelDataFoundA1();
        mockOpenapiComposite.assertModelDataFoundA2();
        mockOpenapiComposite.assertModelDataFoundC1();
        mockOpenapiComposite.assertModelDataFoundC2();
    }

    @Test
    public void testOpenapiCommitWithTx2() {
        mockOpenapiComposite.mockWithTxCreateB1B2();

        mockOpenapiComposite.assertModelDataFoundB1();
        mockOpenapiComposite.assertModelDataFoundB2();
    }

    @Test
    public void testOpenapiCommitWithTxFirst1() {
        mockOpenapiComposite.mockWithTxFirstCreateA1A2C1C2();

        mockOpenapiComposite.assertModelDataFoundA1();
        mockOpenapiComposite.assertModelDataFoundA2();
        mockOpenapiComposite.assertModelDataFoundC1();
        mockOpenapiComposite.assertModelDataFoundC2();
    }

    @Test
    public void testOpenapiCommitWithoutTxFirst1() {
        mockOpenapiComposite.mockWithoutTxFirstCreateA1A2C1C2();

        mockOpenapiComposite.assertModelDataFoundA1();
        mockOpenapiComposite.assertModelDataFoundA2();
        mockOpenapiComposite.assertModelDataFoundC1();
        mockOpenapiComposite.assertModelDataFoundC2();
    }

    @Test
    public void testOpenapiCommitWithTxFirstA1A2C1C2CRU1() {
        mockOpenapiComposite.mockWithTxFirstA1A2C1C2CRU();

        mockOpenapiComposite.assertModelDataFoundA1();
        mockOpenapiComposite.assertModelDataFoundA2();
        mockOpenapiComposite.assertModelDataFoundC1();
        mockOpenapiComposite.assertModelDataFoundC2();
    }

    @Test
    public void testOpenapiCommitWithTxFirstA1A2C1C2CRUD1() {
        mockOpenapiComposite.mockWithTxFirstA1A2C1C2CRUD();

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
    }

    @Test
    public void testOpenapiCommitWithoutTxFirstA1A2C1C2CRU1() {
        mockOpenapiComposite.mockWithoutTxFirstA1A2C1C2CRU();

        mockOpenapiComposite.assertModelDataFoundA1();
        mockOpenapiComposite.assertModelDataFoundA2();
        mockOpenapiComposite.assertModelDataFoundC1();
        mockOpenapiComposite.assertModelDataFoundC2();
    }

    @Test
    public void testOpenapiCommitWithoutTxFirstA1A2C1C2CRUD1() {
        mockOpenapiComposite.mockWithoutTxFirstA1A2C1C2CRUD();

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
    }

    @Test
    public void testOpenapiRollbackWithTx1() {
        try {
            mockOpenapiComposite.mockWithTxCreateA1A2A1C1C2();
        } catch (Exception e) {
            e.printStackTrace();
        }

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
    }

    @Test
    public void testOpenapiRollbackWithTx2() {
        try {
            mockOpenapiComposite.mockWithTxCreateA1A2B1B2();
        } catch (Exception e) {
            e.printStackTrace();
        }

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundB1();
        mockOpenapiComposite.assertModelDataNotFoundB2();
    }

    @Test
    public void testOpenapiRollbackWithTx3() {
        try {
            mockOpenapiComposite.mockWithTxBatchCreateA1A2C1C2B1B2();
        } catch (Exception e) {
            e.printStackTrace();
        }

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
        mockOpenapiComposite.assertModelDataNotFoundB1();
        mockOpenapiComposite.assertModelDataNotFoundB2();
    }

    @Test
    public void testOpenapiTimeout1() {
        try {
            mockOpenapiComposite.mockTxTimeout1();
        } catch (Exception e) {
            e.printStackTrace();
        }

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
    }

    @Test
    public void testOpenapiTimeout2() {
        try {
            mockOpenapiComposite.mockTxTimeout2();
        } catch (Exception e) {
            e.printStackTrace();
        }

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
    }

    @Test
    public void testOpenapiTimeout3() {
        try {
            mockOpenapiComposite.mockTxTimeout3();
        } catch (Exception e) {
            e.printStackTrace();
        }

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
    }

    @Test
    public void testOpenapiTimeout4() {
        try {
            mockOpenapiComposite.mockTxTimeout4();
        } catch (Exception e) {
            e.printStackTrace();
        }

        mockOpenapiComposite.assertModelDataNotFoundA1();
        mockOpenapiComposite.assertModelDataNotFoundA2();
        mockOpenapiComposite.assertModelDataNotFoundC1();
        mockOpenapiComposite.assertModelDataNotFoundC2();
    }


    @Test
    public void testActionWithoutTxCreateA1A2C1C2() {
        mockActionComposite.mockWithoutTxCreateA1A2C1C2(null);

        mockActionComposite.assertModelDataFoundA1();
        mockActionComposite.assertModelDataFoundA2();
        mockActionComposite.assertModelDataFoundC1();
        mockActionComposite.assertModelDataFoundC2();
    }

    @Test
    public void testContinuation() {
        testOpenapiRollbackWithoutTx1();
        testOpenapiCommitWithoutTx1();
        testOpenapiCommitWithoutTx2();
    }

}
