package io.terminus.trantor2.ddl;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.ddl.data.DataConst;
import io.terminus.trantor2.model.management.ddl.IndexTypeEnum;
import io.terminus.trantor2.model.management.ddl.SqlInfo;
import io.terminus.trantor2.model.management.ddl.builder.MySQLDDLExecutor;
import io.terminus.trantor2.model.management.meta.consts.FieldType;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldProperties;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.model.SaveStructNodeRequest;
import io.terminus.trantor2.test.tool.mysql.MysqlSpringTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(SpringRunner.class)
@Slf4j
public class MySQLDDLExecutorTest implements MysqlSpringTest {
    private final static String DRIVER = "com.mysql.cj.jdbc.Driver";
    private final static String TABLE_NAME = "test_table";
    DriverManagerDataSource dataSource = new DriverManagerDataSource();
    SqlInfo sqlInfo = new SqlInfo();
    String createTableSQL = " create table `" + TABLE_NAME + "`(`field_text`varchar(256)  not null ," +
        "`field_number`decimal(65,30)  null ," +
        "`field_multi_text`mediumtext null ," +
        "`field_date`datetime null ," +
        "`field_email`varchar(256)  null ," +
        "`field_time`time(3)  null ," +
        "`field_enum`varchar(256)  null ," +
        "`field_bool`tinyint(1)  null ," +
        "`field_attachment`text null ," +
        "`id`bigint(20)  not null  AUTO_INCREMENT ," +
        "`created_at`datetime not null ," +
        "`updated_at`datetime not null ," +
        "`version`bigint(20)  not null  default 0," +
        "`deleted`bigint(20)  not null  default 0," +
        "`origin_org_id`bigint(20)  null  default 0, " +
        "primary key (`id`)) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;";

    @Before
    public void before() {
        //为每个测试方法配置docker生成的数据源
        dataSource.setDriverClassName(DRIVER);
        dataSource.setUrl(mysqlContainer.getJdbcUrl());
        dataSource.setUsername(USER_NAME);
        dataSource.setPassword(PASSWORD);
    }

    @After
    public void after() {
        if (sqlInfo.getExecuteSql().isEmpty()) {
            return;
        } else if (!sqlInfo.getExecuteSql().get(0).contains("drop table")) {
            MySQLDDLExecutor mySQLDDLExecutor = new MySQLDDLExecutor();
            sqlInfo = mySQLDDLExecutor.dropTable(dataSource, TABLE_NAME, true);
        }
    }

    @Test
    public void createAndDropTableTest() {
        SaveStructNodeRequest modelCreateRequest = JsonUtil.fromJson(DataConst.CREATE_MODEL, SaveStructNodeRequest.class);
        DataStructNode model = modelCreateRequest.convertToEntity();
        MySQLDDLExecutor mySQLDDLExecutor = new MySQLDDLExecutor();
        // 创建表
        sqlInfo = mySQLDDLExecutor.createTable(dataSource, model, TABLE_NAME, true);
        assertEquals(createTableSQL,
            sqlInfo.getExecuteSql().get(0));
        //删表
        sqlInfo = mySQLDDLExecutor.dropTable(dataSource, TABLE_NAME, true);
        assertEquals("drop table `" + TABLE_NAME + "`;",
            sqlInfo.getExecuteSql().get(0));
    }

    @Test
    public void createIndexAndRemoveTest() {
        SaveStructNodeRequest modelCreateRequest = JsonUtil.fromJson(DataConst.CREATE_MODEL, SaveStructNodeRequest.class);
        DataStructNode model = modelCreateRequest.convertToEntity();
        MySQLDDLExecutor mySQLDDLExecutor = new MySQLDDLExecutor();
        sqlInfo = mySQLDDLExecutor.createTable(dataSource, model, TABLE_NAME, true);
        List<DataStructFieldNode> originalList = model.getChildren();
        List<DataStructFieldNode> Index = originalList.stream()
            .filter(element -> element.getKey().equals("field_text"))
            .collect(Collectors.toList());
        //为field_text添加index索引
        sqlInfo = mySQLDDLExecutor.createIndex(dataSource, TABLE_NAME, "test_index", IndexTypeEnum.INDEX, Index, true);
        assertEquals("create index `test_index` on `" + TABLE_NAME + "`(`field_text`(191) ASC);",
            sqlInfo.getExecuteSql().get(0));
        //删除index索引
        sqlInfo = mySQLDDLExecutor.removeIndex(dataSource, TABLE_NAME, "test_index", true);
        assertEquals("drop index `test_index` on `" + TABLE_NAME + "`;",
            sqlInfo.getExecuteSql().get(0));
    }

    @Test
    public void createUniqueAndRemoveTest() {
        SaveStructNodeRequest modelCreateRequest = JsonUtil.fromJson(DataConst.CREATE_MODEL, SaveStructNodeRequest.class);
        DataStructNode model = modelCreateRequest.convertToEntity();
        MySQLDDLExecutor mySQLDDLExecutor = new MySQLDDLExecutor();
        sqlInfo = mySQLDDLExecutor.createTable(dataSource, model, TABLE_NAME, true);
        List<DataStructFieldNode> originalList = model.getChildren();
        List<DataStructFieldNode> Index = originalList.stream()
            .filter(element -> element.getKey().equals("field_text"))
            .collect(Collectors.toList());
        //为field_text添加unique索引
        sqlInfo = mySQLDDLExecutor.createIndex(dataSource, TABLE_NAME, "test_index", IndexTypeEnum.UNIQUE, Index, true);
        assertEquals("create unique index `test_index` on `" + TABLE_NAME + "`(`field_text`(191) ASC);",
            sqlInfo.getExecuteSql().get(0));
        //删除unique索引
        sqlInfo = mySQLDDLExecutor.removeIndex(dataSource, TABLE_NAME, "test_index", true);
        assertEquals("drop index `test_index` on `" + TABLE_NAME + "`;",
            sqlInfo.getExecuteSql().get(0));
    }

    @Test
    public void createRelationColumnTest() {
        SaveStructNodeRequest modelCreateRequest = JsonUtil.fromJson(DataConst.CREATE_MODEL, SaveStructNodeRequest.class);
        DataStructNode model = modelCreateRequest.convertToEntity();
        MySQLDDLExecutor mySQLDDLExecutor = new MySQLDDLExecutor();
        sqlInfo = mySQLDDLExecutor.createTable(dataSource, model, TABLE_NAME, true);
        //添加relation_field关联列
        sqlInfo = mySQLDDLExecutor.createRelationColumn(dataSource, TABLE_NAME, "relation_field", true);
        assertEquals("alter table `" + TABLE_NAME + "` add `relation_field` bigint null;",
            sqlInfo.getExecuteSql().get(0));
    }

    @Test
    public void createAndDeleteColumnTest() {
        SaveStructNodeRequest modelCreateRequest = JsonUtil.fromJson(DataConst.CREATE_MODEL, SaveStructNodeRequest.class);
        DataStructNode model = modelCreateRequest.convertToEntity();
        MySQLDDLExecutor mySQLDDLExecutor = new MySQLDDLExecutor();
        sqlInfo = mySQLDDLExecutor.createTable(dataSource, model, TABLE_NAME, true);
        //新增及删除各种类型的列
        List<FieldType> list = new ArrayList<>();
        Collections.addAll(list,
            FieldType.DATE,
            FieldType.TEXT,
            FieldType.NUMBER,
            FieldType.ATTACHMENT,
            FieldType.ENUM,
            FieldType.OBJECT,
            FieldType.TIME,
            FieldType.BOOL,
            FieldType.MULTI_TEXT,
            FieldType.EMAIL);
        list.forEach(this::createAndDeleteColumn);
    }

    @Test
    public void updateColumnTest() {
        SaveStructNodeRequest modelCreateRequest = JsonUtil.fromJson(DataConst.CREATE_MODEL, SaveStructNodeRequest.class);
        DataStructNode model = modelCreateRequest.convertToEntity();
        MySQLDDLExecutor mySQLDDLExecutor = new MySQLDDLExecutor();
        sqlInfo = mySQLDDLExecutor.createTable(dataSource, model, TABLE_NAME, true);
        DataStructFieldNode oldfield = createDataStructFieldNode(FieldType.NUMBER);
        DataStructFieldNode field = createDataStructFieldNode(FieldType.TEXT, 100);
        mySQLDDLExecutor.createColumn(dataSource, TABLE_NAME, oldfield, "id", true);
        //更新列
        sqlInfo = mySQLDDLExecutor.updateColumn(dataSource, TABLE_NAME, field, "id", oldfield, true);
        assertEquals("alter table `" + TABLE_NAME + "` change column `test_columnName_NUMBER`" +
                "  `test_columnName_TEXT`  varchar (100)  not null  after  `id` ;",
            sqlInfo.getExecuteSql().get(0));
    }

    @Test
    public void renameColumnTest() {
        SaveStructNodeRequest modelCreateRequest = JsonUtil.fromJson(DataConst.CREATE_MODEL, SaveStructNodeRequest.class);
        DataStructNode model = modelCreateRequest.convertToEntity();
        MySQLDDLExecutor mySQLDDLExecutor = new MySQLDDLExecutor();
        sqlInfo = mySQLDDLExecutor.createTable(dataSource, model, TABLE_NAME, true);
        DataStructFieldNode field = createDataStructFieldNode(FieldType.NUMBER);
        mySQLDDLExecutor.createColumn(dataSource, TABLE_NAME, field, "id", true);
        DataStructFieldProperties props = field.getProps();
        String oldName = props.getColumnName();
        props.setColumnName("newColumnName");
        field.setProps(props);
        //重命名列
        sqlInfo = mySQLDDLExecutor.renameColumn(dataSource, TABLE_NAME, oldName, "newColumnName", field, true);
        assertEquals("alter table `" + TABLE_NAME + "` change column " +
                "`test_columnName_NUMBER` `newColumnName` decimal(65, 30);",
            sqlInfo.getExecuteSql().get(0));
    }

    @Test
    public void createTableByDdlTest() {
        //验证使用ddl建表和request建表并无差异
        MySQLDDLExecutor mySQLDDLExecutor = new MySQLDDLExecutor();
        sqlInfo = mySQLDDLExecutor.createTableByDdl(dataSource, createTableSQL, true);
        SaveStructNodeRequest modelCreateRequest = JsonUtil.fromJson(DataConst.CREATE_MODEL, SaveStructNodeRequest.class);
        DataStructNode model = modelCreateRequest.convertToEntity();
        SqlInfo compareSqlInfo = mySQLDDLExecutor.createTable(dataSource, model, TABLE_NAME, false);
        assertEquals(sqlInfo.getExecuteSql().get(0),compareSqlInfo.getExecuteSql().get(0));
    }

    private DataStructFieldNode createDataStructFieldNode(FieldType type) {
        DataStructFieldNode fieldNode = new DataStructFieldNode();
        DataStructFieldProperties properties = new DataStructFieldProperties();
        properties.setFieldType(type);
        properties.setCompositeKey(true);
        properties.setRequired(true);
        properties.setIsSystemField(false);
        properties.setColumnName("test_columnName_" + type);
        fieldNode.setKey("test_key" + type);
        fieldNode.setName("test_name" + type);
        fieldNode.setProps(properties);
        return fieldNode;
    }

    private DataStructFieldNode createDataStructFieldNode(FieldType type, int length) {
        DataStructFieldNode fieldNode = new DataStructFieldNode();
        DataStructFieldProperties properties = new DataStructFieldProperties();
        properties.setFieldType(type);
        properties.setCompositeKey(true);
        properties.setRequired(true);
        properties.setIsSystemField(false);
        properties.setLength(length);
        properties.setColumnName("test_columnName_" + type);
        fieldNode.setKey("test_key" + type);
        fieldNode.setName("test_name" + type);
        fieldNode.setProps(properties);
        return fieldNode;
    }

    private void createAndDeleteColumn(FieldType type) {
        MySQLDDLExecutor mySQLDDLExecutor = new MySQLDDLExecutor();
        DataStructFieldNode fieldNode;
        String preType = "alter table `" + TABLE_NAME + "` add column `test_columnName_";
        String afterType = "  not null  after  `id` ;";
        String typeValue;
        switch (type) {
            case TEXT:
            case ENUM:
            case EMAIL:
            case OBJECT:
                fieldNode = createDataStructFieldNode(type, 50);
                typeValue = type + "`  varchar (50)";
                break;
            case NUMBER:
                fieldNode = createDataStructFieldNode(type);
                typeValue = type + "`  bigint";
                break;
            case ATTACHMENT:
                fieldNode = createDataStructFieldNode(type);
                typeValue = type + "`  text";
                break;
            case TIME:
                fieldNode = createDataStructFieldNode(type);
                typeValue = type + "`  time";
                break;
            case DATE:
                fieldNode = createDataStructFieldNode(type);
                typeValue = type + "`  datetime";
                break;
            case BOOL:
                fieldNode = createDataStructFieldNode(type);
                typeValue = type + "`  tinyint";
                break;
            case MULTI_TEXT:
                fieldNode = createDataStructFieldNode(type);
                typeValue = type + "`  mediumtext";
                break;
            default:
                log.error("type is null");
                return;
        }
        log.info("start to add column whose type is " + type);
        sqlInfo = mySQLDDLExecutor.createColumn(dataSource, TABLE_NAME, fieldNode, "id", true);
        assertEquals(preType + typeValue + afterType,
            sqlInfo.getExecuteSql().get(0));
        log.info("done");
        log.info("start to drop column whose type is " + type);
        sqlInfo = mySQLDDLExecutor.deleteColumn(dataSource, TABLE_NAME, fieldNode.getProps().getColumnName(), true);
        assertEquals("alter table `" + TABLE_NAME + "` drop `test_columnName_" + type + "`;",
            sqlInfo.getExecuteSql().get(0));
        log.info("done");
    }
}
