package io.terminus.trantor2.partition.singleDB;

import io.terminus.trantor2.partition.TablePartitionTest;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import java.util.Map;

/**
 * 跨模块单库
 */
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT,
    properties = {
        "ELASTICSEARCH_HOST=127.0.0.1",
        "ELASTICSEARCH_PORT=9200",
        "MYSQL_HOST=127.0.0.1",
        "MYSQL_PORT=3306",
        "server.port=8081",
        "trantor2.meta.event.type=redis",
        "UNIT_TEST_MODE=true"
    })
//@ActiveProfiles("model")
public class SingleDBTablePartitionTest extends TablePartitionTest {
    protected void beforeCustum() {
        // 分表配置，test_a_p模型分表
        jdbcTemplate.execute("INSERT INTO `environment_configuration` (`id`, `created_at`, `created_by`, `updated_at`, `updated_by`, `version`, `config`, `module_key`, `team_id`, `type`)\n" +
            "VALUES\n" +
            "\t(1111, '2023-09-28 23:25:58.333000', 435423901065925, '2023-09-28 23:25:58.333000', 435423901065925, 0, '{\\\"shardingEnable\\\": true, \\\"shardingNum\\\": 5}', 'testAdd$test_a_p', 1, 'Model_Sharding_Config')");
    }

    protected void afterCustum() {
        businessDMLDataSourceHolderManager.clean();
    }

    public void prepareModelDefinitionCustum() {

    }

    /**
     * JOIN查询
     * 因为sharding不支持跨库的表关联，所以分多个库的时候，跨多个库的表关联结果会不一致
     */
    @Test
    public void modelJoin() {
        // 模型准备
        prepareModelDefinition();

        // 期望test_a_p_alias模型这条数据分布在test_a_p_1表
        _innerInitModelPartitionData();

        // 这里单纯测试sharding sql的可行性
        JdbcTemplate jdbcTemplate1 = new JdbcTemplate(businessDMLDataSourceFactory.getDataSourceByTeamIdAndModule(teamId, testAdd_module));
        List<Map<String, Object>> resp = jdbcTemplate1.queryForList("select ap.code,a.id from test_a_p as ap left join test_a as a on ap.id = a.id where ap.id=11001");
        Assert.assertTrue(resp.size() == 1);

        mockService.createByModelEngine(test_a_alias, 11004L, "zzz");
        resp = jdbcTemplate1.queryForList("select ap.code,a.id from test_a_p as ap left join test_a as a on ap.id = a.id where ap.id in (11001,11004)");
        Assert.assertTrue(resp.size() == 2);

        resp = jdbcTemplate1.queryForList("select ap.code,a.id from test_a as a left join test_a_p as ap on ap.id = a.id where ap.id in (11001,11004)");
        Assert.assertTrue(resp.size() == 2);

        // 测试数据坐标的正确性
        // trantor2_ceshi.test_a_p_0
        String coordiate = dataSourceDebugService.getDataCoordinate(teamCode, test_a_p_alias, "order_id", 95);
        Assert.assertEquals("trantor2_ceshi.test_a_p_0", coordiate);
        // trantor2_ceshi_0.test_a_p_2
        coordiate = dataSourceDebugService.getDataCoordinate(teamCode, test_a_p_alias, "id", 11002L);
        Assert.assertEquals("trantor2_ceshi.test_a_p_2", coordiate);
        // trantor2_ceshi_1.test_a_p_4
        coordiate = dataSourceDebugService.getDataCoordinate(teamCode, test_a_p_alias, "code", "100411");
        Assert.assertEquals("trantor2_ceshi.test_a_p_4", coordiate);
    }
}
