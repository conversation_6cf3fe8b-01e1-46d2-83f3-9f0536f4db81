package io.terminus.trantor2.tx.partition;

import io.terminus.trantor2.datasource.manager.BusinessDDLDataSourceHolderManager;
import io.terminus.trantor2.datasource.manager.BusinessDMLDataSourceHolderManager;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.domain.sharding.ShardingConfig;
import io.terminus.trantor2.model.management.meta.domain.sharding.ShardingField;
import io.terminus.trantor2.model.management.meta.domain.sharding.type.ShardingType;
import io.terminus.trantor2.tx.ProcodeSingleDbTxTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

/**
 * 扩展ProcodeSingleDbTxTest类中的事务单侧
 * 增加test_a模型开启分表，但是分表位于单个库内
 */
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT,
    properties = {
        "ELASTICSEARCH_HOST=127.0.0.1",
        "ELASTICSEARCH_PORT=9200",
        "MYSQL_HOST=127.0.0.1",
        "MYSQL_PORT=3306",
        "server.port=8081",
        "trantor2.meta.event.type=redis",
        "UNIT_TEST_MODE=true"
    })
//@ActiveProfiles("model")
public class MulTable_ProcodeSingleDbTxTest extends ProcodeSingleDbTxTest {
    @Autowired
    protected BusinessDMLDataSourceHolderManager businessDMLDataSourceHolderManager;
    @Autowired
    protected BusinessDDLDataSourceHolderManager businessDDLDataSourceHolderManager;

    protected void initCustum() {
        businessDMLDataSourceHolderManager.clean();
        businessDDLDataSourceHolderManager.clean();

        // 分表配置，test_a模型分表
        jdbcTemplate.execute("INSERT INTO `environment_configuration` (`id`, `created_at`, `created_by`, `updated_at`, `updated_by`, `version`, `config`, `module_key`, `team_id`, `type`)\n" +
            "VALUES\n" +
            "\t(1111, '2023-09-28 23:25:58.333000', 435423901065925, '2023-09-28 23:25:58.333000', 435423901065925, 0, '{\\\"shardingEnable\\\": true, \\\"shardingNum\\\": 5}', 'testAdd$test_a', 1, 'Model_Sharding_Config')");
    }

    /**
     * 这里扩展下模型的分表信息，设置test_a使用id尾号分表
     *
     * @param dataStructNode
     */
    protected void modelMetaExpand(DataStructNode dataStructNode) {
        if (dataStructNode.getKey().equals("testAdd$test_a")) {
            ShardingConfig shardingConfig = new ShardingConfig();
            shardingConfig.setEnabled(true);
            shardingConfig.setShardingSuffixLength(1);
            List<ShardingField> fieldList = new ArrayList<>();
            ShardingField shardingField = new ShardingField();
            shardingField.setField("id");
            shardingField.setBusinessKey(true);
            shardingField.setShardingType(ShardingType.HASH);
            fieldList.add(shardingField);

            ShardingField shardingField2 = new ShardingField();
            shardingField2.setField("id");
            shardingField2.setShardingType(ShardingType.SUFFIX);
            fieldList.add(shardingField2);

            shardingConfig.setShardingFields(fieldList);
            dataStructNode.getProps().setShardingConfig(shardingConfig);
        }
    }


}
