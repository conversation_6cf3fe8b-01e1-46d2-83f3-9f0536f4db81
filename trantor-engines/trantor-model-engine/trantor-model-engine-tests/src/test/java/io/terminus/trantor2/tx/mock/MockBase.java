package io.terminus.trantor2.tx.mock;

import com.google.common.collect.Lists;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.condition.ConditionItem;
import io.terminus.trantor2.condition.ConditionItems;
import io.terminus.trantor2.model.common.model.request.*;
import io.terminus.trantor2.model.runtime.api.dml.DataStructDataApi;
import io.terminus.trantor2.model.runtime.api.dml.SqlExecuteApi;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static io.terminus.trantor2.tx._SingleDbBaseTest.*;

/**
 * 模拟调用模型引擎crud api（需要覆盖所有方法，避免被测场景遗漏）
 */
@Service
@Slf4j
public class MockBase {
    @Autowired
    private DataStructDataApi dataStructDataApi;
    @Autowired
    private SqlExecuteApi sqlExecuteApi;

    public Response<Long> createByModelEngine(String modelAlias, long id, String name) {
        return createByModelEngine(modelAlias, id, null, null, name);
    }

    public Response<Long> createByModelEngine(String modelAlias, long id, String code, String orderId, String name) {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("id", id);
        dataMap.put("name", name);
        if (null != code) {
            dataMap.put("code", code);
        }

        if (null != orderId) {
            dataMap.put("orderId", orderId);
        }

        CreateObject createObject = new CreateObject();
        createObject.setTeamId(teamId);
        createObject.setModelAlias(modelAlias);
        createObject.setData(dataMap);
        Response<Long> response = dataStructDataApi.create(createObject);
        log.info("create response: {}", response);
        return response;
    }

    public Response<List<Long>> batchCreateByModelEngine(String modelAlias) {
        List<Map<String, Object>> mapList = Lists.newArrayList(1L, 2L).stream()
            .map(id -> {
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("id", id);
                if (test_a_alias.equals(modelAlias)) {
                    dataMap.put("name", "test_a_" + id);
                } else if (test_b_alias.equals(modelAlias)) {
                    dataMap.put("name", "test_b_" + id);
                } else if (test_c_alias.equals(modelAlias)) {
                    dataMap.put("name", "test_c_" + id);
                } else {
                    throw new RuntimeException("currently not supported modelAlias: " + modelAlias);
                }
                return dataMap;
            })
            .collect(Collectors.toList());
        BatchCreateObject batchCreateObject = new BatchCreateObject();
        batchCreateObject.setTeamId(teamId);
        batchCreateObject.setModelAlias(modelAlias);
        batchCreateObject.setDataList(mapList);
        Response<List<Long>> listResponse = dataStructDataApi.batchCreate(batchCreateObject);
        log.info("batchCreate response: {}", listResponse);
        return listResponse;
    }

    public Response<Void> updateByIdByModelEngine(String modelAlias, long id, String name) {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("id", id);
        dataMap.put("name", name);
        UpdateObject updateObject = new UpdateObject();
        updateObject.setTeamId(teamId);
        updateObject.setModelAlias(modelAlias);
        updateObject.setData(dataMap);
        Response<Void> response = dataStructDataApi.updateById(updateObject);
        log.info("updateById response: {}", response);
        return response;
    }

    public Response<Void> batchUpdateByIdByModelEngine(String modelAlias) {
        List<Map<String, Object>> mapList = Lists.newArrayList(1L, 2L).stream()
            .map(id -> {
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("id", id);
                if (test_a_alias.equals(modelAlias)) {
                    dataMap.put("name", "test_a_" + id + "bu");
                } else if (test_b_alias.equals(modelAlias)) {
                    dataMap.put("name", "test_b_" + id + "bu");
                } else if (test_c_alias.equals(modelAlias)) {
                    dataMap.put("name", "test_c_" + id + "bu");
                } else {
                    throw new RuntimeException("currently not supported modelAlias: " + modelAlias);
                }
                return dataMap;
            })
            .collect(Collectors.toList());
        BatchUpdateObject updateObject = new BatchUpdateObject();
        updateObject.setTeamId(teamId);
        updateObject.setModelAlias(modelAlias);
        updateObject.setDataList(mapList);
        Response<Void> response = dataStructDataApi.batchUpdateById(updateObject);
        log.info("batchUpdateById response: {}", response);
        return response;
    }

    public Response<Void> deleteByIdByModelEngine(String modelAlias, long id) {
        DeleteObject deleteObject = new DeleteObject();
        deleteObject.setTeamId(teamId);
        deleteObject.setModelAlias(modelAlias);
        deleteObject.setIds(Lists.newArrayList(id));
        Response<Void> response = dataStructDataApi.deleteById(deleteObject);
        log.info("deleteById response: {}", response);
        return response;
    }

    public Response<Map<String, Object>> findByIdByModelEngine(String modelAlias, long id) {
        IdQueryRequest idQueryRequest = new IdQueryRequest();
        idQueryRequest.setTeamId(teamId);
        idQueryRequest.setModelAlias(modelAlias);
        List<Select> selectList = new ArrayList<>();
        Select select = new Select();
        select.setField("id");
        selectList.add(select);
        idQueryRequest.setId(id);
        idQueryRequest.setSelect(selectList);
        Response<Map<String, Object>> response = dataStructDataApi.findById(idQueryRequest);
        log.info("findById response: {}", response);
        return response;
    }

    public Response<Map<String, Object>> findOneByModelEngine(String modelAlias, long id) {
        QueryRequest queryRequest = new QueryRequest();
        queryRequest.setTeamId(teamId);
        queryRequest.setModelAlias(modelAlias);
        List<Select> selectList = new ArrayList<>();
        Select select = new Select();
        select.setField("id");
        selectList.add(select);
        queryRequest.setSelect(selectList);
        Map<String, ConditionItem> conditions = new HashMap<>();
        ConditionItem conditionItem = new ConditionItem();
        conditionItem.setValue(id);
        conditions.put("id", conditionItem);
        ConditionItems conditionItems = new ConditionItems();
        conditionItems.setConditions(conditions);
        queryRequest.setConditionItems(conditionItems);
        Response<Map<String, Object>> response = dataStructDataApi.findOne(queryRequest);
        log.info("findOne response: {}", response);
        return response;
    }

    public Response<List<Map<String, Object>>> findByModelEngine(String modelAlias, long id) {
        QueryRequest queryRequest = new QueryRequest();
        queryRequest.setTeamId(teamId);
        queryRequest.setModelAlias(modelAlias);
        List<Select> selectList = new ArrayList<>();
        Select select = new Select();
        select.setField("id");
        selectList.add(select);
        queryRequest.setSelect(selectList);
        Map<String, ConditionItem> conditions = new HashMap<>();
        ConditionItem conditionItem = new ConditionItem();
        conditionItem.setValue(id);
        conditions.put("id", conditionItem);
        ConditionItems conditionItems = new ConditionItems();
        conditionItems.setConditions(conditions);
        queryRequest.setConditionItems(conditionItems);
        Response<List<Map<String, Object>>> response = dataStructDataApi.find(queryRequest);
        log.info("find response: {}", response);
        return response;
    }

    public void assertModelDataFoundA1() {
        assertModelDataFound(test_a_alias, 1L);
    }

    public void assertModelDataNotFoundA1() {
        assertModelDataNotFound(test_a_alias, 1L);
    }

    public void assertModelDataFoundA2() {
        assertModelDataFound(test_a_alias, 2L);
    }

    public void assertModelDataNotFoundA2() {
        assertModelDataNotFound(test_a_alias, 2L);
    }

    public void assertModelDataFoundC1() {
        assertModelDataFound(test_c_alias, 1L);
    }

    public void assertModelDataNotFoundC1() {
        assertModelDataNotFound(test_c_alias, 1L);
    }

    public void assertModelDataFoundC2() {
        assertModelDataFound(test_c_alias, 2L);
    }

    public void assertModelDataNotFoundC2() {
        assertModelDataNotFound(test_c_alias, 2L);
    }

    public void assertModelDataFoundB1() {
        assertModelDataFound(test_b_alias, 1L);
    }

    public void assertModelDataNotFoundB1() {
        assertModelDataNotFound(test_b_alias, 1L);
    }

    public void assertModelDataFoundB2() {
        assertModelDataFound(test_b_alias, 2L);
    }

    public void assertModelDataNotFoundB2() {
        assertModelDataNotFound(test_b_alias, 2L);
    }

    public void assertModelDataFound(String modelAlias, long id) {
        findModelDataByModelEngineWithAssert(modelAlias, id, true);
    }

    private void assertModelDataNotFound(String modelAlias, long id) {
        findModelDataByModelEngineWithAssert(modelAlias, id, false);
    }

    private void findModelDataByModelEngineWithAssert(String modelAlias, long id, boolean assertFound) {
        Response<Map<String, Object>> response = findByIdByModelEngine(modelAlias, id);
        if (assertFound) {
            Assert.assertEquals(String.valueOf(response.getData().get("id")), "" + id);
        } else {
            Assert.assertTrue(null == response.getData() || response.getData().isEmpty());
        }
    }

    public long appIdForModelAlias(String modelAlias) {
        // appId 与 Constants.PERSIST_MODEL_ADD_STR 中定义保持一致
        if (test_a_alias.equals(modelAlias)) {
            return 3;
        } else if (test_b_alias.equals(modelAlias)) {
            return 4;
        } else if (test_c_alias.equals(modelAlias)) {
            return 5;
        } else {
            throw new RuntimeException("currently not supported modelAlias: " + modelAlias);
        }
    }
}
