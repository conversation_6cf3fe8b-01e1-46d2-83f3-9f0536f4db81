server:
  port: 8082
  tomcat:
    basedir: logs
    accesslog:
      enabled: ${ACCESSLOG_ENABLED:false}
      pattern: '%{yyyy-MM-dd HH:mm:ss}t|[%I][%{reqId}i]-|cost=%D remoteIp=%a localIp=%A url=%U status=%s'
logging:
  level:
    org.springframework.security:
      - info
    org.springframework.web: error
    org.hibernate.SQL: info
    org.hibernate.engine.QueryParameters: info
    org.hibernate.engine.query.HQLQueryPlan: info
    org.hibernate.type.descriptor.sql.BasicBinder: info
spring:
  application:
    name: trantor2-model
  main:
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  servlet:
    multipart:
      enabled: ${upload_file_standard:true}
      max-file-size: ${upload_max_file:500MB}
      max-request-size: ${upload_max_request:500MB}
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${MYSQL_HOST:127.0.0.1}:${MYSQL_PORT:3306}/${MYSQL_DATABASE:trantor2_ceshi}?useUnicode=true&characterEncoding=utf-8&useSSL=false
    username: ${MYSQL_USERNAME:root}
    password: ${MYSQL_PASSWORD:password}
    hikari:
      maxLifetime: 180000
  jpa:
    show-sql: true
    properties:
      hibernate:
        globally_quoted_identifiers: false
    #                jdbc:
    #                  batch_size: 50
    #                order_updates: true
    hibernate:
      ddl-auto: ${AUTO_DDL_TYPE:create-drop}
    database-platform: org.hibernate.dialect.MySQLDialect
    defer-datasource-initialization: true
#  sql:
#    init:
#      mode: ALWAYS
#      username: ${MYSQL_USERNAME:root}
#      password: ${MYSQL_PASSWORD:password}
#      schema-locations:
#        - classpath*:testdata/mysql/schema.sql
  redis:
    host: ${REDIS_HOST:127.0.0.1}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:password}
    database: ${REDIS_DATABASE:0}
    sentinel:
      masterName: ${MASTER_NAME:} # redis哨兵模式下master名，redis哨兵模式下必须配置
      redisSentinels: ${REDIS_SENTINELS:} # redis哨兵模式下sentinel节点，格式为IP:PORT,多个节点间使用逗号分割
    pool:
      max-active: ${REDIS_POOL_MAX_ACTIVE:8}
      max-wait: ${REDIS_POOL_MAX_WAIT:-1}
      max-idle: ${REDIS_POOL_MAX_IDLE:8}
      min-idle: ${REDIS_POOL_MIN_IDLE:0}
    timeout: ${REDIS_TIMEOUT:500}
springdoc:
  api-docs:
    enabled: ${SWAGGER_ENABLE:false}
  packages-to-scan: io.terminus.trantor2
  swagger-ui:
    filter: true
trantor2:
  datasource:
    data-source-password-decrypt-iv: ${MODEL_DATA_SOURCE_PASSWORD_DECRYPT_IV:terminus_trantor}
    data-source-password-decrypt-key: ${MODEL_DATA_SOURCE_PASSWORD_DECRYPT_KEY:terminus_trantor}
  meta:
    event:
      type: redis
  runtime:
    default: false
terminus:
  mqServerAddress: ${MQ_SERVER_ADDRESS:127.0.0.1:9876}
  producerGroup: ${MQ_PRODUCER_GROUP:${SEARCH_PRODUCER_GROUP:datastoreProducerGroup1}}
  consumerGroup: ${MQ_CONSUMER_GROUP:${SEARCH_CONSUMER_GROUP:datastoreConsumerGroup1}}}
  clientType: ${CLIENT_TYPE:${MQ_CLIENT_TYPE:${DS_MQ_CLIENT_TYPE:ROCKETMQ}}}
  aliyun:
    accessKey: ${ALIYUN_ACCESSKEY:}
    secretKey: ${ALIYUN_SECRETKEY:}

iam:
  mock: ${IAM_MOCK:true}
  host: ${IAM_HOST:127.0.0.1}
  access-key: ${IAM_ACCESS_KEY:console}
  access-secret: ${IAM_ACCESS_SECRET:}
  internal:
    application-key: ${IAM_APPLICATION_KEY:console}
  web:
    order: 4 # 实际值：Ordered.HIGHEST_PRECEDENCE + order
    request-white-list:
      - /v3/api-docs/**
      - /swagger-ui/**
      - /swagger-ui.html
      - /swagger-resources/**
      - /doc.html
      - /webjars/**
      - /actuator/**
      - /favicon.ico
      - /api/trantor/workflow/v2/instances/**
      - /api/trantor/workflow/v2/definitions/**
      - /api/trantor/console/user/logout
      - /api/trantor/console/user/login
      - /api/trantor/portal/user/logout
      - /api/trantor/portal/user/login
      - /webjars/**
      - /actuator/**
      - /api/trantor/doc/engine/action/**
      - /api/trantor/rule/engine/**
      - /api/trantor/console/rule/engine/**
      - /api/trantor/portal/current
      - /api/trantor/portal/ops/permission-cache/**
      - /api/trantor/meta/ops/**
      - /api/trantor/searchModel/**
