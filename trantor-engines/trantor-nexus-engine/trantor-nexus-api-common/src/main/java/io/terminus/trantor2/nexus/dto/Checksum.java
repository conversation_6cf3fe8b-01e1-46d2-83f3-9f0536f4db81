package io.terminus.trantor2.nexus.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Objects;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Checksum {
    private String sha1;
    private String sha256;
    private String sha512;
    private String md5;

    public static Checksum of(String checksum) {
        if (checksum == null) {
            return null;
        }
        if (checksum.length() == 40) {
            return new Checksum(checksum, null, null, null);
        } else if (checksum.length() == 64) {
            return new Checksum(null, checksum, null, null);
        } else if (checksum.length() == 128) {
            return new Checksum(null, null, checksum, null);
        } else if (checksum.length() == 32) {
            return new Checksum(null, null, null, checksum);
        } else {
            throw new IllegalArgumentException("invalid checksum length");
        }
    }

    public String getChecksumStr() {
        if (sha1 != null) {
            return sha1;
        } else if (sha256 != null) {
            return sha256;
        } else if (sha512 != null) {
            return sha512;
        } else {
            return md5;
        }
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        Checksum other = (Checksum) obj;
        return (sha1 != null && sha1.equals(other.sha1)) ||
                (sha256 != null && sha256.equals(other.sha256)) ||
                (sha512 != null && sha512.equals(other.sha512)) ||
                (md5 != null && md5.equals(other.md5));
    }

    public int hashCode() {
        return Objects.hash(sha1, sha256, sha512, md5);
    }
}
