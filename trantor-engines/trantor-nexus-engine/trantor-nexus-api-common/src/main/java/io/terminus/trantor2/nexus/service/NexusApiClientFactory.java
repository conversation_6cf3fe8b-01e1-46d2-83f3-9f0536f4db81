package io.terminus.trantor2.nexus.service;

import io.terminus.trantor2.common.exception.TrantorRuntimeException;

import java.util.Optional;

public interface NexusApiClientFactory {
    default NexusApiClient getClient(Boolean isPublic) {
        return getClientOptional(isPublic).orElseThrow(
                () -> new TrantorRuntimeException("failure get " +
                        (isPublic ? "public" : "local") + " nexus client "));
    }

    Optional<NexusApiClient> getClientOptional(Boolean isPublic);
}
