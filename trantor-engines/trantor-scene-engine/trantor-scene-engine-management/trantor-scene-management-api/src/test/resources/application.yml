spring:
  main:
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  jpa:
    properties:
      hibernate:
        globally_quoted_identifiers: false
        format_sql: true
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.MySQLDialect
    show-sql: true

  liquibase:
    enabled: false
iam:
  mock: true

logging:
  level:
    org.springframework.security:
      - debug
      - info
    org.springframework.web: error
    org.hibernate.SQL: debug
    org.hibernate.engine.QueryParameters: debug
    org.hibernate.engine.query.HQLQueryPlan: debug
    org.hibernate.type.descriptor.sql.BasicBinder: trace
