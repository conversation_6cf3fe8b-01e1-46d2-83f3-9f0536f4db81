package io.terminus.trantor2.scene.runtime.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.iam.api.enums.permission.AuthorizationEffect;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.meta.api.dto.ViewPermissionDTO;
import io.terminus.trantor2.meta.editor.service.EditorMetaQueryService;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.permission.runtime.api.service.RoleFieldPermissionService;
import io.terminus.trantor2.permission.runtime.api.service.evaluate.UserPermissionEvaluateService;
import io.terminus.trantor2.scene.runtime.service.DataManagerRuntimeService;
import io.terminus.trantor2.scene.runtime.vo.DataManagerSceneVO;
import io.terminus.trantor2.scene.runtime.vo.LightDataManagerSceneVO;
import io.terminus.trantor2.scene.runtime.vo.ViewPermissionVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/trantor/runtime/scene/data-manager")
@Tag(name = "数据管理场景运行时", description = "portal")
@RequiredArgsConstructor
public class DataManagerRuntimeController {
    private final DataManagerRuntimeService sceneService;
    private final EditorMetaQueryService metaQueryService;
    private final UserPermissionEvaluateService userPermissionEvaluateService;
    private final RoleFieldPermissionService roleFieldPermissionService;

    @GetMapping("/{key}")
    @Operation(summary = "查询 portal 侧数据管理场景", description = "通过场景标识查询 portal 侧数据管理场景详情")
    public DataManagerSceneVO get(@PathVariable("key") String key) {
        return sceneService.findSceneVoByKey(key);
    }

    @GetMapping("/light/{key}")
    @Operation(summary = "查询 portal 侧数据管理场景，只加载简要的视图信息")
    public LightDataManagerSceneVO getLight(@PathVariable("key") String key,
                                            @RequestParam(required = false) String view) {
        return sceneService.findLightSceneVoByKey(key, view);
    }

    @GetMapping("/view/{view}")
    @Operation(summary = "查询 portal 侧数据管理场景指定视图")
    public DataManagerSceneVO.RuntimeDataSceneView getView(@PathVariable("view") String view) {
        return sceneService.findRuntimeViewByKey(view);
    }

    @GetMapping("/view-permission/{view}")
    @Operation(summary = "查询 portal 侧数据管理场景指定视图权限")
    public ViewPermissionVO getViewPermission(@PathVariable("view") String view) {
        // construct vo
        ViewPermissionVO vo = new ViewPermissionVO();

        // fill permission config
        userPermissionEvaluateService.fillPermissionProps(vo);

        if (Boolean.TRUE.equals(vo.getFunctionPermissionEnabled())) {   // enable function permission
            ViewPermissionDTO dto = metaQueryService.findSingleViewPermissionsCached(EditUtil.ctxFromThreadLocal(), TrantorContext.getPortalCode(), view);
            if (dto == null) {
                return new ViewPermissionVO();
            }

            // all permissionKeys
            List<String> allPermissionKeys = new ArrayList<>();

            // do collect
            dto.getComponents().forEach(item -> {
                allPermissionKeys.addAll(item.getResolvedPermissionKeys());
                item.getVirtualComponents().forEach(vc -> allPermissionKeys.addAll(vc.getResolvedPermissionKeys()));
            });
            if (dto.getPermissionKey() != null) {
                allPermissionKeys.add(dto.getPermissionKey());
            }

            Map<String, Boolean> authMap = new HashMap<>();
            // query iam to get user auth
            {
                if (CollectionUtils.isNotEmpty(allPermissionKeys)) {
                    Long currentUserId = TrantorContext.safeGetCurrentUser().map(User::getId).orElseThrow(() -> new TrantorRuntimeException("current user not found"));
                    String portalCode = TrantorContext.getCurrentPortalOptional().map(Portal::getCode).orElseThrow(() -> new TrantorRuntimeException("current portal not found"));
                    Map<String, AuthorizationEffect> effectMap = userPermissionEvaluateService.findUserFunctionPermissionEffect(currentUserId, portalCode, allPermissionKeys);
                    effectMap.forEach((permissionKey, effect) -> authMap.put(permissionKey, effect.equals(AuthorizationEffect.ALLOW)));
                }
            }

            String viewPermKey = dto.getPermissionKey();
            if (StringUtils.isNotBlank(viewPermKey)) {
                vo.getViewFunctionItems().put(viewPermKey, authMap.getOrDefault(viewPermKey, false));
            }
            dto.getComponents().forEach(item -> {
                Map<String, Boolean> perm = new HashMap<>();
                item.getResolvedPermissionKeys().forEach(key -> {
                    perm.put(key, authMap.getOrDefault(key, false));
                });
                ViewPermissionVO.ComponentFunctionItems cfi = new ViewPermissionVO.ComponentFunctionItems();
                cfi.setComponentKey(item.getCompKey());
                cfi.setResourceType(item.getResourceType());
                if (Objects.equals(item.getResourceType(), "Button")) {
                    // button has its own perm
                    cfi.setFunctionItems(perm);
                } else {
                    // and set fake perm to pass the frontend check
                    cfi.setFunctionItems(Collections.singletonMap("_fake_perm", true));
                }
                cfi.setVirtualComponents(new ArrayList<>());
                item.getVirtualComponents().forEach(vc -> {
                    Map<String, Boolean> vPerm = new HashMap<>();
                    vc.getResolvedPermissionKeys().forEach(key -> {
                        vPerm.put(key, authMap.getOrDefault(key, false));
                    });
                    ViewPermissionVO.VirtualComponentFunctionItems vcfi = new ViewPermissionVO.VirtualComponentFunctionItems();
                    vcfi.setVirtualComponentKey(vc.getVirtualCompKey());
                    vcfi.setResourceType(vc.getResourceType());
                    // do same thing as above
                    if (Objects.equals(vc.getResourceType(), "Button")) {
                        vcfi.setFunctionItems(vPerm);
                    } else {
                        vo.getViewFunctionItems().putAll(vPerm);
                        vcfi.setFunctionItems(Collections.singletonMap("_fake_perm", true));
                    }
                    cfi.getVirtualComponents().add(vcfi);
                });
                vo.getComponentFunctionItems().add(cfi);
            });
            if (dto.getPermissionKey() != null) {
                vo.getViewFunctionItems().put(dto.getPermissionKey(), authMap.getOrDefault(dto.getPermissionKey(), false));
            }
        }

        if (Boolean.TRUE.equals(vo.getFieldPermissionEnabled())) {  // enable field permission
            List<ViewPermissionVO.ComponentFieldRules> fieldPermission = roleFieldPermissionService.findFieldPermission(view);
            if (fieldPermission != null) {
                vo.getComponentFieldRules().addAll(fieldPermission);
            }
        }

        return vo;
    }

    @Deprecated
    @GetMapping
    @Operation(summary = "查询 portal 侧数据管理场景", description = "通过场景标识查询 portal 侧数据管理场景详情")
    public DataManagerSceneVO getConfig(@Parameter(description = "场景标识") @RequestParam String key) {
        return sceneService.findSceneVoByKey(key);
    }
}
