package io.terminus.trantor2.scene;

import io.terminus.iam.sdk.client.IAMClient;
import io.terminus.iam.sdk.toolkit.invoker.WebInvoker;
import io.terminus.trantor2.meta.editor.event.publish.RedisMetaEventPublisher;
import io.terminus.trantor2.module.service.TeamService;
import org.mockito.Mockito;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.*;
import org.springframework.context.annotation.ComponentScan.Filter;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import jakarta.persistence.EntityManagerFactory;
import java.util.Properties;

/**
 * <AUTHOR>
 */
@Configuration
@ComponentScan(basePackages = "io.terminus.trantor2",
    excludeFilters = {
        @Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.service.*"),
        @Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.model.*"),
        @Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.code.*"),
        @Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.rule.*")
    })
@EntityScan(basePackages = {"io.terminus.trantor2"})
@EnableJpaRepositories(basePackages = {"io.terminus.trantor2"})
public class SceneTestConfiguration {

    @Bean
    @Primary
    public PlatformTransactionManager transactionManager(EntityManagerFactory entityManagerFactory) {
        JpaTransactionManager jpaTransactionManager = new JpaTransactionManager(entityManagerFactory);
        Properties properties = new Properties();
        properties.setProperty("hibernate.dialect", "org.springframework.orm.jpa.vendor.HibernateJpaDialect");
        jpaTransactionManager.setJpaProperties(properties);
        return jpaTransactionManager;
    }

    @Bean
    public TeamService teamService() {
        return Mockito.mock(TeamService.class);
    }

    @Bean
    public RedisTemplate redisTemplate() {
        return Mockito.mock(RedisTemplate.class);
    }

    @Bean
    public IAMClient iamClient() {
        return Mockito.mock(IAMClient.class);
    }

    @Bean
    public WebInvoker webInvoker() {
        return Mockito.mock(WebInvoker.class);
    }

    @Bean
    public RedisMetaEventPublisher metaEventPublisher() {
        return Mockito.mock(RedisMetaEventPublisher.class);
    }

}
