package io.terminus.trantor2.scene.model;

import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.meta.request.QueryByKeysRequest;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.model.common.api.DataStructNodeQueryApi;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.scene.model.ModelApiWrapper.ModelNotExistException;
import io.terminus.trantor2.scene.model.ModelApiWrapper.ModelRuntimeException;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static io.terminus.trantor2.common.exception.ErrorType.MODEL_UNKNOWN_ERROR;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ModelApiWrapperTest {

    @Mock
    DataStructNodeQueryApi dataStructNodeApi;

    @InjectMocks
    ModelApiWrapper modelApiWrapper;

    @Test
    void findByNullAlias() {
        Assertions.assertEquals(0,
            modelApiWrapper.findByAliasStrictly(null, 1L, 1L).size(),
            "模型不为空");
    }

    @Test
    void findByAliasStrictly() {
        Response<List<DataStructNode>> response = new Response<>(true);
        List<DataStructNode> models = new ArrayList<>();
        models.add(ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/mainModel.json", DataStructNode.class));
        models.add(ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/subModel.json", DataStructNode.class));
        response.setData(models);
        Mockito.doReturn(response).when(dataStructNodeApi).findByKeys(Mockito.any());

        Assertions.assertEquals(2,
            modelApiWrapper.findByAliasStrictly(Arrays.asList("main", "sub"), 1L, 1L).size());

        ArgumentCaptor<QueryByKeysRequest> queryCaptor = ArgumentCaptor.forClass(QueryByKeysRequest.class);
        Mockito.verify(dataStructNodeApi).findByKeys(queryCaptor.capture());
        Assertions.assertEquals(2, queryCaptor.getValue().getKeys().size());
        Assertions.assertTrue(queryCaptor.getValue().getKeys().contains("main"));
        Assertions.assertTrue(queryCaptor.getValue().getKeys().contains("sub"));
    }

    @Test
    void findByAliasStrictlyWithException2() {
        Response<List<DataStructNode>> response = new Response<>(true);
        List<DataStructNode> models = new ArrayList<>();
        response.setData(models);
        Mockito.doReturn(response).when(dataStructNodeApi).findByKeys(Mockito.any());
        Assertions.assertThrows(ModelNotExistException.class,
            () -> modelApiWrapper.findByAliasStrictly(new ArrayList<>(Arrays.asList("main", "sub")), 1L, 1L),
            String.format(ErrorType.MODEL_NOT_EXIST_ERROR.getMessage(),
                1L, 1L, "[\"main\",\"sub\"]"));
    }

    @Test
    void findByAliasStrictlyWithException() {
        Response<List<DataStructNode>> response = new Response<>(true);
        List<DataStructNode> models = new ArrayList<>();
        models.add(ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/mainModel.json", DataStructNode.class));
        response.setData(models);
        Mockito.doReturn(response).when(dataStructNodeApi).findByKeys(Mockito.any());

        Assertions.assertThrows(ModelNotExistException.class,
            () -> modelApiWrapper.findByAliasStrictly(new ArrayList<>(Arrays.asList("main", "sub")), 1L, 1L),
            String.format(ErrorType.MODEL_NOT_EXIST_ERROR.getMessage(),
                1L, 1L, "[\"sub\"]"));
    }

    @Test
    void findByAliasStrictlyWithModelException() {
        Response<List<DataStructNode>> response = new Response<>(false);
        response.setErr(new Response.Error(MODEL_UNKNOWN_ERROR.getInnerCode(), "模型异常"));
        Mockito.doReturn(response).when(dataStructNodeApi).findByKeys(Mockito.any());

        Assertions.assertThrows(ModelRuntimeException.class,
            () -> modelApiWrapper.findByAliasStrictly(new ArrayList<>(Arrays.asList("main", "sub")), 1L, 1L),
            "模型异常");
    }

    @Test
    void findByAliasLoosely() {
        Response<List<DataStructNode>> response = new Response<>(true);
        List<DataStructNode> models = new ArrayList<>();
        response.setData(models);
        Mockito.doReturn(response).when(dataStructNodeApi).findByKeys(Mockito.any());

        Assertions.assertEquals(0,
            modelApiWrapper.findByAliasLoosely(Collections.singletonList("nonexistent"), 1L).size());

        ArgumentCaptor<QueryByKeysRequest> queryCaptor = ArgumentCaptor.forClass(QueryByKeysRequest.class);
        Mockito.verify(dataStructNodeApi).findByKeys(queryCaptor.capture());
        Assertions.assertEquals(1, queryCaptor.getValue().getKeys().size());
        Assertions.assertTrue(queryCaptor.getValue().getKeys().contains("nonexistent"));
    }

    @Test
    void findByAliasLoosely2() {
        Assertions.assertEquals(0,
            modelApiWrapper.findByAliasLoosely(null, 1L).size());
        Mockito.verifyNoInteractions(dataStructNodeApi);
    }

    @Test
    void findOneByAliasOrElseThrow() {
        Response<List<DataStructNode>> response = new Response<>(true);
        List<DataStructNode> models = new ArrayList<>();
        models.add(ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/mainModel.json", DataStructNode.class));
        response.setData(models);
        Mockito.doReturn(response).when(dataStructNodeApi).findByKeys(Mockito.any());

        DataStructNode node = modelApiWrapper.findOneByAliasOrElseThrow("main", 1L);
        Assertions.assertNotNull(node);
        Assertions.assertEquals("main", node.getAlias(), "模型别名不一致");

        ArgumentCaptor<QueryByKeysRequest> queryCaptor = ArgumentCaptor.forClass(QueryByKeysRequest.class);
        Mockito.verify(dataStructNodeApi).findByKeys(queryCaptor.capture());
        Assertions.assertEquals(1, queryCaptor.getValue().getKeys().size());
        Assertions.assertTrue(queryCaptor.getValue().getKeys().contains("main"));
    }


    @Test
    void findOneByAliasOrElseThrowWithException() {
        Response<List<DataStructNode>> response = new Response<>(true);
        response.setData(null);
        Mockito.doReturn(response).when(dataStructNodeApi).findByKeys(Mockito.any());

        Assertions.assertThrows(ModelNotExistException.class,
            () -> modelApiWrapper.findOneByAliasOrElseThrow("main", 1L),
            String.format(ErrorType.MODEL_NOT_EXIST_ERROR.getMessage(),
                1L, 1L, "main"));

        ArgumentCaptor<QueryByKeysRequest> queryCaptor = ArgumentCaptor.forClass(QueryByKeysRequest.class);
        Mockito.verify(dataStructNodeApi).findByKeys(queryCaptor.capture());
        Assertions.assertEquals(1, queryCaptor.getValue().getKeys().size());
        Assertions.assertTrue(queryCaptor.getValue().getKeys().contains("main"));
    }

    @Test
    void findOneByAliasWithException2() {
        Response<List<DataStructNode>> response = new Response<>(true);
        List<DataStructNode> models = new ArrayList<>();
        models.add(ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/subModel.json", DataStructNode.class));
        response.setData(models);

        Mockito.doReturn(response).when(dataStructNodeApi).findByKeys(Mockito.any());
        Assertions.assertThrows(ModelNotExistException.class,
            () -> modelApiWrapper.findOneByAliasOrElseThrow("main", 1L),
            String.format(ErrorType.MODEL_NOT_EXIST_IN_TEAM_ERROR.getMessage(),
                1L, "main"));

        ArgumentCaptor<QueryByKeysRequest> queryCaptor = ArgumentCaptor.forClass(QueryByKeysRequest.class);
        Mockito.verify(dataStructNodeApi).findByKeys(queryCaptor.capture());
        Assertions.assertEquals(1, queryCaptor.getValue().getKeys().size());
        Assertions.assertTrue(queryCaptor.getValue().getKeys().contains("main"));
    }
}
