package io.terminus.trantor2.doc.api.request;

import io.terminus.trantor2.doc.api.dto.ActionDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ActionUploadRequest {

    private List<ActionDTO> actions;

    private Long userId;

    private String appKey;

    private String folderPath;

    private Long teamId;

    private String teamCode;

    public ActionUploadRequest(){}

    public ActionUploadRequest(Long userId, String appKey, String folderPath, Long teamId, String teamCode, List<ActionDTO> actions){
        this.userId = userId;
        this.appKey = appKey;
        this.folderPath = folderPath;
        this.teamId = teamId;
        this.teamCode = teamCode;
        this.actions = actions;
    }
}
