package io.terminus.trantor2.doc.api.context;

import io.terminus.trantor2.common.dto.PageInfo;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class EventRequest {

    private String eventCode;

    private Object param;

    /**
     * 页面信息，审计需要，在异步回调时，需要设置该值
     */
    private PageInfo pageInfo;

    /**
     * 功能权限项key
     */
    private String permissionKey;

    /**
     * 数据权限key
     */
    private String dataConditionPermissionKey;
}
