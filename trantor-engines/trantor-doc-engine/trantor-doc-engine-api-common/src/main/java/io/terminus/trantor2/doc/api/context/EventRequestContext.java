package io.terminus.trantor2.doc.api.context;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public final class EventRequestContext {

    private static final ThreadLocal<EventInfoContext> EVENT_INFO_CONTEXT = ThreadLocal.withInitial(EventInfoContext::new);

    public static EventInfoContext getEventInfoContext() {
        return EVENT_INFO_CONTEXT.get();
    }

    public static void init(String eventCode) {
        EventInfoContext context = EVENT_INFO_CONTEXT.get();
        context.setEventCode(eventCode);
        init(context);
    }

    public static void init(String eventCode, String mainModelKey, String mainModelName, Boolean enabledStatusVerify) {
        EventInfoContext context = EVENT_INFO_CONTEXT.get();
        context.setEventCode(eventCode);
        context.setMainModelKey(mainModelKey);
        context.setMainModelName(mainModelName);
        context.setEnabledStatusVerify(enabledStatusVerify);
        context.setEventStateChangeLogs(new ArrayList<>());
        init(context);
    }

    public static void init(EventRequest eventRequest, String mainModelKey, String mainModelName, Boolean enabledStatusVerify) {
        EventInfoContext context = EVENT_INFO_CONTEXT.get();
        context.setEventCode(eventRequest.getEventCode());
        context.setMainModelKey(mainModelKey);
        context.setMainModelName(mainModelName);
        context.setEnabledStatusVerify(enabledStatusVerify);
        context.setEventParam((JSON) JSON.toJSON(eventRequest.getParam()));
        context.setEventStateChangeLogs(new ArrayList<>());
        init(context);
    }

    public static void clear() {
        EventInfoContext context = EVENT_INFO_CONTEXT.get();
        context.setEventCode(null);
        context.setMainModelKey(null);
        context.setMainModelName(null);
        context.setEnabledStatusVerify(false);
        context.setEventParam(null);
        context.getEventStateChangeLogs().clear();
        init(context);
    }

    public static EventInfoContext snapshot() {
        EventInfoContext context = EVENT_INFO_CONTEXT.get();
        EventInfoContext snapshot = new EventInfoContext();
        snapshot.setEventCode(context.eventCode);
        snapshot.setMainModelKey(context.getMainModelKey());
        snapshot.setMainModelName(context.getMainModelName());
        snapshot.setEnabledStatusVerify(context.enabledStatusVerify);
        snapshot.setExtra(new HashMap<>(context.getExtra()));
        snapshot.setModelStateTags(new ArrayList<>(context.getModelStateTags()));
        snapshot.setRelatedModelIds(new ArrayList<>(context.getRelatedModelIds()));
        snapshot.setEventOplogInfoMap(new HashMap<>(context.getEventOplogInfoMap()));
        snapshot.setEventParam(context.getEventParam());
        snapshot.setEventStateChangeLogs(context.eventStateChangeLogs);
        return snapshot;
    }

    public static void reset(EventInfoContext snapshot) {
        EventInfoContext context = EVENT_INFO_CONTEXT.get();
        context.setEventCode(snapshot.eventCode);
        context.setMainModelKey(snapshot.getMainModelKey());
        context.setMainModelName(snapshot.getMainModelName());
        context.setEnabledStatusVerify(snapshot.enabledStatusVerify);
        context.setExtra(new HashMap<>(snapshot.getExtra()));
        context.setModelStateTags(new ArrayList<>(snapshot.getModelStateTags()));
        context.setRelatedModelIds(new ArrayList<>(snapshot.getRelatedModelIds()));
        context.setEventOplogInfoMap(new HashMap<>(snapshot.getEventOplogInfoMap()));
        context.setEventParam(snapshot.getEventParam());
        context.setEventStateChangeLogs(snapshot.eventStateChangeLogs);
    }

    public static String getEventCode() {
        EventInfoContext eventInfoContext = getEventInfoContext();
        if (eventInfoContext == null) {
            return null;
        }
        return eventInfoContext.getEventCode();
    }

    private static void init(EventInfoContext context) {
        Map<String, EventOplogInfo> eventOplogInfoMap = context.getEventOplogInfoMap();
        if (MapUtils.isNotEmpty(eventOplogInfoMap)) {
            eventOplogInfoMap.clear();
        }
        if (Objects.isNull(context.getExtra())) {
            context.setExtra(new HashMap<>(16));
        } else {
            context.getExtra().clear();
        }

        if (Objects.isNull(context.getModelStateTags())) {
            context.setModelStateTags(new ArrayList<>());
        } else {
            context.getModelStateTags().clear();
        }

        if (Objects.isNull(context.getRelatedModelIds())) {
            context.setRelatedModelIds(new ArrayList<>());
        } else {
            context.getRelatedModelIds().clear();
        }
    }

    @Data
    public static class EventInfoContext implements Serializable {
        private static final long serialVersionUID = 5189030322307067085L;

        private JSON eventParam;
        private String eventCode;

        private String mainModelKey;

        private String mainModelName;

        private Map<String, EventOplogInfo> eventOplogInfoMap = new HashMap<>(16);

        private Map<String, Object> extra;

        private List<ModelStateTag> modelStateTags;

        private List<RelatedModelId> relatedModelIds;

        private Boolean enabledStatusVerify;

        private List<EventStateChangeLog> eventStateChangeLogs;

        public void addStateChangeLog(String modelKey, String fieldKey, Object body, Long dataId, String sourceState, String targetState) {
            if (eventStateChangeLogs == null) {
                eventStateChangeLogs = new ArrayList<>();
            }

            EventStateChangeLog changeLog = new EventStateChangeLog();
            changeLog.setModel(modelKey);
            changeLog.setSource(sourceState);
            changeLog.setTarget(targetState);
            eventStateChangeLogs.add(changeLog);
        }

        public void stateMatch(String modelAlias) {
            if (modelStateTags == null) {
                modelStateTags = new ArrayList<>();
            }
            ModelStateTag modelStateTag = new ModelStateTag();
            modelStateTag.setModelAlias(modelAlias);
            modelStateTag.setExecuteTag(Boolean.TRUE);
            modelStateTags.add(modelStateTag);
        }

        public boolean judgeModelStateExecute(String modelAlias) {
            if (modelStateTags == null) {
                return false;
            }

            ModelStateTag modelStateTag = modelStateTags.stream()
                .filter(item -> item.getModelAlias().equals(modelAlias))
                .findFirst()
                .orElse(null);
            if (modelStateTag == null) {
                return false;
            }

            return true;
        }

        public List<Object> getModeIds(String modeAlias) {
            if (relatedModelIds == null) {
                return null;
            }

            return relatedModelIds.stream()
                .filter(item -> item.getModelAlias().equals(modeAlias))
                .map(RelatedModelId::getIds)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        }

        public void setModeIds(Object id, String modeAlias) {
            if (relatedModelIds == null) {
                relatedModelIds = new ArrayList<>();
            }

            RelatedModelId relatedModelId = relatedModelIds.stream()
                .filter(item -> item.getModelAlias().equals(modeAlias))
                .findFirst()
                .orElse(null);
            if (relatedModelId == null) {
                relatedModelId = new RelatedModelId();
                relatedModelId.setIds(Sets.newHashSet(id));
                relatedModelId.setModelAlias(modeAlias);
            } else {
                relatedModelId.getIds().add(id);
            }

            relatedModelIds.add(relatedModelId);
        }
    }

    @Data
    public static class ModelStateTag implements Serializable {
        /**
         * 模型别名
         */
        private String modelAlias;

        /**
         * 执行判断
         */
        private Boolean executeTag;
    }


    @Data
    public static class RelatedModelId implements Serializable {

        private String modelAlias;

        private Set<Object> ids;
    }

    @Data
    public static class EventOplogInfo implements Serializable {

        private String bizKey;

        private String bizKeyField;

        private Long bizId;

        private Long bizVersion;

        private String eventKey;

        private String modelKey;

        private String modelName;

        private Long relationBizId;
    }
}
