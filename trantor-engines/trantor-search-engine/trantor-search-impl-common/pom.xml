<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>trantor-search-engine</artifactId>
        <groupId>io.terminus.trantor2</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>trantor-search-impl-common</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-globalreq-engine</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-impl-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-search-api-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-meta-runtime-impl</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--TODO 这里不应该依赖，因为目前依赖了model-runtime-imp，而model中依赖了model-management-impl-->
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-meta-management-impl</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-meta-api-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-model-runtime-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-model-runtime-impl</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>redisson</artifactId>
                    <groupId>org.redisson</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-properties</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

</project>
