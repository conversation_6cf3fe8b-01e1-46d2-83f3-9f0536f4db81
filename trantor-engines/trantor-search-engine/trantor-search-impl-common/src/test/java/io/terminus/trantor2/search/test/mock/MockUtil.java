package io.terminus.trantor2.search.test.mock;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.domain.search.SearchModelConfigMeta;
import io.terminus.trantor2.model.management.meta.domain.search.SearchModelFieldConfigMeta;

public class MockUtil {
    public static void main(String[] args) {
        String str = "{\n" +
            "                \"teamId\": 3,\n" +
            "                \"appId\": 33252,\n" +
            "                \"key\": \"testAdd$s_ceshi\",\n" +
            "                \"parentKey\": \"testAdd$ungroup\",\n" +
            "                \"name\": \"s_ceshi\",\n" +
            "                \"access\": \"Private\",\n" +
            "                \"description\": null,\n" +
            "                \"createdBy\": 435466533868229,\n" +
            "                \"updatedBy\": 435466533868229,\n" +
            "                \"createdAt\": 1689909000360,\n" +
            "                \"updatedAt\": 1689909000360,\n" +
            "                \"createdByName\": \"u115-528072424\",\n" +
            "                \"updatedByName\": \"u115-528072424\",\n" +
            "                \"id\": 34617,\n" +
            "                \"alias\": \"testAdd$s_ceshi\",\n" +
            "                \"desc\": \"s_ceshi\",\n" +
            "                \"children\": [\n" +
            "                    {\n" +
            "                        \"teamId\": 3,\n" +
            "                        \"appId\": 33252,\n" +
            "                        \"key\": \"id\",\n" +
            "                        \"parentKey\": null,\n" +
            "                        \"name\": \"ID\",\n" +
            "                        \"access\": null,\n" +
            "                        \"description\": null,\n" +
            "                        \"createdBy\": null,\n" +
            "                        \"updatedBy\": null,\n" +
            "                        \"createdAt\": null,\n" +
            "                        \"updatedAt\": null,\n" +
            "                        \"createdByName\": null,\n" +
            "                        \"updatedByName\": null,\n" +
            "                        \"alias\": \"id\",\n" +
            "                        \"props\": {\n" +
            "                            \"fieldType\": \"NUMBER\",\n" +
            "                            \"required\": true,\n" +
            "                            \"unique\": true,\n" +
            "                            \"compositeKey\": false,\n" +
            "                            \"isSystemField\": true,\n" +
            "                            \"autoGenerated\": false,\n" +
            "                            \"defaultValue\": null,\n" +
            "                            \"comment\": \"ID\",\n" +
            "                            \"columnName\": \"id\",\n" +
            "                            \"length\": 20,\n" +
            "                            \"intLength\": 20,\n" +
            "                            \"scale\": null,\n" +
            "                            \"relationMeta\": null,\n" +
            "                            \"dictPros\": null,\n" +
            "                            \"attachmentProps\": null,\n" +
            "                            \"encrypted\": false,\n" +
            "                            \"desensitizedRule\": null\n" +
            "                        },\n" +
            "                        \"type\": \"DataStructField\"\n" +
            "                    },\n" +
            "                    {\n" +
            "                        \"teamId\": 3,\n" +
            "                        \"appId\": 33252,\n" +
            "                        \"key\": \"created_by\",\n" +
            "                        \"parentKey\": null,\n" +
            "                        \"name\": \"创建人\",\n" +
            "                        \"access\": null,\n" +
            "                        \"description\": null,\n" +
            "                        \"createdBy\": null,\n" +
            "                        \"updatedBy\": null,\n" +
            "                        \"createdAt\": null,\n" +
            "                        \"updatedAt\": null,\n" +
            "                        \"createdByName\": null,\n" +
            "                        \"updatedByName\": null,\n" +
            "                        \"alias\": \"createdBy\",\n" +
            "                        \"props\": {\n" +
            "                            \"fieldType\": \"OBJECT\",\n" +
            "                            \"required\": false,\n" +
            "                            \"unique\": true,\n" +
            "                            \"compositeKey\": false,\n" +
            "                            \"isSystemField\": true,\n" +
            "                            \"autoGenerated\": false,\n" +
            "                            \"defaultValue\": null,\n" +
            "                            \"comment\": \"创建人\",\n" +
            "                            \"columnName\": \"created_by\",\n" +
            "                            \"length\": 20,\n" +
            "                            \"intLength\": 20,\n" +
            "                            \"scale\": null,\n" +
            "                            \"relationMeta\": {\n" +
            "                                \"relationKey\": null,\n" +
            "                                \"relationType\": \"LINK\",\n" +
            "                                \"relationModelAlias\": \"testAdd$user\",\n" +
            "                                \"linkModelFieldAlias\": null,\n" +
            "                                \"relationModelKey\": \"testAdd$user\",\n" +
            "                                \"currentModelAlias\": \"testAdd$s_ceshi\",\n" +
            "                                \"currentModelFieldAlias\": \"createdBy\",\n" +
            "                                \"linkModelAlias\": null,\n" +
            "                                \"sync\": false\n" +
            "                            },\n" +
            "                            \"dictPros\": null,\n" +
            "                            \"attachmentProps\": null,\n" +
            "                            \"encrypted\": false,\n" +
            "                            \"desensitizedRule\": null\n" +
            "                        },\n" +
            "                        \"type\": \"DataStructField\"\n" +
            "                    },\n" +
            "                    {\n" +
            "                        \"teamId\": 3,\n" +
            "                        \"appId\": 33252,\n" +
            "                        \"key\": \"updated_by\",\n" +
            "                        \"parentKey\": null,\n" +
            "                        \"name\": \"更新人\",\n" +
            "                        \"access\": null,\n" +
            "                        \"description\": null,\n" +
            "                        \"createdBy\": null,\n" +
            "                        \"updatedBy\": null,\n" +
            "                        \"createdAt\": null,\n" +
            "                        \"updatedAt\": null,\n" +
            "                        \"createdByName\": null,\n" +
            "                        \"updatedByName\": null,\n" +
            "                        \"alias\": \"updatedBy\",\n" +
            "                        \"props\": {\n" +
            "                            \"fieldType\": \"OBJECT\",\n" +
            "                            \"required\": false,\n" +
            "                            \"unique\": true,\n" +
            "                            \"compositeKey\": false,\n" +
            "                            \"isSystemField\": true,\n" +
            "                            \"autoGenerated\": false,\n" +
            "                            \"defaultValue\": null,\n" +
            "                            \"comment\": \"更新人\",\n" +
            "                            \"columnName\": \"updated_by\",\n" +
            "                            \"length\": 20,\n" +
            "                            \"intLength\": 20,\n" +
            "                            \"scale\": null,\n" +
            "                            \"relationMeta\": {\n" +
            "                                \"relationKey\": null,\n" +
            "                                \"relationType\": \"LINK\",\n" +
            "                                \"relationModelAlias\": \"testAdd$user\",\n" +
            "                                \"linkModelFieldAlias\": null,\n" +
            "                                \"relationModelKey\": \"testAdd$user\",\n" +
            "                                \"currentModelAlias\": \"testAdd$s_ceshi\",\n" +
            "                                \"currentModelFieldAlias\": \"updatedBy\",\n" +
            "                                \"linkModelAlias\": null,\n" +
            "                                \"sync\": false\n" +
            "                            },\n" +
            "                            \"dictPros\": null,\n" +
            "                            \"attachmentProps\": null,\n" +
            "                            \"encrypted\": false,\n" +
            "                            \"desensitizedRule\": null\n" +
            "                        },\n" +
            "                        \"type\": \"DataStructField\"\n" +
            "                    },\n" +
            "                    {\n" +
            "                        \"teamId\": 3,\n" +
            "                        \"appId\": 33252,\n" +
            "                        \"key\": \"created_at\",\n" +
            "                        \"parentKey\": null,\n" +
            "                        \"name\": \"创建时间\",\n" +
            "                        \"access\": null,\n" +
            "                        \"description\": null,\n" +
            "                        \"createdBy\": null,\n" +
            "                        \"updatedBy\": null,\n" +
            "                        \"createdAt\": null,\n" +
            "                        \"updatedAt\": null,\n" +
            "                        \"createdByName\": null,\n" +
            "                        \"updatedByName\": null,\n" +
            "                        \"alias\": \"createdAt\",\n" +
            "                        \"props\": {\n" +
            "                            \"fieldType\": \"DATE\",\n" +
            "                            \"required\": true,\n" +
            "                            \"unique\": true,\n" +
            "                            \"compositeKey\": false,\n" +
            "                            \"isSystemField\": true,\n" +
            "                            \"autoGenerated\": false,\n" +
            "                            \"defaultValue\": null,\n" +
            "                            \"comment\": \"创建时间\",\n" +
            "                            \"columnName\": \"created_at\",\n" +
            "                            \"length\": null,\n" +
            "                            \"intLength\": null,\n" +
            "                            \"scale\": null,\n" +
            "                            \"relationMeta\": null,\n" +
            "                            \"dictPros\": null,\n" +
            "                            \"attachmentProps\": null,\n" +
            "                            \"encrypted\": false,\n" +
            "                            \"desensitizedRule\": null\n" +
            "                        },\n" +
            "                        \"type\": \"DataStructField\"\n" +
            "                    },\n" +
            "                    {\n" +
            "                        \"teamId\": 3,\n" +
            "                        \"appId\": 33252,\n" +
            "                        \"key\": \"updated_at\",\n" +
            "                        \"parentKey\": null,\n" +
            "                        \"name\": \"更新时间\",\n" +
            "                        \"access\": null,\n" +
            "                        \"description\": null,\n" +
            "                        \"createdBy\": null,\n" +
            "                        \"updatedBy\": null,\n" +
            "                        \"createdAt\": null,\n" +
            "                        \"updatedAt\": null,\n" +
            "                        \"createdByName\": null,\n" +
            "                        \"updatedByName\": null,\n" +
            "                        \"alias\": \"updatedAt\",\n" +
            "                        \"props\": {\n" +
            "                            \"fieldType\": \"DATE\",\n" +
            "                            \"required\": true,\n" +
            "                            \"unique\": true,\n" +
            "                            \"compositeKey\": false,\n" +
            "                            \"isSystemField\": true,\n" +
            "                            \"autoGenerated\": false,\n" +
            "                            \"defaultValue\": null,\n" +
            "                            \"comment\": \"更新时间\",\n" +
            "                            \"columnName\": \"updated_at\",\n" +
            "                            \"length\": null,\n" +
            "                            \"intLength\": null,\n" +
            "                            \"scale\": null,\n" +
            "                            \"relationMeta\": null,\n" +
            "                            \"dictPros\": null,\n" +
            "                            \"attachmentProps\": null,\n" +
            "                            \"encrypted\": false,\n" +
            "                            \"desensitizedRule\": null\n" +
            "                        },\n" +
            "                        \"type\": \"DataStructField\"\n" +
            "                    },\n" +
            "                    {\n" +
            "                        \"teamId\": 3,\n" +
            "                        \"appId\": 33252,\n" +
            "                        \"key\": \"version\",\n" +
            "                        \"parentKey\": null,\n" +
            "                        \"name\": \"版本号\",\n" +
            "                        \"access\": null,\n" +
            "                        \"description\": null,\n" +
            "                        \"createdBy\": null,\n" +
            "                        \"updatedBy\": null,\n" +
            "                        \"createdAt\": null,\n" +
            "                        \"updatedAt\": null,\n" +
            "                        \"createdByName\": null,\n" +
            "                        \"updatedByName\": null,\n" +
            "                        \"alias\": \"version\",\n" +
            "                        \"props\": {\n" +
            "                            \"fieldType\": \"NUMBER\",\n" +
            "                            \"required\": true,\n" +
            "                            \"unique\": true,\n" +
            "                            \"compositeKey\": false,\n" +
            "                            \"isSystemField\": true,\n" +
            "                            \"autoGenerated\": false,\n" +
            "                            \"defaultValue\": 0,\n" +
            "                            \"comment\": \"版本号\",\n" +
            "                            \"columnName\": \"version\",\n" +
            "                            \"length\": 20,\n" +
            "                            \"intLength\": 20,\n" +
            "                            \"scale\": null,\n" +
            "                            \"relationMeta\": null,\n" +
            "                            \"dictPros\": null,\n" +
            "                            \"attachmentProps\": null,\n" +
            "                            \"encrypted\": false,\n" +
            "                            \"desensitizedRule\": null\n" +
            "                        },\n" +
            "                        \"type\": \"DataStructField\"\n" +
            "                    },\n" +
            "                    {\n" +
            "                        \"teamId\": 3,\n" +
            "                        \"appId\": 33252,\n" +
            "                        \"key\": \"deleted\",\n" +
            "                        \"parentKey\": null,\n" +
            "                        \"name\": \"逻辑删除标识\",\n" +
            "                        \"access\": null,\n" +
            "                        \"description\": null,\n" +
            "                        \"createdBy\": null,\n" +
            "                        \"updatedBy\": null,\n" +
            "                        \"createdAt\": null,\n" +
            "                        \"updatedAt\": null,\n" +
            "                        \"createdByName\": null,\n" +
            "                        \"updatedByName\": null,\n" +
            "                        \"alias\": \"deleted\",\n" +
            "                        \"props\": {\n" +
            "                            \"fieldType\": \"NUMBER\",\n" +
            "                            \"required\": true,\n" +
            "                            \"unique\": true,\n" +
            "                            \"compositeKey\": false,\n" +
            "                            \"isSystemField\": true,\n" +
            "                            \"autoGenerated\": false,\n" +
            "                            \"defaultValue\": 0,\n" +
            "                            \"comment\": \"逻辑删除标识\",\n" +
            "                            \"columnName\": \"deleted\",\n" +
            "                            \"length\": 20,\n" +
            "                            \"intLength\": 20,\n" +
            "                            \"scale\": null,\n" +
            "                            \"relationMeta\": null,\n" +
            "                            \"dictPros\": null,\n" +
            "                            \"attachmentProps\": null,\n" +
            "                            \"encrypted\": false,\n" +
            "                            \"desensitizedRule\": null\n" +
            "                        },\n" +
            "                        \"type\": \"DataStructField\"\n" +
            "                    },\n" +
            "                    {\n" +
            "                        \"teamId\": 3,\n" +
            "                        \"appId\": 33252,\n" +
            "                        \"key\": \"name\",\n" +
            "                        \"parentKey\": null,\n" +
            "                        \"name\": \"name\",\n" +
            "                        \"access\": null,\n" +
            "                        \"description\": null,\n" +
            "                        \"createdBy\": null,\n" +
            "                        \"updatedBy\": null,\n" +
            "                        \"createdAt\": null,\n" +
            "                        \"updatedAt\": null,\n" +
            "                        \"createdByName\": null,\n" +
            "                        \"updatedByName\": null,\n" +
            "                        \"alias\": \"name\",\n" +
            "                        \"props\": {\n" +
            "                            \"fieldType\": \"TEXT\",\n" +
            "                            \"required\": false,\n" +
            "                            \"unique\": false,\n" +
            "                            \"compositeKey\": false,\n" +
            "                            \"isSystemField\": false,\n" +
            "                            \"autoGenerated\": false,\n" +
            "                            \"defaultValue\": null,\n" +
            "                            \"comment\": \"name\",\n" +
            "                            \"columnName\": \"name\",\n" +
            "                            \"length\": 256,\n" +
            "                            \"intLength\": null,\n" +
            "                            \"scale\": null,\n" +
            "                            \"relationMeta\": null,\n" +
            "                            \"dictPros\": null,\n" +
            "                            \"attachmentProps\": null,\n" +
            "                            \"encrypted\": false,\n" +
            "                            \"desensitizedRule\": null\n" +
            "                        },\n" +
            "                        \"type\": \"DataStructField\"\n" +
            "                    },\n" +
            "                    {\n" +
            "                        \"teamId\": 3,\n" +
            "                        \"appId\": 33252,\n" +
            "                        \"key\": \"ageder\",\n" +
            "                        \"parentKey\": null,\n" +
            "                        \"name\": \"ageder\",\n" +
            "                        \"access\": null,\n" +
            "                        \"description\": null,\n" +
            "                        \"createdBy\": null,\n" +
            "                        \"updatedBy\": null,\n" +
            "                        \"createdAt\": null,\n" +
            "                        \"updatedAt\": null,\n" +
            "                        \"createdByName\": null,\n" +
            "                        \"updatedByName\": null,\n" +
            "                        \"alias\": \"ageder\",\n" +
            "                        \"props\": {\n" +
            "                            \"fieldType\": \"NUMBER\",\n" +
            "                            \"required\": false,\n" +
            "                            \"unique\": false,\n" +
            "                            \"compositeKey\": false,\n" +
            "                            \"isSystemField\": false,\n" +
            "                            \"autoGenerated\": false,\n" +
            "                            \"defaultValue\": null,\n" +
            "                            \"comment\": \"ageder\",\n" +
            "                            \"columnName\": \"ageder\",\n" +
            "                            \"length\": null,\n" +
            "                            \"intLength\": 20,\n" +
            "                            \"scale\": null,\n" +
            "                            \"relationMeta\": null,\n" +
            "                            \"dictPros\": null,\n" +
            "                            \"attachmentProps\": null,\n" +
            "                            \"encrypted\": false,\n" +
            "                            \"desensitizedRule\": null\n" +
            "                        },\n" +
            "                        \"type\": \"DataStructField\"\n" +
            "                    }\n" +
            "                ],\n" +
            "                \"props\": {\n" +
            "                    \"config\": {\n" +
            "                        \"persist\": false,\n" +
            "                        \"system\": false,\n" +
            "                        \"self\": false,\n" +
            "                        \"selfRelationFieldAlias\": null\n" +
            "                    },\n" +
            "                    \"tableName\": \"s_ceshi\",\n" +
            "                    \"indexes\": null,\n" +
            "                    \"ddl\": null,\n" +
            "                    \"mainField\": \"name\",\n" +
            "                    \"type\": \"PERSIST\",\n" +
            "                    \"physicalDelete\": false,\n" +
            "                    \"mainFieldAlias\": \"name\"\n" +
            "                },\n" +
            "                \"type\": \"DataStruct\"\n" +
            "            }";

        DataStructNode dataStructNode = JsonUtil.fromJson(str, DataStructNode.class);
        dataStructNode.getProps().setSearchModel(true);
        dataStructNode.getProps().setSearchModelConfigMeta(SearchModelConfigMeta.builder()
            .topicName("zw_topic2")
            .build());

        dataStructNode.getChildren().forEach(field->{
            field.getProps().setSearchModelFieldConfigMeta(SearchModelFieldConfigMeta.builder()
                .build());
        });
        System.err.println(JsonUtil.toJson(dataStructNode));
    }
}
