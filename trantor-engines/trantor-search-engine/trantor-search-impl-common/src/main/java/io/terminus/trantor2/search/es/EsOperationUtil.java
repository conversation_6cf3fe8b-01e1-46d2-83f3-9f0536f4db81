package io.terminus.trantor2.search.es;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

@Slf4j
public final class EsOperationUtil {
    /**
     * 构建es请求url
     *
     * @param hosts
     * @param port
     * @return
     */
    public static StringBuilder buildUrlPrefix(String hosts, Integer port) {
        StringBuilder sb = new StringBuilder("http://");
        String[] hostsList = hosts.split(",");
        return sb.append(hostsList[new Random().nextInt(hostsList.length)]).append(":").append(port);
    }

    /**
     * 构建es请求头
     *
     * @param authorizationBase64
     * @return
     */
    public static Map<String, String> makeReqHeader(String authorizationBase64) {
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json;charset=utf-8");
        if (!StringUtils.isEmpty(authorizationBase64)) {
            header.put("Authorization", authorizationBase64);
        }
        return header;
    }
}
