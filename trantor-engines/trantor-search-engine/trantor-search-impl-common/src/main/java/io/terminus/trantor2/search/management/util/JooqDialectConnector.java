package io.terminus.trantor2.search.management.util;

import org.jooq.SQLDialect;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class JooqDialectConnector {

    public static final SQLDialect H2_SQL_DIALECT = SQLDialect.H2;

    public static final SQLDialect MYSQL_SQL_DIALECT = SQLDialect.MYSQL_5_7;

    public static final SQLDialect ORACLE_SQL_DIALECT = SQLDialect.ORACLE18C;

    /**
     * 根据数据源类型获取对应的SQLDialect
     *
     * @param type 数据源类型
     * @return
     */
    public static SQLDialect getSqlDialectByType(String type) {
        SupportDialectType dialectType = SupportDialectType.find(type);
        switch (dialectType) {
            case MySQL:
            case DRDS:
                return MYSQL_SQL_DIALECT;
            case H2:
                return H2_SQL_DIALECT;
            case ORACLE:
                return ORACLE_SQL_DIALECT;
            default:
                throw new UnsupportedOperationException("un support dialect type");
        }
    }
}
