package io.terminus.trantor2.search.management.config;

import io.terminus.trantor2.search.management.cache.SearchModelRedisMessageSubscriber;
import io.terminus.trantor2.search.management.cache.SearchSyncTaskCacheCleaner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;

import static io.terminus.trantor2.search.management.cache.CachePubSubConst.SEARCH_PUB_SUB_TOPIC;
import static io.terminus.trantor2.search.management.cache.CachePubSubConst.SENDER_ID;


@Configuration
public class CacheCleanAutoConfig {

    @Bean
    SearchSyncTaskCacheCleaner searchSyncTaskCacheCleaner() {
        return new SearchSyncTaskCacheCleaner();
    }

    @Bean
    @Primary
    RedisMessageListenerContainer redisMessageListenerContainerSearch(SearchSyncTaskCacheCleaner searchSyncTaskCacheCleaner,
                                                                      RedisConnectionFactory factory) {
        if (!searchSyncTaskCacheCleaner.isEnableDistribute()) {
            return null;
        }
        ChannelTopic topic = new ChannelTopic(SEARCH_PUB_SUB_TOPIC);
        SearchModelRedisMessageSubscriber subscriber = new SearchModelRedisMessageSubscriber(searchSyncTaskCacheCleaner, SENDER_ID);
        MessageListenerAdapter messageListenerAdapter = new MessageListenerAdapter(subscriber);
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(factory);
        container.addMessageListener(messageListenerAdapter, topic);
        return container;
    }


}
