package io.terminus.trantor2.search.expand.defaultimpl;

import io.terminus.trantor2.datasource.dto.PartitionDatasourceDto;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.search.expand.api.QueryTableDataByPageWhenFullSyncService;
import io.terminus.trantor2.search.expand.domain.SearchModelChangeData;
import io.terminus.trantor2.search.expand.manager.QueryDataByPageWhenFullSyncManager;
import io.terminus.trantor2.search.management.util.JooqDialectConnector;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.conf.ParamType;
import org.jooq.conf.Settings;
import org.jooq.impl.DSL;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 适用于模型分表场景
 * 全量同步时，分页查询数据默认实现方式
 */
@Service
@Slf4j
public class DefaultQueryTableDataByPageWhenFullSyncService extends BaseDataQueryWhenSync implements QueryTableDataByPageWhenFullSyncService {

    private DSLContext dslContext;

    /**
     * // todo 多数据库类型
     * select * from %s where id > %s and `isDeleted` = 0 order by id asc limit %s
     *
     * @param syncedRecordMaxId 上次查询返回的数据中最大的id值，
     *                          起始为0，查询数据返回后，内存中计算当前批次中最大的id值，并作为调用下一次该接口的入参值
     *                          当返回结果为空时，代表分页结束
     * @param step              分批次查询步长，即每次分页期望查询的条数，当实际返回数量小于期望分页返回数量时，代表分页结束
     * @param dataStructNode    模型元信息
     * @param tableName         物理表名
     * @return
     */
    @Override
    public SearchModelChangeData getSyncDataByPageWhenFullSync(long syncedRecordMaxId, int step,
                                                               DataStructNode dataStructNode, String tableName) {
        long teamId = dataStructNode.getTeamId();
        String modelKey = dataStructNode.getKey();
        String logicTableName = dataStructNode.getProps().getTableName();
        Map<String, PartitionDatasourceDto> datasourceDtoMap = businessDMLDataSourceFactory.getPartitionDataSourceByModel(teamId, modelKey, logicTableName);

        PartitionDatasourceDto datasourceDto = datasourceDtoMap.get(tableName);
        if (null == datasourceDto) {
            String errMsg = "can not find datasource when DefaultQueryTableDataByPageWhenFullSyncService, modelKey:"
                + modelKey + ",tableName:" + tableName;
            log.error(errMsg);
            throw new RuntimeException(errMsg);
        }
        JdbcTemplate jdbcTemplate = new JdbcTemplate(datasourceDto.getDataSource());

        if (null == dslContext) {
            dslContext = DSL.using(JooqDialectConnector.getSqlDialectByType(searchDumpProperties.getDefaultDialectType()),
                new Settings().withRenderFormatted(false));
        }

        String sql = dslContext.select()
            .from(DSL.table(DSL.name(tableName)))
            .where(DSL.field(DSL.name("id")).gt(syncedRecordMaxId))
            .and(DSL.field(DSL.name("deleted")).eq(0))
            .orderBy(DSL.field(DSL.name("id")).asc())
            .limit(step)
            .getSQL(ParamType.INLINED);

        List<Map<String, Object>> dbData = queryForList(jdbcTemplate, "SEARCH_FULL_TABLE_PARTITION_PAGE_QUERY", sql, null, sql);

        return convertDbData2SearchModelChangeData(dbData, dataStructNode);
    }

    @Override
    public String supportModel() {
        return QueryDataByPageWhenFullSyncManager.TABLE_PARTITION_DEFAULT;
    }
}
