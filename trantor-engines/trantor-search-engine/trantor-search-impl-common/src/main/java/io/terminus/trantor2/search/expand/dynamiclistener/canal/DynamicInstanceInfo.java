package io.terminus.trantor2.search.expand.dynamiclistener.canal;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class DynamicInstanceInfo {
    private String key;
    private String dbHost;
    private String dbUserName;
    private String dbPassword;
    private String listenTableRule;
    private String topic;
    private String dynamicTopicRule;

    @Override
    public String toString() {
        return "DynamicInstanceInfo{" +
            "dbHost='" + dbHost + '\'' +
            ", dbUserName='" + dbUserName + '\'' +
            ", listenTableRule='" + listenTableRule + '\'' +
            ", topic='" + topic + '\'' +
            ", dynamicTopicRule='" + dynamicTopicRule + '\'' +
            '}';
    }
}
