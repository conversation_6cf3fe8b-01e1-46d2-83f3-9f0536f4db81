package io.terminus.trantor2.search.management.config;

import com.alibaba.fastjson.JSONObject;
import io.terminus.trantor2.properties.SearchDumpProperties;
import io.terminus.trantor2.search.adaptor.EsHttpClientAdapter;
import io.terminus.trantor2.search.adaptor.LogLevel;
import io.terminus.trantor2.search.es.Es5ClientImpl;
import io.terminus.trantor2.search.es.Es6ClientImpl;
import io.terminus.trantor2.search.es.Es7ClientImpl;
import io.terminus.trantor2.search.es.EsOperationUtil;
import io.terminus.trantor2.search.management.util.HttpClientRes;
import io.terminus.trantor2.search.management.util.HttpClientUtils;
import io.terminus.trantor2.search.management.util.HttpMethodEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;


@Configuration
@EnableConfigurationProperties({SearchDumpProperties.class})
@Slf4j
public class SearchAutoConfiguration {

    @Autowired
    private SearchDumpProperties searchDumpProperties;

    @Bean
    public EsHttpClientAdapter esHttpClientAdapter() {
        EsHttpClientAdapter adapter = new EsHttpClientAdapter();

        String esVersion = getESVersion();
        log.info("now es version is:{}", esVersion);
        if (null == esVersion) {
            adapter.setEsClientApi(new Es5ClientImpl());
            return adapter;
        }

        // 设置不同的es集群实现
        if (esVersion.startsWith("7.")) {
            adapter.setEsClientApi(new Es7ClientImpl());
        } else if (esVersion.startsWith("6.")) {
            adapter.setEsClientApi(new Es6ClientImpl());
        } else {
            adapter.setEsClientApi(new Es5ClientImpl());
        }
        adapter.setSearchDumpProperties(searchDumpProperties);
        return adapter;
    }

    /**
     * 校验并获取es版本
     */
    private String getESVersion() {
        try {
            String host = searchDumpProperties.getElasticSearch().getHost();
            Integer port = searchDumpProperties.getElasticSearch().getPort();
            String url = EsOperationUtil.buildUrlPrefix(host, port).append("/").toString();
            Map<String, String> header = EsOperationUtil.makeReqHeader(searchDumpProperties.getElasticSearch().getAuthorizationBase64());
            HttpClientRes resp = HttpClientUtils.httpRequest(HttpMethodEnum.GET,
                url, null, header, LogLevel.debug);

            if (null != resp && null != resp.getResponseBody()) {
                JSONObject obj = JSONObject.parseObject(resp.getResponseBody());
                if (null != obj && null != obj.getJSONObject("version")) {
                    return obj.getJSONObject("version").getString("number");
                }
            }
        } catch (Exception e) {
            log.warn("---------- checkESVersion error...err:{}" + e.getMessage(), e);
        }
        return null;
    }
}
