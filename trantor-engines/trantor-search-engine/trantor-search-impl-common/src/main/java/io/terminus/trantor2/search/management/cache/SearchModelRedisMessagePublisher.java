package io.terminus.trantor2.search.management.cache;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.scripting.support.ResourceScriptSource;

import java.util.ArrayList;
import java.util.List;

import static io.terminus.trantor2.search.management.cache.CachePubSubConst.DS_SEARCH_VERSION_REDIS_KEY;
import static io.terminus.trantor2.search.management.cache.CachePubSubConst.PROJECT_MESSAGE_PUBLISH_SCRIPT;

@Slf4j
public class SearchModelRedisMessagePublisher {
    private final RedisTemplate<String, Object> redisTemplate;

    private final ChannelTopic topic;

    private final DefaultRedisScript<String> luaScript;

    private boolean redisPubSubNeedTransaction;

    SearchModelRedisMessagePublisher(RedisTemplate<String, Object> redisTemplate, ChannelTopic topic, boolean redisPubSubNeedTransaction) {
        this.redisTemplate = redisTemplate;
        this.topic = topic;

        // lua script初始化
        luaScript = new DefaultRedisScript<>();
        luaScript.setScriptSource(new ResourceScriptSource(new ClassPathResource(PROJECT_MESSAGE_PUBLISH_SCRIPT)));
        luaScript.setResultType(String.class);

        this.redisPubSubNeedTransaction = redisPubSubNeedTransaction;
    }

    public void publishAndUpdateRedisCache(Object message, String newCacheKey, String newCacheValue) {
        try {
            log.info("search redis is sending message:{}", JSONObject.toJSONString(message));
            redisPublishMessageAndRevision(message, newCacheKey, newCacheValue);
        } catch (DataAccessException e) {
            log.error("search redis message publisher error,message: {}, error: {}",
                JSONObject.toJSONString(message), e.getMessage(), e);
            throw e;
        }
    }

    // 发送模型变更消息
    public void redisPublishMessageAndRevision(Object message, String newCacheKey, String newCacheValue) {
        // 判断是否是CleanMessage消息
        if (isRevisionChanged(message)) {
            if (redisPubSubNeedTransaction) {
                doPubWithTranscation(message, newCacheKey, newCacheValue);
            } else {
                doPubWithoutTransaction(message, newCacheKey, newCacheValue);
            }
        } else {
            // 发送变更消息
            redisTemplate.convertAndSend(topic.getTopic(), message);
        }
    }

    /**
     * publish和版本号更新在一个事务内
     *
     * @param message
     */
    private void doPubWithTranscation(Object message, String newCacheKey, String newCacheValue) {
        // lua脚本执行keys设置
        List<String> luaKeys = new ArrayList<>();
        luaKeys.add(DS_SEARCH_VERSION_REDIS_KEY);
        luaKeys.add(topic.getTopic());
        luaKeys.add(CachePubSubConst.getDsSearchSubKey(newCacheKey));
        // lua脚本进行redis message publish
        redisTemplate.execute(luaScript, luaKeys, message, newCacheValue);
    }

    /**
     * publish和版本号更新不在一个事务内
     * k
     *
     * @param message
     */
    private void doPubWithoutTransaction(Object message, String newCacheKey, String newCacheValue) {
        redisTemplate.opsForHash().put(DS_SEARCH_VERSION_REDIS_KEY, CachePubSubConst.getDsSearchSubKey(newCacheKey),
            newCacheValue);
        redisTemplate.convertAndSend(topic.getTopic(), message);
    }

    private boolean isRevisionChanged(Object message) {
        return message instanceof SearchSyncTaskCacheCleaner.CleanMessage;
    }
}
