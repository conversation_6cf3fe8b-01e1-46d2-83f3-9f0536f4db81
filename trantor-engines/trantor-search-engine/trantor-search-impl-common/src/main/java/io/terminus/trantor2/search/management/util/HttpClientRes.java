package io.terminus.trantor2.search.management.util;

import lombok.Data;
import org.apache.http.Header;

import java.util.List;

/**
 * 类HttpClientRes.java的实现描述：http请求返回结果
 */
@Data
public class HttpClientRes {

    private int statusCode;

    private List<Header> heads;

    private String responseBody;

    public HttpClientRes(int statusCode, List<Header> heads, String responseBody) {
        this.statusCode = statusCode;
        this.heads = heads;
        this.responseBody = responseBody;
    }

    public Boolean isOk() {
        return statusCode >= 200 && statusCode < 300;
    }
}
