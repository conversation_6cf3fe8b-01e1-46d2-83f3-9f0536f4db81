package io.terminus.trantor2.search.expand.dynamiclistener;


import io.terminus.trantor2.model.management.meta.domain.DataStructNode;

/**
 * TODO
 * 更新cdc中数据库表同topic的监听关系
 */
public interface RebuildTopicRoute2CdcService {

    /**
     * 更新cdc中数据库表同topic的监听关系
     *
     * @param teamCode
     * @param dataStructNode
     */
    void rebuildTopicRoute2Cdc(String teamCode, DataStructNode dataStructNode);

    /**
     * 删除cdc中数据库表同topic的监听关系
     *
     * @param teamCode
     * @param dataStructNode
     */
    void deleteTopicRoute2Cdc(String teamCode, DataStructNode dataStructNode);

    String getType();
}
