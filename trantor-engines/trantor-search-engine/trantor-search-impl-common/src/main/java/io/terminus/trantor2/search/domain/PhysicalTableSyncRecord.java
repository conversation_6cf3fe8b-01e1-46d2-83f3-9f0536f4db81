package io.terminus.trantor2.search.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

@Data
@SuperBuilder
@NoArgsConstructor
public class PhysicalTableSyncRecord implements Serializable {
    /**
     * 同步的表名
     */
    private String tableName;
    /**
     * 已同步记录最大id
     */
    @Schema(description = "已同步记录最大id")
    private Long syncedRecordMaxId;

    /**
     * 已同步记录条数
     */
    @Schema(description = "已同步记录条数")
    private Long syncedRecordCount;
}
