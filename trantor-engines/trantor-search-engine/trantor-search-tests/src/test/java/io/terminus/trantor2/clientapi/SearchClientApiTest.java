package io.terminus.trantor2.clientapi;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import io.terminus.common.api.model.Paging;
import io.terminus.common.runtime.context.RequestContext;
import io.terminus.trantor2.BaseTest;
import io.terminus.trantor2.clientapi.model.DumpSearchRepo;
import io.terminus.trantor2.clientapi.model.SearchTestBO;
import io.terminus.trantor2.clientapi.request.DumpSearchReq;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.model.management.meta.cache.DataStructMetaCache;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.domain.search.SearchModelConfigMeta;
import io.terminus.trantor2.model.management.meta.domain.search.SearchModelFieldConfigMeta;
import io.terminus.trantor2.model.management.meta.model.SaveStructNodeRequest;
import io.terminus.trantor2.model.management.meta.service.DataStructNodeService;
import io.terminus.trantor2.search.adaptor.EsHttpClientAdapter;
import io.terminus.trantor2.search.domain.SearchModelSyncTask;
import io.terminus.trantor2.search.domain.SyncStatusEnum;
import io.terminus.trantor2.search.elasticsearch.client.SearchClient;
import io.terminus.trantor2.search.elasticsearch.model.SearchResult;
import io.terminus.trantor2.search.elasticsearch.wrapper.SearchWrapper;
import io.terminus.trantor2.search.elasticsearch.wrapper.TrantorSearchRequest;
import io.terminus.trantor2.search.management.api.SearchModelApi;
import io.terminus.trantor2.search.management.cache.SearchModelSyncTaskCache;
import io.terminus.trantor2.search.management.cache.SearchModelTaskManager;
import io.terminus.trantor2.search.management.dto.SearchModelCreateRequest;
import io.terminus.trantor2.search.repository.SearchModelSyncTaskRepository;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 搜索api测试
 */
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT,
    properties = {
        "ELASTICSEARCH_HOST=127.0.0.1",
        "ELASTICSEARCH_PORT=9200",
        "MYSQL_HOST=127.0.0.1",
        "MYSQL_PORT=3306",
        "server.port=8081"
    })
@ActiveProfiles("dump")
public class SearchClientApiTest extends BaseTest {

    @Autowired
    private SearchModelApi searchModelApi;

    @Autowired
    private DataStructNodeService dataStructNodeService;

    @Autowired
    private SearchModelTaskManager searchModelTaskManager;

    @Autowired
    private EsHttpClientAdapter esHttpClientAdapter;

    @Autowired
    private SearchModelSyncTaskRepository searchModelSyncTaskRepository;

    @Autowired
    private SearchModelSyncTaskCache cache;

    @Autowired
    private DataStructMetaCache dataStructMetaCache;

    @Autowired
    private SearchClient searchClient;
    @Autowired
    private DumpSearchRepo dumpSearchRepo;

    ////////////////////////////////////////////使用repo的方式/////////////////////////////////////////////////////
    @Test
    public void byId() {
        // 模型索引+数据准备
        DataStructNode dataStructNode = prepareSearchModelData();
        initRequestContext();

        // 按照id查询
        SearchTestBO searchTestBO = dumpSearchRepo.selectById(1L);
        Assert.assertTrue(searchTestBO.getName().equals("name1"));
    }

    @Test
    public void byReqAndAnnotation() {
        // 模型索引+数据准备
        DataStructNode dataStructNode = prepareSearchModelData();

        // EQUALS注解使用
        testEqual();
        testEquals();

        // NotEQUALS注解使用
        testNotEqual();

        // LIKE注解使用
        testLike();

        // Greater & Less注解使用
        testBetween();

    }

    @Test
    public void byAnalyzer() {
        // 模型索引+数据准备
        DataStructNode dataStructNode = prepareSearchModelData(true);
        initRequestContext();

        // Match注解使用
        DumpSearchReq req = new DumpSearchReq();
        req.setNameMatch("name");
        SearchWrapper searchWrapper = SearchWrapper.of(req);

        SearchResult<SearchTestBO> dumpSearch = dumpSearchRepo.search(searchWrapper);
        Assert.assertTrue(dumpSearch.getDocuments().getTotal() == 21);
    }

    @Test
    public void byPage() {
        // 模型索引+数据准备
        DataStructNode dataStructNode = prepareSearchModelData();
        initRequestContext();

        // 分页查询
        DumpSearchReq req = new DumpSearchReq();
        req.setPageNo(1);
        req.setPageSize(8);
        Paging<SearchTestBO> dumpSearch = dumpSearchRepo.selectPage(req);
        Assert.assertTrue(dumpSearch.getData().size() == 8);
        Assert.assertTrue(dumpSearch.getTotal() == 21);
    }

    private void testBetween() {
        initRequestContext();

        DumpSearchReq req = new DumpSearchReq();
        req.setAgeMax(12);
        req.setAgeMin(1);
        SearchWrapper searchWrapper = SearchWrapper.of(req);

        SearchResult<SearchTestBO> dumpSearch = dumpSearchRepo.search(searchWrapper);
        Assert.assertTrue(dumpSearch.getDocuments().getTotal() == 12);
    }

    private void testNotEqual() {
        initRequestContext();

        DumpSearchReq req = new DumpSearchReq();
        req.setNameNot("name1");
        SearchWrapper searchWrapper = SearchWrapper.of(req);

        SearchResult<SearchTestBO> dumpSearch = dumpSearchRepo.search(searchWrapper);
        Assert.assertTrue(dumpSearch.getDocuments().getTotal() == 20);
    }

    private void testLike() {
        initRequestContext();

        DumpSearchReq req = new DumpSearchReq();
        req.setName("name");
        SearchWrapper searchWrapper = SearchWrapper.of(req);

        SearchResult<SearchTestBO> dumpSearch = dumpSearchRepo.search(searchWrapper);
        Assert.assertTrue(dumpSearch.getDocuments().getTotal() == 21);
    }

    private void testEquals() {
        initRequestContext();

        DumpSearchReq req = new DumpSearchReq();
        req.setAges(Lists.newArrayList(7L, 8L));
        SearchWrapper searchWrapper = SearchWrapper.of(req);

        SearchResult<SearchTestBO> dumpSearch = dumpSearchRepo.search(searchWrapper);
        Assert.assertTrue(dumpSearch.getData().size() == 2);
        Assert.assertTrue(dumpSearch.getData().get(0).getAge() == 7);
        Assert.assertTrue(dumpSearch.getData().get(1).getAge() == 8);
    }

    private void testEqual() {
        initRequestContext();

        DumpSearchReq req = new DumpSearchReq();
        req.setAge(7);
        SearchWrapper searchWrapper = SearchWrapper.of(req);

        SearchResult<SearchTestBO> dumpSearch = dumpSearchRepo.search(searchWrapper);
        Assert.assertTrue(dumpSearch.getData().size() == 1);
        Assert.assertTrue(dumpSearch.getData().get(0).getAge() == 7);
    }

    /////////////////////////////////////////////////////////////////////////////////////////////////

    ////////////////////////////////////////////直接使用searchClient.search进行高级查询/////////////////////////////////////////////////////

    /**
     * es 原生高级查询 demo， 详细使用可直接参见 es high level 官方使用手册
     */
    @Test
    public void bySearchRequest() {
        // 模型索引+数据准备
        DataStructNode dataStructNode = prepareSearchModelData();
        initRequestContext();

        // 索引别名
        String alias = dumpSearchRepo.index().toLowerCase();
        //dataStructNode.getProps().getSearchModelConfigMeta().generateAlias(dataStructNode.getTeamId(),
        //  dataStructNode.getAppId(), dataStructNode.getAlias());

        // 构造es 原生 searchRequest
        TrantorSearchRequest searchRequest = new TrantorSearchRequest();
        searchRequest.setTeamCode(TrantorContext.getTeamCode());
        searchRequest.setModelKey("testAdd$dump_search");
        // 必须设置索引别名
        searchRequest.indices(alias);

        // term查询
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.termQuery("name", "name7"));
        searchSourceBuilder.from(0);
        searchSourceBuilder.size(8);

        searchRequest.source(searchSourceBuilder);

        SearchResult<SearchTestBO> searchResult = searchClient.search(searchRequest, SearchTestBO.class);
        Assert.assertTrue(searchResult.getData().size() == 1);
        Assert.assertTrue(searchResult.getData().get(0).getName().equals("name7"));
    }

    /**
     * 多字段match， es 原生高级查询 demo， 详细使用可直接参见 es high level 官方使用手册
     */
    @Test
    public void byMultiFieldsSearchRequest() {
        // 模型索引+数据准备
        DataStructNode dataStructNode = prepareSearchModelData(true);
        initRequestContext();

        // 索引别名
        String alias = dumpSearchRepo.index().toLowerCase();
        //dataStructNode.getProps().getSearchModelConfigMeta().generateAlias(dataStructNode.getTeamId(),
        //  dataStructNode.getAppId(), dataStructNode.getAlias());

        // 构造es 原生 searchRequest
        TrantorSearchRequest searchRequest = new TrantorSearchRequest();
        searchRequest.setTeamCode(TrantorContext.getTeamCode());
        searchRequest.setModelKey("testAdd$dump_search");
        // 必须设置索引别名
        searchRequest.indices(alias);

        // term查询
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.multiMatchQuery("name", "name", "email"));
        searchSourceBuilder.from(0);
        searchSourceBuilder.size(100);

        searchRequest.source(searchSourceBuilder);

        SearchResult<SearchTestBO> searchResult = searchClient.search(searchRequest, SearchTestBO.class);
        Assert.assertTrue(searchResult.getDocuments().getTotal() == 21);
    }

    /**
     * es 原生高级查询 demo， 详细使用可直接参见 es high level 官方使用手册
     */
    @Test
    public void bySearchRequestBool() {
        // 模型索引+数据准备
        DataStructNode dataStructNode = prepareSearchModelData();
        initRequestContext();

        // 索引别名
        String alias = dumpSearchRepo.index().toLowerCase();
        //dataStructNode.getProps().getSearchModelConfigMeta().generateAlias(dataStructNode.getTeamId(),
        //    dataStructNode.getAppId(), dataStructNode.getAlias());

        // 构造es 原生 searchRequest
        TrantorSearchRequest searchRequest = new TrantorSearchRequest();
        searchRequest.setTeamCode(TrantorContext.getTeamCode());
        searchRequest.setModelKey("testAdd$dump_search");
        // 必须设置索引别名
        searchRequest.indices(alias);

        // term查询
        // 创建 Bool 查询构建器
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 构建查询条件
        boolQueryBuilder.must(QueryBuilders.termsQuery("name", "name1", "name2", "name3"))
            .filter().add(QueryBuilders.rangeQuery("age").gte("2").lte("3"));
        // 构建查询源构建器
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.sort("name", SortOrder.ASC);
        searchSourceBuilder.from(0);
        searchSourceBuilder.size(8);

        searchRequest.source(searchSourceBuilder);

        SearchResult<SearchTestBO> searchResult = searchClient.search(searchRequest, SearchTestBO.class);
        Assert.assertTrue(searchResult.getData().size() == 2);
        Assert.assertTrue(searchResult.getData().get(0).getName().equals("name2"));
        Assert.assertTrue(searchResult.getData().get(1).getName().equals("name3"));
    }

    /**
     * es 原生高级查询 demo， 详细使用可直接参见 es high level 官方使用手册
     */
    @Test
    public void bySearchRequestAggs() {
        // 模型索引+数据准备
        DataStructNode dataStructNode = prepareSearchModelData();
        initRequestContext();

        // 索引别名
        String alias = dumpSearchRepo.index().toLowerCase();
        //dataStructNode.getProps().getSearchModelConfigMeta().generateAlias(dataStructNode.getTeamId(),
        //    dataStructNode.getAppId(), dataStructNode.getAlias());

        // 构造es 原生 searchRequest
        TrantorSearchRequest searchRequest = new TrantorSearchRequest();
        searchRequest.setTeamCode(TrantorContext.getTeamCode());
        searchRequest.setModelKey("testAdd$dump_search");
        // 必须设置索引别名
        searchRequest.indices(alias);

        AggregationBuilder aggr = AggregationBuilders.terms("age_bucket").field("boll");
        // 查询源构建器
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(10);
        searchSourceBuilder.aggregation(aggr);
        searchRequest.source(searchSourceBuilder);

        SearchResult<SearchTestBO> searchResult = searchClient.search(searchRequest, SearchTestBO.class);
        ParsedLongTerms aggregation = (ParsedLongTerms) searchResult.getAggregations().get("age_bucket");
        Assert.assertTrue(aggregation.getBuckets().size() == 2);
    }

    @Test
    public void fieldTypeTest() {
        // 模型索引+数据准备
        DataStructNode dataStructNode = prepareSearchModelData();
        initRequestContext();

        // bigDecimal测试
        SearchTestBO searchTestBO = dumpSearchRepo.selectById(1L);
        Assert.assertTrue(searchTestBO.getAmt().toString().equals("10284000.00"));
    }


    /////////////////////////////////////////////////////////////////////////////////////////////////
    private DataStructNode prepareSearchModelData() {
        return prepareSearchModelData(false);
    }

    private DataStructNode prepareSearchModelData(boolean needAnalyzer) {
        // 创建普通模型
        DataStructNode dataStructNode = mockDataStructNode();
        SaveStructNodeRequest request = createSaveRequest(dataStructNode);
        initContext();
        dataStructNodeService.save(request);


        // 更新模型搜索信息
        DataStructNode nodeInDb = dataStructMetaCache.getModelMeta(dataStructNode.getTeamCode(), dataStructNode.getAlias());
        nodeInDb.getProps().setSearchModel(true);
        SearchModelConfigMeta searchModelConfigMeta = new SearchModelConfigMeta();
        searchModelConfigMeta.setTopicName("testTopic");
        nodeInDb.getProps().setSearchModelConfigMeta(searchModelConfigMeta);
        nodeInDb.getChildren().forEach(field -> {
            field.getProps().setSearchModelFieldConfigMeta(new SearchModelFieldConfigMeta());
        });

        if (needAnalyzer) {
            DataStructFieldNode fieldNode = nodeInDb.getChildren().stream()
                .filter(field -> field.getAlias().equals("name"))
                .findAny()
                .get();
            JSONObject obj = new JSONObject();
            obj.put("type", "text");
            obj.put("analyzer", "ik_max_word");
            fieldNode.getProps().getSearchModelFieldConfigMeta().setMapping(obj);
        }

        formEsFieldTypeToDataStructNode(nodeInDb);

        boolean updateSuccess = false;
        while (!updateSuccess) {
            try {
                initContext();
                dataStructNodeService.updateSearchModel(createSaveRequest(nodeInDb));
                updateSuccess = true;
            } catch (Exception e) {
                if (e.getMessage().contains("trantor-dump: nodename nor servname provided, or not known")) {
                    updateSuccess = true;
                    Assert.assertTrue(true);
                } else {
                    e.printStackTrace();
                }
                // 忽略异常， 因为远程触发调用不通
            }
            sleepSecond(2);
        }
        DataStructNode newNode = dataStructMetaCache.getModelMeta(dataStructNode.getTeamCode(), dataStructNode.getAlias());

        // insert data to DataStructNode
        insertData2DataStructNodeWhenSearch();
        searchModelApi.createSearchModel(SearchModelCreateRequest.builder()
            .teamId(newNode.getTeamId())
            .appId(newNode.getAppId())
            .dataStructNode(newNode)
            .build());

        // 等待全量同步执行完
        sleepSecond(1);

        while (!searchModelTaskManager.getSyncTaskMap().isEmpty()) {
            sleepSecond(1);
        }

        // 全量同步完成后，searchModelSyncTask表中应该有running状态的task
        assertSyncSuccess(newNode);
        sleepSecond(1);
        return newNode;
    }

    private void assertSyncSuccess(DataStructNode newNode) {
        // 全量同步完成后，searchModelSyncTask表中应该有running状态的task
        List<SearchModelSyncTask> taskList = searchModelSyncTaskRepository.findByTeamCodeAndModelAliasAndSyncStatusEnum(newNode.getTeamCode(),
            newNode.getAlias(), SyncStatusEnum.RUNNING);
        Assert.assertFalse(CollectionUtils.isEmpty(taskList));
        Assert.assertEquals(1, taskList.size());
        SearchModelSyncTask syncTask = taskList.get(0);

        // 验证索引存在
        Assert.assertTrue(esHttpClientAdapter.isIndexExist(syncTask.getIndexName()));

        // 验证索引别名指向了索引
        String aliasName = newNode.getProps().getSearchModelConfigMeta().generateAlias(newNode.getTeamCode(), newNode.getAlias());
        Map<String, Set<String>> existAliasMapping = esHttpClientAdapter.queryAlias(aliasName);
        Assert.assertTrue(existAliasMapping.get(aliasName).contains(syncTask.getIndexName()));

        // 验证缓存都已经生效
        Assert.assertEquals(1, cache.getSearchModelSyncTask(newNode.getTeamCode(), newNode.getAlias()).size());
        Assert.assertEquals(1, cache.getCanalMqConsumerModelCache().size());

    }

    @NotNull
    private SaveStructNodeRequest createSaveRequest(DataStructNode dataStructNode) {
        SaveStructNodeRequest request = new SaveStructNodeRequest();
        request.setTeamId(dataStructNode.getTeamId());
        request.setAlias(dataStructNode.getAlias());
        request.setName(dataStructNode.getName());
        request.setProps(dataStructNode.getProps());
        request.setChildren(dataStructNode.getChildren());
        request.setParentKey(dataStructNode.getParentKey());
        if (null != dataStructNode.getId()) {
            request.setId(dataStructNode.getId());
        }
        return request;
    }

    private void initRequestContext() {
        RequestContext.setAppId(appId);
        RequestContext.setTenantId(teamId);
    }
}
