package io.terminus.trantor2;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Sets;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.common.utils.AesCBCUtil;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.datasource.entity.ModelDataSourceConfigMeta;
import io.terminus.trantor2.datasource.manager.BusinessDDLDataSourceHolderManager;
import io.terminus.trantor2.datasource.manager.BusinessDMLDataSourceHolderManager;
import io.terminus.trantor2.datasource.model.DataSourceExtraConfigMeta;
import io.terminus.trantor2.datasource.model.SupportDialectTypeEnum;
import io.terminus.trantor2.datasource.repository.DataSourceConfigRepository;
import io.terminus.trantor2.datasource.runtime.factory.BusinessDMLDataSourceFactory;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.ModuleInfo;
import io.terminus.trantor2.meta.api.dto.MoveTarget;
import io.terminus.trantor2.meta.api.dto.MoveTargetType;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.context.MetaContext;
import io.terminus.trantor2.meta.management.service.EditorMetaEditService;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.model.management.meta.cache.DataStructMetaCache;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.service.DataStructNodeService;
import io.terminus.trantor2.model.management.meta.util.DateUtils;
import io.terminus.trantor2.module.entity.TrantorTeamEntity;
import io.terminus.trantor2.module.repository.TrantorTeamRepository;
import io.terminus.trantor2.properties.SearchDumpProperties;
import io.terminus.trantor2.search.adaptor.EsHttpClientAdapter;
import io.terminus.trantor2.search.management.api.SearchModelApi;
import io.terminus.trantor2.search.management.cache.SearchModelSyncTaskCache;
import io.terminus.trantor2.search.management.cache.SearchModelTaskManager;
import io.terminus.trantor2.search.repository.SearchModelSyncTaskRepository;
import io.terminus.trantor2.test.tool.mysql.MysqlSpringTest;
import io.terminus.trantor2.test.tool.redis.RedisSpringTest;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.junit.After;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

@Slf4j
@RunWith(value = SpringRunner.class)
public class BaseTest implements RedisSpringTest, MysqlSpringTest {
    public static final Long teamId = 1L;
    public static final Long appId = 1L;
    public static final String teamCode = "QA";

    public static final String testAdd_module = "testAdd";
    public static final String testAdd2_module = "testAdd2";
    public static final String testAdd3_module = "testAdd3";

    @Autowired
    protected DataStructNodeService dataStructNodeService;

    @Autowired
    protected DataSourceConfigRepository modelDataSourceRepository;

    @Autowired
    protected JdbcTemplate jdbcTemplate;

    @Autowired
    protected EditorMetaEditService metaEditService;

    @Autowired
    protected TrantorTeamRepository trantorTeamRepository;

    @Autowired
    protected BusinessDMLDataSourceFactory businessDMLDataSourceFactory;

    @Autowired
    protected BusinessDMLDataSourceHolderManager businessDMLDataSourceHolderManager;

    @Autowired
    protected BusinessDDLDataSourceHolderManager businessDDLDataSourceHolderManager;
    @Autowired
    protected DataStructMetaCache dataStructMetaCache;
    @Autowired
    protected SearchModelApi searchModelApi;
    @Autowired
    protected SearchModelTaskManager searchModelTaskManager;

    @Autowired
    protected EsHttpClientAdapter esHttpClientAdapter;

    @Autowired
    protected SearchModelSyncTaskRepository searchModelSyncTaskRepository;

    @Autowired
    protected SearchModelSyncTaskCache cache;

    @Autowired
    protected SearchDumpProperties searchDumpProperties;

    protected MetaEditAndQueryContext ctx = EditUtil.newCtx(teamId,1L);

    @Before
    public void before() {
        ctx.setTeamCode(teamCode);
        // 上下文初始化
        initContext();
        // team初始化
        initTeam();
        // 数据源初始化
        initDataSource();

        // initMeta
        initMeta();
    }

    private void initMeta() {
        MetaContext.setCurrentDeployModules(Sets.newHashSet(ModuleInfo.of(ctx.getTeamCode(), testAdd_module),
            ModuleInfo.of(ctx.getTeamCode(), testAdd2_module), ModuleInfo.of(ctx.getTeamCode(), testAdd3_module)));

        metaEditService.initRepo(ctx);
        String rootKey = KeyUtil.ROOT_KEY;

        submitModuleMeta(testAdd_module, rootKey);
        submitModuleMeta(testAdd2_module, rootKey);
        submitModuleMeta(testAdd3_module, rootKey);

        Set<ModuleInfo> modulesCode = new HashSet<>();
        modulesCode.add(ModuleInfo.of(teamCode, testAdd_module));
        modulesCode.add(ModuleInfo.of(teamCode, testAdd2_module));
        modulesCode.add(ModuleInfo.of(teamCode, testAdd3_module));
        MetaContext.setCurrentDeployModules(modulesCode);

        searchDumpProperties.setSearchUnitTest(true);
    }

    private void submitModuleMeta(String testAdd_module, String rootKey) {
        try {
            metaEditService.submitOp(ctx,
                EditUtil.createNodeOp(
                    easyNodeO("Module", testAdd_module, testAdd_module + "-name", null, rootKey, null),
                    new MoveTarget(rootKey, MoveTargetType.ChildFirst)
                )
            ).get(testAdd_module).getId();
        } catch (IOException e) {
        }
    }

    private void initTeam() {
        TrantorTeamEntity teamEntity = new TrantorTeamEntity();
        teamEntity.setId(teamId);
        teamEntity.setCode(teamCode);
        teamEntity.setName("name");
        trantorTeamRepository.save(teamEntity);
    }

    @After
    public void clean() {
        trantorTeamRepository.deleteAll();
        TrantorContext.clear();

        modelDataSourceRepository.deleteAll();
        clearModel();
        clearData2DataStructNode();
        clearDataStructTable();
    }

    protected MetaTreeNode easyNodeO(String type, String key, String name, String description, String parentKey, ObjectNode props) throws IOException {
        MetaTreeNode node = new MetaTreeNode();
        {
            node.setType(type);
            node.setKey(key);
            node.setName(name);
            node.setDescription(description);
            node.setProps(props);
            node.setParentKey(parentKey);
        }
        return node;
    }

    protected void initContext() {
        TrantorContext.init();
        User user = new User();
        user.setId(1L);
        TrantorContext.setCurrentUser(user);
        TrantorContext.setTeamCode(teamCode);
        TrantorContext.setModuleKey(testAdd_module);
    }

    private void initDataSource() {
        createDatabase("trantor2_ceshi");

        createDatabase("trantor2");
        createDatasource(1L, "CESHI", "trantor2_ceshi", false);
        createDatasource(11L, "CESHI2", "trantor2", true);


        // test_a和test_c为同一个库，test_b为不同的库
        jdbcTemplate.execute("INSERT INTO `environment_configuration` (`id`, `created_at`, `created_by`, `updated_at`, `updated_by`, `version`, `config`, `module_key`, `team_id`, `type`)\n" +
            "VALUES\n" +
            "\t(1, '2023-09-28 23:25:58.333000', 435423901065925, '2023-09-28 23:25:58.333000', 435423901065925, 0, '{\\\"moduleKey\\\": \\\"" + testAdd_module + "\\\", \\\"datasourceName\\\": \\\"CESHI\\\"}', '" + testAdd_module + "', 1, 'Module_Datasource')");

        jdbcTemplate.execute("INSERT INTO `environment_configuration` (`id`, `created_at`, `created_by`, `updated_at`, `updated_by`, `version`, `config`, `module_key`, `team_id`, `type`)\n" +
            "VALUES\n" +
            "\t(11, '2023-09-28 23:25:58.333000', 435423901065925, '2023-09-28 23:25:58.333000', 435423901065925, 0, '{\\\"moduleKey\\\": \\\"" + testAdd2_module + "\\\", \\\"datasourceName\\\": \\\"CESHI2\\\"}', '" + testAdd2_module + "', 1, 'Module_Datasource')");

        jdbcTemplate.execute("INSERT INTO `environment_configuration` (`id`, `created_at`, `created_by`, `updated_at`, `updated_by`, `version`, `config`, `module_key`, `team_id`, `type`)\n" +
            "VALUES\n" +
            "\t(111, '2023-09-28 23:25:58.333000', 435423901065925, '2023-09-28 23:25:58.333000', 435423901065925, 0, '{\\\"moduleKey\\\": \\\"" + testAdd3_module + "\\\", \\\"datasourceName\\\": \\\"CESHI\\\"}', '" + testAdd3_module + "', 1, 'Module_Datasource')");


    }

    protected void createDatabase(String name) {
        String dropSql = "drop database " + name + ";";
        String createSql = "create database " + name + ";";
        try {
            jdbcTemplate.execute(dropSql);
        } catch (Exception e) {
            log.warn("drop database error, {}", e.getMessage());
        } finally {
            jdbcTemplate.execute(createSql);
        }
    }

    protected void createDatasource(Long id, String dataSourceName, String dbName, boolean defaultDB) {
        ModelDataSourceConfigMeta configMeta = getModelDataSourceConfigMeta(id, dataSourceName, dbName, defaultDB);

        Optional<ModelDataSourceConfigMeta> dbMeta = modelDataSourceRepository.findById(id);
        if (!dbMeta.isPresent()) {
            modelDataSourceRepository.save(configMeta);
        }
    }

    @NotNull
    protected static ModelDataSourceConfigMeta getModelDataSourceConfigMeta(Long id, String dataSourceName, String dbName, boolean defaultDB) {
        String encrypt = AesCBCUtil.encrypt(PASSWORD, "terminus_trantor", "terminus_trantor");
        Integer port = mysqlContainer.getFirstMappedPort();

        ModelDataSourceConfigMeta configMeta = new ModelDataSourceConfigMeta();
        configMeta.setDataSourceName(dataSourceName);
        configMeta.setExtraConfig(JSONObject.parseObject("{\"maxPoolSize\":200,\"minPoolSize\":5,\"connectionTimeout\":3000,\"idleTimeout\":600000,\"maxLifeTime\":300000,\"connectionTestQuery\":\"SELECT 1\"}",
            DataSourceExtraConfigMeta.class));
        configMeta.setDbHost("127.0.0.1");
        configMeta.setTeamId(teamId);
        configMeta.setDbName(dbName);
        configMeta.setDbPassword(encrypt);
        configMeta.setDbPort(port);
        configMeta.setDbUsername(USER_NAME);
        configMeta.setDbType(SupportDialectTypeEnum.MYSQL);
        configMeta.setCreatedBy(1L);
        configMeta.setId(id);
        configMeta.setDefaultConfig(defaultDB);
        return configMeta;
    }

    public DataStructNode mockModel(String modelStr) {
        return JsonUtil.fromJson(modelStr, DataStructNode.class);
    }


    public void clearDataStructTable() {
        dropTable(teamId, testAdd_module, "drop table `dump_search`");
        dropTable(teamId, testAdd_module, "drop table `dump_search_2`");

    }

    public void clearData2DataStructNode() {
        try {
            JdbcTemplate jdbcTemplate1 = new JdbcTemplate(businessDMLDataSourceFactory.getDataSourceByTeamIdAndModule(teamId, testAdd_module));
            jdbcTemplate1.execute("delete from `dump_search`");
            jdbcTemplate1.execute("delete from `dump_search_2`");
        } catch (Exception e) {
        }
    }

    protected void dropTable(long teamId, String testAdd_module, String sql) {
        try {
            JdbcTemplate jdbcTemplate1 = new JdbcTemplate(businessDMLDataSourceFactory.getDataSourceByTeamIdAndModule(teamId, testAdd_module));
            jdbcTemplate1.execute(sql);
        } catch (Exception e) {
        }
    }

    public void clearModel() {
        jdbcTemplate.execute("delete from `search_model_sync_task`");
        jdbcTemplate.execute("delete from `environment_configuration`");
    }


    public DataStructNode mockDataStructNode() {
        return JSONObject.parseObject(Constants.PERSIST_MODEL_ADD_STR, DataStructNode.class);
    }

    public DataStructNode mockDataStructNode2() {
        return JSONObject.parseObject(Constants.PERSIST_MODEL_ADD_STR2, DataStructNode.class);
    }

    public DataStructNode mockDataStructNode_normal() {
        return JSONObject.parseObject(Constants.normal_node_add, DataStructNode.class);
    }

    public void insertData2DataStructNode() {
        JdbcTemplate jdbcTemplate1 = new JdbcTemplate(businessDMLDataSourceFactory.getDataSourceByTeamIdAndModule(teamId, testAdd_module));
        jdbcTemplate1.execute("INSERT INTO `dump_search` (`id`, `created_by`, `updated_by`, `created_at`, `updated_at`," +
            " `version`, `deleted`, `name`, `age`, `riqi`, `email`, `boll`, `arrayintfield`, `arraystringfield`," +
            " `jsonfield`,`jsonarrayfield`)\n" +
            "VALUES\n" +
            "\t(1, NULL, NULL, '2022-11-11 11:11:11', '2022-11-11 11:11:11', 0, 0, 'name1', 11, '2022-11-11 11:11:11'," +
            " '<EMAIL>', 1, '[123,456]','[\"123\",\"456\"]','{\"name\":\"zw\",\"age\":\"35\"}'" +
            ",'[{\"name\":\"zw\",\"age\":\"35\"}]');");

        jdbcTemplate1.execute("INSERT INTO `dump_search` (`id`, `created_by`, `updated_by`, `created_at`, `updated_at`," +
            " `version`, `deleted`, `name`, `age`, `riqi`, `email`, `boll`, `arrayintfield`, `arraystringfield`," +
            " `jsonfield`,`jsonarrayfield`)\n" +
            "VALUES\n" +
            "\t(11, NULL, NULL, '2022-11-11 11:11:11', '2022-11-11 11:11:11', 0, 0, 'name1', 11, '2022-11-11 11:11:11'," +
            " '<EMAIL>', 0, '[123,456]','[\"123\",\"456\"]','{\"name\":\"zw\",\"age\":\"35\"}'" +
            ",'[{\"name\":\"zw\",\"age\":\"35\"}]');");
    }

    public void insertData2DataStructNodeBatch() {
        JdbcTemplate jdbcTemplate1 = new JdbcTemplate(businessDMLDataSourceFactory.getDataSourceByTeamIdAndModule(teamId, testAdd_module));
        for (int i = 1; i <= 3386; i++) {
            jdbcTemplate1.execute("INSERT INTO `dump_search` (`id`, `created_by`, `updated_by`, `created_at`, `updated_at`, `version`, `deleted`, `name`, `age`, `riqi`, `email`, `boll`)\n" +
                "VALUES\n" +
                "\t(" + i + ", NULL, NULL, '2022-11-11 11:11:11', '2022-11-11 11:11:11', 0, 0, 'name1', 11, '2022-11-11 11:11:11', '<EMAIL>', 1);");
        }
    }

    public void insertData2DataStructNodeWhenSearch() {
        JdbcTemplate jdbcTemplate1 = new JdbcTemplate(businessDMLDataSourceFactory.getDataSourceByTeamIdAndModule(teamId, testAdd_module));
        for (int i = 1; i <= 21; i++) {
            String email = "'<EMAIL>'";
            BigDecimal bigDecimal = null;
            if (i % 2 == 0) {
                email = null;
            }

            if (i % 3 == 0) {
                bigDecimal = new BigDecimal("1000000000.00");
            } else if (i % 3 == 1) {
                bigDecimal = new BigDecimal(10284000.0);
            } else {
                bigDecimal = BigDecimal.valueOf(1.0);
            }

            jdbcTemplate1.execute("INSERT INTO `dump_search` (`id`, `created_by`, `updated_by`, `created_at`, `updated_at`, `version`," +
                " `deleted`, `name`, `age`, `riqi`, `email`, `boll`, `amt`, `arrayintfield`, `arraystringfield`,`jsonfield`,`jsonarrayfield`)\n" +
                "VALUES\n" +
                "\t(" + i + ", NULL, NULL, '" + DateUtils.formatDate(new Date()) + "', '" + DateUtils.formatDate(new Date()) + "'" +
                ", 0, 0, 'name" + i + "', " + i + ", '" + DateUtils.formatDate(new Date()) + "', " + email + ", " + i % 2 + "," + bigDecimal + "," +
                "'[123,456]','[\"123\",\"456\"]','{\"name\":\"zw\",\"age\":\"35\"}','[{\"name\":\"zw\",\"age\":\"35\"}]');");
            sleepMil(500);
        }
    }

    protected void sleepMil(int num) {
        try {
            Thread.sleep(num);
        } catch (InterruptedException e) {
        }
    }

    protected void sleepSecond(int second) {
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
        }
    }

    public void insertData2DataStructNode2() {
        JdbcTemplate jdbcTemplate1 = new JdbcTemplate(businessDMLDataSourceFactory.getDataSourceByTeamIdAndModule(teamId, testAdd_module));
        jdbcTemplate1.execute("INSERT INTO `dump_search_2` (`id`, `created_by`, `updated_by`, `created_at`, `updated_at`, `version`, `deleted`, `name_2`, `age_2`, `riqi`, `email`, `boll`)\n" +
            "VALUES\n" +
            "\t(1, NULL, NULL, '2022-11-11 11:11:11', '2022-11-11 11:11:11', 0, 0, 'name2', 22, '2022-11-11 11:11:11', '<EMAIL>', 1);");

        jdbcTemplate1.execute("INSERT INTO `dump_search_2` (`id`, `created_by`, `updated_by`, `created_at`, `updated_at`, `version`, `deleted`, `name_2`, `age_2`, `riqi`, `email`, `boll`)\n" +
            "VALUES\n" +
            "\t(11, NULL, NULL, '2022-11-11 11:11:11', '2022-11-11 11:11:11', 0, 0, 'name2', 22, '2022-11-11 11:11:11', '<EMAIL>', 0);");
    }

    public static void formEsFieldTypeToDataStructNode(DataStructNode nodeInDb) {
        // 用户自定义字段mapping信息
        JSONObject fieldObj = JSONObject.parseObject("{\n" +
            "        \"type\": \"array\"\n" +
            "      }");
        JSONObject fieldObj2 = JSONObject.parseObject("{\n" +
            "        \"type\": \"object\"\n" +
            "      }");
        nodeInDb.getChildren().forEach(field -> {
            if (field.getAlias().equals("arrayintfield")
                || field.getAlias().equals("arraystringfield")) {
                field.getProps().getSearchModelFieldConfigMeta().setMapping(fieldObj);
            }
            if (field.getAlias().equals("jsonfield")
                || field.getAlias().equals("jsonarrayfield")) {
                field.getProps().getSearchModelFieldConfigMeta().setMapping(fieldObj2);
            }
        });
    }
}
