package io.terminus.trantor2.clientapi.request;


import io.terminus.common.api.request.AbstractPageRequest;
import io.terminus.trantor2.search.elasticsearch.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
public class DumpSearchReq extends AbstractPageRequest {
    private static final long serialVersionUID = 330499869480877535L;

    @Like
    private String name;

    @Equals
    private Integer age;

    @Equals(field = "age")
    private List<Long> ages;

    @NotEquals(field = "name")
    private String nameNot;

    @Less(field = "age")
    private Integer ageMax;

    @Greater(field = "age")
    private Integer ageMin;

    @Match(field = "name")
    private String nameMatch;

}
