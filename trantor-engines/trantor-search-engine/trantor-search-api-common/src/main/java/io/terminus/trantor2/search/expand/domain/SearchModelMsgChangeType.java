package io.terminus.trantor2.search.expand.domain;

import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.Set;

public enum SearchModelMsgChangeType {

    INSERT("INSERT"),
    UPDATE("UPDATE"),
    DELETE("DELETE");
    private String code;

    SearchModelMsgChangeType(String code) {
        this.code = code;
    }

    /**
     * 类型查找
     *
     * @param value
     * @return 数据库类型
     */
    public static SearchModelMsgChangeType find(String value) {
        for (SearchModelMsgChangeType supportTypeEnum : values()) {
            if (supportTypeEnum.name().equalsIgnoreCase(value)) {
                return supportTypeEnum;
            }
        }
        return null;
    }


    public static Set<SearchModelMsgChangeType> decodeType(String type) {
        Set<SearchModelMsgChangeType> result = new HashSet<>();
        if (StringUtils.isEmpty(type)) {
            return result;
        }
        for (SearchModelMsgChangeType searchModelMsgChangeTypeEnum : SearchModelMsgChangeType.values()) {
            if (type.contains(searchModelMsgChangeTypeEnum.code)) {
                result.add(searchModelMsgChangeTypeEnum);
            }
        }
        return result;
    }

    public String getCode() {
        return code;
    }

    /**
     * 校验订阅类型是否合法
     *
     * @param value
     * @return
     */
    public static boolean validate(String value) {
        if (StringUtils.isEmpty(value)) {
            return false;
        }
        Set<SearchModelMsgChangeType> searchModelMsgChangeTypes = decodeType(value);
        return value.length() == searchModelMsgChangeTypes.size();
    }
}
