package io.terminus.trantor2.search.mqimpl.util;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MqIllegalCheckUtils {
    /**
     * mq对于topic和group的命名规则定义
     */
    public static final String MQ_TOPIC_AND_GROUP_REGEX = "^[%|a-zA-Z0-9_-]+$";

    /**
     * 判断是否满足mq对于topic和group命名的规则校验，如果不满足，则将不满足规则的字符删除
     *
     * @param needFormatStr
     * @return
     */
    public static String mqIllegalCharactersReplace(String needFormatStr) {
        if (!needFormatStr.matches(MQ_TOPIC_AND_GROUP_REGEX)) {
            StringBuilder buffer = new StringBuilder();
            for (char spiltC : needFormatStr.toCharArray()) {
                if (String.valueOf(spiltC).matches(MQ_TOPIC_AND_GROUP_REGEX)) {
                    buffer.append(spiltC);
                }
            }
            return buffer.toString();
        }
        return needFormatStr;
    }
}
