package io.terminus.trantor2.search.mqimpl.annotation;

import org.springframework.stereotype.Component;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Component
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface SearchModelEventListener {

    /**
     * 当前Listener监听的模型
     *
     * @return
     */
    String[] listenModels() default {"*"};

}
