package io.terminus.trantor2.search.mqimpl.consumer;

import com.aliyun.openservices.ons.api.Consumer;
import com.aliyun.openservices.ons.api.MessageListener;
import com.aliyun.openservices.ons.api.ONSFactory;
import com.aliyun.openservices.ons.api.PropertyKeyConst;
import io.terminus.common.rocketmq.autoconfigure.MQProperties;
import io.terminus.common.rocketmq.consumer.TerminusMQConsumer;
import io.terminus.common.rocketmq.listener.AbstractMessageListener;
import io.terminus.trantor2.properties.SearchDumpProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;

import java.util.Properties;
import java.util.Set;

/**
 * Ons 普通消费者
 */
@Slf4j
public class SearchModelOnsConsumer extends TerminusMQConsumer {
    private Consumer consumer;
    private Properties properties = new Properties();
    private MessageListener listener;

    public SearchModelOnsConsumer(MQProperties mqProperties, SearchDumpProperties searchDumpProperties,
                                  String consumerGroup, MessageModel messageModel, String consumeThreadNums) {
        this.properties.put(PropertyKeyConst.GROUP_ID, consumerGroup);
        this.properties.put(PropertyKeyConst.AccessKey, mqProperties.getAliyun().getAccessKey());
        this.properties.put(PropertyKeyConst.SecretKey, mqProperties.getAliyun().getSecretKey());
        this.properties.put(PropertyKeyConst.NAMESRV_ADDR, mqProperties.getMqServerAddress());
        this.properties.put(PropertyKeyConst.MaxReconsumeTimes,
            String.valueOf(searchDumpProperties.getMqSubscribe().getMaxReconsumeTimes()));
        this.properties.put(PropertyKeyConst.ConsumeThreadNums, StringUtils.isNotEmpty(consumeThreadNums) ?
            consumeThreadNums :
            String.valueOf(searchDumpProperties.getMqSubscribe().getConsumeThreadNums()));
        this.properties.put(PropertyKeyConst.ConsumeMessageBatchMaxSize,
            String.valueOf(searchDumpProperties.getMqSubscribe().getConsumeMessageBatchMaxSize()));
        if (messageModel != null) {
            this.properties.put("MessageModel", messageModel.getModeCN());
        }

        this.consumer = ONSFactory.createConsumer(this.properties);
    }

    @Override
    public void start() {
        this.consumer.start();
    }

    @Override
    public void subscribe(String topic, Set<String> tags) {
        this.consumer.subscribe(topic, StringUtils.join(tags, "||"), this.listener);
    }

    @Override
    public String getConsumerGroup() {
        return properties.getProperty(PropertyKeyConst.GROUP_ID);
    }

    @Override
    public void setMessageListener(AbstractMessageListener listener) {
        this.listener = (MessageListener) listener;
    }

    @Override
    public void stop() {
        try {
            this.consumer.shutdown();
        } catch (Exception var2) {
            log.error("Ons consumer stop error:" + var2.getMessage(), var2);
        }
    }
}
