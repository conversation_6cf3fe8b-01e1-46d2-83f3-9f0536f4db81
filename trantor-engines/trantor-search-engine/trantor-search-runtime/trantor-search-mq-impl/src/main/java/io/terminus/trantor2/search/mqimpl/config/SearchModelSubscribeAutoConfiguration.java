package io.terminus.trantor2.search.mqimpl.config;

import io.terminus.common.rocketmq.autoconfigure.MQProperties;
import io.terminus.trantor2.properties.SearchDumpProperties;
import io.terminus.trantor2.search.mqimpl.bootstrap.AbstractSearchModelMsgListenerManager;
import io.terminus.trantor2.search.mqimpl.bootstrap.SearchSearchModelMsgListenerManager;
import io.terminus.trantor2.search.mqimpl.process.MessageProcessAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.Resource;

/**
 * 消费管理者注入
 */
@Configuration
@EnableConfigurationProperties({SearchDumpProperties.class})
@Slf4j
public class SearchModelSubscribeAutoConfiguration {

    @Resource
    private MQProperties mqProperties;

    @Resource
    private MessageProcessAdapter messageProcessAdapter;

    @Resource
    private SearchDumpProperties searchDumpProperties;

    @Bean
    public AbstractSearchModelMsgListenerManager searchModelMsgListenerManager() {
        log.info("start init bean AbstractSearchModelMsgListenerManager");
        return new SearchSearchModelMsgListenerManager(mqProperties, messageProcessAdapter,
            searchDumpProperties);
    }
}
