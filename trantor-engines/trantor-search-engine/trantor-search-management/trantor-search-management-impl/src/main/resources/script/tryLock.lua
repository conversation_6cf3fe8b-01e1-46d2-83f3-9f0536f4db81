redis.replicate_commands()
--local projectLockKey = tostring(KEYS[1]) KEYS[1]代表全量同步锁key
--local projectHeartBeatLockKey = tostring(KEYS[2]) KEYS[2]代表全量同步锁心跳key
local redisValue = tostring(ARGV[1])
local expireTime = tonumber(ARGV[2])
local nowTimes = redis.call('TIME')
local nowTime = nowTimes[1]
local maxTime = tonumber(ARGV[3])

if redis.call("SET", KEYS[1], redisValue, "EX", expireTime, "NX") then
    redis.call('SET', KEYS[2], nowTime, "EX", expireTime)
    return "1"
else
    local heartbeatValue = redis.call("GET", KEYS[2]);
    if heartbeatValue then
        local heartbeatValueNum = tonumber(heartbeatValue)
        if (nowTime - heartbeatValueNum) > maxTime then
            redis.call("SET",KEYS[1], redisValue, "EX", expireTime)
            redis.call('SET', KEYS[2], nowTime, "EX", expireTime)
            return "1"
        end
    else
        redis.call("SET",KEYS[1], redisValue, "EX", expireTime)
        redis.call('SET', KEYS[2], nowTime, "EX", expireTime)
        return "1"
    end
end
return redis.call("GET",KEYS[1])