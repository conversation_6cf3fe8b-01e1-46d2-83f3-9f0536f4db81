package io.terminus.trantor2.search.management.task.reindex;

import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.search.expand.domain.SearchModelChangeData;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Data
@Slf4j
public class IncTableReindexTask extends AbstractTableReindexTask {
    /**
     * 增量修复的数据
     */
    private List<Object> ids;

    @Override
    protected SearchModelChangeData queryNeedSyncData(long syncedRecordMaxId, int step, DataStructNode dataStructNode) {
        return assistService.getSyncDataWhenIncSync(syncedRecordMaxId, step, dataStructNode, ids);
    }

    @Override
    protected SearchModelChangeData queryTablePartitionNeedSyncData(long syncedRecordMaxId, int step,
                                                                    DataStructNode dataStructNode, String tableName) {
        return assistService.getTablePartitionSyncDataWhenIncSync(syncedRecordMaxId, step, dataStructNode, ids, tableName);
    }

    @Override
    protected SearchModelChangeData convertData2EsDataStruct(SearchModelChangeData changeData, DataStructNode dataStructNode) {
        return assistService.convertData2EsDataStruct(changeData, dataStructNode);
    }
}
