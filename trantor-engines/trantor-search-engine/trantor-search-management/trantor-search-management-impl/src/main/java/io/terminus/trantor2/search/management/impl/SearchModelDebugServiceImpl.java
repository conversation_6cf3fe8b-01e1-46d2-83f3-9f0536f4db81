package io.terminus.trantor2.search.management.impl;

import com.google.common.collect.Lists;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.properties.SearchDumpProperties;
import io.terminus.trantor2.search.adaptor.EsHttpClientAdapter;
import io.terminus.trantor2.search.domain.SearchModelSyncTask;
import io.terminus.trantor2.search.domain.SyncStatusEnum;
import io.terminus.trantor2.search.es.EsBuilder;
import io.terminus.trantor2.search.exception.SearchModelException;
import io.terminus.trantor2.search.expand.dynamiclistener.RebuildTopicRoute2CdcManager;
import io.terminus.trantor2.search.management.cache.SearchModelSyncTaskCache;
import io.terminus.trantor2.search.management.cache.SearchModelTaskManager;
import io.terminus.trantor2.search.management.dto.SearchModelIncSyncRequest;
import io.terminus.trantor2.search.management.impl.api.SearchModelAssistService;
import io.terminus.trantor2.search.management.impl.api.SearchModelDebugService;
import io.terminus.trantor2.search.management.task.SearchModelIncSyncTask;
import io.terminus.trantor2.search.management.task.SearchModelSyncTaskExecutor;
import io.terminus.trantor2.search.management.task.TaskSyncTypeEnum;
import io.terminus.trantor2.search.management.util.SyncTaskErrorMessage;
import io.terminus.trantor2.search.manager.SearchEngineStartManager;
import io.terminus.trantor2.search.repository.SearchModelSyncTaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static io.terminus.trantor2.common.exception.ErrorType.SEARCH_TASK_POOL_EXECUTE_ERROR;
import static io.terminus.trantor2.search.management.impl.SearchModelServiceImpl.getUserName;

/**
 * 搜索模型运维service
 */
@Service
@Slf4j
public class SearchModelDebugServiceImpl implements SearchModelDebugService {

    @Autowired
    private RebuildTopicRoute2CdcManager rebuildTopicRoute2CdcManager;

    @Autowired
    private SearchEngineStartManager startManager;

    @Autowired
    private SearchModelSyncTaskRepository repository;

    @Autowired
    private SearchModelSyncTaskCache cache;

    @Autowired
    private SearchDumpProperties searchDumpProperties;

    @Autowired
    private SearchModelSyncTaskExecutor executor;

    @Autowired
    private SearchModelAssistService assistService;

    @Autowired
    private EsHttpClientAdapter esHttpClientAdapter;

    @Autowired
    private SearchModelTaskManager searchModelTaskManager;

    @Override
    public void refreshCanal() {
        startManager.initCdc();

        List<SearchModelSyncTask> syncTasks = repository.findBySyncStatusEnumIn(Lists.newArrayList(SyncStatusEnum.RUNNING));
        if (CollectionUtils.isEmpty(syncTasks)) {
            return;
        }

        syncTasks.forEach(task -> {
            rebuildTopicRoute2CdcManager.rebuildTopicRoute2Cdc(task.getTeamCode(),
                cache.getLatestModelMeta(task.getModelAlias(), task.getTeamCode()));
        });

    }

    @Override
    public void incSyncSearchModel(SearchModelIncSyncRequest request) {
        // 构建异步处理全量同步任务
        SearchModelIncSyncTask syncTask = createIncSyncTask(request);

        try {
            // 异步执行
            executor.executeSyncTask(syncTask);
        } catch (Exception e) {
            String errMsg = e.getMessage();
            throw new SearchModelException(SEARCH_TASK_POOL_EXECUTE_ERROR, errMsg, new Object[]{errMsg});
        }
    }

    private SearchModelIncSyncTask createIncSyncTask(SearchModelIncSyncRequest request) {
        String teamCode = request.getTeamCode();
        DataStructNode dataStructNode = cache.getLatestModelMeta(request.getModelAlias(), request.getTeamCode());
        TaskSyncTypeEnum taskSyncTypeEnum = TaskSyncTypeEnum.INC_SYNC;
        String reason = request.getReason();
        // 创建执行任务
        SearchModelIncSyncTask syncTask = SearchModelIncSyncTask.builder()
            .teamCode(teamCode)
            .dataStructNode(dataStructNode)
            .esHttpClientAdapter(esHttpClientAdapter)
            .indexType(EsBuilder.getEsType())
            .searchModelTaskManager(searchModelTaskManager)
            .searchDumpProperties(searchDumpProperties)
            .assistService(assistService)
            .syncTaskErrorMessage(new SyncTaskErrorMessage())
            .syncTypeEnum(taskSyncTypeEnum)
            .threadName(Thread.currentThread().getName())
            .syncReason(reason)
            .executor(getUserName())
            .ids(request.getIds())
            .taskExecutor(executor)
            .build();

        // 任务管理
        searchModelTaskManager.putSyncTask(syncTask);
        return syncTask;
    }
}
