package io.terminus.trantor2.search.management.task;

import cn.hutool.core.util.RandomUtil;
import com.google.common.collect.Maps;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.properties.SearchDumpProperties;
import io.terminus.trantor2.search.adaptor.EsHttpClientAdapter;
import io.terminus.trantor2.search.domain.SearchModelSyncTask;
import io.terminus.trantor2.search.management.cache.SearchModelTaskManager;
import io.terminus.trantor2.search.management.impl.api.SearchModelAssistService;
import io.terminus.trantor2.search.management.task.reindex.AbstractTableReindexTask;
import io.terminus.trantor2.search.management.util.DrivingRecorderUtil;
import io.terminus.trantor2.search.management.util.SyncTaskErrorMessage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * 搜索模型同步任务
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
public abstract class SearchModelAbstractSyncTask extends SyncTaskAdapter {
    // 最长全量同步等待72h
    public static final int MAX_THREAD_WAIT_HOUR = 72;
    protected String teamCode;

    /**
     * 搜索模型元信息
     */
    protected DataStructNode dataStructNode;

    /**
     * es工具类
     */
    protected EsHttpClientAdapter esHttpClientAdapter;

    /**
     * es type类型
     */
    protected String indexType;

    /**
     * 同步任务管理器
     */
    protected SearchModelTaskManager searchModelTaskManager;

    /**
     * 搜索模型相关配置信息
     */
    protected SearchDumpProperties searchDumpProperties;

    /**
     * 搜索模型同步协助类
     */
    protected SearchModelAssistService assistService;

    /**
     * 异常message封装
     */
    protected SyncTaskErrorMessage syncTaskErrorMessage;

    /**
     * 全量同步触发类型
     */
    protected TaskSyncTypeEnum syncTypeEnum;

    /**
     * 启动同步任务的线程名
     */
    protected String threadName;

    /**
     * 同步原因
     */
    protected String syncReason;

    /**
     * 执行人
     */
    protected String executor;

    protected SearchModelSyncTaskExecutor taskExecutor;

    /**
     * 物理表同步任务列表
     */
    protected List<AbstractTableReindexTask> physicalTableBulkTasks;

    /**
     * todo 后面抽象一波
     * 全量同步分为两部分
     * 1、索引diff
     * 1、索引重建
     * 2、数据reindex
     */
    @Override
    public void run() {
        long start = System.currentTimeMillis();
        MDC.put("requestId", RandomUtil.randomString(10));

        // 模型别名
        String modelAlias = dataStructNode.getAlias();

        // 模型变更元信息比对
        ModelChangeRecognition modelChangeRecognition = recognizeModelChange();

        // 模型定义存在修改后，做最细粒度更新（字段新增、删除不需要进行索引重建，字段修改需要进行索引重建）
        doMinimumUpdateWhenSearchModelDefinitionUpdated(modelChangeRecognition);

        SearchModelSyncTask task = null;
        String needSyncIndexName = null;
        try {
            if (modelChangeRecognition.onlyHasFieldAdd() && Objects.equals(syncTypeEnum, TaskSyncTypeEnum.META_CHANGE)) {
                // 如果只有字段新增场景，那么后续都不需要进行数据的reindex了, 如果syncTypeEnum=META_CHANGE,则直接返回
                rebuildTopicRoute2Cdc();
                return;
            }

            // 获取需要进行同步的索引名
            needSyncIndexName = getNeedSyncIndexName(teamCode, dataStructNode);

            // 待同步模型最新生成的索引名
            log.info("begin to sync, teamCode:{}, modelAlias:{}, needSyncIndexName:{}",
                teamCode, modelAlias, needSyncIndexName);

            // 创建一条同步状态为syncing的SearchModelSyncTask记录
            // TODO 针对原来为syncing状态的记录，断点续传功能，后续单独设计，此处暂不考虑
            task = createSyncingSyncTask(needSyncIndexName);

            // 索引创建，索引提前创建
            rebuildIndexBeforeSync(needSyncIndexName, dataStructNode);

            // 更新监听canal监听关系
            rebuildTopicRoute2Cdc();

            // 按模型维度拆分任务，分线程执行
            physicalTableBulkTasks = generatePhysicalTableBulkTasks(task);

            Map<String, Future<SyncTaskErrorMessage>> futures = Maps.newHashMap();
            physicalTableBulkTasks.forEach(tableTask ->
                futures.put(tableTask.generateKey(), taskExecutor.executeDataQuery(tableTask)));

            // 等待线程执行完成
            waitThreadComplete(futures);

            // 同步结束处理
            doAfterSuccessSynced(task, dataStructNode);
        } catch (Exception e) {
            log.error("model sync task error..., cost={}ms, {}, teamCode:{}, modelAlias:{},indexName:{}, error:{}",
                (System.currentTimeMillis() - start), DrivingRecorderUtil.allRecordersString(),
                teamCode, modelAlias, needSyncIndexName, e.getMessage(), e);
            String errMsg = createErrMsg(e);
            syncTaskErrorMessage.setOtherErrorMessage(errMsg);

            // 同步失败处理
            doAfterErrorSynced(task, dataStructNode);
        } finally {
            // errors如果存在则更新
            String error = "";
            if (Boolean.FALSE.equals(syncTaskErrorMessage.isEmptyRecordErrorMessage())) {
                error = syncTaskErrorMessage.generateErrorString();
                if (null != task) {
                    updateSearchModelSyncTaskErrors(task.getId(), error);
                }
            }

            // 缓存清理
            cleanSearchModelCache(dataStructNode);

            // 同步任务移除
            searchModelTaskManager.removeSyncTask(this);

            // 全量同步锁释放
            releaseSyncTaskLock(threadName);

            log.info("model sync task execute complete.....error:{}, cost={}ms, {},teamCode:{}," +
                    " modelAlias:{}, indexName:{}",
                error, (System.currentTimeMillis() - start), DrivingRecorderUtil.allRecordersString(),
                teamCode, modelAlias, needSyncIndexName);

            MDC.remove("requestId");
        }
    }

    protected abstract List<AbstractTableReindexTask> generatePhysicalTableBulkTasks(SearchModelSyncTask task);


    /**
     * 等待所有线程执行完成
     *
     * @param futures
     * @throws Exception
     */
    private void waitThreadComplete(Map<String, Future<SyncTaskErrorMessage>> futures) throws Exception {
        // 等待所有规则字段序列更新操作完成
        for (Map.Entry<String, Future<SyncTaskErrorMessage>> futureEntry : futures.entrySet()) {
            SyncTaskErrorMessage subSyncTaskErrorMessage = futureEntry.getValue().get(MAX_THREAD_WAIT_HOUR, TimeUnit.HOURS);
            if (!StringUtils.isEmpty(subSyncTaskErrorMessage.getOtherErrorMessage())) {
                // 如果子线程中有一个返回的为false，说明同步过程中存在异常，本次全量同步失败，并且停止其它子线程的执行
                physicalTableBulkTasks.forEach(task -> task.kill("SYNC_ERROR_OUTSIDE_TASK_KILL"));
                throw new RuntimeException(futureEntry.getKey() + " 数据同步执行失败。。。err=" + subSyncTaskErrorMessage.generateErrorString());
            }

            if (Boolean.FALSE.equals(subSyncTaskErrorMessage.isEmptyRecordErrorMessage())) {
                syncTaskErrorMessage.appendOtherErrorMessage(subSyncTaskErrorMessage.generateErrorString());
            }
        }
    }

    private String createErrMsg(Exception e) {
        String errMsg = e.getMessage();
        if (e instanceof NullPointerException) {
            errMsg = "NullPointerException:" + e.getStackTrace()[0].toString();
        }
        return errMsg;
    }

    /**
     * 全量同步结束后，做缓存清理
     *
     * @param dataStructNode
     */
    protected abstract void cleanSearchModelCache(DataStructNode dataStructNode);

    /**
     * 同步任务锁释放
     *
     * @param threadName
     */
    protected abstract void releaseSyncTaskLock(String threadName);

    /**
     * 创建同步记录
     *
     * @param needSyncIndexName
     * @return
     */
    protected abstract SearchModelSyncTask createSyncingSyncTask(String needSyncIndexName);

    protected abstract void rebuildTopicRoute2Cdc();

    protected abstract ModelChangeRecognition recognizeModelChange();

    /**
     * 针对元数据的变更，做索引最细粒度更新
     * 新增字段、删除字段，不会重建索引
     * 存在字段修改时，将重建索引
     *
     * @param modelChangeRecognition
     */
    protected abstract void doMinimumUpdateWhenSearchModelDefinitionUpdated(ModelChangeRecognition modelChangeRecognition);

    /**
     * 全量同步开始前，进行索引重建
     *
     * @param needSyncIndexName
     * @param dataStructNode
     */
    protected abstract void rebuildIndexBeforeSync(String needSyncIndexName, DataStructNode dataStructNode);

    /**
     * 获取需要进行同步的索引名
     *
     * @param teamCode
     * @param dataStructNode
     * @return
     */
    protected abstract String getNeedSyncIndexName(String teamCode, DataStructNode dataStructNode);

    /**
     * 更新search_model_sync_task失败信息
     *
     * @param syncTaskId
     * @param error
     */
    private void updateSearchModelSyncTaskErrors(Long syncTaskId, String error) {
        assistService.updateSearchModelSyncTaskErrors(syncTaskId, error);
    }

    /**
     * 获取当前任务标识key
     *
     * @return
     */
    public abstract String getKey();

    /**
     * 获取任务详情
     *
     * @return
     */
    public String getTaskDetail() {
        return "SyncTask{" +
            "teamCode='" + teamCode + '\'' +
            "modelAlias='" + dataStructNode.getAlias() + '\'' +
            '}';
    }

    @Override
    public void killTask(String killType) {
        log.warn("begin to kill task, modelKey:{}", dataStructNode.getKey());
        if (!CollectionUtils.isEmpty(physicalTableBulkTasks)) {
            physicalTableBulkTasks.forEach(task -> {
                task.kill(killType);
            });
        }
    }

    @Override
    public void suspendTask(String suspendType) {
        log.warn("begin to suspend task, modelKey:{}", dataStructNode.getKey());
        if (!CollectionUtils.isEmpty(physicalTableBulkTasks)) {
            physicalTableBulkTasks.forEach(task -> {
                task.suspend(suspendType);
            });
        }
    }

    /**
     * 同步成功后置处理器
     *
     * @param task
     * @param dataStructNode
     */
    protected abstract void doAfterSuccessSynced(SearchModelSyncTask task, DataStructNode dataStructNode);

    /**
     * 同步失败后置处理器
     *
     * @param task
     * @param dataStructNode
     */
    protected abstract void doAfterErrorSynced(SearchModelSyncTask task, DataStructNode dataStructNode);
}

