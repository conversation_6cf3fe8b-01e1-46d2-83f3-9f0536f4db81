package io.terminus.trantor2.search.management.scheduler;

import cn.hutool.core.util.RandomUtil;
import com.google.common.collect.Lists;
import io.terminus.trantor2.search.adaptor.EsHttpClientAdapter;
import io.terminus.trantor2.search.domain.SearchModelSyncTask;
import io.terminus.trantor2.search.domain.SyncStatusEnum;
import io.terminus.trantor2.search.management.cache.SearchModelSyncTaskCache;
import io.terminus.trantor2.search.management.util.DrivingRecorderUtil;
import io.terminus.trantor2.search.repository.SearchModelSyncTaskRepository;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * dump系统内置定时任务<br/>
 * 1、清理deleted状态的search_model_sync_task表、es索引
 * 2、CanalMqConsumerModelCache reload
 */
@Service
@Slf4j
public class DumpSystemInnerSchedule {

    @Autowired
    private SearchModelSyncTaskRepository repository;

    @Autowired
    private SearchModelSyncTaskCache searchModelCache;

    @Autowired
    private EsHttpClientAdapter esHttpClientAdapter;

    @Setter
    private boolean isShutdownHook = false;

    @Scheduled(cron = "${trantor2.search.dump-inner-schedule}")
    public void dumpSystemInnerSchedule() {
        if (isShutdownHook) {
            return;
        }
        // 清理deleted状态的search_model_sync_task表、es索引
        deletedSyncTaskClear();

        // CanalMqConsumerModelCache reload
        canalMqConsumerModelCacheReload();
    }

    /**
     * 1、重新reload一把canalMqConsumerModelCache
     * 2、遍历CanalMqConsumerModelCache，重新计算里面对应模型的database-tablename缓存，并将无效的缓存做清理
     */
    private void canalMqConsumerModelCacheReload() {
        searchModelCache.initCanalMqConsumerModelCache();
        searchModelCache.deleteUnUsefulCanalMqConsumerModelCache();
    }

    private void deletedSyncTaskClear() {
        long start = System.currentTimeMillis();
        DrivingRecorderUtil.resetAllRecorders();
        MDC.put("requestId", RandomUtil.randomString(10));

        try {
            // 1、search_model_sync_task加载deleted状态
            List<SearchModelSyncTask> syncTasks = repository.findBySyncStatusEnumIn(Lists.newArrayList(SyncStatusEnum.DELETED));
            if (CollectionUtils.isEmpty(syncTasks)) {
                return;
            }
            // 2、search_model_sync_task按照模型分组
            Map<String, List<SearchModelSyncTask>> taskMap = syncTasks.stream()
                .collect(Collectors.groupingBy(task -> task.getTeamCode() + "_" + task.getModelAlias()));

            List<SearchModelSyncTask> needDeleteTaskList = new ArrayList<>();
            // 3、每个模型保留最近的5条记录
            taskMap.forEach((key, dataList) -> {
                List<SearchModelSyncTask> tasks = dataList.stream().sorted(Comparator.comparingLong(SearchModelSyncTask::getId)).collect(Collectors.toList());
                if (tasks.size() <= 5) {
                    return;
                }
                needDeleteTaskList.addAll(tasks.subList(0, tasks.size() - 5));
            });


            // deleted状态的es索引清理
            if (!CollectionUtils.isEmpty(needDeleteTaskList)) {
                needDeleteTaskList.forEach(model -> {
                    try {
                        esHttpClientAdapter.deleteIndex(model.getIndexName());
                    } catch (Exception e) {
                        log.warn("DumpSystemInnerSchedule delete index error, error:{},index:{}",
                            e.getMessage(), model.getIndexName());
                    }

                    repository.deleteById(model.getId());
                });
            }

            log.info("DumpSystemInnerSchedule, cost={}ms, {}",
                (System.currentTimeMillis() - start), DrivingRecorderUtil.allRecordersString());
        } catch (Exception e) {
            log.error("DumpSystemInnerSchedule, cost={}ms, {}, err={}",
                (System.currentTimeMillis() - start), DrivingRecorderUtil.allRecordersString(), e.getMessage(), e);
        } finally {
            DrivingRecorderUtil.resetAllRecorders();
            MDC.remove("requestId");
        }
    }

}
