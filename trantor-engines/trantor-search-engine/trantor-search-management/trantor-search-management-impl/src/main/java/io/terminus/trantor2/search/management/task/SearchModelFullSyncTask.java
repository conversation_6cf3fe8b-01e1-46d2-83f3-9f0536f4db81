package io.terminus.trantor2.search.management.task;

import com.alibaba.fastjson.JSONObject;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.properties.SearchDumpProperties;
import io.terminus.trantor2.search.adaptor.EsHttpClientAdapter;
import io.terminus.trantor2.search.domain.SearchModelSyncTask;
import io.terminus.trantor2.search.es.EsBuilder;
import io.terminus.trantor2.search.es.index.Setting;
import io.terminus.trantor2.search.management.cache.SearchModelTaskManager;
import io.terminus.trantor2.search.management.impl.api.SearchModelAssistService;
import io.terminus.trantor2.search.management.task.reindex.AbstractTableReindexTask;
import io.terminus.trantor2.search.management.task.reindex.FullTableReindexTask;
import io.terminus.trantor2.search.management.util.SearchModelUtil;
import io.terminus.trantor2.search.management.util.SyncTaskErrorMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 全量同步任务
 */
@Slf4j
@Data
@AllArgsConstructor
public class SearchModelFullSyncTask extends SearchModelAbstractSyncTask {
    @Builder(toBuilder = true)
    public SearchModelFullSyncTask(String teamCode, DataStructNode dataStructNode,
                                   EsHttpClientAdapter esHttpClientAdapter, String indexType,
                                   SearchModelTaskManager searchModelTaskManager, SearchDumpProperties searchDumpProperties,
                                   SearchModelAssistService assistService, SyncTaskErrorMessage syncTaskErrorMessage,
                                   TaskSyncTypeEnum syncTypeEnum, String threadName, String syncReason, String executor,
                                   SearchModelSyncTaskExecutor taskExecutor) {
        super(teamCode, dataStructNode, esHttpClientAdapter, indexType, searchModelTaskManager,
            searchDumpProperties, assistService, syncTaskErrorMessage, syncTypeEnum, threadName, syncReason, executor,
            taskExecutor, null);
    }


    @Override
    protected List<AbstractTableReindexTask> generatePhysicalTableBulkTasks(SearchModelSyncTask task) {
        List<AbstractTableReindexTask> tasks = new ArrayList<>();
        Set<String> tables = assistService.getPhysicalTables(dataStructNode);
        tables.forEach(tableName -> {
            FullTableReindexTask tableReindexTask = new FullTableReindexTask();
            tableReindexTask.setTableName(tableName);
            tableReindexTask.setTask(task);
            tableReindexTask.setStep(searchDumpProperties.getFullTaskWorker().getDataPageQueryStep());
            tableReindexTask.setTeamCode(teamCode);
            tableReindexTask.setDataStructNode(dataStructNode);
            tableReindexTask.setEsHttpClientAdapter(esHttpClientAdapter);
            tableReindexTask.setAssistService(assistService);
            tableReindexTask.setSyncTaskErrorMessage(new SyncTaskErrorMessage());
            tableReindexTask.setIndexType(indexType);
            tasks.add(tableReindexTask);
        });
        return tasks;
    }

    @Override
    protected void cleanSearchModelCache(DataStructNode dataStructNode) {
        assistService.cleanSearchModelCache(dataStructNode);
    }

    @Override
    protected void releaseSyncTaskLock(String threadName) {
        assistService.releaseSyncTaskLock(teamCode, dataStructNode.getAlias(), threadName);
    }

    @Override
    protected SearchModelSyncTask createSyncingSyncTask(String needSyncIndexName) {
        return assistService.createSyncingSyncTask(dataStructNode.getAlias(),
            needSyncIndexName, dataStructNode.getTeamCode(), syncReason, executor);
    }

    @Override
    protected void rebuildTopicRoute2Cdc() {
        assistService.rebuildTopicRoute2Cdc(teamCode, dataStructNode);
    }

    @Override
    protected ModelChangeRecognition recognizeModelChange() {
        ModelChangeRecognition modelChangeRecognition = new ModelChangeRecognition();
        try {
            String oldIndexName = assistService.getLatestRunningIndex(teamCode, dataStructNode.getAlias());
            modelChangeRecognition.setOldIndexName(oldIndexName);

            if (null == oldIndexName) {
                // 说明历史不存在运行状态的索引
                modelChangeRecognition.setHasFieldUpdate(true);
                return modelChangeRecognition;
            }

            // 获取运行态索引的mapping信息
            JSONObject fieldsInRunningObj = esHttpClientAdapter.getIndexMappings(oldIndexName, indexType);
            if (null == fieldsInRunningObj) {
                // 说明历史不存在运行状态的索引
                modelChangeRecognition.setHasFieldUpdate(true);
                return modelChangeRecognition;
            }

            // 当前最新的元信息结构
            JSONObject fieldsNewObj = SearchModelUtil.generateFieldsProperty(dataStructNode.getChildren());

            JSONObject newAddObj = new JSONObject();

            fieldsNewObj.keySet().forEach(fieldName -> {
                String ro = fieldsInRunningObj.getString(fieldName);
                String no = fieldsNewObj.getString(fieldName);
                if (null == ro) {
                    // 这里不会get不到
                    DataStructFieldNode fieldNode = dataStructNode.getChildren().stream()
                        .filter(field -> Objects.equals(field.getAlias(), fieldName))
                        .findAny().get();
                    if (null != fieldNode.getProps().getDefaultValue()) {
                        // 说明字段存在默认值，这个时候不能认为单纯的字段新增，先当作全量同步处理
                        modelChangeRecognition.setHasFieldUpdate(true);
                    } else {
                        // 当作新增字段处理，当只有字段新增时，不会触发全量同步
                        newAddObj.put(fieldName, fieldsNewObj.getJSONObject(fieldName));
                    }
                    return;
                }
                if (!ro.equalsIgnoreCase(no)) {
                    // 说明存在字段变更
                    modelChangeRecognition.setHasFieldUpdate(true);
                }
            });
            modelChangeRecognition.setCreatedFields(newAddObj);


            // 获取运行态索引的setting信息
            JSONObject settingsInRunningObj = esHttpClientAdapter.getIndexSettings(oldIndexName, indexType);
            if (null == settingsInRunningObj) {
                // 说明历史不存在运行状态的索引
                modelChangeRecognition.setHasSettingChange(true);
                return modelChangeRecognition;
            }

            Setting setting = SearchModelUtil.generateEsSettings(dataStructNode.getProps());
            if (!Objects.equals(setting.getEsMaxAllowRowSize(), settingsInRunningObj.getInteger("max_result_window"))
                || !Objects.equals(setting.getNumber_of_shards(), settingsInRunningObj.getString("number_of_shards"))
                || !Objects.equals(setting.getNumber_of_replicas(), settingsInRunningObj.getString("number_of_replicas"))) {
                modelChangeRecognition.setHasSettingChange(true);
            }
            if (null != setting.getUserDefineSetting()) {
                setting.getUserDefineSetting().keySet().forEach(settingName -> {
                    String ro = settingsInRunningObj.getString(settingName);
                    String no = setting.getUserDefineSetting().getString(settingName);
                    if (!Objects.equals(ro, no)) {
                        modelChangeRecognition.setHasSettingChange(true);
                    }
                });
            }
        } catch (Exception e) {
            log.warn("recognizeModelChange error... error:{},teamCode:{},modelAlias:{}",
                e.getMessage(), teamCode, dataStructNode.getAlias(), e);
        }

        return modelChangeRecognition;
    }

    /**
     * 对历史索引中缺少的字段，动态添加mapping信息
     *
     * @param modelChangeRecognition
     */
    @Override
    protected void doMinimumUpdateWhenSearchModelDefinitionUpdated(ModelChangeRecognition modelChangeRecognition) {
        JSONObject newAddFieldObj = modelChangeRecognition.getCreatedFields();
        if (null == newAddFieldObj || newAddFieldObj.isEmpty()) {
            return;
        }
        try {
            esHttpClientAdapter.addMappings(modelChangeRecognition.getOldIndexName(), indexType, newAddFieldObj);
        } catch (Exception e) {
            log.warn("doMinimumUpdateWhenSearchModelDefinitionUpdated error... error:{},indexName:{}",
                e.getMessage(), modelChangeRecognition.getOldIndexName(), e);
        }

    }

    @Override
    protected void rebuildIndexBeforeSync(String needSyncIndexName, DataStructNode dataStructNode) {
        // 索引创建
        assistService.createIndex(needSyncIndexName, dataStructNode);

        String alias = dataStructNode.getProps().getSearchModelConfigMeta().getAlias();
        if (null == alias) {
            alias = dataStructNode.getProps().getSearchModelConfigMeta()
                .generateAlias(dataStructNode.getTeamCode(), dataStructNode.getAlias());
        }
        // 如果当前别名没有指向任何索引，则优先变更别名-索引指向关系
        assistService.checkAndRedirectAlias(needSyncIndexName,
            alias, false);
    }

    /**
     * 获取需要同步的索引名
     *
     * @param teamCode
     * @param dataStructNode
     * @return
     */
    @Override
    protected String getNeedSyncIndexName(String teamCode, DataStructNode dataStructNode) {
        return EsBuilder.generateIndex(teamCode, dataStructNode.getAlias());
    }

    public String getKey() {
        return getTeamCode() + "_" + getDataStructNode().getAlias() + "_" + SearchModelTaskManager.FULL_SYNC_FLAG;
    }

    @Override
    protected void doAfterSuccessSynced(SearchModelSyncTask task, DataStructNode dataStructNode) {
        try {
            assistService.changeSyncTaskUseful(task, dataStructNode);
        } catch (Exception e) {
            log.error("doAfterSuccessSyne is error，错误信息: {}，taskId: {}, indexName:{}",
                e.getMessage(), task.getId(), task.getIndexName(), e);
            throw e;
        }
    }

    @Override
    protected void doAfterErrorSynced(SearchModelSyncTask task, DataStructNode dataStructNode) {
        assistService.rollbackSyncTask(task, dataStructNode);
    }
}
