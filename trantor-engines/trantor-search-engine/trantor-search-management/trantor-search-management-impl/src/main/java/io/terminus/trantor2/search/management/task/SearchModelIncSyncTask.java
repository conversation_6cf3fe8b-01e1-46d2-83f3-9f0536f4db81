package io.terminus.trantor2.search.management.task;

import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.properties.SearchDumpProperties;
import io.terminus.trantor2.search.adaptor.EsHttpClientAdapter;
import io.terminus.trantor2.search.domain.SearchModelSyncTask;
import io.terminus.trantor2.search.management.cache.SearchModelTaskManager;
import io.terminus.trantor2.search.management.impl.api.SearchModelAssistService;
import io.terminus.trantor2.search.management.task.reindex.AbstractTableReindexTask;
import io.terminus.trantor2.search.management.task.reindex.IncTableReindexTask;
import io.terminus.trantor2.search.management.util.SyncTaskErrorMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 增量数据修复
 */
@Slf4j
@Data
@AllArgsConstructor
public class SearchModelIncSyncTask extends SearchModelAbstractSyncTask {
    /**
     * 增量修复的数据
     */
    private List<Object> ids;

    @Builder(toBuilder = true)
    public SearchModelIncSyncTask(String teamCode, DataStructNode dataStructNode,
                                  EsHttpClientAdapter esHttpClientAdapter, String indexType,
                                  SearchModelTaskManager searchModelTaskManager, SearchDumpProperties searchDumpProperties,
                                  SearchModelAssistService assistService, SyncTaskErrorMessage syncTaskErrorMessage,
                                  TaskSyncTypeEnum syncTypeEnum, String threadName, String syncReason, String executor,
                                  List<Object> ids, SearchModelSyncTaskExecutor taskExecutor) {
        super(teamCode, dataStructNode, esHttpClientAdapter, indexType, searchModelTaskManager,
            searchDumpProperties, assistService, syncTaskErrorMessage, syncTypeEnum, threadName, syncReason, executor,
            taskExecutor, null);
        this.ids = ids;
    }


    @Override
    protected List<AbstractTableReindexTask> generatePhysicalTableBulkTasks(SearchModelSyncTask task) {
        List<AbstractTableReindexTask> tasks = new ArrayList<>();
        Set<String> tables = assistService.getPhysicalTables(dataStructNode);
        tables.forEach(tableName -> {
            IncTableReindexTask tableReindexTask = new IncTableReindexTask();
            tableReindexTask.setTableName(tableName);
            tableReindexTask.setTask(task);
            tableReindexTask.setStep(searchDumpProperties.getFullTaskWorker().getDataPageQueryStep());
            tableReindexTask.setTeamCode(teamCode);
            tableReindexTask.setDataStructNode(dataStructNode);
            tableReindexTask.setEsHttpClientAdapter(esHttpClientAdapter);
            tableReindexTask.setAssistService(assistService);
            tableReindexTask.setSyncTaskErrorMessage(new SyncTaskErrorMessage());
            tableReindexTask.setIndexType(indexType);
            tableReindexTask.setIds(ids);
            tasks.add(tableReindexTask);
        });
        return tasks;
    }

    @Override
    protected void cleanSearchModelCache(DataStructNode dataStructNode) {
    }

    @Override
    protected void releaseSyncTaskLock(String threadName) {
    }

    @Override
    protected SearchModelSyncTask createSyncingSyncTask(String needSyncIndexName) {
        return assistService.getLatestRunningSearchModelSyncTask(teamCode, dataStructNode.getAlias());
    }

    @Override
    protected void rebuildTopicRoute2Cdc() {
    }

    @Override
    protected ModelChangeRecognition recognizeModelChange() {
        return new ModelChangeRecognition();
    }

    /**
     * 对历史索引中缺少的字段，动态添加mapping信息
     *
     * @param modelChangeRecognition
     */
    @Override
    protected void doMinimumUpdateWhenSearchModelDefinitionUpdated(ModelChangeRecognition modelChangeRecognition) {
    }

    @Override
    protected void rebuildIndexBeforeSync(String needSyncIndexName, DataStructNode dataStructNode) {
    }

    /**
     * 获取需要同步的索引名
     *
     * @param teamCode
     * @param dataStructNode
     * @return
     */
    @Override
    protected String getNeedSyncIndexName(String teamCode, DataStructNode dataStructNode) {
        return assistService.getLatestRunningIndex(teamCode, dataStructNode.getAlias());
    }

    public String getKey() {
        return getTeamCode() + "_" + getDataStructNode().getAlias() + "_" + SearchModelTaskManager.INC_SYNC_FLAG;
    }

    @Override
    protected void doAfterSuccessSynced(SearchModelSyncTask task, DataStructNode dataStructNode) {
    }

    @Override
    protected void doAfterErrorSynced(SearchModelSyncTask task, DataStructNode dataStructNode) {
    }
}
