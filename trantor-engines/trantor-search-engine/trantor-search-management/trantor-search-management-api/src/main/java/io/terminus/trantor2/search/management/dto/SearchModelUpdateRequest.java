package io.terminus.trantor2.search.management.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

/**
 * 搜索模型修改请求
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Schema(description = "搜索模型修改请求")
@ToString(callSuper = true)
public class SearchModelUpdateRequest extends BaseSyncRequest {

    /**
     * 搜索模型元信息
     */
    @NonNull
    private DataStructNode dataStructNode;

    public String getReason() {
        if (StringUtils.isEmpty(reason)) {
            return "SearchModelUpdate";
        }
        return reason;
    }
}
