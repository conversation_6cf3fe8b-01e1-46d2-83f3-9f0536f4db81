package io.terminus.trantor2.notice.service.impl;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import io.terminus.notice.sdk.req.scene.NoticeScenePagingReq;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.service.MenuRuntimeQueryService;
import io.terminus.trantor2.notice.meta.NoticeSceneMeta;
import io.terminus.trantor2.notice.repository.NoticeSceneRepo;
import io.terminus.trantor2.notice.service.NoticeSceneService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class NoticeSceneServiceImpl implements NoticeSceneService {

    private final MenuRuntimeQueryService menuQueryService;
    private final NoticeSceneRepo noticeSceneRepo;

    @Override
    public List<NoticeSceneMeta> listForPortal(String portalCode, NoticeScenePagingReq req) {
        List<MenuMeta> menuMetas = menuQueryService.getMenuTree(portalCode);
        Set<String> moduleKeys = getMenuRelateModules(menuMetas);
//        Set<Long> moduleIds = moduleQueryService.findAllByKeysIn(moduleKeys).stream()
//            .map(ModuleMeta::getTeamId)
//            .collect(Collectors.toSet());

        Cond cond = Field.type().equal(MetaType.NoticeScene.name());
        if (req != null && !Strings.isNullOrEmpty(req.getSceneName())) {
            cond = cond.and(Field.name().like("%" + req.getSceneName() + "%"));
        }
        List<NoticeSceneMeta> noticeScenes = noticeSceneRepo.findAll(cond, ResourceContext.ctxFromThreadLocal());
        return noticeScenes.stream().filter(noticeSceneMeta -> moduleKeys.contains(KeyUtil.moduleKey(noticeSceneMeta.getKey()))).collect(Collectors.toList());
    }

    @Override
    public NoticeSceneMeta queryNoticeScene(String key) {
        return noticeSceneRepo.findOneByKey(key, ResourceContext.ctxFromThreadLocal()).orElse(null);
    }

    @Override
    public NoticeSceneMeta queryNoticeSceneById(Long id) {
        return noticeSceneRepo.findOneById(id).orElse(null);
    }

    private Set<String> getMenuRelateModules(List<MenuMeta> menuMetas) {
        return menuMetas.stream()
            .map(this::getMenuRelateModules)
            .flatMap(Collection::stream)
            .collect(Collectors.toSet());
    }

    private List<String> getMenuRelateModules(MenuMeta menuMeta) {
        List<String> result = Lists.newArrayList();
        if (menuMeta != null && CollectionUtils.isNotEmpty(menuMeta.getChildren())) {
            menuMeta.getChildren().forEach(child -> {
                result.addAll(getMenuRelateModules(child));
            });
            return result;
        }
        if (menuMeta != null && menuMeta.getRouteConfig() != null && menuMeta.getRouteConfig().get("sceneKey") != null) {
            String sceneKey = menuMeta.getRouteConfig().get("sceneKey").toString();
            result.add(KeyUtil.moduleKey(sceneKey));
        }
        return result;
    }

}
