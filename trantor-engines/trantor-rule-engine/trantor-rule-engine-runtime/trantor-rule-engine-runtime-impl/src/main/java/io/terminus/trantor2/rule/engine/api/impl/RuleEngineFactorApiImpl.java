package io.terminus.trantor2.rule.engine.api.impl;

import io.terminus.common.api.util.MD5Utils;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.rule.engine.api.RuleEngineFactorApi;
import io.terminus.trantor2.rule.engine.api.model.dto.RuleEngineFactorDTO;
import io.terminus.trantor2.rule.engine.api.model.dto.factor.FactorCombDTO;
import io.terminus.trantor2.rule.engine.api.model.dto.factor.FactorDTO;
import io.terminus.trantor2.rule.engine.client.RuleEngineExecutor;
import io.terminus.trantor2.rule.engine.service.RuleEngineFactorService;
import io.terminus.trantor2.rule.engine.spi.BizRuleEngineFactorApi;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 表达式因子
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
@RestController
@RequiredArgsConstructor
public class RuleEngineFactorApiImpl implements RuleEngineFactorApi {

    private final RuleEngineFactorService ruleEngineFactorService;
    private final RuleEngineExecutor ruleEngineExecutor;
    private final BizRuleEngineFactorApi bizRuleEngineFactorApi;

    @Override
    public Response<FactorCombDTO> queryFactorBySceneKey(RuleEngineFactorDTO request) {
        // 系统变量
        FactorCombDTO factorCombDTO = ruleEngineFactorService.queryFactorBySceneKey(request.getSceneKeys());
        // 业务变量
        Response<List<FactorDTO>> bizFactorRes = bizRuleEngineFactorApi.queryAllFactor(request.getSceneKeys());
        if (bizFactorRes != null && CollectionUtils.isNotEmpty(bizFactorRes.getData())) {
            factorCombDTO.setBizFactor(bizFactorRes.getData());
        }
        return Response.ok(factorCombDTO);
    }

    @Override
    public Response<List<FactorDTO>> queryAllSysFactor() {
        // 系统变量
        List<FactorDTO> sysFactors = ruleEngineFactorService.queryAllFactor();
        if (CollectionUtils.isNotEmpty(sysFactors)) {
            return Response.ok(sysFactors);
        }
        return Response.ok(Collections.emptyList());
    }

    @Override
    public Response<Object> findSystemVariableVale(String key) {
        List<FactorDTO> sysFactors = ruleEngineFactorService.queryAllFactor();
        if (CollectionUtils.isNotEmpty(sysFactors)) {
            for (FactorDTO factor : sysFactors) {
                if (factor.getKey().equals(key)) {
                    return execute(factor.getFactorKey());
                }
                // 兼容一下
                if (convertFactorKey(factor.getFactorKey()).equals(key)) {
                    return execute(factor.getFactorKey());
                }
            }
        }
        return Response.ok(null);
    }

    @Deprecated
    private String convertFactorKey(String factorKey) {
        return "SYS_VAR_" + MD5Utils.md5Hex(factorKey, "utf-8");
    }

    @Override
    public Response<List<FactorDTO>> queryAllBusinessFactor() {
        List<FactorDTO> result = new ArrayList<>();
        Response<List<FactorDTO>> bizFactorRes = bizRuleEngineFactorApi.queryAllFactor();
        if (bizFactorRes != null && CollectionUtils.isNotEmpty(bizFactorRes.getData())) {
            result.addAll(bizFactorRes.getData());
        }
        return Response.ok(result);
    }

    @Override
    public Response<List<FactorDTO>> queryAllFactor() {
        List<FactorDTO> result = new ArrayList<>();
        // 系统变量
        List<FactorDTO> sysFactors = ruleEngineFactorService.queryAllFactor();
        if (CollectionUtils.isNotEmpty(sysFactors)) {
            result.addAll(sysFactors);
        }
        // 业务变量
        Response<List<FactorDTO>> bizFactorRes = bizRuleEngineFactorApi.queryAllFactor();
        if (bizFactorRes != null && CollectionUtils.isNotEmpty(bizFactorRes.getData())) {
            result.addAll(bizFactorRes.getData());
        }
        return Response.ok(result);
    }

    @Override
    public Response<Object> execute(String express) {
        return execute(express, new HashMap<>(0));
    }

    @Override
    public Response<Object> execute(String express, Map<String, Object> context) {
        if (isSysFactor(express)) {
            // 执行系统变量
            return Response.ok(ruleEngineExecutor.execute(express, context));
        } else {
            // 执行业务变量
            return bizRuleEngineFactorApi.execute(express, context);
        }
    }

    private boolean isSysFactor(String express) {
        List<FactorDTO> sysFactors = ruleEngineFactorService.queryAllFactor();
        if (CollectionUtils.isEmpty(sysFactors)) {
            return false;
        }
        String key;
        if (!express.contains("(")) {
            key = express;
        } else {
            key = express.substring(0, express.indexOf("("));
        }
        for (FactorDTO factorDTO : sysFactors) {
            if (key.equals(factorDTO.getKey())) {
                return true;
            }
        }
        return false;
    }
}
