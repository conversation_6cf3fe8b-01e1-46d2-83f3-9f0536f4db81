package io.terminus.trantor2.rule.engine.function;

import io.terminus.trantor2.rule.engine.api.DataLoadApi;
import io.terminus.trantor2.rule.engine.api.model.dto.DataLoadDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 数据加载函数
 * <AUTHOR>
 */
@Slf4j
@Component("DATA_LOAD")
public class DataLoadFunction {

    @Resource
    private DataLoadApi dataLoadApi;

    public Object dataLoad(String modelKey, String fieldKey, Object conditionValue) {
        log.info("[DataLoadFunction] modelKey:{}, fieldKey:{}, conditionValue:{}", modelKey, fieldKey, conditionValue);
        return dataLoadApi.dataLoad(new DataLoadDTO(modelKey, fieldKey, conditionValue));
    }

}
