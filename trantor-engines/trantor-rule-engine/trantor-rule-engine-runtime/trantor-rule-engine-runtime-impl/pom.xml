<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>trantor-rule-engine-runtime</artifactId>
        <groupId>io.terminus.trantor2</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>trantor-rule-engine-runtime-impl</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencies>

        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-rule-engine-runtime-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
        </dependency>

        <!-- mapstruct -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <!-- model engine -->
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-model-runtime-impl</artifactId>
        </dependency>

        <!-- trantor organization -->
        <dependency>
            <groupId>io.terminus.trantor</groupId>
            <artifactId>trantor-org-api</artifactId>
            <version>${trantor-org.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.terminus.erp</groupId>
                    <artifactId>erp-md-spi</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- terminus config -->
        <dependency>
            <groupId>io.terminus.config</groupId>
            <artifactId>terminus-spring-boot-starter-config</artifactId>
            <version>${terminus.config.version}</version>
        </dependency>

        <!-- rule engine api -->
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-rule-engine-api-common</artifactId>
        </dependency>

        <!-- QLExpress -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>QLExpress</artifactId>
            <version>${qlexpress.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
    </dependencies>

</project>
