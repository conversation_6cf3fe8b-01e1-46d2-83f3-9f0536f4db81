package io.terminus.trantor2.rule.engine.api;

import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.rule.engine.api.model.dto.RuleEngineFactorDTO;
import io.terminus.trantor2.rule.engine.api.model.dto.factor.FactorCombDTO;
import io.terminus.trantor2.rule.engine.api.model.dto.factor.FactorDTO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 表达式因子库 Facade
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
@RequestMapping("api/trantor/rule/engine/factor/")
public interface RuleEngineFactorApi {

    /**
     * 按场景key集合查询因子库和函数因子库
     *
     * @param request 场景key集合
     * @return 因子库和函数因子库
     */
    @RequestMapping(value = "queryFactorBySceneKey", method = RequestMethod.POST)
    Response<FactorCombDTO> queryFactorBySceneKey(@RequestBody RuleEngineFactorDTO request);

    /**
     * 查询所有的系统变量
     *
     * @return 系统变量
     */
    @RequestMapping(value = "queryAllSysFactor", method = RequestMethod.POST)
    Response<List<FactorDTO>> queryAllSysFactor();


    /**
     * 查询系统变量的值
     */
    @RequestMapping(value = "findSystemVariableVale", method = RequestMethod.POST)
    Response<Object> findSystemVariableVale(String key);


    /**
     * 查询所有的系统变量
     *
     * @return 系统变量
     */
    @RequestMapping(value = "queryAllBusinessFactor", method = RequestMethod.POST)
    Response<List<FactorDTO>> queryAllBusinessFactor();

    /**
     * 查询全部的因子库和函数因子库
     *
     * @return 全部的因子库和函数因子库
     */
    @RequestMapping(value = "queryAllFactor", method = RequestMethod.POST)
    Response<List<FactorDTO>> queryAllFactor();

    /**
     * 规则引擎执行
     *
     * @param express 表达式
     * @return 执行结果
     */
    @RequestMapping(value = "exec", method = RequestMethod.POST)
    Response<Object> execute(String express);

    /**
     * 规则引擎执行
     *
     * @param express 表达式
     * @return 执行结果
     */
    @RequestMapping(value = "execute", method = RequestMethod.POST)
    Response<Object> execute(String express, Map<String, Object> context);

}
