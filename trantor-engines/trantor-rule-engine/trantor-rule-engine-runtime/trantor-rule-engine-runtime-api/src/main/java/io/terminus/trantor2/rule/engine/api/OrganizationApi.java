package io.terminus.trantor2.rule.engine.api;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * 获取用户组织信息
 * <AUTHOR>
 */
@RequestMapping("api/trantor/rule/engine/")
public interface OrganizationApi {

    @RequestMapping(value =  "org/employee/query-user-org", method = RequestMethod.POST)
    List<Long> queryUserOrg();

    @RequestMapping(value =  "org/employee/query-user-org-and-all-child-org", method = RequestMethod.POST)
    List<Long> queryCurrentUserOrgAndAllChildOrg();

    @RequestMapping(value =  "org/employee/query-user-and-all-child-user", method = RequestMethod.POST)
    List<Long> queryCurrentUserAllChildUser();

    @RequestMapping(value =  "org/employee/query-user-all-child-org", method = RequestMethod.POST)
    List<Long> queryCurrentUserAllChildOrg();

    @RequestMapping(value =  "org/employee/query-user-for-iam", method = RequestMethod.POST)
    String queryCurrentEmployeeForIam();
}
