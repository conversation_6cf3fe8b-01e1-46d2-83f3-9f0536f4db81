package io.terminus.trantor2.rule.engine.util;


import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * http工具类
 * <AUTHOR>
 * @createTime 2023/5/12 5:23 下午
 */
@Slf4j
public class HttpUtil {

    public static String sendGet(String url) {
        String response = "";
        try {
            URL obj = new URL(url);
            HttpURLConnection con = (HttpURLConnection) obj.openConnection();
            String newUrl = con.getHeaderField("Location");
            con = (HttpURLConnection) new URL(newUrl).openConnection();
            con.setRequestMethod("GET");
            int responseCode = con.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(
                    con.getInputStream()));
                String inputLine;
                StringBuffer responseBuffer = new StringBuffer();
                while ((inputLine = in.readLine()) != null) {
                    responseBuffer.append(inputLine);
                }
                in.close();
                response = responseBuffer.toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("[HttpUtil]http调用，url:{}, result:{}", url, response);
        return response;
    }

    public static String sendPost(String url, String data) {
        String response = "";
        try {
            URL obj = new URL(url);
            HttpURLConnection con = (HttpURLConnection) obj.openConnection();
            String newUrl = con.getHeaderField("Location");
            con = (HttpURLConnection) new URL(newUrl).openConnection();
            con.setRequestMethod("POST");
            con.setRequestProperty("Content-Type", "application/json");
            con.setDoOutput(true);
            OutputStream os = con.getOutputStream();
            os.write(data.getBytes());
            os.flush();
            os.close();
            int responseCode = con.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(
                    con.getInputStream()));
                String inputLine;
                StringBuffer responseBuffer = new StringBuffer();
                while ((inputLine = in.readLine()) != null) {
                    responseBuffer.append(inputLine);
                }
                in.close();
                response = responseBuffer.toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("[HttpUtil]http调用，url:{}, param:{}, result:{}", url, data, response);
        return response;
    }

    public static void main(String[] args) {
        String result = sendPost("http://trantor2-back.app.terminus.io//api/trantor/rule/engine/factor/queryFactorBySceneKey", "{}");
        System.out.println(result);
    }
}
