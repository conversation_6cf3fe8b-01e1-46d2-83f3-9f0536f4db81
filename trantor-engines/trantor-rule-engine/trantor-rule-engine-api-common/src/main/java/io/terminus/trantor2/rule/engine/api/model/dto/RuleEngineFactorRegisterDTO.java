//package io.terminus.trantor2.rule.engine.api.model.dto;
//
//import io.terminus.trantor2.rule.engine.api.message.RuleEngineFunctionFactorMsg;
//import io.terminus.trantor2.rule.engine.api.model.dict.RuleEngineFunctionTypeDict;
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//import org.springframework.util.Assert;
//
//import java.io.Serializable;
//import java.util.Objects;
//
///**
// * 规则引擎因子注册DTO
// * <AUTHOR>
// * @createTime 2023/4/13 10:17 上午
// */
//@Data
//@NoArgsConstructor
//@AllArgsConstructor
//public class RuleEngineFactorRegisterDTO implements Serializable {
//
//    private static final long serialVersionUID = 8667557154264187312L;
//
//    /**
//     * 场景key
//     */
//    private String sceneKey;
//
//    /**
//     * 场景名称
//     */
//    private String sceneName;
//
//    /**
//     * 因子key
//     */
//    private String factorKey;
//
//    /**
//     * 因子名称
//     */
//    private String factorName;
//
//    /**
//     * 函数类型
//     * @see RuleEngineFunctionTypeDict
//     */
//    private String functionType;
//
//    /**
//     * 返回值类型
//     * @see io.terminus.trantor2.rule.engine.api.enums.FieldTypeEnum
//     */
//    private String returnType;
//
//    /**
//     * 普通函数注册参数
//     */
//    private RuleFunctionDTO functionDTO;
//
//    /**
//     * bean函数注册参数
//     */
//    private RuleBeanFunctionDTO beanFunctionDTO;
//
//    /**
//     * 参数校验
//     */
//    public void checkParam() {
//        Assert.notNull(this.sceneKey, RuleEngineFunctionFactorMsg.RULE_ENGINE_FUNCTION_FACTOR_SCENE_KEY_IS_NULL);
//        Assert.notNull(this.factorKey, RuleEngineFunctionFactorMsg.RULE_ENGINE_FUNCTION_FACTOR_KEY_IS_NULL);
//        Assert.notNull(this.factorName, RuleEngineFunctionFactorMsg.RULE_ENGINE_FUNCTION_FACTOR_NAME_IS_NULL);
//        Assert.notNull(this.returnType, RuleEngineFunctionFactorMsg.RULE_ENGINE_FUNCTION_FACTOR_RETURN_TYPE_IS_NULL);
//        Assert.notNull(this.functionType, RuleEngineFunctionFactorMsg.RULE_ENGINE_FUNCTION_FACTOR_FUNCTION_TYPE_IS_NULL);
//        if(Objects.equals(this.functionType, RuleEngineFunctionTypeDict.NORMAL)) {
//            Assert.notNull(this.functionDTO.getFullClassName(), RuleEngineFunctionFactorMsg.RULE_ENGINE_FUNCTION_FACTOR_FULL_CLASS_NAME_IS_NULL);
//            Assert.notNull(this.functionDTO.getFunctionName(), RuleEngineFunctionFactorMsg.RULE_ENGINE_FUNCTION_FACTOR_BEAN_FUNCTION_NAME_IS_NULL);
//        } else if(Objects.equals(this.functionType, RuleEngineFunctionTypeDict.BEAN)) {
//            Assert.notNull(this.beanFunctionDTO.getBeanName(), RuleEngineFunctionFactorMsg.RULE_ENGINE_FUNCTION_FACTOR_BEAN_NAME_IS_NULL);
//            Assert.notNull(this.beanFunctionDTO.getBeanFunctionName(), RuleEngineFunctionFactorMsg.RULE_ENGINE_FUNCTION_FACTOR_BEAN_FUNCTION_NAME_IS_NULL);
//        } else {
//            throw new RuntimeException(RuleEngineFunctionFactorMsg.RULE_ENGINE_FUNCTION_FACTOR_FUNCTION_TYPE_ILLEGAL);
//        }
//    }
//
//}
//
