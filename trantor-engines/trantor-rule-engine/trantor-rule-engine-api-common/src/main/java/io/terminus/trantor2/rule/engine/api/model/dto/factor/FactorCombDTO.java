package io.terminus.trantor2.rule.engine.api.model.dto.factor;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 因子DTO
 * <AUTHOR>
 * @createTime 2023/4/27 4:48 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FactorCombDTO implements Serializable {

    private static final long serialVersionUID = 2591226235591928485L;

    /**
     * 系统因子
     */
    private List<FactorDTO> sysFactor;

    /**
     * 业务因子
     */
    private List<FactorDTO> bizFactor;

}

