<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>trantor-rule-engine</artifactId>
        <groupId>io.terminus.trantor2</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>trantor-rule-engine-management</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>trantor-rule-engine-management-api</module>
        <module>trantor-rule-engine-management-impl</module>
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-rule-engine-api-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-rule-engine-management-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-rule-engine-management-impl</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
