package io.terminus.trantor2.globalreq.transfer;


import io.terminus.trantor2.common.RPCContext;
import io.terminus.trantor2.globalreq.context.GlobalReq;
import org.jetbrains.annotations.Nullable;
import org.springframework.core.Ordered;

/**
 * 数据加载的处理器
 * <p>可以在入口将对应数据放进上下文中</p>
 *
 * <AUTHOR>
 */
public interface GlobalDataProcessor extends Ordered {

    /**
     * 数据解析的前置处理，可以将额外的数据放进全局请求上下文中
     *
     * @param globalReq  请求上下文
     * @param rpcContext 在redis进行数据传输时改值为空
     */
    default void beforeLoad(GlobalReq globalReq, @Nullable RPCContext rpcContext) {

    }

    /**
     * 数据解析的后置处理，可以将额外数据放到 context 之类的地方
     */
    default void afterLoad(GlobalReq globalReq) {

    }

    @Override
    default int getOrder() {
        return 0;
    }
}
