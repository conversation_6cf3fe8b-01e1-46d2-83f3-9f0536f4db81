package io.terminus.trantor2.globalreq.transfer;


import io.terminus.trantor2.globalreq.context.GlobalReq;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * 全局请求数据传递接口
 *
 * <AUTHOR>
 */
public interface GlobalDataTransfer<T> {


    /**
     * 将数据传递下去
     *
     * @param to 数据要传递到的目标对象
     */
    void to(@NotNull GlobalReq globalReq, @Nullable T to);


    /**
     * 将数据解析回来
     *
     * @param from 数据要解析的来源对象
     */
    void from(@NotNull GlobalReq globalReq, @Nullable T from);
}
