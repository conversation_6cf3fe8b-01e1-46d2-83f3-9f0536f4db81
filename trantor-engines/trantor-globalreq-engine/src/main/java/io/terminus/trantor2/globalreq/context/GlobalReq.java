package io.terminus.trantor2.globalreq.context;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.terminus.trantor2.common.utils.JsonUtil;
import lombok.Getter;
import org.jetbrains.annotations.NotNull;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Getter
public class GlobalReq {


    /**
     * 提供管理 GlobalReq 的管理器，方便数据传递给下台jvm时候使用
     */
    @JsonIgnore
    private GlobalReqManger manger;


    /**
     * 全局请求的id
     */
    @NotNull
    private final String id;

    /**
     * 当前是不是分布式链路中的第一个入口
     * <p>一般资源的清理都是在 jvm 的第一个入口侧进行的
     */
    private final boolean firstEntrance;

    /**
     * 存储额外的全局请求数据
     */
    private final Map<String, Object> data;

    public GlobalReq(@NotNull String id, boolean firstEntrance) {
        this.id = id;
        this.firstEntrance = firstEntrance;
        this.data = new ConcurrentHashMap<>();
    }


    /**
     * 批量添加数据
     */
    public void addAllData(Map<String, Object> map) {
        data.putAll(map);
    }


    /**
     * 添加全局请求数据， 该数据会跟着链路一起往下传递
     * <p>其中 o 的类全名称作为key</p>
     */
    public void addData(Object o) {
        this.addData(generateKey(o.getClass()), o);
    }

    /**
     * 添加全局请求数据， 该数据会跟着链路一起往下传递
     */
    public void addData(@NotNull String key, Object o) {
        if (o == null) {
            return;
        }
        data.put(key, o);
    }


    /**
     * 获取全局请求的数据
     */
    public <T> T getData(Class<T> clazz) {
        return this.getData(generateKey(clazz), clazz);
    }

    /**
     * 根据指定的key获取全局请求的数据
     */
    @SuppressWarnings("unchecked")
    public <T> T getData(@NotNull String key, Class<T> clazz) {
        Object o = data.get(key);
        if (o == null) {
            return null;
        }
        if (clazz.isAssignableFrom(o.getClass())) {
            return (T) o;
        }

        // 避免多次进行值转换
        T convertResult = JsonUtil.convert(o, clazz);
        data.put(key, convertResult);
        return convertResult;
    }


    @NotNull
    private <T> String generateKey(Class<T> clzz) {
        return clzz.getName();
    }


    void setManger(GlobalReqManger manger) {
        this.manger = manger;
    }
}
