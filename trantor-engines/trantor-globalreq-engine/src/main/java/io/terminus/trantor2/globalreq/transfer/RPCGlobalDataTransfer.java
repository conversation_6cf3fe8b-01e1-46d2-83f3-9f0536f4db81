package io.terminus.trantor2.globalreq.transfer;


import io.terminus.trantor2.common.RPCContext;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.globalreq.context.GlobalReq;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Map;

/**
 * remote function call 的方式进行数据传递
 *
 * <AUTHOR>
 */
public class RPCGlobalDataTransfer implements GlobalDataTransfer<RPCContext> {


    private final String GLOBAL_DATA_KEY = "_GLOBAL_DATA_KEY";

    @Override
    public void to(@NotNull GlobalReq globalReq, @Nullable RPCContext to) {
        if (to != null) {
            to.setValue(GLOBAL_DATA_KEY, globalReq.getData());
        }
    }

    @Override
    public void from(@NotNull GlobalReq globalReq, @Nullable RPCContext from) {
        if (null == from || null == from.getContext() || from.getContext().isEmpty()) {
            return;
        }

        Map<String, Object> map = JsonUtil.toMap(from.getValue(GLOBAL_DATA_KEY));
        if (map != null && !map.isEmpty()) {
            globalReq.addAllData(map);
        }
    }
}
