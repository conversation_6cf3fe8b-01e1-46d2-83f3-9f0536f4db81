# Title: Summary what to do, format: <commitType>(<scope>): <Your Summary>

# eg: feat(scene): 新增供应链场景。

# Remember blank line between title and body.

# Body: Explain *what* and *why* (not *how*). Include erda taskId.

# commitType:
# 
# - feat: 特性提交, 一般是新增新的功能或者 API 等。
# - fix: 修复性提交。
# - test: 单元测试提交, 一般是增加或者说调整单元测试。
# - docs: 文档提交, 增加或者修改文档, 暂时文档没有维护在项目内, 后续视情况而定。
# - perf: 优化性提交, 比如功能重构, 性能重构等。
# - chore: 其他杂项。

# scope: 修改的业务模块。
# 
# - model: 模型部分
# - scene: 场景部分
# - flow: 业务流部分
# - connector: 连接器部分
# - console: 控制台部分
# - common: 公用部分
# - ...: 后续有新模块，补充即可
