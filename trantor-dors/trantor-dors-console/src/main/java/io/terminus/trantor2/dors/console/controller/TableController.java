package io.terminus.trantor2.dors.console.controller;

import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.dors.common.param.TableInfo;
import io.terminus.trantor2.dors.console.controller.request.SqlTableRequest;
import io.terminus.trantor2.dors.console.controller.request.SqlTableSaveRequest;
import io.terminus.trantor2.dors.console.service.SqlTableService;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/api/trantor/console/report/table")
@RequiredArgsConstructor
public class TableController {
    private final SqlTableService sqlTableService;

    @PostMapping("/sql/execute")
    public Response<TableInfo> executeSql(@RequestBody SqlTableRequest request) {
        return Response.ok(sqlTableService.executeQuery(request));
    }

    @PostMapping("/sql/save")
    public Response<DataStructNode> saveSql(@RequestBody SqlTableSaveRequest request) {
        DataStructNode model = sqlTableService.saveTable(request);
        return Response.ok(model);
    }
}
