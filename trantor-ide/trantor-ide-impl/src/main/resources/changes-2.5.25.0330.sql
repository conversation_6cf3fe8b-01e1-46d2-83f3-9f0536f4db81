CREATE TABLE IF NOT EXISTS `trantor_meta_blob`
(
  `id`         BIGINT       NOT NULL AUTO_INCREMENT,
  `team_id`    BIGINT       NOT NULL COMMENT 'team id',
  `key`        VARCHAR(255) NOT NULL COMMENT 'key of meta',
  `type`       VARCHAR(25)  NOT NULL COMMENT 'type of meta',
  `module_key` VARCHAR(50)  NOT NULL COMMENT 'module key, parsed from key prefix',
  `parent_key` VARCHAR(255) NULL COMMENT 'parent key of meta',
  `path`       VARCHAR(255) CHARSET utf8mb4 COLLATE utf8mb4_general_ci
                            NULL COMMENT 'path of meta',
  `oid`        BINARY(32)   NULL COMMENT 'object id, hash of meta content',
  `object`     MEDIUMBLOB   NOT NULL COMMENT 'meta content packed as object',
  `created_by` BIGINT       NOT NULL COMMENT 'created by',
  `updated_by` BIGINT       NOT NULL COMMENT 'updated by',
  `created_at` DATETIME     NOT NULL COMMENT 'created time',
  `updated_at` DATETIME     NOT NULL COMMENT 'updated time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_team_id_key` (`team_id`, `key`),
  KEY `idx_team_id_parent_key` (`team_id`, `parent_key`),
  KEY `idx_team_id_module_key_path` (`team_id`, `module_key`, `path`)
) ENGINE = InnoDB
  DEFAULT CHARSET = ascii
  COLLATE = ascii_general_ci
  COMMENT = 'trantor meta blob (main table of meta)';

CREATE TABLE IF NOT EXISTS `trantor_meta_history`
(
  `id`               BIGINT       NOT NULL AUTO_INCREMENT,
  `team_id`          BIGINT       NOT NULL COMMENT 'team id',
  `key`              VARCHAR(255) NOT NULL COMMENT 'key of meta',
  `last_oid`         BINARY(32)   NULL COMMENT 'object id, hash of previous meta content',
  `current_oid`      BINARY(32)   NULL COMMENT 'object id, hash of current meta content',
  `change_operation` VARCHAR(25)  NULL COMMENT 'type of change operation',
  `related_task_id`  BIGINT       NULL COMMENT 'related task id',
  `created_by`       BIGINT       NOT NULL COMMENT 'created by',
  `created_at`       DATETIME     NOT NULL COMMENT 'created time',
  PRIMARY KEY (`id`),
  KEY `idx_team_id_key_id` (`team_id`, `key`, `id`),
  KEY `idx_team_id_key_created_at` (`team_id`, `key`, `created_at`)
) ENGINE = InnoDB
  DEFAULT CHARSET = ascii
  COLLATE = ascii_general_ci
  COMMENT = 'trantor meta history (history records of meta changes)';
