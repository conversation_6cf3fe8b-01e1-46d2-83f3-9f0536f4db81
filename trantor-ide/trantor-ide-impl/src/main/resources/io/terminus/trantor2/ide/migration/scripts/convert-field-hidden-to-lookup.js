'use strict';

// 重写节点props中的配置
// 获取模块key
const getModuleKey = (str) => {
  return str?.includes('$') ? str?.split('$')?.[0] : '';
};
// 是否是主数据场景
const isMasterScene = (node) => {
  const contentStr = JSON.stringify(node);
  return (contentStr.includes('SYS_MasterData_') ||
    (contentStr.includes('"service":"saveMaster"') &&
      contentStr.includes('bindSystemServiceConfig')));
};
// 初始化节点Map，只能在初始化时调用
const _initParentNodeMap = (node) => {
  const nodeMap = {};
  const parentNodeMap = {};
  const keySet = {};
  traverseNode(node, (item) => {
    if (!keySet[item.key]) {
      keySet[item.key] = 1;
    }
    else {
      keySet[item.key]++;
    }
    nodeMap[item.key] = item;
    if (item.children) {
      item.children.forEach((cItem) => {
        parentNodeMap[cItem.key] = item;
      });
    }
  });
  return { nodeMap, parentNodeMap };
};
// 遍历节点
const traverseNode = (node, fun) => {
  const queue = [node];
  while (queue.length) {
    const item = queue.shift();
    const result = fun(item);
    if (item?.children) {
      queue.push(...item.children);
    }
    if (result === false) {
      break;
    }
  }
};
function nanoid(length = 16) {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_';
  const id = Array.from({ length }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
  return id;
}

const converts = {
  cvtFieldHiddenToLookup: (node, ctx) => {
    if (node.name === 'Field' ||
      node.name === 'FormField' ||
      node.name === 'DetailField') {
      const hasHidden = (p) => Object.keys(p).includes('hidden');
      const isPropsHidden = hasHidden(node?.props);
      const editFields = node?.props?.editComponentProps?.fields || [];
      const filterFields = node?.props?.editComponentProps?.filterFields || [];
      const isFieldsHidden = editFields?.find((item) => hasHidden(item));
      const isFilterFieldsHidden = filterFields?.find((item) => hasHidden(item));
      const isHideConfig = (lookup) => {
        return (!Object.keys(lookup?.valueRules || {}).length &&
          !Object.keys(lookup?.conditionGroup || {}).length &&
          lookup?.fieldRules?.hidden);
      };
      const addHiddenLookup = (lookup) => {
        if (!(lookup || []).find((item) => isHideConfig(item))) {
          return (lookup || []).concat({
            fieldRules: {
              hidden: true,
            },
            key: nanoid(),
          });
        }
        return lookup;
      };
      const convertHiddenToLookup = (n) => {
        if (n) {
          const { hidden, lookup, ...rest } = n;
          return {
            ...rest,
            lookup: hidden ? addHiddenLookup(lookup) : lookup,
          };
        }
        return n;
      };
      const reNode = { ...node };
      if (isPropsHidden) {
        reNode.props = convertHiddenToLookup(reNode?.props);
      }
      if (isFieldsHidden) {
        reNode.props.editComponentProps.fields = editFields.map((item) => convertHiddenToLookup(item));
      }
      if (isFilterFieldsHidden) {
        reNode.props.editComponentProps.filterFields = filterFields.map((item) => convertHiddenToLookup(item));
      }
      return reNode;
    }
    return node;
  },
};
const migrate = (content, ctx) => {
  const isMaster = ctx?.isMaster || isMasterScene(content);
  const testFunKey = ctx.__testFunc__;
  const moduleKey = getModuleKey(ctx.viewKey || '');
  const invokeConverts = (item) => {
    return (testFunKey ? [testFunKey] : Object.keys(converts)).reduce((prevItem, funKey) => {
      const curFun = converts[funKey];
      return (curFun?.(prevItem, {
        ...(ctx || {}),
        ..._initParentNodeMap(content),
        isMaster,
        rootNode: content,
        moduleKey,
      }) || prevItem);
    }, item);
  };
  const traverse = (item) => {
    const newItem = invokeConverts(item);
    if (newItem?.children?.length) {
      return {
        ...newItem,
        children: newItem.children.map((cItem) => traverse(cItem)),
      };
    }
    else {
      return newItem;
    }
  };
  const newContent = traverse(content);
  return newContent;
};

