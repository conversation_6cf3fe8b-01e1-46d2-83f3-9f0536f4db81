'use strict';

function migrate(content) {
    nodeUtils.traverseNode(content, (node) => {
        if (node.name === 'Field' ||
            node.name === 'DetailField' ||
            node.name === 'FormField') {
            fixDisplayComponentPropsFindFlow(node);
        }
    });
    return content;
}
function fixDisplayComponentPropsFindFlow(node) {
    // 还没被转换过，直接return
    if (node.props?.componentProps?.serviceInfo) {
        return;
    }
    // 已经被转换过
    if (!node.props.displayComponentProps?.findFlow) {
        const candidate = node.props?.editComponentProps?.findFlow;
        if (candidate) {
            if (!node.props.displayComponentProps) {
                node.props.displayComponentProps = {};
            }
            node.props.displayComponentProps.findFlow = candidate;
        }
    }
}
