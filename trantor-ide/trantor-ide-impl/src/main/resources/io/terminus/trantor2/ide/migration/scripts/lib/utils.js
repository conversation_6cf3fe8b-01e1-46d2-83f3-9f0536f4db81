const migUtil = {
  // 重写节点props中的配置
  rewriteNodeProps: (node, newProps, delKeys) => {
    if (node === null || node === void 0 ? void 0 : node.props) {
      const newNode = Object.assign(Object.assign({}, node), {
        props: Object.assign(Object.assign({}, node.props), newProps),
      });
      return delKeys ? migUtil.delPropsKey(newNode, delKeys) : newNode;
    }
    return node;
  },
  // 删除node下props中的某个key
  delPropsKey: (node, keys) => {
    if (node === null || node === void 0 ? void 0 : node.props) {
      const newP = {};
      Object.keys(node.props).forEach((k) => {
        var _a;
        if (!keys.includes(k)) {
          newP[k] = (_a = node.props) === null || _a === void 0 ? void 0 : _a[k];
        }
      });
      return Object.assign(Object.assign({}, node), { props: newP });
    }
    return node;
  },
  // 获取模块key
  getModuleKey: (str) => {
    var _a;
    return (str === null || str === void 0 ? void 0 : str.includes('$'))
      ? (_a = str === null || str === void 0 ? void 0 : str.split('$')) === null || _a === void 0
        ? void 0
        : _a[0]
      : '';
  },
  getServiceKey: (service, ctx) => {
    const moduleKey = ctx.moduleKey || '';
    return moduleKey ? `${moduleKey}$${service}` : service;
  },
  // 获取服务入参结构
  getParamsConfig: (p) => {
    const { config, context } = p;
    const travelObj = (obj) => {
      return Object.keys(obj).map((key) => {
        const curVal = obj[key];
        let curParams = {};
        if (Object.prototype.toString.call(curVal) === '[object Object]') {
          curParams = {
            fieldAlias: key,
            fieldName: key,
            fieldType: 'Object',
            elements: travelObj(obj[key]),
          };
        } else if (typeof curVal === 'string') {
          const [fieldType, type, val] = curVal.split('$');
          const configVal = context[val];
          const cMap = {
            exp: { expression: configVal, type: 'expression' },
            const: { value: configVal, type: 'const' },
            action: {
              type: 'action',
              action: {
                target: configVal,
              },
            },
          };
          curParams = Object.assign(
            { fieldAlias: key, fieldName: key, fieldType: fieldType },
            cMap[type]
              ? {
                valueConfig: type === 'action' ? cMap[type] : Object.assign({ name: key }, cMap[type]),
              }
              : {}
          );
        }
        return curParams;
      });
    };
    const params = travelObj(config);
    return params;
  },
  // 根据Flow的种类key获取服务
  getFlowConfigByType: (type, p, ctx) => {
    const { modelAlias, containerKey, serviceKey, paramsConfig, test$ } = p;
    if (type === 'FormGroup_Edit') {
      const curServiceKey = serviceKey || migUtil.getServiceKey('SYS_FindDataByIdService', ctx);
      return Object.assign(
        { test$: test$ || '!!route.recordId' },
        migUtil.getFlowConfig({
          modelAlias,
          serviceKey: curServiceKey,
          containerKey,
          paramsConfig: paramsConfig || {
            config: {
              request: { id: 'Number$exp$recordId' },
              modelKey: 'Text$const$modelAlias',
            },
            context: { modelAlias, recordId: 'route.recordId' },
          },
        })
      );
    } else if (type === 'FormGroup_Copy') {
      const curServiceKey = serviceKey || migUtil.getServiceKey('SYS_CopyDataConverterService', ctx);
      return Object.assign(
        { test$: test$ || '!!route.query.copyId' },
        migUtil.getFlowConfig({
          modelAlias,
          serviceKey: curServiceKey,
          containerKey,
          paramsConfig: paramsConfig || {
            config: {
              request: { id: 'Number$exp$copyId' },
              modelKey: 'Text$const$modelAlias',
            },
            context: { modelAlias, copyId: 'route.query.copyId' },
          },
        })
      );
    } else if (type === 'Table') {
      const curServiceKey = serviceKey || migUtil.getServiceKey('SYS_PagingDataService', ctx);
      return migUtil.getFlowConfig({
        serviceKey: curServiceKey,
        modelAlias,
        containerKey,
        paramsConfig: paramsConfig || {
          config: {
            request: { pageable: 'Pageable' },
            modelKey: 'Text$const$modelAlias',
          },
          context: { modelAlias, recordId: 'route.recordId' },
        },
      });
    } else if (type === 'Detail') {
      const curServiceKey = serviceKey || migUtil.getServiceKey('SYS_FindDataByIdService', ctx);
      return migUtil.getFlowConfig({
        modelAlias,
        serviceKey: curServiceKey,
        containerKey,
        paramsConfig: paramsConfig || {
          config: {
            request: { id: 'Number$exp$recordId' },
            modelKey: 'Text$const$modelAlias',
          },
          context: { modelAlias, recordId: 'route.recordId' },
        },
      });
    }
  },
  // 获取系统服务配置
  getFlowConfig: (p) => {
    const { serviceKey, modelAlias, paramsConfig, containerKey } = p;
    return Object.assign(
      { serviceKey, type: 'InvokeSystemService', modelAlias, context$: '$context', containerKey },
      paramsConfig ? { params: migUtil.getParamsConfig(paramsConfig) } : {}
    );
  },
  // 获取actionConfig中BindService服务
  getBindServiceConfig: (p) => {
    const { serviceKey, paramsConfig } = p;
    return Object.assign(
      { service: serviceKey },
      paramsConfig ? { params: migUtil.getParamsConfig(paramsConfig) } : {}
    );
  },
  // 是否是主数据场景
  isMasterScene: (node) => {
    const contentStr = JSON.stringify(node);
    return (
      contentStr.includes('SYS_MasterData_') ||
      (contentStr.includes('"service":"saveMaster"') && contentStr.includes('bindSystemServiceConfig'))
    );
  },
  /**
   * 截取脚本中的服务和模型：仅限格式  xxxx('serviceKey', 'modelAlias', ...)
   * **/
  getServiceAndModelFromScript: (scriptStr, splitStr) => {
    var _a, _b, _c, _d;
    const [serviceKey, modelAlias] =
      (_d =
        (_c =
          (_b = (_a = scriptStr.split(splitStr || '(')) === null || _a === void 0 ? void 0 : _a[1]) === null ||
          _b === void 0
            ? void 0
            : _b.split) === null || _c === void 0
          ? void 0
          : _c.call(_b, ',')) === null || _d === void 0
        ? void 0
        : _d.map((item) => {
          var _a, _b;
          return (_b = (_a = item.replaceAll("'", '')) === null || _a === void 0 ? void 0 : _a.trim) === null ||
          _b === void 0
            ? void 0
            : _b.call(_a);
        });
    return { serviceKey, modelAlias };
  },
};
const nodeUtils = {
  // 初始化节点Map，只能在初始化时调用
  _initParentNodeMap: (node) => {
    const nodeMap = {};
    const parentNodeMap = {};
    const keySet = {};
    nodeUtils.traverseNode(node, (item) => {
      if (!keySet[item.key]) {
        keySet[item.key] = 1;
      } else {
        keySet[item.key]++;
      }
      nodeMap[item.key] = item;
      if (item.children) {
        item.children.forEach((cItem) => {
          parentNodeMap[cItem.key] = item;
        });
      }
    });
    return { nodeMap, parentNodeMap };
  },
  // 获取当前节点所有父节点
  findNodeAncestors: (node, ctx) => {
    const getNodeParent = (k) => {
      var _a;
      return (_a = ctx === null || ctx === void 0 ? void 0 : ctx.parentNodeMap) === null || _a === void 0
        ? void 0
        : _a[k];
    };
    const key = node.key;
    const result = [];
    let next = getNodeParent(key);
    while (next) {
      result.unshift(next);
      next = getNodeParent(next === null || next === void 0 ? void 0 : next.key);
    }
    return result;
  },
  // 遍历节点
  traverseNode: (node, fun) => {
    const queue = [node];
    while (queue.length) {
      const item = queue.shift();
      const result = fun(item);
      if (item === null || item === void 0 ? void 0 : item.children) {
        queue.push(...item.children);
      }
      if (result === false) {
        break;
      }
    }
  },
  // 按key或name寻找节点
  findNodeByKeyOrName: (p, ctx) => {
    const node = ctx.rootNode;
    const { key, name, modelAlias } = p;
    let curNode = null;
    nodeUtils.traverseNode(node, (item) => {
      var _a;
      if (key && item.key === key) {
        curNode = item;
        return false;
      }
      if (item.name === name) {
        if (
          modelAlias &&
          modelAlias ===
          ((_a = item === null || item === void 0 ? void 0 : item.props) === null || _a === void 0
            ? void 0
            : _a.modelAlias)
        ) {
          curNode = item;
        } else {
          curNode = item;
        }
        return false;
      }
    });
    return curNode;
  },
};
