const converts = {
  // 删除props.serviceKey
  delPropsServiceKey: node => migUtil.delPropsKey(node, ['serviceKey']),

  // 删除onFinish$
  delOnFinish$: node => migUtil.delPropsKey(node, ['onFinish$']),

  // 存在flow配置时，删除flow$/loadFlow$
  delFlow$: node => {
    if (node?.props?.flow?.serviceKey && node?.props?.flow$) {
      return migUtil.delPropsKey(node, ['flow$', 'loadFlow$'])
    }
    return node
  },
}

const migrate = (content, context) => {
  const invokeConverts = item => {
    return Object.keys(converts).reduce((prevItem, funKey) => {
      const curFun = converts[funKey]
      return curFun(prevItem) || prevItem
    }, item)
  }

  const traverse = item => {
    const newItem = invokeConverts(item)
    if (newItem?.children?.length) {
      return {
        ...newItem,
        children: newItem.children.map(cItem => traverse(cItem)),
      }
    } else {
      return newItem
    }
  }

  const newContent = traverse(content)
  return newContent
}
