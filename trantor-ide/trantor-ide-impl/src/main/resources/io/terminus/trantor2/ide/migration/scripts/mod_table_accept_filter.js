const converts = {
  cvtTableAcceptFilterQuery: (node, ctx) => {
    var _a, _b;
    if (node.name === 'Table') {
      const curViewKey = ctx === null || ctx === void 0 ? void 0 : ctx.viewKey;
      const curSceneView =
        (_a = ctx === null || ctx === void 0 ? void 0 : ctx.sceneViewList) === null || _a === void 0
          ? void 0
          : _a.find((item) => item.key === curViewKey);
      if ((curSceneView === null || curSceneView === void 0 ? void 0 : curSceneView.type) === 'LIST') {
        const pNodeNames =
          (_b = nodeUtils.findNodeAncestors(node, ctx) || []) === null || _b === void 0
            ? void 0
            : _b.map((item) => item.name);
        // 当前Table组件是List类型页面里，且不被ChildView和Detail包裹的。
        if (!(pNodeNames.includes('Detail') || pNodeNames.includes('ChildView'))) {
          return migUtil.rewriteNodeProps(node, {
            acceptFilterQuery: true,
          });
        }
      }
    }
    return node;
  },
};
const migrate = (content, ctx) => {
  const isMaster = (ctx === null || ctx === void 0 ? void 0 : ctx.isMaster) || migUtil.isMasterScene(content);
  const testFunKey = ctx.__testFunc__;
  const moduleKey = migUtil.getModuleKey(ctx.viewKey || '');
  const invokeConverts = (item) => {
    return (testFunKey ? [testFunKey] : Object.keys(converts)).reduce((prevItem, funKey) => {
      const curFun = converts[funKey];
      return (
        (curFun === null || curFun === void 0
          ? void 0
          : curFun(
            prevItem,
            Object.assign(Object.assign(Object.assign({}, ctx || {}), nodeUtils._initParentNodeMap(content)), {
              isMaster,
              rootNode: content,
              moduleKey,
            })
          )) || prevItem
      );
    }, item);
  };
  const traverse = (item) => {
    var _a;
    const newItem = invokeConverts(item);
    if (
      (_a = newItem === null || newItem === void 0 ? void 0 : newItem.children) === null || _a === void 0
        ? void 0
        : _a.length
    ) {
      return Object.assign(Object.assign({}, newItem), { children: newItem.children.map((cItem) => traverse(cItem)) });
    } else {
      return newItem;
    }
  };
  const newContent = traverse(content);
  return newContent;
};
