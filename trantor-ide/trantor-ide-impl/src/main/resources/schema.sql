-- V2.5.24.0330
CREATE TABLE IF NOT EXISTS `trantor_dlock`
(
  id        BIGINT       NOT NULL AUTO_INCREMENT,
  lock_key  VARCHAR(255) NOT NULL COMMENT 'lock key, unique',
  token     VARCHAR(255) NOT NULL COMMENT 'lock token, to prevent other process release lock',
  expire_at TIMESTAMP    NOT NULL COMMENT 'expire time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_lock_key` (`lock_key`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  COMMENT = 'trantor distributed lock';

CREATE TABLE IF NOT EXISTS `trantor_platform_config`
(
  id         BIGINT        NOT NULL AUTO_INCREMENT,
  config_key VARCHAR(255)  NOT NULL COMMENT 'config key, unique',
  config_val VARCHAR(4096) NOT NULL COMMENT 'config value',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  COMMENT = 'trantor platform config';

-- V2.5.24.0530
CREATE TABLE IF NOT EXISTS `trantor_task_run`
(
  `id`                 BIGINT      NOT NULL AUTO_INCREMENT,
  `team_id`            BIGINT      NOT NULL COMMENT 'team id',
  `team_code`          VARCHAR(64) NOT NULL COMMENT 'team code, which the task belongs to',
  `task_code`          VARCHAR(64) NOT NULL COMMENT 'task code, indicate what task to run',
  `dry_run`            BIT(1)      NOT NULL COMMENT 'is dry run',
  `comment`            VARCHAR(255) CHARSET utf8mb4 COLLATE utf8mb4_general_ci
                                   NOT NULL COMMENT 'comment',
  `run_display_name`   VARCHAR(255) CHARSET utf8mb4 COLLATE utf8mb4_general_ci
                                   NULL DEFAULT NULL COMMENT 'run display name',
  `exec_type`          VARCHAR(25) NOT NULL COMMENT 'execution type, e.g. MANUAL, CRON, SUB_TASK',
  `exec_user_id`       BIGINT      NOT NULL COMMENT 'user id, who execute this task',
  `exec_cron_id`       BIGINT      NULL DEFAULT NULL COMMENT 'cron id, NULL means not scheduled',
  `status`             VARCHAR(25) NOT NULL COMMENT 'status',
  `options`            JSON        NOT NULL COMMENT 'options, json object, structure is defined by task (task_name)',
  `result`             JSON        NOT NULL COMMENT 'result, json object, output.data structure is defined by task (task_name)',
  `logs`               JSON        NOT NULL COMMENT 'logs, json array of string',
  `panic_message`      VARCHAR(5000) CHARSET utf8mb4 COLLATE utf8mb4_general_ci
                                   NOT NULL COMMENT 'panic message',
  `parent_task_run_id` BIGINT      NULL DEFAULT NULL COMMENT 'parent task run id, NULL means this is root task',
  `sub_order_no`       INT         NULL DEFAULT NULL COMMENT 'order no of sub task, NULL means this is root task',
  `start_at`           DATETIME    NULL DEFAULT NULL COMMENT 'start time',
  `end_at`             DATETIME    NULL DEFAULT NULL COMMENT 'end time',
  `created_at`         DATETIME    NOT NULL COMMENT 'created time',
  `updated_at`         DATETIME    NOT NULL COMMENT 'updated time',
  PRIMARY KEY (`id`),
  KEY `idx_team_id_task_code_status` (`team_id`, `task_code`, `status`),
  KEY `idx_parent_task_run_id` (`parent_task_run_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = ascii
  COLLATE = ascii_general_ci
  COMMENT = 'trantor task run (run detail of task)';

-- V2.5.24.0730
CREATE TABLE IF NOT EXISTS `trantor_meta_index_asset`
(
  `id`              BIGINT       NOT NULL AUTO_INCREMENT,
  `team_id`         BIGINT       NOT NULL COMMENT 'team id',
  `team_code`       VARCHAR(64)  NOT NULL COMMENT 'team code',
  `key`             VARCHAR(255) NOT NULL COMMENT 'key of asset',
  `type`            VARCHAR(25)  NOT NULL COMMENT 'type of asset',
  `sub_type`        VARCHAR(25)  NOT NULL COMMENT 'sub type of asset',
  `module_key`      VARCHAR(50)  NOT NULL COMMENT 'module key, parsed from key prefix',
  `name`            VARCHAR(150) CHARSET utf8mb4 COLLATE utf8mb4_general_ci
                                 NOT NULL COMMENT 'name of asset',
  `path`            VARCHAR(255) CHARSET utf8mb4 COLLATE utf8mb4_general_ci
                                 NOT NULL COMMENT 'path of asset',
  `parent_key`      VARCHAR(255) NULL COMMENT 'parent key of asset, (deprecated)',
  `description`     VARCHAR(5000) CHARSET utf8mb4 COLLATE utf8mb4_general_ci
                                 NOT NULL COMMENT 'description of asset',
  `access`          VARCHAR(15)  NOT NULL COMMENT 'access level of asset',
  `oid`             BINARY(32)   NOT NULL COMMENT 'object id, hash of ori meta content',
  `ext_oid`         BINARY(32)   NOT NULL COMMENT 'object id, hash of ext meta content',
  `structure`       MEDIUMBLOB   NOT NULL COMMENT 'structure of asset, to describe inner nodes and refs, etc.',
  `lite_props`      JSON         NOT NULL COMMENT 'console properties of asset, trimmed to only keep necessary fields, used for search',
  `designer_object` MEDIUMBLOB   NOT NULL COMMENT 'asset content packed as object (ext merged for console)',
  `runtime_object`  MEDIUMBLOB   NOT NULL COMMENT 'asset content packed as object (ext merged for runtime)',
  `modified_at`     DATETIME     NULL COMMENT 'last modified time',
  `modified_by`     BIGINT       NULL COMMENT 'last modified by',
  `created_at`      DATETIME     NOT NULL COMMENT 'created time',
  `updated_at`      DATETIME     NOT NULL COMMENT 'updated time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_team_id_key` (`team_id`, `key`),
  KEY `idx_team_id_type_sub_type` (`team_id`, `type`, `sub_type`),
  KEY `idx_team_id_module_key_path` (`team_id`, `module_key`, `path`),
  KEY `idx_team_id_module_key_access` (`team_id`, `module_key`, `access`),
  KEY `idx_team_id_parent_key` (`team_id`, `parent_key`)
) ENGINE = InnoDB
  DEFAULT CHARSET = ascii
  COLLATE = ascii_general_ci
  COMMENT = 'trantor meta index asset (asset index of meta)';

-- V2.5.24.0930
CREATE TABLE IF NOT EXISTS `trantor_meta_object`
(
  `team_id`   BIGINT      NOT NULL COMMENT 'team id',
  `oid`       BINARY(32)  NOT NULL COMMENT 'object id, hash of object content',
  `content`   MEDIUMBLOB  NOT NULL COMMENT 'object content, maximum 16MB',
  `type`      VARCHAR(25) NOT NULL COMMENT 'object type',
  `size`      INT         NOT NULL COMMENT 'object size, in bytes',
  `expire_at` BIGINT      NOT NULL COMMENT 'expire time, 0 means never expire',
  PRIMARY KEY (`team_id`, `oid`)
) ENGINE = InnoDB
  DEFAULT CHARSET = ascii
  COLLATE = ascii_general_ci
  COMMENT = 'trantor meta object (all objects including snapshot)';

CREATE TABLE IF NOT EXISTS `trantor_meta_index_ref`
(
  `id`          BIGINT       NOT NULL AUTO_INCREMENT,
  `team_id`     BIGINT       NOT NULL COMMENT 'team id',
  `source_key`  VARCHAR(255) NOT NULL COMMENT 'source key of asset',
  `source_type` VARCHAR(25)  NOT NULL COMMENT 'source type of asset',
  `target_key`  VARCHAR(255) NOT NULL COMMENT 'target key of asset',
  `target_type` VARCHAR(25)  NOT NULL COMMENT 'target type of asset',
  `created_at`  DATETIME     NOT NULL COMMENT 'created time',
  `updated_at`  DATETIME     NOT NULL COMMENT 'updated time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_team_id_source_key_target_key` (`team_id`, `source_key`, `target_key`),
  KEY `idx_team_id_target_key_source_type` (`team_id`, `target_key`, `source_type`),
  KEY `idx_team_id_source_key_target_type` (`team_id`, `source_key`, `target_type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = ascii
  COLLATE = ascii_general_ci
  COMMENT = 'trantor meta index ref (ref of meta)';

-- V2.5.25.0330
CREATE TABLE IF NOT EXISTS `trantor_meta_blob`
(
  `id`         BIGINT       NOT NULL AUTO_INCREMENT,
  `team_id`    BIGINT       NOT NULL COMMENT 'team id',
  `key`        VARCHAR(255) NOT NULL COMMENT 'key of meta',
  `type`       VARCHAR(25)  NOT NULL COMMENT 'type of meta',
  `module_key` VARCHAR(50)  NOT NULL COMMENT 'module key, parsed from key prefix',
  `parent_key` VARCHAR(255) NULL COMMENT 'parent key of meta',
  `path`       VARCHAR(255) CHARSET utf8mb4 COLLATE utf8mb4_general_ci
                            NULL COMMENT 'path of meta',
  `oid`        BINARY(32)   NULL COMMENT 'object id, hash of meta content',
  `object`     MEDIUMBLOB   NOT NULL COMMENT 'meta content packed as object',
  `created_by` BIGINT       NOT NULL COMMENT 'created by',
  `updated_by` BIGINT       NOT NULL COMMENT 'updated by',
  `created_at` DATETIME     NOT NULL COMMENT 'created time',
  `updated_at` DATETIME     NOT NULL COMMENT 'updated time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_team_id_key` (`team_id`, `key`),
  KEY `idx_team_id_parent_key` (`team_id`, `parent_key`),
  KEY `idx_team_id_module_key_path` (`team_id`, `module_key`, `path`)
) ENGINE = InnoDB
  DEFAULT CHARSET = ascii
  COLLATE = ascii_general_ci
  COMMENT = 'trantor meta blob (main table of meta)';

CREATE TABLE IF NOT EXISTS `trantor_meta_history`
(
  `id`               BIGINT       NOT NULL AUTO_INCREMENT,
  `team_id`          BIGINT       NOT NULL COMMENT 'team id',
  `key`              VARCHAR(255) NOT NULL COMMENT 'key of meta',
  `last_oid`         BINARY(32)   NULL COMMENT 'object id, hash of previous meta content',
  `current_oid`      BINARY(32)   NULL COMMENT 'object id, hash of current meta content',
  `change_operation` VARCHAR(25)  NULL COMMENT 'type of change operation',
  `related_task_id`  BIGINT       NULL COMMENT 'related task id',
  `created_by`       BIGINT       NOT NULL COMMENT 'created by',
  `created_at`       DATETIME     NOT NULL COMMENT 'created time',
  PRIMARY KEY (`id`),
  KEY `idx_team_id_key_id` (`team_id`, `key`, `id`),
  KEY `idx_team_id_key_created_at` (`team_id`, `key`, `created_at`)
) ENGINE = InnoDB
  DEFAULT CHARSET = ascii
  COLLATE = ascii_general_ci
  COMMENT = 'trantor meta history (history records of meta changes)';

CREATE TABLE IF NOT EXISTS `trantor_meta_edit_session`
(
  `id`          BIGINT      NOT NULL AUTO_INCREMENT,
  `team_id`     BIGINT      NOT NULL COMMENT 'team id',
  `session_id`  VARCHAR(64) NOT NULL COMMENT 'unique session identifier',
  `title`       VARCHAR(255) CHARSET utf8mb4 COLLATE utf8mb4_general_ci
                            NULL COMMENT 'session title or description',
  `checkpoints` JSON        NULL COMMENT 'checkpoint list with snapshot OIDs and descriptions',
  `status`      VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT 'session status: ACTIVE, CLOSED',
  `created_by`  BIGINT      NOT NULL COMMENT 'session creator',
  `updated_by`  BIGINT      NOT NULL COMMENT 'updated by',
  `created_at`  DATETIME    NOT NULL COMMENT 'session creation time',
  `updated_at`  DATETIME    NOT NULL COMMENT 'last update time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_team_session_id` (`team_id`, `session_id`),
  KEY `idx_team_created_by` (`team_id`, `created_by`),
  KEY `idx_team_created_at` (`team_id`, `created_at`)
) ENGINE = InnoDB
  DEFAULT CHARSET = ascii
  COLLATE = ascii_general_ci
  COMMENT = 'trantor meta edit session (metadata editing sessions)';
