package io.terminus.trantor2.ide.import_export;

import com.google.common.base.CaseFormat;
import io.terminus.trantor2.ide.import_export.pipe.*;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.service.ExtMetaService;
import io.terminus.trantor2.meta.api.service.MetaEditService;
import io.terminus.trantor2.meta.api.service.MetaQueryService;

import java.io.File;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 **/
public class ReaderFactory {
    public static MetaReader createMetaReader(File file, MetaQueryService queryService, MetaEditService editService, ExtMetaService extMetaService) {
        //module
        if (file.getName().equals("trantor.json")) {
            return ModulePipe.instance(queryService, editService, extMetaService);
        }
        //version
        if (file.getName().equals("version")) {
            return VersionPipe.instance(queryService, editService);
        }
        if (file.getName().equals("index.json")) {
            //error code
            if (file.getParent().endsWith("error_code")) {
                return ErrorCodePipe.instance(queryService, editService, extMetaService);
            } else if (file.getParent().endsWith("variable")) {
                //variable
                return VariablePipe.instance(queryService, editService, extMetaService);
            }
            //scene
            return ScenePipe.instance(queryService, editService, extMetaService);
        }
        //model
        if (file.getName().contains(".model.")) {
            return ModelPipe.instance(queryService, editService, extMetaService);
        }

        //service
        if (file.getName().contains(".servicedefinition.")) {
            return ServicePipe.instance(queryService, editService, extMetaService);
        }

        //menu tree
        if (file.getName().equals("menu.json")) {
            return MenuPipe.instance(queryService, editService, extMetaService);
        }
        //menu icon
        if (file.getName().endsWith("icon.json")) {
            return MenuIconPipe.instance(queryService, editService, extMetaService);
        }
        //api icon
        if (file.getName().equals("api.json")) {
            return ApiPipe.instance(queryService, editService, extMetaService);
        }
        //

        if (isFolder(file)) {
            return FolderPipe.instance(queryService, editService);
        }

        //default reader
        MetaPipe metaPipe = defaultReader(file, queryService, editService, extMetaService);
        if (metaPipe != null) {
            return metaPipe;
        }

        // ignore
        return IgnorePipe.instance();
    }

    private static boolean isPermission(File file) {
        String name = file.getName();
        return Stream.of(MetaType.Permission, MetaType.DataControlDimension).anyMatch(value -> name.equals(CaseFormat.UPPER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, value.name()) + ".json"));
    }

    private static boolean isFolder(File file) {
        if (file.isFile()) {
            return false;
        }
        if (file.getName().equals("source")) {
            return false;
        }
        if (file.getName().equals("code_rule")) {
            return false;
        }
        if (file.getName().equals("error_code")) {
            return false;
        }
        if (file.getName().equals("variable")) {
            return false;
        }
        if (file.listFiles() == null) {
            return true;
        }
        for (File f : file.listFiles()) {
            if (f.getName().equals("index.json")) {
                return false;
            }
        }
        return true;
    }

    private static MetaPipe defaultReader(File file, MetaQueryService queryService, MetaEditService editService, ExtMetaService extMetaService) {
        if (file.isDirectory()) {
            return null;
        }
        String fileName = file.getName();
        for (MetaType value : MetaType.values()) {
            if (fileName.contains("." + value.name().toLowerCase() + ".")) {
                return StandardPipe.instance(queryService, editService, extMetaService);
            }
        }
        return null;
    }
}
