package io.terminus.trantor2.meta.management.task;

/**
 * @deprecated we use result.summary.failed instead
 */
@Deprecated
public class TaskInternalException extends RuntimeException {
    private static final long serialVersionUID = -8350490461855505083L;

    public TaskInternalException(String message) {
        super(message);
    }

    public TaskInternalException(String message, Throwable cause) {
        super(message, cause);
    }
}
