package io.terminus.trantor2.meta.management.util;

import io.terminus.trantor2.meta.api.dto.Manifest;
import io.terminus.trantor2.meta.editor.service.ObjectTraversal;
import io.terminus.trantor2.meta.exception.MetaTaskException;
import io.terminus.trantor2.meta.management.task.composite.ObjectExportVisitor;
import io.terminus.trantor2.module.service.OSSService;
import io.terminus.trantor2.module.util.OSSConstant;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.function.Consumer;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @author: yangyuqiang
 * @date: 2023/11/15 10:39 AM
 **/
@Slf4j
public class UploadUtil {
    public static String uploadMeta(OSSService ossService, ObjectTraversal traversal, Long teamId, String teamCode, String snapshotOid, Boolean v2) {
        Path tempFile = null;
        try {
            tempFile = Files.createTempFile("temp-export-", ".zip");

            try (OutputStream fos = Files.newOutputStream(tempFile);
                 ZipOutputStream zos = new ZipOutputStream(fos)) {
                traversal.breadthFirst(teamId, snapshotOid, new ObjectExportVisitor(zos, ossService, v2, Manifest.PACKAGE_TYPE_FULL));
            }

            String zipFileName = String.format("%d-%s-%s.zip", teamId, teamCode, snapshotOid);

            return ossService.uploadFileAndGetUrl(OSSConstant.CONSOLE_FILE_PREFIX + "export/", zipFileName, Files.newInputStream(tempFile), "application/zip", false, true);
        } catch (IOException e) {
            throw new MetaTaskException("export object failed: " + e.getMessage());
        } finally {
            if (tempFile != null) {
                try {
                    Files.deleteIfExists(tempFile);
                } catch (IOException e) {
                    log.error("delete temp file failed: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 上传补丁包：
     * - manifest.packageType = PATCH
     * - 追加 patch.json 到 zip 根目录，结构为 { "metaKeys": [ ... ] }
     * - 文件名以 "PATCH-" 前缀标识，保持 oid 仍位于最后一个 '-' 与 '.zip' 之间
     */
    public static String uploadPatch(OSSService ossService, ObjectTraversal traversal, Long teamId, String teamCode, String snapshotOid, Boolean v2, java.util.List<String> metaKeys) {
        Path tempFile = null;
        try {
            tempFile = Files.createTempFile("temp-patch-", ".zip");

            try (OutputStream fos = Files.newOutputStream(tempFile);
                 ZipOutputStream zos = new ZipOutputStream(fos)) {
                // 导出对象，同时写入 manifest，类型为 PATCH
                traversal.breadthFirst(teamId, snapshotOid, new ObjectExportVisitor(zos, ossService, v2, Manifest.PACKAGE_TYPE_PATCH));

                // 写入 patch.json（可扩展）
                com.fasterxml.jackson.databind.node.ObjectNode node = io.terminus.trantor2.meta.util.ObjectJsonUtil.MAPPER.createObjectNode();
                if (metaKeys != null && !metaKeys.isEmpty()) {
                    node.set("metaKeys", io.terminus.trantor2.meta.util.ObjectJsonUtil.MAPPER.valueToTree(metaKeys));
                } else {
                    node.putArray("metaKeys");
                }
                String json = io.terminus.trantor2.meta.util.ObjectJsonUtil.serialize(node);
                ZipEntry entry = new ZipEntry("patch.json");
                zos.putNextEntry(entry);
                byte[] bytes = json.getBytes(java.nio.charset.StandardCharsets.UTF_8);
                zos.write(bytes, 0, bytes.length);
                zos.closeEntry();
            }

            String zipFileName = String.format("PATCH-%d-%s-%s.zip", teamId, teamCode, snapshotOid);

            return ossService.uploadFileAndGetUrl(OSSConstant.CONSOLE_FILE_PREFIX + "export/", zipFileName, Files.newInputStream(tempFile), "application/zip", false, true);
        } catch (IOException e) {
            throw new MetaTaskException("export patch package failed: " + e.getMessage());
        } finally {
            if (tempFile != null) {
                try {
                    Files.deleteIfExists(tempFile);
                } catch (IOException e) {
                    log.error("delete temp file failed: {}", e.getMessage());
                }
            }
        }
    }

    public static String parseOidFromUrl(String url) {
        return url.substring(url.lastIndexOf("-") + 1, url.lastIndexOf("."));
    }

    public static String uploadIconPackage(OSSService ossService, Stream<Consumer<ZipOutputStream>> iconWriters, String fileName) {
        Path tempFile = null;
        try {
            tempFile = Files.createTempFile("temp-export-", ".zip");

            try (OutputStream fos = Files.newOutputStream(tempFile);
                 ZipOutputStream zos = new ZipOutputStream(fos)) {
                iconWriters.forEach(writer -> writer.accept(zos));
            }

            return ossService.uploadFileAndGetUrl(OSSConstant.CONSOLE_FILE_PREFIX + "icon/export/", fileName, Files.newInputStream(tempFile), "application/zip", false, true);
        } catch (IOException e) {
            throw new MetaTaskException("export icon package failed: " + e.getMessage());
        } finally {
            if (tempFile != null) {
                try {
                    Files.deleteIfExists(tempFile);
                } catch (IOException e) {
                    log.error("delete temp file failed: {}", e.getMessage());
                }
            }
        }
    }
}
