package io.terminus.trantor2.ide.import_export.pipe;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.ide.import_export.MetaReadContext;
import io.terminus.trantor2.ide.import_export.MetaWriteContext;
import io.terminus.trantor2.ide.import_export.dto.BaseExportDTO;
import io.terminus.trantor2.ide.import_export.exception.ImportCheckException;
import io.terminus.trantor2.ide.import_export.io.MetaFileConverter;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.service.ExtMetaService;
import io.terminus.trantor2.meta.api.service.MetaEditService;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;

import java.io.File;
import java.util.zip.ZipOutputStream;

import static io.terminus.trantor2.meta.api.model.MetaType.FolderRoot;

/**
 * <AUTHOR>
 **/
public class ServicePipe extends StandardPipe {
    private static ServicePipe instance;

    public ServicePipe(MetaQueryService queryService, MetaEditService editService, ExtMetaService extMetaService) {
        super(queryService, editService, extMetaService);
    }

    public static ServicePipe instance(MetaQueryService queryService, MetaEditService editService, ExtMetaService extMetaService) {
        if (instance == null) {
            instance = new ServicePipe(queryService, editService, extMetaService);
        }
        return instance;
    }

    @Override
    public void read(File file, MetaReadContext ctx) {
        MetaFileConverter.MetaFile metaFile = converter.read(file);
        if (metaFile.getMetaContent() == null || metaFile.getMetaContent().isNull()) {
            throw new ImportCheckException("[" + metaFile.getName() + "]元信息信息为空");
        }
        BaseExportDTO dto = ObjectJsonUtil.MAPPER.convertValue(metaFile.getMetaContent(), BaseExportDTO.class);
        MetaEditAndQueryContext metaEditAndQueryContext = EditUtil.newCtx(ctx.getTeamId(), TrantorContext.getCurrentUserId());
        String parentPath = file.getParent().replace(ctx.getBasePath() + "/source", "") + "/";
        String parentKey = ctx.getKeyByPath(parentPath);
        if (parentKey == null) {
            metaEditAndQueryContext.setModuleKey(ctx.getTargetModule());
            parentKey = queryService.queryInTeam(ctx.getTeamId())
                    .findOne(Cond.moduleOf(ctx.getTargetModule()).and(Field.type().equal(FolderRoot.name())))
                    .map(MetaTreeNode::getKey)
                    .orElseThrow(() -> new TrantorRuntimeException("folder root not found"));
        }
        MetaTreeNodeExt service = new MetaTreeNodeExt();
        service.setKey(dto.getKey());
        service.setName(dto.getName());
        service.setType(dto.getType());
        service.setParentKey(parentKey);
        service.setParentPath(parentPath);
        service.setProps(dto.getProps());
        service.setAccess(dto.getAccess());
        service.setDescription(dto.getDescription());
        saveMeta(metaEditAndQueryContext, service, ctx);
    }

    @Override
    public void write(MetaTreeNode meta, MetaWriteContext ctx, ZipOutputStream zos) {
        String path = ctx.getPath(meta.getParentKey());
        // 系统预置元数据
        if (path == null) {
            return;
        }

        converter.write(ObjectJsonUtil.deserialize(JsonUtil.toJson(toExportDTO(meta))), KeyUtil.shortKey(meta.getKey()) + "." + meta.getType().toLowerCase() + ".json", path, zos);
    }
}
