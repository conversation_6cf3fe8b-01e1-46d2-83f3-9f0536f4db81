package io.terminus.trantor2.meta.management.dlock;

import io.terminus.trantor2.meta.jdbc.MetaJdbcTemplateFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_COMMITTED)
public class JdbcDLock implements DLock {

    private static final String DELETE_EXPIRED_SQL = "DELETE FROM trantor_dlock WHERE expire_at < ?";
    private static final String LOCK_SQL = "INSERT INTO trantor_dlock (lock_key, token, expire_at) VALUES (?, ?, ?)";
    private static final String UNLOCK_SQL = "DELETE FROM trantor_dlock WHERE lock_key = ? and token = ?";
    private static final String _UNLOCK_SQL = "DELETE FROM trantor_dlock WHERE lock_key = ?";
    private static final String RENEWAL_SQL = "UPDATE trantor_dlock SET expire_at = ? WHERE lock_key = ? and token = ?";
    private static final String SELECT_TOKEN_SQL = "SELECT trantor_dlock.token FROM trantor_dlock WHERE lock_key = ?";
    private static final String SELECT_LOCK_INFO_SQL = "SELECT trantor_dlock.token,trantor_dlock.expire_at FROM trantor_dlock WHERE lock_key = ?";

    private final MetaJdbcTemplateFactory metaJdbcTemplateFactory;

    public JdbcDLock(MetaJdbcTemplateFactory metaJdbcTemplateFactory) {
        this.metaJdbcTemplateFactory = metaJdbcTemplateFactory;
    }

    private JdbcTemplate getJdbcTemplate() {
        return metaJdbcTemplateFactory.createJdbcTemplate();
    }

    @Override
    public boolean lock(String lockKey, String token, long timeoutSeconds) {
        checkTimeoutSeconds(timeoutSeconds);
        Date now = new Date();
        // lazy delete expired lock
        getJdbcTemplate().update(DELETE_EXPIRED_SQL, now);
        try {
            return 1 == getJdbcTemplate().update(LOCK_SQL, lockKey, token, new Date(now.getTime() + timeoutSeconds * 1000));
        } catch (DuplicateKeyException e) {
            return false;
        }
    }

    @Override
    public boolean unLock(String lockKey, String token) {
        if (token == null) {
            return 1 == getJdbcTemplate().update(_UNLOCK_SQL, lockKey);
        }
        return 1 == getJdbcTemplate().update(UNLOCK_SQL, lockKey, token);
    }

    @Override
    public boolean renewal(String lockKey, String token, long timeoutSeconds) {
        checkTimeoutSeconds(timeoutSeconds);
        return 1 == getJdbcTemplate().update(RENEWAL_SQL, new Date(new Date().getTime() + timeoutSeconds * 1000), lockKey, token);
    }

    @Override
    public Optional<LockInfo> getLockInfo(String lockKey) {
        // lazy delete expired lock
        getJdbcTemplate().update(DELETE_EXPIRED_SQL, new Date());
        return getJdbcTemplate().query(SELECT_LOCK_INFO_SQL, rs -> {
            if (rs.next()) {
                return Optional.of(
                        new LockInfo(rs.getString("token"), rs.getTimestamp("expire_at")));
            } else {
                return Optional.empty();
            }
        }, lockKey);
    }

    @Override
    public Optional<String> getToken(String lockKey) {
        // lazy delete expired lock
        getJdbcTemplate().update(DELETE_EXPIRED_SQL, new Date());
        return getJdbcTemplate().query(SELECT_TOKEN_SQL,
                rs -> rs.next() ? Optional.ofNullable(rs.getString("token")) : Optional.empty(),
                lockKey);
    }

    private void checkTimeoutSeconds(long timeoutSeconds) {
        if (timeoutSeconds < 1) {
            throw new IllegalArgumentException("timeoutSeconds must be greater than 0");
        }
    }
}
