package io.terminus.trantor2.meta.management.service.editop;

import com.google.auto.service.AutoService;
import com.google.common.base.CharMatcher;
import io.terminus.trantor2.meta.api.dto.EditOpType;
import io.terminus.trantor2.meta.editor.model.MetaObject;
import io.terminus.trantor2.meta.editor.util.IndexUtil;
import io.terminus.trantor2.meta.editor.util.ObjectUtil;
import io.terminus.trantor2.meta.exception.MetaException;
import io.terminus.trantor2.meta.object.MetaObjectV2;
import lombok.NoArgsConstructor;

@AutoService(EditOp.class)
@NoArgsConstructor
public class CreateNodeOp extends BaseEditOp {
    public CreateNodeOp(EditOpParams params) {
        super(params);
    }

    @Override
    protected void execOp() {
        // It is lock-free to create new node, 'cause we've already locked the entire branch.
        loadCurNode();
        //loadParentKey();
        loadMoveTargetAndNewParent();
        parentKey = newParentKey;
        curNode.setParentKey(parentKey);
        loadParent();

        //lock parent
        lockNodes.add(parent.getKey());
        lockNodes();

        if (!CharMatcher.ascii().matchesAllOf(request.getCurrentKey())) {
            throw new MetaException("key must be ascii");
        }

        // TODO: validate type

        // make sure key not exist
        validateKeyNotExist(request.getCurrentKey());

        MetaObject node = ObjectUtil.newMetaObject(curNode, curNode.getProps());
        MetaObjectV2 obj = ObjectUtil.hash(node);
        cur = IndexUtil.newBlob(obj, node);
        idxTasks.add(cur);
        applyDataChanges();

        // refresh index for current node
        refreshIndex(cur.getKey());
    }

    @Override
    public EditOpType getEditOpType() {
        return EditOpType.CreateNode;
    }
}
