package io.terminus.trantor2.ide.migration._24_0228;

import com.google.auto.service.AutoService;
import io.terminus.trantor2.ide.migration.MetaMigrate;
import io.terminus.trantor2.ide.migration.VersionedMetaMigrate;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2.5.24.0228
 */
@AutoService(MetaMigrate.class)
public class Migrate_24_0228 extends VersionedMetaMigrate {
    @Override
    protected void register(List<MetaMigrate> migrates) {
        migrates.add(new FrontendConfigModuleAppendTService());
    }
}
