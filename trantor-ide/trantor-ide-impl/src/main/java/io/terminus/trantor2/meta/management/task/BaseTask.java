package io.terminus.trantor2.meta.management.task;

import io.terminus.trantor2.meta.api.service.MetaEditService;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.object.MetaObjectRepo;
import io.terminus.trantor2.task.TaskOutput;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public abstract class BaseTask<O extends BaseTask.Options> implements Task<O> {
    @Data
    public static abstract class Options implements Task.Options {
    }

    @Autowired
    protected MetaQueryService metaQueryService;
    @Autowired
    protected MetaEditService metaEditService;
    @Autowired
    protected MetaObjectRepo metaObjectRepo;

    @Override
    public String showOpts(O opts) {
        return "";
    }

    @Override
    public void preCheck(O opts, TaskContext ctx) {
    }

    @Override
    public void exec(O opts, TaskOutput output, TaskContext ctx) {
        exec(opts, ctx.getSubTasks(), output.getLogs(), ctx);
    }

    /**
     * @deprecated use {@link #exec(BaseTask.Options, TaskOutput, TaskContext)} instead
     */
    @Deprecated
    public void exec(O opts, List<TaskDefine> subTasks, List<String> logs, TaskContext ctx) {
    }
}
