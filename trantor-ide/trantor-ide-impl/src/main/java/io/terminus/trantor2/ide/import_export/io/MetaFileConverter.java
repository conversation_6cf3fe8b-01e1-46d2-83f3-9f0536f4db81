package io.terminus.trantor2.ide.import_export.io;

import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.Data;

import java.io.File;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 **/
public interface MetaFileConverter {
    /**
     * 将元数据文件读入内存
     * @param file 元数据文件
     * @return
     */
    MetaFile read(File file);

    /**
     * 将元数据写入文件
     * @param meta 元数据对象
     * @param fileName 元数据文件名
     * @param path 路径
     * @param out zip output stream
     */
    void write(ObjectNode meta, String fileName, String path, ZipOutputStream out);

    @Data
    class MetaFile {
        private String name;
        private ObjectNode MetaContent;
    }
}
