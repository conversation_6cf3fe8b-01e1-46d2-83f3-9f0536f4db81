package io.terminus.trantor2.meta.management.service.editop;

import com.google.auto.service.AutoService;
import com.google.common.collect.Lists;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.meta.api.dto.EditOpType;
import io.terminus.trantor2.meta.editor.service.ObjectTraversal;
import io.terminus.trantor2.meta.exception.MetaException;
import io.terminus.trantor2.meta.exception.MetaNotFoundException;
import io.terminus.trantor2.meta.util.KeyUtil;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@AutoService(EditOp.class)
@NoArgsConstructor
public class ResetFullOp extends BaseTreeEditOp {
    public ResetFullOp(EditOpParams params) {
        super(params);
    }

    @Override
    protected void execOp() {
        String rootOid = request.getResetRootOid();
        List<String> skipModules = Optional.ofNullable(request.getSkipModules()).orElse(Lists.newArrayList());

        skipModules.forEach(skipModule -> {
            if (StringUtils.hasText(skipModule) && skipModule.contains(KeyUtil.MODULE_SEPARATOR)) {
                throw new TrantorRuntimeException("skipModule must be module key, param error: " + skipModule);
            }
        });
        if (!StringUtils.hasText(rootOid)) {
            throw new MetaException("resetRootOid is empty");
        }

        // lock all meta
        List<String> allExistKeys = lockIndexWithTeamId();

        ObjectTraversal traversal = new ObjectTraversal(metaObjectRepo, true);
        Map<String, String> k2oid = new HashMap<>();
        Map<String, String> parentMap = new HashMap<>();
        // at this time, order is matter, 'cause we need create module first,
        // so here newKeys is List, not Set
        List<String> skipKeys = new ArrayList<>(skipModules);
        traversal.breadthFirst(teamId, rootOid, (oid, obj, parentKey) -> {
            if (obj == null) {
                throw new MetaNotFoundException("object empty: " + oid);
            }
            if (!Objects.equals(obj.getParentKey(), parentKey)) {
                // skip bad data
                return;
            }
            if (k2oid.containsKey(obj.getKey())) {
                log.warn("duplicate key: {}", obj.getKey());
                throw new MetaException("duplicate key: " + obj.getKey());
            }
            if (skipKeys.contains(obj.getKey())) {
                return;
            }
            if (skipKeys.contains(parentKey)) {
                skipKeys.add(obj.getKey());
                return;
            }
            k2oid.put(obj.getKey(), oid);
            parentMap.put(obj.getKey(), parentKey);
        }, true);

        Map<String, List<String>> metaGroup = allExistKeys.stream().collect(Collectors.groupingBy(KeyUtil::moduleKey));
        List<String> existKeys = new ArrayList<>();
        skipModules.forEach(metaGroup::remove);
        metaGroup.forEach((key, value) -> existKeys.addAll(value));

        Set<String> relatedKeys = new HashSet<>(k2oid.keySet());

        existKeys.forEach(existKey -> {
            if (!k2oid.containsKey(existKey)) {
                pushDelete(existKey);
                relatedKeys.add(existKey);
                return;
            }
        });
        // make sure all queued delete items are deleted
        batchDelete();

        // TODO: keys not in existKeys but in allExistKeys should throw exception

        int totalKeys = k2oid.size();
        AtomicInteger processedKeys = new AtomicInteger(0);

        k2oid.keySet().forEach(newKey -> {
            String parentKey = parentMap.get(newKey);
            String oid = k2oid.get(newKey);
            pushSave(oid, parentKey);
            log.info("Processed {}/{} keys", processedKeys.incrementAndGet(), totalKeys);
        });
        // make sure all queued save items are saved
        batchSave();

        // refresh index
        refreshIndex(relatedKeys);
    }

    @Override
    public EditOpType getEditOpType() {
        return EditOpType.ResetFull;
    }
}
