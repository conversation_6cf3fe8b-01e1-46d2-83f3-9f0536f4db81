package io.terminus.trantor2.ide.import_export.pipe;

import cn.hutool.core.io.FileUtil;
import com.fasterxml.jackson.databind.JsonNode;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.ide.import_export.MetaReadContext;
import io.terminus.trantor2.ide.import_export.MetaWriteContext;
import io.terminus.trantor2.ide.import_export.io.FileConverter;
import io.terminus.trantor2.meta.api.dto.EditOpRequest;
import io.terminus.trantor2.meta.api.dto.MetaChangeOperation;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.service.MetaEditService;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;

import java.io.File;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 **/
public class VersionPipe implements MetaPipe {
    private static final String VERSION_JSON = "version";
    private static VersionPipe instance;

    private final FileConverter converter;
    private final MetaQueryService queryService;
    private final MetaEditService editService;

    public VersionPipe(MetaQueryService queryService, MetaEditService editService) {
        this.queryService = queryService;
        this.editService = editService;
        this.converter = new FileConverter();
    }

    public static VersionPipe instance(MetaQueryService queryService, MetaEditService editService) {
        if (instance == null) {
            instance = new VersionPipe(queryService, editService);
        }
        return instance;
    }

    @Override
    public void read(File file, MetaReadContext ctx) {
        if (!ctx.isNativeModule()) {
            return;
        }
        byte[] bytes = FileUtil.readBytes(file);
        ctx.setVersion(new String(bytes));
        MetaEditAndQueryContext metaEditAndQueryContext = EditUtil.newCtx(ctx.getTeamId(), TrantorContext.getCurrentUserId());
        MetaTreeNode module = queryService.findByKey(metaEditAndQueryContext, ctx.getTargetModule()).orElseThrow(() -> new TrantorRuntimeException("模块不存在"));
        if (module.getProps() == null) {
            module.setProps(ObjectJsonUtil.MAPPER.createObjectNode());
        }
        module.getProps().put("version", ctx.getVersion());
        EditOpRequest editOpRequest = EditUtil.updateNodeOp(module);
        editOpRequest.setChangeOperation(MetaChangeOperation.ModuleImport);
        editService.submitOp(metaEditAndQueryContext, editOpRequest);
    }

    @Override
    public void write(MetaTreeNode meta, MetaWriteContext ctx, ZipOutputStream zos) {
        String version = "1.0.0";
        if (meta.getProps() != null) {
            JsonNode jsonNode = meta.getProps().get("version");
            if (jsonNode != null && jsonNode.isTextual()) {
                version = jsonNode.asText();
            }
        }
        converter.write(version, VERSION_JSON, meta.getKey() + "/", zos);
    }
}
