package io.terminus.trantor2.meta.management.platform;

import io.terminus.trantor2.meta.platform.PlatformConfigHolder;
import io.terminus.trantor2.properties.TrantorConsoleProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/trantor/platform")
public class PlatformController {

    @Autowired
    private TrantorConsoleProperties trantorConsoleProperties;

    @GetMapping
    public PlatformInfo getPlatformInfo() {
        PlatformInfo platformInfo = new PlatformInfo();
        platformInfo.setVersion(PlatformConfigHolder.getPlatformVersion());
        platformInfo.setIamDomain(trantorConsoleProperties.getIamDomain());
        platformInfo.setDomain(trantorConsoleProperties.getDomain());
        return platformInfo;
    }
}
