package io.terminus.trantor2.meta.management.service.editop;

import io.terminus.trantor2.meta.api.dto.EditOpRequest;
import io.terminus.trantor2.meta.api.dto.EditOpType;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.blob.MetaBlobRepo;
import io.terminus.trantor2.meta.editor.repository.TagRepository;
import io.terminus.trantor2.meta.event.EventPublisher;
import io.terminus.trantor2.meta.exception.MetaException;
import io.terminus.trantor2.meta.history.MetaHistoryRepo;
import io.terminus.trantor2.meta.index.MetaIndexRefRepo;
import io.terminus.trantor2.meta.session.MetaEditSessionRepo;
import io.terminus.trantor2.meta.index.MetaIndexer;
import io.terminus.trantor2.meta.manager.MetaManager;
import io.terminus.trantor2.meta.object.MetaObjectRepo;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Constructor;
import java.util.Map;
import java.util.ServiceLoader;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Slf4j
public class EditOpFactory {
    private final EventPublisher publisher;
    @Getter
    private final MetaManager metaManager;
    private final MetaBlobRepo indexRepo;
    private final MetaIndexRefRepo linkRepo;
    private final MetaIndexer metaIndexer;
    private final MetaObjectRepo metaObjectRepo;

    private final MetaHistoryRepo metaHistoryRepo;
    private final MetaEditSessionRepo metaEditSessionRepo;
    private final TagRepository tagRepository;
    private final Map<EditOpType, Class<? extends EditOp>> opTypeClassMap =
        StreamSupport.stream(ServiceLoader.load(EditOp.class).spliterator(), false)
            .peek(it -> BaseEditOp.checkConstructor(it.getClass()))
            .collect(Collectors.toMap(EditOp::getEditOpType, EditOp::getClass));

    public EditOpFactory(EventPublisher publisher, MetaManager metaManager,
                         MetaBlobRepo indexRepo, MetaIndexRefRepo linkRepo, MetaIndexer metaIndexer, MetaObjectRepo metaObjectRepo,
                         MetaHistoryRepo metaHistoryRepo, MetaEditSessionRepo metaEditSessionRepo, TagRepository tagRepository) {
        this.publisher = publisher;
        this.metaManager = metaManager;
        this.indexRepo = indexRepo;
        this.linkRepo = linkRepo;
        this.metaIndexer = metaIndexer;
        this.metaObjectRepo = metaObjectRepo;
        this.metaHistoryRepo = metaHistoryRepo;
        this.metaEditSessionRepo = metaEditSessionRepo;
        this.tagRepository = tagRepository;
    }

    public EditOp getAction(MetaEditAndQueryContext ctx, EditOpRequest req) {
        EditOpType type = req.getOpType();
        if (type == null) {
            throw new MetaException("unknown EditOpType");
        }
        if (opTypeClassMap.containsKey(type)) {
            return createOp(opTypeClassMap.get(type), ctx, req);
        } else {
            throw new MetaException("unsupported EditOpType: " + type);
        }
    }

    @SuppressWarnings("unchecked")
    private <T extends BaseEditOp> T createOp(Class<? extends EditOp> opClass, MetaEditAndQueryContext ctx, EditOpRequest req) {
        try {
            Constructor<?> constructor = opClass.getConstructor(EditOpParams.class);
            T op = (T) constructor.newInstance(
                new EditOpParams(metaManager, indexRepo, linkRepo, metaIndexer, metaObjectRepo, metaHistoryRepo, metaEditSessionRepo, tagRepository, ctx, req)
            );
            return (T) op.setEventPublisher(publisher);
        } catch (Exception e) {
            log.error("Fail to create operation for class [{}]: {}", opClass.getName(), e.getMessage(), e);
            throw new MetaException("Fail to create operation");
        }
    }
}
