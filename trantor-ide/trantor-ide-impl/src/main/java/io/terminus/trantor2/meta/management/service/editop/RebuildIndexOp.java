package io.terminus.trantor2.meta.management.service.editop;

import com.google.auto.service.AutoService;
import io.terminus.trantor2.meta.api.dto.EditOpType;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@AutoService(EditOp.class)
@NoArgsConstructor
public class RebuildIndexOp extends BaseTreeEditOp {

    public RebuildIndexOp(EditOpParams params) {
        super(params);
    }

    @Override
    protected void execOp() {
        List<String> allExistKeys = lockIndexWithTeamId();
        metaIndexer.rebuild(ctx, allExistKeys);
    }

    @Override
    public EditOpType getEditOpType() {
        return EditOpType.RebuildIndex;
    }
}
