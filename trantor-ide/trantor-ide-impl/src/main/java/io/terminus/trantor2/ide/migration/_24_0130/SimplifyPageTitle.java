package io.terminus.trantor2.ide.migration._24_0130;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.auto.service.AutoService;
import com.google.common.collect.ImmutableSet;
import io.terminus.trantor2.ide.migration.Hit;
import io.terminus.trantor2.ide.migration.MetaMigrate;
import io.terminus.trantor2.ide.migration.SimpleViewCompTraversal;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2.5.24.0130
 */
@AutoService(MetaMigrate.class)
public class SimplifyPageTitle extends SimpleViewCompTraversal {
    @Override
    protected void handleComps(ObjectNode rawProps, Map<String, Comp> comps, List<Hit> hits) {
        comps.forEach((k, v) -> {
            if (!"PageTitle".equals(v.getName())) {
                return;
            }
            boolean updated = false;
            if (v.getRaw().has("type") && "Meta".equals(v.getRaw().get("type").asText())) {
                // already good
            } else {
                v.getRaw().put("type", "Meta");
                updated = true;
            }
            ObjectNode props = v.getRaw().with("props");
            List<ExtractedTitle> guessTitles = new ArrayList<>();
            if (props.has("title$")) {
                String title = props.get("title$").asText();
                if (StringUtils.hasText(title)) {
                    guessTitles.add(new ExtractedTitle(true, title, ""));
                }
                props.remove("title$");
                updated = true;
            }
            if (props.has("title")) {
                String title = props.get("title").asText();
                if (StringUtils.hasText(title)) {
                    guessTitles.add(new ExtractedTitle(false, title, ""));
                }
                // no need to remove title, because it will be overwritten by guessTitles
            }
            if (v.getRaw().has("children")) {
                ArrayNode children = (ArrayNode) v.getRaw().get("children");
                ArrayNode newChildren = v.getRaw().arrayNode();
                List<ExtractedTitle> extractedTitles = new ArrayList<>();
                for (int i = 0; i < children.size(); i++) {
                    JsonNode child = children.get(i);
                    if (shouldRemove(child)) {
                        simpleParse(child).ifPresent(extractedTitles::add);
                    } else {
                        newChildren.add(child);
                    }
                }
                buildTitle(extractedTitles).ifPresent(guessTitles::add);
                v.getRaw().set("children", newChildren);
                updated = true;
            }

            if (!guessTitles.isEmpty()) {
                ExtractedTitle finalTitle = guessTitles.get(0);
                String title = wrap(finalTitle.getValue(), finalTitle.isExpr());
                if (props.has("title") && title.equals(props.get("title").asText())) {
                    // already good, prevent update
                } else {
                    props.put("title", title);
                    props.put("useExpression", finalTitle.isExpr());
                    updated = true;
                }
            }

            hits.add(Hit.of(this.getClass(), k, "simplify PageTitle", updated));
        });
    }

    private boolean shouldRemove(JsonNode node) {
        if (!node.has("name")) {
            // unknown case
            return false;
        }
        String name = node.get("name").asText();
        return ImmutableSet.of("Text", "Show", "Observable").contains(name);
    }

    private Optional<ExtractedTitle> simpleParse(JsonNode child) {
        return Optional.ofNullable(
            simpleParseText(child, "").orElseGet(() ->
                simpleParseShow(child).orElseGet(() ->
                    simpleParseObservable(child).orElse(null))));
    }

    private Optional<ExtractedTitle> simpleParseObservable(JsonNode node) {
        return tryGetProps("Observable", node).flatMap(props -> {
            if (node.has("children")) {
                JsonNode children = node.get("children");
                for (int i = 0; i < children.size(); i++) {
                    JsonNode child = children.get(i);
                    Optional<ExtractedTitle> text = simpleParseText(child, "");
                    if (text.isPresent()) {
                        return text;
                    }
                }
            }
            return Optional.empty();
        });
    }

    private Optional<ExtractedTitle> simpleParseShow(JsonNode node) {
        return tryGetProps("Show", node).flatMap(props -> {
            String condition = "";
            if (props.has("value$")) {
                condition = props.get("value$").asText();
            }
            if (node.has("children")) {
                JsonNode children = node.get("children");
                for (int i = 0; i < children.size(); i++) {
                    JsonNode child = children.get(i);
                    Optional<ExtractedTitle> text = simpleParseText(child, condition);
                    if (text.isPresent()) {
                        return text;
                    }
                }
            }
            return Optional.empty();
        });
    }

    private Optional<ExtractedTitle> simpleParseText(JsonNode node, String condition) {
        return tryGetProps("Text", node).map(props -> {
            if (props.has("value$")) {
                String value = props.get("value$").asText();
                if (StringUtils.hasText(value)) {
                    return new ExtractedTitle(true, value, condition);
                }
            }
            if (props.has("value")) {
                String value = props.get("value").asText();
                if (StringUtils.hasText(value)) {
                    return new ExtractedTitle(false, value, condition);
                }
            }
            return null;
        });
    }

    private Optional<JsonNode> tryGetProps(String requiredName, JsonNode node) {
        if (node.has("name") && requiredName.equals(node.get("name").asText())) {
            return Optional.ofNullable(node.get("props"));
        }
        return Optional.empty();
    }

    private Optional<ExtractedTitle> buildTitle(List<ExtractedTitle> extractedTitles) {
        if (extractedTitles.size() == 1) {
            return Optional.of(extractedTitles.get(0));
        } else if (extractedTitles.size() == 2) {
            ExtractedTitle l = extractedTitles.get(0);
            ExtractedTitle r = extractedTitles.get(1);
            if (StringUtils.hasText(l.getCondition()) &&
                StringUtils.hasText(r.getCondition())) {
                String raw = l.getCondition() + " ? " + quote(l.getValue(), l.isExpr()) + " : " + quote(r.getValue(), r.isExpr());
                return Optional.of(new ExtractedTitle(true, raw, ""));
            }
        } else {
            // unknown case
        }
        return Optional.empty();
    }

    private String quote(String value, boolean isExpr) {
        if (isExpr) {
            return value;
        }
        return "\"" + value + "\"";
    }

    private String wrap(String value, boolean isExpr) {
        if (isExpr) {
            return "{{" + value + "}}";
        }
        return value;
    }

    @Data
    static class ExtractedTitle {
        private final boolean isExpr;
        private final String value;
        private final String condition;
    }
}
