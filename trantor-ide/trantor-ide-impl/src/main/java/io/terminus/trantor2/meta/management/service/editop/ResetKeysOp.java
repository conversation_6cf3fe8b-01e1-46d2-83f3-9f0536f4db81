package io.terminus.trantor2.meta.management.service.editop;

import com.google.auto.service.AutoService;
import io.terminus.trantor2.meta.api.dto.EditOpType;
import io.terminus.trantor2.meta.editor.service.ObjectTraversal;
import io.terminus.trantor2.meta.exception.MetaException;
import io.terminus.trantor2.meta.exception.MetaNotFoundException;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * Reset specific keys to their state in a given snapshot
 * This operation is used for checkpoint reverting in MetaEditSession
 *
 * <AUTHOR>
 */
@Slf4j
@AutoService(EditOp.class)
@NoArgsConstructor
public class ResetKeysOp extends BaseTreeEditOp {
    public ResetKeysOp(EditOpParams params) {
        super(params);
    }

    @Override
    protected void execOp() {
        String snapshotOid = request.getResetRootOid();
        List<String> resetKeys = request.getResetKeys();
        
        if (CollectionUtils.isEmpty(resetKeys)) {
            log.warn("resetKeys is empty");
            return;
        }
        
        if (!StringUtils.hasText(snapshotOid)) {
            throw new MetaException("snapshotOid (resetRootOid) is empty");
        }
        
        log.info("Starting ResetKeys operation for {} keys from snapshot {}", 
                resetKeys.size(), snapshotOid);
        
        // lock all meta
        List<String> allExistKeys = lockIndexWithTeamId();
        
        // Traverse the snapshot to find objects for the specified keys
        ObjectTraversal traversal = new ObjectTraversal(metaObjectRepo, true);
        Map<String, String> k2oid = new HashMap<>();
        Map<String, String> parentMap = new HashMap<>();
        Set<String> targetKeys = new HashSet<>(resetKeys);
        
        traversal.breadthFirst(teamId, snapshotOid, (oid, obj, parentKey) -> {
            if (obj == null) {
                throw new MetaNotFoundException("object empty: " + oid);
            }
            if (!Objects.equals(obj.getParentKey(), parentKey)) {
                // skip bad data
                return;
            }
            
            // Only process objects that are in our target keys
            if (targetKeys.contains(obj.getKey())) {
                if (k2oid.containsKey(obj.getKey())) {
                    log.warn("duplicate key in snapshot: {}", obj.getKey());
                    throw new MetaException("duplicate key in snapshot: " + obj.getKey());
                }
                k2oid.put(obj.getKey(), oid);
                parentMap.put(obj.getKey(), parentKey);
                log.debug("Found key {} with oid {} in snapshot", obj.getKey(), oid);
            }
        }, true);
        
        Set<String> relatedKeys = new HashSet<>();
        
        // Process each reset key
        for (String resetKey : resetKeys) {
            boolean existsInCurrent = allExistKeys.contains(resetKey);
            boolean existsInSnapshot = k2oid.containsKey(resetKey);
            
            if (!existsInCurrent && !existsInSnapshot) {
                // Key doesn't exist in either current state or snapshot, skip
                log.debug("Key {} doesn't exist in current state or snapshot, skipping", resetKey);
                continue;
            }
            
            if (existsInCurrent && !existsInSnapshot) {
                // Key exists in current but not in snapshot, delete it
                pushDelete(resetKey);
                relatedKeys.add(resetKey);
                log.info("Deleting key {} (exists in current but not in snapshot)", resetKey);
            } else if (existsInSnapshot) {
                // Key exists in snapshot, restore it
                String oid = k2oid.get(resetKey);
                String parentKey = parentMap.get(resetKey);
                pushSave(oid, parentKey);
                relatedKeys.add(resetKey);
                log.info("Restoring key {} to oid {} from snapshot", resetKey, oid);
            }
        }
        
        // Execute all queued deletes
        batchDelete();
        
        // Execute all queued saves
        batchSave();
        
        // Refresh index for all affected keys
        refreshIndex(relatedKeys);
        
        log.info("Successfully reset {} keys to snapshot state", relatedKeys.size());
    }

    @Override
    public EditOpType getEditOpType() {
        return EditOpType.ResetKeys;
    }
}