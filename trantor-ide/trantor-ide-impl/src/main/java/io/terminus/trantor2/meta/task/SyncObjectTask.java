package io.terminus.trantor2.meta.task;

import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.meta.api.dto.Manifest;
import io.terminus.trantor2.meta.editor.util.ObjectUtil;
import io.terminus.trantor2.meta.exception.MetaTaskException;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskDefine;
import io.terminus.trantor2.meta.util.ZipFileUtil;
import io.terminus.trantor2.meta.object.MetaObjectV2;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.module.service.OSSService;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import static io.terminus.trantor2.module.util.OSSConstant.CONSOLE_FILE_PREFIX;
import static io.terminus.trantor2.module.util.OSSConstant.PRIVATE_PREFIX;

@Slf4j
@Component
public final class SyncObjectTask extends BaseTask<SyncObjectTask.Options> {
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
        private String downloadUrl;
        private String sourceUrl;
        private Long sourceTeamId;
        private String rootOid;
        // Optional: if provided, use localPath instead of downloading
        private String localPath;
        // Whether to delete the zip file after import; default true
        private Boolean deleteWhenFinish = Boolean.TRUE;
    }

    @Autowired(required = false)
    private OSSService ossService;

    @Override
    public void preCheck(Options opts, TaskContext ctx) {
        super.preCheck(opts, ctx);
        // If a download URL is provided, skip source validation
        if (opts.getDownloadUrl() != null) {
            return;
        }
        // If a local path is provided, validate it exists and skip source validation
        if (opts.getLocalPath() != null) {
            java.nio.file.Path p = java.nio.file.Paths.get(opts.getLocalPath());
            if (!java.nio.file.Files.exists(p)) {
                throw new MetaTaskException("localPath not found: " + opts.getLocalPath());
            }
            return;
        }
        // Otherwise, require sourceUrl, sourceTeamId and rootOid for remote export
        if (opts.getSourceUrl() == null) {
            throw new MetaTaskException("sourceUrl is required");
        }
        String statusUrl = concatUrl(
            opts.getSourceUrl(),
            "/api/trantor/task/status"
        );
/*
        if (!isSourceOnline(statusUrl)) {
            throw new MetaTaskException("sourceUrl is not online");
        }
*/
        if (opts.getSourceTeamId() == null) {
            throw new MetaTaskException("sourceTeamId is required");
        }
        if (opts.getRootOid() == null) {
            throw new MetaTaskException("rootOid is required");
        }
    }

    @Override
    public void exec(Options opts, List<TaskDefine> subTasks, List<String> outputs, TaskContext ctx) {
        // step1: download from sourceUrl
        Path path;
        if (opts.getLocalPath() != null) {
            path = java.nio.file.Paths.get(opts.getLocalPath());
        } else {
            String downloadUrl = opts.getDownloadUrl();
            if (downloadUrl == null) {
                downloadUrl = concatUrl(
                    opts.getSourceUrl(),
                    String.format("/api/trantor/meta/ops/export/%d/%s", opts.getSourceTeamId(), opts.getRootOid())
                );
            }
            path = ZipFileUtil.downloadFromURL(downloadUrl);
        }

        // step2: import objects
        try {
            if (!ctx.isDryRun()) {
                importArtifact(path, ctx.getTeamId(), ctx.getUserId(), outputs);
            }
        } finally {
            if (!Boolean.FALSE.equals(opts.getDeleteWhenFinish()) && opts.getLocalPath() == null) {
                deleteTempFile(path);
            } else if (!Boolean.FALSE.equals(opts.getDeleteWhenFinish()) && opts.getLocalPath() != null) {
                // delete only if caller asked and provided localPath; typically caller passes true to cleanup
                deleteTempFile(path);
            }
        }
    }

    private boolean isSourceOnline(String statusUrl) throws MetaTaskException {
        HttpURLConnection connection = null;
        try {
            URL url = new URL(statusUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000); // Set connection timeout
            connection.setReadTimeout(5000);    // Set reading timeout

            int responseCode = connection.getResponseCode();
            return responseCode == HttpURLConnection.HTTP_OK;

        } catch (IOException e) {
            throw new MetaTaskException("Error checking source status at " + statusUrl + " " + e.getMessage());
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    private String concatUrl(String sourceUrl, String endpoint) {
        // Remove trailing slash if present
        if (sourceUrl.endsWith("/")) {
            sourceUrl = sourceUrl.substring(0, sourceUrl.length() - 1);
        }

        // Return the concatenated URL
        return sourceUrl + endpoint;
    }

    // After processing the file, you can delete it using:
    private void deleteTempFile(Path tempFile) throws MetaTaskException {
        try {
            Files.deleteIfExists(tempFile);
        } catch (IOException e) {
            throw new MetaTaskException("Failed to delete temporary file: " + tempFile + " " + e.getMessage());
        }
    }

    private Manifest getManifest(Path path) {
        try (ZipInputStream zis = new ZipInputStream(Files.newInputStream(path))) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                if ("manifest.json".equals(entry.getName())) {
                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    byte[] buffer = new byte[1024];
                    int len;
                    while ((len = zis.read(buffer)) > 0) {
                        baos.write(buffer, 0, len);
                    }
                    byte[] entireContent = baos.toByteArray();
                    String json = new String(entireContent, StandardCharsets.UTF_8);
                    return ObjectJsonUtil.MAPPER.readValue(json, Manifest.class);
                }
            }
            return null;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private void importArtifact(Path path, Long teamId, Long userId, List<String> outputs) {
        Manifest manifest = ObjectUtil.getArtifactManifest(path);
        if (manifest != null) {
            outputs.add("Manifest:");
            outputs.add(String.format("- version: %s", manifest.getVersion()));
            outputs.add(String.format("  snapshotOid: %s", manifest.getSnapshotOid()));
            outputs.add("");
            outputs.add("Library:");
            importLibrary(path, outputs);
            outputs.add("");
            outputs.add("Objects:");
        }
        ImportStatistic res = importObjects(path, teamId, userId, manifest);
        outputs.add(String.format("- total: %d", res.total));
        outputs.add(String.format("  alreadyExist: %d", res.alreadyExist));
        outputs.add(String.format("  newCreated: %d", res.newCreated));
        outputs.add(String.format("  badOid: %d", res.badOid));
    }

    private String extractOSSObjectNameFromFileName(String fileName) {
        if (!fileName.startsWith("library/")) {
            return null;
        }
        return fileName.substring("library/".length());
    }

    private String probeContentType(String fileName) {
        return URLConnection.guessContentTypeFromName(fileName);
    }

    private void importLibrary(Path path, List<String> outputs) {
        ByteArrayOutputStream baos = null;
        ByteArrayInputStream bais = null;
        try (ZipInputStream zis = new ZipInputStream(Files.newInputStream(path))) {
            ZipEntry entry = zis.getNextEntry();
            while (entry != null) {
                String fileName = entry.getName();
                String objectName = extractOSSObjectNameFromFileName(fileName);
                if (objectName == null) {
                    entry = zis.getNextEntry();
                    continue;
                }

                baos = new ByteArrayOutputStream();
                byte[] buffer = new byte[1024];
                int len;
                while ((len = zis.read(buffer)) > 0) {
                    baos.write(buffer, 0, len);
                }
                baos.flush();
                byte[] fileBytes = baos.toByteArray();

                // Check if the object size is 0B, skip migration if it is
                if (fileBytes.length == 0) {
                    outputs.add("- skipped: " + objectName + " (0B file)");
                } else {
                    bais = new ByteArrayInputStream(fileBytes);
                    boolean isPrivate = objectName.startsWith(CONSOLE_FILE_PREFIX + PRIVATE_PREFIX);
                    String contentType = probeContentType(objectName);
                    String uri = ossService.migrateFileIn(objectName, bais, contentType, isPrivate);
                    outputs.add("- imported: " + uri);
                    outputs.add("  contentType: " + contentType);
                }
                entry = zis.getNextEntry();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (baos != null) {
                    baos.close();
                }
                if (bais != null) {
                    bais.close();
                }
            } catch (Exception e) {
                throw new TrantorRuntimeException(e);
            }
        }
    }

    private ImportStatistic importObjects(Path path, Long teamId, Long userId, Manifest manifest) {
        ImportStatistic res = new ImportStatistic();
        List<MetaObjectV2> saveItems = new ArrayList<>();
        ObjectUtil.traverseObjects(path, manifest, (oid, obj) -> {
            saveItems.add(obj);
            if (saveItems.size() >= 1000) {
                doBatchSave(teamId, saveItems, res);
                saveItems.clear();
            }
        });
        doBatchSave(teamId, saveItems, res);
        return res;
    }

    private void doBatchSave(Long teamId, List<MetaObjectV2> saveItems, ImportStatistic res) {
        if (saveItems.isEmpty()) {
            return;
        }
        int total = saveItems.size();
        int created = metaObjectRepo.save(teamId, saveItems);
        int alreadyExist = total - created;
        res.total += total;
        res.newCreated += created;
        res.alreadyExist += alreadyExist;
    }

    static class ImportStatistic {
        private int total = 0;
        private int alreadyExist = 0;
        private int newCreated = 0;
        private int badOid = 0;
    }
}
