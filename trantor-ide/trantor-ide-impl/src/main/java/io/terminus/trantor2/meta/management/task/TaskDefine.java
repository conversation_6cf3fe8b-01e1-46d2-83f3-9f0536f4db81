package io.terminus.trantor2.meta.management.task;

import lombok.Getter;

@Getter
public class TaskDefine {
    private final Class<? extends Task<? extends Task.Options>> taskClass;
    private final Task.Options opts;
    private final String comment;
    private final Boolean fastFail;

    public TaskDefine(Class<? extends Task<? extends Task.Options>> taskClass, Task.Options opts, String comment, Boolean fastFail) {
        this.taskClass = taskClass;
        this.opts = opts;
        this.comment = comment;
        this.fastFail = fastFail;
    }
}
