package io.terminus.trantor2.meta.management;

import io.terminus.trantor2.meta.behaviors.RefreshCompPackJob;
import io.terminus.trantor2.meta.config.ConsoleEnabledCondition;
import io.terminus.trantor2.properties.TrantorConsoleProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;

@Configuration
@Conditional(ConsoleEnabledCondition.class)
public class BehaviorsConfig {

    @Bean
    public RefreshCompPackJob refreshCompPackJob(TrantorConsoleProperties trantorConsoleProperties) {
        if (!trantorConsoleProperties.getLatestJson().isAutoUpdate()) {
            return null;
        }
        return new RefreshCompPackJob(trantorConsoleProperties);
    }
}
