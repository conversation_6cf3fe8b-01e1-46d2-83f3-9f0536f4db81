package io.terminus.trantor2.meta.management.service.editop;

import com.google.auto.service.AutoService;
import io.terminus.trantor2.meta.api.dto.EditOpType;
import io.terminus.trantor2.meta.editor.util.IndexUtil;
import lombok.NoArgsConstructor;

@AutoService(EditOp.class)
@NoArgsConstructor
public class UpdateNodeOp extends BaseEditOp {
    public UpdateNodeOp(EditOpParams params) {
        super(params);
    }

    @Override
    protected void execOp() {
        loadCur();
        loadCurNode();
        loadParentKey();
        loadParent();

        //lock cur and parent
        lockNodes.add(cur.getKey());
        lockNodes.add(parent.getKey());
        lockNodes();

        checkParent();

        //get new cur
        loadCur();

        // validate that curNode only update name and props
        validateCurNodeOnlyUpdateNameAndPropsCompareWithExistCur();

        IndexUtil.updateBlob(cur, curNode);
        idxTasks.add(cur);
        applyDataChanges();

        // refresh index for current node
        refreshIndex(cur.getKey());
    }

    @Override
    public EditOpType getEditOpType() {
        return EditOpType.UpdateNode;
    }
}
