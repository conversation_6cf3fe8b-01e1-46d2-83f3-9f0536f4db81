package io.terminus.trantor2.meta.management.service.editop;

import cn.hutool.core.util.BooleanUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.CharMatcher;
import io.terminus.trantor2.meta.api.dto.ModuleInfo;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.blob.MetaBlob;
import io.terminus.trantor2.meta.blob.MetaBlobImpl;
import io.terminus.trantor2.meta.context.MetaContext;
import io.terminus.trantor2.meta.editor.model.MetaObject;
import io.terminus.trantor2.meta.editor.util.IndexUtil;
import io.terminus.trantor2.meta.editor.util.ObjectUtil;
import io.terminus.trantor2.meta.event.ModuleCreateEvent;
import io.terminus.trantor2.meta.event.ModuleDeleteEvent;
import io.terminus.trantor2.meta.exception.MetaException;
import io.terminus.trantor2.meta.object.MetaObjectV2;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.meta.util.ZipUtil;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.meta.ModuleType;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.*;
import java.util.stream.Collectors;

@NoArgsConstructor(access = AccessLevel.PROTECTED)
public abstract class BaseTreeEditOp extends BaseEditOp {

    public BaseTreeEditOp(EditOpParams params) {
        super(params);
    }

    protected final List<String> deleteItems = new ArrayList<>();
    protected final List<MetaSaveItem> saveItems = new ArrayList<>();
    protected final Map<String, List<MetaTreeNode>> childrenAdj = new HashMap<>();

    protected void loadChildrenAdj() {
        if (request.getChangedTreeNodes() == null) {
            return;
        }
        request.getChangedTreeNodes().forEach(v -> {
            List<MetaTreeNode> c = childrenAdj.computeIfAbsent(v.getParentKey(), it -> new ArrayList<>());
            c.add(v);
        });
    }

    protected void buildTree(MetaTreeNode newNode, MetaTreeNode parent) {
        List<MetaTreeNode> children = childrenAdj.get(newNode.getKey());
        if (children != null && !children.isEmpty()) {
            for (MetaTreeNode child : children) {
                buildTree(child, newNode);
            }
        }

        MetaObject node = ObjectUtil.newMetaObject(newNode, newNode.getProps());
        MetaObjectV2 obj = ObjectUtil.hash(node);
        MetaBlobImpl newIdx = IndexUtil.newBlob(obj, node);
        idxTasks.add(newIdx);
        // UpdateTree has already deleted the old link, CreateTree does not have an old link
        // so here we are passing an emptyList
        //buildLinks(Collections.emptyList(), analyzeLinks(newNode, parent));
    }

    protected void pushDelete(String key) {
        deleteItems.add(key);
        if (deleteItems.size() >= 1000) {
            batchDelete();
        }
    }

    protected void batchDelete() {
        tryDelete(deleteItems);
        deleteItems.clear();
    }

    private void tryDelete(List<String> keys) {
        if (keys.isEmpty()) {
            return;
        }
        List<MetaBlobImpl> exists = metaBlobRepo.find(teamId, MetaBlob.Full.class, keys)
                        .stream().map(it -> (MetaBlobImpl) it).collect(Collectors.toList());
        deleteMeta(exists);
        exists.forEach(exist -> {
            if (Objects.equals(exist.getType(), MetaType.Module.name())) {
                MetaObject node = ZipUtil.unzip(exist.getObject(), MetaObject.class);
                publishModuleDeleteEvent(node);
            }
        });
    }

    protected void pushSave(String oid, String parentKey) {
        saveItems.add(new MetaSaveItem(oid, parentKey));
        if (saveItems.size() >= 1000) {
            batchSave();
        }
    }

    protected void batchSave() {
        trySave(saveItems);
        saveItems.clear();
    }

    private void trySave(List<MetaSaveItem> saveItems) {
        if (saveItems.isEmpty()) {
            return;
        }
        List<MetaObject> shouldList = buildFromObject(saveItems);
        // only ascii
        shouldList = shouldList.stream()
                .filter(it -> CharMatcher.ascii().matchesAllOf(it.getKey()))
                .collect(Collectors.toList());
        Map<String, MetaBlob.Base> existMap = metaBlobRepo.find(teamId, MetaBlob.Base.class, shouldList.stream().map(MetaObject::getKey).collect(Collectors.toList()))
                .stream()
                .map(it -> (MetaBlobImpl) it)
                .collect(Collectors.toMap(MetaBlobImpl::getKey, it -> it));

        List<MetaObject> toUpdates = new ArrayList<>();
        for (MetaObject should : shouldList) {
            MetaBlob.Base old = existMap.get(should.getKey());
            if (old == null) {
                // create
                toUpdates.add(should);
                if (Objects.equals(should.getType(), MetaType.Module.name())) {
                    publishModuleCreateEvent(should);
                }
            } else {
                // update
                toUpdates.add(should);

                if (Objects.equals(old.getType(), MetaType.Module.name())) {
                    Optional.ofNullable(moduleInfo(should)).ifPresent(moduleInfo -> {
                        if (moduleInfo.isNativeModule()) {
                            MetaContext.setNativeModule(moduleInfo);
                        } else {
                            MetaContext.setNonNativeModule(moduleInfo);
                        }
                    });
                }
            }
        }
        metaManager.save(toUpdates, teamId, userId, getChangeOperation(), getEditSessionContext());
    }

    private void publishModuleCreateEvent(MetaObject meta) {
        ModuleInfo moduleInfo = moduleInfo(meta);
        if (moduleInfo == null) {
            return;
        }
        ModuleCreateEvent event = new ModuleCreateEvent();
        event.setModule(moduleInfo(meta));
        publisher.publish(event);
    }

    private void publishModuleDeleteEvent(MetaObject meta) {
        ModuleDeleteEvent event = new ModuleDeleteEvent();
        event.setModule(moduleInfo(meta));
        publisher.publish(event);
    }

    private ModuleInfo moduleInfo(MetaObject meta) {
        if (!Objects.equals(meta.getType(), MetaType.Module.name())) {
            return null;
        }
        ModuleMeta.ModuleProps moduleProps;
        try {
            moduleProps = ObjectJsonUtil.MAPPER.treeToValue(meta.getProps(), ModuleMeta.ModuleProps.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("failed to parse module props", e);
        }
        Boolean nativeModule = Optional.ofNullable(moduleProps).map(ModuleMeta.ModuleProps::getNativeModule).orElse(null);
        boolean isPortal = Optional.ofNullable(moduleProps).map(ModuleMeta.ModuleProps::getType).filter(type -> Objects.equals(type, ModuleType.Portal)).isPresent();
        boolean enhance = false;
        if (BooleanUtil.isFalse(nativeModule)) {
            enhance = metaBlobRepo.findOne(teamId, MetaBlob.Key.class, KeyUtil.extKey(KeyUtil.moduleKey(meta.getKey()))).isPresent();
        }
        return ModuleInfo.of(teamCode, meta.getKey(), nativeModule, enhance, isPortal);
    }

    protected List<MetaObject> buildFromObject(List<MetaSaveItem> saveItems) {
        if (saveItems.isEmpty()) {
            return Collections.emptyList();
        }
        Set<String> oids = saveItems.stream()
                .map(MetaSaveItem::getOid)
                .collect(Collectors.toSet());
        Map<String, MetaObjectV2> objMap = metaObjectRepo.findByOids(teamId, oids).stream()
                .collect(Collectors.toMap(it -> it.getOidStr(), it -> it));
        return saveItems.stream()
                .map(it -> {
                    MetaObjectV2 objEnt = objMap.get(it.getOid());
                    if (objEnt == null) {
                        throw new MetaException("object not found: " + it.getOid());
                    }
                    return objEnt.getContentStruct();
                })
                .collect(Collectors.toList());
    }
}
