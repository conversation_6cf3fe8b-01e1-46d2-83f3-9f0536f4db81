package io.terminus.trantor2.meta.management.task;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.base.Throwables;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.TrantorBizException;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.meta.exception.MetaTaskException;
import io.terminus.trantor2.meta.management.dlock.DLock;
import io.terminus.trantor2.meta.management.event.TaskExecutedSuccessEvent;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.module.service.TeamService;
import io.terminus.trantor2.task.Progress;
import io.terminus.trantor2.task.TaskLocalContext;
import io.terminus.trantor2.task.TaskRun;
import io.terminus.trantor2.task.TaskRunStatus;
import jakarta.annotation.PreDestroy;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
public class TaskManager implements ApplicationContextAware, ApplicationEventPublisherAware {

    private final ExecutorService pool = Executors.newCachedThreadPool(r -> {
        Thread thread = new Thread(r);
        thread.setName("trantor-task-manager");
        return thread;
    });

    @PreDestroy
    private void destroy() {
        pool.shutdown();
    }

    private final TaskRunRepo taskRunRepo;

    private final DLock dLock;

    private final TeamService teamService;

    public TaskManager(TaskRunRepo taskRunRepo, DLock dLock, TeamService teamService) {
        this.taskRunRepo = taskRunRepo;
        this.dLock = dLock;
        this.teamService = teamService;
    }

    private ApplicationContext applicationContext;
    private ApplicationEventPublisher eventPublisher;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        initInternalTaskDesc();
    }

    @Override
    public void setApplicationEventPublisher(@NotNull ApplicationEventPublisher applicationEventPublisher) {
        this.eventPublisher = applicationEventPublisher;
    }

    @Getter
    private final Map<String, InternalTaskDesc> tasks = new HashMap<>();

    @Getter
    @AllArgsConstructor
    static final class InternalTaskDesc {
        private final TaskDesc taskDesc;
        private final Class<? extends Task> taskClass;
    }

    public List<TaskDesc> getSupportedTasks(Boolean onlyVisible) {
        return tasks.values().stream()
                .map(InternalTaskDesc::getTaskDesc)
                .filter(it -> {
                    if (onlyVisible) {
                        return it.getVisible();
                    }
                    return true;
                })
                .collect(Collectors.toList());
    }

    private void initInternalTaskDesc() {
        applicationContext.getBeansOfType(Task.class).forEach((k, v) -> {
            Class<? extends Task> taskClass = v.getClass();
            Class<? extends Task.Options> optClass = mustGetOptionClass(taskClass);
            String displayName = taskClass.getSimpleName();
            TaskService ann = taskClass.getAnnotation(TaskService.class);
            if (ann != null && org.springframework.util.StringUtils.hasText(ann.displayName())) {
                displayName = ann.displayName();
            }
            boolean visible = false;
            if (ann != null) {
                visible = ann.visible();
            }

            InternalTaskDesc internalTaskDesc = new InternalTaskDesc(
                    new TaskDesc(
                            taskClass.getSimpleName(),
                            taskClass.getSimpleName(),
                            displayName,
                            "",
                            visible,
                            buildOptionDesc(optClass)),
                    taskClass
            );
            tasks.put(taskClass.getSimpleName(), internalTaskDesc);
        });
    }

    private List<TaskDesc.TaskOptionDesc> buildOptionDesc(Class<? extends Task.Options> optClass) {
        return Arrays.stream(optClass.getDeclaredFields()).map(field -> new TaskDesc.TaskOptionDesc(
                field.getName(),
                field.getType().getSimpleName(),
                true,
                ""
        )).collect(Collectors.toList());
    }

    private Class<? extends Task.Options> mustGetOptionClass(Class<? extends Task> taskClass) {
        for (Class<?> clazz : taskClass.getDeclaredClasses()) {
            if (Task.Options.class.isAssignableFrom(clazz)) {
                return (Class<? extends Task.Options>) clazz;
            }
        }
        throw new MetaTaskException("task opt class not found: " + taskClass.getSimpleName());
    }

    public StartTaskResult startAsyncTask(String taskCode, Boolean dryRun, ObjectNode taskOpts) {
        InternalTaskDesc taskDesc = tasks.get(taskCode);
        if (taskDesc == null) {
            throw new MetaTaskException("task not found: " + taskCode);
        }
        Class<? extends Task<? extends Task.Options>> taskClass =
                (Class<? extends Task<? extends Task.Options>>) taskDesc.getTaskClass();
        Class<? extends Task.Options> optClass = mustGetOptionClass(taskClass);
        Task.Options opts;
        try {
            opts = ObjectJsonUtil.MAPPER.treeToValue(taskOpts, optClass);
        } catch (JsonProcessingException e) {
            throw new MetaTaskException("json error");
        }
        if (opts == null) {
            throw new MetaTaskException("task opts not found: " + taskCode);
        }
        String teamCode = TrantorContext.getTeamCode();
        Long teamId = TrantorContext.getTeamId();
        if (teamId == null) {
            teamId = teamService.getTeamIdByCode(teamCode);
        }
        TaskContext ctx = new TaskContext(teamId, teamCode, TrantorContext.getCurrentUserId(), dryRun);
        return startAsyncTask(new TaskDefine(taskClass, opts, "", false), ctx);
    }

    public StartTaskResult startAsyncTask(TaskDefine rootTaskDefine, TaskContext ctx) {
        validateTaskContext(ctx);
        initTrantorContext(ctx);
        // TODO: check not task exist
        TaskRunIdWithLock rootTaskWithLock = taskRunRepo.createRootTaskWithLock(rootTaskDefine, ctx);
        Long taskRunId = rootTaskWithLock.getTaskRunId();

        CompletableFuture<Void> klf = keepLock(rootTaskWithLock);
        CompletableFuture<Void> rtf = runTask(taskRunId, rootTaskDefine, ctx);

        CompletableFuture<Void> aof = CompletableFuture.anyOf(klf, rtf)
            .thenRunAsync(() -> {
                if (!klf.isDone()) {
                    klf.completeExceptionally(new CancellationException());
                }
                if (!rtf.isDone()) {
                    rtf.completeExceptionally(new CancellationException());
                }
            }, pool);

        return new StartTaskResult(taskRunId, aof);
    }

    public void startTask(TaskDefine def, TaskContext ctx) {
        StartTaskResult result = startAsyncTask(def, ctx);
        try {
            result.getFuture().get();
        } catch (Exception e) {
            log.error("task failed", e);
        }
    }

    private CompletableFuture<Void> runTask(Long taskRunId, TaskDefine rootTaskDefine, TaskContext ctx) throws MetaTaskException {
        return CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(500); // delay start
                initTrantorContext(ctx);
                boolean success = execTask(rootTaskDefine, taskRunId, ctx);
                if (success) {
                    log.info("task success: {}", taskRunId);
                } else {
                    log.error("task failed: {}", taskRunId);
                }
            } catch (Exception e) {
                log.error("task failed: {}", taskRunId, e);
            } finally {
                TrantorContext.clear();
                TaskLocalContext.clear();
            }
        }, pool);
    }

    private CompletableFuture<Void> keepLock(TaskRunIdWithLock rootTaskWithLock) {
        CompletableFuture<Void> keep = CompletableFuture.runAsync(() -> {
            while (true) {
                try {
                    Thread.sleep(30_000);
                } catch (InterruptedException e) {
                    // ignore
                }
                boolean renewal = dLock.renewal(rootTaskWithLock.getLockKey(), rootTaskWithLock.getToken(), 60);
                if (!renewal) {
                    throw new MetaTaskException("taskRun has bad lock: " + rootTaskWithLock.getTaskRunId());
                }
            }
        }, pool);
        keep.exceptionally(e -> {
            if (e instanceof CancellationException) {
                log.info("keep lock cancelled: {}", rootTaskWithLock.getTaskRunId());
                dLock.unLock(rootTaskWithLock.getLockKey(), rootTaskWithLock.getToken());
            } else {
                log.error("keep lock failed", e);
            }
            return null;
        });
        return keep;
    }

    private boolean execTask(TaskDefine def, Long runId, TaskContext ctx) throws MetaTaskException {
        TaskRun run = new TaskRun(runId, TaskRunStatus.Running, new Progress(1, 0, 0));
        boolean result = false;
        // TaskLocalContext will be cleared in runTask method
        TaskLocalContext.init(runId, def.getTaskClass().getSimpleName(), run);

        // TODO: 改成手动压栈，共用一个 thread
        ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
        Runnable periodicUpdate = () -> taskRunRepo.updateTaskRun(run);
        // update taskRun every 60s
        ScheduledFuture<?> future = scheduler.scheduleAtFixedRate(periodicUpdate, 30, 60, TimeUnit.SECONDS);

        try {
            // quick update: Pending -> Running
            taskRunRepo.updateTaskRun(run);
            taskRunRepo.setStart(runId);

            // do exec
            _execTask(def, run, ctx);

            // check result
            if (run.getProgress().succeed() && run.getOutput().getResult().getSummary().getFailed() == 0) {
                run.setStatus(TaskRunStatus.Success);
                result = true;
                eventPublisher.publishEvent(new TaskExecutedSuccessEvent(def.getTaskClass(), def.getOpts()));
            } else {
                run.setStatus(TaskRunStatus.Error);
            }
        } catch (Exception e) {
            log.error("task id {} execute failed. error: {}", run.getTaskRunId(), Throwables.getStackTraceAsString(e));
            run.getOutput().log(e.getMessage());
            run.setStatus(TaskRunStatus.Panic);
        } finally {
            future.cancel(true);
            scheduler.shutdown();
            taskRunRepo.updateTaskRun(run);
            taskRunRepo.setEnd(runId);
        }
        return result;
    }

    private void _execTask(TaskDefine def, TaskRun run, TaskContext ctx) throws MetaTaskException {
        Task task = applicationContext.getBean(def.getTaskClass());
        Task.Options opts = def.getOpts();

        if (task == null || opts == null) {
            run.getProgress().failedIncrease();
            throw new MetaTaskException("task or opts is null");
        }

        // list to collect subTasks
        if (ctx.getSubTasks() == null) {
            ctx.setSubTasks(new ArrayList<>());
        }

        try {
            TaskService ann = def.getTaskClass().getAnnotation(TaskService.class);
            String runDisplayName = def.getTaskClass().getSimpleName();
            if (ann != null && StringUtils.isNotBlank(ann.displayName())) {
                runDisplayName = ann.displayName();
            }
            runDisplayName += task.showOpts(opts);
            run.setRunDisplayName(runDisplayName);
            taskRunRepo.setRunDisplayName(run.getTaskRunId(), runDisplayName);

            // 1. handle pre-check, if failed, throw exception
            task.preCheck(opts, ctx);
            // 2. handle this task
            task.exec(opts, run.getOutput(), ctx);

            // increase total before increase success, to prevent resulting 1/1 when exception occurred
            run.getProgress().setTotal(run.getProgress().getTotal() + ctx.getSubTasks().size());
            run.getProgress().successIncrease();
        } catch (Exception e) {
            run.getProgress().failedIncrease();
            // compatible with old task that use exception to exit
            String msg = e.getMessage();
            if (msg == null) {
                msg = e.getClass().getSimpleName();
            }
            if (e instanceof TrantorBizException) {
                TrantorBizException bizE = (TrantorBizException) e;
                msg = String.format(bizE.getErrorType().getMessage(), bizE.getArgs());
            }
            // column length limit
            if (StringUtils.length(msg) > 5000) {
                msg = msg.substring(0, 5000);
            }
            run.setPanicMessage(msg);
            throw e;
        }

        // quick update for changed progress.total
        taskRunRepo.updateTaskRun(run);

        // 3. handle subTasks
        for (int i = 0; i < ctx.getSubTasks().size(); i++) {
            TaskDefine subTaskDef = ctx.getSubTasks().get(i);
            try {
                Integer step = i + 1; // step start from 1 (because 0 is `exec` of this task)
                Long subTaskRunId = taskRunRepo.createSubTask(subTaskDef, run.getTaskRunId(), step, ctx);
                TaskContext subTaskCtx = ctx.inherit(def);
                boolean success = execTask(subTaskDef, subTaskRunId, subTaskCtx);
                if (!success) {
                    throw new MetaTaskException("go fail");
                }
                run.getProgress().successIncrease();
            } catch (Exception e) {
                run.getProgress().failedIncrease();
                if (subTaskDef.getFastFail()) {
                    // prevent exception and duplicated output record
                    return;
                }
            } finally {
                // sub-tasks may exec slow, do quick update progress
                taskRunRepo.updateTaskRun(run);
            }
        }
    }

    private void validateTaskContext(TaskContext ctx) {
        if (ctx == null) {
            throw new MetaTaskException("taskContext is null");
        }
        if (ctx.getTeamId() == null) {
            throw new MetaTaskException("teamId is null");
        }
        if (ctx.getTeamCode() == null) {
            throw new MetaTaskException("teamCode is null");
        }
        if (ctx.getUserId() == null) {
            throw new MetaTaskException("userId is null");
        }
    }

    private void initTrantorContext(TaskContext ctx) {
        if (TrantorContext.getContext() == null) {
            TrantorContext.init();
        }
        User user = new User();
        user.setId(ctx.getUserId());
        TrantorContext.setCurrentUser(user);
        TrantorContext.setTeamId(ctx.getTeamId());
        TrantorContext.setTeamCode(ctx.getTeamCode());
    }

    @EventListener(value = ApplicationReadyEvent.class, condition = "@springApplicationProperties.name == 'trantor2'")
    @Async
    protected void fixBadRunningWithoutLock() {
        try {
            Thread.sleep(1000 * 65);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        log.info("TaskRun:: fix bad running start");
        User user = new User();
        user.setId(1L);
        TrantorContext.init();
        TrantorContext.setCurrentUser(user);
        List<Long> orphanRootTaskRunIds = taskRunRepo.setFailedForRunningRootTasksWithoutLock();
        for (Long taskRunId : orphanRootTaskRunIds) {
            log.info("TaskRun:: fix bad running: {}", taskRunId);
        }
        log.info("TaskRun:: fix bad running end, total: {}", orphanRootTaskRunIds.size());
        TrantorContext.clear();
    }
}
