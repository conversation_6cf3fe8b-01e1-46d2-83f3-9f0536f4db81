package io.terminus.trantor2.meta.management.platform;

import io.terminus.trantor2.common.VersionConfig;
import io.terminus.trantor2.common.utils.VersionUtil;
import io.terminus.trantor2.meta.management.util.ChecksumUtil;
import io.terminus.trantor2.meta.platform.PlatformVersion;
import org.eclipse.jgit.lib.Repository;
import org.eclipse.jgit.storage.file.FileRepositoryBuilder;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public final class VersionCalculator {

    public PlatformVersion calculate() {
        Path workdir = getWorkdir();
        Optional<Path> gitRoot = findGitRoot(workdir);
        if (gitRoot.isPresent()) {
            workdir = gitRoot.get();
            // local startup, force re-calculate
            ChecksumUtil.checksum(workdir);
        }
        String checksum = ChecksumUtil.readTotalChecksum(workdir);
        String versionNumber = VersionUtil.getVersion(VersionConfig.projectVersion);
        return new PlatformVersion(versionNumber, checksum);
    }

    private Path getWorkdir() {
        return Paths.get(System.getProperty("user.dir"));
    }

    private Optional<Path> findGitRoot(Path workdir) {
        try (Repository repository = new FileRepositoryBuilder()
                .findGitDir(workdir.toFile())
                .build()) {
            if (!repository.isBare()) {
                // Normal repository or worktree with proper working tree
                File workTree = repository.getWorkTree();
                if (workTree != null) {
                    return Optional.of(workTree.toPath());
                }
            } else {
                // Bare repository case: might be in worktree scenario
                // The git directory itself might be inside a worktree structure
                File gitDir = repository.getDirectory();
                if (gitDir != null) {
                    // If this is a worktree, the git dir is usually .git/worktrees/xxx
                    // We should return the current working directory as the project root
                    return Optional.of(workdir);
                }
            }
        } catch (Exception e) {
            // If any error occurs, fallback to empty
            System.err.println("Warning: Failed to find git root: " + e.getMessage());
        }
        return Optional.empty();
    }

    public static void main(String[] args) {
        VersionCalculator versionCalculator = new VersionCalculator();
        PlatformVersion platformVersion = versionCalculator.calculate();
        System.out.println(platformVersion);
    }
}
