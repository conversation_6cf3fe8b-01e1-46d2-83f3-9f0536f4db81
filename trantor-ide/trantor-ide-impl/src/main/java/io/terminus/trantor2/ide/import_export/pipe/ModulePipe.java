package io.terminus.trantor2.ide.import_export.pipe;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.ide.import_export.MetaReadContext;
import io.terminus.trantor2.ide.import_export.MetaWriteContext;
import io.terminus.trantor2.ide.import_export.dto.ModuleExportDTO;
import io.terminus.trantor2.ide.import_export.exception.ImportCheckException;
import io.terminus.trantor2.ide.import_export.io.FileConverter;
import io.terminus.trantor2.ide.import_export.io.FolderConverter;
import io.terminus.trantor2.ide.import_export.io.MetaFileConverter;
import io.terminus.trantor2.meta.api.dto.EditOpRequest;
import io.terminus.trantor2.meta.api.dto.MetaChangeOperation;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.service.ExtMetaService;
import io.terminus.trantor2.meta.api.service.MetaEditService;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.platform.PlatformConfigHolder;
import io.terminus.trantor2.meta.platform.PlatformVersion;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.module.meta.ModuleMeta;

import java.io.File;
import java.util.Optional;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 **/
public class ModulePipe implements MetaPipe {
    private static final String MODULE_JSON = "trantor.json";

    private static ModulePipe instance;
    private final MetaFileConverter folderConverter;
    private final MetaFileConverter converter;
    private final MetaQueryService queryService;
    private final MetaEditService editService;
    private final ExtMetaService extMetaService;

    public ModulePipe(MetaQueryService queryService, MetaEditService editService, ExtMetaService extMetaService) {
        this.extMetaService = extMetaService;
        this.folderConverter = new FolderConverter();
        this.converter = new FileConverter();
        this.queryService = queryService;
        this.editService = editService;
    }

    public static MetaPipe instance(MetaQueryService queryService, MetaEditService editService, ExtMetaService extMetaService) {
        if (instance == null) {
            instance = new ModulePipe(queryService, editService, extMetaService);
        }
        return instance;
    }

    @Override
    public void read(File file, MetaReadContext ctx) {
        MetaFileConverter.MetaFile metaFile = converter.read(file);
        if (metaFile.getMetaContent() == null || metaFile.getMetaContent().isNull()) {
            throw new ImportCheckException("模块信息为空");
        }
        if (!metaFile.getMetaContent().has("platformVersion")) {
            throw new ImportCheckException("[trantor.json] 模块信息缺少platformVersion");
        }
        ModuleExportDTO module = ObjectJsonUtil.MAPPER.convertValue(metaFile.getMetaContent(), ModuleExportDTO.class);
        ctx.setSourceModule(module.getKey());
        ctx.setSchemaVersion(Optional.ofNullable(module).map(ModuleExportDTO::getPlatformVersion).map(PlatformVersion::getNumber).orElse("1.0.0"));
        MetaTreeNodeExt meta = new MetaTreeNodeExt();
        meta.setKey(module.getKey());
        if (!ctx.getSourceModule().equals(ctx.getTargetModule())) {
            throw new TrantorRuntimeException("[" + ctx.getSourceModule() + "]不能导入到[" + ctx.getTargetModule() + "]");
        }
        meta.setName(module.getName());
        meta.setType(MetaType.Module.name());
        meta.setDescription(module.getDescription());
        meta.setAccess(module.getAccess());
        meta.setProps(module.getProps());

        checkNativeModule(meta, ctx);
        if (!ctx.isNativeModule()) {
            return;
        }

        MetaEditAndQueryContext metaEditAndQueryContext = EditUtil.newCtx(ctx.getTeamId(), TrantorContext.getCurrentUserId());
        String parentKey = queryService.findByKey(metaEditAndQueryContext, ctx.getSourceModule()).map(MetaTreeNode::getParentKey).orElseThrow(() -> new TrantorRuntimeException("模块不存在"));
        meta.setParentKey(parentKey);
        EditOpRequest editOpRequest = EditUtil.updateNodeOp(meta);
        editOpRequest.setChangeOperation(MetaChangeOperation.ModuleImport);
        editService.submitOp(metaEditAndQueryContext, editOpRequest);
    }

    @Override
    public void write(MetaTreeNode meta, MetaWriteContext ctx, ZipOutputStream zos) {
        folderConverter.write(null, meta.getKey(), "", zos);
        ctx.putPath(meta.getKey(), meta.getKey() + "/");

        // 生成版本文件
        VersionPipe.instance(queryService, editService).write(meta, ctx, zos);

        ModuleMeta module = new ModuleMeta();
        module.from((MetaTreeNodeExt) meta);
        ctx.setPortal(module.isPortal());

        // 生成trantor.json文件
        ModuleExportDTO dto = new ModuleExportDTO();
        dto.setKey(meta.getKey());
        dto.setName(meta.getName());
        dto.setDescription(meta.getDescription());
        dto.setType(meta.getType());
        dto.setProps(meta.getProps());
        dto.setPlatformVersion(PlatformConfigHolder.getPlatformVersion());
        converter.write(ObjectJsonUtil.deserialize(JsonUtil.toJson(dto)), MODULE_JSON, meta.getKey() + "/", zos);

        // api
        ApiPipe.instance(queryService, editService, extMetaService).write(meta, ctx, zos);
    }

    private void checkNativeModule(MetaTreeNodeExt meta, MetaReadContext ctx) {
        ModuleMeta moduleMeta = new ModuleMeta();
        moduleMeta.from(meta);
        if (moduleMeta.isNativeModule() && !ctx.isNativeModule()) {
            throw new TrantorRuntimeException("内部模块不能往外部模块导入");
        }
        if (!moduleMeta.isNativeModule() && ctx.isNativeModule()) {
            throw new TrantorRuntimeException("外部模块不能往内部模块导入");
        }
    }
}
