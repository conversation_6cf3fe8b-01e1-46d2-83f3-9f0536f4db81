package io.terminus.trantor2.ide.import_export.pipe;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.ide.import_export.MetaReadContext;
import io.terminus.trantor2.ide.import_export.MetaWriteContext;
import io.terminus.trantor2.ide.import_export.dto.BaseExportDTO;
import io.terminus.trantor2.ide.import_export.exception.ImportCheckException;
import io.terminus.trantor2.ide.import_export.io.FileConverter;
import io.terminus.trantor2.ide.import_export.io.MetaFileConverter;
import io.terminus.trantor2.meta.api.dto.EditOpRequest;
import io.terminus.trantor2.meta.api.dto.MetaChangeOperation;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.MoveTarget;
import io.terminus.trantor2.meta.api.dto.MoveTargetType;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.repository.ResourceRepository;
import io.terminus.trantor2.meta.api.service.ExtMetaService;
import io.terminus.trantor2.meta.api.service.MetaEditService;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.editor.component.ResourceRegistry;
import io.terminus.trantor2.meta.resource.BaseMeta;
import io.terminus.trantor2.meta.resource.ResourceBaseMeta;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;

import java.io.File;
import java.util.Objects;
import java.util.zip.ZipOutputStream;

import static io.terminus.trantor2.meta.api.model.MetaType.FolderRoot;

/**
 * <AUTHOR>
 **/
public class StandardPipe implements MetaPipe {
    public static final String INDEX = "index.json";
    private static StandardPipe instance;
    protected final MetaFileConverter converter;
    protected final MetaQueryService queryService;
    protected final MetaEditService editService;
    protected final ExtMetaService extMetaService;

    public StandardPipe(MetaQueryService queryService, MetaEditService editService, ExtMetaService extMetaService) {
        this.converter = new FileConverter();
        this.queryService = queryService;
        this.editService = editService;
        this.extMetaService = extMetaService;
    }

    public static MetaPipe instance(MetaQueryService queryService, MetaEditService editService, ExtMetaService extMetaService) {
        if (instance == null) {
            instance = new StandardPipe(queryService, editService, extMetaService);
        }
        return instance;
    }


    @Override
    public void read(File file, MetaReadContext ctx) {
        MetaFileConverter.MetaFile metaFile = converter.read(file);
        if (metaFile.getMetaContent() == null || metaFile.getMetaContent().isNull()) {
            throw new ImportCheckException("[" + metaFile.getName() + "]元信息信息为空");
        }
        MetaTreeNodeExt meta = ObjectJsonUtil.MAPPER.convertValue(metaFile.getMetaContent(), MetaTreeNodeExt.class);
        MetaEditAndQueryContext metaEditAndQueryContext = EditUtil.newCtx(ctx.getTeamId(), TrantorContext.getCurrentUserId());
        String parentPath = file.getParent().replace(ctx.getBasePath() + "/source", "") + "/";
        String parentKey = ctx.getKeyByPath(parentPath);
        if (parentKey == null) {
            metaEditAndQueryContext.setModuleKey(ctx.getTargetModule());
            parentKey = queryService.queryInTeam(ctx.getTeamId())
                    .findOne(Cond.moduleOf(ctx.getTargetModule()).and(Field.type().equal(FolderRoot.name())))
                    .map(MetaTreeNode::getKey)
                    .orElseThrow(() -> new TrantorRuntimeException("folder root not found"));
        }
        meta.setParentKey(parentKey);
        meta.setParentPath(parentPath);
        saveMeta(metaEditAndQueryContext, meta, ctx);
    }

    @Override
    public void write(MetaTreeNode meta, MetaWriteContext ctx, ZipOutputStream zos) {
        String path = ctx.getPath(meta.getParentKey());
        // 系统预置元数据
        if (path == null) {
            return;
        }
        BaseExportDTO exportDTO = toExportDTO(meta);
        converter.write(ObjectJsonUtil.deserialize(JsonUtil.toJson(exportDTO)), KeyUtil.shortKey(meta.getKey()) + "." + meta.getType().toLowerCase() + ".json", path, zos);
    }

    protected BaseExportDTO toExportDTO(MetaTreeNode obj) {
        BaseExportDTO dto = new BaseExportDTO();
        dto.setName(obj.getName());
        dto.setType(obj.getType());
        dto.setKey(obj.getKey());
        dto.setAccess(obj.getAccess());
        dto.setDescription(obj.getDescription());
        dto.setProps(obj.getProps());
        return dto;
    }

    protected MetaTreeNodeExt toMeta(BaseExportDTO obj, String parentKey) {
        MetaTreeNodeExt dto = new MetaTreeNodeExt();
        dto.setName(obj.getName());
        dto.setType(obj.getType());
        dto.setKey(obj.getKey());
        dto.setAccess(obj.getAccess());
        dto.setDescription(obj.getDescription());
        dto.setProps(obj.getProps());
        dto.setParentKey(parentKey);
        return dto;
    }

    protected void saveMeta(MetaEditAndQueryContext metaEditAndQueryContext, MetaTreeNodeExt meta, MetaReadContext readCtx) {
        readCtx.checkDuplicate(meta.getKey());

        Class<? extends ResourceBaseMeta<?>> metaClass = ResourceRegistry.getMetaClass(meta.getMetaType());
        ResourceRepository repo = ResourceRegistry.getRepository(meta.getMetaType())
                .orElseThrow(() -> new TrantorRuntimeException("meta type not supported to import: " + meta.getType()));
        ResourceContext ctx = ResourceContext.newResourceCtx(metaEditAndQueryContext);
        ResourceBaseMeta<?> resourceBaseMeta = BaseMeta.newInstanceFrom(meta, metaClass);
        if (readCtx.containsMeta(meta.getKey())) {
            // update
            String parentKey = readCtx.metaParent(meta.getKey());
            if (!Objects.equals(parentKey, meta.getParentKey())) {
                // move
                EditOpRequest editOpRequest = EditUtil.moveNodeOp(meta.getKey(), new MoveTarget(meta.getParentKey(), MoveTargetType.ChildLast));
                editOpRequest.setChangeOperation(MetaChangeOperation.ModuleImport);
                editService.submitOp(metaEditAndQueryContext, editOpRequest);
            }
            repo.update(resourceBaseMeta, ctx);
            readCtx.removeMeta(meta.getKey());
        } else {
            // create
            repo.create(resourceBaseMeta, ctx);
        }
    }
}
