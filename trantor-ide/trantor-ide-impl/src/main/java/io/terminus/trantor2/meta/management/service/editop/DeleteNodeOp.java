package io.terminus.trantor2.meta.management.service.editop;

import com.google.auto.service.AutoService;
import io.terminus.trantor2.meta.api.dto.EditOpType;
import io.terminus.trantor2.meta.blob.MetaBlob;
import io.terminus.trantor2.meta.blob.MetaBlobImpl;
import io.terminus.trantor2.meta.exception.MetaInvalidRequestException;
import io.terminus.trantor2.meta.index.MetaIndexRef;
import io.terminus.trantor2.meta.index.RefCond;
import io.terminus.trantor2.meta.util.KeyUtil;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

@AutoService(EditOp.class)
@NoArgsConstructor
public class DeleteNodeOp extends BaseEditOp {
    public DeleteNodeOp(EditOpParams params) {
        super(params);
    }

    @Override
    protected void execOp() {
        if (isForce()) {
            loadCurOrElseNull();
            if (cur == null) {
                return;
            }
        } else {
            loadCur();
        }
        loadParentKey();

        //lock cur and parent
        lockNodes.add(cur.getKey());
        lockNodes();

        //get new cur
        loadCur();

        if (metaBlobRepo.findByParentKey(teamId, MetaBlob.Key.class, cur.getKey()).size() > 0) {
            // means has subtree
            if (!isRecursive()) {
                throw new MetaInvalidRequestException("Node has sub-tree, can not be deleted. Please use recursive mode.");
            }
        }

        if (!isForce()) {
            validateCurNodeNotBeingUsed();
        }
        if (isVerbose()) {
            // verbose mode, just do validation, not delete
            return;
        }
        List<MetaBlobImpl> allNodes = metaBlobRepo.findSubTree(teamId, cur.getKey())
                .stream().map(it -> (MetaBlobImpl) it).collect(Collectors.toList());

        // delete all nodes include sub-tree
        deleteMeta(allNodes);
        // delete all the links of nodes(include sub-tre).
        List<String> allKeys = allNodes.stream().map(MetaBlobImpl::getKey).collect(Collectors.toList());
        // refresh index for all nodes
        refreshIndex(allKeys);
    }

    private void validateCurNodeNotBeingUsed() {
        // TODO: subtree usage check
        List<MetaIndexRef> us = linkRepo.find(teamId, RefCond.builder().targetKey(KeyUtil.originalKey(cur.getKey())).build());
        if (us == null || us.isEmpty()) {
            return;
        }
        List<String> usages = us.stream().map(MetaIndexRef::getSourceKey)
                .filter(key -> !key.equals(cur.getKey()))
                .distinct().sorted().limit(10)
                .collect(Collectors.toList());
        if (usages.isEmpty()) {
            return;
        }
        StringBuilder sb = new StringBuilder("Cannot delete node, being used by (top10): [");
        for (int i = 0; i < usages.size(); i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append(usages.get(i));
        }
        sb.append("]");
        throw new MetaInvalidRequestException(sb.toString());
    }

    @Override
    public EditOpType getEditOpType() {
        return EditOpType.DeleteNode;
    }
}
