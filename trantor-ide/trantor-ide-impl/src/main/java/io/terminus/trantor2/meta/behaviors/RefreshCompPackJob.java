package io.terminus.trantor2.meta.behaviors;

import io.terminus.trantor2.lang.meta.CompPack;
import io.terminus.trantor2.properties.TrantorConsoleProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.scheduling.annotation.Scheduled;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 */
@Slf4j
public class RefreshCompPackJob {

    private final Path dlBaseDir = Paths.get(System.getProperty("java.io.tmpdir")).resolve("trantor2-comp-packs");

    private long latestLastModified = 0;

    private final TrantorConsoleProperties trantorConsoleProperties;

    public RefreshCompPackJob(TrantorConsoleProperties trantorConsoleProperties) {
        this.trantorConsoleProperties = trantorConsoleProperties;
    }

    @Scheduled(fixedDelay = 30 * 1000)
    public void refreshCompPacks() throws IOException {
        log.info("refreshing comp packs");

        // 1. clean up old dir
        log.info("cleaning up old dir: {}", dlBaseDir);
        FileUtils.deleteDirectory(dlBaseDir.toFile());
        Files.createDirectories(dlBaseDir);

        // 2. fetch latest.json
        String latestUrl = getLatestJsonUrl();
        Path latestJsonPath = CompPack.latestJsonPath(dlBaseDir);
        log.info("fetching latest.json from {}", latestUrl);
        String endpoint = null;
        long lastModified = 0;
        HttpURLConnection conn = null;
        try {
            URL url = new URL(latestUrl);
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            int responseCode = conn.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                log.error("Failed to fetch latest.json from {}, response code: {}", latestUrl, responseCode);
                return;
            }
            lastModified = conn.getLastModified();
            if (lastModified > 0 && lastModified <= latestLastModified) {
                // no new version
                return;
            }
            endpoint = getTrantorEndpoint(conn, latestUrl);
            log.info("endpoint: {}", endpoint);
            try (InputStream is = conn.getInputStream()) {
                Files.copy(is, latestJsonPath);
            }
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }

        // 3. read latest.json
        CompPack.Latest latest = CompPack.readLatest(dlBaseDir);

        // 4. fetch each comp pack
        for (CompPack.Info info : latest.getInfos().values()) {
            fetchBehaviors(dlBaseDir, endpoint, info);
        }

        // 5. load comp pack
        CompPack.load(dlBaseDir);
        // safely set latestLastModified after all success, so that next time we can skip if no new version
        latestLastModified = lastModified;
    }

    private void fetchBehaviors(Path dlBaseDir, String endpoint, CompPack.Info compPackInfo) {
        String behaviorsUrl = CompPack.behaviorsJsonUrl(endpoint, compPackInfo);
        Path behaviorsPath = CompPack.behaviorsJsonPath(dlBaseDir, compPackInfo);
        log.info("fetching behaviors from {}", behaviorsUrl);
        try (InputStream is = new URL(behaviorsUrl).openStream()) {
            Files.createDirectories(behaviorsPath.getParent());
            Files.copy(is, behaviorsPath);
        } catch (Exception e) {
            log.error("Failed to fetch behaviors from {}", behaviorsUrl, e);
            throw new RuntimeException(e);
        }
    }

    private String getLatestJsonUrl() {
        String latestJsonUrl = trantorConsoleProperties.getLatestJson().getUrl();
        if (latestJsonUrl != null) {
            return latestJsonUrl;
        }
        return getConsoleBaseUrl() + "/latest.json";
    }

    private String getConsoleBaseUrl() {
        String domain = trantorConsoleProperties.getDomain();
        if (domain == null) {
            // use internal host at erda
            return "http://trantor2-console:8099";
        }
        if (domain.startsWith("http://") || domain.startsWith("https://")) {
            return domain;
        }
        String protocol = "https://";
        if (trantorConsoleProperties.getIamDomain() != null && trantorConsoleProperties.getIamDomain().startsWith("http://")) {
            // follow iam domain protocol
            protocol = "http://";
        }
        return protocol + domain;
    }

    /**
     * 遵循前端的逻辑，获取 trantor endpoint
     * <p>
     * 如果前端逻辑有变化，这里也需要同步
     */
    private String getTrantorEndpoint(HttpURLConnection conn, String latestUrl) {
        String endpoint = conn.getHeaderField("X-Trantor-Endpoint");
        if (endpoint == null || endpoint.isEmpty()) {
            endpoint = "https://terminus-new-trantor.oss-cn-hangzhou.aliyuncs.com";
        } else if (endpoint.equalsIgnoreCase("local")) {
            endpoint = latestUrl.substring(0, latestUrl.lastIndexOf('/'));
        }
        return endpoint;
    }
}
