package io.terminus.trantor2.meta.management.task;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.AppenderBase;
import com.google.common.collect.ImmutableSet;
import io.terminus.trantor2.task.TaskLocalContext;
import io.terminus.trantor2.task.TaskRun;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Set;

/**
 * <AUTHOR>
 */
public class TaskRunLogAppender extends AppenderBase<ILoggingEvent> {

    private static final Set<String> BLOCK_LIST = ImmutableSet.of(
            TaskManager.class.getCanonicalName(),
            JdbcTemplate.class.getCanonicalName()
    );

    @Override
    protected void append(ILoggingEvent event) {
        TaskRun taskRun = TaskLocalContext.getCurrentTaskRun();

        if (taskRun == null) {
            return;
        }
        if (BLOCK_LIST.contains(event.getLoggerName())) {
            return;
        }
        String logMessage = event.getFormattedMessage();
        taskRun.getOutput().log(logMessage);
    }
}
