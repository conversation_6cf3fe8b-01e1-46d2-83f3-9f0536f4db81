package io.terminus.trantor2.ide.migration;

import io.terminus.trantor2.scene.config.SceneTemplateConfig;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public final class MigrateContext {

    // TODO: 当前为场景视图特殊处理，未来需要标准化一下
    private String viewKey;
    private List<SceneView> sceneViewList;
    private SceneTemplateConfig templateConfig;

    @Data
    public static class SceneView {
        private String key;
        private String type;
    }
}
