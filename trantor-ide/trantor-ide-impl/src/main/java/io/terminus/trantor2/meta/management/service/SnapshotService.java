package io.terminus.trantor2.meta.management.service;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.service.MetaEditService;
import io.terminus.trantor2.meta.editor.model.MetaObject;
import io.terminus.trantor2.meta.editor.util.ObjectUtil;
import io.terminus.trantor2.meta.exception.MetaException;
import io.terminus.trantor2.meta.history.MetaHistory;
import io.terminus.trantor2.meta.history.MetaHistoryRepo;
import io.terminus.trantor2.meta.management.model.MetaDiff;
import io.terminus.trantor2.meta.management.model.MetaDiffResult;
import io.terminus.trantor2.meta.object.MetaObjectRepo;
import io.terminus.trantor2.meta.object.MetaObjectV2;
import io.terminus.trantor2.meta.objects.tree.ResourceAuditField;
import io.terminus.trantor2.meta.objects.tree.ResourceNodeIdentity;
import io.terminus.trantor2.meta.objects.vcs.SnapshotNode;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Slf4j
@Service
@AllArgsConstructor
public class SnapshotService {
    private final MetaObjectRepo metaObjectRepo;
    private final MetaEditService metaEditService;
    private final MetaHistoryRepo metaHistoryRepo;

    public String snapshot(Long teamId) {
        MetaEditAndQueryContext ctx = EditUtil.newCtx(teamId, TrantorContext.getCurrentUserId());
        metaEditService.submitOp(ctx, EditUtil.snapshotOp());
        MetaHistory snap = metaHistoryRepo.findLastVersion(ctx.getTeamId(), "__snapshot__").orElse(null);
        if (Objects.isNull(snap)) {
            throw new MetaException("snapshot not found, team id [" + teamId + "]");
        }
        if (ObjectUtil.isZero(snap.getCurrentOid())) {
            throw new MetaException("snapshot current oid is null, team id [" + teamId + "]");
        }
        return snap.getCurrentOid().toHex();
    }

    public MetaDiffResult diff(Long teamId, String leftOid, String rightOid) {
        MetaObject left = metaObjectRepo.findByOid(teamId, leftOid).map(MetaObjectV2::getContentStruct).orElseThrow(() -> new MetaException("object not found, team id [" + teamId + "], oid [" + leftOid + "]"));
        MetaObject right = metaObjectRepo.findByOid(teamId, rightOid).map(MetaObjectV2::getContentStruct).orElseThrow(() -> new MetaException("object not found, team id [" + teamId + "], oid [" + rightOid + "]"));

        if (!Objects.equals(left.getSchemaVersion(), right.getSchemaVersion()) && Objects.equals(2L, left.getSchemaVersion())) {
            throw new MetaException("schema version is different, team id [" + teamId + "], source oid [" + leftOid + "], right oid [" + rightOid + "]");
        }

        SnapshotNode leftSnapshot = ObjectJsonUtil.MAPPER.convertValue(left.getProps(), SnapshotNode.class);
        SnapshotNode rightSnapshot = ObjectJsonUtil.MAPPER.convertValue(right.getProps(), SnapshotNode.class);

        Map<String, ResourceAuditField> leftMap = leftSnapshot.getResources().stream().collect(Collectors.toMap(ResourceNodeIdentity::getKey, Function.identity()));
        Map<String, ResourceAuditField> rightMap = rightSnapshot.getResources().stream().collect(Collectors.toMap(ResourceNodeIdentity::getKey, Function.identity()));

        MetaDiff leftDiff = diffResource(leftOid, leftMap, rightMap);
        MetaDiff rightDiff = diffResource(rightOid, rightMap, leftMap);

        return new MetaDiffResult(leftDiff, rightDiff);
    }

    private MetaDiff diffResource(String sourceOid, Map<String, ResourceAuditField> source, Map<String, ResourceAuditField> target) {
        MetaDiff diff = new MetaDiff(sourceOid);

        for (String key : source.keySet()) {
            ResourceAuditField leftResource = source.get(key);
            ResourceAuditField rightResource = target.get(key);
            if (Objects.isNull(rightResource)) {
                //新增
                diff.addCreated(ObjectUtil.resourcePath(leftResource, source));
            } else if (!Objects.equals(leftResource.getOid(), rightResource.getOid())) {
                //修改
                diff.addUpdated(ObjectUtil.resourcePath(leftResource, source));
            } else {
                diff.addUnchanged(ObjectUtil.resourcePath(leftResource, source));
            }
        }
        diff.sort();
        return diff;
    }
}
