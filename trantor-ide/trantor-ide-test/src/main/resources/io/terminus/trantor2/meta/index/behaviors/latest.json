{"service": {"prefix": "fe-resources/dev", "dirname": "service-20250210110927", "namespace": "service", "endpointType": "PC", "scopes": {"runtime": {"styles": ["assets/runtime-CVdvR1l6.css", "assets/index-BSVsjzXO.css", "assets/request-CfRQENoh.css"], "main": "assets/runtime-5v0S-8b3.js"}, "designer": {"styles": ["assets/designer-B2Mql6v8.css", "assets/request-CfRQENoh.css"], "main": "assets/designer-CGU9fNZM.js"}, "layout": {"styles": ["assets/layout-B-UhJF0R.css", "assets/request-CfRQENoh.css", "assets/index-BSVsjzXO.css"], "main": "assets/layout-CPCavAt2.js"}}, "metadata": {"branch": "feature/latest", "commitSha": "4c44050", "buildAt": "2025-02-10 11:08:02"}, "priority": 0}, "service-mobile": {"prefix": "fe-resources/dev", "dirname": "service-mobile-20250205195808", "namespace": "service-mobile", "endpointType": "APP", "scopes": {"function": {"styles": ["assets/wrapper-CnVUZN3o.css"], "main": "assets/function-4X_nkzvK.js"}, "layout": {"styles": [], "main": "assets/layout-BLvXv4zb.js"}, "runtime": {"styles": ["assets/runtime-BbNhf_WS.css", "assets/wrapper-CnVUZN3o.css"], "main": "assets/runtime-3Q_-CdIq.js"}, "designer": {"styles": [], "main": "assets/designer-Do_vjDJK.js"}}, "metadata": {"branch": "feature/latest", "commitSha": "eee1a54", "buildAt": "2025-02-05 19:57:39"}, "priority": 0}, "iam": {"prefix": "fe-resources/dev", "dirname": "iam-20250208104916", "namespace": "iam", "endpointType": "PC", "scopes": {"runtime": {"styles": ["assets/runtime-C7Ydu-rx.css", "assets/const-FifcLAXN.css"], "main": "assets/runtime-BkJQQKGl.js"}, "designer": {"styles": ["assets/designer-C_LAjs48.css", "assets/const-FifcLAXN.css"], "main": "assets/designer-DjtmYSpQ.js"}}, "metadata": {"buildAt": "2025-02-08 10:48:30", "commitSha": "2d63d11", "branch": "feature/develop"}}, "terp": {"prefix": "fe-resources/dev", "dirname": "terp-20250205105833", "namespace": "terp", "endpointType": "PC", "scopes": {"runtime": {"styles": ["assets/runtime-COa_hxcJ.css", "assets/config-CfRQENoh.css"], "main": "assets/runtime-0--5UBsw.js"}, "designer": {"styles": ["assets/designer-D_wrwsnH.css", "assets/config-CfRQENoh.css"], "main": "assets/designer-QHKGBsyA.js"}}, "metadata": {"branch": "feature/latest", "commitSha": "46cf558", "buildAt": "2025-02-05 10:57:28"}, "priority": 0}, "terp-mobile": {"prefix": "fe-resources/dev", "dirname": "terp-mobile-20250205105747", "namespace": "terp-mobile", "endpointType": "APP", "scopes": {"runtime": {"styles": ["assets/runtime-0wgrAIJD.css", "assets/useService-DQ0B6GHQ.css"], "main": "assets/runtime-BpgjRttk.js"}, "designer": {"styles": ["assets/useService-DQ0B6GHQ.css"], "main": "assets/designer-GNOFNNKQ.js"}}, "metadata": {"branch": "feature/latest", "commitSha": "46cf558", "buildAt": "2025-02-05 10:57:31"}, "priority": 0}, "dors": {"prefix": "fe-resources/dev", "dirname": "dors-20250208104925", "namespace": "dors", "endpointType": "PC", "scopes": {"designer": {"styles": ["assets/designer-139a481c.css"], "main": "assets/designer-d27f9b99.js"}, "runtime": {"styles": ["assets/t-lib-6e36a96e.css"], "main": "assets/runtime-675c987e.js"}}, "metadata": {"buildAt": "2025-02-08 10:48:33", "commitSha": "2d63d11", "branch": "feature/develop"}}, "base-mobile": {"prefix": "fe-resources/dev", "dirname": "base-mobile-20250212135241", "namespace": "base-mobile", "endpointType": "APP", "scopes": {"designer": {"styles": ["assets/designer-KLb38MJy.css"], "main": "assets/designer-po8J7vND.js"}, "runtime": {"styles": ["assets/runtime-4RpOCd0k.css"], "main": "assets/runtime-wGCCMGaX.js"}}, "metadata": {"buildAt": "2025-02-12 13:50:47", "commitSha": "9104ff2", "branch": "feature/develop"}, "priority": 0}, "base": {"prefix": "fe-resources/dev", "dirname": "base-20250212135239", "namespace": "base", "endpointType": "PC", "scopes": {"designer": {"styles": ["assets/designer-BvwFllCu.css"], "main": "assets/designer-BPK2dv7u.js"}, "runtime": {"styles": ["assets/runtime-DuArEZ0c.css"], "main": "assets/runtime-N5rNa70b.js"}}, "metadata": {"buildAt": "2025-02-12 13:50:47", "commitSha": "9104ff2", "branch": "feature/develop"}, "priority": 0}, "dors-mobile": {"prefix": "fe-resources/dev", "dirname": "dors-mobile-20250208104939", "namespace": "dors-mobile", "endpointType": "APP", "scopes": {"designer": {"styles": ["assets/designer-139a481c.css"], "main": "assets/designer-ae20000f.js"}, "runtime": {"styles": ["assets/t-lib-ebe380f4.css"], "main": "assets/runtime-fbd65982.js"}}, "metadata": {"buildAt": "2025-02-08 10:49:27", "commitSha": "2d63d11", "branch": "feature/develop"}}, "theme-blue": {"prefix": "fe-resources/dev", "dirname": "theme-blue-20241225133615", "namespace": "theme-blue", "endpointType": "PC", "scopes": {"theme": {"styles": [], "main": "assets/theme-g9SLVGN9.js"}}, "metadata": {"branch": "develop", "commitSha": "b07c7ad", "buildAt": "2024-12-24 16:20:50", "syncBy": "aHVzdGNlcg==", "syncFrom": "terp-test", "syncAt": "2024/12/25 13:36:12"}, "priority": 0}, "theme-dark": {"prefix": "fe-resources/dev", "dirname": "theme-dark-20241225133608", "namespace": "theme-dark", "endpointType": "PC", "scopes": {"theme": {"styles": [], "main": "assets/theme-CKcv8Du_.js"}}, "metadata": {"branch": "develop", "commitSha": "b07c7ad", "buildAt": "2024-12-24 16:19:55", "syncBy": "aHVzdGNlcg==", "syncFrom": "terp-test", "syncAt": "2024/12/25 13:36:03"}, "priority": 0}, "charts": {"prefix": "fe-resources/dev", "dirname": "charts-20250212135243", "namespace": "charts", "endpointType": "PC", "scopes": {"runtime": {"styles": ["assets/runtime-BUzhsahP.css", "assets/index-C3AIvZrM.css"], "main": "assets/runtime-BBcuRZBk.js"}, "designer": {"styles": ["assets/designer-dxune1T1.css", "assets/index-C3AIvZrM.css"], "main": "assets/designer-D3z-xx7G.js"}}, "metadata": {"buildAt": "2025-02-12 13:50:47", "commitSha": "9104ff2", "branch": "feature/develop"}, "priority": 0}, "theme-example": {"prefix": "fe-resources/dev", "dirname": "theme-example-20250115104848", "namespace": "theme-example", "endpointType": "PC", "scopes": {"theme": {"styles": [], "main": "assets/theme-3ba81b93.js"}}, "metadata": {"commitId": "5687ca90f9bf868cf0f468c6ba8e49b1d490ebd1"}, "priority": 0}}