{"behaviors": [{"key": "AppManage", "name": "AppManage", "type": "Widget", "title": "应用管理", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"listFlow": {"type": "InvokeApi", "url": "/api/trantor/portal/application/list", "method": "GET"}, "favoritesFlow": {"type": "InvokeApi", "url": "/api/trantor2/runtime/preference/menu/favorites", "method": "GET"}, "favoritesSaveFlow": {"type": "InvokeApi", "url": "/api/trantor2/runtime/preference/menu/save", "method": "POST"}}}, {"key": "BatchInButton", "name": "BatchInButton", "type": "Widget", "title": "批次生成按钮", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"queryMatMdFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "queryCreatePermissionFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_BS_TYPE_QUERY_BY_MVM_TYPE_EVENT_SERVICE"}, "queryBatchFlow": {"type": "InvokeService", "serviceKey": "TERP_MIGRATE$DEL_DN_QUERY_BATCH_BY_RELATED_ITEM_SERVICE_SERVICE"}, "queryBatchCharaFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_QUERY_MAT_BATCH_CHARA_EVENT_SERVICE"}, "queryAvailableBatchFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_QUERY_AVAILABLE_BATCH_EVENT_SERVICE"}, "createBatchCodeFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_CREATE_BATCH_CODE_EVENT_SERVICE"}, "createPurchaseBatchCodeFlow": {"type": "InvokeService", "serviceKey": "TERP_MIGRATE$FSGAS_DN_CREATE_BATCH_CODE_EVENT_SERVICE"}}}, {"key": "ClientWorkbench", "name": "ClientWorkbench", "type": "Widget", "title": "客户端工作台-应用", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"listFlow": {"type": "InvokeApi", "url": "/api/trantor/portal/application/list", "method": "GET"}}}, {"key": "OperationWorkbench", "name": "OperationWorkbench", "type": "Widget", "title": "运营端工作台", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "运营端工作台"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"operationWorkbenchServiceProps": {"countFlow": {"type": "InvokeService", "serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_TASK_INSTANCE_COUNT_GET"}, "favoritesFlow": {"type": "InvokeApi", "url": "/api/trantor2/runtime/preference/menu/favorites", "method": "GET"}, "orgSelectFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_SWITCH_QUERY_USER_COM_EVENT_SERVICE"}}}}, {"key": "OrgSelector", "name": "OrgSelector", "type": "Widget", "title": "组织切换选择器", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"flow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_SWITCH_QUERY_USER_COM_EVENT_SERVICE"}}}, {"key": "Payment", "name": "Payment", "type": "Widget", "title": "订单支付", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"queryOrderInfoFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_SCM$sls_so_head_tr", "serviceKey": "ERP_SCM$SYS_FindDataByIdService"}, "queryPaymentInfoFlow": {"type": "InvokeService", "serviceKey": "TERP_MIGRATE$SO_EXT_SO_RENDER_PAY_EVENT_SERVICE"}, "payFlow": {"type": "InvokeService", "serviceKey": "TERP_MIGRATE$FSGAS_APPLY_PAY_EVENT_SERVICE"}}}, {"key": "PrintManagement", "name": "PrintManagement", "type": "Widget", "title": "打印管理", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "打印管理配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"getPrintPageFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$DEL_DN_APP_PRINT_PAGE_SERVICE"}, "postPrintEventFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$DEL_DN_PRINT_EVENT_SERVICE"}}}, {"key": "Picking", "name": "Picking", "type": "Widget", "title": "物料拣配", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "生产物料拣配中心配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"workCenterFlow": {"type": "InvokeService", "serviceKey": "ERP_PRD$PRD_ISSUE_APP_WORK_CENTER_DISTRIBUTION_FINISH_EVENT_SERVICE"}, "genMatMdFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "orderFlow": {"type": "InvokeService", "serviceKey": "ERP_PRD$PRD_ISSUE_APP_ORDER_DETAIL_EVENT_SERVICE"}, "createOrderFlow": {"type": "InvokeService", "serviceKey": "ERP_PRD$PRD_ISSUE_APP_CREATE_BY_PRD_ORDER_BOM_LIST_EVENT_SERVICE"}, "issueFlow": {"type": "InvokeService", "serviceKey": "ERP_PRD$PRD_ISSUE_ORDER_DETAILS_EVENT_SERVICE"}, "createIssueFlow": {"type": "InvokeService", "serviceKey": "ERP_PRD$PRD_ISSUE_APP_SAVE_EVENT_SERVICE"}, "updateIssueFlow": {"type": "InvokeService", "serviceKey": "ERP_PRD$PRD_ISSUE_ITEM_UPDATE_EVENT_SERVICE"}}}, {"key": "UserCenter", "name": "UserCenter", "type": "Widget", "title": "用户中心", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "用户中心配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"UserCenterServiceProps": {"queryCustomAccountFlow": {"type": "InvokeService", "serviceKey": "TERP_MIGRATE$CUST_ACCOUNT_QUERY_SERVICE"}, "getWechatBindStatusFlow": {"type": "InvokeService", "serviceKey": "sys_common$API_TRANTOR_PORTAL_USER_FIND_THIRD_PARTY_BIND_STATUS_POST"}, "queryOperationAccountFlow": {"type": "InvokeService", "serviceKey": "TERP_MIGRATE$OPERATION_ACCOUNT_QUERY_SERVICE"}, "queryOrgListFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_SWITCH_QUERY_USER_COM_EVENT_SERVICE"}, "unBindWechatFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$wechat_mini_app_unbind"}, "getCurrentFlow": {"type": "InvokeApi", "url": "/api/trantor/portal/current", "method": "GET"}}}}, {"key": "DeliveryPicking", "name": "DeliveryPicking", "type": "Widget", "title": "交货单拣/收货", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "交货单拣/收货配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"barcodeRuleFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_BARCODE_RULE_QUERY_SERVICE"}, "paginateFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$DEL_APP_DN_LIST_PAGE_EVENT_SERVICE"}, "invLocationFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_PDA_ORG_STRUCT_MD_PAGE_SERVICE"}}}, {"key": "DeliveryPickingOperate", "name": "DeliveryPickingOperate", "type": "Widget", "title": "交货单拣/收货", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "交货单拣/收货配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"barcodeRuleFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_BARCODE_RULE_QUERY_SERVICE"}, "saveFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$DEL_APP_INV_EXECUTED_SAVE_EVENT_SERVICE"}, "detailFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$DEL_APP_INV_EXECUTED_TASK_TILE_EVENT_SERVICE"}, "submitFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$DEL_APP_INV_EXECUTED_EVENT_SERVICE"}, "genUomTypeCfFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}}}, {"key": "PickingTaskOperate", "name": "PickingTaskOperate", "type": "Widget", "title": "拣配任务拣/收货", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "拣配任务拣货配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"barcodeRuleFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_BARCODE_RULE_QUERY_SERVICE"}, "saveFlow": {"type": "InvokeService", "serviceKey": "ERP_WM$WM_PDA_JOB_TASK_SAVE_EVENT_SERVICE"}, "postFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$DEL_DN_TASK_FINISH_EVENT_SERVICE"}, "detailFlow": {"type": "InvokeService", "serviceKey": "ERP_WM$WM_QUERY_PDA_JOB_TASK_EVENT_SERVICE"}, "submitFlow": {"type": "InvokeService", "serviceKey": "ERP_WM$WM_WH_PDA_COMPLETED_TASK_EVENT_SERVICE"}, "orgStructFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "warehouseBinFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_WM$wm_warehouse_bin_md", "serviceKey": "ERP_WM$SYS_FindDataByIdService"}, "barcodeFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_CODE_BY_LABEL_FIND_EVENT_SERVICE"}, "genUomTypeCfFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}}}, {"key": "ReportWorkList", "name": "ReportWorkList", "type": "Widget", "title": "报工确认列表", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "生产物料报工确认配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"paginateFlow": {"type": "InvokeService", "serviceKey": "ERP_PRD$PRD_APP_PAGING_OPERATION_CONFIRM_LIST_EVENT_SERVICE"}, "workCenterFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_work_centor_header_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}}}, {"key": "PickingTask", "name": "PickingTask", "type": "Widget", "title": "拣配任务拣/收货", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "拣配任务拣/收货配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"barcodeRuleFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_BARCODE_RULE_QUERY_SERVICE"}, "paginateFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$DEL_APP_DN_LIST_PAGE_EVENT_SERVICE"}, "invLocationFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_PDA_ORG_STRUCT_MD_PAGE_SERVICE"}}}, {"key": "ReportWorkOperate", "name": "ReportWorkOperate", "type": "Widget", "title": "报工确认", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "生产物料报工确认配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"processFlow": {"type": "InvokeService", "serviceKey": "ERP_PRD$PRD_ORDER_ROUTINGS_CONFIRM_BATCH_RENDER_EVENT_SERVICE"}, "detailFlow": {"type": "InvokeService", "serviceKey": "ERP_PRD$PRD_ORDER_ROUTING_CONFIRM_RENDER_BY_PRD_ORDER_CODE_EVENT_SERVICE"}, "confirmFlow": {"type": "InvokeService", "serviceKey": "ERP_PRD$PRD_ORDER_DELIVERY_CONFIRM_BATCH_EVENT_SERVICE"}}}, {"key": "WarehousePosting", "name": "WarehousePosting", "type": "Widget", "title": "出/入库过账", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "出/入库过账配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"barcodeRuleFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_BARCODE_RULE_QUERY_SERVICE"}, "paginateFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$DEL_APP_DN_LIST_PAGE_EVENT_SERVICE"}, "invLocationFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_PDA_ORG_STRUCT_MD_PAGE_SERVICE"}}}, {"key": "WarehousePostingOperate", "name": "WarehousePostingOperate", "type": "Widget", "title": "出/入库过账操作", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "出/入库过账操作配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"barcodeRuleFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_BARCODE_RULE_QUERY_SERVICE"}, "detailFlow": {"type": "InvokeService", "serviceKey": "ERP_WM$WM_QUERY_PDA_JOB_TASK_EVENT_SERVICE"}, "headInfoFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$DN_PST_DETAIL_EVENT_SERVICE"}, "supplierSubmitFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$DEL_DN_POSTING_EVENT_SERVICE"}, "purchaserSubmitFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$DEL_DN_POSTING_EVENT_SERVICE"}}}, {"key": "WorkOrder", "name": "WorkOrder", "type": "Widget", "title": "送装工单", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "送装工单"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"quotaFlow": {"type": "InvokeService", "serviceKey": "ERP_DIM$DIM_ORDER_OUT_FORUS_SAVE_EVENT_SERVICE"}, "summaryFlow": {"type": "InvokeService", "serviceKey": "ERP_DIM$DIM_ORDER_EMPLOYEE_APP_BIZ_STATE_SUMMARY_EVENT_SERVICE"}, "typesFlow": {"type": "InvokeService", "serviceKey": "ERP_DIM$DIM_ORDER_EMPLOYEE_APP_QUERY_ENABLED_TYPE_EVENT_SERVICE"}, "identityFlow": {"type": "InvokeService", "serviceKey": "ERP_DIM$DIM_ORDER_EMPLOYEE_APP_IS_ADMIN_EVENT_SERVICE"}, "pagingFlow": {"type": "InvokeService", "serviceKey": "ERP_DIM$DIM_ORDER_EMPLOYEE_APP_LIST_PAGE_EVENT_SERVICE"}, "areaFlow": {"type": "InvokeService", "serviceKey": "ERP_DIM$DIM_ORDER_EMPLOYEE_APP_AREA_SEARCH_EVENT_SERVICE"}, "saveRemarkFlow": {"type": "InvokeService", "serviceKey": "ERP_DIM$DIM_ORDER_EMPLOYEE_APP_FIX_FILED_SAVE_SERVICE"}}}, {"key": "WorkOrderDetail", "name": "WorkOrderDetail", "type": "Widget", "title": "送装工单详情", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "送装工单详情"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"summaryFlow": {"type": "InvokeService", "serviceKey": "ERP_DIM$DIM_ORDER_EMPLOYEE_APP_BIZ_STATE_SUMMARY_EVENT_SERVICE"}, "typesFlow": {"type": "InvokeService", "serviceKey": "ERP_DIM$DIM_ORDER_EMPLOYEE_APP_QUERY_ENABLED_TYPE_EVENT_SERVICE"}, "identityFlow": {"type": "InvokeService", "serviceKey": "ERP_DIM$DIM_ORDER_EMPLOYEE_APP_IS_ADMIN_EVENT_SERVICE"}, "pagingFlow": {"type": "InvokeService", "serviceKey": "ERP_DIM$DIM_ORDER_EMPLOYEE_APP_LIST_PAGE_EVENT_SERVICE"}, "areaFlow": {"type": "InvokeService", "serviceKey": "ERP_DIM$DIM_ORDER_EMPLOYEE_APP_AREA_SEARCH_EVENT_SERVICE"}, "compressCompanyFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_EXPRESS_COM_QUERY_SERVICE"}, "saveFlow": {"type": "InvokeService", "serviceKey": "ERP_DIM$DIM_ORDER_EMPLOYEE_APP_CONFIRM_EVENT_SERVICE"}, "saveRemarkFlow": {"type": "InvokeService", "serviceKey": "ERP_DIM$DIM_ORDER_EMPLOYEE_APP_FIX_FILED_SAVE_SERVICE"}}}, {"key": "WorkOrderExpressDetail", "name": "WorkOrderExpressDetail", "type": "Widget", "title": "送装工单物流详情", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "送装工单物流详情"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"mainExpressFlow": {"type": "InvokeService", "serviceKey": "ERP_DIM$DIM_ORDER_EMPLOYEE_APP_QUERY_EXPRESS_TRACE_EVENT_SERVICE"}, "itemExpressFlow": {"type": "InvokeService", "serviceKey": "ERP_DIM$DIM_ORDER_EMPLOYEE_APP_QUERY_SEND_TRACE_SERVICE"}}}], "namespace": "terp-mobile", "endpointType": "APP", "metadata": {"branch": "feature/latest", "commitSha": "46cf558", "buildAt": "2025-02-05 10:57:31"}, "updateAt": "2025-02-05T02:57:45.916Z"}