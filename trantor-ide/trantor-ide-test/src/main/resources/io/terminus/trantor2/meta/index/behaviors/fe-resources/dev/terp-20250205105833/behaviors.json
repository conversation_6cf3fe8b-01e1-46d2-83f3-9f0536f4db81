{"behaviors": [{"key": "AccountAssignment", "name": "AccountAssignment", "type": "Widget", "title": "科目分配", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permission": true, "permissions": [{"type": "Self", "resourceType": "Container"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "accountAssignmentTarget", "key": "accountAssignment", "label": "科目分配"}], "editable": true, "defaultProps": {"accountAssignmentTarget": {}}, "staticProps": {"accountAssignmentServiceProps": {"pagingAccountancyFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_STRUCT_BUILD_TREE_EVENT_SERVICE"}, "saveFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AA_DISTRIBUTE_EVENT_SERVICE"}}, "loadSubjectPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_coa_type_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "loadOrganizationFindFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}}}, {"key": "AccountCashFlowRelationship", "name": "AccountCashFlowRelationship", "type": "Widget", "title": "科目现金流量关系", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "editable": true, "defaultProps": {}, "staticProps": {"loadOrgPagingFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService"}, "loadCoaTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_coa_type_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "loadCfsTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_cfs_type_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "gen_mat_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_mat_md_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_mat_cate_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_mat_cate_md_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_business_partner_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_business_partner_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_business_partner_md_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_business_partner_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_vend_info_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_vend_info_md_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_cust_info_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_cust_info_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_cust_info_md_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_cust_info_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_curr_type_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_curr_type_cf_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_sub_bank_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_sub_bank_cf_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_bank_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_bank_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_bank_cf_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_bank_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_tax_type_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_tax_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_tax_type_cf_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_tax_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "org_struct_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService"}, "org_struct_md_FindFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}}}, {"key": "AccountSubjects", "name": "AccountSubjects", "type": "Widget", "title": "会计科目", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "会计科目配置"}, "permission": true, "permissions": [{"type": "Self", "resourceType": "Container"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "createAccountSubjectTarget", "key": "createAccountSubject", "label": "新建"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "editAccountSubjectTarget", "key": "editAccountSubject", "label": "编辑"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "deleteAccountSubjectTarget", "key": "deleteAccountSubject", "label": "删除"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "detailAccountSubjectTarget", "key": "detailAccountSubject", "label": "查看"}], "editable": true, "defaultProps": {"createAccountSubjectTarget": {}, "editAccountSubjectTarget": {}, "deleteAccountSubjectTarget": {}, "detailAccountSubjectTarget": {}}, "staticProps": {"accountSubjectsServiceProps": {"loadRootFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AA_ALL_ROOT_HEAD_QUERY_EVENT_SERVICE"}, "loadChildrenFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AA_QUERY_BY_ROOT_AA_EVENT_SERVICE"}}, "loadSubjectPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_coa_type_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "loadGlmAaListFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AA_LIST_AD_QUERY_EVENT_SERVICE"}, "saveGlmAaListFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AA_DISTRIBUTE_AD_EVENT_SERVICE"}, "loadAuxiliaryDimensionPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ad_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}}}, {"key": "AccountancyOrganizationRelation", "name": "AccountancyOrganizationRelation", "type": "Widget", "title": "引用核算组织", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "引用核算组织配置"}, "permission": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"accountancyOrganizationRelationServiceProps": {"getPagingFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AS_SYS_ORG_CF_TREE_QUERY_EVENT_SERVICE"}}}}, {"key": "AllowColumns", "name": "AllowColumns", "type": "Widget", "title": "允许字段", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "允许字段配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"allowFieldsModelAlias": "ERP_GEN$gen_allow_field_item_cf", "queryFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ALLOW_FIELD_ITEM_PAGING_EVENT_SERVICE"}, "saveFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ALLOW_FIELD_ITEM_BATCH_SAVE_EVENT_SERVICE"}, "deleteFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ALLOW_FIELD_ITEM_DELETE_EVENT_SERVICE"}, "queryAllowFieldsByIdFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ALLOW_FIELD_ITEM_QUERY_LIST_BY_KEY_EVENT_SERVICE"}, "queryAllowFieldsListFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ALLOW_FIELD_LIST_EVENT_SERVICE"}}}}, {"key": "Approve", "name": "Approve", "type": "Widget", "title": "工作台审批", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "工作台审批"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"queryAllowFieldsFlow": {"type": "InvokeService", "serviceKey": "sys_common$API_TRANTOR_WORKFLOW_V2_TASK_INSTANCE_COUNT_GET"}}}, {"key": "ArrangeRules", "name": "ArrangeRules", "type": "Widget", "title": "凭证整理", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "凭证整理配置"}, "permission": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"arrangeRulesServiceProps": {"createFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VOUCHER_ORG_PROCESS_EVENT_SERVICE"}, "pagingFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VOUCHER_ORG_ITEM_PAGE_EVENT_SERVICE"}, "editFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VOUCHER_ORG_ITEM_EDIT_EVENT_SERVICE"}}}}, {"key": "ATP", "name": "ATP", "type": "Widget", "title": "ATP", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "ATP配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {"slsSoItemTypeModalAlias": "ERP_SCM$sls_so_item_type_cf", "matSlsModelAlias": "ERP_GEN$gen_mat_sls_md", "slsSoHeaderModelAlias": "ERP_SCM$sls_so_head_tr", "custSlsModelAlias": "ERP_GEN$gen_cust_sls_md"}, "staticProps": {"groupCheckFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ATP_GROUP_CHECK_SERVICE_SERVICE"}, "calNewCheckFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ATP_DOC_CHECK_EVENT_SERVICE"}, "querySlsSoItemTypeFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_SCM$sls_so_item_type_cf", "serviceKey": "ERP_SCM$SYS_FindDataByIdService"}, "queryMatSlsFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_sls_md", "serviceKey": "ERP_GEN$SYS_FindOneDataService"}, "querySlsSoHeaderFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_SCM$sls_so_head_tr", "serviceKey": "ERP_SCM$SYS_FindDataByIdService"}, "queryCustSlsFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_cust_sls_md", "serviceKey": "ERP_GEN$SYS_FindOneDataService"}, "queryUomFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "queryInvOrgFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}}}, {"key": "AuxiliaryBalance", "name": "AuxiliaryBalance", "type": "Widget", "title": "辅助余额", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permission": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"getAccountPeriodFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AB_TYPE_CALENDER_ITEM_LIST_EVENT_SERVICE"}, "getAccountSubjectsFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AB_TYPE_SUBJECT_ITEM_LIST_EVENT_SERVICE"}, "getPagingFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_ABS_BALANCE_QUERY_PAGE_EVENT_SERVICE"}}, "loadAccountingPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ab_type_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "getCurrencyPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getAuxiliaryDimensionPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ad_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "getMatPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getMatFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getOrgPagingFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService"}, "getOrgFindFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "getTaxTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_tax_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getTaxTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_tax_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getVendInfoPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getVendInfoFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getMatSlsPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_sls_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getMatSlsFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_sls_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}}}, {"key": "AuxiliaryDetails", "name": "AuxiliaryDetails", "type": "Widget", "title": "辅助明细", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "辅助明细配置"}, "permission": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"auxiliaryDetailsServiceProps": {"loadAccountingPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ab_type_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "getAccountPeriodFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AB_TYPE_CALENDER_ITEM_LIST_EVENT_SERVICE"}, "getAccountSubjectsFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AB_TYPE_SUBJECT_ITEM_LIST_EVENT_SERVICE"}, "getPagingFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_ABS_BALANCE_QUERY_PAGE_EVENT_SERVICE"}}}}, {"key": "AuxiliaryDimensionTypeRelation", "name": "AuxiliaryDimensionTypeRelation", "type": "Widget", "title": "引用辅助维度类型", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "引用辅助维度类型配置"}, "permission": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"auxiliaryDimensionTypeRelationServiceProps": {"getPagingFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_ADS_HEAD_QUERY_PAGE_EVENT_SERVICE"}}}}, {"key": "AuxiliaryDimensionType", "name": "AuxiliaryDimensionType", "type": "Widget", "title": "辅助维度类型", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permission": true, "permissions": [{"type": "Self", "resourceType": "Container"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "createAuxiliaryDimensionTypeTarget", "key": "createAuxiliaryDimensionType", "label": "创建"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "editAuxiliaryDimensionTypeTarget", "key": "editAuxiliaryDimensionType", "label": "编辑"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "deleteAuxiliaryDimensionTypeTarget", "key": "deleteAuxiliaryDimensionType", "label": "删除"}], "editable": true, "defaultProps": {"createAuxiliaryDimensionType": {}, "editAuxiliaryDimensionType": {}, "deleteAuxiliaryDimensionType": {}}, "staticProps": {"auxiliaryDimensionTypeServiceProps": {"getPagingFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_ADS_HEAD_QUERY_PAGE_EVENT_SERVICE"}, "deleteFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_ADS_HEAD_DELETE_BY_ID_EVENT_SERVICE"}}, "getSubjectPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_coa_type_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "getAuxiliaryDimensionPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ad_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}}}, {"key": "BankReconciliation", "name": "BankReconciliation", "type": "Widget", "title": "余额调节表", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"queryFlow": {"type": "InvokeService", "serviceKey": "ERP_GTR$GTR_BALANCE_ADJUST_PREVIEW_SERVICE"}, "journalPreviewFlow": {"type": "InvokeService", "serviceKey": "ERP_GTR$GTR_ADJUST_BANK_JOURNAL_PREVIEW_EVENT_SERVICE"}, "statementPreviewFlow": {"type": "InvokeService", "serviceKey": "ERP_GTR$GTR_ADJUST_BANK_STATEMENT_PREVIEW_EVENT_SERVICE"}}, "queryItemFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GTR$gtr_balance_adjust_item_tr", "serviceKey": "ERP_GTR$SYS_PagingDataService"}}}, {"key": "BankStatement", "name": "BankStatement", "type": "Widget", "title": "银企对账", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"queryFlow": {"type": "InvokeService", "serviceKey": "ERP_GTR$GTR_BALANCE_ADJUST_PREVIEW_SERVICE"}, "saveFlow": {"type": "InvokeService", "serviceKey": "ERP_GTR$GTR_BALANCE_ADJUST_SAVE_EVENT_SERVICE"}, "journalPreviewFlow": {"type": "InvokeService", "serviceKey": "ERP_GTR$GTR_ADJUST_BANK_JOURNAL_PREVIEW_EVENT_SERVICE"}, "statementPreviewFlow": {"type": "InvokeService", "serviceKey": "ERP_GTR$GTR_ADJUST_BANK_STATEMENT_PREVIEW_EVENT_SERVICE"}, "autoAccountFlow": {"type": "InvokeService", "serviceKey": "ERP_GTR$GTR_RECONCILIATION_AUTO_EVENT_SERVICE"}, "manualAccountFlow": {"type": "InvokeService", "serviceKey": "ERP_GTR$GTR_RECONCILIATION_MANUAL_EVENT_SERVICE"}, "cancelAccountFlow": {"type": "InvokeService", "serviceKey": "ERP_GTR$GTR_RECONCILIATION_CLEAN_EVENT_SERVICE"}, "queryTaskFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ASYNC_TASK_QUERY_EVENT_SERVICE"}}, "queryBankAccListFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_bank_acc_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "queryComTypeListFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_com_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "queryBankJournalListFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GTR$gtr_bank_journal_ba", "serviceKey": "ERP_GTR$SYS_PagingDataService"}, "queryBankStatementListFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GTR$gtr_bank_statement_ba", "serviceKey": "ERP_GTR$SYS_PagingDataService"}, "queryBankAccDetailFlow": {"type": "InvokeSystemService", "modelKey": "ERP_GEN$gen_bank_acc_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "queryCurrDetailFlow": {"type": "InvokeSystemService", "modelKey": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "queryComTypeDetailFlow": {"type": "InvokeSystemService", "modelKey": "ERP_GEN$gen_com_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "queryAdjustListFlow": {"type": "InvokeSystemService", "modelKey": "ERP_GTR$gtr_balance_adjust_head_tr", "serviceKey": "ERP_GTR$SYS_PagingDataService"}, "queryRuleListFlow": {"type": "InvokeSystemService", "modelKey": "ERP_GTR$gtr_reconciliation_rule_head_tr", "serviceKey": "ERP_GTR$SYS_PagingDataService"}}}, {"key": "BatchCode", "name": "BatchCode", "type": "Widget", "title": "批次号", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"queryDetailFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_QUERY_DETAIL_EVENT_SERVICE"}, "createFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_CREATE_BATCH_CODE_EVENT_SERVICE"}}}}, {"key": "BatchInButton", "name": "BatchInButton", "type": "Widget", "title": "批次生成按钮", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "批次生成配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {"label": "批次生成", "allowAdd": true, "allowRemove": true, "batchNumber": "select"}, "staticProps": {"queryBSTypeFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_BS_TYPE_QUERY_BY_MVM_TYPE_EVENT_SERVICE"}, "queryDNFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$DEL_DN_QUERY_BATCH_BY_RELATED_ITEM_SERVICE_SERVICE"}, "batchQueryAvailableFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_QUERY_AVAILABLE_BATCH_EVENT_SERVICE"}, "createBatchCodeFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_CREATE_BATCH_CODE_EVENT_SERVICE"}, "batchGenerateBatchCodeFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_CREATE_BATCH_BATCH_CODE_EVENT_SERVICE"}, "queryMatCharaFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_QUERY_MAT_BATCH_CHARA_EVENT_SERVICE"}, "batchQuerySuggestFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_QUERY_SUGGEST_BATCH_EVENT_SERVICE"}, "queryBatchPagingFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_CREATE_PAGE_QUERY_EVENT_SERVICE"}, "queryBatchByCodesFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_QUERY_DETAIL_BY_CODES_EVENT_SERVICE"}, "queryButtonInfoFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MAT_QUERY_MAT_INV_BY_INV_ORG_AND_INV_LOC_EVENT_SERVICE"}, "filterBatchParamsFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$DEL_DN_BATCH_CONTROL_BATCH_EVENT_SERVICE"}, "queryTemplateFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_FIND_IMPORT_TEMPLATE_EVENT_SERVICE"}}}, {"key": "BatchOutButton", "name": "BatchOutButton", "type": "Widget", "title": "批次确认按钮", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "批次确认配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {"label": "批次确认", "allowAdd": true, "allowRemove": true}, "staticProps": {"purchaseBatchConfirmFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$PO_ITEM_QUERY_BATCH_INFO_BY_PO_ITEM_EVENT_SERVICE"}, "salesBatchConfirmFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$DEL_DN_BATCH_COMFIRM_SERVICE_SERVICE"}, "queryBatchPagingFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$DEL_DN_BATCH_CONFIRM_PAGING_EVENT_SERVICE"}, "queryButtonInfoFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MAT_QUERY_MAT_INV_BY_INV_ORG_AND_INV_LOC_EVENT_SERVICE"}, "queryMatFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}}}, {"key": "BmsBudgetAnalysis", "name": "BmsBudgetAnalysis", "type": "Widget", "title": "预算分析", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "预算分析配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"getFiltersFlow": {"type": "InvokeService", "serviceKey": "ERP_CO$COA_BDG_ANALYZE_FIND_QUERY_PARAMS_EVENT_SERVICE"}, "generateTaskFlow": {"type": "InvokeService", "serviceKey": "ERP_CO$COA_BDG_ANALYZE_TASK_SAVE_EVENT_SERVICE"}, "getTaskDetailFlow": {"type": "InvokeService", "serviceKey": "ERP_CO$COA_BDG_ANALYZE_TASK_DETAIL_ACTION_EVENT_SERVICE"}, "getTableDataFlow": {"type": "InvokeService", "serviceKey": "ERP_CO$COA_BDG_ANALYZE_QUERY_DETAIL_EVENT_SERVICE"}, "getPopoverTextFlow": {"type": "InvokeService", "serviceKey": "ERP_CO$COA_BDG_ANALYZE_HISTORY_QUERY_FROM_ES_EVENT_SERVICE"}}, "coaBdgModelMdIdPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_CO$coa_bdg_model_md", "serviceKey": "ERP_CO$SYS_PagingDataService"}, "coaBdgModelMdIdFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_CO$coa_bdg_model_md", "serviceKey": "ERP_CO$SYS_FindDataByIdService"}, "coaDimenCombinCfIdPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_CO$coa_dimen_combin_cf", "serviceKey": "ERP_CO$SYS_PagingDataService"}, "coaDimenCombinCfIdFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_CO$coa_dimen_combin_cf", "serviceKey": "ERP_CO$SYS_FindDataByIdService"}}}, {"key": "BmsBudgetOrderingTable", "name": "BmsBudgetOrderingTable", "type": "Widget", "title": "预算下达", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "预算下达配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {"customValidate": true}, "staticProps": {"containerProps": {"serviceProps": {"getColumnsFlow": {"type": "InvokeService", "serviceKey": "ERP_CO$COA_BDG_RELEASE_TABLE_HEADER_QUERY_SERVICE_SERVICE"}, "getTableDataFlow": {"type": "InvokeService", "serviceKey": "ERP_CO$COA_BDG_PAGING_DIMEN_MEMBER_NEW_SERVICE"}, "getRowDataByDimenFlow": {"type": "InvokeService", "serviceKey": "ERP_CO$COA_BDG_ADJUST_ITEM_AMT_QUERY_EVENT_SERVICE"}}}}}, {"key": "BmsBudgetPeriod", "name": "BmsBudgetPeriod", "type": "Widget", "title": "预算期间", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {"customValidate": true}, "staticProps": {"serviceProps": {"queryTreeDataFlow": {"type": "InvokeService", "serviceKey": "ERP_CO$COA_BDG_CALENDAR_ITEM_QUERY_EVENT_SERVICE"}}}}, {"key": "BMSRuleAdd", "name": "BMSRuleAdd", "type": "Widget", "title": "预算规则新增", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "预算规则新增"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"loadBdgModelFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_CO$coa_bdg_model_md", "serviceKey": "ERP_CO$SYS_PagingDataService"}, "loadBillFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_CO$coa_bdg_bill_register_cf", "serviceKey": "ERP_CO$SYS_PagingDataService"}, "findBillFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_CO$coa_bdg_bill_register_cf", "serviceKey": "ERP_CO$SYS_FindListDataService"}, "loadDimenCombinFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_CO$coa_dimen_combin_cf", "serviceKey": "ERP_CO$SYS_PagingDataService"}, "loadDimenByIdFlow": {"type": "InvokeService", "serviceKey": "ERP_CO$COA_BDG_SELECT_ALL_DIMEN_BY_ID_EVENT_SERVICE"}}}, {"key": "BMSRuleEdit", "name": "BMSRuleEdit", "type": "Widget", "title": "预算规则编辑/详情", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "预算规则编辑/详情"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"COA_BDG_RULE_QUERY_DETAIL_EVENT_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_CO$COA_BDG_RULE_QUERY_DETAIL_EVENT_SERVICE"}, "COA_BDG_RULE_UPDATE_EVENT_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_CO$COA_BDG_RULE_UPDATE_EVENT_SERVICE"}, "loadBillFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_CO$coa_bdg_bill_register_cf", "serviceKey": "ERP_CO$SYS_PagingDataService"}, "findBillFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_CO$coa_bdg_bill_register_cf", "serviceKey": "ERP_CO$SYS_FindListDataService"}}}, {"key": "BMSStrategyDetail", "name": "BMSStrategyDetail", "type": "Widget", "title": "维度匹配策略详情", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "维度匹配策略详情"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"BMSStrategyDetailServiceProps": {"queryBdgFlow": {"type": "InvokeService", "serviceKey": "ERP_CO$COA_BDG_STRATEGY_QUERY_EVENT_SERVICE"}, "queryAllDimFlow": {"type": "InvokeService", "serviceKey": "ERP_CO$COA_BDG_SELECT_ALL_DIMEN_BY_ID_EVENT_SERVICE"}}}}, {"key": "BMSStrategy", "name": "BMSStrategy", "type": "Widget", "title": "预算策略", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "预算策略配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {"allowFieldsModelAlias": "ERP_CO$coa_bdg_strategy_cf"}, "staticProps": {"queryAllowFieldsFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_CO$coa_bdg_strategy_cf", "serviceKey": "ERP_CO$SYS_PagingDataService"}, "queryDimStFlow": {"type": "InvokeService", "serviceKey": "ERP_CO$COA_BDG_SELECT_ALL_DIMEN_BY_ID_EVENT_SERVICE"}, "COA_BDG_STRATEGY_QUERY_EVENT_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_CO$COA_BDG_STRATEGY_QUERY_EVENT_SERVICE"}, "pageBillFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_CO$coa_bdg_bill_event_cf", "serviceKey": "ERP_CO$SYS_PagingDataService"}, "pageCoaPerformanceFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_CO$coa_performance_type_cf", "serviceKey": "ERP_CO$SYS_PagingDataService"}, "coaBdgBillRegisterCfFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_CO$coa_bdg_bill_register_cf", "serviceKey": "ERP_CO$SYS_FindDataByIdService"}, "COA_BDG_STRATEGY_UPDATE_SAVE_EVENT_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_CO$COA_BDG_STRATEGY_UPDATE_SAVE_EVENT_SERVICE"}, "COA_BILL_REGISTER_QUERY_SELF_EVENT_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_CO$COA_BILL_REGISTER_QUERY_SELF_EVENT_SERVICE"}, "genCurrTypeCfFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "COA_BDG_PAGING_DIMEN_MEMBER_NEW_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_CO$COA_BDG_PAGING_DIMEN_MEMBER_NEW_SERVICE"}, "coaMappingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_CO$coa_mapping_head_md", "serviceKey": "ERP_CO$SYS_PagingDataService"}}}, {"key": "CollectionFinancialAuditing", "name": "CollectionFinancialAuditing", "type": "Widget", "title": "收款钩稽", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "收款钩稽配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"vendInfoModelAlias": "ERP_GEN$gen_vend_info_md", "orgModelAlias": "sys_common$org_struct_md", "custModelAlias": "ERP_GEN$gen_cust_info_md", "finDocTypeModelAlias": "ERP_FIN$fin_doc_type_md", "currencyModelAlias": "ERP_GEN$gen_curr_type_cf", "funCmPnTypeModelAlias": "ERP_FIN$fin_cm_pn_type_md", "matchFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$PCC_MATCH_CLEAR_EVENT_SERVICE"}, "forceFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$PCC_FORCE_CLEAR_EVENT_SERVICE"}, "apQueryESFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$AR_SCHL_QUERY_FROM_ES_EVENT_SERVICE"}, "fnQueryESFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$PN_CLEAR_QUERY_FROM_ES_EVENT_SERVICE"}, "apQueryFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$AR_SCHL_QUERY_EVENT_SERVICE"}, "fnQueryFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$PN_CLEAR_QUERY_EVENT_SERVICE"}}, "custInfoPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_cust_info_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "queryVendInfoFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "queryCustFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_cust_info_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "queryOrgFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "queryFinDocTypeFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FIN$fin_doc_type_md", "serviceKey": "ERP_FIN$SYS_FindDataByIdService"}, "queryCurrencyFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "queryFunCmPnTypeFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FIN$fin_cm_pn_type_md", "serviceKey": "ERP_FIN$SYS_FindDataByIdService"}}}, {"key": "CompanyWebview", "name": "CompanyWebview", "type": "Widget", "title": "天眼查专业版", "group": "Portal业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "天眼查专业版通用表单配置"}, "editable": true, "defaultProps": {}, "staticProps": {}}, {"key": "ConditionSelect", "name": "ConditionSelect", "type": "Widget", "title": "条件选择", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "displayNamePath": "label", "designerProps": {"type": "formily-react", "title": "表单字段配置"}, "widgetDesignerProps": {"widgetType": ["Display"], "fieldType": ["TEXT"], "title": "条件组选择"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "icon": "zidingyiyemian", "defaultProps": {"label": "条件选择", "ConditionSelectServiceProps": {}}, "staticProps": {}}, {"key": "ContractDetail", "name": "ContractDetail", "type": "Widget", "title": "合同模板富文本详情", "group": "动态表单组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "合同模板详情配置"}, "editable": true, "defaultProps": {}, "staticProps": {}}, {"key": "ContractConsumer", "name": "ContractConsumer", "type": "Widget", "title": "合同模板编制", "group": "动态表单组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "合同模板编制配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"contractConsumerProps": {"toPdfFlow": {"type": "InvokeService", "serviceKey": "contract_management_module$full_text_to_pdf"}, "getPlaceholderFlow": {"type": "InvokeService", "serviceKey": "contract_management_module$CT_DRAFT_PREFIX_QUERY_SERVICE"}}}}, {"key": "ContractTemplate", "name": "ContractTemplate", "type": "Widget", "title": "合同模板创建", "group": "动态表单组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "合同模板创建配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"contractProps": {"placeholderFlow": {"type": "InvokeService", "serviceKey": "contract_management_module$query_tpl_prefix"}, "bizTypeFlow": {"type": "InvokeService", "serviceKey": "contract_management_module$query_tpl_biz_type"}, "queryDetailFlow": {"type": "InvokeSystemService", "modelAlias": "contract_management_module$ct_template_info", "serviceKey": "SYS_SaveDataService"}, "findDataFlow": {"type": "InvokeSystemService", "modelAlias": "contract_management_module$ct_template_info", "serviceKey": "SYS_FindDataByIdService"}, "modelAlias": "contract_management_module$ct_template_info"}}}, {"key": "CustomFieldsCreator", "name": "CustomFieldsCreator", "type": "Widget", "title": "自定义表字段", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "自定义组件配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {}}, {"key": "DetailMainInfo", "name": "DetailMainInfo", "type": "Container", "title": "详情主信息", "group": "TERP业务组件", "droppable": true, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "详情主信息配置"}, "editable": true, "defaultProps": {}, "staticProps": {}}, {"key": "DynamicFormDesigner", "name": "DynamicFormDesigner", "type": "Widget", "title": "动态表单引设计", "group": "动态表单组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "displayNamePath": "label", "designerProps": {"type": "formily-react", "title": "表单字段配置"}, "editable": true, "icon": "zidingyiyemian", "defaultProps": {"label": "动态表单设计", "name": ""}, "staticProps": {}}, {"key": "DynamicFormViewer", "name": "DynamicFormViewer", "type": "Widget", "title": "动态表单引用", "group": "动态表单组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "displayNamePath": "label", "designerProps": {"type": "formily-react", "title": "表单字段配置"}, "editable": true, "icon": "zidingyiyemian", "defaultProps": {"label": "动态表单引用", "name": ""}, "staticProps": {}}, {"key": "EhrOrgList", "name": "EhrOrgList", "type": "Widget", "title": "组织列表", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "组织页面配置"}, "permissions": [{"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "createOrgTarget", "key": "createOrg", "label": "新建"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key": "editOrg", "label": "编辑"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "disable<PERSON><PERSON><PERSON><PERSON><PERSON>", "key": "disableOrg", "label": "停用"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "enableOrgTarget", "key": "enableOrg", "label": "启用"}, {"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {"createOrgTarget": {}, "editOrgTarget": {}, "disableOrgTarget": {}, "enableOrgTarget": {}}, "staticProps": {"orgLoadFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_STRUCT_MD_FIND_BY_PID_EVENT_SERVICE"}, "orgQueryCurrentOrgFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_STRUCT_QUERY_CURRENT_COM_ORG_EVENT_SERVICE"}, "orgFindTypeFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_STRUCT_MD_FIND_TYPE_EVENT_SERVICE"}, "orgDetailFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_STRUCT_MD_DETAIL_EVENT_SERVICE"}, "orgSaveFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_STRUCT_MD_SAVE_EVENT_SERVICE"}, "orgDeleteFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_STRUCT_MD_DELETE_EVENT_SERVICE"}, "orgDisabledFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_STRUCT_MD_DISABLED_EVENT_SERVICE"}, "orgEnableFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_STRUCT_MD_ENABLED_EVENT_SERVICE"}, "orgSearchFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_STRUCT_MD_SEARCH_EVENT_SERVICE"}, "orgHistoryFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_STRUCT_MD_HISTORY_DETAIL_EVENT_SERVICE"}, "orgDeleteRoleFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_ADMIN_EMPLOYEE_ORG_LINK_DELETE_EVENT_SERVICE"}, "orgSaveRoleFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_ADMIN_EMPLOYEE_ORG_LINK_SAVE_EVENT_SERVICE"}, "orgDownFileFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_STRUCT_FIND_IMPORT_TEMPLATE_EVENT_SERVICE"}, "orgSearchEmployeeFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_EMPLOYEE_PAGE_EVENT_SERVICE"}, "orgImportServiceFlow": {"type": "InvokeService", "serviceKey": "sys_common$API_GEI_TASK_IMPORT_DIRECT_BY_OSS_POST"}, "orgObjectAttrPageFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_OBJECT_ATTR_PAGING"}, "orgObjectAttrBatchQueryFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_OBJECT_ATTR_BATCH_QUERY_DETAIL"}, "orgDimensionListQueryFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_DIMENSION_QUERY_ENABLE_LIST_EVENT_SERVICE"}, "orgFindParentFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindTreeChildrenDataService"}, "orgDicSearchFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_dict_item_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdsService"}, "orgDicPageFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_dict_item_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "orgFindChildFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_ReverseConstructTreeService"}, "orgDimensionDataFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_dimension_cf", "serviceKey": "sys_common$SYS_FindOneDataService"}, "loadRoleListFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_identity_cf", "serviceKey": "sys_common$SYS_PagingDataService"}, "loadOrgListFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService"}, "loadPartnerListFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_business_partner_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}}}, {"key": "EhrOrgProperty", "name": "EhrOrgProperty", "type": "Widget", "title": "组织属性", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"deleteFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ATTR_DELETE_EVENT_SERVICE"}, "saveFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ATTR_SAVE_EVENT_SERVICE"}, "enableFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ATTR_ENABLED_EVENT_SERVICE"}, "queryAvailableFieldFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ATTR_QUERY_AVAILABLE_BIND_FILED_EVENT_SERVICE"}, "disableAttrFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_attr_cf", "serviceKey": "ERP_GEN$SYS_MasterData_DisableDataService"}, "loadAttrListFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_attr_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "loadAttrDetailFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_attr_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "loadAttrClassListFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_attr_class_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "loadAttrClassDetailFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_attr_class_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "loadDictListFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_dict_head_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "loadDictDetailFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_dict_head_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}}}, {"key": "FeatureClassTableForm", "name": "FeatureClassTableForm", "type": "Widget", "title": "特征分类表格表单", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"queryCharaFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_chara_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "queryPresetValueFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_chara_preset_value_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "queryCharaByCharaClassFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_BATCH_RANGE_QUERY_CHARA_BY_CHARA_CLASS_SERVICE"}}}, {"key": "FeatureManagement", "name": "FeatureManagement", "type": "Widget", "title": "特征管理", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"matCreateFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MAT_CREATE_CHARA_SERVICE_SERVICE"}, "matUpdateFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MAT_UPDATE_CHARA_SERVICE_SERVICE"}, "queryCharaDetailFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_CHARA_DETAIL_SERVICE_SERVICE"}, "queryFunctionFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$RULE_ENGINE_QUERY_ALL_FACTOR_ACTION_EVENT"}, "queryModelFieldsFlow": {"type": "InvokeApi", "url": "/api/service/erp-md/match/queryModelAndFieldList", "method": "POST"}}, "updateCharaFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_chara_md", "serviceKey": "ERP_GEN$SYS_UpdateDataByIdService"}, "deleteCharaFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_chara_md", "serviceKey": "ERP_GEN$SYS_DeleteDataByIdService"}, "queryCharaFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_chara_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "queryCurrencyTypeFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "queryUomTypeFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "queryAllowFieldsFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_allow_field_item_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}}}, {"key": "FeatureValueTableForm", "name": "FeatureValueTableForm", "type": "Widget", "title": "特征值表格表单", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"queryMatCharaFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_QUERY_MAT_BATCH_CHARA_EVENT_SERVICE"}, "batchQueryDetailFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_QUERY_DETAIL_EVENT_SERVICE"}}}}, {"key": "FeatureValueText", "name": "FeatureValueText", "type": "Widget", "title": "特征数据", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"queryCharaDetailFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MAT_CHARA_DETAIL_SERVICE_SERVICE"}}}}, {"key": "FeatureValue", "name": "FeatureValue", "type": "Widget", "title": "特征值", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"queryCharaDetailFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MAT_CHARA_DETAIL_SERVICE_SERVICE"}}}}, {"key": "FeatureValueRead", "name": "FeatureValueRead", "type": "Widget", "title": "特征值展示", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {}}, {"key": "FieldExprEditor", "name": "FieldExprEditor", "type": "Widget", "title": "模型函数计算", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "displayNamePath": "label", "designerProps": {"type": "formily-react", "title": "表单字段配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "icon": "zidingyiyemian", "defaultProps": {}, "staticProps": {"PLN_MRP_SUPPLY_DEMAND_QUERY_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_MRP_SUPPLY_DEMAND_QUERY_SERVICE"}}}, {"key": "FunctionEditor", "name": "FunctionEditor", "type": "Widget", "title": "函数编辑器", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"queryFunctionFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$RULE_ENGINE_QUERY_ALL_FACTOR_ACTION_EVENT"}, "getModelFieldsFlow": {"type": "InvokeApi", "url": "/api/service/erp-md/match/queryModelAndFieldList", "method": "POST"}}}}, {"key": "FundingPlanPeriod", "name": "FundingPlanPeriod", "type": "Widget", "title": "资金计划选择期间", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "资金计划选择期间配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {"customValidate": true}, "staticProps": {"serviceProps": {"flow": {"type": "InvokeService", "serviceKey": "ERP_GTR$GTR_FUND_PLAN_PLAN_CALENDAR_QUERY_EVENT_SERVICE"}}, "planSchemeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GTR$gtr_plan_scheme", "serviceKey": "ERP_GTR$SYS_FindDataByIdService"}}}, {"key": "FundingPlanTable", "name": "FundingPlanTable", "type": "Widget", "title": "资金计划表格", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {"customValidate": true}, "staticProps": {"containerProps": {"getPlanProjectFlow": {"type": "InvokeService", "serviceKey": "ERP_GTR$GTR_FUND_PLAN_PAGE_EVENT_SERVICE"}, "getPlanProjectRecordFlow": {"type": "InvokeService", "serviceKey": "TERP_MIGRATE$GTR_FUND_PLAN_ROLL_AMT_QUERY__EVEN"}}}}, {"key": "GeneralLedgerCashFlowInitialization", "name": "GeneralLedgerCashFlowInitialization", "type": "Widget", "title": "现金流量初始化", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"saveCashflowFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$Fin_GLM_IBC_CASH_ITEM_SUBMIT_EVENT_SERVICE"}, "queryRtTypeFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_IB_RATE_QUERY_BY_CURR_AND_RT_EVENT_SERVICE"}, "queryTrialBalanceFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_IBC_CASH_ITEM_QUERY_EVENT_SERVICE"}}, "abTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ab_type_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "abTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ab_type_cf", "serviceKey": "ERP_FI$SYS_FindDataByIdService"}, "currIdPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "currIdFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "comPeriodFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_calendar_item_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "asOrgFindFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}}}, {"key": "GeneralLedgerCashFlowInquiry", "name": "GeneralLedgerCashFlowInquiry", "type": "Widget", "title": "现金流量查询", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"queryCashflowFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_CASH_QUERY_LIST_EVENT_SERVICE"}, "queryCalendarItemFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AB_TYPE_CALENDER_ITEM_LIST_EVENT_SERVICE"}}, "abTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ab_type_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "abTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ab_type_cf", "serviceKey": "ERP_FI$SYS_FindDataByIdService"}, "currIdPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "currIdFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}}}, {"key": "GeneralLedgerEndTermTransferButton", "name": "GeneralLedgerEndTermTransferButton", "type": "Widget", "title": "期末结转凭证新建按钮", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "期末结转凭证新建按钮配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {"executionMethod": "BLANK_ENTRY"}, "staticProps": {"serviceProps": {"getVoucherIdFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_RULE_VE_EXECUTE_EVENT_SERVICE"}}, "rulePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_rule_ve_head_tr", "serviceKey": "ERP_FI$SYS_PagingDataService"}}}, {"key": "GeneralLedgerOpeningBalance", "name": "GeneralLedgerOpeningBalance", "type": "Widget", "title": "期初余额", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"getUserAbTypeFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AB_INFO_QUERY_BY_USER_ID_EVENT_SERVICE"}, "queryRtTypeFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_IB_RATE_QUERY_BY_CURR_AND_RT_EVENT_SERVICE"}, "queryTrialBalanceFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_IBE_TRIAL_BALANCE_VIEW_QUERY_EVENT_SERVICE"}, "saveDimensionalInfoNumberFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_IBE_AD_CF_TABLE_QUERY_EVENT_SERVICE"}, "getDimensionalInfoNumberFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_IBE_AD_CF_TABLE_QUERY_EVENT_SERVICE"}, "getVouchNumberFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_IBE_ITEM_TABLE_QUERY_EVENT_SERVICE"}, "getDimensionalInfoFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VE_QUERY_ADS_BY_ITEM_EVENT_SERVICE"}, "saveOpeningBalanceFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_IBE_ITEM_TABLE_SUBMIT_EVENT_SERVICE"}, "saveDimensionalValuesFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_IBE_AD_INFO_SUBMIT_EVENT_SERVICE"}, "abTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ab_type_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "abTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ab_type_cf", "serviceKey": "ERP_FI$SYS_FindDataByIdService"}, "currIdPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "currIdFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "comPeriodFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_calendar_item_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "coaTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_coa_type_cf", "serviceKey": "ERP_FI$SYS_FindDataByIdService"}, "uomTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getMatPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getMatFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getOrgPagingFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService"}, "getOrgFindFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "getTaxTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_tax_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getTaxTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_tax_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getVendInfoPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getVendInfoFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getMatSlsPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_sls_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getMatSlsFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_sls_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_mat_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_mat_md_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_mat_cate_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_mat_cate_md_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_business_partner_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_business_partner_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_business_partner_md_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_business_partner_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_vend_info_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_vend_info_md_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_cust_info_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_cust_info_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_cust_info_md_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_cust_info_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_curr_type_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_curr_type_cf_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_sub_bank_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_sub_bank_cf_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_bank_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_bank_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_bank_cf_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_bank_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_tax_type_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_tax_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_tax_type_cf_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_tax_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_uom_type_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_uom_type_cf_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_calendar_item_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_calendar_item_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_calendar_item_cf_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_calendar_item_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "org_struct_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService"}, "org_struct_md_FindFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "fin_cm_al_type_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "serviceKey": "ERP_FIN$SYS_PagingDataService"}, "fin_cm_al_type_md_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "serviceKey": "ERP_FIN$SYS_FindDataByIdService"}}}, {"key": "GeneralLedgerVoucherEntry", "name": "GeneralLedgerVoucherEntry", "type": "Widget", "title": "凭证录入", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"getUserAbTypeFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AB_INFO_QUERY_BY_USER_ID_EVENT_SERVICE"}, "userFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$user", "serviceKey": "ERP_FI$SYS_FindDataByIdService"}, "getCalendarItemIdFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VE_AD_PERIOD_EVENT_SERVICE"}, "getOrigCurrAmtFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VE_LOAD_ORIG_CURR_AMT_EVENT_SERVICE"}, "getVouchNumberFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VE_VOUCH_NUMBER_EVENT_SERVICE"}, "getDimensionFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VE_QUERY_ADS_BY_ITEM_EVENT_SERVICE"}, "getCashFlowAnalysisFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_CASH_FLOW_ANALYSIS_EVENT_SERVICE"}, "getProjectTreeFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AB_TYPE_ITEM_TREE_LIST_EVENT_SERVICE"}, "getSubjectTreeFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AA_TREE_QUERY_BY_AB_COA_EVENT_SERVICE"}, "getSubjectListFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AA_LIST_QUERY_BY_CODE_OR_NAME_SERVICE"}, "autoAnalysisFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_CASH_FLOW_AUTO_ANALYSIS_EVENT_SERVICE"}, "saveFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VE_SAVE_EVENT_SERVICE"}, "submitFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VE_SUBMIT_EVENT_SERVICE"}, "queryVoucherByIdFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VE_QUERY_DETAIL_EVENT_SERVICE"}, "abItemFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "serviceKey": "ERP_FI$SYS_FindDataByIdService"}, "aaHeadIdFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_aa_head_cf", "serviceKey": "ERP_FI$SYS_FindDataByIdService"}, "abTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ab_type_cf", "serviceKey": "ERP_FI$SYS_FindDataByIdService"}, "abTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ab_type_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "asOrgIdFindFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "calendarItemIdFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_calendar_item_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "vtTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_vt_type_cf", "serviceKey": "ERP_FI$SYS_FindDataByIdService"}, "vtTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_vt_type_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "currIdPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "currIdFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "rtTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_exchange_rate_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "rtTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_exchange_rate_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "unitFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getMatPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getMatFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getOrgPagingFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService"}, "getOrgFindFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "getTaxTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_tax_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getTaxTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_tax_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getVendInfoPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getVendInfoFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getMatSlsPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_sls_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getMatSlsFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_sls_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_mat_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_mat_md_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_mat_cate_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_mat_cate_md_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_business_partner_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_business_partner_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_business_partner_md_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_business_partner_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_vend_info_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_vend_info_md_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_cust_info_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_cust_info_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_cust_info_md_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_cust_info_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_curr_type_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_curr_type_cf_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_sub_bank_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_sub_bank_cf_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_bank_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_bank_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_bank_cf_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_bank_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_tax_type_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_tax_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_tax_type_cf_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_tax_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_uom_type_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_uom_type_cf_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "gen_calendar_item_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_calendar_item_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_calendar_item_cf_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_calendar_item_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "org_struct_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService"}, "org_struct_md_FindFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "fin_cm_al_type_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "serviceKey": "ERP_FIN$SYS_PagingDataService"}, "fin_cm_al_type_md_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "serviceKey": "ERP_FIN$SYS_FindDataByIdService"}}}, {"key": "PrintVoucher", "name": "PrintVoucher", "type": "Widget", "title": "打印凭证录入", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "打印凭证录入配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {}}, {"key": "GlobalBillTrace", "name": "GlobalBillTrace", "type": "Widget", "title": "单据追踪", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "displayNamePath": "label", "designerProps": {"type": "formily-react", "title": "表单字段配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "icon": "zidingyiyemian", "defaultProps": {"label": "单据追踪", "name": ""}, "staticProps": {"GlobalBillTraceServiceProps": {"GEN_DOC_TRACE_GLOBAL_LINK_QUERY_EVENT_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_DOC_TRACE_GLOBAL_LINK_QUERY_EVENT_SERVICE"}}}}, {"key": "GlobalPostScript", "name": "GlobalPostScript", "type": "Widget", "title": "付款渠道规则", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "displayNamePath": "label", "designerProps": {"type": "formily-react", "title": "表单字段配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "icon": "zidingyiyemian", "defaultProps": {"label": "付款渠道规则", "name": ""}, "staticProps": {"gen_bank_acc_mdFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_bank_acc_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gtr_payment_method_cfFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GTR$gtr_payment_method_cf", "serviceKey": "ERP_GTR$SYS_PagingDataService"}, "GTR_RULE_TRANSFER_NOTE_META_INFO_EVENT_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_GTR$GTR_RULE_TRANSFER_NOTE_META_INFO_EVENT_SERVICE"}, "GTR_RULE_PAYMENT_TASK_META_EVENT_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_GTR$GTR_RULE_PAYMENT_TASK_META_EVENT_SERVICE"}}}, {"key": "GlobalTransferNote", "name": "GlobalTransferNote", "type": "Widget", "title": "转账附言-附言选择", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "displayNamePath": "label", "designerProps": {"type": "formily-react", "title": "表单字段配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "icon": "zidingyiyemian", "defaultProps": {"label": "转账附言", "name": ""}, "staticProps": {"GlobalTransferNoteServiceProps": {"GTR_RULE_PAYMENT_TASK_META_EVENT_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_GTR$GTR_RULE_PAYMENT_TASK_META_EVENT_SERVICE"}, "GTR_RULE_TRANSFER_NOTE_META_INFO_EVENT_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_GTR$GTR_RULE_TRANSFER_NOTE_META_INFO_EVENT_SERVICE"}}}}, {"key": "GlobalTransfer", "name": "GlobalTransfer", "type": "Widget", "title": "转账附言", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "displayNamePath": "label", "designerProps": {"type": "formily-react", "title": "表单字段配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "icon": "zidingyiyemian", "defaultProps": {"label": "转账附言", "name": ""}, "staticProps": {"GlobalTransferServiceProps": {"GTR_RULE_PAYMENT_TASK_META_EVENT_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_GTR$GTR_RULE_PAYMENT_TASK_META_EVENT_SERVICE"}, "GTR_RULE_TRANSFER_NOTE_META_INFO_EVENT_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_GTR$GTR_RULE_TRANSFER_NOTE_META_INFO_EVENT_SERVICE"}}}}, {"key": "IndicatorCard", "name": "IndicatorCard", "type": "Widget", "title": "我的待办", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "我的待办"}, "editable": true, "defaultProps": {}, "staticProps": {}}, {"key": "IssuingInvoiceFinancialAuditing", "name": "IssuingInvoiceFinancialAuditing", "type": "Widget", "title": "开票钩稽", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "开票钩稽配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"vendInfoModelAlias": "ERP_GEN$gen_vend_info_md", "orgModelAlias": "sys_common$org_struct_md", "custModelAlias": "ERP_GEN$gen_cust_info_md", "finDocTypeModelAlias": "ERP_FIN$fin_doc_type_md", "currencyModelAlias": "ERP_GEN$gen_curr_type_cf", "funCmPnTypeModelAlias": "ERP_FIN$fin_cm_pn_type_md", "matchFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$IBC_SUBMIT_EVENT_SERVICE"}, "forceFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$IBC_FORCE_CLEAR_MANUAL_EVENT_SERVICE"}, "queryAPESFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$AR_QUERY_FROM_ES_EVENT_SERVICE"}, "queryFNESFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$SB_QUERY_FROM_ES_EVENT_SERVICE"}, "queryAPFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$AR_QUERY_EVENT_SERVICE"}, "queryFNFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$SB_QUERY_EVENT_SERVICE"}}, "custInfoPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_cust_info_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "queryVendInfoFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "queryMaterialFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "queryOrgFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "queryCustFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_cust_info_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "queryFinDocTypeFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FIN$fin_doc_type_md", "serviceKey": "ERP_FIN$SYS_FindDataByIdService"}, "queryCurrencyFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "queryFunCmPnTypeFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FIN$fin_cm_pn_type_md", "serviceKey": "ERP_FIN$SYS_FindDataByIdService"}}}, {"key": "KeyMappingEdit", "name": "KeyMappingEdit", "type": "Widget", "title": "关键字映射表", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "关键字映射表"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"FIN_AES_ACC_EXH_HEAD_CF_DETAIL_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_ACC_EXH_HEAD_CF_DETAIL_SERVICE"}, "FIN_AES_LEDG_EXH_HEAD_CF_DETAIL_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_LEDG_EXH_HEAD_CF_DETAIL_SERVICE"}, "FIN_AES_EXH_MAPPING_HEAD_CF_DETAIL_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_EXH_MAPPING_HEAD_CF_DETAIL_SERVICE"}, "FIN_AES_ACC_EXH_HEAD_CF_SAVE_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_ACC_EXH_HEAD_CF_SAVE_SERVICE"}, "FIN_AES_LEDG_EXH_HEAD_CF_SAVE_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_LEDG_EXH_HEAD_CF_SAVE_SERVICE"}, "FIN_AES_EXH_MAPPING_HEAD_CF_SAVE_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_EXH_MAPPING_HEAD_CF_SAVE_SERVICE"}, "FIN_AES_USABLE_MODEL_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_USABLE_MODEL_SERVICE"}, "FIN_AES_OUTER_OBJECT_PAGE_MOD_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_OUTER_OBJECT_PAGE_MOD_SERVICE"}, "fin_glm_aa_head_cf_terpPageFlow": {"type": "InvokeSystemService", "modelAlias": "TERP_MIGRATE$fin_glm_aa_head_cf_terp", "serviceKey": "sys_common$SYS_PagingDataService"}, "fin_glm_calculate_ab_type_viewPageFlow": {"type": "InvokeSystemService", "modelAlias": "TERP_MIGRATE$fin_glm_calculate_ab_type_view", "serviceKey": "sys_common$SYS_PagingDataService"}}}, {"key": "MaterialIndicatorCard", "name": "MaterialIndicatorCard", "type": "Widget", "title": "物料指标卡", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "物料指标卡"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"MaterialIndicatorServiceProps": {"valueFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$GEN_MAT_ORDER_COUNT_SERVICE"}, "listFlow": {"type": "InvokeApi", "url": "/api/trantor2default/preference/INDICATOR", "method": "GET"}, "saveFlow": {"type": "InvokeApi", "url": "/api/trantor2default/preference/INDICATOR/save", "method": "POST"}}}}, {"key": "MergeRule", "name": "MergeRule", "type": "Widget", "title": "合并规则", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "displayNamePath": "label", "designerProps": {"type": "formily-react", "title": "表单字段配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "icon": "zidingyiyemian", "defaultProps": {"label": "合并规则", "MergeRuleServiceProps": {}}, "staticProps": {}}, {"key": "MoveVoucherEdit", "name": "MoveVoucherEdit", "type": "Widget", "title": "移动凭证", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"queryMvmTypeFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_QUERY_POSITIVE_MVM_TYPE_EVENT_SERVICE"}, "queryUnitNameFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_QUERY_MAT_INV_UNIT_EVENT_SERVICE"}, "queryRequiredFieldsFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_QUERY_MVM_REQUIRED_FILED_EVENT_SERVICE"}, "queryMvmLineFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_BUILD_MVM_DOC_LINE_EVENT_SERVICE"}, "createMvmFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_CREATE_MVM_VOUCHER_EVENT_SERVICE"}, "queryChargeDataFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_BUILD_WRITE_OFF_MVM_VOUCHER_EVENT_SERVICE"}, "queryCopyDataFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_MVM_VOUCHER_COPY_EVENT_SERVICE"}, "queryAvailableBatchFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_QUERY_AVAILABLE_BATCH_EVENT_SERVICE"}, "createBatchCodeFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_CREATE_BATCH_CODE_EVENT_SERVICE"}, "queryMatCharaFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_QUERY_MAT_BATCH_CHARA_EVENT_SERVICE"}, "querySuggestBatchFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_QUERY_SUGGEST_BATCH_EVENT_SERVICE"}, "determineFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_DETERMINE_EVENT_SERVICE"}, "queryBatchInPagingFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_CREATE_PAGE_QUERY_EVENT_SERVICE"}, "queryBatchOutPagingFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_DETERMINE_PAGING_EVENT_SERVICE"}, "queryBatchByCodesFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_QUERY_DETAIL_BY_CODES_EVENT_SERVICE"}, "queryBatchButtonTypeFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_QUERY_BATCH_BUTTON_TYPE_EVENT_SERVICE"}, "queryCalculateQtyFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_TRANSFER_QUANTITY_CALCULATE_EVENT_SERVICE"}, "batchGenerateBatchCodeFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_CREATE_BATCH_BATCH_CODE_EVENT_SERVICE"}, "queryTemplateFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_ADMIN_BATCH_FIND_IMPORT_TEMPLATE_EVENT_SERVICE"}, "queryOrgLocFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_QUERY_INV_ORG_INV_LOC_SERVICE"}, "queryMatFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "queryOrgFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService"}, "queryStockSpecFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_SCM$inv_spc_stk_type_cf", "serviceKey": "ERP_SCM$SYS_PagingDataService"}, "querySupplierFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "queryCustomerFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_cust_info_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}}}, {"key": "MyNotice", "name": "MyNotice", "type": "Widget", "title": "个人站内信", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "个人站内信配置"}, "permissions": [{"type": "Custom", "resourceType": "Container", "target": "WorkbenchMyNoticeTarget", "key": "WorkbenchMyNotice", "label": "组件调用服务"}], "editable": true, "defaultProps": {"pageSize": 10, "WorkbenchMyNoticeTarget": {}}, "staticProps": {"queryNoticeListFlow": {"type": "InvokeService", "serviceKey": "sys_common$API_NOTICE_STATION_PAGING_POST"}}}, {"key": "OrgList", "name": "OrgList", "type": "Widget", "title": "组织列表", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "组织页面配置"}, "permissions": [{"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "createOrgTarget", "key": "createOrg", "label": "新建"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key": "editOrg", "label": "编辑"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "disable<PERSON><PERSON><PERSON><PERSON><PERSON>", "key": "disableOrg", "label": "停用"}, {"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {"createOrgTarget": {}, "editOrgTarget": {}, "disableOrgTarget": {}}, "staticProps": {"serviceProps": {"queryOrgUnitDetailFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_ORG_UNIT_QUERY_DETAIL_EVENT_SERVICE"}, "queryOrgUnitParentFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_ORG_UNIT_PARENT_PAGE_EVENT_SERVICE"}, "queryOrgBizTypeListFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_BIZ_TYPE_FIND_ALL_EVENT_SERVICE"}, "saveOrgUnitFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_ORG_UNIT_SAVE_EVENT_SERVICE"}, "queryOrgUnitByPidFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_ORG_UNIT_QUERY_PID_EVENT_SERVICE"}, "deleteOrgUnitFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_ORG_UNIT_DELETE_EVENT_SERVICE"}, "disableOrgUnitFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_ORG_UNIT_DISABLE_EVENT_SERVICE"}, "enableOrgUnitFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_ORG_UNIT_ENABLE_EVENT_SERVICE"}, "OrgUnitRoleFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_ORG_FRONT_ROLE_PAGE_EVENT_SERVICE"}, "OrgSearchFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_ORG_UNIT_SEARCH_EVENT_SERVICE"}, "OrgHiddenColFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_BIZ_TYPE_QUERY_HIDDE_FILED_EVENT_SERVICE"}}}}, {"key": "PayFinancialAuditing", "name": "PayFinancialAuditing", "type": "Widget", "title": "付款钩稽", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "付款钩稽配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"vendInfoModelAlias": "ERP_GEN$gen_vend_info_md", "orgModelAlias": "sys_common$org_struct_md", "custModelAlias": "ERP_GEN$gen_cust_info_md", "finDocTypeModelAlias": "ERP_FIN$fin_doc_type_md", "currencyModelAlias": "ERP_GEN$gen_curr_type_cf", "funCmPnTypeModelAlias": "ERP_FIN$fin_cm_pn_type_md", "matchFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$PCC_MATCH_CLEAR_EVENT_SERVICE"}, "forceFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$PCC_FORCE_CLEAR_EVENT_SERVICE"}, "queryAPFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$AP_SCHL_QUERY_EVENT_SERVICE"}, "queryFNFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$PN_CLEAR_QUERY_EVENT_SERVICE"}, "queryAPESFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$AP_SCHL_QUERY_FROM_ES_EVENT_SERVICE"}, "queryFNESFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$PN_CLEAR_QUERY_FROM_ES_EVENT_SERVICE"}}, "vendInfoPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "queryVendInfoFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "queryOrgFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "queryCustFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_cust_info_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "queryFinDocTypeFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FIN$fin_doc_type_md", "serviceKey": "ERP_FIN$SYS_FindDataByIdService"}, "queryCurrencyFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "queryFunCmPnTypeFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FIN$fin_cm_pn_type_md", "serviceKey": "ERP_FIN$SYS_FindDataByIdService"}, "queryFunApmAPTypeFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FIN$fin_apm_ap_type_md", "serviceKey": "ERP_FIN$SYS_FindDataByIdService"}}}, {"key": "PayableAgingAnalysis", "name": "PayableAgingAnalysis", "type": "Widget", "title": "应付账龄分析", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"payableAgingAnalysisServiceProps": {"queryComOrgFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$AR_REPORT_AGING_EVENT_SERVICE"}, "comOrgModelAlias": "ERP_GEN$org_com_org_cf"}, "queryOrgListFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService"}}}, {"key": "PeriodTaskQuickEntry", "name": "PeriodTaskQuickEntry", "type": "Widget", "title": "期末结账任务", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "期末结账任务配置"}, "permission": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"periodTaskQuickEntryServiceProps": {"queryTaskPagingFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_END_TASK_LIST_QUERY_EVENT_NEW_SERVICE"}, "getDetailFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_END_TASK_DETAIL_EVENT_SERVICE"}}}}, {"key": "PlanItemSplit", "name": "PlanItemSplit", "type": "Widget", "title": "计划订单拆分", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "凭证模板管理"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {}}, {"key": "PricingAnalysis", "name": "PricingAnalysis", "type": "Widget", "title": "定价分析", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"priceAnalysisServiceProps": {"flow": {"type": "InvokeService", "serviceKey": "ERP_GEN$PRICE_ANALYSIS_EVENT_SERVICE"}}}}, {"key": "PricingCondition", "name": "PricingCondition", "type": "Widget", "title": "定价条件", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-schema", "title": "定价条件配置", "schema": {"type": "object", "properties": {"usageType": {"title": "用途类型", "x-decorator": {}, "x-decorator-props": {"compact": true}, "x-component-props": {"placeholder": "请输入"}}, "isCreateTable": {"title": "新建条件表", "default": true, "x-component": {}, "x-decorator-props": {"compact": true}}, "isCreateField": {"title": "新建字段值", "default": true, "x-decorator-props": {"compact": true}}}}}, "permissions": [{"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "addConditionServiceProps", "key": "addConditionPrice", "label": "新建"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "addConditionFieldValueServiceProps", "key": "addFieldValue", "label": "新建字段值"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "configConditionPriceTarget", "key": "configConditionPrice", "label": "条件配置"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "deleteConditionDataServiceProps", "key": "deleteConditionPrice", "label": "删除"}, {"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {"configConditionPriceTarget": {}}, "staticProps": {"priceConditionServiceProps": {"matchRecordModelAlias": "ERP_GEN$gen_match_record_head_cf", "matchFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MATCH_CONDITION_TABLE_PROR_EVENT_SERVICE"}, "queryFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_QUERY_CONDITION_DATA_LIST_EVENT_SERVICE"}, "allowModelFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ALLOW_MODE_LIST_EVENT_SERVICE"}, "updateFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_UPDATE_MATCH_RECORD_EVENT_SERVICE"}, "queryFunctionFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$RULE_ENGINE_QUERY_ALL_FACTOR_ACTION_EVENT"}, "updateItemFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MATCH_RECORD_HEAD_UPDATE_EVENT_SERVICE"}, "queryRecordFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MATCH_RECORD_HEAD_PAGING_QUERY_EVENT_SERVICE"}}, "addConditionServiceProps": {"createFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_CREATE_MATCH_RECORD_HEAD_EVENT_SERVICE"}}, "addConditionFieldValueServiceProps": {"saveFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_CREATE_MATCH_RECORD_DATA_EVENT_SERVICE"}}, "deleteConditionDataServiceProps": {"deleteFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_BATCH_DELETE_MATCH_RECORD_EVENT_SERVICE"}, "deleteItemFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MATCH_RECORD_HEAD_DELETE_EVENT_SERVICE"}}, "queryMatchRecordFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_match_record_head_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceProcedurePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_procedure_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceProcedureFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_procedure_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceStrategyPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_strategy_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceStrategyFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_strategy_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceMatchRecordPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_record_log", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceMatchRecordFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_record_log", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceTransferValuePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_transfer_value_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceTransferValueFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_transfer_value_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceMatchScopeFieldFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_scope_field_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceMatchScopeFieldPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_scope_field_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceMatchScopePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_scope_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceMatchScopeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_scope_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getBatchCharClassFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_chara_class_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getBatchCharClassPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_chara_class_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getCharClassLinkFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_class_chara_link_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getCharClassLinkPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_class_chara_link_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "queryPresetValueFlow": {"modelAlias": "ERP_GEN$gen_chara_preset_value_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService", "type": "InvokeSystemService"}, "queryCharaByCharaClassFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_BATCH_RANGE_QUERY_CHARA_BY_CHARA_CLASS_SERVICE"}}}, {"key": "PricingOverview", "name": "PricingOverview", "type": "Widget", "title": "定价总览", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "定价总览配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "key": "addOverviewPrice", "target": "createOverviewPriceServiceProps", "label": "新建"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "key": "configOverviewPrice", "target": "configOverviewPriceTarget", "label": "规则配置"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "key": "detailOverviewPrice", "target": "detailOverviewPriceServiceProps", "label": "查看"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "key": "deleteOverviewPrice", "target": "deleteOverviewPriceServiceProps", "label": "删除"}], "editable": true, "defaultProps": {"configOverviewPriceTarget": {}, "usageType": "SLS"}, "staticProps": {"importModelAlias": "ERP_GEN$price_import_dto", "pricingOverviewServiceProps": {"getPriceOverviewFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$PRICE_STRATEGY_OVERVIEW_EVENT_SERVICE"}, "matchScopeListFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MATCH_CONDITION_TABLE_PROR_EVENT_SERVICE"}, "matchScopeDataFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MATCH_RECORD_MD_PAGING_QUERY_EVENT_SERVICE"}, "createRuleEngineFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ALLOW_MODE_LIST_EVENT_SERVICE"}, "queryRuleEngineDetailFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$RULE_ENGINE_QUERY_ONE_EVENT"}, "updateMatchScopeFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MATCH_RECORD_MD_UPDATE_EVENT_SERVICE"}, "saveLadderPriceFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_LADDER_MD_SAVE_EVENT_SERVICE"}, "exportTaskDirectFlow": {"type": "InvokeService", "serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST"}}, "createOverviewPriceServiceProps": {"createMatchScopeFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MATCH_RECORD_MD_BATCH_SAVE_EVENT_SERVICE"}}, "detailOverviewPriceServiceProps": {"detailMatchScopeFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MATCH_RECORD_MD_SAME_CONDITION_QUERY_EVENT_SERVICE"}}, "deleteOverviewPriceServiceProps": {"deleteMatchScopeFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_BATCH_DELETE_MATCH_RECORD_EVENT_SERVICE"}}, "getPriceTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceProcedurePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_procedure_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceProcedureFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_procedure_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceStrategyPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_strategy_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceStrategyFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_strategy_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceMatchRecordPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_record_log", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceMatchRecordFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_record_log", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceTransferValuePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_transfer_value_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceTransferValueFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_transfer_value_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceMatchScopeFieldFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_scope_field_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceMatchScopeFieldPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_scope_field_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceMatchScopePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_scope_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceMatchScopeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_scope_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceAdjustmentDetailFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$PRICE_ADJ_QUERY_EVENT_SERVICE"}, "savePriceAdjustmentFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$PRICE_ADJ_SAVE_EVENT_SERVICE"}, "submitPriceAdjustmentFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$PRICE_ADJ_SUBMIT_EVENT_SERVICE"}, "createRuleEngineFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_RULE_ENGINE_CREATE_EVENT_SERVICE"}, "queryRuleEngineByIdFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_RULE_ENGINE_QUERY_BY_ID_EVENT_SERVICE"}, "priceImportFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$price_import_adapter_service"}, "getOptionsFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$PRICE_GLOBAL_CF_FIND_DATA_BY_CODE_SERVICE"}, "getPriceCustTaxPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_cust_tax_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "findPriceCustTaxByIdFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_cust_tax_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceSlsSoTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_SCM$sls_so_type_cf", "serviceKey": "ERP_SCM$SYS_PagingDataService"}, "findPriceSlsSoTypeByIdFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_SCM$sls_so_type_cf", "serviceKey": "ERP_SCM$SYS_FindDataByIdService"}, "getPriceMatTaxTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_tax_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "findPriceMatTaxTypeByIdFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_tax_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "findPriceInvSpcStkTypeByIdFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_SCM$inv_spc_stk_type_cf", "serviceKey": "ERP_SCM$SYS_FindDataByIdService"}}}, {"key": "PricingStrategyForm", "name": "PricingStrategyForm", "type": "Widget", "title": "定价类型表格", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"PricingStrategyFormServiceProps": {"conditionDeleteFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$PRICE_TYPE_CONDITION_DELETE_EVENT_SERVICE"}, "fetchAllowFieldsFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ALLOW_FIELD_ITEM_PAGING_EVENT_SERVICE"}, "getAllowModelFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ALLOW_FIELD_LIST_EVENT_SERVICE"}, "findByKeyFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ALLOW_FIELD_ITEM_QUERY_LIST_BY_KEY_EVENT_SERVICE"}, "queryModelFieldsFlow": {"type": "InvokeApi", "url": "/api/trantor/struct-node/page", "method": "POST"}, "postNodeFindByAliasFlow": {"type": "InvokeApi", "url": "/api/trantor/struct-node/find-by-alias", "method": "POST"}}}}, {"key": "PricingStrategy", "name": "PricingStrategy", "type": "Widget", "title": "匹配范围", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "key": "addStrategyPrice", "target": "createPriceStrategyServiceProps", "label": "新建"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "key": "deleteStrategyPrice", "target": "deletePriceStrategyServiceProps", "label": "删除"}], "editable": true, "defaultProps": {}, "staticProps": {"priceStrategyServiceProps": {"matchScopeListFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$PRICE_MATCH_SCOPE_TABLE_EVENT_SERVICE"}, "matchScopeDataFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$PRICE_MATCH_SCOPE_DATA_PAGE_EVENT_SERVICE"}, "queryAllowModelFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ALLOW_MODE_LIST_EVENT_SERVICE"}, "updateMatchScopeFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$PRICE_UPDATE_MATCH_SCOPE_DATA_EVENT_SERVICE"}}, "createPriceStrategyServiceProps": {"createMatchScopeFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$PRICE_BATCH_CREATE_MATCH_SCOPE_DATA_EVENT_SERVICE"}}, "deletePriceStrategyServiceProps": {"deleteMatchScopeFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$PRICE_BATCH_DELETE_MATCH_SCOPE_DATA_EVENT_SERVICE"}}, "getPriceTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceProcedurePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_procedure_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceProcedureFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_procedure_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceStrategyPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_strategy_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceStrategyFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_strategy_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceMatchRecordPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_record_log", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceMatchRecordFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_record_log", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceTransferValuePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_transfer_value_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceTransferValueFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_transfer_value_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceMatchScopeFieldFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_scope_field_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceMatchScopeFieldPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_scope_field_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceMatchScopePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_scope_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceMatchScopeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_scope_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}}}, {"key": "ProductionPlanAnalysis", "name": "ProductionPlanAnalysis", "type": "Widget", "title": "生产计划分析", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"productionPlanAnalysisServiceProps": {"queryFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_SUM_SPECIAL_QUERY_EVENT_SERVICE"}, "displayHierarchyFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_DISPLAY_HIERARCHY_QUERY_EVENT_SERVICE"}, "queryDropdownItemFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_SUM_FRONT_BUTTON_INFO_EVENT_SERVICE"}, "queryUnfixedRowFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_SUM_UNFIXED_ROWS_QUERY_EVENT_SERVICE"}, "rollbackFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_SUM_ROLLBACK_EVENT_SERVICE"}, "submitFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_SUM_SUBMIT_EVENT_SERVICE"}, "stashFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_SUM_UPDATE_EVENT_SERVICE"}, "confirmFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_SUM_CONFIRM_EVENT_SERVICE"}, "unConfirmFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_SUM_CANCEL_CONFIRM_EVENT_SERVICE"}}}}, {"key": "ProductionPlanPublish", "name": "ProductionPlanPublish", "type": "Widget", "title": "生产计划发布", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"productionPlanPublishServiceProps": {"queryFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_BKW_SPECIAL_QUERY_EVENT_SERVICE"}, "displayHierarchyFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_DISPLAY_HIERARCHY_QUERY_EVENT_SERVICE"}, "queryUnfixedRowFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_BKW_UNFIXED_ROWS_QUERY_EVENT_SERVICE"}, "queryDropdownItemFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_BKW_FRONT_BUTTON_INFO_EVENT_SERVICE"}, "rollbackFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_BKW_BACK_SUBMIT_EVENT_SERVICE"}, "stashFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_BKW_TEMPORARY_STORAGE_EVENT_SERVICE"}, "submitFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_BKW_SUMIT_EVENT_SERVICE"}, "confirmFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_BKW_ROW_SUBMIT_EVENT_SERVICE"}, "unConfirmFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_BKW_ROW_CANAL_SUBMIT_EVENT_SERVICE"}, "queryBalanceTaskFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_SUM_NOT_BALANCE_ORG_QUERY_EVENT_SERVICE"}, "vrsBalanceJobFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_QUERY_VRS_BALANCE_JOB_EVENT_SERVICE"}, "supplyMethodFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_SUPPLY_METHOD_PAGING_SERVICE"}}}}, {"key": "ProductionPlanTable", "name": "ProductionPlanTable", "type": "Widget", "title": "生产计划提报", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"productionPlanServiceProps": {"queryFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_RAW_QUERY_EVENT_SERVICE"}, "submitFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_RAW_SUBMIT_EVENT_SERVICE"}, "queryUnfixedRowFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_RAW_UNFIXED_ROWS_QUERY_EVENT_SERVICE"}, "stashFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_RAW_UPDATE_EVENT_SERVICE"}, "confirmFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_RAW_CONFIRM_EVENT_SERVICE"}, "materialFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$MATERIAL_PAGINATION_SERVICE"}, "saveAnnotationFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_REMARK_SAVE_EVENT_SERVICE"}, "queryAnnotationListFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_REMARK_QUERY_EVENT_SERVICE"}, "unConfirmFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_RAW_CANCEL_CONFIRM_EVENT_SERVICE"}, "getRegionFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$REGION_PAGINATION_SERVICE"}}}}, {"key": "ProductionSplitRuleSelect", "name": "ProductionSplitRuleSelect", "type": "Widget", "title": "生产分单规则选择", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"ruleItemPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_PRD$prd_issue_rule_item_cf", "serviceKey": "ERP_PRD$SYS_PagingDataService"}}}, {"key": "ProductionDailyReport ", "name": "ProductionDailyReport", "type": "Widget", "title": "生产日报", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"dailyReportQueryFlow": {"type": "InvokeService", "serviceKey": "ERP_PRD$PRD_ORDER_QUERY_DAILY_REPORT_EVENT_SERVICE"}, "orgStructFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "genWorkCenterFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$gen_work_centor_header_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}}}, {"key": "ProductionExecutionTrackingReport", "name": "ProductionExecutionTrackingReport", "type": "Widget", "title": "生产执行追踪", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"executionTrackingReportQueryFlow": {"type": "InvokeService", "serviceKey": "ERP_PRD$PRD_ORDER_QUERY_EXECUTION_TRACKING_EVENT_SERVICE"}, "orgStructFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "genMatFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$gen_mat_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}}}, {"key": "PlanVersionTrackingReport", "name": "PlanVersionTrackingReport", "type": "Widget", "title": "销售计划版本跟踪报表", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"loadPlanVersionFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_VRS_TR_PAGING_DATA_SERVICE"}, "loadPlanMaintenanceFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_SCENARIO_HEADER_CF_PAGING_DATA_SERVICE"}, "loadPagingDataFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_VRS_FALL_EVENT_SERVICE"}, "loadPeriodTypeFlow": {"type": "InvokeService", "serviceKey": "ERP_PLN$PLN_SLS_PERIOD_TYPE_CF_PAGING_DATA_SERVICE"}, "getMatPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}}}, {"key": "ProofRecordDetail", "name": "ProofRecordDetail", "type": "Widget", "title": "凭证记录详情", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "凭证记录详情"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"ProofRecordDetailServiceProps": {"FIN_AES_QUERY_DSD_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_QUERY_DSD_SERVICE"}}}}, {"key": "ProofTemplate", "name": "ProofTemplate", "type": "Widget", "title": "凭证模板管理", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "凭证模板管理"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"FIN_AES_SYS_TREE_MOD_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_SYS_TREE_MOD_SERVICE"}, "FIN_AES_BD_DOC_CF_QUERY_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_BD_DOC_CF_QUERY_SERVICE"}, "FIN_AES_OUTER_OBJECT_PAGE_MOD_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_OUTER_OBJECT_PAGE_MOD_SERVICE"}, "FIN_AES_OUTER_OBJECT_DELETE_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_OUTER_OBJECT_DELETE_SERVICE"}, "FIN_AES_OUTER_OBJECT_SAVE_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_OUTER_OBJECT_SAVE_SERVICE"}, "FIN_AES_ACC_EXH_HEAD_CF_PAGE_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_ACC_EXH_HEAD_CF_PAGE_SERVICE"}, "FIN_AES_DSD_TMPL_CF_DETAIL_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_DSD_TMPL_CF_DETAIL_SERVICE"}, "FIN_AES_BD_GLS_TYPE_PAGE_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_BD_GLS_TYPE_PAGE_SERVICE"}, "FIN_AES_DSD_DEF_CF_PAGE_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_DSD_DEF_CF_PAGE_SERVICE"}, "FIN_AES_DSD_DEF_QUERY_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_DSD_DEF_QUERY_SERVICE"}, "FIN_AES_DSD_TMPL_CF_SAVE_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_DSD_TMPL_CF_SAVE_SERVICE"}, "FIN_AES_DSD_TMPL_CF_DELETE_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_DSD_TMPL_CF_DELETE_SERVICE"}, "FIN_AES_DSD_TMPL_CF_COPY_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_DSD_TMPL_CF_COPY_SERVICE"}, "FIN_AES_DSD_TMPL_ENABLE_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_DSD_TMPL_ENABLE_SERVICE"}, "FIN_AES_DSD_TMPL_DISABLE_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_DSD_TMPL_DISABLE_SERVICE"}, "FIN_AES_LEDG_EXH_HEAD_CF_PAGE_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_LEDG_EXH_HEAD_CF_PAGE_SERVICE"}, "FIN_AES_EXH_MAPPING_HEAD_CF_PAGE_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_EXH_MAPPING_HEAD_CF_PAGE_SERVICE"}, "FIN_DSD_CNSLD_CF_PAGE_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_DSD_CNSLD_CF_PAGE_SERVICE"}, "fin_aes_bd_doc_cfFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_aes_dsd_vld", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "fin_arm_ar_type_mdFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FIN$fin_arm_ar_type_md", "serviceKey": "ERP_FIN$SYS_PagingDataService"}, "gen_cust_info_mdFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_cust_info_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}}}, {"key": "ProofRecord", "name": "ProofRecord", "type": "Widget", "title": "凭证记录", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "凭证记录"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"FIN_AES_BD_GLS_TYPE_PAGE_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_BD_GLS_TYPE_PAGE_SERVICE"}, "FIN_AES_QUERY_DSD_TR_BY_GLS_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_QUERY_DSD_TR_BY_GLS_SERVICE"}, "FIN_AES_DSD_TR_INNER_PUSH_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_DSD_TR_INNER_PUSH_SERVICE"}, "FIN_AES_RE_GEN_DSD_SERVICEFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_AES_RE_GEN_DSD_SERVICE"}, "fin_aes_bd_doc_cfFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_aes_bd_doc_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}}}, {"key": "ReceivableAgingAnalysis", "name": "ReceivableAgingAnalysis", "type": "Widget", "title": "应收账龄分析", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"receivableAgingAnalysisServiceProps": {"queryComOrgFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$AR_REPORT_AGING_EVENT_SERVICE"}, "comOrgModelAlias": "ERP_GEN$org_com_org_cf"}}}, {"key": "ReceivingInvoiceFinancialAuditing", "name": "ReceivingInvoiceFinancialAuditing", "type": "Widget", "title": "收票钩稽", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "收票钩稽配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"vendInfoModelAlias": "ERP_GEN$gen_vend_info_md", "orgModelAlias": "sys_common$org_struct_md", "finDocTypeModelAlias": "ERP_FIN$fin_doc_type_md", "matchFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$IBC_SUBMIT_EVENT_SERVICE"}, "forceFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$IBC_FORCE_CLEAR_MANUAL_EVENT_SERVICE"}, "queryAPFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$AP_QUERY_EVENT_SERVICE"}, "queryFNFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$PI_QUERY_EVENT_SERVICE"}, "queryAPESFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$AP_QUERY_FROM_ES_EVENT_SERVICE"}, "queryFNESFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$PI_QUERY_FROM_ES_EVENT_SERVICE"}}, "vendInfoPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "queryVendInfoFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "queryMaterialFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "queryOrgFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "queryFinDocTypeFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FIN$fin_doc_type_md", "serviceKey": "ERP_FIN$SYS_FindDataByIdService"}}}, {"key": "ReportPage", "name": "ReportPage", "type": "Widget", "title": "报表", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "displayNamePath": "label", "designerProps": {"type": "formily-react", "title": "表单字段配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {}}, {"key": "ResumeDetail", "name": "ResumeDetail", "type": "Widget", "title": "简历管理详情", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "editJobForApplicantTarget", "key": "resume.editJobForApplicant", "label": "编辑应聘者"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "deleteJobForApplicantTarget", "key": "resume.deleteJobForApplicant", "label": "删除应聘者"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "viewInterviewTarget", "key": "resume.viewInterview", "label": "查看面试评价"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "operationArrangeProcessTarget", "key": "resume.operationArrangeProcess", "label": "安排流程"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "operationAddProcessTarget", "key": "resume.operationAddProcess", "label": "新增流程"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "operationNextProcessTarget", "key": "resume.operationNextProcess", "label": "进入下一轮"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "operationQuickArrangeTarget", "key": "resume.operationQuickArrange", "label": "快速安排"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "operationChangeJobTarget", "key": "resume.operationChangeJob", "label": "转荐职位"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "operationChangeHumanTarget", "key": "resume.operationChangeHuman", "label": "转人才库"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "operationOutTarget", "key": "resume.operationOut", "label": "淘汰"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "operationChangeBlackTarget", "key": "resume.operationChangeBlack", "label": "转黑名单"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "arrangeProcessTarget", "key": "resume.arrangeProcess", "label": "安排流程（应聘流程中）"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "skipProcessTarget", "key": "resume.skipProcess", "label": "跳过"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "failProcessTarget", "key": "resume.failProcess", "label": "淘汰"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "editProcessTarget", "key": "resume.editProcess", "label": "编辑流程"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "deleteProcessTarget", "key": "resume.deleteProcess", "label": "删除流程"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "notify<PERSON><PERSON><PERSON>", "key": "resume.notify", "label": "通知提醒"}], "editable": true, "defaultProps": {"editJobForApplicantTarget": {}, "deleteJobForApplicantTarget": {}, "viewInterviewTarget": {}, "operationArrangeProcessTarget": {}, "operationAddProcessTarget": {}, "operationNextProcessTarget": {}, "operationQuickArrangeTarget": {}, "operationChangeJobTarget": {}, "operationChangeHumanTarget": {}, "operationOutTarget": {}, "operationChangeBlackTarget": {}, "arrangeProcessTarget": {}, "skipProcessTarget": {}, "failProcessTarget": {}, "editProcessTarget": {}, "deleteProcessTarget": {}, "notifyTarget": {}}, "staticProps": {"serviceProps": {"resumeSaveFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_RESUME_SAVE_EVENT_SERVICE"}, "resumeFileFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$RESUME_FILE_IDENTFY_EVENT_SERVICE"}, "resumeListFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_RESUME_LIST_QUERY_EVENT_SERVICE"}, "detailQueryFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_RESUME_DETAIL_QUERY_EVENT_SERVICE"}, "processQueryFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_QUERY_INTERVIEW_PROCESS_EVENT_SERVICE"}, "resultQueryFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_QUERY_INTERVIEW_RESULT_EVENT_SERVICE"}, "recordQueryFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_RESUME_RECORD_QUERY_EVENT_SERVICE"}, "historyQueryFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_PROCESS_HISTORY_EVENT_SERVICE"}, "fastArrangeFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_FAST_ARRANGE_INTERVIEW_EVENT_SERVICE"}, "deleteProcessFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_DELETE_INTERVIEW_EVENT_SERVICE"}, "noticeScenePageFlow": {"type": "InvokeService", "serviceKey": "sys_common$API_NOTICE_SCENE_PAGING_POST"}, "interviewNoticeFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_INTERVIEW_NOTICE_EVENT_SERVICE"}, "resumeDeleteFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_RESUME_DELETE_EVENT_SERVICE"}, "createInterviewFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_CREATE_INTERVIEW_EVENT_SERVICE"}, "arrangeFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_ARRANGE_INTERVIEW_EVENT_SERVICE"}, "updateStatusFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_RESUME_CHANGE_STATUS_EVENT_SERVICE"}, "nextInterviewFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_NEXT_INTERVIEW_EVENT_SERVICE"}}, "dictItemCfFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_dict_item_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "jobMdFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_HR$rec_job_md", "serviceKey": "ERP_HR$SYS_PagingDataService"}, "interviewerMdFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_HR$rec_interviewer_md", "serviceKey": "ERP_HR$SYS_PagingDataService"}, "questionTemplateFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_HR$hr_question_template_cf", "serviceKey": "ERP_HR$SYS_PagingDataService"}}}, {"key": "ResumeFilterDetail", "name": "ResumeFilterDetail", "type": "Widget", "title": "简历筛选详情", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"detailQueryFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_RESUME_DETAIL_QUERY_EVENT_SERVICE"}, "resultQueryFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_QUERY_INTERVIEW_RESULT_EVENT_SERVICE"}, "recordQueryFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_RESUME_RECORD_QUERY_EVENT_SERVICE"}, "historyQueryFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_PROCESS_HISTORY_EVENT_SERVICE"}, "fastArrangeFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_FAST_ARRANGE_INTERVIEW_EVENT_SERVICE"}, "deleteProcessFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_DELETE_INTERVIEW_EVENT_SERVICE"}, "noticeScenePageFlow": {"type": "InvokeService", "serviceKey": "sys_common$API_NOTICE_SCENE_PAGING_POST"}, "interviewNoticeFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_INTERVIEW_NOTICE_EVENT_SERVICE"}, "updateStatusFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_INTERVIEW_STATUS_CHANGE_EVENT_SERVICE"}}, "dictItemCfFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_dict_item_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}}}, {"key": "ResumeFilterList", "name": "ResumeFilterList", "type": "Widget", "title": "简历筛选列表", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"screenPageFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_SCREEN_PAGING_EVENT_SERVICE"}, "interviewAcceptFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_INTERVIEW_ACCEPT_EVENT_SERVICE"}, "updateStatusFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_INTERVIEW_STATUS_CHANGE_EVENT_SERVICE"}}, "dictItemCfFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_dict_item_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "orgEmployeeFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_employee_md", "serviceKey": "sys_common$SYS_PagingDataService"}}}, {"key": "ResumeInterviewDetailForm", "name": "ResumeInterviewDetailForm", "type": "Widget", "title": "简历面试评价表", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"queryFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_QUERY_INTERVIEW_EVALUATE_PRINT_INFO_EVENT_SERVICE"}}}}, {"key": "ResumeInterviewDetail", "name": "ResumeInterviewDetail", "type": "Widget", "title": "简历面试详情", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"detailQueryFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_RESUME_DETAIL_QUERY_EVENT_SERVICE"}, "resultQueryFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_QUERY_INTERVIEW_RESULT_EVENT_SERVICE"}, "recordQueryFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_RESUME_RECORD_QUERY_EVENT_SERVICE"}, "historyQueryFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_PROCESS_HISTORY_EVENT_SERVICE"}, "fastArrangeFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_FAST_ARRANGE_INTERVIEW_EVENT_SERVICE"}, "deleteProcessFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_DELETE_INTERVIEW_EVENT_SERVICE"}, "noticeScenePageFlow": {"type": "InvokeService", "serviceKey": "sys_common$API_NOTICE_SCENE_PAGING_POST"}, "interviewNoticeFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_INTERVIEW_NOTICE_EVENT_SERVICE"}, "queryEvaluationFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_QUERY_INTERVIEW_EVALUATE_EVENT_SERVICE"}, "updateStatusFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_INTERVIEW_STATUS_CHANGE_EVENT_SERVICE"}}, "dictItemCfFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_dict_item_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}}}, {"key": "ResumeInterviewList", "name": "ResumeInterviewList", "type": "Widget", "title": "简历面试列表", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"interviewPageFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_INTERVIEW_PAGING_EVENT_SERVICE"}, "interviewAcceptFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_INTERVIEW_ACCEPT_EVENT_SERVICE"}, "queryEvaluationFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_QUERY_INTERVIEW_EVALUATE_EVENT_SERVICE"}, "updateStatusFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_INTERVIEW_STATUS_CHANGE_EVENT_SERVICE"}}, "orgEmployeeFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_employee_md", "serviceKey": "sys_common$SYS_PagingDataService"}}}, {"key": "ResumeList", "name": "ResumeList", "type": "Widget", "title": "简历管理列表", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "createJobForApplicantTarget", "key": "resume.createJobForApplicant", "label": "新增应聘者"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "batchArrangeFilterTarget", "key": "resume.batch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "安排筛选"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "batchArrangeInterviewTarget", "key": "resume.batchArrangeInterview", "label": "安排面试"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "editJobForApplicantTarget", "key": "resume.editJobForApplicant", "label": "编辑应聘者"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "deleteJobForApplicantTarget", "key": "resume.deleteJobForApplicant", "label": "删除应聘者"}], "editable": true, "defaultProps": {"createJobForApplicantTarget": {}, "batchArrangeFilterTarget": {}, "batchArrangeInterviewTarget": {}, "editJobForApplicantTarget": {}, "deleteJobForApplicantTarget": {}}, "staticProps": {"serviceProps": {"jobMdQueryFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_REC_JOB_PAGING_EVENT_SERVICE"}, "resumeSaveFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_RESUME_SAVE_EVENT_SERVICE"}, "resumeFileFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$RESUME_FILE_IDENTFY_EVENT_SERVICE"}, "resumeListFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_RESUME_LIST_QUERY_EVENT_SERVICE"}, "resumeDeleteFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_RESUME_DELETE_EVENT_SERVICE"}, "fastArrangeFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_FAST_ARRANGE_INTERVIEW_EVENT_SERVICE"}, "noticeScenePageFlow": {"type": "InvokeService", "serviceKey": "sys_common$API_NOTICE_SCENE_PAGING_POST"}, "createInterviewFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_CREATE_INTERVIEW_EVENT_SERVICE"}, "arrangeFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_ARRANGE_INTERVIEW_EVENT_SERVICE"}, "queryCountFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_PROCESS_QUERY_COUNT_STATUS_EVENT_SERVICE"}, "checkResumeFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_RESUME_CHECK_EVENT_SERVICE"}, "batchCheckResumeFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_RESUME_BATCH_CHECK_EVENT_SERVICE"}, "batchSaveResumeFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_RESUME_BATCH_SAVE_EVENT_SERVICE"}, "batchArrangeFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_BATCH_ARRANGE_INTERVIEW_EVENT_SERVICE"}, "arrangeCheckFlow": {"type": "InvokeService", "serviceKey": "ERP_HR$REC_RESUME_ARRANGE_CHECK_EVENT_SERVICE"}}, "jobMdFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_HR$rec_job_md", "serviceKey": "ERP_HR$SYS_PagingDataService"}, "dictItemCfFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_dict_item_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "interviewerMdFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_HR$rec_interviewer_md", "serviceKey": "ERP_HR$SYS_PagingDataService"}, "orgEmployeeFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_employee_md", "serviceKey": "sys_common$SYS_PagingDataService"}, "orgStructFindFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindTreeChildrenDataService"}, "orgStructReverseFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_ReverseConstructTreeService"}, "questionTemplateFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_HR$hr_question_template_cf", "serviceKey": "ERP_HR$SYS_PagingDataService"}}}, {"key": "RuleVoucher", "name": "RuleVoucher", "type": "Widget", "title": "规则凭证", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "规则凭证配置"}, "permission": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"createFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VOUCHER_ORG_PROCESS_EVENT_SERVICE"}, "pagingFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VOUCHER_ORG_ITEM_PAGE_EVENT_SERVICE"}, "editFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VOUCHER_ORG_ITEM_EDIT_EVENT_SERVICE"}}, "getAuxiliaryDimensionPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ad_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "getCurrencyPagingFlow": {"type": "InvokeSystemService", "modelAlias": "TERP_MIGRATE$fin_glm_currency_cf", "serviceKey": "sys_common$SYS_PagingDataService"}}}, {"key": "SourcingDetail", "name": "SourcingDetail", "type": "Widget", "title": "寻源详情", "group": "Portal业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "寻源详情配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {"pageTitle": "招标单详情"}, "staticProps": {}}, {"key": "SourcingTabList", "name": "SourcingTabList", "type": "Container", "title": "寻源动态Tab", "group": "Portal业务组件", "droppable": true, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "寻源动态Tab"}, "editable": true, "defaultProps": {}, "staticProps": {}}, {"key": "SourcingTable", "name": "SourcingTable", "type": "Container", "title": "招投标询比价通用表单", "group": "Portal业务组件", "droppable": true, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "招投标询比价通用表单配置"}, "editable": true, "defaultProps": {}, "staticProps": {}}, {"key": "SourcingTips", "name": "SourcingTips", "type": "Widget", "title": "寻源固定提示", "group": "Portal业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "寻源固定提示"}, "editable": true, "defaultProps": {}, "staticProps": {}}, {"key": "StockStatisticList", "name": "StockStatisticList", "type": "Widget", "title": "库存统计", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "库存统计"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"queryStkCountFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$INV_BA_STK_COUNT_QUERY_EVENT_SERVICE"}}, "queryMatFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "queryOrgFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService"}, "queryOrgLocFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$ORG_QUERY_INV_ORG_INV_LOC_SERVICE"}, "queryInvTypeFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_SCM$inv_inv_type_cf", "serviceKey": "ERP_SCM$SYS_PagingDataService"}, "queryInvSpeStkTypeFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_SCM$inv_spc_stk_type_cf", "serviceKey": "ERP_SCM$SYS_PagingDataService"}}}, {"key": "TrialBalance", "name": "TrialBalance", "type": "Widget", "title": "期末试算平衡", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permission": true, "permissions": [{"type": "Self", "resourceType": "Container"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "target": "trialBalanceTarget", "key": "trialBalance", "label": "试算平衡"}], "editable": true, "defaultProps": {"trialBalanceTarget": {}}, "staticProps": {"getUserAbTypeFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AB_INFO_QUERY_BY_USER_ID_EVENT_SERVICE"}, "getAccountPeriodFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AB_TYPE_CALENDER_ITEM_LEAF_LIST_SERVICE"}, "loadTaskFlow": {"type": "InvokeService", "serviceKey": "sys_common$API_T_SCHEDULER_JOB_INSTANCE_FIND_BY_UUID_POST"}, "getPagingFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_END_TRIAL_BALANCE_DATA_PROCESS_EVENT_SERVICE"}, "getAsyncIdFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_END_TRIAL_BALANCE_ASYNC_EVENT_SERVICE"}, "abTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ab_type_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "currIdFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}}}, {"key": "GiftOverview", "name": "GiftOverview", "type": "Widget", "title": "赠品总览", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "key": "addOverviewPrice", "target": "createOverviewPriceServiceProps", "label": "新建"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "key": "configOverviewPrice", "target": "configOverviewPriceTarget", "label": "规则配置"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "key": "detailOverviewPrice", "target": "detailOverviewPriceServiceProps", "label": "查看"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "key": "deleteOverviewPrice", "target": "deleteOverviewPriceServiceProps", "label": "删除"}], "editable": true, "defaultProps": {"configOverviewPriceTarget": {}}, "staticProps": {"giftOverviewServiceProps": {"getGiftOverviewFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$GIFT_STRATEGY_OVERVIEW_EVENT_SERVICE"}, "matchScopeListFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MATCH_CONDITION_TABLE_PROR_EVENT_SERVICE"}, "matchScopeDataFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MATCH_RECORD_MD_PAGING_QUERY_EVENT_SERVICE"}, "createRuleEngineFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ALLOW_MODE_LIST_EVENT_SERVICE"}, "queryRuleEngineDetailFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$RULE_ENGINE_QUERY_ONE_EVENT_SERVICE"}, "updateMatchScopeFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MATCH_RECORD_MD_UPDATE_EVENT_SERVICE"}, "saveLadderPriceFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_LADDER_MD_SAVE_EVENT_SERVICE"}, "exportTaskDirectFlow": {"type": "InvokeService", "serviceKey": "sys_common$API_GEI_TASK_EXPORT_DIRECT_POST"}}, "createOverviewPriceServiceProps": {"createMatchScopeFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MATCH_RECORD_MD_BATCH_SAVE_EVENT_SERVICE"}}, "detailOverviewPriceServiceProps": {"detailMatchScopeFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MATCH_RECORD_MD_SAME_CONDITION_QUERY_EVENT_SERVICE"}}, "deleteOverviewPriceServiceProps": {"deleteMatchScopeFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_BATCH_DELETE_MATCH_RECORD_EVENT_SERVICE"}}, "getPriceTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceProcedurePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_procedure_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceProcedureFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_procedure_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceStrategyPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_strategy_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceStrategyFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_strategy_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceMatchRecordPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_record_log", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceMatchRecordFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_record_log", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceTransferValuePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_transfer_value_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceTransferValueFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_transfer_value_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceMatchScopeFieldFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_scope_field_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getPriceMatchScopeFieldPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_scope_field_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceMatchScopePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_scope_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getPriceMatchScopeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_scope_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}}}, {"key": "GiftStrategyMatchingRange", "name": "GiftStrategyMatchingRange", "type": "Widget", "title": "赠品策略匹配范围", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "key": "addStrategyGift", "target": "createGiftStrategyServiceProps", "label": "新建"}, {"type": "Custom", "resourceType": "<PERSON><PERSON>", "key": "deleteStrategyGift", "target": "deleteGiftStrategyServiceProps", "label": "删除"}], "editable": true, "defaultProps": {}, "staticProps": {"giftStrategyServiceProps": {"matchScopeListFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$GIFT_STRATEGY_MATCH_SCOPE_TABLE_EVENT_SERVICE"}, "matchScopeDataFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$GIFT_MATCH_SCOPE_DATA_PAGE_EVENT_SERVICE"}, "queryAllowModelFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ALLOW_MODE_LIST_EVENT_SERVICE"}, "updateMatchScopeFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$GIFT_UPDATE_MATCH_SCOPE_DATA_EVENT_SERVICE"}}, "createGiftStrategyServiceProps": {"createMatchScopeFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$GIFT_BATCH_CREATE_MATCH_SCOPE_DATA_EVENT_SERVICE"}}, "deleteGiftStrategyServiceProps": {"deleteMatchScopeFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$GIFT_BATCH_DELETE_MATCH_SCOPE_DATA_EVENT_SERVICE"}}, "getGiftTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getGiftTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getGiftProcedurePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_procedure_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getGiftProcedureFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_procedure_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getGiftStrategyPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_strategy_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getGiftStrategyFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_strategy_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getGiftMatchRecordPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_record_log", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getGiftMatchRecordFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_record_log", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getGiftTransferValuePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_transfer_value_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getGiftTransferValueFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_transfer_value_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getGiftMatchScopeFieldFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_scope_field_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getGiftMatchScopeFieldPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_scope_field_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getGiftMatchScopePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_scope_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getGiftMatchScopeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$price_match_scope_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}}}, {"key": "GiftTypeTableForm", "name": "GiftTypeTableForm", "type": "Widget", "title": "赠品类型表格", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "赠品类型表格配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"giftTypeTableFormServiceProps": {"conditionDeleteFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$GIFT_TYPE_CONDITION_DELETE_EVENT_SERVICE"}, "fetchAllowFieldsFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ALLOW_FIELD_ITEM_PAGING_EVENT_SERVICE"}, "getAllowModelFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ALLOW_FIELD_LIST_EVENT_SERVICE"}, "findByKeyFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_ALLOW_FIELD_ITEM_QUERY_LIST_BY_KEY_EVENT_SERVICE"}, "queryModelFieldsFlow": {"type": "InvokeApi", "url": "/api/trantor/struct-node/page", "method": "POST"}, "postNodeFindByAliasFlow": {"type": "InvokeApi", "url": "/api/trantor/struct-node/find-by-alias", "method": "POST"}}}}, {"key": "PriceAdjustmentSheetForm", "name": "PriceAdjustmentSheetForm", "type": "Widget", "title": "调价单表单", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "调价单表单配置"}, "actions": [{"key": "getData", "category": "getData", "label": "保存", "dataType": "Object"}], "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {"readonly": false}, "staticProps": {"getPriceAdjustmentDetailFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$PRICE_ADJ_QUERY_EVENT_SERVICE"}, "matchScopeDataFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MATCH_RECORD_MD_PAGING_QUERY_EVENT_SERVICE"}, "createRuleEngineFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_RULE_ENGINE_CREATE_EVENT_SERVICE"}, "queryRuleEngineByIdFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_RULE_ENGINE_QUERY_BY_ID_EVENT_SERVICE"}, "getOptionsFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$PRICE_GLOBAL_CF_FIND_DATA_BY_CODE_SERVICE"}}}, {"key": "Countdown", "name": "Countdown", "type": "Widget", "title": "倒计时", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "widgetDesignerProps": {"widgetType": ["Display"], "fieldType": ["DATE"], "type": "formily-react", "title": "倒计时"}, "editable": true, "defaultProps": {}, "staticProps": {}}, {"key": "SettlementImport", "name": "SettlementImport", "type": "Widget", "title": "结算项导入", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "结算项导入配置"}, "permission": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"batchMatchFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$SETT_BATCH_AGGREGATION_LOCK_EVENT_SERVICE"}}}, {"key": "FinanceHistory", "name": "FinanceHistory", "type": "Widget", "title": "钩稽记录", "group": "TERP业务组件", "displayNamePath": "title", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "钩稽记录配置"}, "permissions": [{"type": "Self", "resourceType": "<PERSON><PERSON>"}], "editable": true, "defaultProps": {}, "staticProps": {"infoCleanFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$FIN_BRM_INFO_CLEAN_EVENT_SERVICE"}, "pollQueryTaskFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$FIN_BRM_BATCH_POLL_EVENT_SERVICE"}, "headBatchPageFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$FIN_BRM_HEAD_BATCH_PAGE_EVENT_SERVICE"}, "itemBatchPageFlow": {"type": "InvokeService", "serviceKey": "ERP_FIN$FIN_BRM_ITEM_BATCH_PAGE_EVENT_SERVICE"}}}, {"key": "VoucherDetail", "name": "VoucherDetail", "type": "Widget", "title": "凭证详情", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "actions": [{"key": "getData", "label": "获取数据", "category": "getData", "dataType": "Object"}], "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"getCalendarItemIdFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VE_AD_PERIOD_EVENT_SERVICE"}, "getOrigCurrAmtFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VE_LOAD_ORIG_CURR_AMT_EVENT_SERVICE"}, "getVouchNumberFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VE_VOUCH_NUMBER_EVENT_SERVICE"}, "getDimensionFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VE_QUERY_ADS_BY_ITEM_EVENT_SERVICE"}, "getCashFlowAnalysisFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_CASH_FLOW_ANALYSIS_EVENT_SERVICE"}, "getProjectTreeFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AB_TYPE_ITEM_TREE_LIST_EVENT_SERVICE"}, "getSubjectTreeFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AA_TREE_QUERY_BY_AB_COA_EVENT_SERVICE"}, "getSubjectListFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AA_LIST_QUERY_BY_CODE_OR_NAME_SERVICE"}, "autoAnalysisFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_CASH_FLOW_AUTO_ANALYSIS_EVENT_SERVICE"}, "saveFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VE_SAVE_EVENT_SERVICE"}, "submitFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VE_SUBMIT_EVENT_SERVICE"}, "queryVoucherByIdFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_VE_QUERY_DETAIL_EVENT_SERVICE"}, "abItemFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ab_item_cf", "serviceKey": "ERP_FI$SYS_FindDataByIdService"}, "aaHeadIdFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_aa_head_cf", "serviceKey": "ERP_FI$SYS_FindDataByIdService"}, "abTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ab_type_cf", "serviceKey": "ERP_FI$SYS_FindDataByIdService"}, "abTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ab_type_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "asOrgIdFindFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "calendarItemIdFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_calendar_item_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "vtTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_vt_type_cf", "serviceKey": "ERP_FI$SYS_FindDataByIdService"}, "vtTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_vt_type_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "currIdPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "currIdFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "rtTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_exchange_rate_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "rtTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_exchange_rate_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "unitFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getMatPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getMatFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getOrgPagingFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService"}, "getOrgFindFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_FindDataByIdService"}, "getTaxTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_tax_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getTaxTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_tax_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getVendInfoPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getVendInfoFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getMatSlsPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_sls_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "getMatSlsFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_sls_md", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "userFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$user", "serviceKey": "ERP_FI$SYS_FindDataByIdService"}}}, {"key": "FinanceReportPage", "name": "FinanceReportPage", "type": "Widget", "title": "财务通用报表", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "财务通用报表配置"}, "actions": [{"key": "getData", "category": "getData", "label": "获取报表数据", "dataType": "Object"}], "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {"title": "财务通用报表"}, "staticProps": {"getAccountSubjectsFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AB_TYPE_SUBJECT_ITEM_LIST_EVENT_SERVICE"}, "getAccountCalenderFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AB_TYPE_CALENDER_ITEM_LEAF_LIST_SERVICE"}, "loadTaskFlow": {"type": "InvokeService", "serviceKey": "sys_common$API_T_SCHEDULER_JOB_INSTANCE_FIND_BY_UUID_POST"}, "fin_glm_ad_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ad_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "fin_glm_abs_ad_value_tr_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_abs_ad_value_tr", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "gen_curr_type_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$$gen_curr_type_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "gen_vend_info_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_vend_info_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_cust_info_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_cust_info_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_sub_bank_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_sub_bank_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_tax_type_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_tax_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_uom_type_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_uom_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_calendar_item_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_calendar_item_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_addr_type_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_addr_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "org_struct_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "sys_common$org_struct_md", "serviceKey": "sys_common$SYS_PagingDataService"}, "gen_business_partner_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_business_partner_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_bank_cf_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_bank_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_mat_cate_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "gen_mat_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_mat_cate_md", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "fin_cm_al_type_md_PagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FIN$fin_cm_al_type_md", "serviceKey": "ERP_FIN$SYS_PagingDataService"}, "getUserAbTypeFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AB_INFO_QUERY_BY_USER_ID_EVENT_SERVICE"}}}, {"key": "AssetsLiabilitiesReportPage", "name": "AssetsLiabilitiesReportPage", "type": "Widget", "title": "资产负债自定义报表", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"abTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ab_type_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "abTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ab_type_cf", "serviceKey": "ERP_FI$SYS_FindDataByIdService"}, "getAccountPeriodFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AB_TYPE_CALENDER_ITEM_LIST_EVENT_SERVICE"}, "currIdPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "currIdFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getDetailFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$GYPCS_FIN_GLM_BALANCE_SHEET_QUERY_EVENT_SERVICE"}, "registerTaskFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$GYPCS_FIN_GLM_BALANCE_SHEET_QUERY_ASYNC_EVENT_SERVICE"}}}, {"key": "IncomeReportPage", "name": "IncomeReportPage", "type": "Widget", "title": "利润自定义报表", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {}, "staticProps": {"abTypePagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ab_type_cf", "serviceKey": "ERP_FI$SYS_PagingDataService"}, "abTypeFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_FI$fin_glm_ab_type_cf", "serviceKey": "ERP_FI$SYS_FindDataByIdService"}, "getAccountPeriodFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$FIN_GLM_AB_TYPE_CALENDER_ITEM_LIST_EVENT_SERVICE"}, "currIdPagingFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_PagingDataService"}, "currIdFindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_GEN$gen_curr_type_cf", "serviceKey": "ERP_GEN$SYS_FindDataByIdService"}, "getDetailFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$GYPCS_FIN_GLM_INCOME_STATEMENT_QUERY_EVENT_SERVICE"}, "registerTaskFlow": {"type": "InvokeService", "serviceKey": "ERP_FI$GYPCS_GLM_INCOME_STATEMENT_QUERY_ASYNC_EVENT_SERVICE"}}}, {"key": "AuxiliaryDimensionAllowModelSelect", "name": "AuxiliaryDimensionAllowModelSelect", "type": "Widget", "title": "辅助维度允许模型选择器", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "辅助维度允许模型选择器"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true, "defaultProps": {"readonly": false}, "staticProps": {"options": [{"relationModelKey": "sys_common$org_struct_md", "relationModelName": "组织"}, {"relationModelKey": "ERP_GEN$gen_mat_md", "relationModelName": "物料主数据定义表"}, {"relationModelKey": "ERP_GEN$gen_mat_cate_md", "relationModelName": "物料类目"}, {"relationModelKey": "ERP_GEN$gen_business_partner_md", "relationModelName": "合作伙伴"}, {"relationModelKey": "ERP_GEN$gen_vend_info_md", "relationModelName": "供应商主数据"}, {"relationModelKey": "ERP_GEN$gen_cust_info_md", "relationModelName": "客户"}, {"relationModelKey": "ERP_GEN$gen_curr_type_cf", "relationModelName": "币种配置表"}, {"relationModelKey": "ERP_GEN$gen_sub_bank_cf", "relationModelName": "银行支行表"}, {"relationModelKey": "ERP_GEN$gen_bank_cf", "relationModelName": "银行配置表"}, {"relationModelKey": "ERP_HR$pen_main_info_md", "relationModelName": "人事主信息表"}, {"relationModelKey": "ERP_GEN$gen_tax_type_cf", "relationModelName": "税配置表"}]}}, {"key": "PickingInspect", "name": "PickingInspect", "type": "Widget", "title": "拣配验货", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "designerProps": {"type": "formily-react", "title": "拣配验货"}, "editable": true, "defaultProps": {}, "staticProps": {"serviceProps": {"getBarcodeRuleFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_CODE_BY_LABEL_FIND_EVENT_SERVICE"}, "queryFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$DEL_DN_SCAN_CODE_INSPECT_GOODS_EVENT_SERVICE"}, "postFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$DEL_DN_SCAN_CODE_POSTED_QUERY_EVENT_SERVICE"}}}}, {"key": "BatchStrategyMatchingRange", "name": "BatchStrategyMatchingRange", "type": "Widget", "title": "批次策略匹配范围", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "defaultProps": {"readonly": false}, "staticProps": {"createConditionIdFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_CREATE_MATCH_RECORD_HEAD_EVENT_SERVICE"}, "editConditionIdFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MATCH_RECORD_HEAD_UPDATE_EVENT_SERVICE"}, "fetchConditionIdDetailFlow": {"type": "InvokeService", "serviceKey": "ERP_GEN$GEN_MATCH_CONDITION_TABLE_PROR_EVENT_SERVICE"}}, "designerProps": {"type": "formily-react", "title": "批次策略匹配范围配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true}, {"key": "FinRuleConditionSelect", "name": "FinRuleConditionSelect", "type": "Widget", "title": "财审规则条件选择", "group": "TERP业务组件", "droppable": false, "draggable": true, "selectable": true, "cloneable": true, "deletable": true, "defaultProps": {"title": "财审规则条件选择", "readonly": false}, "staticProps": {"ext_sls_bill_register_cf_FindFlow": {"type": "InvokeSystemService", "modelAlias": "ERP_SCM$ext_sls_bill_register_cf", "serviceKey": "ERP_SCM$SYS_FindDataByIdService"}, "scmExtSoFinBillRegisterFindServiceFlow": {"type": "InvokeService", "serviceKey": "ERP_SCM$EXT_SO_FIN_BILL_REGISTER_FIND_SERVICE"}}, "designerProps": {"type": "formily-react", "title": "财审规则条件选择配置"}, "permissions": [{"type": "Self", "resourceType": "Container"}], "editable": true}], "namespace": "terp", "metadata": {"branch": "feature/latest", "commitSha": "46cf558", "buildAt": "2025-02-05 10:57:28"}, "updateAt": "2025-02-05T02:58:29.928Z"}