spring:
  application:
    name: trantor2
  jpa:
    properties:
      hibernate:
        globally_quoted_identifiers: false
        jdbc:
          batch_size: 500
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.MySQLDialect
    show-sql: false # we already use dataSource logging
  sql:
    init:
      mode: always
trantor2:
  meta:
    event:
      type: local
